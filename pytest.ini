[pytest]
markers =
    huawei: Tests for Huawei platform integration
    gitee: Tests for Gitee platform integration
    ollama: Tests for Ollama integration
    fuyao: Tests for Fuyao integration
    integration: Integration tests
    performance: Performance tests
    deduplication: Deduplication tests
    complete_workflow: Complete workflow tests
    error_handling: Error handling tests
    production: Production tests 

# 忽略特定目录
norecursedirs = .* build dist CVS _darcs *.egg venv env .env .venv tests/integration/huawei/* tests/infrastructure/huawei/* test_external_projects/*  tools/*

# 日志配置，保证IDE和命令行都能看到日志
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 添加默认的测试选项
addopts = -v -s --tb=short --ignore=tests/integration/huawei --ignore=tests/infrastructure/huawei 