# Git Keeper 当前架构总结

## 🏗️ **架构概述**

Git Keeper 是一个基于 LLM 的代码质量检查工具，采用分层架构设计，支持两种核心模式：
1. **实际运行模式**: 检查真正的MR/Branch，提升代码质量
2. **评估模式**: 评估系统的检查性能，支持MR、Branch、Dataset三种子模式

```mermaid
graph TB
    %% 接口层
    subgraph "接口层 (Interface Layer)"
        CLI[CLI命令行]
        API[REST API]
        WebUI[Web界面]
        EvalCLI[评估模式CLI]
    end

    %% 应用层
    subgraph "应用层 (Application Layer)"
        subgraph "用例层 (Use Cases)"
            AnalyzeMR[AnalyzeMRUseCase<br/>MR分析用例]
            AnalyzeBranch[AnalyzeBranchUseCase<br/>分支分析用例]
            EvalUseCase[EvalUseCase<br/>评测集自包含评测]
        end
        
        subgraph "服务层 (Services)"
            ServiceOrchestrator[ServiceOrchestrator<br/>服务编排器]
            LLMService[LLMService<br/>LLM服务]
            GitService[GitService<br/>Git服务]
            ContextManager[ContextManager<br/>上下文管理器]
            RuleManager[RuleManager<br/>规则管理器]
            ReportService[ReportService<br/>报告服务]
            RepositoryAnalyzer[RepositoryAnalyzer<br/>仓库分析器]
        end
        
        subgraph "评估协调层 (Evaluation Coordination)"
            EvalCoordinator[EvaluationCoordinator<br/>评估协调器]
            EvalConfig[EvaluationConfigManager<br/>评估配置管理器]
            EvalReport[OptimizedEvaluationReportService<br/>优化评估报告服务]
        end
    end

    %% 领域层
    subgraph "领域层 (Domain Layer)"
        subgraph "实体 (Entities)"
            CheckRule[CodeCheckRule<br/>检查规则]
            TestCase[TestCase<br/>测试用例]
            MergeRequest[MergeRequest<br/>合并请求]
        end
        
        subgraph "值对象 (Value Objects)"
            AnalyzeLLMResult[AnalyzeLLMResult<br/>LLM分析结果]
            CheckMRResult[CheckMRResult<br/>MR检查结果]
            DiffResult[DiffResult<br/>差异结果]
            AffectedFunction[AffectedFunction<br/>受影响函数]
        end
    end

    %% 基础设施层
    subgraph "基础设施层 (Infrastructure Layer)"
        GitClient[GitClient<br/>Git客户端]
        LLMClient[LLMClient<br/>LLM客户端]
        FileSystem[FileSystem<br/>文件系统]
        ConfigSystem[ConfigSystem<br/>配置系统]
    end

    %% 外部层
    subgraph "外部层 (External Layer)"
        CodeAnalyzer[CodeAnalyzer<br/>代码分析器]
        GitPlatform[Git平台<br/>Gitee/GitHub]
        LLMProvider[LLM提供商<br/>Ollama/OpenAI]
        YamlDataset[YamlDataset<br/>YAML数据集]
    end

    %% 连接关系
    %% 接口层到应用层
    CLI --> AnalyzeMR
    CLI --> AnalyzeBranch
    EvalCLI --> EvalCoordinator
    API --> ServiceOrchestrator
    
    %% 评估模式的统一架构
    EvalCoordinator --> AnalyzeMR
    EvalCoordinator --> AnalyzeBranch
    EvalCoordinator --> EvalUseCase
    EvalCoordinator --> EvalConfig
    EvalCoordinator --> EvalReport
    
    %% 用例层到服务层
    AnalyzeMR --> ServiceOrchestrator
    AnalyzeBranch --> ServiceOrchestrator
    EvalUseCase --> ServiceOrchestrator
    
    %% 服务编排器管理所有服务
    ServiceOrchestrator --> LLMService
    ServiceOrchestrator --> GitService
    ServiceOrchestrator --> ContextManager
    ServiceOrchestrator --> RuleManager
    ServiceOrchestrator --> ReportService
    ServiceOrchestrator --> RepositoryAnalyzer
    
    %% TestCase与CheckRule的统一
    TestCase -.->|to_check_rule()| CheckRule
    
    %% 服务层到基础设施层
    LLMService --> LLMClient
    GitService --> GitClient
    RuleManager --> FileSystem
    ReportService --> FileSystem
    
    %% 基础设施层到外部层
    GitClient --> GitPlatform
    LLMClient --> LLMProvider
    RepositoryAnalyzer --> CodeAnalyzer
    EvalCoordinator --> YamlDataset
    
    %% 样式定义
    classDef interfaceStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef applicationStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef domainStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef infrastructureStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef externalStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef evalStyle fill:#fff9c4,stroke:#f57f17,stroke-width:3px
    
    %% 应用样式
    class CLI,API,WebUI,EvalCLI interfaceStyle
    class AnalyzeMR,AnalyzeBranch,EvalUseCase,ServiceOrchestrator,LLMService,GitService,ContextManager,RuleManager,ReportService,RepositoryAnalyzer applicationStyle
    class CheckRule,TestCase,MergeRequest,AnalyzeLLMResult,CheckMRResult,DiffResult,AffectedFunction domainStyle
    class GitClient,LLMClient,FileSystem,ConfigSystem infrastructureStyle
    class CodeAnalyzer,GitPlatform,LLMProvider,YamlDataset externalStyle
    class EvalCoordinator,EvalConfig,EvalReport evalStyle
```


## 📐 **分层架构**

### 1. **接口层 (Interface Layer)**
- **CLI命令行**: 主要的用户交互接口
- **REST API**: 提供HTTP接口服务
- **Web界面**: 可视化操作界面
- **评估模式CLI**: 专门的评估模式命令行接口

### 2. **应用层 (Application Layer)**

#### **用例层 (Use Cases)**
- **AnalyzeMRUseCase**: MR分析用例，处理合并请求的代码检查
- **AnalyzeBranchUseCase**: 分支分析用例，处理整个分支的代码检查
- **EvalUseCase**: 评测集自包含评测用例，处理评估模式的第三种子模式

#### **服务层 (Services)**
- **ServiceOrchestrator**: 服务编排器，管理所有服务的生命周期和依赖关系
- **LLMService**: LLM服务，负责与大语言模型的交互
- **GitService**: Git服务，处理Git仓库操作
- **ContextManager**: 上下文管理器，基于调用链的智能上下文选择
- **RuleManager**: 规则管理器，管理代码检查规则
- **ReportService**: 报告服务，生成检查报告
- **RepositoryAnalyzer**: 仓库分析器，进行代码静态分析

#### **评估协调层 (Evaluation Coordination)**
- **EvaluationCoordinator**: 评估协调器，专注于结果收集和指标计算
- **EvaluationConfigManager**: 评估配置管理器，复用gate_keeper配置
- **OptimizedEvaluationReportService**: 优化评估报告服务

### 3. **领域层 (Domain Layer)**

#### **实体 (Entities)**
- **CodeCheckRule**: 检查规则实体，定义代码检查的具体规则
- **TestCase**: 测试用例实体，评估模式的核心领域对象
- **MergeRequest**: 合并请求实体

#### **值对象 (Value Objects)**
- **AnalyzeLLMResult**: LLM分析结果
- **CheckMRResult**: MR检查结果
- **DiffResult**: 差异结果
- **AffectedFunction**: 受影响函数

### 4. **基础设施层 (Infrastructure Layer)**
- **GitClient**: Git客户端，封装Git操作
- **LLMClient**: LLM客户端，封装LLM调用
- **FileSystem**: 文件系统操作
- **ConfigSystem**: 配置系统

### 5. **外部层 (External Layer)**
- **CodeAnalyzer**: 代码分析器，提供AST解析和静态分析能力
- **Git平台**: Gitee/GitHub等Git托管平台
- **LLM提供商**: Ollama/OpenAI等LLM服务提供商
- **YamlDataset**: YAML数据集，评估模式的数据源

## 🔄 **核心设计原则**

### 1. **统一架构原则**
- **单一代码基础**: 评估模式和实际运行模式使用同一套核心代码
- **用例复用**: 评估模式直接复用AnalyzeMRUseCase、AnalyzeBranchUseCase
- **领域对象统一**: TestCase通过`to_check_rule()`转换为CheckRule

### 2. **职责分离原则**
- **评估协调器**: 专注于结果收集和指标计算，不重新实现检查逻辑
- **服务编排器**: 管理服务生命周期，处理依赖关系
- **用例层**: 封装业务逻辑，协调服务调用

### 3. **依赖倒置原则**
- **接口抽象**: 通过接口定义服务契约
- **依赖注入**: 通过服务编排器注入依赖
- **松耦合**: 各层之间通过接口交互

## 🚀 **核心流程**

### **实际运行模式流程**
```
1. CLI/API接收请求
2. 调用AnalyzeMRUseCase或AnalyzeBranchUseCase
3. ServiceOrchestrator协调各服务
4. GitService获取代码差异
5. RepositoryAnalyzer进行静态分析
6. ContextManager生成上下文
7. LLMService调用LLM进行分析
8. ReportService生成报告
```

### **评估模式流程**
```
1. EvaluationCoordinator接收评估请求
2. 加载YamlDataset数据集
3. TestCase转换为CheckRule
4. 根据模式调用对应的UseCase:
   - MR模式: AnalyzeMRUseCase
   - Branch模式: AnalyzeBranchUseCase  
   - Dataset模式: EvalUseCase
5. 收集分析结果
6. 计算评估指标
7. 生成评估报告
```

## 🔧 **关键技术特性**

### 1. **服务编排器 (ServiceOrchestrator)**
- **延迟初始化**: 按需创建服务，提高启动性能
- **依赖管理**: 自动处理服务间的依赖关系
- **资源复用**: 避免重复创建相同的服务
- **配置驱动**: 通过配置控制服务行为

### 2. **上下文管理器 (ContextManager)**
- **调用链分析**: 基于静态分析的调用链构建
- **智能上下文选择**: 为每个调用链单独生成优化的上下文
- **上下文膨胀控制**: 解决上下文过大的问题
- **相关性计算**: 基于调用关系计算上下文相关性

### 3. **LLM服务 (LLMService)**
- **并发调用**: 支持多个LLM并发调用
- **调用限制**: 任务级、函数级、规则组级调用次数限制
- **结果解析**: 智能解析LLM返回结果
- **错误处理**: 完善的错误处理和重试机制

### 4. **代码分析器 (CodeAnalyzer)**
- **多语言支持**: 支持Python、C、Java等多种语言
- **AST解析**: 基于抽象语法树的代码分析
- **调用关系分析**: 跨文件的函数调用关系分析
- **静态分析**: 提供丰富的静态分析能力

## 📊 **架构优势**

### 1. **统一性**
- 评估模式和实际运行模式共享同一套核心代码
- 减少代码重复，提高维护效率
- 保证两种模式的一致性

### 2. **可扩展性**
- 分层架构便于功能扩展
- 插件化的规则管理
- 支持新的LLM提供商和Git平台

### 3. **可维护性**
- 清晰的职责分离
- 松耦合的组件设计
- 完善的错误处理和日志

### 4. **性能优化**
- 服务的延迟初始化
- 智能的上下文管理
- 并发的LLM调用

## 🔍 **关键集成点**

### 1. **TestCase与CheckRule的统一**
```python
# TestCase通过to_check_rule()方法转换为CheckRule
test_case = TestCase(...)
check_rule = test_case.to_check_rule()
```

### 2. **评估模式与核心用例的集成**
```python
# 评估协调器直接使用核心用例
coordinator = EvaluationCoordinator(orchestrator)
metrics, results = coordinator.evaluate_mr_mode(test_cases, ...)
```

### 3. **服务编排器的中心化管理**
```python
# 所有服务通过编排器获取
orchestrator = ServiceOrchestrator(config)
llm_service = orchestrator.get_service("llm_service")
git_service = orchestrator.get_service("git_service")
```

## 📈 **未来发展方向**

1. **微服务化**: 将核心服务拆分为独立的微服务
2. **云原生**: 支持容器化部署和Kubernetes编排
3. **实时分析**: 支持实时代码检查和反馈
4. **AI增强**: 集成更多AI能力，提升检查准确性
5. **多租户**: 支持多租户的SaaS模式

这个架构成功地实现了评估模式与实际运行模式的统一，通过清晰的分层设计和职责分离，提供了一个可扩展、可维护的代码质量检查平台。
