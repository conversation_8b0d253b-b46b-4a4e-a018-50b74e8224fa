"""
自定义Prompt模板使用示例

展示如何使用新的自定义prompt模板功能来改进代码分析效果。
"""

from gate_keeper.application.service.rule.rule_service.manager import \
    RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule, PromptTemplate
from gate_keeper.infrastructure.llm.prompt.tasks.analyze_code.analyze_code import \
    create_instruction


def create_custom_prompt_rule_example():
    """创建带有自定义prompt模板的规则示例"""
    
    # 创建自定义prompt模板
    prompt_template = PromptTemplate(
        instruction="请特别关注内存复制操作，检查是否存在重叠内存区域的情况。重点关注memcpy的使用场景，判断是否应该使用memmove_s。",
        requirement_template="""规则ID: {rule_id}
规则名称: {rule_name}
规则类型: {rule_type}
优先级: {severity}
规则内容: {rule_value}

请检查代码中是否存在以下问题：
1. 使用memcpy进行重叠内存区域的复制
2. 没有检查源地址和目标地址是否重叠
3. 应该使用memmove_s但使用了memcpy""",
        example_template="""错误示例：
```c
void unsafe_buffer_rearrange(char* buffer, size_t size) {
    // 危险：重叠内存区域使用memcpy
    memcpy(buffer, buffer + 10, size - 10);
}
```

正确示例：
```c
void safe_buffer_rearrange(char* buffer, size_t size) {
    // 安全：使用memmove_s处理重叠区域
    memmove_s(buffer, size, buffer + 10, size - 10);
}
```""",
        output_format="""请按照以下格式输出检测结果：
{
  "is_pass": false,
  "reason": "发现重叠内存区域使用memcpy的情况",
  "violations": [
    {
      "rule_id": "1.2",
      "rule_content": "禁止使用memcpy进行重叠内存区域的复制",
      "location": {
        "file_path": "src/memory_ops.c",
        "line_number": 15
      },
      "severity": "高",
      "message": "函数unsafe_buffer_rearrange中使用memcpy复制重叠内存区域，应使用memmove_s"
    }
  ]
}""",
        context_requirements="检查时需要关注：\n1. 函数的参数和局部变量\n2. 内存操作的源地址和目标地址\n3. 是否存在地址重叠的可能性\n4. 相关的内存分配和释放操作",
        focus_points=["memcpy函数调用", "内存地址计算", "缓冲区大小检查", "重叠区域检测"]
    )
    
    # 创建带有自定义prompt模板的规则
    custom_rule = CodeCheckRule(
        id="1.2",
        name="内存搬移",
        description="内存复制安全规范",
        category=["内存管理", "安全"],
        enabled=True,
        severity="高",
        rule_value="禁止使用memcpy进行重叠内存区域的复制，必须使用memmove_s函数。",
        example="使用memmove_s替代memcpy",
        languages=["C"],
        prompt_template=prompt_template
    )
    
    return custom_rule


def create_simple_custom_rule_example():
    """创建使用简化自定义字段的规则示例"""
    
    simple_rule = CodeCheckRule(
        id="2.1",
        name="字符串格式化",
        description="字符串格式化安全规范",
        category=["字符串操作", "安全"],
        enabled=True,
        severity="中",
        rule_value="禁止使用sprintf进行字符串格式化，必须使用snprintf函数并指定缓冲区大小。",
        example="使用snprintf替代sprintf",
        languages=["C"],
        custom_instruction="请检查字符串格式化操作，确保使用安全的格式化函数并正确指定缓冲区大小。",
        custom_requirement_format="重点关注：\n1. sprintf函数的使用\n2. snprintf的缓冲区大小参数\n3. 格式化字符串的长度控制",
        focus_keywords=["sprintf函数调用", "snprintf函数调用", "缓冲区大小参数", "格式化字符串长度"]
    )
    
    return simple_rule


def demonstrate_prompt_generation():
    """演示prompt生成过程"""
    
    print("=== 自定义Prompt模板功能演示 ===\n")
    
    # 创建示例规则
    custom_rule = create_custom_prompt_rule_example()
    simple_rule = create_simple_custom_rule_example()
    
    # 检查规则是否有自定义prompt
    print(f"规则 {custom_rule.id} 是否有自定义prompt: {custom_rule.has_custom_prompt()}")
    print(f"规则 {simple_rule.id} 是否有自定义prompt: {simple_rule.has_custom_prompt()}\n")
    
    # 显示自定义指令
    print(f"规则 {custom_rule.id} 的自定义指令:")
    print(custom_rule.get_effective_instruction())
    print()
    
    print(f"规则 {simple_rule.id} 的自定义指令:")
    print(simple_rule.get_effective_instruction())
    print()
    
    # 显示格式化后的要求
    print(f"规则 {custom_rule.id} 的格式化要求:")
    formatted_requirement = custom_rule.format_requirement_with_variables(
        rule_id=custom_rule.id,
        rule_name=custom_rule.name,
        rule_type='-'.join(custom_rule.category),
        severity=custom_rule.severity,
        rule_value=custom_rule.rule_value
    )
    print(formatted_requirement)
    print()
    
    # 显示重点关注点
    print(f"规则 {custom_rule.id} 的重点关注点:")
    for point in custom_rule.get_focus_points():
        print(f"  - {point}")
    print()
    
    # 使用create_instruction生成完整的prompt
    print("=== 生成的完整Prompt ===")
    instruction = create_instruction([custom_rule, simple_rule], "C")
    print(f"指令内容长度: {len(instruction.instruction)} 字符")
    print(f"要求数量: {len(instruction.requirements)}")
    print(f"示例数量: {len(instruction.examples)}")
    print()
    
    # 显示第一个要求的内容
    if instruction.requirements:
        print("第一个要求的内容:")
        print(instruction.requirements[0].requirement)
        print()


def demonstrate_rule_manager_integration():
    """演示与规则管理器的集成"""
    
    print("=== 规则管理器集成演示 ===\n")
    
    # 创建规则管理器（使用示例文件）
    try:
        rule_manager = RuleManager("resource/编码规范_custom_prompt_example.md")
        rules = rule_manager.load_rules()
        
        print(f"加载的规则数量: {len(rules)}")
        
        # 检查是否有规则包含自定义prompt
        custom_prompt_rules = [rule for rule in rules if rule.has_custom_prompt()]
        print(f"包含自定义prompt的规则数量: {len(custom_prompt_rules)}")
        
        for rule in custom_prompt_rules:
            print(f"\n规则 {rule.id} ({rule.name}):")
            print(f"  自定义指令: {bool(rule.get_effective_instruction())}")
            print(f"  自定义要求格式: {bool(rule.get_effective_requirement_format())}")
            print(f"  重点关注点数量: {len(rule.get_focus_points())}")
            
    except Exception as e:
        print(f"加载规则文件失败: {e}")
        print("请确保 resource/编码规范_custom_prompt_example.md 文件存在")


if __name__ == "__main__":
    # 运行演示
    demonstrate_prompt_generation()
    print("\n" + "="*50 + "\n")
    demonstrate_rule_manager_integration() 