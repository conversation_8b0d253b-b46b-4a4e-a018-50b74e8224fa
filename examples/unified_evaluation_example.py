#!/usr/bin/env python3
"""
统一评估系统使用示例

展示如何使用重构后的统一评估系统
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from eval.unified_evaluator import UnifiedEvaluator
from gate_keeper.application.service.service_orchestrator import (
    ServiceConfig, ServiceOrchestrator)
from gate_keeper.shared.log import app_logger as logger


def main():
    """主函数 - 展示统一评估系统的使用"""
    logger.info("🚀 开始统一评估系统使用示例")
    
    try:
        # 1. 初始化服务编排器
        logger.info("🔧 步骤1: 初始化服务编排器")
        
        # 创建服务配置
        config = ServiceConfig(
            git_token="your_git_token_here",  # 实际使用时需要提供真实的token
            git_platform="gitee",
            llm_endpoint="http://localhost:11434",
            llm_model="qwen2.5:7b",
            use_static_analysis=True,
            use_optimized_context=True,
            max_workers=5,
            max_context_chain_depth=3,
            max_context_chains=3,
            max_context_size=8000
        )
        
        orchestrator = ServiceOrchestrator(config)
        logger.info("✅ 服务编排器初始化完成")
        
        # 2. 初始化统一评估器
        logger.info("🔧 步骤2: 初始化统一评估器")
        evaluator = UnifiedEvaluator(orchestrator)
        logger.info("✅ 统一评估器初始化完成")
        
        # 3. 加载数据集
        logger.info("🔧 步骤3: 加载数据集")
        dataset_path = "eval/datasets/c_evalset_simple.yaml"
        if not os.path.exists(dataset_path):
            logger.warning(f"数据集文件不存在: {dataset_path}")
            logger.info("使用模拟数据集进行演示")
            # 这里可以创建一个模拟数据集
            return
        
        from eval.datasets.yaml_dataset import YamlDataset
        dataset = YamlDataset.load(dataset_path)
        logger.info(f"✅ 数据集加载完成，包含 {len(dataset.get_all_test_cases())} 个测试用例")
        
        # 4. 执行不同模式的评估
        repo_path = "/tmp/test_repo"  # 示例仓库路径
        
        # 4.1 数据集模式评估
        logger.info("🔧 步骤4.1: 执行数据集模式评估")
        try:
            metrics, results, report_service = evaluator.evaluate_dataset(
                dataset=dataset,
                repo_path=repo_path,
                analysis_mode="dataset",
                branch="master"
            )
            
            logger.info("📊 数据集模式评估结果:")
            logger.info(f"  总测试用例数: {metrics.total_cases}")
            logger.info(f"  准确率: {metrics.accuracy:.3f}")
            logger.info(f"  精确率: {metrics.precision:.3f}")
            logger.info(f"  召回率: {metrics.recall:.3f}")
            logger.info(f"  F1分数: {metrics.f1_score:.3f}")
            logger.info(f"  获得 {len(results)} 个分析结果")
            
        except Exception as e:
            logger.warning(f"数据集模式评估失败: {e}")
        
        # 4.2 Branch模式评估（如果有真实仓库）
        logger.info("🔧 步骤4.2: 执行Branch模式评估")
        try:
            metrics, results, report_service = evaluator.evaluate_dataset(
                dataset=dataset,
                repo_path=repo_path,
                analysis_mode="branch",
                branch="develop"
            )
            
            logger.info("📊 Branch模式评估结果:")
            logger.info(f"  总测试用例数: {metrics.total_cases}")
            logger.info(f"  准确率: {metrics.accuracy:.3f}")
            logger.info(f"  精确率: {metrics.precision:.3f}")
            logger.info(f"  召回率: {metrics.recall:.3f}")
            logger.info(f"  F1分数: {metrics.f1_score:.3f}")
            logger.info(f"  获得 {len(results)} 个分析结果")
            
        except Exception as e:
            logger.warning(f"Branch模式评估失败: {e}")
        
        # 4.3 MR模式评估（如果有真实MR）
        logger.info("🔧 步骤4.3: 执行MR模式评估")
        try:
            metrics, results, report_service = evaluator.evaluate_dataset(
                dataset=dataset,
                repo_path=repo_path,
                analysis_mode="mr",
                mr_id=123,
                base_branch="master",
                dev_branch="feature"
            )
            
            logger.info("📊 MR模式评估结果:")
            logger.info(f"  总测试用例数: {metrics.total_cases}")
            logger.info(f"  准确率: {metrics.accuracy:.3f}")
            logger.info(f"  精确率: {metrics.precision:.3f}")
            logger.info(f"  召回率: {metrics.recall:.3f}")
            logger.info(f"  F1分数: {metrics.f1_score:.3f}")
            logger.info(f"  获得 {len(results)} 个分析结果")
            
        except Exception as e:
            logger.warning(f"MR模式评估失败: {e}")
        
        logger.info("✅ 统一评估系统使用示例完成")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}", exc_info=True)


def demonstrate_test_case_conversion():
    """演示TestCase转换功能"""
    logger.info("🔧 演示TestCase转换功能")
    
    from gate_keeper.domain.rule.check_rule import (CodeCheckRule, GitInfo,
                                                    TestCase)

    # 创建一个CodeCheckRule
    check_rule = CodeCheckRule(
        id="R001",
        name="函数命名规范",
        description="函数名应使用小写字母和下划线",
        category=["命名规范"],
        enabled=True,
        severity="medium"
    )
    
    # 转换为TestCase
    test_case = TestCase.from_check_rule(
        check_rule,
        case_name="正面示例",
        expected_answer="通过",
        code="void good_function() { }",
        type="positive"
    )
    
    logger.info(f"✅ 从CodeCheckRule创建TestCase: {test_case.id}")
    logger.info(f"  规则名称: {test_case.name}")
    logger.info(f"  测试用例名称: {test_case.case_name}")
    logger.info(f"  期望答案: {test_case.expected_answer}")
    logger.info(f"  测试代码: {test_case.code}")
    logger.info(f"  测试类型: {test_case.type}")
    
    # 转换回CodeCheckRule
    converted_rule = test_case.to_check_rule()
    logger.info(f"✅ 转换回CodeCheckRule: {converted_rule.id}")
    logger.info(f"  规则名称: {converted_rule.name}")
    logger.info(f"  规则描述: {converted_rule.description}")
    logger.info(f"  规则分类: {converted_rule.category}")


if __name__ == "__main__":
    # 演示TestCase转换功能
    demonstrate_test_case_conversion()
    
    # 运行主示例
    main() 