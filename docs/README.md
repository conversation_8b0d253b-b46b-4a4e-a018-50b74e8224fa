# Git Keeper 文档

Git Keeper 是一个基于 LLM 的智能代码质量检查工具，通过分析 Git 仓库的变更来检测代码规范问题。

## 核心特性

- 🔍 **智能代码分析**: 基于调用链的上下文优化，提高LLM分析准确性
- 🚀 **高性能**: 延迟初始化、并发处理、智能缓存
- 🔧 **灵活配置**: 支持多种环境配置和动态参数调整
- 📊 **多平台支持**: 支持Gitee、CodeHub等多种Git平台
- 🤖 **LLM集成**: 支持多种LLM提供商（华为云Fuyao、Ollama等）
- 📋 **规则管理**: 智能规则分组和相似度去重

## 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/your-org/git-keeper.git
cd git-keeper

# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export GK_ENV=dev
export GIT_TOKEN=your_token
export LLM_ENDPOINT=your_llm_endpoint
```

### 基本使用

```bash
# 分析MR
python -m gate_keeper.interfaces.cli.main analyze-mr \
  --repo-dir /path/to/repo \
  --mr-id 123 \
  --base-branch main \
  --dev-branch feature/new-feature

# 分析分支
python -m gate_keeper.interfaces.cli.main analyze-branch \
  --repo-dir /path/to/repo \
  --branch feature/new-feature
```

## 文档导航

### 📚 核心文档

- **[架构设计](architecture.md)**: 系统整体架构和设计原则
- **[核心服务](core-services.md)**: 详细的服务实现说明
- **[用例和流程](use-cases-and-flows.md)**: 核心业务逻辑和流程
- **[配置和部署](configuration.md)**: 配置系统和部署指南

### 🔧 开发文档

- **[API文档](api/)**: 接口和组件API文档
- **[测试指南](testing/)**: 测试策略和用例
- **[贡献指南](contributing.md)**: 开发贡献指南

### 📖 用户指南

- **[安装指南](installation.md)**: 详细的安装步骤
- **[使用教程](tutorials/)**: 使用教程和示例
- **[常见问题](faq.md)**: 常见问题解答

## 核心组件

### 服务编排器 (ServiceOrchestrator)

管理所有服务的生命周期和依赖关系，实现延迟初始化和资源复用。

```python
orchestrator = ServiceOrchestrator(
    git_service=git_service,
    llm_client=llm_client
)

# 按需获取服务
llm_service = orchestrator.llm_service
context_manager = orchestrator.context_manager
```

### 上下文管理器 (ContextManager)

基于调用链的智能上下文选择，解决上下文膨胀问题。

```python
context_manager = ContextManager(static_analyzer)

# 生成优化上下文
contexts = context_manager.generate_optimized_contexts(
    af=affected_function,
    max_chains=3
)
```

### LLM服务 (LLMService)

调用LLM进行代码质量分析，支持并发调用和调用限制。

```python
llm_service = LLMService(
    client=llm_client,
    static_analyzer=static_analyzer,
    use_optimized_context=True
)

# 分析MR
result = llm_service.analyze_mr(check_mr_result)
```

## 配置系统

### 环境配置

支持多种环境配置：

```python
# 开发环境
export GK_ENV=dev

# 生产环境  
export GK_ENV=prod

# 测试环境
export GK_ENV=test
```

### 核心配置项

```python
# 上下文优化配置
use_optimized_context = True
max_context_size = 8000
max_context_chains = 3
max_context_chain_depth = 3

# LLM调用限制
max_llm_calls_per_affected_function = 2
max_llm_calls_per_task = None
max_llm_calls_per_rule_group = None

# 规则分组配置
rule_grouping_strategy = "adaptive"
min_rule_group_size = 3
max_rule_group_size = 8
```

## 部署方式

### 本地开发

```bash
# 设置开发环境
export GK_ENV=dev
python -m gate_keeper.interfaces.cli.main
```

### Docker部署

```bash
# 构建镜像
docker build -t git-keeper .

# 运行容器
docker run -e GK_ENV=prod -e GIT_TOKEN=your_token git-keeper
```

### Kubernetes部署

```bash
# 部署到K8s
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
```

## 性能优化

### 延迟初始化

- RepositoryIndex: 只在需要时构建
- ContextManager: 按需创建
- LLMService: 延迟初始化

### 并发处理

- 多线程LLM调用
- 并行文件处理
- 异步规则加载

### 缓存机制

- 仓库索引缓存
- 上下文缓存
- 规则缓存

## 扩展开发

### 添加新的LLM客户端

```python
class CustomLLMClient(LLMClient):
    def generate(self, prompt, parameters, token=None):
        # 实现自定义LLM调用逻辑
        pass
```

### 添加新的Git平台

```python
class CustomGitClient(BaseGitClient):
    def get_diff_by_mr_id(self, repo_dir, mr_id, base_branch, dev_branch):
        # 实现自定义Git平台逻辑
        pass
```

### 添加新的代码分析器

```python
class CustomStaticAnalyzer(StaticAnalyzer):
    def analyze_functions(self, file_content):
        # 实现自定义代码分析逻辑
        pass
```

## 贡献指南

我们欢迎社区贡献！请查看 [贡献指南](contributing.md) 了解如何参与项目开发。

### 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/your-org/git-keeper.git
cd git-keeper

# 创建开发分支
git checkout -b feature/your-feature

# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
pytest tests/

# 提交代码
git commit -m "feat: add your feature"
git push origin feature/your-feature
```

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 联系我们

- 项目主页: https://github.com/your-org/git-keeper
- 问题反馈: https://github.com/your-org/git-keeper/issues
- 讨论区: https://github.com/your-org/git-keeper/discussions

---

**Git Keeper** - 让代码质量检查更智能、更高效！ 