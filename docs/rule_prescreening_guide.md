# 规则预筛选配置指南

## 概述

规则预筛选系统提供了多种策略来智能筛选适用的代码检查规则，提高检查效率和准确性。支持以下预筛选策略：

- **无预筛选 (none)**: 使用所有规则，不进行预筛选
- **正则表达式 (regex)**: 基于规则的match_regex字段进行筛选
- **语义匹配 (embedding)**: 基于代码和规则的语义相似度进行筛选
- **混合策略 (hybrid)**: 结合正则表达式和语义匹配

## 配置方式

### 1. 基本配置

```python
from gate_keeper.application.service.rule.rule_service.manager import RuleManager

# 使用正则表达式预筛选（默认）
rule_manager = RuleManager(
    rule_file_path="eval/datasets/c_evalset_real.yaml",
    prescreening_config={
        "strategy": "regex"
    }
)

# 使用语义匹配预筛选
rule_manager = RuleManager(
    rule_file_path="eval/datasets/c_evalset_real.yaml",
    prescreening_config={
        "strategy": "embedding",
        "embedding_model": "text-embedding-ada-002",
        "embedding_similarity_threshold": 0.7
    }
)

# 不使用预筛选
rule_manager = RuleManager(
    rule_file_path="eval/datasets/c_evalset_real.yaml",
    prescreening_config={
        "strategy": "none"
    }
)
```

### 2. 详细配置选项

```python
prescreening_config = {
    # 基本策略配置
    "strategy": "hybrid",  # none, regex, embedding, hybrid
    
    # 正则表达式配置
    "regex_timeout": 1.0,           # 正则匹配超时时间（秒）
    "regex_cache_size": 1000,       # 正则缓存大小
    
    # Embedding配置
    "embedding_model": "text-embedding-ada-002",  # 使用的embedding模型
    "embedding_similarity_threshold": 0.7,        # 相似度阈值
    "embedding_cache_file": "data/embedding_cache.json",  # 缓存文件路径
    
    # 混合策略配置
    "hybrid_regex_weight": 0.3,      # 正则匹配权重
    "hybrid_embedding_weight": 0.7,  # 语义匹配权重
    "hybrid_min_score": 0.5,         # 最小综合分数
    
    # 性能配置
    "enable_fallback": True,         # 启用降级策略
    "max_processing_time": 5.0,      # 最大处理时间（秒）
    
    # 调试配置
    "enable_stats": True,            # 启用统计信息
    "log_performance": False         # 记录性能日志
}
```

## 使用方法

### 1. 获取适用规则

```python
from gate_keeper.external.code_analyzer.models.element import CodeElement

# 创建代码元素
code_element = CodeElement(
    name="unsafe_copy",
    type="function",
    filepath="test.c",
    code="""
    void unsafe_copy(char *dst, char *src, int len) {
        memcpy(dst, src, len);  // 未检查边界
    }
    """
)

# 使用预筛选获取适用规则
applicable_rules = rule_manager.get_applicable_rules_with_prescreening(code_element)

# 传统方法（不使用预筛选）
all_rules = rule_manager.get_applicable_rules(code_element)
```

### 2. 动态更新配置

```python
# 运行时更新配置
new_config = {
    "strategy": "embedding",
    "embedding_similarity_threshold": 0.8
}
rule_manager.update_prescreening_config(new_config)
```

### 3. 获取统计信息

```python
# 获取预筛选统计
stats = rule_manager.get_prescreening_stats()
print(f"总请求数: {stats['total_requests']}")
print(f"策略使用情况: {stats['strategy_usage']}")
print(f"平均处理时间: {stats['performance']}")

# 清空统计信息
rule_manager.clear_prescreening_stats()
```

## 策略详解

### 1. 无预筛选 (none)

- **适用场景**: 规则数量较少，或需要确保所有规则都被考虑
- **优点**: 简单可靠，不会遗漏任何规则
- **缺点**: 性能较低，特别是规则数量很多时

```python
config = {"strategy": "none"}
```

### 2. 正则表达式预筛选 (regex)

- **适用场景**: 规则有明确的代码模式匹配需求
- **优点**: 快速准确，基于明确的模式匹配
- **缺点**: 需要规则提供match_regex字段，无法处理语义相似性

```python
config = {
    "strategy": "regex",
    "regex_timeout": 1.0,
    "regex_cache_size": 1000
}
```

### 3. 语义匹配预筛选 (embedding)

- **适用场景**: 需要基于语义相似度进行智能匹配
- **优点**: 能理解代码和规则的语义关系，更智能
- **缺点**: 需要调用embedding服务，性能开销较大

```python
config = {
    "strategy": "embedding",
    "embedding_model": "text-embedding-ada-002",
    "embedding_similarity_threshold": 0.7,
    "embedding_cache_file": "data/embedding_cache.json"
}
```

### 4. 混合策略 (hybrid)

- **适用场景**: 需要结合模式匹配和语义理解的优势
- **优点**: 综合了两种方法的优点，更准确
- **缺点**: 配置复杂，性能开销最大

```python
config = {
    "strategy": "hybrid",
    "hybrid_regex_weight": 0.3,
    "hybrid_embedding_weight": 0.7,
    "hybrid_min_score": 0.5
}
```

## 性能优化建议

### 1. 缓存策略

- **正则缓存**: 自动缓存编译后的正则表达式
- **Embedding缓存**: 缓存计算过的embedding向量
- **规则索引**: 预先为规则建立embedding索引

### 2. 降级机制

- 当预筛选处理时间超过阈值时，自动降级到无预筛选
- 当embedding服务不可用时，降级到正则匹配
- 当正则匹配失败时，降级到无预筛选

### 3. 配置建议

```python
# 生产环境推荐配置
production_config = {
    "strategy": "regex",  # 生产环境优先使用regex，性能稳定
    "enable_fallback": True,
    "max_processing_time": 2.0,
    "enable_stats": True,
    "log_performance": False
}

# 开发环境推荐配置
development_config = {
    "strategy": "hybrid",  # 开发环境可以使用更智能的策略
    "embedding_similarity_threshold": 0.6,  # 稍微宽松的阈值
    "enable_fallback": True,
    "max_processing_time": 5.0,
    "enable_stats": True,
    "log_performance": True
}
```

## 监控和调试

### 1. 统计信息

```python
stats = rule_manager.get_prescreening_stats()

# 关键指标
print(f"总请求数: {stats['total_requests']}")
print(f"降级次数: {stats['fallback_count']}")
print(f"平均处理时间: {stats['performance']['regex_avg']:.3f}s")
print(f"缓存命中率: {stats['regex_cache']['cache_size']}")
```

### 2. 性能分析

```python
# 启用性能日志
config = {
    "strategy": "embedding",
    "log_performance": True,
    "enable_stats": True
}

# 查看详细性能数据
stats = rule_manager.get_prescreening_stats()
for strategy, times in stats['performance'].items():
    if times:
        avg_time = sum(times) / len(times)
        max_time = max(times)
        print(f"{strategy}: 平均 {avg_time:.3f}s, 最大 {max_time:.3f}s")
```

### 3. 故障排查

- 检查embedding服务是否可用
- 验证正则表达式的有效性
- 监控缓存命中率和大小
- 观察降级策略的触发频率

## 最佳实践

1. **渐进式部署**: 从regex策略开始，逐步尝试embedding和hybrid
2. **性能监控**: 持续监控预筛选的性能影响
3. **阈值调优**: 根据实际效果调整相似度阈值
4. **缓存管理**: 定期清理和优化缓存
5. **降级准备**: 确保降级策略能正常工作
