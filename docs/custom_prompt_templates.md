# 自定义Prompt模板功能

## 概述

GateKeeper现在支持为每个规则自定义prompt模板，这允许你为特定的代码规范规则创建更精确、更有针对性的提示词，从而提高LLM的分析准确性和效果。

## 功能特性

### 1. 完整的Prompt模板支持
- **自定义指令**：为每个规则定义特定的分析指令
- **自定义要求格式**：支持变量替换的要求模板
- **自定义示例格式**：提供规则特定的示例模板
- **自定义输出格式**：定义期望的输出格式
- **上下文要求**：指定分析时需要的上下文信息
- **重点关注点**：列出需要特别关注的检查点

### 2. 灵活的配置方式
- **Markdown文件**：使用标签语法定义自定义prompt
- **Excel文件**：通过额外的列配置自定义prompt
- **程序化创建**：通过代码直接创建规则和模板

### 3. 向后兼容
- 现有规则文件无需修改即可继续使用
- 新功能是可选的，不影响现有功能

## 使用方法

### 方法1：Markdown文件配置

在Markdown规则文件中，使用以下标签来定义自定义prompt模板：

```markdown
### 1.2 内存搬移

【规则】禁止使用memcpy进行重叠内存区域的复制，必须使用memmove_s函数。

【指令】请特别关注内存复制操作，检查是否存在重叠内存区域的情况。重点关注memcpy的使用场景，判断是否应该使用memmove_s。

【要求模板】规则ID: {rule_id}
规则名称: {rule_name}
规则类型: {rule_type}
优先级: {severity}
规则内容: {rule_value}

请检查代码中是否存在以下问题：
1. 使用memcpy进行重叠内存区域的复制
2. 没有检查源地址和目标地址是否重叠
3. 应该使用memmove_s但使用了memcpy

【示例模板】错误示例：
```c
void unsafe_buffer_rearrange(char* buffer, size_t size) {
    memcpy(buffer, buffer + 10, size - 10);
}
```

正确示例：
```c
void safe_buffer_rearrange(char* buffer, size_t size) {
    memmove_s(buffer, size, buffer + 10, size - 10);
}
```

【输出格式】请按照以下格式输出检测结果：
{
  "is_pass": false,
  "reason": "发现重叠内存区域使用memcpy的情况",
  "violations": [
    {
      "rule_id": "1.2",
      "rule_content": "禁止使用memcpy进行重叠内存区域的复制",
      "location": {
        "file_path": "src/memory_ops.c",
        "line_number": 15
      },
      "severity": "高",
      "message": "函数unsafe_buffer_rearrange中使用memcpy复制重叠内存区域，应使用memmove_s"
    }
  ]
}

【上下文要求】检查时需要关注：
1. 函数的参数和局部变量
2. 内存操作的源地址和目标地址
3. 是否存在地址重叠的可能性
4. 相关的内存分配和释放操作

【重点关注】
- memcpy函数调用
- 内存地址计算
- 缓冲区大小检查
- 重叠区域检测
```

### 方法2：Excel文件配置

在Excel文件中，添加以下列来配置自定义prompt模板：

| 列名 | 描述 | 示例 |
|------|------|------|
| custom_instruction | 自定义指令 | "请特别关注内存复制操作..." |
| custom_requirement_format | 自定义要求格式 | "规则ID: {rule_id}\n规则名称: {rule_name}..." |
| custom_example_format | 自定义示例格式 | "错误示例：使用memcpy..." |
| custom_output_format | 自定义输出格式 | "请按照以下格式输出..." |
| context_requirements | 上下文要求 | "检查时需要关注..." |
| focus_keywords | 重点关注关键词（逗号分隔） | "memcpy函数调用,重叠区域检测" |

### 方法3：程序化创建

```python
from gate_keeper.domain.rule.check_rule import CodeCheckRule, PromptTemplate

# 创建自定义prompt模板
prompt_template = PromptTemplate(
    instruction="请特别关注内存复制操作，检查是否存在重叠内存区域的情况。",
    requirement_template="规则ID: {rule_id}\n规则名称: {rule_name}\n重点关注：{rule_value}",
    example_template="错误示例：使用memcpy复制重叠区域\n正确示例：使用memmove_s",
    output_format="请输出JSON格式的检测结果",
    context_requirements="检查内存操作的源地址和目标地址",
    focus_points=["memcpy函数调用", "重叠区域检测", "缓冲区大小检查"]
)

# 创建带有自定义prompt模板的规则
custom_rule = CodeCheckRule(
    id="1.2",
    name="内存搬移",
    category=["内存管理", "安全"],
    rule_value="禁止使用memcpy进行重叠内存区域的复制",
    prompt_template=prompt_template
)
```

## 变量替换

在要求模板中，支持以下变量的替换：

| 变量 | 描述 | 示例值 |
|------|------|--------|
| {rule_id} | 规则ID | "1.2" |
| {rule_name} | 规则名称 | "内存搬移" |
| {rule_type} | 规则类型 | "内存管理-安全" |
| {severity} | 优先级 | "高" |
| {rule_value} | 规则内容 | "禁止使用memcpy..." |
| {description} | 规则描述 | "内存复制安全规范" |
| {language} | 编程语言 | "C" |

## 使用示例

### 示例1：内存管理规则

```python
# 创建专门针对内存管理的自定义prompt
memory_prompt = PromptTemplate(
    instruction="请特别关注内存操作的安全性，检查是否存在内存泄漏、缓冲区溢出等问题。",
    requirement_template="""内存安全规则检查：
规则ID: {rule_id}
规则名称: {rule_name}
规则内容: {rule_value}

重点关注：
1. 内存分配和释放的配对
2. 缓冲区边界检查
3. 指针的有效性验证""",
    focus_points=["内存分配", "内存释放", "缓冲区溢出", "空指针检查"]
)
```

### 示例2：字符串操作规则

```python
# 创建专门针对字符串操作的自定义prompt
string_prompt = PromptTemplate(
    instruction="请检查字符串操作的安全性，确保不会发生缓冲区溢出。",
    requirement_template="""字符串安全规则检查：
规则ID: {rule_id}
规则名称: {rule_name}
规则内容: {rule_value}

检查要点：
1. 字符串长度检查
2. 缓冲区大小验证
3. 安全的字符串函数使用""",
    focus_points=["字符串长度", "缓冲区大小", "安全函数", "边界检查"]
)
```

## 最佳实践

### 1. 指令设计
- **具体明确**：指令应该明确指出要检查的具体问题
- **上下文相关**：考虑代码的上下文和调用关系
- **语言特定**：针对特定编程语言的特点设计指令

### 2. 要求模板设计
- **结构化**：使用清晰的层次结构组织要求
- **可读性**：使用易于理解的描述
- **完整性**：包含所有必要的检查点

### 3. 示例设计
- **典型场景**：提供典型的违规和正确示例
- **代码质量**：确保示例代码的质量和可读性
- **实用性**：示例应该贴近实际开发场景

### 4. 重点关注点设计
- **关键词**：使用准确的关键词描述检查点
- **覆盖性**：确保涵盖所有重要的检查方面
- **优先级**：按重要性排序重点关注点

## 测试和验证

### 运行测试
```bash
# 运行自定义prompt模板的单元测试
python -m pytest tests/domain/test_custom_prompt_rules.py -v

# 运行使用示例
python examples/custom_prompt_usage_example.py
```

### 验证规则加载
```python
from gate_keeper.application.service.rule.rule_service.manager import RuleManager

# 加载包含自定义prompt的规则文件
rule_manager = RuleManager("resource/编码规范_custom_prompt_example.md")
rules = rule_manager.load_rules()

# 检查自定义prompt规则
custom_rules = [rule for rule in rules if rule.has_custom_prompt()]
print(f"找到 {len(custom_rules)} 个自定义prompt规则")
```

## 注意事项

1. **性能考虑**：自定义prompt会增加规则文件的大小和加载时间
2. **维护性**：确保自定义prompt模板的可维护性和一致性
3. **测试覆盖**：为自定义prompt模板编写充分的测试用例
4. **文档更新**：及时更新相关文档和示例

## 故障排除

### 常见问题

1. **模板变量替换失败**
   - 检查变量名是否正确
   - 确保变量在规则对象中存在

2. **规则加载失败**
   - 检查Markdown标签语法是否正确
   - 验证Excel列名是否匹配

3. **自定义prompt未生效**
   - 确认规则对象是否正确创建
   - 检查`has_custom_prompt()`方法的返回值

### 调试技巧

```python
# 检查规则的自定义prompt状态
rule = CodeCheckRule(...)
print(f"是否有自定义prompt: {rule.has_custom_prompt()}")
print(f"自定义指令: {rule.get_effective_instruction()}")
print(f"重点关注点: {rule.get_focus_points()}")

# 测试变量替换
formatted = rule.format_requirement_with_variables(
    rule_id="1.2",
    rule_name="测试规则",
    rule_value="测试内容"
)
print(f"格式化结果: {formatted}")
```

## 总结

自定义prompt模板功能为GateKeeper提供了更强大的规则定制能力，通过为每个规则定义专门的提示词，可以显著提高代码分析的准确性和针对性。合理使用这个功能，可以更好地满足不同项目和团队的特定需求。 