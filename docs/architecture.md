# Git Keeper 核心架构

## 概述

Git Keeper 是一个基于 LLM 的代码质量检查工具，通过分析 Git 仓库的变更来检测代码规范问题。

## 核心架构

### 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Interfaces Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    CLI      │  │    API      │  │   Web UI    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Use Cases  │  │   Services  │  │    DTOs     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Domain Layer                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Entities  │  │   Rules     │  │ Value Objs  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     Git     │  │     LLM     │  │   Report    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  External Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Code Analyzer│  │ Git Platform│  │ LLM Client  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. 服务编排器 (ServiceOrchestrator)

**职责**: 管理所有服务的生命周期和依赖关系

**核心特性**:
- 延迟初始化：按需创建服务
- 依赖管理：自动处理服务间的依赖关系
- 资源复用：避免重复创建相同的服务
- 解耦：服务间不直接依赖，通过编排器协调

**关键方法**:
```python
class ServiceOrchestrator:
    def __init__(self, git_service: GitService, llm_client: LLMClient)
    
    @property
    def code_analyzer(self) -> RepositoryAnalyzer
    @property
    def context_manager(self) -> Optional[ContextManager]
    @property
    def llm_service(self) -> LLMService
```

### 2. 代码分析器 (RepositoryAnalyzer)

**职责**: 分析代码变更，识别受影响的函数

**核心功能**:
- 构建仓库索引
- 分析函数变更
- 识别调用链关系

**依赖关系**:
- GitService: 获取代码内容
- RepositoryIndex: 延迟构建，重量级
- StaticAnalyzer: 立即创建，轻量级

### 3. 上下文管理器 (ContextManager)

**职责**: 基于调用链的智能上下文选择，解决上下文膨胀问题

**核心特性**:
- 调用链分析
- 相关性计算
- 上下文优化
- 规则集成

**配置管理**:
```python
class ContextManagerConfig:
    max_context_size: int = 8000
    max_chain_depth: int = 3
    max_chains: int = 3
    use_optimized_context: bool = True
```

### 4. LLM服务 (LLMService)

**职责**: 调用LLM进行代码质量分析

**核心功能**:
- 生成优化提示词
- 并发LLM调用
- 结果解析
- 调用次数限制

**配置项**:
- `max_llm_calls_per_affected_function`: 每个函数最大调用次数
- `max_llm_calls_per_task`: 任务级调用限制
- `max_llm_calls_per_rule_group`: 规则组级调用限制

### 5. Git服务 (GitService)

**职责**: 处理Git仓库操作

**核心功能**:
- 分支管理
- 差异分析
- 文件内容获取
- 提交历史分析

## 核心流程

### MR分析流程

```
1. 初始化服务编排器
   ├── GitService
   └── LLMClient

2. 执行分析用例
   ├── 获取Git差异
   ├── 分析受影响的函数
   ├── 生成调用链上下文
   └── LLM分析

3. 生成报告
   ├── 结果聚合
   ├── 去重处理
   └── 报告输出
```

### 上下文构建流程

```
1. 识别变更函数
2. 构建调用链
3. 计算相关性
4. 选择最优上下文
5. 生成提示词
```

## 配置系统

### 核心配置项

```python
# 上下文优化配置
use_optimized_context = True
max_context_size = 8000
max_context_chains = 3
max_context_chain_depth = 3

# LLM调用限制
max_llm_calls_per_affected_function = 2
max_llm_calls_per_task = None
max_llm_calls_per_rule_group = None

# 规则分组配置
rule_grouping_strategy = "adaptive"
min_rule_group_size = 3
max_rule_group_size = 8
```

### 配置传播

配置从 `config.py` 开始，通过以下路径传播：

```
config.py → ContextManagerConfig → ContextManager → LLMService → UseCase
```

## 依赖关系

### 核心组件的职责和依赖关系

```
RepositoryAnalyzer
├── GitService (获取代码)
├── RepositoryIndex (延迟构建，重量级)
└── StaticAnalyzer (立即创建，轻量级)

ContextManager
└── StaticAnalyzer (需要已构建的 RepositoryIndex)

LLMService
├── LLMClient
├── StaticAnalyzer (通过 ContextManager 间接使用)
└── ContextManager (可选，用于上下文优化)
```

## 扩展点

### 1. LLM客户端扩展

支持多种LLM提供商：
- 华为云 Fuyao
- Ollama
- 其他自定义客户端

### 2. Git平台扩展

支持多种Git平台：
- Gitee
- CodeHub
- 其他Git平台

### 3. 代码分析器扩展

支持多种编程语言：
- C/C++
- Python
- 其他语言

## 设计原则

1. **单一职责**: 每个组件只负责一个明确的功能
2. **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
3. **开闭原则**: 对扩展开放，对修改封闭
4. **延迟初始化**: 按需创建资源，提高性能
5. **配置驱动**: 通过配置控制行为，提高灵活性 