# 用例和流程

## 核心用例

### 1. MR/PR 代码质量分析

**用例描述**: 分析合并请求中的代码变更，检测潜在的代码规范问题。

**参与者**: 开发者、代码审查者

**前置条件**: 
- 存在有效的Git仓库
- 有可用的LLM服务
- 配置了代码规范规则

**主流程**:

```
1. 初始化分析环境
   ├── 创建服务编排器
   ├── 配置Git服务
   └── 配置LLM服务

2. 获取代码变更
   ├── 获取MR/PR信息
   ├── 分析文件差异
   └── 识别变更类型（新增/修改/删除）

3. 分析受影响的函数
   ├── 构建仓库索引（延迟）
   ├── 识别变更的函数
   └── 分析函数调用关系

4. 生成优化上下文
   ├── 构建调用链
   ├── 计算相关性
   └── 选择最优上下文

5. LLM分析
   ├── 生成提示词
   ├── 并发调用LLM
   └── 解析分析结果

6. 生成报告
   ├── 聚合分析结果
   ├── 去重处理
   └── 输出报告
```

**后置条件**: 生成代码质量分析报告

### 2. 分支代码质量分析

**用例描述**: 分析整个分支的代码质量，适用于分支合并前的全面检查。

**参与者**: 开发者、项目管理者

**主流程**:

```
1. 初始化分析环境
2. 获取分支信息
3. 分析分支中的所有代码文件
4. 识别所有函数和调用关系
5. 批量LLM分析
6. 生成分支质量报告
```

## 核心流程详解

### MR分析流程

#### 1. 初始化阶段

```python
# 创建服务编排器
orchestrator = ServiceOrchestrator(
    git_service=git_service,
    llm_client=llm_client
)

# 创建分析用例
analyze_usecase = AnalyzeMRUseCase(
    git_service=orchestrator.git_service,
    llm_service=orchestrator.llm_service,
    repo_analyzer=orchestrator.code_analyzer
)
```

#### 2. 差异分析阶段

```python
# 获取Git差异
diffs = git_service.get_diff_by_mr_id(
    repo_dir=repo_dir,
    mr_id=mr_id,
    base_branch=base_branch,
    dev_branch=dev_branch,
    base_commit_sha=base_commit_sha,
    dev_commit_sha=dev_commit_sha
)

# 处理每个变更的文件
for patched_file in diffs:
    if determine_language_by_filename(patched_file.path):
        diff_result = process_file_change(patched_file)
        diff_results.append(diff_result)
```

#### 3. 函数分析阶段

```python
# 分析受影响的函数
affected_funcs = repo_analyzer.analyze_functions(
    repo_dir=repo_dir,
    file_path=patched_file.path,
    file_content=new_content,
    changed_lines=changed_lines,
    max_context_chain_depth=max_context_chain_depth
)
```

#### 4. 上下文构建阶段

```python
# 生成优化上下文
if context_manager and use_optimized_context:
    contexts = context_manager.generate_optimized_contexts(
        af=affected_function,
        max_chains=config.max_chains
    )
    
    # 为每个调用链生成上下文
    for context in contexts:
        prompt_content = context_manager.format_context_for_prompt(context)
```

#### 5. LLM分析阶段

```python
# 生成提示词
prompt = llm_service._generate_prompt(
    af=affected_function,
    file_path=file_path,
    rules=applicable_rules
)

# 并发调用LLM
with ThreadPoolExecutor(max_workers=max_workers) as executor:
    futures = []
    for rule_group in rule_groups:
        future = executor.submit(
            llm_service._analyze_single_function,
            af=affected_function,
            file_path=file_path,
            rule_manager=rule_manager,
            grouped_rules_dict={rule_group: rules}
        )
        futures.append(future)
```

#### 6. 结果聚合阶段

```python
# 聚合分析结果
for diff in analyze_results.diffs:
    for affected_function in diff.affected_functions:
        if affected_function.llm_results:
            # 筛选违规结果
            violations = [result for result in affected_function.llm_results 
                         if result and not result.is_pass]
            
            # 去重处理
            deduplicated_violations = violation_deduplication_service.deduplicate_violations(violations)
```

### 上下文构建流程

#### 1. 调用链构建

```python
def build_call_chains(self, af: AffectedFunction) -> List[List[str]]:
    """构建从变更函数出发的调用链"""
    chains = []
    
    # 获取直接调用者
    callers = self.static_analyzer.get_function_callers(af.name, af.filepath)
    
    # 递归构建调用链
    for caller in callers:
        chain = self._build_chain_recursive(caller, max_depth=self.config.max_chain_depth)
        if chain:
            chains.append(chain)
    
    return chains
```

#### 2. 相关性计算

```python
def calculate_chain_relevance(self, af: AffectedFunction, chain: List[str], functions: List[Function]) -> float:
    """计算调用链的相关性分数"""
    score = 0.0
    
    # 调用链长度权重
    chain_length_score = 1.0 / len(chain) if chain else 0.0
    score += chain_length_score * self.config.relevance_weights["chain_length"]
    
    # 函数位置权重
    position_score = self._calculate_position_score(af, functions)
    score += position_score * self.config.relevance_weights["function_position"]
    
    # 调用密度权重
    density_score = self._calculate_call_density(chain, functions)
    score += density_score * self.config.relevance_weights["call_density"]
    
    # 文件相关性权重
    file_score = self._calculate_file_relevance(af, functions)
    score += file_score * self.config.relevance_weights["file_relevance"]
    
    return score
```

#### 3. 上下文选择

```python
def select_optimal_contexts(self, contexts: List[CallChainContext], max_chains: int) -> List[CallChainContext]:
    """选择最优的上下文"""
    # 按相关性排序
    sorted_contexts = sorted(contexts, key=lambda x: x.relevance_score, reverse=True)
    
    # 应用大小限制
    selected = []
    total_size = 0
    
    for context in sorted_contexts:
        if len(selected) >= max_chains:
            break
            
        if total_size + context.context_size <= self.config.max_context_size:
            selected.append(context)
            total_size += context.context_size
    
    return selected
```

### 规则分组流程

#### 1. 规则加载

```python
def load_rules(self) -> List[CodeCheckRule]:
    """加载代码规范规则"""
    rules = []
    
    # 从Excel文件加载规则
    if self.rule_file_path:
        rules.extend(self._load_rules_from_excel())
    
    # 从URL加载规则
    if self.rule_file_url:
        rules.extend(self._load_rules_from_url())
    
    return rules
```

#### 2. 规则分组

```python
def group_rules(self, rules: List[CodeCheckRule]) -> Dict[str, List[CodeCheckRule]]:
    """智能规则分组"""
    if self.grouping_strategy == "category":
        return self._group_by_category(rules)
    elif self.grouping_strategy == "similarity":
        return self._group_by_similarity(rules)
    elif self.grouping_strategy == "adaptive":
        return self._group_adaptively(rules)
    else:
        return self._group_by_category(rules)
```

#### 3. 自适应分组

```python
def _group_adaptively(self, rules: List[CodeCheckRule]) -> Dict[str, List[CodeCheckRule]]:
    """自适应分组策略"""
    groups = {}
    
    # 首先按类别分组
    category_groups = self._group_by_category(rules)
    
    # 对每个类别组进行相似度分组
    for category, category_rules in category_groups.items():
        if len(category_rules) <= self.max_rule_group_size:
            groups[category] = category_rules
        else:
            # 进行相似度分组
            similarity_groups = self._group_by_similarity(category_rules)
            for i, group in enumerate(similarity_groups.values()):
                group_name = f"{category}_similarity_{i}"
                groups[group_name] = group
    
    return groups
```

## 错误处理流程

### 1. LLM调用失败处理

```python
def _call_llm_with_retry(self, prompt_str: str, max_retries: int = 3) -> Dict:
    """带重试的LLM调用"""
    for attempt in range(max_retries):
        try:
            return self.client.generate(prompt_str, self.parameters)
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"LLM调用失败，已重试{max_retries}次: {e}")
                raise
            else:
                logger.warning(f"LLM调用失败，第{attempt + 1}次重试: {e}")
                time.sleep(2 ** attempt)  # 指数退避
```

### 2. 配置验证错误处理

```python
def validate_config(self) -> List[str]:
    """配置验证"""
    errors = []
    
    try:
        # 类型检查和转换
        if isinstance(self.max_context_size, str):
            self.max_context_size = int(self.max_context_size)
    except (ValueError, TypeError):
        self.max_context_size = 8000
        errors.append("max_context_size 类型错误，使用默认值 8000")
    
    return errors
```

### 3. 文件处理错误处理

```python
def process_file_change(self, patched_file) -> DiffResult:
    """处理文件变更，包含错误处理"""
    try:
        if patched_file.is_removed_file:
            return self._process_deleted_file(patched_file)
        elif patched_file.is_added_file:
            return self._process_new_file(patched_file)
        else:
            return self._process_modified_file(patched_file)
    except Exception as e:
        logger.error(f"处理文件 {patched_file.path} 时发生错误: {e}")
        return DiffResult(
            file_path=patched_file.path,
            affected_functions=[],
            error=str(e)
        )
```

## 性能优化策略

### 1. 延迟初始化

- RepositoryIndex: 只在需要时构建
- ContextManager: 按需创建
- LLMService: 延迟初始化

### 2. 并发处理

- 多线程LLM调用
- 并行文件处理
- 异步规则加载

### 3. 缓存机制

- 仓库索引缓存
- 上下文缓存
- 规则缓存

### 4. 调用限制

- 函数级调用限制
- 任务级调用限制
- 规则组级调用限制 