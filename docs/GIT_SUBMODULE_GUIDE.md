# Git Submodule 管理指南

## 概述

Git Submodule 允许你将一个Git仓库作为另一个Git仓库的子目录，这对于管理项目依赖、共享代码模块非常有用。

## 当前项目配置

本项目包含以下submodule：

### 1. Cursor规则配置
- **路径**: `.cursor/rules`
- **远程仓库**: `https://gitee.com/archertest277/coding_gundam.git`
- **分支**: `main`
- **用途**: Cursor编辑器的编码规范和项目规则

### 2. 代码分析器
- **路径**: `gate_keeper/external/code_analyzer`
- **远程仓库**: `https://gitee.com/archertest277/code_analyzer.git`
- **分支**: `main`
- **用途**: 代码静态分析工具

## 基本操作

### 1. 克隆包含submodule的项目

```bash
# 方法一：递归克隆（推荐）
git clone --recursive <repository-url>

# 方法二：分步克隆
git clone <repository-url>
git submodule init
git submodule update
```

### 2. 查看submodule状态

```bash
# 查看所有submodule状态
git submodule status

# 查看详细信息
git submodule foreach git status
```

### 3. 更新submodule

```bash
# 更新所有submodule到最新版本
git submodule update --remote

# 更新特定submodule
git submodule update --remote .cursor/rules

# 进入submodule目录更新
cd .cursor/rules
git pull origin main
cd ../..
```

### 4. 添加新的submodule

```bash
# 添加新的submodule
git submodule add <repository-url> <path>

# 示例
git submodule add https://github.com/username/repo.git external/dependency
```

### 5. 删除submodule

```bash
# 1. 从.gitmodules中删除配置
git config -f .gitmodules --remove-section submodule.<path>

# 2. 从.git/config中删除配置
git config -f .git/config --remove-section submodule.<path>

# 3. 从索引中删除
git rm --cached <path>

# 4. 删除目录
rm -rf <path>

# 5. 提交更改
git commit -m "Remove submodule <path>"
```

## 高级操作

### 1. 批量操作

```bash
# 对所有submodule执行命令
git submodule foreach 'git checkout main && git pull'

# 初始化所有submodule
git submodule init

# 更新所有submodule
git submodule update
```

### 2. 分支管理

```bash
# 切换到特定分支
git submodule foreach 'git checkout <branch-name>'

# 创建新分支
cd .cursor/rules
git checkout -b feature/new-rule
cd ../..
```

### 3. 提交submodule更改

```bash
# 1. 进入submodule目录
cd .cursor/rules

# 2. 提交更改
git add .
git commit -m "Update cursor rules"

# 3. 推送到远程
git push origin main

# 4. 返回主项目
cd ../..

# 5. 更新主项目中的submodule引用
git add .cursor/rules
git commit -m "Update cursor rules submodule"
```

## 配置文件说明

### .gitmodules 文件结构

```ini
[submodule "path/to/submodule"]
    path = path/to/submodule
    url = https://github.com/username/repo.git
    branch = main
    active = true
```

### 配置选项

- **path**: submodule在项目中的路径
- **url**: 远程仓库地址
- **branch**: 默认分支
- **active**: 是否激活（可选）

## 最佳实践

### 1. 版本管理

- 始终在主项目中记录submodule的具体commit
- 定期更新submodule到稳定版本
- 避免在主项目中直接修改submodule内容

### 2. 团队协作

- 团队成员克隆项目时使用 `--recursive` 参数
- 在README中说明submodule的作用和更新方法
- 建立submodule更新流程

### 3. 故障排除

```bash
# 重置submodule到指定commit
git submodule update --init --recursive

# 强制更新submodule
git submodule update --force --recursive

# 清理submodule缓存
git submodule deinit -f .cursor/rules
git submodule update --init .cursor/rules
```

## 常见问题

### 1. Submodule显示为"modified"
```bash
# 检查是否有未提交的更改
cd .cursor/rules
git status
git add .
git commit -m "Update rules"
cd ../..
git add .cursor/rules
git commit -m "Update submodule reference"
```

### 2. Submodule更新失败
```bash
# 重置submodule
git submodule deinit -f .cursor/rules
git submodule update --init .cursor/rules
```

### 3. 克隆时submodule为空
```bash
# 初始化并更新submodule
git submodule init
git submodule update
```

## 自动化脚本

### 更新所有submodule的脚本

```bash
#!/bin/bash
# update_submodules.sh

echo "Updating all submodules..."

# 更新所有submodule
git submodule update --remote

# 检查是否有更新
if git diff --quiet .gitmodules; then
    echo "No submodule updates found."
else
    echo "Submodules updated. Please review and commit changes."
    git status
fi
```

### 初始化项目的脚本

```bash
#!/bin/bash
# init_project.sh

echo "Initializing project with submodules..."

# 初始化submodule
git submodule init

# 更新submodule
git submodule update

# 检查状态
git submodule status

echo "Project initialization complete!"
```

## 总结

Git Submodule 是管理项目依赖的强大工具，但需要团队成员都了解其使用方法。通过合理的配置和规范的操作流程，可以有效地管理复杂的项目结构。 