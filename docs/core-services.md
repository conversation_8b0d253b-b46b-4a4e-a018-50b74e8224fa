# 核心服务详解

## 服务编排器 (ServiceOrchestrator)

### 设计目标

解决组件间循环依赖和紧耦合问题，通过组合模式管理复杂的服务依赖关系。

### 核心实现

```python
class ServiceOrchestrator:
    def __init__(self, git_service: GitService, llm_client: LLMClient):
        self.git_service = git_service
        self.llm_client = llm_client
        
        # 延迟初始化的服务
        self._code_analyzer: Optional[RepositoryAnalyzer] = None
        self._context_manager: Optional[ContextManager] = None
        self._llm_service: Optional[LLMService] = None
        self._static_analyzer: Optional[StaticAnalyzer] = None
```

### 延迟初始化策略

1. **代码分析器**: 按需创建，依赖GitService
2. **上下文管理器**: 需要先有StaticAnalyzer
3. **LLM服务**: 依赖LLMClient和可选的StaticAnalyzer

### 依赖管理

```python
@property
def llm_service(self) -> LLMService:
    if self._llm_service is None:
        self._llm_service = LLMService(
            client=self.llm_client,
            static_analyzer=self._static_analyzer
        )
    
    # 动态设置上下文管理器
    if (self._context_manager is not None and 
        self._llm_service.context_manager is None):
        self._llm_service.context_manager = self._context_manager
    
    return self._llm_service
```

## 代码分析器 (RepositoryAnalyzer)

### 职责

分析代码变更，识别受影响的函数和调用链关系。

### 核心功能

1. **仓库索引构建**: 延迟构建，重量级操作
2. **函数变更分析**: 识别受影响的函数
3. **调用链分析**: 构建函数间的调用关系

### 实现特点

- **延迟构建**: RepositoryIndex在需要时才构建
- **轻量级分析**: StaticAnalyzer立即创建
- **缓存机制**: 避免重复分析

## 上下文管理器 (ContextManager)

### 问题背景

传统方法将所有相关代码都放入上下文，导致上下文膨胀，影响LLM分析效果。

### 解决方案

基于调用链的智能上下文选择：

1. **调用链构建**: 分析函数间的调用关系
2. **相关性计算**: 计算每个调用链的相关性分数
3. **上下文选择**: 选择最相关的调用链
4. **上下文优化**: 控制上下文大小和质量

### 核心配置

```python
@dataclass
class ContextManagerConfig:
    max_context_size: int = 8000          # 单个上下文的最大字符数
    max_chain_depth: int = 3              # 单条调用链的最大深度
    max_chains: int = 3                   # 每个函数最多包含的调用链数量
    use_optimized_context: bool = True    # 是否使用优化的上下文
```

### 调用链上下文

```python
@dataclass
class CallChainContext:
    chain: List[str]          # 调用链：['main', 'process_data', 'validate_input']
    functions: List[Function] # 链中函数的完整定义
    relevance_score: float    # 相关性评分
    context_size: int         # 上下文大小（字符数）
```

### 相关性计算

使用多维度权重计算：

```python
relevance_weights = {
    "chain_length": 0.3,           # 调用链长度权重
    "function_position": 0.3,      # 函数位置权重
    "call_density": 0.2,           # 调用密度权重
    "file_relevance": 0.2          # 文件相关性权重
}
```

## LLM服务 (LLMService)

### 核心功能

1. **提示词生成**: 基于上下文和规则生成优化提示词
2. **并发调用**: 支持多线程并发LLM调用
3. **结果解析**: 解析LLM返回的分析结果
4. **调用限制**: 防止LLM调用次数爆炸

### 调用次数限制

```python
# 三级调用限制机制
max_llm_calls_per_affected_function = 2   # 每个被修改函数最多调用次数
max_llm_calls_per_task = None             # 任务级总调用次数限制
max_llm_calls_per_rule_group = None       # 规则组级调用次数限制
```

### 上下文优化集成

```python
def _generate_prompt(self, af: AffectedFunction, file_path: str, rules: List[CodeCheckRule]) -> Prompt:
    if self.context_manager and self.use_optimized_context:
        # 使用ContextManager生成包含规则的prompt内容
        prompt_contents = self.context_manager.generate_prompt_with_rules(af, file_path)
        input_content = list(prompt_contents.values())[0] if prompt_contents else self.context_manager.generate_prompt_content(af, file_path)
    else:
        # 使用简单上下文
        input_content = self._generate_simple_prompt_content(af, file_path)
```

## Git服务 (GitService)

### 核心功能

1. **分支管理**: 保持分支与远程同步
2. **差异分析**: 获取MR/PR的代码变更
3. **文件操作**: 获取指定版本的文件内容
4. **提交历史**: 分析提交历史信息

### 平台适配

支持多种Git平台：

- **Gitee**: 通过GiteeClient实现
- **CodeHub**: 通过CodeHubClient实现
- **通用Git**: 通过Git命令行操作

### 文件变更处理

```python
def get_diff_by_mr_id(self, repo_dir: str, mr_id: int, base_branch: str, dev_branch: str, base_commit_sha: str, dev_commit_sha: str) -> List[PatchedFile]:
    # 获取MR的差异信息
    # 支持不同类型的文件变更：新增、修改、删除
```

## 规则管理 (RuleManager)

### 规则分组

智能规则分组策略：

1. **按类别分组**: 根据规则类别进行分组
2. **按相似度分组**: 根据规则内容相似度分组
3. **自适应分组**: 综合考虑类别、相似度和组大小

### 分组配置

```python
rule_grouping_strategy = "adaptive"       # 分组策略
min_rule_group_size = 3                   # 最小分组大小
max_rule_group_size = 8                   # 最大分组大小
target_rule_group_size = 5                # 目标分组大小
rule_similarity_threshold = 0.7           # 相似度分组阈值
```

## 去重服务

### 评论去重 (CommentDeduplicationService)

防止重复的评论信息，提高分析效率。

### 违规去重 (ViolationDeduplicationService)

对检测到的违规项进行相似度去重，避免重复报告。

## 报告服务 (ReportService)

### 功能特性

1. **多平台支持**: 支持不同Git平台的报告格式
2. **结果聚合**: 将分析结果聚合为统一格式
3. **报告生成**: 生成可读性强的分析报告

### 报告格式

支持多种报告格式：
- 评论形式（直接在MR/PR中评论）
- 文件报告（生成独立的报告文件）
- 控制台输出（命令行工具使用） 