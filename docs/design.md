# Git Keeper 系统设计文档

## 概述

Git Keeper 是一个基于 LLM 的代码质量检查系统，采用分层架构设计，支持多种 Git 平台和 LLM 提供商。系统通过智能的规则预筛选、上下文感知的代码分析和可配置的检查流程，为开发团队提供高质量的代码审查服务。

## 系统架构

### 整体架构

系统采用经典的分层架构模式，从上到下分为五个层次：

```
┌─────────────────────────────────────────────────────────────┐
│                    接口层 (Interface Layer)                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    CLI      │  │    API      │  │   Web UI    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   应用层 (Application Layer)                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Use Cases  │  │   Services  │  │ Prescreening│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   领域层 (Domain Layer)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Entities  │  │   Rules     │  │ Value Objs  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                基础设施层 (Infrastructure Layer)             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │     Git     │  │     LLM     │  │   Report    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  外部层 (External Layer)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Code Analyzer│  │ Git Platform│  │ LLM Client  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件详解

#### 1. 服务编排器 (ServiceOrchestrator)

**职责**: 管理所有服务的生命周期和依赖关系

**核心特性**:
- 延迟初始化：按需创建服务
- 依赖管理：自动处理服务间的依赖关系
- 资源复用：避免重复创建相同的服务
- 解耦：服务间不直接依赖，通过编排器协调

#### 2. 规则管理器 (RuleManager)

**职责**: 管理代码检查规则的加载、筛选和分组

**核心功能**:
- 多格式支持：Excel、YAML、Markdown
- 智能预筛选：基于正则表达式和语义匹配
- 灵活分组：支持多种分组策略
- 缓存优化：规则解析结果缓存

**预筛选系统**:
- **无预筛选 (none)**: 使用所有规则
- **正则预筛选 (regex)**: 基于match_regex字段筛选
- **语义预筛选 (embedding)**: 基于代码语义相似度筛选
- **混合策略 (hybrid)**: 结合正则和语义匹配

#### 3. LLM服务 (LLMService)

**职责**: 调用LLM进行代码质量分析

**核心功能**:
- 生成优化提示词
- 并发LLM调用
- 结果解析和验证
- 调用次数限制

#### 4. Git服务 (GitService)

**职责**: 处理Git仓库操作

**核心功能**:
- 分支管理
- 差异分析
- 文件内容获取
- 提交历史分析

#### 5. 仓库分析器 (RepositoryAnalyzer)

**职责**: 分析代码结构和调用关系

**核心功能**:
- 受影响函数识别
- 调用链分析
- 上下文构建
- 代码结构分析

## 核心流程设计

### MR检查主流程

1. **初始化阶段**: 加载配置，初始化服务，验证参数
2. **分析阶段**: 获取Git差异，分析受影响函数，构建调用链上下文
3. **检查阶段**: 加载检查规则，规则预筛选和分组，LLM分析调用
4. **报告阶段**: 结果聚合，去重处理，生成报告

### 规则预筛选流程

预筛选系统是Git Keeper的核心创新之一，通过智能筛选减少不必要的LLM调用：

1. **输入验证**: 检查规则列表和代码元素的有效性
2. **策略选择**: 根据配置选择预筛选策略
3. **规则筛选**: 执行具体的筛选逻辑
4. **结果优化**: 按相关性排序和性能统计
5. **降级处理**: 异常情况下的安全降级

## 预筛选系统详解

### 策略对比

| 策略 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| none | 简单可靠，不遗漏规则 | 性能较低 | 规则数量少，确保完整性 |
| regex | 快速准确，基于明确模式 | 需要regex字段，无法处理语义 | 有明确代码模式的规则 |
| embedding | 智能语义匹配 | 需要API调用，性能开销大 | 需要语义理解的场景 |
| hybrid | 综合两种优势 | 配置复杂，开销最大 | 追求最佳准确性 |

### 性能优化

1. **缓存机制**:
   - 正则表达式编译缓存
   - Embedding向量缓存
   - 规则索引缓存

2. **降级策略**:
   - 超时自动降级
   - 服务不可用降级
   - 错误处理降级

3. **性能监控**:
   - 处理时间统计
   - 缓存命中率
   - 降级触发频率

## 配置架构

### 配置层次结构

1. **全局配置 (config.py)**: 系统默认配置
2. **环境配置 (.env)**: 环境特定配置
3. **用户配置**: 用户自定义配置
4. **运行时配置**: 动态参数

### 核心配置模块

#### LLM配置
```python
llm_config = {
    "provider": "ollama",  # ollama, openai, claude
    "model": "qwen2.5-coder:7b",
    "temperature": 0.1,
    "max_tokens": 4000,
    "timeout": 30,
    "max_calls_per_function": 3
}
```

#### 预筛选配置
```python
prescreening_config = {
    "strategy": "regex",  # none, regex, embedding, hybrid
    "regex_timeout": 1.0,
    "embedding_model": "text-embedding-ada-002",
    "embedding_similarity_threshold": 0.7,
    "hybrid_regex_weight": 0.3,
    "hybrid_embedding_weight": 0.7,
    "enable_fallback": True,
    "max_processing_time": 5.0
}
```

#### 规则配置
```python
rule_config = {
    "file_path": "eval/datasets/c_evalset_real.yaml",
    "grouping_strategy": "adaptive",  # category, similarity, adaptive
    "min_group_size": 3,
    "max_group_size": 10,
    "target_group_size": 5
}
```

## 数据模型设计

### 核心实体

#### CodeCheckRule
```python
class CodeCheckRule(BaseModel):
    id: str
    name: str
    description: str
    category: List[str]
    severity: str
    rule_value: str
    match_regex: str = ""      # 新增：用于预筛选
    suggestion: str = ""       # 新增：检查建议
    languages: List[str] = []
```

#### TestCase
```python
class TestCase(BaseModel):
    # 继承CodeCheckRule的所有字段
    # 新增评测相关字段
    match_regex: str = ""
    suggestion: str = ""
    prompt_template: Optional[PromptTemplate] = None
```

#### AnalyzeLLMResult
```python
class AnalyzeLLMResult(BaseModel):
    is_pass: bool
    reason: str
    violations: List[ViolationItem]
    code: str
    prompt: Optional[str] = None
    response: Optional[str] = None
    call_id: Optional[str] = None
```

### 值对象

- **DiffResult**: Git差异结果
- **AffectedFunction**: 受影响的函数
- **ViolationItem**: 违规项详情
- **CallChainContext**: 调用链上下文

## 扩展性设计

### 插件化架构

1. **LLM提供商插件**: 统一的LLM接口，可插拔的提供商实现
2. **Git平台插件**: 标准化的Git操作接口，多平台适配器
3. **规则格式插件**: 可扩展的规则解析器，多格式支持
4. **预筛选策略插件**: 可插拔的筛选策略，自定义匹配算法

### 配置驱动设计

- 通过配置文件控制系统行为
- 支持运行时配置更新
- 功能开关控制
- 环境特定配置

## 性能优化策略

### 缓存策略

1. **多层缓存**: L1内存缓存、L2本地文件缓存、L3分布式缓存
2. **缓存内容**: LLM响应、代码分析结果、规则解析结果、Embedding向量
3. **缓存策略**: LRU淘汰、TTL过期、缓存预热、缓存统计

### 并发处理

1. **异步处理**: 异步LLM调用、并行代码分析、流水线处理
2. **资源池管理**: 连接池、线程池、协程池
3. **负载均衡**: 请求分发、资源调度、故障转移

## 错误处理和容错

### 分层错误处理

1. **业务逻辑错误**: 输入验证错误、业务规则违反、数据一致性错误
2. **技术实现错误**: 网络连接错误、文件操作错误、解析错误
3. **外部服务错误**: LLM服务不可用、Git平台API错误、第三方服务超时

### 降级策略

1. **服务降级**: LLM服务降级到规则检查、复杂分析降级到简单检查
2. **功能降级**: 预筛选降级到全量检查、深度分析降级到表面检查
3. **性能降级**: 并发降级到串行、实时降级到异步

## 监控和可观测性

### 日志系统

1. **结构化日志**: JSON格式、统一字段、上下文信息
2. **日志分级**: DEBUG、INFO、WARN、ERROR
3. **日志聚合**: 集中收集、实时分析、告警通知

### 性能监控

1. **关键指标**: 响应时间、吞吐量、错误率、资源使用率
2. **业务指标**: 检查成功率、规则命中率、预筛选效果
3. **技术指标**: 缓存命中率、数据库性能、网络延迟

## 部署架构

### 单机部署
- 适用于小团队、开发环境
- 部署简单、资源要求低

### 分布式部署
- 适用于大规模团队、生产环境
- 服务拆分、水平扩展、高可用性

### 容器化部署
- 适用于云原生环境、DevOps流程
- 容器化打包、编排管理、弹性伸缩

## 未来规划

### 短期目标 (3-6个月)
1. 更多LLM提供商和Git平台支持
2. 预筛选算法和性能优化
3. Web界面和用户体验优化

### 中期目标 (6-12个月)
1. 机器学习模型集成和智能规则推荐
2. 插件市场和第三方集成
3. 多租户支持和权限管理增强

### 长期目标 (1-2年)
1. 开发者社区和插件生态建设
2. 云原生架构和AI能力增强
3. SaaS服务和商业化发展

## 总结

Git Keeper系统采用现代化的分层架构设计，通过智能的规则预筛选、灵活的配置管理和完善的错误处理机制，为代码质量检查提供了高效、可靠的解决方案。系统具备良好的扩展性和可维护性，能够适应不同规模和场景的需求。
