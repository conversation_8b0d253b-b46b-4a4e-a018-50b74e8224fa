# 配置和部署

## 配置系统

### 配置层次结构

Git Keeper 采用分层配置系统，支持环境特定的配置：

```
config.py (基础配置)
├── config_home.py (家庭环境)
├── config_dev.py (开发环境)
├── config_prod.py (生产环境)
└── config_test.py (测试环境)
```

### 环境配置

通过环境变量 `GK_ENV` 控制使用哪个配置：

```python
ENV = os.environ.get("GK_ENV", "test")  # 默认使用test环境
env_map = {
    "home": "config_home",
    "dev": "config_dev", 
    "prod": "config_prod",
    "test": "config_test"
}
```

### 核心配置项

#### 1. Git 配置

```python
# Git 仓库配置
repo_dir = "not_set"              # 仓库目录
repo_project_id = "not_set"       # 项目ID
repo_url = "not_set"              # 仓库URL
mr_id = "not_set"                 # MR/PR ID
token = "not_set"                 # 访问令牌
devBranch = "not_set"             # 开发分支
baseBranch = "not_set"            # 基础分支
```

#### 2. LLM 配置

```python
# LLM 服务配置
llm_model_id = "not_set"          # 模型ID
llm_concurrent = "not_set"        # 并发数
default_llm_max_tokens = "not_set" # 最大token数
```

#### 3. 上下文优化配置

```python
# 上下文优化配置
use_optimized_context = True      # 是否使用基于调用链的优化上下文
max_context_size = 8000           # 单个上下文的最大字符数
max_context_chains = 3            # 每个函数最多包含的调用链数量
max_context_chain_depth = 3       # 单条调用链的最大深度（半径）

# 上下文管理器详细配置
context_relevance_weight_chain_length = 0.3      # 调用链长度权重
context_relevance_weight_function_position = 0.3 # 函数位置权重
context_relevance_weight_call_density = 0.2      # 调用密度权重
context_relevance_weight_file_relevance = 0.2    # 文件相关性权重

# 选择策略配置
context_function_selection_strategy = "balanced"  # 函数选择策略
context_selection_strategy = "relevance_first"    # 上下文选择策略
```

#### 4. LLM调用限制配置

```python
# LLM调用次数限制配置
max_llm_calls_per_task = None                    # 每次任务的总LLM调用次数限制
max_llm_calls_per_affected_function = 2          # 每个被修改函数的LLM调用次数限制
max_llm_calls_per_rule_group = None              # 每个规则组的LLM调用次数限制
```

#### 5. 规则分组配置

```python
# 规则分组配置
rule_grouping_strategy = "adaptive"              # 分组策略
min_rule_group_size = 3                          # 最小分组大小
max_rule_group_size = 8                          # 最大分组大小
target_rule_group_size = 5                       # 目标分组大小
rule_similarity_threshold = 0.7                  # 相似度分组阈值
```

#### 6. 缓存配置

```python
# 缓存配置
repo_cache_dir = "not_set"                       # 仓库缓存目录
max_cache_repos = "not_set"                      # 最大缓存仓库数
max_cache_branches = "not_set"                   # 最大缓存分支数
max_cache_branch_commits = "not_set"             # 最大缓存提交数
```

#### 7. 规则配置

```python
# 规则配置
rule_file_path = "not_set"                       # 规则文件路径
rule_include_sheets = "not_set"                  # 包含的工作表
rule_merge_on = "not_set"                        # 合并字段
rule_file_url = "not_set"                        # 规则文件URL
```

### 环境变量配置

支持通过环境变量覆盖配置：

```bash
# 设置环境
export GK_ENV=prod

# 上下文配置
export MAX_CONTEXT_CHAIN_DEPTH=5
export MAX_CONTEXT_CHAINS=7
export USE_OPTIMIZED_CONTEXT=true
export MAX_LLM_CALLS_PER_AFFECTED_FUNCTION=3

# Git配置
export GIT_TOKEN=your_token_here
export REPO_URL=https://gitee.com/your/repo.git

# LLM配置
export LLM_ENDPOINT=https://your-llm-endpoint.com
export LLM_MODEL_ID=your_model_id
```

## 部署方式

### 1. 本地开发环境

#### 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export GK_ENV=dev
export GIT_TOKEN=your_token
export LLM_ENDPOINT=your_llm_endpoint
```

#### 配置文件

创建 `gate_keeper/config/config_dev.py`:

```python
# 开发环境配置
DEBUG = True

# Git配置
git_platform = "gitee"
token = "your_gitee_token"

# LLM配置
llm_model_id = "your_model_id"
llm_concurrent = 3

# 上下文配置
use_optimized_context = True
max_context_chain_depth = 3
max_context_chains = 3

# 日志配置
log_level = "DEBUG"
```

### 2. Docker 部署

#### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制应用代码
COPY . .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 设置环境变量
ENV GK_ENV=prod
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "gate_keeper.interfaces.cli.main"]
```

#### Docker Compose

```yaml
version: '3.8'

services:
  git-keeper:
    build: .
    environment:
      - GK_ENV=prod
      - GIT_TOKEN=${GIT_TOKEN}
      - LLM_ENDPOINT=${LLM_ENDPOINT}
      - LLM_MODEL_ID=${LLM_MODEL_ID}
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    ports:
      - "8000:8000"
```

### 3. 生产环境部署

#### 系统要求

- Python 3.11+
- Git 2.0+
- 足够的内存（建议4GB+）
- 网络访问权限

#### 部署步骤

1. **安装应用**

```bash
# 克隆代码
git clone https://github.com/your-org/git-keeper.git
cd git-keeper

# 安装依赖
pip install -e .
```

2. **配置环境**

```bash
# 创建生产配置
cp gate_keeper/config/config_test.py gate_keeper/config/config_prod.py

# 编辑生产配置
vim gate_keeper/config/config_prod.py
```

3. **设置环境变量**

```bash
export GK_ENV=prod
export GIT_TOKEN=your_production_token
export LLM_ENDPOINT=your_production_llm_endpoint
```

4. **启动服务**

```bash
# 直接运行
python -m gate_keeper.interfaces.cli.main

# 或使用systemd服务
sudo systemctl start git-keeper
```

### 4. 云原生部署

#### Kubernetes 部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: git-keeper
spec:
  replicas: 2
  selector:
    matchLabels:
      app: git-keeper
  template:
    metadata:
      labels:
        app: git-keeper
    spec:
      containers:
      - name: git-keeper
        image: your-registry/git-keeper:latest
        env:
        - name: GK_ENV
          value: "prod"
        - name: GIT_TOKEN
          valueFrom:
            secretKeyRef:
              name: git-keeper-secrets
              key: git-token
        - name: LLM_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: git-keeper-secrets
              key: llm-endpoint
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 监控和日志

### 日志配置

```python
# 日志配置
log_dir = "logs"                  # 日志目录
log_level = "INFO"                # 日志级别

# 上下文日志配置
context_enable_detailed_logging = False  # 是否启用详细日志
context_log_context_building = True      # 是否记录上下文构建日志
```

### 健康检查

```python
# 健康检查端点
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    }
```

### 性能监控

```python
# 性能指标
- LLM调用次数
- 分析耗时
- 内存使用
- 缓存命中率
```

## 安全配置

### 1. 令牌管理

```python
# 使用环境变量存储敏感信息
token = os.environ.get('GIT_TOKEN')
llm_api_key = os.environ.get('LLM_API_KEY')
```

### 2. 网络安全

```python
# 限制网络访问
allowed_git_platforms = ['gitee.com', 'github.com']
allowed_llm_endpoints = ['your-llm-endpoint.com']
```

### 3. 文件权限

```bash
# 设置适当的文件权限
chmod 600 config/prod_config.py
chmod 700 logs/
```

## 故障排除

### 常见问题

1. **配置加载失败**
   - 检查环境变量设置
   - 验证配置文件语法
   - 确认文件路径正确

2. **LLM调用失败**
   - 检查网络连接
   - 验证API密钥
   - 查看错误日志

3. **Git操作失败**
   - 检查令牌权限
   - 验证仓库访问权限
   - 确认分支存在

### 调试模式

```bash
# 启用调试模式
export GK_ENV=dev
export LOG_LEVEL=DEBUG
export CONTEXT_ENABLE_DETAILED_LOGGING=true
``` 