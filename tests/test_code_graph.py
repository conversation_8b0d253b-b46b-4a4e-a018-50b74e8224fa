"""
Test Enhanced Code Graph

测试增强的代码图功能
"""

import os
import tempfile
from unittest.mock import Mock, patch

import pytest

from gate_keeper.external.code_analyzer import (CodeGraph, RepositoryIndex,
                                                StaticAnalyzer)
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)
from gate_keeper.external.code_analyzer.models.function import \
    Parameter as FunctionParameter


class TestCodeGraph:
    """测试增强的代码图功能"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.create_test_files()
    
    def teardown_method(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_files(self):
        """创建测试文件"""
        # 创建 main.c
        main_c_content = """
#include "utils.h"

int main() {
    int result = add(10, 20);
    print_result(result);
    return 0;
}

void print_result(int value) {
    printf("Result: %d\\n", value);
}
"""
        with open(os.path.join(self.temp_dir, "main.c"), "w") as f:
            f.write(main_c_content)
        
        # 创建 utils.h
        utils_h_content = """
#ifndef UTILS_H
#define UTILS_H

int add(int a, int b);
int multiply(int a, int b);

#endif
"""
        with open(os.path.join(self.temp_dir, "utils.h"), "w") as f:
            f.write(utils_h_content)
        
        # 创建 utils.c
        utils_c_content = """
#include "utils.h"

int add(int a, int b) {
    return a + b;
}

int multiply(int a, int b) {
    return a * b;
}
"""
        with open(os.path.join(self.temp_dir, "utils.c"), "w") as f:
            f.write(utils_c_content)
    
    @patch('gate_keeper.external.code_analyzer.core.repository_index.collect_source_files')
    @patch('gate_keeper.external.code_analyzer.core.repository_index.ASTParserFactory')
    def test_repository_index_build(self, mock_factory, mock_collect_files):
        """测试增强仓库索引构建"""
        # Mock 文件收集
        mock_collect_files.return_value = ["main.c", "utils.h", "utils.c"]
        
        # Mock 解析器
        mock_parser = Mock()
        mock_factory.create.return_value = mock_parser
        
        # Mock 函数定义
        main_func = Function(
            name="main",
            range=CodeRange(start_line=4, end_line=10),
            filepath="main.c",
            signature=FunctionSignature(
                name="main",
                parameters=[],
                return_type="int"
            ),
            code="int main() { ... }",
            is_declaration=False
        )
        
        print_func = Function(
            name="print_result",
            range=CodeRange(start_line=12, end_line=15),
            filepath="main.c",
            signature=FunctionSignature(
                name="print_result",
                parameters=[FunctionParameter(name="value", type_hint="int")],
                return_type="void"
            ),
            code="void print_result(int value) { ... }",
            is_declaration=False
        )
        
        add_func = Function(
            name="add",
            range=CodeRange(start_line=4, end_line=6),
            filepath="utils.c",
            signature=FunctionSignature(
                name="add",
                parameters=[
                    FunctionParameter(name="a", type_hint="int"),
                    FunctionParameter(name="b", type_hint="int")
                ],
                return_type="int"
            ),
            code="int add(int a, int b) { ... }",
            is_declaration=False
        )
        
        # Mock 函数调用
        main_call = FunctionCall(
            caller="main",
            callee="add",
            file_path="main.c",
            line=[6],
            code="add(10, 20)"
        )
        
        print_call = FunctionCall(
            caller="main",
            callee="print_result",
            file_path="main.c",
            line=[7],
            code="print_result(result)"
        )
        
        # 设置解析器返回值
        mock_parser.extract_functions.side_effect = [
            [main_func, print_func],  # main.c
            [],  # utils.h
            [add_func]  # utils.c
        ]
        
        mock_parser.extract_calls.side_effect = [
            [main_call, print_call],  # main.c
            [],  # utils.h
            []  # utils.c
        ]
        
        mock_parser.extract_macros.return_value = []
        
        # 创建索引
        index = RepositoryIndex(
            repo_dir=self.temp_dir,
            branch="main"
        )
        index.build()
        
        # 验证结果
        assert len(index.function_definitions) > 0
        assert len(index.function_calls) > 0
        assert index.call_graph.number_of_nodes() > 0
    
    def test_enhanced_static_analyzer(self):
        """测试增强的静态分析器"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index._get_node_id.return_value = "test::function::main"
        index.find_function_definition.return_value = Mock()
        index.graph_builder = Mock()
        
        # 创建分析器
        analyzer = StaticAnalyzer(index)
        
        # 测试基本功能
        assert analyzer.repo_index == index
        assert analyzer.graph == index.get_call_graph()
    
    def test_enhanced_code_graph_basic_functionality(self):
        """测试增强代码图的基本功能"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试基本功能
        assert code_graph.repo_index == index
        assert code_graph.static_analyzer is not None
        assert code_graph.graph == index.get_call_graph()
    
    def test_enhanced_code_graph_analysis_methods(self):
        """测试增强代码图的分析方法"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        index.graph_builder.get_modules.return_value = {}
        index.get_graph_statistics.return_value = {"nodes": 0, "edges": 0, "functions": 0, "calls": 0, "modules": 0, "files": 0}
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试分析方法
        stats = code_graph.get_graph_statistics()
        assert isinstance(stats, dict)
        
        modules = code_graph.get_modules()
        assert isinstance(modules, dict)
        
        structure = code_graph.analyze_codebase_structure()
        assert isinstance(structure, dict)
        assert "overview" in structure
        assert "language_distribution" in structure
        assert "dependency_matrix" in structure
    
    def test_enhanced_code_graph_module_analysis(self):
        """测试模块分析功能"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        
        # 模拟模块
        mock_module = Mock()
        mock_module.path = "test.c"
        mock_module.language = "c"
        mock_module.node_ids = ["node1", "node2"]
        mock_module.imports = ["utils.h"]
        mock_module.exports = ["main"]
        
        index.graph_builder.get_modules.return_value = {"test.c": mock_module}
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试模块分析
        analysis = code_graph.get_module_analysis("test.c")
        assert isinstance(analysis, dict)
        assert analysis["module_path"] == "test.c"
        assert "dependencies" in analysis
        assert "cross_module_calls" in analysis
        assert "functions" in analysis
        assert "statistics" in analysis
    
    def test_enhanced_code_graph_import_impact_analysis(self):
        """测试导入影响分析功能"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        
        # 模拟模块
        mock_module = Mock()
        mock_module.path = "test.c"
        mock_module.imports = ["utils.h"]
        mock_module.exports = ["main"]
        
        index.graph_builder.get_modules.return_value = {"test.c": mock_module}
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试导入影响分析
        impact = code_graph.get_import_impact_analysis("test.c")
        assert isinstance(impact, dict)
        assert impact["module_path"] == "test.c"
        assert "imports" in impact
        assert "exports" in impact
        assert "dependents" in impact
        assert "cross_module_calls" in impact
        assert "statistics" in impact
    
    def test_enhanced_code_graph_high_coupling_detection(self):
        """测试高耦合模块检测"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        
        # 模拟多个模块
        mock_modules = {}
        for i in range(3):
            mock_module = Mock()
            mock_module.path = f"module{i}.c"
            mock_module.imports = [f"module{(i+1)%3}.h"]  # 循环依赖
            mock_module.exports = [f"func{i}"]
            mock_modules[f"module{i}.c"] = mock_module
        
        index.graph_builder.get_modules.return_value = mock_modules
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试高耦合检测
        high_coupling = code_graph.find_high_coupling_modules(threshold=0.1)
        assert isinstance(high_coupling, list)
        # 由于有循环依赖，应该检测到高耦合模块
        assert len(high_coupling) > 0
    
    def test_enhanced_code_graph_isolated_modules_detection(self):
        """测试孤立模块检测"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        index.get_call_graph.return_value = Mock()
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        
        # 模拟模块
        mock_modules = {
            "isolated.c": Mock(imports=[], exports=["func1"]),
            "connected.c": Mock(imports=["utils.h"], exports=["func2"])
        }
        
        index.graph_builder.get_modules.return_value = mock_modules
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试孤立模块检测
        isolated = code_graph.find_isolated_modules()
        assert isinstance(isolated, list)
        assert "isolated.c" in isolated
        assert "connected.c" not in isolated
    
    def test_enhanced_code_graph_function_call_graph(self):
        """测试函数调用图功能"""
        # 创建模拟的仓库索引
        index = Mock(spec=RepositoryIndex)
        mock_graph = Mock()
        mock_graph.__contains__ = Mock(return_value=True)
        mock_graph.predecessors = Mock(return_value=[])
        mock_graph.successors = Mock(return_value=[])
        index.get_call_graph.return_value = mock_graph
        index.function_definitions = {}
        index.function_calls = []
        index.graph_builder = Mock()
        index._get_node_id.return_value = "test.c::function::main"
        
        # 创建代码图
        code_graph = CodeGraph(index)
        
        # 测试函数调用图
        call_graph = code_graph.get_function_call_graph("test.c::main", depth=2)
        assert isinstance(call_graph, dict)
        assert "nodes" in call_graph
        assert "edges" in call_graph
        assert "center" in call_graph 