"""
CLI参数传递测试

测试目标：
1. 验证CLI参数的正确解析
2. 测试参数从CLI到UseCase的完整传递链路
3. 验证参数类型转换和验证
4. 测试参数默认值处理

测试覆盖：
- CLI参数解析
- 参数类型转换
- 参数验证
- 默认值处理
- 错误处理
"""

import os
import sys
import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """Mock LLM客户端"""
    
    def __init__(self):
        super().__init__("http://mock.local")
    
    def generate(self, prompt, parameters, token=None):
        return """<FinalAnswer>
{
  "is_pass": true,
  "reason": "测试通过",
  "violations": []
}
</FinalAnswer>"""
    
    def get_config(self):
        return {}


class TestCLIParameterPassing(unittest.TestCase):
    """CLI参数传递测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_git_service = Mock(spec=GitService)
        self.mock_llm_client = MockLLMClient()
        
        # 设置基本的mock返回值
        self.mock_git_service.get_diff_by_mr_id = Mock(return_value=[])
        self.mock_git_service.get_file_content_by_ref = Mock(return_value="def test(): pass")
        self.mock_git_service.keep_branch_update_if_needed = Mock()
        self.mock_git_service.get_remote_latest_branch_commit_sha = Mock(return_value="test-sha")
    
    def test_parameter_type_conversion(self):
        """
        测试参数类型转换
        
        验证目标：确保CLI参数能正确转换为目标类型
        """
        # 测试各种参数类型
        test_cases = [
            ('--mr-id', '123', int, 123),
            ('--max-context-chain-depth', '5', int, 5),
            ('--max-context-chains', '3', int, 3),
            ('--max-context-size', '4000', int, 4000),
            ('--use-optimized-context', 'true', bool, True),
            ('--use-optimized-context', 'false', bool, False),
            ('--repo-dir', '/test/repo', str, '/test/repo'),
            ('--project-id', 'test/project', str, 'test/project')
        ]
        
        for arg_name, arg_value, expected_type, expected_value in test_cases:
            with self.subTest(arg_name=arg_name, arg_value=arg_value):
                # 这里我们验证参数类型转换的逻辑
                if expected_type == int:
                    converted_value = int(arg_value)
                elif expected_type == bool:
                    converted_value = arg_value.lower() == 'true'
                else:
                    converted_value = arg_value
                
                self.assertIsInstance(converted_value, expected_type)
                self.assertEqual(converted_value, expected_value)
    
    def test_parameter_validation(self):
        """
        测试参数验证
        
        验证目标：确保无效参数能被正确检测
        """
        # 测试无效参数
        invalid_cases = [
            ('--mr-id', 'abc'),  # 非数字
            ('--max-context-chain-depth', '-1'),  # 负数
            ('--max-context-chain-depth', '0'),   # 零值
            ('--max-context-chains', '-5'),       # 负数
            ('--max-context-size', '-1000'),      # 负数
        ]
        
        for arg_name, arg_value in invalid_cases:
            with self.subTest(arg_name=arg_name, arg_value=arg_value):
                # 这里我们验证参数验证的逻辑
                if arg_name in ['--mr-id', '--max-context-chain-depth', '--max-context-chains', '--max-context-size']:
                    try:
                        value = int(arg_value)
                        if value <= 0:
                            # 应该抛出异常或返回错误
                            self.assertLessEqual(value, 0, f"参数 {arg_name} 应该是无效的")
                    except ValueError:
                        # 非数字值应该抛出ValueError
                        pass
    
    def test_cli_to_usecase_parameter_flow(self):
        """
        测试CLI到UseCase的参数流
        
        验证目标：模拟CLI调用，验证参数传递的完整性
        """
        # 模拟CLI参数
        cli_args = {
            'repo_dir': '/test/repo',
            'project_id': 'test/project',
            'mr_id': 123,
            'base_branch': 'main',
            'dev_branch': 'feature',
            'max_context_chain_depth': 5,
            'exclude_patterns': ['*.tmp', '*.log'],
            'max_diff_results': 10
        }
        
        # 创建LLM服务
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )

        # 创建 mock repo_analyzer
        repo_analyzer_mock = Mock()
        
        # 创建服务编排器
        from gate_keeper.application.service.service_orchestrator import \
            ServiceOrchestrator
        orchestrator = ServiceOrchestrator(
            git_service=self.mock_git_service,
            llm_client=self.mock_llm_client
        )
        
        # 创建UseCase
        usecase = AnalyzeMRAndReportUsecase(orchestrator)

        # 模拟Git diff结果
        mock_diff = Mock()
        mock_diff.path = "test.py"
        mock_diff.is_removed_file = False
        mock_diff.is_added_file = True
        
        self.mock_git_service.get_diff_by_mr_id.return_value = [mock_diff]
        
        # 模拟 repo_analyzer 返回受影响的函数
        from gate_keeper.application.dto.result import AffectedFunction
        mock_affected_function = Mock(spec=AffectedFunction)
        mock_affected_function.name = "test_function"
        mock_affected_function.filepath = "test.py"
        mock_affected_function.start_line = 1
        mock_affected_function.end_line = 10
        mock_affected_function.code = "def test_function(): pass"
        mock_affected_function.llm_results = []
        
        # 模拟代码分析器返回受影响的函数
        with patch.object(usecase.repo_analyzer, 'analyze_functions') as mock_analyze:
            mock_analyze.return_value = [mock_affected_function]
        
        # 使用真实的LLMService，但mock其analyze_mr方法
        with patch.object(usecase.llm_service, 'analyze_mr') as mock_analyze_mr:
            # 创建一个正确的mock返回值
            from gate_keeper.application.dto.result import (CheckMRResult,
                                                            DiffResult)
            mock_diff_result = Mock(spec=DiffResult)
            mock_diff_result.affected_functions = [mock_affected_function]
            mock_result = Mock(spec=CheckMRResult)
            mock_result.diffs = [mock_diff_result]
            mock_analyze_mr.return_value = mock_result
            
            # 执行UseCase（模拟CLI调用）
            result, report = usecase.execute(
                repo_dir=cli_args['repo_dir'],
                project_id=cli_args['project_id'],
                mr_id=cli_args['mr_id'],
                base_branch=cli_args['base_branch'],
                dev_branch=cli_args['dev_branch'],
                max_context_chain_depth=cli_args['max_context_chain_depth'],
                exclude_patterns=cli_args['exclude_patterns'],
                max_diff_results=cli_args['max_diff_results']
            )
            # 验证参数传递
            self.mock_git_service.get_diff_by_mr_id.assert_called()
            # 验证analyze_mr被调用
            mock_analyze_mr.assert_called()
    
    def test_required_parameters(self):
        """
        测试必需参数
        
        验证目标：确保必需参数被正确识别和处理
        """
        # 定义必需参数
        required_params = [
            'repo_dir',
            'project_id',
            'mr_id'
        ]
        
        # 验证必需参数
        for param in required_params:
            with self.subTest(param=param):
                # 这里我们验证必需参数的逻辑
                self.assertIsNotNone(param)
    
    def test_optional_parameters(self):
        """
        测试可选参数
        
        验证目标：确保可选参数有合理的默认值
        """
        # 定义可选参数及其默认值
        optional_params = {
            'max_context_chain_depth': 3,
            'max_context_chains': 5,
            'use_optimized_context': True,
            'exclude_patterns': [],
            'max_diff_results': 10
        }
        
        # 验证可选参数
        for param, default_value in optional_params.items():
            with self.subTest(param=param):
                # 这里我们验证可选参数的逻辑
                self.assertIsNotNone(default_value)
    
    def test_parameter_error_handling(self):
        """
        测试参数错误处理
        
        验证目标：确保参数错误能被正确处理
        """
        # 测试错误情况
        error_cases = [
            ('--mr-id', 'abc', ValueError),  # 非数字
            ('--max-context-chain-depth', '-1', ValueError),  # 负数
            ('--repo-dir', '', ValueError),  # 空字符串
        ]
        
        for arg_name, arg_value, expected_error in error_cases:
            with self.subTest(arg_name=arg_name, arg_value=arg_value):
                # 这里我们验证错误处理的逻辑
                try:
                    if arg_name == '--mr-id':
                        int(arg_value)
                    elif arg_name == '--max-context-chain-depth':
                        value = int(arg_value)
                        if value <= 0:
                            raise ValueError(f"Invalid value: {value}")
                    elif arg_name == '--repo-dir':
                        if not arg_value:
                            raise ValueError("Empty repository directory")
                except expected_error:
                    # 期望的错误
                    pass
                except Exception as e:
                    # 其他错误
                    self.fail(f"Unexpected error: {e}")


if __name__ == '__main__':
    unittest.main() 