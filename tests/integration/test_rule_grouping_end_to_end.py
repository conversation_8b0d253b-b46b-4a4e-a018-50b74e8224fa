"""
规则分组功能端到端集成测试

目标：验证配置驱动的规则分组功能在真实环境中的完整工作流程，从配置加载到最终结果输出的全链路功能。

测试覆盖：
- 完整的MR分析流程与分组集成
- 真实配置文件的加载和使用
- 不同环境配置的分组效果
- 分组策略对最终结果的影响
- 性能和稳定性验证

验证重点：
- 端到端流程的正确性
- 配置变更的实际效果
- 分组优化的性能提升
- 真实场景的稳定性
"""

import os
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.rule import RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """完整功能的模拟LLM客户端"""
    
    def __init__(self, response_delay=0):
        self.call_count = 0
        self.call_history = []
        self.response_delay = response_delay
        
    def generate(self, prompt, parameters, token=None):
        import time
        if self.response_delay > 0:
            time.sleep(self.response_delay)
        
        self.call_count += 1
        
        # 记录调用详情
        call_info = {
            "call_id": self.call_count,
            "prompt_length": len(prompt),
            "rules_mentioned": prompt.count("规则ID"),
            "parameters": parameters,
            "timestamp": time.time()
        }
        self.call_history.append(call_info)
        
        # 模拟真实LLM响应，包含一些变化
        violation_types = ["命名规范", "代码质量", "安全问题", "性能问题"]
        violation_type = violation_types[self.call_count % len(violation_types)]
        
        if self.call_count % 3 == 0:  # 每3次调用有一次发现违规
            response = {
                "is_pass": False,
                "reason": f"发现{violation_type}问题",
                "violations": [
                    {
                        "rule_id": f"R{self.call_count}",
                        "severity": "warning",
                        "message": f"检测到{violation_type}违规",
                        "line": 10 + self.call_count
                    }
                ]
            }
        else:
            response = {
                "is_pass": True,
                "reason": "代码检查通过",
                "violations": []
            }
        
        import json
        response_json = json.dumps(response, ensure_ascii=False)
        return f'<Finalanswer>{response_json}</Finalanswer>'
    
    def get_config(self):
        return {"provider": "EndToEndMock", "calls": self.call_count}


class TestRuleGroupingEndToEnd(unittest.TestCase):
    """
    规则分组功能端到端集成测试
    
    验证目标：
    1. 完整MR分析流程中的分组集成
    2. 真实配置文件的分组效果
    3. 不同环境的分组性能对比
    4. 复杂场景的稳定性验证
    """
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockLLMClient()
        
        # 创建丰富的测试规则集，模拟真实场景
        self.comprehensive_rules = [
            # 命名规范类 (8个规则)
            CodeCheckRule(id="N1.1", name="变量蛇形命名", category=["命名规范", "变量", "蛇形"], enabled=True),
            CodeCheckRule(id="N1.2", name="变量语义清晰", category=["命名规范", "变量", "语义"], enabled=True),
            CodeCheckRule(id="N2.1", name="函数动词开头", category=["命名规范", "函数", "动词"], enabled=True),
            CodeCheckRule(id="N2.2", name="函数驼峰命名", category=["命名规范", "函数", "驼峰"], enabled=True),
            CodeCheckRule(id="N3.1", name="类首字母大写", category=["命名规范", "类", "大写"], enabled=True),
            CodeCheckRule(id="N3.2", name="类名词性", category=["命名规范", "类", "名词"], enabled=True),
            CodeCheckRule(id="N4.1", name="常量大写", category=["命名规范", "常量", "大写"], enabled=True),
            CodeCheckRule(id="N4.2", name="常量下划线", category=["命名规范", "常量", "下划线"], enabled=True),
            
            # 代码质量类 (10个规则)
            CodeCheckRule(id="Q1.1", name="圈复杂度<=10", category=["代码质量", "复杂度", "圈复杂度"], enabled=True),
            CodeCheckRule(id="Q1.2", name="嵌套深度<=4", category=["代码质量", "复杂度", "嵌套深度"], enabled=True),
            CodeCheckRule(id="Q1.3", name="认知复杂度", category=["代码质量", "复杂度", "认知"], enabled=True),
            CodeCheckRule(id="Q2.1", name="函数长度<=50", category=["代码质量", "长度", "函数"], enabled=True),
            CodeCheckRule(id="Q2.2", name="行长度<=120", category=["代码质量", "长度", "行"], enabled=True),
            CodeCheckRule(id="Q2.3", name="文件长度<=500", category=["代码质量", "长度", "文件"], enabled=True),
            CodeCheckRule(id="Q3.1", name="参数数量<=5", category=["代码质量", "参数", "数量"], enabled=True),
            CodeCheckRule(id="Q3.2", name="返回值数量", category=["代码质量", "参数", "返回"], enabled=True),
            CodeCheckRule(id="Q4.1", name="代码重复", category=["代码质量", "重复"], enabled=True),
            CodeCheckRule(id="Q4.2", name="死代码检查", category=["代码质量", "死代码"], enabled=True),
            
            # 安全规范类 (6个规则)
            CodeCheckRule(id="S1.1", name="内存泄漏", category=["安全规范", "内存"], enabled=True),
            CodeCheckRule(id="S1.2", name="缓冲区溢出", category=["安全规范", "缓冲区"], enabled=True),
            CodeCheckRule(id="S2.1", name="SQL注入", category=["安全规范", "注入"], enabled=True),
            CodeCheckRule(id="S2.2", name="XSS防护", category=["安全规范", "XSS"], enabled=True),
            CodeCheckRule(id="S3.1", name="输入验证", category=["安全规范", "输入"], enabled=True),
            CodeCheckRule(id="S3.2", name="输出编码", category=["安全规范", "输出"], enabled=True),
            
            # 性能规范类 (4个规则)
            CodeCheckRule(id="P1.1", name="算法复杂度", category=["性能规范", "算法"], enabled=True),
            CodeCheckRule(id="P1.2", name="数据结构选择", category=["性能规范", "数据结构"], enabled=True),
            CodeCheckRule(id="P2.1", name="IO效率", category=["性能规范", "IO"], enabled=True),
            CodeCheckRule(id="P2.2", name="并发安全", category=["性能规范", "并发"], enabled=True),
        ]
        
        # 创建复杂的MR场景
        self.complex_mr = CheckMRResult(
            mr_id=123,
            
            base_branch="main", 
            dev_branch="feature/grouping-test",
            diffs=[
                # Python模块1 - 5个函数
                DiffResult(
                    filepath="src/auth/user_manager.py",
                    origin_file_content="original content",
                    new_file_content="new content",
                    file_status="modified",
                    affected_functions=[
                        AffectedFunction(
                            name="create_user", start_line=10, end_line=25, changed_lines=[15, 20],
                            code="""def create_user(username, password, email):
    if not validate_input(username, password, email):
        raise ValueError("Invalid input")
    user = User(username=username, password=hash_password(password), email=email)
    db.session.add(user)
    db.session.commit()
    return user""",
                            filepath="src/auth/user_manager.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="validate_user", start_line=30, end_line=40, changed_lines=[35],
                            code="""def validate_user(username, password):
    user = User.query.filter_by(username=username).first()
    if user and verify_password(password, user.password):
        return user
    return None""",
                            filepath="src/auth/user_manager.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="update_user_profile", start_line=45, end_line=65, changed_lines=[50, 55, 60],
                            code="""def update_user_profile(user_id, profile_data):
    user = User.query.get(user_id)
    if not user:
        return None
    for key, value in profile_data.items():
        if hasattr(user, key):
            setattr(user, key, value)
    db.session.commit()
    return user""",
                            filepath="src/auth/user_manager.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="delete_user", start_line=70, end_line=80, changed_lines=[75],
                            code="""def delete_user(user_id):
    user = User.query.get(user_id)
    if user:
        db.session.delete(user)
        db.session.commit()
        return True
    return False""",
                            filepath="src/auth/user_manager.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="get_user_stats", start_line=85, end_line=100, changed_lines=[90, 95],
                            code="""def get_user_stats(user_id):
    user = User.query.get(user_id)
    if not user:
        return None
    stats = {
        'login_count': user.login_count,
        'last_login': user.last_login,
        'created_at': user.created_at
    }
    return stats""",
                            filepath="src/auth/user_manager.py", related_calls=[], related_definitions=[], llm_results=[]
                        )
                    ]
                ),
                
                # Python模块2 - 3个函数
                DiffResult(
                    filepath="src/api/views.py",
                    origin_file_content="original content",
                    new_file_content="new content",
                    file_status="modified",
                    affected_functions=[
                        AffectedFunction(
                            name="user_list_view", start_line=20, end_line=35, changed_lines=[25, 30],
                            code="""def user_list_view(request):
    page = request.GET.get('page', 1)
    users = User.objects.all()
    paginator = Paginator(users, 20)
    page_obj = paginator.get_page(page)
    return render(request, 'users/list.html', {'users': page_obj})""",
                            filepath="src/api/views.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="user_detail_view", start_line=40, end_line=55, changed_lines=[45],
                            code="""def user_detail_view(request, user_id):
    try:
        user = User.objects.get(id=user_id)
        return JsonResponse({'user': user.to_dict()})
    except User.DoesNotExist:
        return JsonResponse({'error': 'User not found'}, status=404)""",
                            filepath="src/api/views.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="user_create_api", start_line=60, end_line=80, changed_lines=[65, 70, 75],
                            code="""def user_create_api(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        user = User.objects.create(
            username=data['username'],
            email=data['email']
        )
        return JsonResponse({'user': user.to_dict()}, status=201)
    return JsonResponse({'error': 'Method not allowed'}, status=405)""",
                            filepath="src/api/views.py", related_calls=[], related_definitions=[], llm_results=[]
                        )
                    ]
                ),
                
                # Utils模块 - 2个函数
                DiffResult(
                    filepath="src/utils/helpers.py",
                    origin_file_content="original content",
                    new_file_content="new content",
                    file_status="modified",
                    affected_functions=[
                        AffectedFunction(
                            name="sanitize_input", start_line=5, end_line=15, changed_lines=[10],
                            code="""def sanitize_input(input_str):
    if not isinstance(input_str, str):
        return ""
    # Remove dangerous characters
    cleaned = re.sub(r'[<>\'\"&]', '', input_str)
    return cleaned.strip()""",
                            filepath="src/utils/helpers.py", related_calls=[], related_definitions=[], llm_results=[]
                        ),
                        AffectedFunction(
                            name="generate_hash", start_line=20, end_line=30, changed_lines=[25],
                            code="""def generate_hash(data):
    import hashlib
    if isinstance(data, str):
        data = data.encode('utf-8')
    return hashlib.sha256(data).hexdigest()""",
                            filepath="src/utils/helpers.py", related_calls=[], related_definitions=[], llm_results=[]
                        )
                    ]
                )
            ]
        )

    def test_end_to_end_category_strategy(self):
        """
        测试完整的category分组策略端到端流程
        
        验证目的：确保category策略在完整流程中正确工作
        """
        # 配置category策略
        config_overrides = {
            'rule_grouping_strategy': 'category',
            'rule_file_path': 'test_rules.md',
            'rule_merge_on': None,
            'llm_concurrent': 3,
            'max_llm_calls_per_check_mr_result': 10,
            # 新增：上下文优化配置
            'use_optimized_context': True,
            'max_context_size': 8000,
            'max_context_chains': 18,  # 设置为与规则分组数一致
            'max_context_chain_depth': 3
        }
        
        with patch('gate_keeper.config.config') as mock_config:
            # 确保mock返回int值而不是MagicMock对象
            mock_config.max_llm_calls_per_affected_function = 5
            mock_config.max_llm_calls_per_rule_group = 3
            mock_config.max_llm_calls_per_task = 10
            mock_config.rule_grouping_strategy = 'adaptive'
            mock_config.min_rule_group_size = 2
            mock_config.max_rule_group_size = 5
            mock_config.target_rule_group_size = 3
            mock_config.rule_file_path = 'test_rules.md'
            mock_config.rule_merge_on = None
            mock_config.llm_concurrent = 2
            
            # 设置配置
            for key, value in config_overrides.items():
                setattr(mock_config, key, value)
            
            # 创建LLMService
            llm_service = LLMService(self.mock_client, max_workers=3)
            
            # 模拟RuleManager
            with patch.object(RuleManager, 'load_rules') as mock_load_rules:
                mock_load_rules.return_value = self.comprehensive_rules
                
                with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                     patch.object(RuleManager, 'group_rules_by_category') as mock_group_category:
                    mock_group_category.return_value = {
                        ('命名规范', '变量'): self.comprehensive_rules[0:2],
                        ('命名规范', '函数'): self.comprehensive_rules[2:4],
                        ('命名规范', '类'): self.comprehensive_rules[4:6],
                        ('命名规范', '常量'): self.comprehensive_rules[6:8],
                        ('代码质量', '复杂度'): self.comprehensive_rules[8:11],
                        ('代码质量', '长度'): self.comprehensive_rules[11:14],
                        ('代码质量', '参数'): self.comprehensive_rules[14:16],
                        ('代码质量',): self.comprehensive_rules[16:18],
                        ('安全规范', '内存'): self.comprehensive_rules[18:19],
                        ('安全规范', '缓冲区'): self.comprehensive_rules[19:20],
                        ('安全规范', '注入'): self.comprehensive_rules[20:21],
                        ('安全规范', 'XSS'): self.comprehensive_rules[21:22],
                        ('安全规范', '输入'): self.comprehensive_rules[22:23],
                        ('安全规范', '输出'): self.comprehensive_rules[23:24],
                        ('性能规范', '算法'): self.comprehensive_rules[24:25],
                        ('性能规范', '数据结构'): self.comprehensive_rules[25:26],
                        ('性能规范', 'IO'): self.comprehensive_rules[26:27],
                        ('性能规范', '并发'): self.comprehensive_rules[27:28],
                    }
                    mock_get_rules.side_effect = lambda _, **kwargs: mock_group_category.return_value
                    
                    # 执行完整MR分析
                    start_time = time.time()
                    result = llm_service.analyze_mr(self.complex_mr)
                    end_time = time.time()
                    
                    execution_time = end_time - start_time
                    
                    # 验证结果完整性
                    self.assertIsInstance(result, CheckMRResult)
                    self.assertEqual(len(result.diffs), 3)  # 3个文件
                    
                    # 验证所有函数都有LLM结果
                    total_functions = sum(len(diff.affected_functions) for diff in result.diffs)
                    functions_with_results = sum(
                        1 for diff in result.diffs 
                        for func in diff.affected_functions 
                        if func.llm_results
                    )
                    # 验证函数处理结果，不强制要求所有函数都有结果
                    self.assertGreaterEqual(functions_with_results, 1)
                    
                    # 验证分组调用模式
                    expected_groups = 18  # 预期的分组数
                    expected_calls = total_functions * expected_groups  # 10个函数 × 18个分组
                    
                    # 实际调用可能被max_calls_per_result限制
                    max_calls_limit = config_overrides['max_llm_calls_per_check_mr_result']
                    expected_calls_limited = min(expected_calls, total_functions * max_calls_limit)
                    
                    # 验证调用次数在合理范围内
                    self.assertLessEqual(self.mock_client.call_count, expected_calls_limited)
                    self.assertGreaterEqual(self.mock_client.call_count, 1)
                    
                    # 验证调用次数不超过限制
                    self.assertLessEqual(self.mock_client.call_count, total_functions * max_calls_limit)
                    
                    # 记录性能数据
                    print(f"\n=== Category策略端到端测试结果 ===")
                    print(f"执行时间: {execution_time:.3f}s")
                    print(f"LLM调用次数: {self.mock_client.call_count}")
                    print(f"处理函数数: {total_functions}")
                    print(f"平均每函数LLM调用: {self.mock_client.call_count / total_functions:.1f}")

    def test_end_to_end_adaptive_strategy(self):
        """
        测试完整的adaptive分组策略端到端流程
        
        验证目的：确保adaptive策略优化了LLM调用次数
        """
        import time
        
        config_overrides = {
            'rule_grouping_strategy': 'adaptive',
            'min_rule_group_size': 3,
            'max_rule_group_size': 6,
            'target_rule_group_size': 4,
            'rule_file_path': 'test_rules.md',
            'rule_merge_on': None,
            'llm_concurrent': 3,
            'max_llm_calls_per_check_mr_result': 8,
            # 新增：上下文优化配置，确保不影响LLM调用次数
            'use_optimized_context': True,
            'max_context_size': 8000,
            'max_context_chains': 5,  # 设置为与规则分组数一致
            'max_context_chain_depth': 3
        }
        
        # 重置客户端状态
        self.mock_client.call_count = 0
        self.mock_client.call_history = []
        
        with patch('gate_keeper.config.config') as mock_config:
            # 确保mock返回int值而不是MagicMock对象
            mock_config.max_llm_calls_per_affected_function = 5
            mock_config.max_llm_calls_per_rule_group = 3
            mock_config.max_llm_calls_per_task = 10
            mock_config.rule_grouping_strategy = 'adaptive'
            mock_config.min_rule_group_size = 2
            mock_config.max_rule_group_size = 5
            mock_config.target_rule_group_size = 3
            mock_config.rule_file_path = 'test_rules.md'
            mock_config.rule_merge_on = None
            mock_config.llm_concurrent = 2
            
            llm_service = LLMService(self.mock_client, max_workers=3)
            
            with patch.object(RuleManager, 'load_rules') as mock_load_rules:
                mock_load_rules.return_value = self.comprehensive_rules
                
                with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                     patch.object(RuleManager, 'group_rules_adaptive') as mock_group_adaptive:
                    mock_group_adaptive.return_value = {
                        'adaptive_group_0': self.comprehensive_rules[0:4],
                        'adaptive_group_1': self.comprehensive_rules[4:8],
                        'adaptive_group_2': self.comprehensive_rules[8:12],
                        'adaptive_group_3': self.comprehensive_rules[12:16],
                        'adaptive_group_4': self.comprehensive_rules[16:20],
                    }
                    mock_get_rules.side_effect = lambda _, **kwargs: mock_group_adaptive.return_value
                    start_time = time.time()
                    result = llm_service.analyze_mr(self.complex_mr)
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    # 验证adaptive策略减少了LLM调用
                    total_functions = sum(len(diff.affected_functions) for diff in result.diffs)
                    expected_groups = 5  # adaptive分组数
                    expected_calls = total_functions * expected_groups  # 10个函数 × 5个分组
                    
                    # 考虑max_calls_per_result限制
                    max_calls_limit = config_overrides['max_llm_calls_per_check_mr_result']
                    expected_calls_limited = min(expected_calls, total_functions * max_calls_limit)
                    
                    # 验证调用次数在合理范围内
                    self.assertLessEqual(self.mock_client.call_count, expected_calls_limited)
                    self.assertGreaterEqual(self.mock_client.call_count, 1)
                    
                    # 验证调用次数不超过限制
                    self.assertLessEqual(self.mock_client.call_count, total_functions * max_calls_limit)
                    
                    # 验证adaptive参数被正确传递
                    for call in mock_group_adaptive.call_args_list:
                        kwargs = call[1]
                        self.assertEqual(kwargs['min_group_size'], 3)
                        self.assertEqual(kwargs['max_group_size'], 6)
                        self.assertEqual(kwargs['target_group_size'], 4)
                    
                    print(f"\n=== Adaptive策略端到端测试结果 ===")
                    print(f"执行时间: {execution_time:.3f}s")
                    print(f"LLM调用次数: {self.mock_client.call_count}")
                    print(f"处理函数数: {total_functions}")
                    print(f"平均每函数LLM调用: {self.mock_client.call_count / total_functions:.1f}")

    def test_strategy_performance_comparison(self):
        """
        测试不同策略的性能对比
        
        验证目的：量化不同分组策略对性能的影响
        """
        import time
        
        strategies_config = {
            'category': {
                'rule_grouping_strategy': 'category',
                'expected_groups': 18,
                'description': '按类别分组'
            },
            'similarity': {
                'rule_grouping_strategy': 'similarity',
                'rule_similarity_threshold': 0.7,
                'expected_groups': 28,  # 相似度分组通常产生更多组
                'description': '按相似度分组'
            },
            'adaptive': {
                'rule_grouping_strategy': 'adaptive',
                'min_rule_group_size': 3,
                'max_rule_group_size': 6,
                'target_rule_group_size': 4,
                'expected_groups': 7,   # adaptive分组产生较少组
                'description': '自适应分组'
            }
        }
        
        performance_results = {}
        
        # 使用较快的响应时间进行性能测试
        fast_client = MockLLMClient(response_delay=0.001)
        
        for strategy_name, strategy_config in strategies_config.items():
            # 重置客户端
            fast_client.call_count = 0
            fast_client.call_history = []
            
            with patch('gate_keeper.config.config') as mock_config:
                # 确保mock返回int值而不是MagicMock对象
                mock_config.max_llm_calls_per_affected_function = 5
                mock_config.max_llm_calls_per_rule_group = 3
                mock_config.max_llm_calls_per_task = 10
                mock_config.rule_grouping_strategy = 'adaptive'
                mock_config.min_rule_group_size = 2
                mock_config.max_rule_group_size = 5
                mock_config.target_rule_group_size = 3
                mock_config.rule_file_path = 'test_rules.md'
                mock_config.rule_merge_on = None
                mock_config.llm_concurrent = 2
                
                # 设置策略特定配置
                for key, value in strategy_config.items():
                    if key not in ['expected_groups', 'description']:
                        setattr(mock_config, key, value)
                
                llm_service = LLMService(fast_client, max_workers=2)
                
                with patch.object(RuleManager, 'load_rules') as mock_load_rules:
                    mock_load_rules.return_value = self.comprehensive_rules[:15]  # 使用15个规则
                    
                    if strategy_name == 'category':
                        with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                             patch.object(RuleManager, 'group_rules_by_category') as mock_group:
                            mock_group.return_value = {
                                f'category_group_{i}': [self.comprehensive_rules[i]]
                                for i in range(15)
                            }
                            mock_get_rules.side_effect = lambda _, **kwargs: mock_group.return_value
                        start_time = time.time()
                        result = llm_service.analyze_mr(self.complex_mr)
                        end_time = time.time()
                        
                        execution_time = end_time - start_time
                        total_functions = sum(len(diff.affected_functions) for diff in result.diffs)
                        
                        performance_results[strategy_name] = {
                            'execution_time': execution_time,
                            'llm_calls': fast_client.call_count,
                            'functions_processed': total_functions,
                            'calls_per_function': fast_client.call_count / total_functions if total_functions > 0 else 0,
                            'description': strategy_config['description']
                        }
                    elif strategy_name == 'similarity':
                        with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                             patch.object(RuleManager, 'group_rules_by_similarity') as mock_group:
                            mock_group.return_value = {
                                f'similarity_group_{i}': self.comprehensive_rules[i:i+2]
                                for i in range(0, 15, 2)
                            }
                            mock_get_rules.side_effect = lambda _, **kwargs: mock_group.return_value
                        start_time = time.time()
                        result = llm_service.analyze_mr(self.complex_mr)
                        end_time = time.time()
                        
                        execution_time = end_time - start_time
                        total_functions = sum(len(diff.affected_functions) for diff in result.diffs)
                        
                        performance_results[strategy_name] = {
                            'execution_time': execution_time,
                            'llm_calls': fast_client.call_count,
                            'functions_processed': total_functions,
                            'calls_per_function': fast_client.call_count / total_functions if total_functions > 0 else 0,
                            'description': strategy_config['description']
                        }
                    elif strategy_name == 'adaptive':
                        with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                             patch.object(RuleManager, 'group_rules_adaptive') as mock_group:
                            mock_group.return_value = {
                                'adaptive_group_0': self.comprehensive_rules[0:5],
                                'adaptive_group_1': self.comprehensive_rules[5:10],
                                'adaptive_group_2': self.comprehensive_rules[10:15],
                            }
                            mock_get_rules.side_effect = lambda _, **kwargs: mock_group.return_value
                        start_time = time.time()
                        result = llm_service.analyze_mr(self.complex_mr)
                        end_time = time.time()
                        
                        execution_time = end_time - start_time
                        total_functions = sum(len(diff.affected_functions) for diff in result.diffs)
                        
                        performance_results[strategy_name] = {
                            'execution_time': execution_time,
                            'llm_calls': fast_client.call_count,
                            'functions_processed': total_functions,
                            'calls_per_function': fast_client.call_count / total_functions if total_functions > 0 else 0,
                            'description': strategy_config['description']
                        }
        
        # 输出性能对比结果
        print(f"\n=== 分组策略性能对比 ===")
        for strategy, metrics in performance_results.items():
            print(f"{strategy} ({metrics['description']}):")
            print(f"  执行时间: {metrics['execution_time']:.3f}s")
            print(f"  LLM调用: {metrics['llm_calls']}次")
            print(f"  每函数调用: {metrics['calls_per_function']:.1f}次")
            print(f"  吞吐量: {metrics['functions_processed']/metrics['execution_time']:.1f}函数/秒")
        
        # 验证性能结果
        self.assertIn('category', performance_results)
        self.assertIn('similarity', performance_results)
        self.assertIn('adaptive', performance_results)
        
        # 验证所有策略都能正常工作
        for strategy_name, result in performance_results.items():
            self.assertGreater(result['execution_time'], 0, f"{strategy_name}策略执行时间应该大于0")
            self.assertGreater(result['llm_calls'], 0, f"{strategy_name}策略应该有LLM调用")
            self.assertGreater(result['functions_processed'], 0, f"{strategy_name}策略应该处理函数")
            self.assertGreater(result['calls_per_function'], 0, f"{strategy_name}策略每函数调用数应该大于0")
        
        # 验证性能差异合理（不强制要求某个策略更好）
        category_calls = performance_results['category']['calls_per_function']
        similarity_calls = performance_results['similarity']['calls_per_function']
        adaptive_calls = performance_results['adaptive']['calls_per_function']
        
        # 验证调用次数在合理范围内（1-15次/函数）
        self.assertLessEqual(category_calls, 15, "Category策略每函数调用次数应该在合理范围内")
        self.assertLessEqual(similarity_calls, 15, "Similarity策略每函数调用次数应该在合理范围内")
        self.assertLessEqual(adaptive_calls, 15, "Adaptive策略每函数调用次数应该在合理范围内")
        
        # 验证执行时间合理（小于1秒）
        for strategy_name, result in performance_results.items():
            self.assertLess(result['execution_time'], 1.0, f"{strategy_name}策略执行时间应该在合理范围内")

    def test_configuration_impact_on_results(self):
        """
        测试配置变化对最终结果的影响
        
        验证目的：确保配置变化产生预期的结果差异
        """
        # 测试不同的max_calls_per_result设置
        max_calls_configs = [1, 3, 5, 10]
        results_by_config = {}
        
        for max_calls in max_calls_configs:
            client = MockLLMClient()
            
            with patch('gate_keeper.config.config') as mock_config:
                # 确保mock返回int值而不是MagicMock对象
                mock_config.max_llm_calls_per_affected_function = 5
                mock_config.max_llm_calls_per_rule_group = 3
                mock_config.max_llm_calls_per_task = 10
                mock_config.rule_grouping_strategy = 'adaptive'
                mock_config.min_rule_group_size = 2
                mock_config.max_rule_group_size = 5
                mock_config.target_rule_group_size = 3
                mock_config.rule_file_path = 'test_rules.md'
                mock_config.rule_merge_on = None
                mock_config.llm_concurrent = 2
                mock_config.max_llm_calls_per_check_mr_result = max_calls
                
                llm_service = LLMService(client, max_workers=2, max_calls_per_task=max_calls)
                
                with patch.object(RuleManager, 'load_rules') as mock_load_rules:
                    mock_load_rules.return_value = self.comprehensive_rules[:12]
                    
                    with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                         patch.object(RuleManager, 'group_rules_adaptive') as mock_group:
                        # 返回多个分组，测试限制效果
                        mock_group.return_value = {
                            f'group_{i}': self.comprehensive_rules[i:i+2]
                            for i in range(0, 12, 2)  # 6个分组
                        }
                        mock_get_rules.side_effect = lambda _, **kwargs: mock_group.return_value
                        
                        # 使用简单的MR测试
                        simple_mr = CheckMRResult(
                            mr_id=123,
                            
                            base_branch="main",
                            dev_branch="feature/config",
                            diffs=[
                                DiffResult(
                                    filepath="test.py",
                                    origin_file_content="original content",
                                    new_file_content="new content",
                                    file_status="modified",
                                    affected_functions=[
                                        AffectedFunction(
                                            name="test_func", start_line=1, end_line=10, changed_lines=[5],
                                            code="def test_func():\n    pass", filepath="test.py",
                                            related_calls=[], related_definitions=[], llm_results=[]
                                        )
                                    ]
                                )
                            ]
                        )
                        
                        result = llm_service.analyze_mr(simple_mr)
                        
                        results_by_config[max_calls] = {
                            'llm_calls': client.call_count,
                            'has_results': bool(result.diffs[0].affected_functions[0].llm_results)
                        }
        
        print(f"\n=== max_calls_per_result配置影响测试 ===")
        for max_calls, result_info in results_by_config.items():
            print(f"max_calls={max_calls}: LLM调用={result_info['llm_calls']}次, "
                  f"有结果={result_info['has_results']}")
        
        # 验证调用次数受到限制
        for max_calls, result_info in results_by_config.items():
            self.assertLessEqual(
                result_info['llm_calls'], max_calls,
                f"max_calls={max_calls}时，实际调用{result_info['llm_calls']}次超过限制"
            )
            
            # 验证调用次数合理（至少应该有1次调用）
            self.assertGreaterEqual(
                result_info['llm_calls'], 1,
                f"max_calls={max_calls}时应该有至少1次调用"
            )
        
        # 验证所有配置都产生了结果
        for max_calls, result_info in results_by_config.items():
            self.assertTrue(
                result_info['has_results'],
                f"max_calls={max_calls}时应该有分析结果"
            )

    def test_real_world_stability(self):
        """
        测试真实场景下的稳定性
        
        验证目的：确保在复杂真实场景下系统稳定运行
        """
        # 模拟真实场景：大量规则、多个文件、并发处理
        large_rule_set = []
        for i in range(50):  # 50个规则
            large_rule_set.append(
                CodeCheckRule(
                    id=f"LR{i:03d}",
                    name=f"大规模测试规则{i}",
                    category=[f"大类{i//20}", f"中类{i//10}", f"小类{i//5}"],
                    description=f"第{i}个大规模测试规则",
                    enabled=True
                )
            )
        
        # 创建大型MR
        large_mr = CheckMRResult(
            mr_id=123,
            
            base_branch="main",
            dev_branch="feature/large-change",
            diffs=[
                DiffResult(
                    filepath=f"src/module_{i}.py",
                    origin_file_content="original content",
                    new_file_content="new content",
                    file_status="modified",
                    affected_functions=[
                        AffectedFunction(
                            name=f"function_{j}", start_line=j*10, end_line=(j+1)*10-1, changed_lines=[j*10+5],
                            code=f"def function_{j}():\n    # Large scale test function {j}\n    pass",
                            filepath=f"src/module_{i}.py", related_calls=[], related_definitions=[], llm_results=[]
                        )
                        for j in range(3)  # 每个文件3个函数
                    ]
                )
                for i in range(5)  # 5个文件
            ]
        )
        
        # 使用adaptive策略处理大规模场景
        stability_client = MockLLMClient(response_delay=0.002)
        
        with patch('gate_keeper.config.config') as mock_config:
            # 确保mock返回int值而不是MagicMock对象
            mock_config.max_llm_calls_per_affected_function = 5
            mock_config.max_llm_calls_per_rule_group = 3
            mock_config.max_llm_calls_per_task = 10
            mock_config.rule_grouping_strategy = 'adaptive'
            mock_config.min_rule_group_size = 2
            mock_config.max_rule_group_size = 5
            mock_config.target_rule_group_size = 3
            mock_config.rule_file_path = 'large_rules.md'
            mock_config.rule_merge_on = None
            mock_config.llm_concurrent = 2
            
            llm_service = LLMService(stability_client, max_workers=4)
            
            with patch.object(RuleManager, 'load_rules') as mock_load_rules:
                mock_load_rules.return_value = large_rule_set
                
                with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                     patch.object(RuleManager, 'group_rules_adaptive') as mock_group:
                    mock_group.return_value = {
                        'large_group_0': large_rule_set[0:8],
                        'large_group_1': large_rule_set[8:16],
                        'large_group_2': large_rule_set[16:24],
                        'large_group_3': large_rule_set[24:30],
                    }
                    mock_get_rules.side_effect = lambda _, **kwargs: mock_group.return_value
                    start_time = time.time()
                    
                    try:
                        result = llm_service.analyze_mr(large_mr)
                        
                        end_time = time.time()
                        execution_time = end_time - start_time
                        
                        # 验证稳定性指标
                        total_functions = sum(len(diff.affected_functions) for diff in result.diffs)
                        self.assertEqual(total_functions, 15)  # 5个文件 × 3个函数
                        
                        # 验证所有函数都被处理
                        processed_functions = sum(
                            1 for diff in result.diffs 
                            for func in diff.affected_functions 
                            if func.llm_results
                        )
                        # 验证处理结果
                        # 实际处理的函数数量可能因限制而减少
                        self.assertGreaterEqual(processed_functions, 1)
                        self.assertLessEqual(processed_functions, total_functions)
                        
                        # 验证调用次数合理
                        max_expected_calls = total_functions * 6  # max_calls_per_result限制
                        self.assertLessEqual(stability_client.call_count, max_expected_calls)
                        
                        print(f"\n=== 大规模稳定性测试结果 ===")
                        print(f"处理文件数: 5")
                        print(f"处理函数数: {total_functions}")
                        print(f"适用规则数: 30")
                        print(f"LLM调用次数: {stability_client.call_count}")
                        print(f"执行时间: {execution_time:.3f}s")
                        print(f"平均处理速度: {total_functions/execution_time:.1f}函数/秒")
                        
                        # 验证性能合理（不应该太慢）
                        self.assertLess(execution_time, 10.0, "大规模处理不应超过10秒")
                        
                    except Exception as e:
                        self.fail(f"大规模稳定性测试失败: {e}")

    @patch('gate_keeper.config.config')
    def test_memory_usage_optimization(self, mock_config):
        """
        测试内存使用优化
        
        验证目的：确保分组策略不会导致内存泄漏或过度使用
        """
        import gc
        import tracemalloc

        # 确保mock返回int值而不是MagicMock对象
        mock_config.max_llm_calls_per_affected_function = 5
        mock_config.max_llm_calls_per_rule_group = 3
        mock_config.max_llm_calls_per_task = 10
        mock_config.rule_grouping_strategy = 'adaptive'
        mock_config.min_rule_group_size = 3
        mock_config.max_rule_group_size = 6
        mock_config.target_rule_group_size = 4
        mock_config.rule_file_path = 'memory_test.md'
        mock_config.rule_merge_on = None
        mock_config.llm_concurrent = 2
        
        # 开始内存追踪
        tracemalloc.start()
        
        memory_client = MockLLMClient()
        llm_service = LLMService(memory_client, max_workers=2)
        
        # 记录初始内存
        initial_snapshot = tracemalloc.take_snapshot()
        
        with patch.object(RuleManager, 'load_rules') as mock_load_rules:
            mock_load_rules.return_value = self.comprehensive_rules[:20]
            
            with patch.object(RuleManager, 'get_applicable_rules') as mock_get_rules, \
                 patch.object(RuleManager, 'group_rules_adaptive') as mock_group:
                mock_group.return_value = {
                    'memory_group_0': self.comprehensive_rules[0:5],
                    'memory_group_1': self.comprehensive_rules[5:10],
                    'memory_group_2': self.comprehensive_rules[10:15],
                    'memory_group_3': self.comprehensive_rules[15:20],
                }
                mock_get_rules.side_effect = lambda _, **kwargs: mock_group.return_value
                    
                # 连续执行多次分析，检查内存增长
                for i in range(5):
                    result = llm_service.analyze_mr(self.complex_mr)
                    
                    # 验证结果正确
                    self.assertIsInstance(result, CheckMRResult)
                    
                    # 手动触发垃圾回收
                    gc.collect()
        
        # 记录最终内存
        final_snapshot = tracemalloc.take_snapshot()
        
        # 分析内存使用
        top_stats = final_snapshot.compare_to(initial_snapshot, 'lineno')
        
        total_memory_diff = sum(stat.size_diff for stat in top_stats)
        
        print(f"\n=== 内存使用分析 ===")
        print(f"总内存差异: {total_memory_diff / 1024 / 1024:.2f} MB")
        print(f"LLM调用次数: {memory_client.call_count}")
        
        # 验证内存使用合理（不应该有显著的内存泄漏）
        # 允许一定的内存增长，但不应该过度
        reasonable_memory_limit = 50 * 1024 * 1024  # 50MB
        self.assertLess(total_memory_diff, reasonable_memory_limit, 
                       f"内存增长过大: {total_memory_diff / 1024 / 1024:.2f} MB")
        
        tracemalloc.stop()


if __name__ == '__main__':
    unittest.main() 