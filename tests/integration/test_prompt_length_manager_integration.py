"""
提示词长度管理器集成测试

测试与其他模块的集成：
1. 与 LLMService 的集成
2. 与 ContextManager 的集成
3. 与 RuleManager 的集成
4. 端到端工作流测试
"""

import unittest
from typing import List
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import (CallChainContext,
                                                             ContextManager)
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.prompt import \
    PromptLengthManager
from gate_keeper.application.service.rule import RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestPromptLengthManagerIntegration(unittest.TestCase):
    """测试提示词长度管理器集成"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的上下文管理器
        self.mock_context_manager = Mock(spec=ContextManager)
        
        # 创建提示词长度管理器
        self.prompt_manager = PromptLengthManager(
            max_prompt_length=8000,
            context_manager=self.mock_context_manager,
            allocation_strategy="balanced"
        )
        
        # 创建测试规则
        self.test_rules = [
            CodeCheckRule(
                id="security_rule",
                name="安全规则",
                description="检查输入验证",
                category=["安全"],
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="performance_rule",
                name="性能规则",
                description="检查循环效率",
                category=["性能"],
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="naming_rule",
                name="命名规则",
                description="检查函数命名",
                category=["命名"],
                enabled=True,
                languages=["python"]
            )
        ]
        
        # 创建测试上下文
        self.test_contexts = [
            CallChainContext(
                chain=["main", "process_data"],
                functions=[],
                relevance_score=0.9,
                context_size=500
            ),
            CallChainContext(
                chain=["main", "process_data", "validate_input"],
                functions=[],
                relevance_score=0.7,
                context_size=800
            )
        ]
        
        # 创建测试函数
        self.test_function = AffectedFunction(
            name="test_function",
            filepath="test.py",
            code="def test_function():\n    pass",
            start_line=1,
            end_line=2,
            changed_lines=[1, 2],
            related_definitions=[]
        )
    
    def test_integration_with_llm_service(self):
        """测试与LLMService的集成"""
        # 模拟LLMService
        mock_llm_client = Mock()
        mock_static_analyzer = Mock()
        
        llm_service = LLMService(
            client=mock_llm_client,
            static_analyzer=mock_static_analyzer,
            use_optimized_context=True
        )
        
        # 模拟上下文管理器生成上下文
        self.mock_context_manager.generate_optimized_contexts.return_value = self.test_contexts
        
        # 测试提示词生成
        system_prompt = "你是一个代码质量检查助手"
        output_format = "请返回JSON格式"
        
        optimized_prompt, allocation = self.prompt_manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=self.test_rules,
            affected_function=self.test_function,
            contexts=self.test_contexts,
            output_format=output_format
        )
        
        # 验证结果
        self.assertIsNotNone(optimized_prompt)
        self.assertLessEqual(len(optimized_prompt), self.prompt_manager.max_prompt_length)
        self.assertIn(system_prompt, optimized_prompt)
        self.assertIn(self.test_function.code, optimized_prompt)
        self.assertIn(output_format, optimized_prompt)
        
        # 验证分配方案
        self.assertIsNotNone(allocation)
        self.assertEqual(allocation.total_limit, self.prompt_manager.max_prompt_length)
    
    def test_integration_with_context_manager(self):
        """测试与ContextManager的集成"""
        # 模拟ContextManager的方法
        self.mock_context_manager.format_context_for_prompt.return_value = "模拟上下文内容"
        self.mock_context_manager.get_context_summary.return_value = "上下文摘要"
        
        # 测试上下文选择
        selected_contexts = self.prompt_manager.select_optimal_contexts(
            self.test_contexts, 1000
        )
        
        # 验证选择结果
        self.assertLessEqual(len(selected_contexts), len(self.test_contexts))
        if selected_contexts:
            # 验证按相关性排序
            self.assertEqual(selected_contexts[0].relevance_score, 0.9)
    
    def test_integration_with_rule_manager(self):
        """测试与RuleManager的集成"""
        # 模拟RuleManager
        mock_rule_manager = Mock(spec=RuleManager)
        mock_rule_manager.get_applicable_rules.return_value = {
            "security": [self.test_rules[0]],
            "performance": [self.test_rules[1]],
            "naming": [self.test_rules[2]]
        }
        
        # 测试规则选择
        selected_rules = self.prompt_manager.select_optimal_rules(
            self.test_rules, 1000
        )
        
        # 验证选择结果
        self.assertLessEqual(len(selected_rules), len(self.test_rules))
        if selected_rules:
            # 验证安全规则优先级更高
            first_rule = selected_rules[0]
            self.assertIn("安全", first_rule.category)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 1. 模拟完整的分析流程
        system_prompt = "你是一个代码质量检查助手"
        output_format = "请返回JSON格式"
        
        # 2. 生成优化提示词
        optimized_prompt, allocation = self.prompt_manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=self.test_rules,
            affected_function=self.test_function,
            contexts=self.test_contexts,
            output_format=output_format
        )
        
        # 3. 验证提示词结构
        self.assertIn("编码规范:", optimized_prompt)
        self.assertIn("调用链上下文:", optimized_prompt)
        self.assertIn("待检查内容:", optimized_prompt)
        
        # 4. 验证长度控制
        self.assertLessEqual(len(optimized_prompt), self.prompt_manager.max_prompt_length)
        
        # 5. 验证内容完整性
        self.assertIn(system_prompt, optimized_prompt)
        self.assertIn(self.test_function.code, optimized_prompt)
        self.assertIn(output_format, optimized_prompt)
    
    def test_different_allocation_strategies(self):
        """测试不同分配策略的集成效果"""
        strategies = ["balanced", "context_heavy", "rules_heavy"]
        
        for strategy in strategies:
            with self.subTest(strategy=strategy):
                # 创建使用不同策略的管理器
                strategy_manager = PromptLengthManager(
                    max_prompt_length=8000,
                    allocation_strategy=strategy
                )
                
                system_prompt = "测试"
                target_code = "def test(): pass"
                
                # 计算分配方案
                allocation = strategy_manager.calculate_prompt_allocation(
                    system_prompt, target_code
                )
                
                # 验证分配结果
                self.assertGreater(allocation.rules_length, 0)
                self.assertGreater(allocation.context_length, 0)
                
                # 验证策略差异
                if strategy == "context_heavy":
                    self.assertGreater(allocation.context_length, allocation.rules_length)
                elif strategy == "rules_heavy":
                    self.assertGreater(allocation.rules_length, allocation.context_length)
    
    def test_large_scale_integration(self):
        """测试大规模集成场景"""
        # 创建大量规则和上下文
        large_rules = []
        for i in range(50):
            rule = CodeCheckRule(
                id=f"rule_{i}",
                name=f"规则{i}",
                description=f"这是第{i}个规则的描述",
                category=["测试"],
                enabled=True,
                languages=["python"]
            )
            large_rules.append(rule)
        
        large_contexts = []
        for i in range(30):
            context = CallChainContext(
                chain=[f"func_{j}" for j in range(i % 5 + 1)],
                functions=[],
                relevance_score=0.1 + (i * 0.02),
                context_size=100 + i * 20
            )
            large_contexts.append(context)
        
        # 测试大规模优化
        system_prompt = "大规模测试"
        output_format = "JSON格式"
        
        optimized_prompt, allocation = self.prompt_manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=large_rules,
            affected_function=self.test_function,
            contexts=large_contexts,
            output_format=output_format
        )
        
        # 验证大规模处理结果
        self.assertLessEqual(len(optimized_prompt), self.prompt_manager.max_prompt_length)
        self.assertIsNotNone(allocation)
        
        # 验证性能（应该在合理时间内完成）
        self.assertGreater(len(optimized_prompt), 0)
    
    def test_error_recovery_integration(self):
        """测试错误恢复集成"""
        # 测试包含无效数据的场景
        mixed_rules = [self.test_rules[0], None, self.test_rules[1]]
        mixed_contexts = [self.test_contexts[0], None, self.test_contexts[1]]
        
        # 应该能够处理无效数据而不崩溃
        selected_rules = self.prompt_manager.select_optimal_rules(mixed_rules, 1000)
        selected_contexts = self.prompt_manager.select_optimal_contexts(mixed_contexts, 1000)
        
        # 验证错误恢复
        self.assertLessEqual(len(selected_rules), len([r for r in mixed_rules if r is not None]))
        self.assertLessEqual(len(selected_contexts), len([c for c in mixed_contexts if c is not None]))
    
    def test_memory_efficiency_integration(self):
        """测试内存效率集成"""
        # 测试大量数据时的内存使用
        large_rules = []
        for i in range(100):
            rule = CodeCheckRule(
                id=f"rule_{i}",
                name=f"规则{i}",
                description="x" * 1000,  # 长描述
                category=["测试"],
                enabled=True,
                languages=["python"]
            )
            large_rules.append(rule)
        
        # 测试内存效率
        selected_rules = self.prompt_manager.select_optimal_rules(large_rules, 5000)
        
        # 验证内存效率（选择的数量应该远小于总数）
        self.assertLess(len(selected_rules), len(large_rules))
        self.assertGreater(len(selected_rules), 0)
    
    def test_configuration_integration(self):
        """测试配置集成"""
        # 测试不同配置参数的效果
        configs = [
            {"max_prompt_length": 4000, "strategy": "balanced"},
            {"max_prompt_length": 8000, "strategy": "context_heavy"},
            {"max_prompt_length": 12000, "strategy": "rules_heavy"}
        ]
        
        for config in configs:
            with self.subTest(config=config):
                manager = PromptLengthManager(
                    max_prompt_length=config["max_prompt_length"],
                    allocation_strategy=config["strategy"]
                )
                
                system_prompt = "配置测试"
                target_code = "def test(): pass"
                
                allocation = manager.calculate_prompt_allocation(system_prompt, target_code)
                
                # 验证配置生效
                self.assertEqual(allocation.total_limit, config["max_prompt_length"])
                self.assertGreater(allocation.rules_length, 0)
                self.assertGreater(allocation.context_length, 0)


if __name__ == "__main__":
    unittest.main() 