"""
代码图构建端到端集成测试

测试完整的代码图构建和分析流程：
- 多文件项目索引
- 跨文件调用关系解析
- 调用链分析
- 上下文生成
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.external.code_analyzer import RepositoryIndex, StaticAnalyzer
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestCodeGraphIntegration(unittest.TestCase):
    """代码图构建端到端集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时目录作为测试仓库
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = Path(self.temp_dir)
        
        # 创建模拟的Git服务
        self.mock_git_service = Mock()
        
        # 创建CodeRepositoryService
        from gate_keeper.application.service.git.code_repository_service import \
            CodeRepositoryService
        self.code_repo_service = CodeRepositoryService()
        
        # 创建RepositoryAnalyzer
        self.repo_analyzer = RepositoryAnalyzer(git_service=self.code_repo_service)
        
        # 创建RepositoryIndex用于测试
        self.repo_index = RepositoryIndex(repo_dir=str(self.repo_dir))
        
        # 创建ContextManager（StaticAnalyzer会在需要时创建）
        self.context_manager = ContextManager(
            static_analyzer=None)
        
        # 初始化static_analyzer为None，在需要时创建
        self.static_analyzer = None
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_files(self):
        """创建测试文件"""
        # 创建main.c
        main_c_content = """
#include <stdio.h>
#include "utils.h"

int main() {
    printf("Starting application\\n");
    process_data();
    return 0;
}

void process_data() {
    validate_input();
    transform_data();
    save_result();
}
"""
        
        # 创建utils.c
        utils_c_content = """
#include "utils.h"

void validate_input() {
    printf("Validating input\\n");
}

void transform_data() {
    printf("Transforming data\\n");
    format_output();
}

void format_output() {
    printf("Formatting output\\n");
}

void save_result() {
    printf("Saving result\\n");
}
"""
        
        # 创建utils.h
        utils_h_content = """
#ifndef UTILS_H
#define UTILS_H

void validate_input();
void transform_data();
void format_output();
void save_result();

#endif
"""
        
        # 写入文件
        (self.repo_dir / "main.c").write_text(main_c_content)
        (self.repo_dir / "utils.c").write_text(utils_c_content)
        (self.repo_dir / "utils.h").write_text(utils_h_content)
    
    def test_complete_code_graph_workflow(self):
        """测试完整的代码图构建工作流"""
        # 创建测试文件
        self.create_test_files()
        
        # 模拟函数定义
        main_func = Function.create_simple(
            name="main",
            start_line=4,
            end_line=8,
            filepath="main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() {\n    printf(\"Starting application\\n\");\n    process_data();\n    return 0;\n}"
        )
        
        process_func = Function.create_simple(
            name="process_data",
            start_line=10,
            end_line=15,
            filepath="main.c",
            signature=FunctionSignature(name="process_data", parameters=[]),
            code="void process_data() {\n    validate_input();\n    transform_data();\n    save_result();\n}"
        )
        
        validate_func = Function.create_simple(
            name="validate_input",
            start_line=4,
            end_line=6,
            filepath="utils.c",
            signature=FunctionSignature(name="validate_input", parameters=[]),
            code="void validate_input() {\n    printf(\"Validating input\\n\");\n}"
        )
        
        transform_func = Function.create_simple(
            name="transform_data",
            start_line=8,
            end_line=11,
            filepath="utils.c",
            signature=FunctionSignature(name="transform_data", parameters=[]),
            code="void transform_data() {\n    printf(\"Transforming data\\n\");\n    format_output();\n}"
        )
        
        format_func = Function.create_simple(
            name="format_output",
            start_line=13,
            end_line=15,
            filepath="utils.c",
            signature=FunctionSignature(name="format_output", parameters=[]),
            code="void format_output() {\n    printf(\"Formatting output\\n\");\n}"
        )
        
        save_func = Function.create_simple(
            name="save_result",
            start_line=17,
            end_line=19,
            filepath="utils.c",
            signature=FunctionSignature(name="save_result", parameters=[]),
            code="void save_result() {\n    printf(\"Saving result\\n\");\n}"
        )
        
        # 创建调用关系
        calls = [
            FunctionCall(
                caller="main",
                callee="process_data",
                line=(6, 6),
                file_path="main.c",
                code="process_data();"
            ),
            FunctionCall(
                caller="process_data",
                callee="validate_input",
                line=(12, 12),
                file_path="main.c",
                code="validate_input();"
            ),
            FunctionCall(
                caller="process_data",
                callee="transform_data",
                line=(13, 13),
                file_path="main.c",
                code="transform_data();"
            ),
            FunctionCall(
                caller="process_data",
                callee="save_result",
                line=(14, 14),
                file_path="main.c",
                code="save_result();"
            ),
            FunctionCall(
                caller="transform_data",
                callee="format_output",
                line=(10, 10),
                file_path="utils.c",
                code="format_output();"
            )
        ]
        
        # 使用CodeGraphBuilder构建代码图
        parser_results = [
            ("main.c", [main_func, process_func], calls[:4], []),
            ("utils.c", [validate_func, transform_func, format_func, save_func], [calls[4]], [])
        ]
        self.repo_index.graph_builder.build_from_parser_results(parser_results)
        
        # 更新兼容性属性
        self.repo_index.function_definitions = self.repo_index.graph_builder.get_function_definitions()
        self.repo_index.function_calls = self.repo_index.graph_builder.get_function_calls()
        self.repo_index.call_graph = self.repo_index.graph_builder.get_call_graph()
        
        # 创建StaticAnalyzer（在构建图之后）
        self.static_analyzer = StaticAnalyzer(self.repo_index)
        
        # 验证调用图构建
        graph = self.repo_index.get_call_graph()
        
        # 检查节点
        expected_nodes = [
            "main.c:function:main",
            "main.c:function:process_data", 
            "utils.c:function:validate_input",
            "utils.c:function:transform_data",
            "utils.c:function:format_output",
            "utils.c:function:save_result"
        ]
        
        for node in expected_nodes:
            self.assertTrue(graph.has_node(node), f"Missing node: {node}")
        
        # 检查边
        expected_edges = [
            ("main.c:function:main", "main.c:function:process_data"),
            ("main.c:function:process_data", "utils.c:function:validate_input"),
            ("main.c:function:process_data", "utils.c:function:transform_data"),
            ("main.c:function:process_data", "utils.c:function:save_result"),
            ("utils.c:function:transform_data", "utils.c:function:format_output")
        ]
        
        for edge in expected_edges:
            self.assertTrue(graph.has_edge(*edge), f"Missing edge: {edge}")
    
    def test_call_chain_analysis(self):
        """测试调用链分析"""
        # 创建测试文件
        self.create_test_files()
        
        # 模拟函数定义和调用关系（简化版本）
        main_func = Function.create_simple(
            name="main",
            start_line=4,
            end_line=8,
            filepath="main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { process_data(); return 0; }"
        )
        
        process_func = Function.create_simple(
            name="process_data",
            start_line=10,
            end_line=15,
            filepath="main.c",
            signature=FunctionSignature(name="process_data", parameters=[]),
            code="void process_data() { validate_input(); }"
        )
        
        validate_func = Function.create_simple(
            name="validate_input",
            start_line=4,
            end_line=6,
            filepath="utils.c",
            signature=FunctionSignature(name="validate_input", parameters=[]),
            code="void validate_input() { printf(\"Validating\"); }"
        )
        
        # 添加调用关系
        call1 = FunctionCall(
            caller="main",
            callee="process_data",
            line=(6, 6),
            file_path="main.c",
            code="process_data();"
        )
        
        call2 = FunctionCall(
            caller="process_data",
            callee="validate_input",
            line=(12, 12),
            file_path="main.c",
            code="validate_input();"
        )
        
        # 使用CodeGraphBuilder构建代码图
        parser_results = [
            ("main.c", [main_func, process_func], [call1, call2], []),
            ("utils.c", [validate_func], [], [])
        ]
        self.repo_index.graph_builder.build_from_parser_results(parser_results)
        
        # 更新兼容性属性
        self.repo_index.function_definitions = self.repo_index.graph_builder.get_function_definitions()
        self.repo_index.function_calls = self.repo_index.graph_builder.get_function_calls()
        self.repo_index.call_graph = self.repo_index.graph_builder.get_call_graph()
        
        # 创建StaticAnalyzer（在构建图之后）
        self.static_analyzer = StaticAnalyzer(self.repo_index)
        
        # 测试调用链分析
        # 获取process_data的上游调用链
        upstream_chains = self.static_analyzer.get_upstream_call_chains("process_data", "main.c", max_depth=3)
        self.assertGreater(len(upstream_chains), 0)
        
        # 验证包含main -> process_data的调用链
        main_to_process_found = any(
            "main.c:function:main" in chain and "main.c:function:process_data" in chain
            for chain in upstream_chains
        )
        self.assertTrue(main_to_process_found)
        
        # 获取process_data的下游调用链
        downstream_chains = self.static_analyzer.get_downstream_call_chains("process_data", "main.c", max_depth=3)
        self.assertGreater(len(downstream_chains), 0)
        
        # 验证包含process_data -> validate_input的调用链
        process_to_validate_found = any(
            "main.c:function:process_data" in chain and "utils.c:function:validate_input" in chain
            for chain in downstream_chains
        )
        self.assertTrue(process_to_validate_found)
        
        # 获取双向调用链
        bidirectional_chains = self.static_analyzer.get_bidirectional_call_chains("process_data", "main.c", max_depth=3)
        self.assertGreater(len(bidirectional_chains), 0)
        
        # 验证包含完整调用链
        complete_chain_found = any(
            "main.c:function:main" in chain and "main.c:function:process_data" in chain and "utils.c:function:validate_input" in chain
            for chain in bidirectional_chains
        )
        self.assertTrue(complete_chain_found)
    
    def test_context_generation(self):
        """测试上下文生成"""
        # 创建测试文件
        self.create_test_files()
        
        # 模拟函数定义
        main_func = Function.create_simple(
            name="main",
            start_line=4,
            end_line=8,
            filepath="main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { process_data(); return 0; }"
        )
        
        process_func = Function.create_simple(
            name="process_data",
            start_line=10,
            end_line=15,
            filepath="main.c",
            signature=FunctionSignature(name="process_data", parameters=[]),
            code="void process_data() { validate_input(); }"
        )
        
        validate_func = Function.create_simple(
            name="validate_input",
            start_line=4,
            end_line=6,
            filepath="utils.c",
            signature=FunctionSignature(name="validate_input", parameters=[]),
            code="void validate_input() { printf(\"Validating\"); }"
        )
        
        # 添加调用关系
        call1 = FunctionCall(
            caller="main",
            callee="process_data",
            line=(6, 6),
            file_path="main.c",
            code="process_data();"
        )
        
        call2 = FunctionCall(
            caller="process_data",
            callee="validate_input",
            line=(12, 12),
            file_path="main.c",
            code="validate_input();"
        )
        
        # 使用CodeGraphBuilder构建代码图
        parser_results = [
            ("main.c", [main_func, process_func], [call1, call2], []),
            ("utils.c", [validate_func], [], [])
        ]
        self.repo_index.graph_builder.build_from_parser_results(parser_results)
        
        # 更新兼容性属性
        self.repo_index.function_definitions = self.repo_index.graph_builder.get_function_definitions()
        self.repo_index.function_calls = self.repo_index.graph_builder.get_function_calls()
        self.repo_index.call_graph = self.repo_index.graph_builder.get_call_graph()
        
        # 创建StaticAnalyzer（在构建图之后）
        self.static_analyzer = StaticAnalyzer(self.repo_index)
        
        # 更新ContextManager的static_analyzer
        self.context_manager.static_analyzer = self.static_analyzer
        
        # 创建AffectedFunction
        affected_func = AffectedFunction(
            name="process_data",
            type="function",
            start_line=10,
            end_line=15,
            changed_lines=[12],
            code="void process_data() { validate_input(); }",
            related_calls=[],
            related_definitions=[],
            filepath="main.c"
        )
        
        # 生成上下文
        contexts = self.context_manager.generate_optimized_contexts(affected_func, max_chains=2)
        
        # 验证上下文生成
        self.assertGreater(len(contexts), 0)
        
        # 验证上下文包含相关函数
        context_functions = []
        for context in contexts:
            context_functions.extend([f.name for f in context.functions])
        
        # 应该包含main和validate_input
        self.assertIn("main", context_functions)
        self.assertIn("validate_input", context_functions)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 创建基本的static_analyzer用于测试
        if self.static_analyzer is None:
            self.static_analyzer = StaticAnalyzer(self.repo_index)
        
        # 测试空文件
        from gate_keeper.application.service.git.code_repository_service import \
            CodeRepositoryService
        empty_code_repo_service = CodeRepositoryService()
        empty_repo_analyzer = RepositoryAnalyzer(git_service=empty_code_repo_service)
        
        # 测试不存在的函数
        chains = self.static_analyzer.get_bidirectional_call_chains("nonexistent", "dummy.c")
        self.assertEqual(len(chains), 0)
        
        # 测试空调用图
        empty_repo_index = RepositoryIndex(repo_dir=str(self.repo_dir))
        empty_analyzer = StaticAnalyzer(empty_repo_index)
        chains = empty_analyzer.get_bidirectional_call_chains("any_func", "any_file.c", max_depth=3)
        self.assertEqual(len(chains), 0)
    
    def test_performance_with_large_project(self):
        """测试大型项目性能"""
        # 创建多个文件模拟大型项目
        for i in range(10):
            file_content = f"""
#include <stdio.h>

void func_{i}() {{
    printf("Function {i}\\n");
    func_{(i+1) % 10}();
}}

void helper_{i}() {{
    printf("Helper {i}\\n");
}}
"""
            (self.repo_dir / f"file_{i}.c").write_text(file_content)
        
        # 模拟函数定义和调用关系
        functions = []
        calls = []
        
        for i in range(10):
            func = Function.create_simple(
                name=f"func_{i}",
                start_line=4,
                end_line=7,
                filepath=f"file_{i}.c",
                signature=FunctionSignature(name=f"func_{i}", parameters=[]),
                code=f"void func_{i}() {{ printf(\"Function {i}\\n\"); func_{(i+1) % 10}(); }}"
            )
            functions.append(func)
            
            helper = Function.create_simple(
                name=f"helper_{i}",
                start_line=9,
                end_line=11,
                filepath=f"file_{i}.c",
                signature=FunctionSignature(name=f"helper_{i}", parameters=[]),
                code=f"void helper_{i}() {{ printf(\"Helper {i}\\n\"); }}"
            )
            functions.append(helper)
            
            call = FunctionCall(
                caller=f"func_{i}",
                callee=f"func_{(i+1) % 10}",
                line=(6, 6),
                file_path=f"file_{i}.c",
                code=f"func_{(i+1) % 10}();"
            )
            calls.append(call)
        
        # 构建所有模块的解析结果
        parser_results = []
        for i in range(10):
            file_funcs = [f for f in functions if f.filepath == f"file_{i}.c"]
            file_calls = [c for c in calls if c.file_path == f"file_{i}.c"]
            parser_results.append((f"file_{i}.c", file_funcs, file_calls, []))
        
        # 使用CodeGraphBuilder构建代码图
        self.repo_index.graph_builder.build_from_parser_results(parser_results)
        
        # 更新兼容性属性
        self.repo_index.function_definitions = self.repo_index.graph_builder.get_function_definitions()
        self.repo_index.function_calls = self.repo_index.graph_builder.get_function_calls()
        self.repo_index.call_graph = self.repo_index.graph_builder.get_call_graph()
        
        # 创建StaticAnalyzer（在构建图之后）
        self.static_analyzer = StaticAnalyzer(self.repo_index)
        
        # 测试性能
        import time
        start_time = time.time()
        
        # 获取调用链
        chains = self.static_analyzer.get_bidirectional_call_chains("func_0", "file_0.c", max_depth=5)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能（应该在合理时间内完成）
        self.assertLess(execution_time, 2.0)
        
        # 验证结果
        self.assertGreater(len(chains), 0)


if __name__ == '__main__':
    unittest.main() 