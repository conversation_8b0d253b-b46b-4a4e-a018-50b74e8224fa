"""
真实场景集成测试

测试目标：验证在真实场景下的MR分析功能，包括：
- 复杂的代码结构分析
- 错误恢复机制
- 性能测试

验证重点：
- 真实代码场景的处理
- 错误处理和恢复
- 性能表现
"""

import tempfile
import time
from unittest.mock import Mock

import pytest

from gate_keeper.application.dto.result import AffectedFunction, CheckMRResult
from gate_keeper.application.interfaces.git_intf import ICodeRepositoryService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.external.code_analyzer import RepositoryIndex


class TestRealScenarioIntegration:
    """真实场景集成测试类"""
    
    @pytest.fixture
    def temp_repo_dir(self):
        """创建临时仓库目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    def test_complex_code_analysis(self, temp_repo_dir):
        """测试复杂代码分析场景"""
        
        # 创建复杂的代码结构
        complex_code = '''
import logging
from typing import List, Optional

class DataProcessor:
    """数据处理类"""
    
    def __init__(self, config: dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def validate_input(self, data: List[float]) -> bool:
        """验证输入数据"""
        if not isinstance(data, list):
            return False
        return all(isinstance(x, (int, float)) for x in data)
    
    def process_data(self, data: List[float]) -> List[float]:
        """处理数据"""
        if not self.validate_input(data):
            raise ValueError("无效的输入数据")
        
        result = []
        for item in data:
            processed_item = self._transform_item(item)
            result.append(processed_item)
        
        return result
    
    def _transform_item(self, item: float) -> float:
        """转换单个数据项"""
        return item * self.config.get('multiplier', 1.0)

def main():
    """主函数"""
    config = {'multiplier': 2.0}
    processor = DataProcessor(config)
    
    data = [1.0, 2.0, 3.0, 4.0, 5.0]
    result = processor.process_data(data)
    
    return result
'''
        
        # 创建模拟服务
        mock_git_service = Mock(spec=ICodeRepositoryService)
        mock_llm_service = Mock(spec=LLMService)
        mock_repo_analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 设置返回值
        mock_patched_file = Mock()
        mock_patched_file.path = "src/processor.py"
        mock_patched_file.is_removed_file = False
        mock_patched_file.is_added_file = False
        mock_patched_file.__iter__ = lambda self: iter([])
        mock_git_service.get_diff_by_mr_id.return_value = [mock_patched_file]
        mock_git_service.get_file_content_by_ref.return_value = complex_code
        
        # 模拟复杂的函数关系
        mock_affected_functions = [
            AffectedFunction(
                name="validate_input",
                filepath="src/processor.py",
                start_line=12,
                end_line=16,
                changed_lines=[13, 14, 15],
                code="def validate_input(data):\n    return True",
                call_chains=[["main", "process_data", "validate_input"]],
                related_definitions=[],
                related_calls=[]
            ),
            AffectedFunction(
                name="process_data",
                filepath="src/processor.py",
                start_line=18,
                end_line=28,
                changed_lines=[19, 20, 21, 22, 23, 24, 25, 26, 27],
                code="def process_data(data):\n    return data",
                call_chains=[["main", "process_data"], ["process_data", "validate_input"], ["process_data", "_transform_item"]],
                related_definitions=[],
                related_calls=[]
            ),
            AffectedFunction(
                name="_transform_item",
                filepath="src/processor.py",
                start_line=30,
                end_line=32,
                changed_lines=[31],
                code="def _transform_item(item):\n    return item",
                call_chains=[["process_data", "_transform_item"]],
                related_definitions=[],
                related_calls=[]
            )
        ]
        mock_repo_analyzer.analyze_functions.return_value = mock_affected_functions
        
        # 模拟LLM响应
        mock_result = CheckMRResult(
            mr_id=789,
            base_branch="main",
            dev_branch="feature/complex-refactor",
            diffs=[]
        )
        mock_llm_service.analyze_mr.return_value = mock_result
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 执行分析
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=789,
            base_branch="main",
            dev_branch="feature/complex-refactor",
            max_context_chain_depth=4,  # 更深的调用链
            exclude_patterns=["*.pyc", "__pycache__", "tests/*"],
            max_diff_results=100
        )
        
        # 验证结果
        assert analyze_results is not None
        assert mock_repo_analyzer.analyze_functions.called
    
    def test_error_recovery_scenario(self, temp_repo_dir):
        """测试错误恢复场景"""
        
        # 创建模拟服务，模拟部分失败的情况
        mock_git_service = Mock(spec=ICodeRepositoryService)
        mock_llm_service = Mock(spec=LLMService)
        mock_repo_analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 模拟Git服务异常
        mock_git_service.get_diff_by_mr_id.side_effect = Exception("Git service error")
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 执行分析，应该抛出异常
        with pytest.raises(Exception):
            usecase.execute(
                repo_dir=temp_repo_dir,
                mr_id=999,
                base_branch="main",
                dev_branch="feature/error-recovery",
                max_context_chain_depth=3,
                exclude_patterns=[],
                max_diff_results=None
            )
    
    def test_performance_under_load(self, temp_repo_dir):
        """测试负载下的性能"""
        
        # 创建大量文件的模拟
        mock_git_service = Mock(spec=ICodeRepositoryService)
        mock_llm_service = Mock(spec=LLMService)
        mock_repo_analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 模拟大量变更文件
        mock_patched_file = Mock()
        mock_patched_file.path = "src/large_file.py"
        mock_patched_file.is_removed_file = False
        mock_patched_file.is_added_file = False
        mock_patched_file.__iter__ = lambda self: iter([])
        mock_git_service.get_diff_by_mr_id.return_value = [mock_patched_file]
        mock_git_service.get_file_content_by_ref.return_value = "def test(): pass"
        
        # 模拟大量函数
        large_functions = []
        for i in range(10):  # 减少到10个函数以提高测试速度
            large_functions.append(AffectedFunction(
                name=f"function_{i}",
                filepath=f"src/file_{i//2}.py",
                start_line=1,
                end_line=10,
                changed_lines=[5],
                code=f"def function_{i}():\n    pass",
                call_chains=[[f"function_{i}"]],
                related_definitions=[],
                related_calls=[]
            ))
        
        mock_repo_analyzer.analyze_functions.return_value = large_functions
        
        # 模拟LLM响应
        mock_result = CheckMRResult(
            mr_id=1000,
            base_branch="main",
            dev_branch="feature/large-scale",
            diffs=[]
        )
        mock_llm_service.analyze_mr.return_value = mock_result
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行分析
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=1000,
            base_branch="main",
            dev_branch="feature/large-scale",
            max_context_chain_depth=2,  # 限制深度以提高性能
            exclude_patterns=["*.pyc", "__pycache__"],
            max_diff_results=50  # 限制结果数量
        )
        
        # 记录结束时间
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能
        assert execution_time < 10.0  # 10秒内应该完成
        
        # 验证结果
        assert analyze_results is not None 