#!/usr/bin/env python3
"""
超时控制装饰器
"""

import functools
import signal
import time
from typing import Any, Callable


def timeout(seconds: int = 60):
    """
    超时装饰器，防止测试卡死
    
    Args:
        seconds: 超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            def timeout_handler(signum, frame):
                raise TimeoutError(f"测试超时: {func.__name__} 在 {seconds} 秒内未完成")
            
            # 设置信号处理器
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(seconds)
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 恢复信号处理器
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator

def timeout_with_logging(seconds: int = 60):
    """
    带日志的超时装饰器
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            print(f"开始执行 {func.__name__}，超时时间: {seconds}秒")
            
            def timeout_handler(signum, frame):
                elapsed = time.time() - start_time
                raise TimeoutError(f"测试超时: {func.__name__} 在 {elapsed:.2f} 秒后超时（限制: {seconds}秒）")
            
            # 设置信号处理器
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(seconds)
            
            try:
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time
                print(f"{func.__name__} 执行完成，耗时: {elapsed:.2f}秒")
                return result
            finally:
                # 恢复信号处理器
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator 