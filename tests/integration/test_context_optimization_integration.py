"""
上下文优化集成测试 - 验证基于调用链的智能上下文管理
"""

from pathlib import Path
from unittest.mock import MagicMock, Mock

import pytest

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.application.service.llm import LLMService
from gate_keeper.external.code_analyzer import \
    StaticAnalyzer
from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)
from gate_keeper.infrastructure.llm.client.base import LLMClient


class TestContextOptimizationIntegration:
    """测试上下文优化集成"""
    
    def setup_method(self):
        """设置测试环境"""
        # 创建模拟的 LLMClient
        self.mock_llm_client = Mock(spec=LLMClient)
        
        # 创建模拟的 RepositoryIndex
        self.mock_repo_index = Mock()
        self.mock_repo_index.function_calls = []
        
        # 创建 StaticAnalyzer
        self.static_analyzer = StaticAnalyzer(self.mock_repo_index)
        
        # 创建测试用的 AffectedFunction
        self.test_af = AffectedFunction(
            name="process_data",
            start_line=10,
            end_line=20,
            changed_lines=[12, 15],
            code="def process_data(data):\n    if validate_input(data):\n        return transform_data(data)\n    return None",
            related_calls=[],
            related_definitions=[],
            filepath="data_processor.py"
        )
    
    def test_llm_service_with_optimized_context(self):
        """测试 LLMService 使用优化上下文"""
        # 模拟函数定义
        mock_functions = {
            "main": [self._create_mock_function("main", "def main():\n    data = get_data()\n    result = process_data(data)\n    return result", "main.py")],
            "process_data": [self._create_mock_function("process_data", "def process_data(data):\n    if validate_input(data):\n        return transform_data(data)\n    return None", "data_processor.py")],
            "validate_input": [self._create_mock_function("validate_input", "def validate_input(data):\n    if not data:\n        return False\n    return isinstance(data, dict)", "validator.py")],
            "transform_data": [self._create_mock_function("transform_data", "def transform_data(data):\n    return {k: v for k, v in data.items() if v}", "transformer.py")]
        }
        self.mock_repo_index.function_definitions = mock_functions
        
        # 创建 LLMService（启用优化上下文）
        llm_service = LLMService(
            client=self.mock_llm_client,
            static_analyzer=self.static_analyzer,
            use_optimized_context=True
        )
        
        # 验证上下文管理器已初始化
        assert llm_service.context_manager is not None
        assert isinstance(llm_service.context_manager, ContextManager)
        assert llm_service.use_optimized_context is True
    
    def test_llm_service_without_optimized_context(self):
        """测试 LLMService 不使用优化上下文"""
        # 创建 LLMService（不启用优化上下文）
        llm_service = LLMService(
            client=self.mock_llm_client,
            use_optimized_context=False
        )
        
        # 验证上下文管理器未初始化
        assert llm_service.context_manager is None
        assert llm_service.use_optimized_context is False
    
    def test_prompt_generation_with_optimized_context(self):
        """测试使用优化上下文生成 prompt"""
        # 模拟函数定义
        mock_functions = {
            "main": [self._create_mock_function("main", "def main():\n    data = get_data()\n    result = process_data(data)\n    return result", "main.py")],
            "process_data": [self._create_mock_function("process_data", "def process_data(data):\n    if validate_input(data):\n        return transform_data(data)\n    return None", "data_processor.py")],
            "validate_input": [self._create_mock_function("validate_input", "def validate_input(data):\n    if not data:\n        return False\n    return isinstance(data, dict)", "validator.py")]
        }
        self.mock_repo_index.function_definitions = mock_functions
        
        # 创建 LLMService（启用优化上下文）
        llm_service = LLMService(
            client=self.mock_llm_client,
            static_analyzer=self.static_analyzer,
            use_optimized_context=True
        )
        
        # 模拟调用链
        self.static_analyzer.get_bidirectional_call_chains = Mock(return_value=[
            ["main", "process_data", "validate_input"]
        ])
        
        # 生成 prompt
        from gate_keeper.domain.rule.check_rule import CodeCheckRule
        mock_rules = [CodeCheckRule(
            id="test_rule", 
            name="Test Rule", 
            description="Test",
            category=["test"]
        )]
        
        prompt = llm_service._generate_prompt(self.test_af, "data_processor.py", mock_rules)
        
        # 验证 prompt 包含调用链上下文
        assert "调用链上下文:" in prompt
        assert "调用链: main -> process_data -> validate_input" in prompt
        assert "相关性评分:" in prompt
    
    def test_prompt_generation_without_optimized_context(self):
        """测试不使用优化上下文生成 prompt"""
        # 创建 LLMService（不启用优化上下文）
        llm_service = LLMService(
            client=self.mock_llm_client,
            use_optimized_context=False
        )
        
        # 设置相关定义
        self.test_af.related_definitions = [
            self._create_mock_function("helper", "def helper():\n    return 'help'", "helper.py")
        ]
        
        # 生成 prompt
        from gate_keeper.domain.rule.check_rule import CodeCheckRule
        mock_rules = [CodeCheckRule(
            id="test_rule", 
            name="Test Rule", 
            description="Test",
            category=["test"]
        )]
        
        prompt = llm_service._generate_prompt(self.test_af, "data_processor.py", mock_rules)
        
        # 验证 prompt 使用原有的简单上下文
        assert "参考上下文:" in prompt
        assert "def helper():" in prompt
        assert "待检查内容:" in prompt
        assert "def process_data(data):" in prompt
    
    def test_prompt_generation_without_related_definitions(self):
        """测试没有相关定义时生成 prompt（修复bug场景）"""
        # 创建 LLMService（不启用优化上下文）
        llm_service = LLMService(
            client=self.mock_llm_client,
            use_optimized_context=False
        )
        
        # 确保没有相关定义
        self.test_af.related_definitions = []
        
        # 生成 prompt
        from gate_keeper.domain.rule.check_rule import CodeCheckRule
        mock_rules = [CodeCheckRule(
            id="test_rule", 
            name="Test Rule", 
            description="Test",
            category=["test"]
        )]
        
        prompt = llm_service._generate_prompt(self.test_af, "data_processor.py", mock_rules)
        
        # 验证 prompt 不包含参考上下文，但包含待检查的函数代码
        assert "参考上下文:" not in prompt
        assert "待检查内容:" in prompt
        assert "def process_data(data):" in prompt
        assert "data_processor.py" in prompt
    
    def test_context_manager_integration(self):
        """测试上下文管理器集成"""
        # 模拟函数定义
        mock_functions = {
            "main": [self._create_mock_function("main", "def main():\n    data = get_data()\n    result = process_data(data)\n    return result", "main.py")],
            "process_data": [self._create_mock_function("process_data", "def process_data(data):\n    if validate_input(data):\n        return transform_data(data)\n    return None", "data_processor.py")],
            "validate_input": [self._create_mock_function("validate_input", "def validate_input(data):\n    if not data:\n        return False\n    return isinstance(data, dict)", "validator.py")]
        }
        self.mock_repo_index.function_definitions = mock_functions
        
        # 创建上下文管理器
        context_manager = ContextManager(self.static_analyzer, max_context_size=5000)
        
        # 模拟调用链
        self.static_analyzer.get_bidirectional_call_chains = Mock(return_value=[
            ["main", "process_data", "validate_input"]
        ])
        
        # 生成优化上下文
        contexts = context_manager.generate_optimized_contexts(self.test_af, max_chains=2)
        
        # 验证结果
        assert len(contexts) == 1
        assert contexts[0].chain == ["main", "process_data", "validate_input"]
        assert len(contexts[0].functions) == 3
        assert contexts[0].relevance_score > 0
        
        # 验证上下文大小控制
        assert contexts[0].context_size <= 5000
    
    def test_context_size_control(self):
        """测试上下文大小控制"""
        # 创建大代码的函数
        large_code = "def large_function():\n" + "\n".join([f"    x = {i}" for i in range(2000)])
        mock_functions = {
            "process_data": [self._create_mock_function("process_data", large_code, "data_processor.py")]
        }
        self.mock_repo_index.function_definitions = mock_functions
        
        # 创建上下文管理器（设置较小的最大大小）
        context_manager = ContextManager(self.static_analyzer, max_context_size=1000)
        
        # 模拟调用链
        self.static_analyzer.get_bidirectional_call_chains = Mock(return_value=[
            ["process_data"]
        ])
        
        # 生成优化上下文
        contexts = context_manager.generate_optimized_contexts(self.test_af, max_chains=1)
        
        # 验证结果（应该因为大小限制而跳过）
        assert len(contexts) == 0
    
    def _create_mock_function(self, name: str, code: str, filepath: str = "test.py") -> Function:
        """创建模拟的 Function 对象"""
        # 创建 FunctionSignature
        signature = FunctionSignature(
            name=name,
            parameters=[],
            return_type=None
        )
        
        return Function(
            name=name,
            range=CodeRange(start_line=1, end_line=10),
            filepath=filepath,
            signature=signature,
            code=code,
            is_declaration=False
        )


class TestContextOptimizationPerformance:
    """测试上下文优化性能"""
    
    def test_context_size_reduction(self):
        """测试上下文大小减少效果"""
        # 创建包含大量相关定义的函数
        large_related_defs = []
        for i in range(10):
            large_code = f"def helper_{i}():\n" + "\n".join([f"    x = {j}" for j in range(100)])
            large_related_defs.append(self._create_mock_function(f"helper_{i}", large_code))
        
        # 创建 AffectedFunction（模拟原有实现）
        af_with_large_context = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            changed_lines=[12, 15],
            code="def test_function():\n    return True",
            related_calls=[],
            related_definitions=large_related_defs,
            filepath="test.py"
        )
        
        # 计算原有上下文大小
        original_context_size = sum(len(f.code) for f in large_related_defs)
        
        # 模拟优化后的上下文（只包含最相关的3个函数）
        optimized_context_size = sum(len(f.code) for f in large_related_defs[:3])
        
        # 验证优化效果
        reduction_ratio = (original_context_size - optimized_context_size) / original_context_size
        assert reduction_ratio > 0.5  # 至少减少50%
    
    def _create_mock_function(self, name: str, code: str, filepath: str = "test.py") -> Function:
        """创建模拟的 Function 对象"""
        signature = FunctionSignature(
            name=name,
            parameters=[],
            return_type=None
        )
        
        return Function(
            name=name,
            range=CodeRange(start_line=1, end_line=10),
            filepath=filepath,
            signature=signature,
            code=code,
            is_declaration=False
        ) 