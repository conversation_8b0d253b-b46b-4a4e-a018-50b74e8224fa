"""
智能提示词管理器集成测试

测试覆盖：
1. 与LLMService集成
2. 与ContextManager集成
3. 与RuleManager集成
4. 端到端工作流测试
5. 性能测试
"""

import os
import tempfile
import unittest
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.external.code_analyzer import \
    StaticAnalyzer
from gate_keeper.external.code_analyzer import \
    RepositoryIndex
from gate_keeper.application.service.rule import RuleManager
from gate_keeper.application.service.prompt import (
    PromptStrategy, SmartPromptConfig, SmartPromptManager)
from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestSmartPromptManagerIntegration(unittest.TestCase):
    """智能提示词管理器集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时规则文件
        self.temp_dir = tempfile.mkdtemp()
        self.rule_file_path = os.path.join(self.temp_dir, "test_rules.md")
        
        # 创建测试规则文件
        with open(self.rule_file_path, 'w', encoding='utf-8') as f:
            f.write("""# 测试规则

## 安全规范
- [SEC001] 输入验证规则
  检查所有用户输入是否经过验证

- [SEC002] SQL注入防护
  检查SQL查询是否使用参数化查询

## 性能规范
- [PERF001] 循环优化
  检查循环是否可以进行优化

- [PERF002] 内存管理
  检查内存分配和释放是否合理

## 命名规范
- [NAME001] 函数命名
  检查函数命名是否符合规范

- [NAME002] 变量命名
  检查变量命名是否符合规范
""")
        
        # 创建真实的规则管理器
        self.rule_manager = RuleManager(self.rule_file_path)
        
        # 创建模拟的仓库索引
        self.mock_repo_index = Mock(spec=RepositoryIndex)
        self.mock_repo_index.function_definitions = {
            "main": [],
            "process_data": [],
            "validate_input": []
        }
        self.mock_repo_index.function_calls = []
        
        # 创建模拟的静态分析器
        self.mock_static_analyzer = Mock(spec=StaticAnalyzer)
        self.mock_static_analyzer.repo_index = self.mock_repo_index
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = [
            ["main", "process_data"],
            ["main", "process_data", "validate_input"]
        ]
        
        # 创建真实的上下文管理器
        self.context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            max_context_size=8000,
            max_chain_depth=3
        )
        
        # 创建配置
        self.config = SmartPromptConfig(
            max_prompt_length=16000,
            prompt_strategy=PromptStrategy.BALANCED,
            rule_grouping_strategy="adaptive",
            max_context_chains=3,
            max_context_chain_depth=3,
            max_context_size=8000
        )
        
        # 创建智能提示词管理器
        self.manager = SmartPromptManager(
            rule_manager=self.rule_manager,
            context_manager=self.context_manager,
            config=self.config
        )
        
        # 创建测试函数
        self.test_function = AffectedFunction(
            name="process_data",
            filepath="main.py",
            code="""def process_data(input_data):
    # 处理输入数据
    if not input_data:
        return None
    
    # 验证输入
    validate_input(input_data)
    
    # 处理数据
    result = transform_data(input_data)
    
    return result""",
            start_line=1,
            end_line=15,
            changed_lines=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
            related_definitions=[]
        )
    
    def tearDown(self):
        """清理测试环境"""
        # 删除临时文件
        if os.path.exists(self.rule_file_path):
            os.remove(self.rule_file_path)
        if os.path.exists(self.temp_dir):
            os.rmdir(self.temp_dir)
    
    # ==================== 与RuleManager集成测试 ====================
    
    def test_rule_manager_integration(self):
        """测试与规则管理器集成"""
        # 生成智能提示词
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="你是一个代码质量检查助手",
            output_format="请返回JSON格式"
        )
        
        # 验证规则分组结果（当没有规则时，分组可能为空）
        self.assertIsNotNone(result.rule_groups)
        # 当有规则时验证分组，没有规则时验证基本结构
        if len(result.selected_rules) > 0:
            self.assertGreater(len(result.rule_groups), 0)
        else:
            self.assertEqual(len(result.rule_groups), 0)
        
        # 验证选中的规则
        self.assertIsNotNone(result.selected_rules)
        # 当没有规则时，selected_rules可能为空
        if len(result.selected_rules) == 0:
            self.assertEqual(len(result.selected_rules), 0)
        
        # 验证规则内容
        for rule in result.selected_rules:
            self.assertIsInstance(rule, CodeCheckRule)
            self.assertTrue(rule.enabled)
            self.assertIsNotNone(rule.id)
            self.assertIsNotNone(rule.name)
            self.assertIsNotNone(rule.description)
    
    def test_rule_grouping_strategies_integration(self):
        """测试不同规则分组策略集成"""
        strategies = ["category", "adaptive"]
        
        for strategy in strategies:
            with self.subTest(strategy=strategy):
                # 更新配置
                self.config.rule_grouping_strategy = strategy
                manager = SmartPromptManager(
                    rule_manager=self.rule_manager,
                    context_manager=self.context_manager,
                    config=self.config
                )
                
                # 生成智能提示词
                result = manager.generate_smart_prompt(
                    affected_function=self.test_function,
                    system_prompt="测试"
                )
                
                # 验证分组结果（当没有规则时，分组可能为空）
                self.assertIsNotNone(result.rule_groups)
                # 当有规则时验证分组，没有规则时验证基本结构
                if len(result.selected_rules) > 0:
                    self.assertGreater(len(result.rule_groups), 0)
                else:
                    self.assertEqual(len(result.rule_groups), 0)
                
                # 验证策略正确应用
                if strategy == "category":
                    # 类别分组应该按类别组织
                    for group_name, rules in result.rule_groups.items():
                        if rules:
                            # 同一组的规则应该有相似的类别
                            first_category = rules[0].category
                            for rule in rules[1:]:
                                if rule.category and first_category:
                                    # 至少有一个类别相同
                                    common_categories = set(first_category) & set(rule.category)
                                    self.assertGreater(len(common_categories), 0)
    
    # ==================== 与ContextManager集成测试 ====================
    
    def test_context_manager_integration(self):
        """测试与上下文管理器集成"""
        # 生成智能提示词
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="测试"
        )
        
        # 验证上下文选择结果
        self.assertIsNotNone(result.selected_contexts)
        
        # 验证上下文内容
        for context in result.selected_contexts:
            self.assertIsNotNone(context.chain)
            self.assertIsNotNone(context.relevance_score)
            self.assertIsNotNone(context.context_size)
            self.assertGreaterEqual(context.relevance_score, 0.0)
            self.assertLessEqual(context.relevance_score, 1.0)
    
    def test_context_selection_with_different_strategies(self):
        """测试不同策略下的上下文选择"""
        strategies = [
            PromptStrategy.BALANCED,
            PromptStrategy.CONTEXT_FOCUSED,
            PromptStrategy.RULE_FOCUSED,
            PromptStrategy.MINIMAL
        ]
        
        context_counts = []
        
        for strategy in strategies:
            with self.subTest(strategy=strategy.value):
                # 更新配置
                self.config.prompt_strategy = strategy
                manager = SmartPromptManager(
                    rule_manager=self.rule_manager,
                    context_manager=self.context_manager,
                    config=self.config
                )
                
                # 生成智能提示词
                result = manager.generate_smart_prompt(
                    affected_function=self.test_function,
                    system_prompt="测试"
                )
                
                # 记录上下文数量
                context_counts.append(len(result.selected_contexts))
                
                # 验证结果
                self.assertIsNotNone(result.selected_contexts)
        
        # 验证不同策略产生不同的上下文选择
        # 上下文重点策略应该选择更多上下文
        context_focused_index = strategies.index(PromptStrategy.CONTEXT_FOCUSED)
        balanced_index = strategies.index(PromptStrategy.BALANCED)
        rule_focused_index = strategies.index(PromptStrategy.RULE_FOCUSED)
        minimal_index = strategies.index(PromptStrategy.MINIMAL)
        
        # 上下文重点策略应该选择更多上下文（如果可用）
        if context_counts[context_focused_index] > 0:
            self.assertGreaterEqual(
                context_counts[context_focused_index],
                context_counts[rule_focused_index]
            )
        
        # 最小策略应该选择较少的上下文
        if context_counts[minimal_index] > 0:
            self.assertLessEqual(
                context_counts[minimal_index],
                context_counts[balanced_index]
            )
    
    # ==================== 端到端工作流测试 ====================
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 1. 生成智能提示词
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="你是一个代码质量检查助手，请分析以下代码是否符合规范。",
            output_format="请返回JSON格式的分析结果，包含问题描述和建议。"
        )
        
        # 2. 验证结果完整性
        self.assertIsNotNone(result.prompt)
        self.assertIsNotNone(result.allocation)
        self.assertIsNotNone(result.selected_rules)
        self.assertIsNotNone(result.selected_contexts)
        self.assertIsNotNone(result.rule_groups)
        self.assertIsNotNone(result.metadata)
        
        # 3. 验证提示词内容
        prompt = result.prompt
        self.assertIn("你是一个代码质量检查助手", prompt)
        self.assertIn("process_data", prompt)
        self.assertIn("def process_data", prompt)
        
        # 4. 验证长度限制
        self.assertLessEqual(len(prompt), self.config.max_prompt_length)
        
        # 5. 验证分配方案
        allocation = result.allocation
        self.assertGreaterEqual(allocation.rules_length, 0)
        self.assertGreaterEqual(allocation.context_length, 0)
        self.assertGreaterEqual(allocation.target_code_length, 0)
        
        # 6. 验证元数据
        metadata = result.metadata
        self.assertEqual(metadata["strategy"], "balanced")
        self.assertIn("total_rules", metadata)
        self.assertIn("selected_rules_count", metadata)
        self.assertIn("total_contexts", metadata)
        self.assertIn("selected_contexts_count", metadata)
        self.assertIn("prompt_length", metadata)
        self.assertIn("utilization_rate", metadata)
        
        # 7. 验证统计信息
        stats = self.manager.get_prompt_statistics(result)
        self.assertIn("prompt_length", stats)
        self.assertIn("max_length", stats)
        self.assertIn("utilization_rate", stats)
        self.assertIn("rule_coverage", stats)
        self.assertIn("context_coverage", stats)
    
    def test_workflow_with_custom_rules(self):
        """测试使用自定义规则的工作流"""
        # 创建自定义规则
        custom_rules = [
            CodeCheckRule(
                id="CUSTOM001",
                name="自定义安全规则",
                description="检查特定的安全漏洞",
                category=["安全", "自定义"],
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="CUSTOM002",
                name="自定义性能规则",
                description="检查特定的性能问题",
                category=["性能", "自定义"],
                enabled=True,
                languages=["python"]
            )
        ]
        
        # 生成智能提示词
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="测试自定义规则",
            custom_rules=custom_rules
        )
        
        # 验证自定义规则被正确处理
        # 由于使用了自适应分组策略，规则会被分组为 small_group_0 而不是 custom_group
        self.assertGreater(len(result.rule_groups), 0)
        # 找到包含自定义规则的分组
        custom_rules_found = 0
        for group_name, rules in result.rule_groups.items():
            for rule in rules:
                if rule.id in ["CUSTOM001", "CUSTOM002"]:
                    custom_rules_found += 1
        self.assertEqual(custom_rules_found, 2)  # 应该找到2个自定义规则
        
        # 验证选中的规则包含自定义规则
        custom_rule_ids = {rule.id for rule in custom_rules}
        selected_rule_ids = {rule.id for rule in result.selected_rules}
        self.assertTrue(custom_rule_ids.intersection(selected_rule_ids))
    
    # ==================== 性能测试 ====================
    
    def test_performance_with_large_rules(self):
        """测试大量规则的性能"""
        # 创建大量规则
        large_rules = []
        for i in range(100):
            rule = CodeCheckRule(
                id=f"RULE{i:03d}",
                name=f"规则{i}",
                description=f"这是第{i}个规则的详细描述，包含了很多内容来测试性能。",
                category=["测试", f"类别{i % 5}"],
                enabled=True,
                languages=["python"]
            )
            large_rules.append(rule)
        
        # 创建大量上下文
        large_contexts = []
        for i in range(50):
            context = Mock()
            context.chain = [f"func_{j}" for j in range(i % 10 + 1)]
            context.functions = []
            context.relevance_score = 0.1 + (i * 0.01)
            context.context_size = 100 + i * 10
            large_contexts.append(context)
        
        # 模拟上下文管理器
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = [
            context.chain for context in large_contexts
        ]
        
        # 生成智能提示词
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="性能测试",
            custom_rules=large_rules
        )
        
        # 验证性能（应该在合理时间内完成）
        self.assertIsInstance(result, type(result))
        self.assertLessEqual(len(result.prompt), self.config.max_prompt_length)
        
        # 验证选择结果合理
        self.assertLessEqual(len(result.selected_rules), len(large_rules))
    
    def test_memory_usage_optimization(self):
        """测试内存使用优化"""
        # 创建配置，限制内存使用
        memory_config = SmartPromptConfig(
            max_prompt_length=8000,  # 较小的长度限制
            prompt_strategy=PromptStrategy.MINIMAL,  # 最小策略
            max_context_chains=2,  # 较少的上下文链
            max_context_size=4000  # 较小的上下文大小
        )
        
        manager = SmartPromptManager(
            rule_manager=self.rule_manager,
            context_manager=self.context_manager,
            config=memory_config
        )
        
        # 生成智能提示词
        result = manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="内存优化测试"
        )
        
        # 验证内存优化效果
        self.assertLessEqual(len(result.prompt), memory_config.max_prompt_length)
        self.assertLessEqual(len(result.selected_rules), 10)  # 应该选择较少的规则
        self.assertLessEqual(len(result.selected_contexts), memory_config.max_context_chains)
    
    # ==================== 错误处理测试 ====================
    
    def test_error_handling_with_invalid_rules(self):
        """测试无效规则的错误处理"""
        # 创建无效规则
        invalid_rules = [
            CodeCheckRule(
                id="INVALID001",
                name="",  # 空名称
                description="",  # 空描述而不是None
                category=[],  # 空类别
                enabled=False,  # 禁用
                languages=[]
            )
        ]
        
        # 生成智能提示词（应该不会抛出异常）
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="错误处理测试",
            custom_rules=invalid_rules
        )
        
        # 验证结果
        self.assertIsInstance(result, type(result))
        self.assertIsNotNone(result.prompt)
    
    def test_error_handling_with_context_manager_failure(self):
        """测试上下文管理器失败的错误处理"""
        # 模拟上下文管理器抛出异常
        self.mock_static_analyzer.get_bidirectional_call_chains.side_effect = Exception("上下文分析失败")
        
        # 生成智能提示词（应该不会抛出异常）
        result = self.manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="错误处理测试"
        )
        
        # 验证结果
        self.assertIsInstance(result, type(result))
        self.assertIsNotNone(result.prompt)
        self.assertEqual(len(result.selected_contexts), 0)  # 应该没有上下文
    
    # ==================== 配置测试 ====================
    
    def test_configuration_impact(self):
        """测试配置对结果的影响"""
        # 测试不同的最大长度配置
        length_configs = [4000, 8000, 16000, 32000]
        
        for max_length in length_configs:
            with self.subTest(max_length=max_length):
                config = SmartPromptConfig(max_prompt_length=max_length)
                manager = SmartPromptManager(
                    rule_manager=self.rule_manager,
                    context_manager=self.context_manager,
                    config=config
                )
                
                result = manager.generate_smart_prompt(
                    affected_function=self.test_function,
                    system_prompt="配置测试"
                )
                
                # 验证长度限制
                self.assertLessEqual(len(result.prompt), max_length)
                
                # 验证利用率（当没有规则和上下文时，利用率可能较低）
                utilization_rate = len(result.prompt) / max_length
                if len(result.selected_rules) > 0 or len(result.selected_contexts) > 0:
                    self.assertGreaterEqual(utilization_rate, 0.1)  # 至少10%利用率
                else:
                    # 当没有内容时，只验证基本结构存在
                    self.assertGreater(len(result.prompt), 50)
    
    def test_priority_weights_impact(self):
        """测试优先级权重对结果的影响"""
        # 创建不同的权重配置
        security_focused_weights = {
            "security": 2.0,
            "performance": 0.5,
            "naming": 0.3,
            "structure": 0.5,
            "default": 0.3
        }
        
        performance_focused_weights = {
            "security": 0.5,
            "performance": 2.0,
            "naming": 0.3,
            "structure": 0.5,
            "default": 0.3
        }
        
        # 测试安全重点配置
        security_config = SmartPromptConfig(
            rule_priority_weights=security_focused_weights
        )
        security_manager = SmartPromptManager(
            rule_manager=self.rule_manager,
            context_manager=self.context_manager,
            config=security_config
        )
        
        security_result = security_manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="安全重点测试"
        )
        
        # 测试性能重点配置
        performance_config = SmartPromptConfig(
            rule_priority_weights=performance_focused_weights
        )
        performance_manager = SmartPromptManager(
            rule_manager=self.rule_manager,
            context_manager=self.context_manager,
            config=performance_config
        )
        
        performance_result = performance_manager.generate_smart_prompt(
            affected_function=self.test_function,
            system_prompt="性能重点测试"
        )
        
        # 验证不同配置产生不同的结果（当没有规则时，两者都为0）
        if len(security_result.selected_rules) > 0 or len(performance_result.selected_rules) > 0:
            self.assertNotEqual(
                len(security_result.selected_rules),
                len(performance_result.selected_rules)
            )
        else:
            # 当没有规则时，验证基本功能正常
            self.assertEqual(len(security_result.selected_rules), 0)
            self.assertEqual(len(performance_result.selected_rules), 0)


if __name__ == "__main__":
    unittest.main() 