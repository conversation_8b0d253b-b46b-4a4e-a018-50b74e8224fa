#!/usr/bin/env python3
"""
Gitee集成测试用例

测试代码功能在真实Gitee环境下的效果：
1. 测试MR分析功能
2. 测试评论发布功能
3. 测试评论排重功能
"""

import os
import sys
import time
import unittest
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

import os
# 导入统一的测试配置
import sys

from gate_keeper.application.service.report import ReportService
from gate_keeper.config.config import token
from gate_keeper.domain.value_objects.git import CodeCheckDiscussion
from gate_keeper.infrastructure.git.gitee.client import Gitee
from gate_keeper.shared.log import app_logger as logger

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from test_config import (COMMENT_DEDUPLICATION_CONFIG, TEST_DATA_CONFIG,
                         TEST_MR_CONFIG)


class TestGiteeIntegration(unittest.TestCase):
    """Gitee集成测试类（精简版）"""
    
    @classmethod
    def setUpClass(cls):
        # 使用统一的测试配置
        cls.project_id = TEST_MR_CONFIG["project_id"]
        cls.token = token
        cls.mr_id = TEST_MR_CONFIG["mr_id"]
        cls.base_branch = TEST_MR_CONFIG["base_branch"]
        cls.dev_branch = TEST_MR_CONFIG["dev_branch"]
        cls.gitee_client = Gitee(cls.token)
        cls.report_service = ReportService(cls.token)
        logger.info(f"开始Gitee集成测试 - MR #{cls.mr_id}")
        logger.info(f"仓库: {cls.project_id}")
        logger.info(f"分支: {cls.base_branch} <- {cls.dev_branch}")

    def setUp(self):
        self.test_comment_prefix = f"[测试-{int(time.time())}] "

    def test_01_mr_and_comment_info(self):
        """获取MR信息和评论列表，验证字段完整性"""
        logger.info("测试1: MR与评论信息获取")
        mr_info = self.gitee_client.get_mr_info(self.project_id, self.mr_id)
        self.assertIsNotNone(mr_info)
        self.assertEqual(str(mr_info.id), str(self.mr_id))
        self.assertTrue(hasattr(mr_info, 'title'))
        self.assertTrue(hasattr(mr_info, 'state'))
        logger.info(f"✅ MR信息获取成功: {mr_info.title}")
        comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
        self.assertIsInstance(comments, list)
        logger.info(f"✅ 获取到 {len(comments)} 条评论")
        for i, comment in enumerate(comments[:3]):
            logger.info(f"   评论{i+1}: {getattr(comment, 'body', '')[:50]}...")

    def test_02_comment_publish_and_deduplication(self):
        """单条/批量评论发布与排重，断言评论数量和内容"""
        logger.info("测试2: 评论发布与排重")
        # 单条评论
        single_comment = f"{self.test_comment_prefix}单条评论发布测试"
        success = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, single_comment)
        self.assertTrue(success)
        logger.info("✅ 单条评论发布成功")
        time.sleep(2)
        # 批量评论（含重复/相似内容）
        test_comments = [
            f"{self.test_comment_prefix}第一条测试评论",
            f"{self.test_comment_prefix}第二条测试评论，包含代码：\n```python\ndef test_func():\n    pass\n```",
            f"{self.test_comment_prefix}第三条测试评论，包含相同代码：\n```python\ndef test_func():\n    pass\n```",
            f"{self.test_comment_prefix}第四条测试评论，内容相似：第二条测试评论，包含代码：\n```python\ndef test_func():\n    pass\n```",
        ]
        posted = set()
        for comment in test_comments:
            if comment not in posted:
                success = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, comment)
                self.assertTrue(success)
                posted.add(comment)
                logger.info(f"✅ 评论发布成功: {comment[:30]}")
            time.sleep(1)
        # 验证评论区无重复
        comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
        # 只校验本次测试发布的评论体是否唯一
        test_bodies = [getattr(c, 'body', '') for c in comments if self.test_comment_prefix in getattr(c, 'body', '')]
        unique_test_bodies = set(test_bodies)
        self.assertEqual(len(test_bodies), len(unique_test_bodies), "本次测试评论区存在重复评论")
        logger.info("✅ 评论区无重复评论")

    def test_03_analysis_report_publish(self):
        """发布分析报告，验证内容和格式"""
        logger.info("测试3: 分析报告发布")
        
        # 使用测试配置文件中的数据
        test_case = TEST_DATA_CONFIG["test_comments"][1]  # 使用"代码有问题情况"
        mock_analysis_result = test_case["llm_results"]
        
        report_content = self.report_service.generate_markdown_report(mock_analysis_result)
        self.assertIsNotNone(report_content)
        self.assertGreater(len(report_content), 0)
        
        # 验证报告内容符合预期
        expected_report_start = test_case["expected_report"]
        self.assertTrue(report_content.startswith(expected_report_start), 
                       f"报告内容不符合预期，期望以'{expected_report_start}'开头")
        
        # 验证报告包含必要的关键词
        self.assertTrue(any(keyword in report_content for keyword in ["代码规范检查报告", "编码守则", "代码规范检查报告"]))
        logger.info("✅ 报告生成成功")
        
        report_comment = f"{self.test_comment_prefix}代码规范检查报告\n\n{report_content}"
        success = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, report_comment)
        self.assertTrue(success)
        logger.info("✅ 报告评论发布成功")

    def test_04_comment_delete_and_cleanup(self):
        """发布、删除评论，清理本次测试评论，断言清理效果"""
        logger.info("测试4: 评论删除与清理")
        # 发布一条评论
        comment_body = f"{self.test_comment_prefix}集成测试自动创建评论"
        create_resp = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, comment_body)
        self.assertTrue(create_resp)
        comment_id = create_resp.get("id", None)
        # 删除评论
        success = self.gitee_client.delete_discussion_of_mr(self.project_id, str(self.mr_id), str(comment_id))
        self.assertTrue(success)
        logger.info("✅ 评论删除成功")
        # 清理本次测试评论
        comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
        test_comment_ids = [getattr(c, 'id', None) for c in comments if self.test_comment_prefix in getattr(c, 'body', '')]
        for comment_id in test_comment_ids:
            self.gitee_client.delete_discussion_of_mr(self.project_id, str(self.mr_id), str(comment_id))
        logger.info("✅ 本次测试评论清理完成")
        # 验证清理效果
        time.sleep(2)
        remaining_comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
        remaining_test_comments = [c for c in remaining_comments if self.test_comment_prefix in getattr(c, 'body', '')]
        self.assertEqual(len(remaining_test_comments), 0, "仍有测试评论未清理")
        logger.info("✅ 所有测试评论清理完成")

    def test_05_service_layer_publish(self):
        """服务层直发评论，mock验证"""
        logger.info("测试5: 服务层直发评论")
        test_comment = f"{self.test_comment_prefix}ReportService直接发布测试评论"
        with patch.object(self.report_service, 'post_mr_comment', return_value=True):
            success = self.report_service.post_mr_comment(self.project_id, self.mr_id, test_comment)
            self.assertTrue(success)
            logger.info("✅ ReportService直接发布评论成功")
    
    def test_08_delete_comment_functionality(self):
        """
        测试删除评论功能，真实创建、删除并验证评论
        """
        # 1. 创建一条评论
        comment_body = f"{self.test_comment_prefix}集成测试自动创建评论"
        create_resp = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, comment_body)
        assert create_resp, "评论创建失败"

        
        comment_id = create_resp.get("id",None)

        # 2. 删除评论
        success = self.gitee_client.delete_discussion_of_mr(self.project_id, str(self.mr_id), str(comment_id))
        assert success, "评论删除应成功"

        # 3. 再次查询，确认评论已被删除
        comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
        assert all(getattr(c, 'id', None) != comment_id for c in comments), "评论未被成功删除"
    
    def test_09_cleanup_test_comments(self):
        """清理测试评论"""
        logger.info("测试9: 清理测试评论")
        
        try:
            # 获取所有评论
            comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
            
            # 统计测试评论数量
            test_comment_count = 0
            test_comment_ids = []
            
            for comment in comments:
                    # test_comment_count += 1
                    # test_comment_ids.append(comment.get('id'))                
                if self.test_comment_prefix in getattr(comment, 'body', ''):
                    test_comment_count += 1
                    test_comment_ids.append(getattr(comment, 'id', None))
            
            logger.info(f"发现 {test_comment_count} 条测试评论")
            
            if test_comment_count > 0:
                logger.info("开始清理测试评论...")
                deleted_count = 0
                
                for comment_id in test_comment_ids:
                    try:
                        success = self.gitee_client.delete_discussion_of_mr(self.project_id, str(self.mr_id), str(comment_id))
                        if success:
                            deleted_count += 1
                            logger.info(f"✅ 删除测试评论ID: {comment_id}")
                        else:
                            logger.warning(f"⚠️ 删除测试评论ID: {comment_id} 失败")
                    except Exception as e:
                        logger.error(f"❌ 删除测试评论ID: {comment_id} 异常: {e}")
                
                logger.info(f"测试评论清理完成: 成功删除 {deleted_count}/{test_comment_count} 条")
                
                # 验证清理结果
                time.sleep(2)  # 等待API同步
                remaining_comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
                remaining_test_comments = 0
                
                for comment in remaining_comments:
                    if self.test_comment_prefix in getattr(comment, 'body', ''):
                        remaining_test_comments += 1
                
                logger.info(f"清理后剩余测试评论: {remaining_test_comments} 条")
                
                if remaining_test_comments == 0:
                    logger.info("✅ 所有测试评论清理完成")
                else:
                    logger.warning(f"⚠️ 仍有 {remaining_test_comments} 条测试评论未清理")
            else:
                logger.info("✅ 没有发现需要清理的测试评论")
            
        except Exception as e:
            logger.error(f"❌ 清理测试评论失败: {e}")
            # 不抛出异常，因为清理失败不影响测试结果
    
    def test_10_cleanup_all_comments(self):
        """清理MR的所有评论（完整清理）"""
        logger.info("测试10: 清理MR的所有评论")
        
        try:
            # 获取所有评论
            comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
            total_comments = len(comments)
            
            logger.info(f"发现MR #{self.mr_id} 共有 {total_comments} 条评论")
            
            if total_comments == 0:
                logger.info("✅ MR没有评论，无需清理")
                return
            
            # 显示评论摘要
            logger.info("评论摘要:")
            for i, comment in enumerate(comments[:5]):  # 只显示前5条
                comment_body = getattr(comment, 'body', '')[:50]
                logger.info(f"  评论{i+1}: {comment_body}...")
            
            if total_comments > 5:
                logger.info(f"  ... 还有 {total_comments - 5} 条评论")
            
            # 确认是否清理所有评论
            logger.info("⚠️ 即将删除MR的所有评论，这包括:")
            logger.info("  - 测试评论")
            logger.info("  - 代码检查报告")
            logger.info("  - 其他所有评论")
            
            # 开始清理所有评论
            logger.info("开始清理所有评论...")
            deleted_count = 0
            failed_count = 0
            
            for comment in comments:
                comment_id = getattr(comment, 'id', None)
                comment_body = getattr(comment, 'body', '')[:30]
                
                try:
                    success = self.gitee_client.delete_discussion_of_mr(self.project_id,self.mr_id, comment_id)
                    if success:
                        deleted_count += 1
                        logger.info(f"✅ 删除评论ID: {comment_id} ({comment_body}...)")
                    else:
                        failed_count += 1
                        logger.warning(f"⚠️ 删除评论ID: {comment_id} 失败")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"❌ 删除评论ID: {comment_id} 异常: {e}")
                
                # 添加小延迟，避免API限制
                time.sleep(0.5)
            
            logger.info(f"清理完成: 成功删除 {deleted_count} 条，失败 {failed_count} 条")
            
            # 验证清理结果
            time.sleep(3)  # 等待API同步
            remaining_comments = self.gitee_client.get_discussions_of_mr(self.project_id, self.mr_id)
            remaining_count = len(remaining_comments)
            
            logger.info(f"清理后剩余评论: {remaining_count} 条")
            
            if remaining_count == 0:
                logger.info("🎉 所有评论清理完成！MR已完全清空")
            else:
                logger.warning(f"⚠️ 仍有 {remaining_count} 条评论未清理")
                # 显示剩余评论
                for i, comment in enumerate(remaining_comments):
                    comment_body = getattr(comment, 'body', '')[:50]
                    logger.info(f"  剩余评论{i+1}: {comment_body}...")
            
            # 验证清理效果
            self.assertLessEqual(remaining_count, failed_count, 
                               f"清理后剩余评论数量({remaining_count})应该小于等于失败数量({failed_count})")
            
        except Exception as e:
            logger.error(f"❌ 清理所有评论失败: {e}")
            # 不抛出异常，因为清理失败不影响测试结果
    
    def test_no_issue_no_report(self):
        """无问题时不发评论，报告内容为空"""
        logger.info("测试: 无问题时不发评论")
        
        # 使用测试配置文件中的"无问题"数据
        test_case = TEST_DATA_CONFIG["test_comments"][0]  # 使用"代码无问题情况"
        analysis_result = test_case["llm_results"]
        
        report_content = self.report_service.generate_markdown_report(analysis_result)
        expected_report = test_case["expected_report"]
        self.assertEqual(report_content, expected_report)
        
        # 验证无问题时不会生成详细报告
        self.assertNotIn("## 代码规范检查报告", report_content)
        self.assertEqual(report_content, "", "无问题时应该返回空字符串")
        
        # 尝试发布评论，实际不会发出（因为内容为空）
        success = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, report_content)
        # 空内容时应该返回False或不发出
        self.assertTrue(not success or report_content == "")
        logger.info("✅ 无问题时不会发出评论")

    def test_issue_report_format(self):
        """有问题时报告格式校验，内容包含关键字段"""
        logger.info("测试: 有问题时报告格式校验")
        
        # 使用测试配置文件中的"有问题"数据
        test_case = TEST_DATA_CONFIG["test_comments"][1]  # 使用"代码有问题情况"
        analysis_result = test_case["llm_results"]
        
        report_content = self.report_service.generate_markdown_report(analysis_result)
        
        # 校验报告内容格式
        self.assertIn("代码规范检查报告", report_content)
        self.assertTrue("违规" in report_content or "问题" in report_content)
        
        # 验证报告包含违规详情
        self.assertIn("违规详情", report_content)
        self.assertIn("STYLE001", report_content)
        self.assertIn("函数命名应使用小写字母和下划线", report_content)
        
        # 验证报告格式符合预期
        expected_report_start = test_case["expected_report"]
        self.assertTrue(report_content.startswith(expected_report_start))
        
        # 发布评论
        success = self.gitee_client.post_discussion_to_mr(self.project_id, self.mr_id, report_content)
        self.assertTrue(success)
        logger.info("✅ 有问题时报告格式和发布均正确")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        logger.info("Gitee集成测试完成")
        logger.info("=" * 50)


def run_gitee_integration_test():
    """运行Gitee集成测试"""
    print("开始运行Gitee集成测试...")
    print("=" * 50)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestGiteeIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    print("=" * 50)
    print(f"测试结果: 运行 {result.testsRun} 个测试")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)} 个")
    print(f"失败: {len(result.failures)} 个")
    print(f"错误: {len(result.errors)} 个")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    # 检查必要的环境变量
    if not token:
        print("❌ 错误: 未设置Gitee token")
        print("请在 config.py 中设置正确的 token")
        sys.exit(1)
    
    if not project_id:
        print("❌ 错误: 未设置仓库URL")
        print("请在 config.py 中设置正确的 project_id")
        sys.exit(1)
    
    # 运行测试
    success = run_gitee_integration_test()
    sys.exit(0 if success else 1) 