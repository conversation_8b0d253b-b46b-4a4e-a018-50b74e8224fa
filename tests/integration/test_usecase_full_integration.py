#!/usr/bin/env python3
"""
UseCase全量集成测试

测试真实的usecase执行流程，包括：
1. AnalyzeMRUseCase - MR分析用例
2. AnalyzeMRAndReportUsecase - MR分析并报告用例
3. AnalyzeBranchUseCase - 分支分析用例

使用真实配置和真实代码，不进行mock
"""

import json
import os
import shutil
# 导入超时装饰器
import sys
import tempfile
import time
import unittest
from pathlib import Path
from typing import Any, Dict

import pytest

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from test_timeout_decorator import timeout_with_logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import (AnalyzeBranchUseCase,
                                                      AnalyzeMRUseCase)
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.config import config
from gate_keeper.external.code_analyzer import RepositoryIndex
from gate_keeper.infrastructure.git.gitee.client import Gitee
from gate_keeper.infrastructure.llm.client.client_factory import LLMFactory
from gate_keeper.shared.log import app_logger as logger


class TestUseCaseFullIntegration(unittest.TestCase):
    """UseCase全量集成测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        logger.info("开始UseCase全量集成测试")
        
        # 验证测试环境配置
        cls._validate_test_environment()
        
        # 初始化测试数据
        cls.test_project_path = config.repo_dir
        cls.test_mr_id = config.mr_id
        cls.test_project_id = config.repo_project_id
        cls.test_base_branch = config.baseBranch
        cls.test_dev_branch = config.devBranch
        cls.test_token = config.token
        
        # 创建临时输出目录
        cls.temp_output_dir = tempfile.mkdtemp(prefix="usecase_test_")
        logger.info(f"临时输出目录: {cls.temp_output_dir}")
        
        # 初始化服务依赖
        cls._init_services()
    
    @classmethod
    def teardown_class(cls):
        """测试类清理"""
        logger.info("清理UseCase全量集成测试")
        
        # 清理临时目录
        if os.path.exists(cls.temp_output_dir):
            shutil.rmtree(cls.temp_output_dir)
            logger.info(f"已清理临时目录: {cls.temp_output_dir}")
    
    @classmethod
    def _validate_test_environment(cls):
        """验证测试环境配置"""
        logger.info("验证测试环境配置...")
        
        # 检查必要的配置项
        required_configs = [
            'repo_dir', 'mr_id', 'repo_project_id', 'baseBranch', 
            'devBranch', 'token', 'rule_file_path'
        ]
        
        missing_configs = []
        for config_name in required_configs:
            config_value = getattr(config, config_name, None)
            if config_value is None or config_value == "not_set":
                missing_configs.append(config_name)
        
        if missing_configs:
            raise ValueError(f"缺少必要的测试配置: {missing_configs}")
        
        # 检查测试项目路径是否存在
        if not os.path.exists(config.repo_dir):
            raise ValueError(f"测试项目路径不存在: {config.repo_dir}")
        
        # 检查规则文件是否存在
        if not os.path.exists(config.rule_file_path):
            raise ValueError(f"规则文件不存在: {config.rule_file_path}")
        
        logger.info("测试环境配置验证通过")
    
    @classmethod
    def _init_services(cls):
        """初始化服务依赖"""
        logger.info("初始化服务依赖...")
        
        try:
            # 1. 初始化Git服务
            git_client = Gitee(cls.test_token)
            cls.git_service = GitService(comment_service=git_client)
            logger.info("Git服务初始化成功")
            
            # 2. 初始化LLM服务
            llm_client = LLMFactory.create("ollama")
            cls.llm_service = LLMService(
                client=llm_client,
                max_calls_per_task=1  # 每个函数最多只调用1次LLM
            )
            logger.info("LLM服务初始化成功")
            
            # 3. 初始化仓库分析器
            cls.repo_analyzer = RepositoryAnalyzer(cls.git_service, cls.llm_service)
            logger.info("仓库分析器初始化成功")
            
            # 4. 初始化UseCase
            cls.analyze_mr_usecase = AnalyzeMRUseCase(
                git_service=cls.git_service,
                llm_service=cls.llm_service,
                repo_analyzer=cls.repo_analyzer
            )
            
            # 创建服务编排器
            from gate_keeper.application.service.service_orchestrator import (
                ServiceConfig, ServiceOrchestrator)

            # 创建服务配置
            service_config = ServiceConfig(
                git_token="test_token",
                git_platform="gitee",
                llm_endpoint="http://localhost:11434",
                llm_model="qwen2.5:7b",
                use_static_analysis=True,
                use_optimized_context=True,
                max_workers=3,
                max_context_chain_depth=3,
                max_context_chains=3,
                max_context_size=8000
            )

            cls.orchestrator = ServiceOrchestrator(service_config)
            
            cls.analyze_mr_report_usecase = AnalyzeMRAndReportUsecase(cls.orchestrator)
            
            cls.analyze_branch_usecase = AnalyzeBranchUseCase(
                git_service=cls.git_service,
                llm_service=cls.llm_service,
                repo_analyzer=cls.repo_analyzer
            )
            
            logger.info("UseCase初始化成功")
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            raise
    
    @timeout_with_logging(120)  # 2分钟超时
    def test_analyze_mr_usecase_basic(self):
        """测试AnalyzeMRUseCase基本功能"""
        logger.info("开始测试AnalyzeMRUseCase基本功能")
        
        try:
            # 执行MR分析
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=self.test_mr_id,
                base_branch=self.test_base_branch,
                dev_branch=self.test_dev_branch,
                max_context_chain_depth=1,  # 最小深度
                exclude_patterns=config.exclude_patterns,
                max_diff_results=1  # 最小结果数
            )
            
            # 验证结果结构
            self._validate_mr_result_structure(result)
            
            # 验证结果内容
            self._validate_mr_result_content(result)
            
            logger.info("AnalyzeMRUseCase基本功能测试通过")
            
        except Exception as e:
            logger.error(f"AnalyzeMRUseCase基本功能测试失败: {e}")
            raise
    
    @timeout_with_logging(120)  # 2分钟超时
    def test_analyze_mr_usecase_with_commit_sha(self):
        """测试AnalyzeMRUseCase使用commit SHA"""
        logger.info("开始测试AnalyzeMRUseCase使用commit SHA")
        
        try:
            # 获取最新的commit SHA
            logger.info("正在获取base commit SHA...")
            base_commit_sha = self.git_service.get_remote_latest_branch_commit_sha(
                self.test_project_path, self.test_base_branch, "origin"
            )
            logger.info("正在获取dev commit SHA...")
            dev_commit_sha = self.git_service.get_remote_latest_branch_commit_sha(
                self.test_project_path, self.test_dev_branch, "origin"
            )
            
            logger.info(f"Base commit SHA: {base_commit_sha}")
            logger.info(f"Dev commit SHA: {dev_commit_sha}")
            
            # 执行MR分析
            logger.info("开始执行MR分析...")
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=self.test_mr_id,
                base_branch=self.test_base_branch,
                dev_branch=self.test_dev_branch,
                base_commit_sha=base_commit_sha,
                dev_commit_sha=dev_commit_sha,
                max_context_chain_depth=1,  # 最小深度
                exclude_patterns=config.exclude_patterns,
                max_diff_results=1  # 最小结果数
            )
            
            logger.info("MR分析执行完成，开始验证结果...")
            
            # 验证结果
            self._validate_mr_result_structure(result)
            
            logger.info("AnalyzeMRUseCase使用commit SHA测试通过")
            
        except Exception as e:
            logger.error(f"AnalyzeMRUseCase使用commit SHA测试失败: {e}")
            raise
    
    @timeout_with_logging(120)  # 2分钟超时
    def test_analyze_mr_report_usecase(self):
        """测试AnalyzeMRAndReportUsecase"""
        logger.info("开始测试AnalyzeMRAndReportUsecase")
        
        try:
            # 执行MR分析并生成报告
            analyze_results, report_content = self.analyze_mr_report_usecase.execute(
                repo_dir=self.test_project_path,
                project_id=self.test_project_id,
                mr_id=self.test_mr_id,
                base_branch=self.test_base_branch,
                dev_branch=self.test_dev_branch,
                max_context_chain_depth=1,  # 最小深度
                exclude_patterns=config.exclude_patterns,
                max_diff_results=1  # 最小结果数
            )
            
            # 验证分析结果
            self._validate_mr_result_structure(analyze_results)
            
            # 验证报告内容
            self._validate_report_content(report_content)
            
            # 保存报告到临时文件
            report_file = os.path.join(self.temp_output_dir, "mr_report.md")
            with open(report_file, "w", encoding="utf-8") as f:
                f.write(report_content)
            logger.info(f"报告已保存到: {report_file}")
            
            logger.info("AnalyzeMRAndReportUsecase测试通过")
            
        except Exception as e:
            logger.error(f"AnalyzeMRAndReportUsecase测试失败: {e}")
            raise
    
    @timeout_with_logging(120)  # 2分钟超时
    def test_analyze_branch_usecase(self):
        """测试AnalyzeBranchUseCase"""
        logger.info("开始测试AnalyzeBranchUseCase")
        
        try:
            # 执行分支分析
            result = self.analyze_branch_usecase.execute(
                repo_dir=self.test_project_path,
                branch=self.test_dev_branch,
                max_context_chain_depth=1,  # 最小深度
                max_diff_results=1  # 最小结果数
            )
            
            # 验证结果结构
            self._validate_mr_result_structure(result)
            
            # 验证分支分析特定内容
            self._validate_branch_result_content(result)
            
            logger.info("AnalyzeBranchUseCase测试通过")
            
        except Exception as e:
            logger.error(f"AnalyzeBranchUseCase测试失败: {e}")
            raise
    
    @timeout_with_logging(120)  # 2分钟超时
    def test_analyze_branch_usecase_with_commit_sha(self):
        """测试AnalyzeBranchUseCase使用commit SHA"""
        logger.info("开始测试AnalyzeBranchUseCase使用commit SHA")
        
        try:
            # 获取最新的commit SHA
            logger.info("正在获取commit SHA...")
            commit_sha = self.git_service.get_remote_latest_branch_commit_sha(
                self.test_project_path, self.test_dev_branch, "origin"
            )
            
            logger.info(f"Branch commit SHA: {commit_sha}")
            
            # 执行分支分析
            logger.info("开始执行分支分析...")
            result = self.analyze_branch_usecase.execute(
                repo_dir=self.test_project_path,
                branch=self.test_dev_branch,
                commit_sha=commit_sha,
                max_context_chain_depth=1,  # 最小深度
                max_diff_results=1  # 最小结果数
            )
            
            logger.info("分支分析执行完成，开始验证结果...")
            
            # 验证结果
            self._validate_mr_result_structure(result)
            
            logger.info("AnalyzeBranchUseCase使用commit SHA测试通过")
            
        except Exception as e:
            logger.error(f"AnalyzeBranchUseCase使用commit SHA测试失败: {e}")
            raise
    
    def test_usecase_error_handling(self):
        """测试UseCase错误处理"""
        logger.info("开始测试UseCase错误处理")
        
        # 测试无效的MR ID
        try:
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=99999,  # 无效的MR ID
                base_branch=self.test_base_branch,
                dev_branch=self.test_dev_branch,
                max_diff_results=1
            )
            
            # 即使MR不存在，也应该返回空结果而不是抛出异常
            self.assertIsNotNone(result)
            self.assertTrue(hasattr(result, 'diffs'))
            
            logger.info("无效MR ID错误处理测试通过")
            
        except Exception as e:
            logger.warning(f"无效MR ID测试出现异常（可能是预期的）: {e}")
        
        # 测试无效的分支名
        try:
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=self.test_mr_id,
                base_branch="invalid_branch",
                dev_branch="invalid_branch",
                max_diff_results=1
            )
            
            # 应该能够处理无效分支
            self.assertIsNotNone(result)
            
            logger.info("无效分支名错误处理测试通过")
            
        except Exception as e:
            logger.warning(f"无效分支名测试出现异常（可能是预期的）: {e}")
        
        logger.info("UseCase错误处理测试完成")
    
    def test_usecase_performance(self):
        """测试UseCase性能"""
        logger.info("开始测试UseCase性能")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 执行MR分析
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=self.test_mr_id,
                base_branch=self.test_base_branch,
                dev_branch=self.test_dev_branch,
                max_context_chain_depth=1,  # 最小深度
                exclude_patterns=config.exclude_patterns,
                max_diff_results=1  # 最小结果数
            )
            
            # 记录结束时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"UseCase执行时间: {execution_time:.2f}秒")
            
            # 验证结果
            self._validate_mr_result_structure(result)
            
            # 性能断言（根据实际情况调整）
            self.assertLess(execution_time, 300, f"执行时间过长: {execution_time}秒")
            
            logger.info("UseCase性能测试通过")
            
        except Exception as e:
            logger.error(f"UseCase性能测试失败: {e}")
            raise
    
    def _validate_mr_result_structure(self, result):
        """验证MR结果结构"""
        self.assertIsNotNone(result, "结果不能为空")
        self.assertTrue(hasattr(result, 'mr_id'), "结果缺少mr_id字段")
        self.assertTrue(hasattr(result, 'base_branch'), "结果缺少base_branch字段")
        self.assertTrue(hasattr(result, 'dev_branch'), "结果缺少dev_branch字段")
        self.assertTrue(hasattr(result, 'diffs'), "结果缺少diffs字段")
        self.assertIsInstance(result.diffs, list, "diffs字段必须是列表")
        
        logger.info(f"MR结果结构验证通过: mr_id={result.mr_id}, diffs数量={len(result.diffs)}")
    
    def _validate_mr_result_content(self, result):
        """验证MR结果内容"""
        # 验证基本信息
        self.assertEqual(result.mr_id, self.test_mr_id, f"MR ID不匹配: {result.mr_id} != {self.test_mr_id}")
        self.assertEqual(result.base_branch, self.test_base_branch, f"Base分支不匹配: {result.base_branch} != {self.test_base_branch}")
        self.assertEqual(result.dev_branch, self.test_dev_branch, f"Dev分支不匹配: {result.dev_branch} != {self.test_dev_branch}")
        
        # 验证diffs内容
        for i, diff in enumerate(result.diffs):
            self.assertTrue(hasattr(diff, 'filepath'), f"Diff {i} 缺少filepath字段")
            self.assertTrue(hasattr(diff, 'file_status'), f"Diff {i} 缺少file_status字段")
            self.assertTrue(hasattr(diff, 'affected_functions'), f"Diff {i} 缺少affected_functions字段")
            self.assertIsInstance(diff.affected_functions, list, f"Diff {i} affected_functions必须是列表")
            
            # 验证受影响的函数
            for j, func in enumerate(diff.affected_functions):
                self.assertTrue(hasattr(func, 'name'), f"函数 {j} 缺少name字段")
                self.assertTrue(hasattr(func, 'filepath'), f"函数 {j} 缺少filepath字段")
                self.assertTrue(hasattr(func, 'code'), f"函数 {j} 缺少code字段")
                self.assertTrue(hasattr(func, 'llm_results'), f"函数 {j} 缺少llm_results字段")
                self.assertIsInstance(func.llm_results, list, f"函数 {j} llm_results必须是列表")
        
        logger.info("MR结果内容验证通过")
    
    def _validate_branch_result_content(self, result):
        """验证分支分析结果内容"""
        # 验证基本信息
        self.assertEqual(result.base_branch, self.test_dev_branch, f"分支不匹配: {result.base_branch} != {self.test_dev_branch}")
        self.assertEqual(result.dev_branch, self.test_dev_branch, f"分支不匹配: {result.dev_branch} != {self.test_dev_branch}")
        
        # 分支分析应该有结果
        self.assertGreater(len(result.diffs), 0, "分支分析应该有结果")
        
        logger.info("分支分析结果内容验证通过")
    
    def _validate_report_content(self, report_content: str):
        """验证报告内容"""
        # 报告内容可能为空，这是正常的
        self.assertIsInstance(report_content, str)
        # 不强制要求内容长度，因为可能因限制而提前结束


if __name__ == "__main__":
    # 直接运行测试
    unittest.main() 