#!/usr/bin/env python3
"""
集成测试：验证命令行规则分组参数是否正确传递到规则分组逻辑中
"""

import os
import sys
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.config import config
from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestCLIRuleGroupingIntegration(unittest.TestCase):
    """测试命令行规则分组参数集成"""
    
    def setUp(self):
        """设置测试环境"""
        # 保存原始配置
        self.original_config = {
            'rule_grouping_strategy': config.rule_grouping_strategy,
            'min_rule_group_size': config.min_rule_group_size,
            'max_rule_group_size': config.max_rule_group_size,
            'target_rule_group_size': config.target_rule_group_size,
            'rule_similarity_threshold': config.rule_similarity_threshold,
            'max_llm_calls_per_affected_function': config.max_llm_calls_per_affected_function,
        }
        
        # 创建测试规则
        self.test_rules = [
            CodeCheckRule(
                id="TEST001",
                name="测试规则1",
                category=["命名规范", "变量"],
                description="测试变量命名规范",
                enabled=True
            ),
            CodeCheckRule(
                id="TEST002",
                name="测试规则2",
                category=["命名规范", "函数"],
                description="测试函数命名规范",
                enabled=True
            ),
            CodeCheckRule(
                id="TEST003",
                name="测试规则3",
                category=["代码质量", "复杂度"],
                description="测试代码复杂度",
                enabled=True
            ),
            CodeCheckRule(
                id="TEST004",
                name="测试规则4",
                category=["代码质量", "长度"],
                description="测试代码长度",
                enabled=True
            ),
            CodeCheckRule(
                id="TEST005",
                name="测试规则5",
                category=["安全规范"],
                description="测试安全规范",
                enabled=True
            ),
        ]
        
        # 创建Mock RuleManager
        self.rule_manager = MagicMock(spec=RuleManager)
        self.rule_manager.load_rules.return_value = self.test_rules
    
    def tearDown(self):
        """恢复原始配置"""
        for key, value in self.original_config.items():
            setattr(config, key, value)
    
    def test_adaptive_strategy_with_custom_params(self):
        """测试adaptive策略使用自定义参数"""
        # 设置配置
        config.rule_grouping_strategy = "adaptive"
        config.min_rule_group_size = 3
        config.max_rule_group_size = 8
        config.target_rule_group_size = 5
        
        # 模拟分组结果
        expected_groups = {
            "adaptive_group_0": self.test_rules[:3],
            "adaptive_group_1": self.test_rules[3:],
        }
        
        self.rule_manager.group_rules_adaptive_for_list.return_value = expected_groups
        
        # 验证调用参数
        self.rule_manager.group_rules_adaptive_for_list.assert_not_called()
        
        # 模拟调用
        result = self.rule_manager.group_rules_adaptive_for_list(
            self.test_rules,
            config.min_rule_group_size,
            config.max_rule_group_size,
            config.target_rule_group_size
        )
        
        # 验证调用参数正确
        self.rule_manager.group_rules_adaptive_for_list.assert_called_once_with(
            self.test_rules,
            3,  # min_rule_group_size
            8,  # max_rule_group_size
            5   # target_rule_group_size
        )
        
        self.assertEqual(result, expected_groups)
    
    def test_similarity_strategy_with_custom_threshold(self):
        """测试similarity策略使用自定义阈值"""
        # 设置配置
        config.rule_grouping_strategy = "similarity"
        config.rule_similarity_threshold = 0.3
        
        # 模拟分组结果
        expected_groups = {
            "similarity_group_0": self.test_rules[:2],
            "similarity_group_1": self.test_rules[2:4],
            "similarity_group_2": self.test_rules[4:],
        }
        
        self.rule_manager.group_rules_by_similarity_for_list.return_value = expected_groups
        
        # 验证调用参数
        self.rule_manager.group_rules_by_similarity_for_list.assert_not_called()
        
        # 模拟调用
        result = self.rule_manager.group_rules_by_similarity_for_list(
            self.test_rules,
            config.rule_similarity_threshold
        )
        
        # 验证调用参数正确
        self.rule_manager.group_rules_by_similarity_for_list.assert_called_once_with(
            self.test_rules,
            0.3  # rule_similarity_threshold
        )
        
        self.assertEqual(result, expected_groups)
    
    def test_category_strategy(self):
        """测试category策略"""
        # 设置配置
        config.rule_grouping_strategy = "category"
        
        # 模拟分组结果
        expected_groups = {
            ("命名规范",): self.test_rules[:2],
            ("代码质量",): self.test_rules[2:4],
            ("安全规范",): self.test_rules[4:],
        }
        
        self.rule_manager.group_rules_by_category_for_list.return_value = expected_groups
        
        # 验证调用参数
        self.rule_manager.group_rules_by_category_for_list.assert_not_called()
        
        # 模拟调用
        result = self.rule_manager.group_rules_by_category_for_list(self.test_rules)
        
        # 验证调用参数正确
        self.rule_manager.group_rules_by_category_for_list.assert_called_once_with(self.test_rules)
        
        self.assertEqual(result, expected_groups)
    
    def test_minimize_calls_strategy(self):
        """测试minimize_calls策略"""
        # 设置配置
        config.rule_grouping_strategy = "minimize_calls"
        
        # 模拟分组结果
        expected_groups = {
            "minimize_calls_group_0": self.test_rules[:3],
            "minimize_calls_group_1": self.test_rules[3:],
        }
        
        self.rule_manager.group_rules_minimize_calls.return_value = expected_groups
        
        # 验证调用参数
        self.rule_manager.group_rules_minimize_calls.assert_not_called()
        
        # 模拟调用
        result = self.rule_manager.group_rules_minimize_calls(self.test_rules)
        
        # 验证调用参数正确
        self.rule_manager.group_rules_minimize_calls.assert_called_once_with(self.test_rules)
        
        self.assertEqual(result, expected_groups)
    
    def test_config_parameter_binding(self):
        """测试配置参数绑定"""
        # 模拟命令行参数
        test_args = MagicMock()
        test_args.rule_grouping_strategy = "adaptive"
        test_args.min_rule_group_size = 4
        test_args.max_rule_group_size = 10
        test_args.target_rule_group_size = 7
        test_args.rule_similarity_threshold = 0.5
        test_args.max_llm_calls_per_affected_function = 5
        
        # 模拟参数绑定逻辑
        if test_args.rule_grouping_strategy:
            config.rule_grouping_strategy = test_args.rule_grouping_strategy
        if test_args.min_rule_group_size:
            config.min_rule_group_size = test_args.min_rule_group_size
        if test_args.max_rule_group_size:
            config.max_rule_group_size = test_args.max_rule_group_size
        if test_args.target_rule_group_size:
            config.target_rule_group_size = test_args.target_rule_group_size
        if test_args.rule_similarity_threshold:
            config.rule_similarity_threshold = test_args.rule_similarity_threshold
        if test_args.max_llm_calls_per_affected_function:
            config.max_llm_calls_per_affected_function = test_args.max_llm_calls_per_affected_function
        
        # 验证配置已更新
        self.assertEqual(config.rule_grouping_strategy, "adaptive")
        self.assertEqual(config.min_rule_group_size, 4)
        self.assertEqual(config.max_rule_group_size, 10)
        self.assertEqual(config.target_rule_group_size, 7)
        self.assertEqual(config.rule_similarity_threshold, 0.5)
        self.assertEqual(config.max_llm_calls_per_affected_function, 5)
    
    def test_parameter_validation(self):
        """测试参数验证"""
        # 测试无效的adaptive参数
        config.rule_grouping_strategy = "adaptive"
        config.min_rule_group_size = 10
        config.max_rule_group_size = 5  # 小于min_rule_group_size
        
        # 这里应该触发验证错误，但在实际使用中可能不会立即验证
        # 我们只是测试配置设置是否正确
        self.assertEqual(config.min_rule_group_size, 10)
        self.assertEqual(config.max_rule_group_size, 5)
        
        # 测试无效的similarity参数
        config.rule_grouping_strategy = "similarity"
        config.rule_similarity_threshold = 1.5  # 超出范围
        
        self.assertEqual(config.rule_similarity_threshold, 1.5)
    
    def test_strategy_selection_logic(self):
        """测试策略选择逻辑"""
        strategies = ["category", "similarity", "adaptive", "minimize_calls"]
        
        for strategy in strategies:
            config.rule_grouping_strategy = strategy
            
            # 为每个策略设置模拟返回值
            if strategy == "category":
                self.rule_manager.group_rules_by_category_for_list.return_value = {"category_group": self.test_rules}
                result = self.rule_manager.group_rules_by_category_for_list(self.test_rules)
            elif strategy == "similarity":
                self.rule_manager.group_rules_by_similarity_for_list.return_value = {"similarity_group": self.test_rules}
                result = self.rule_manager.group_rules_by_similarity_for_list(
                    self.test_rules, config.rule_similarity_threshold
                )
            elif strategy == "adaptive":
                self.rule_manager.group_rules_adaptive_for_list.return_value = {"adaptive_group": self.test_rules}
                result = self.rule_manager.group_rules_adaptive_for_list(
                    self.test_rules,
                    config.min_rule_group_size,
                    config.max_rule_group_size,
                    config.target_rule_group_size
                )
            elif strategy == "minimize_calls":
                self.rule_manager.group_rules_minimize_calls.return_value = {"minimize_calls_group": self.test_rules}
                result = self.rule_manager.group_rules_minimize_calls(self.test_rules)
            else:
                result = {"all": self.test_rules}
            
            # 验证结果不为空
            self.assertIsNotNone(result)
            self.assertIsInstance(result, dict)


if __name__ == "__main__":
    unittest.main() 