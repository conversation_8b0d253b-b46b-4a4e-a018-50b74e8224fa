"""
参数名一致性测试

测试目标：
1. 验证参数名在整个调用链路中的一致性
2. 测试新旧参数名的兼容性
3. 验证参数名映射的正确性
4. 测试参数名变更的影响范围

测试覆盖：
- CLI参数名
- UseCase参数名
- Service参数名
- 配置项参数名
- 新旧参数名映射
"""

import inspect
import unittest
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """Mock LLM客户端"""
    
    def __init__(self):
        super().__init__("http://mock.local")
    
    def generate(self, prompt, parameters, token=None):
        return """<FinalAnswer>
{
  "is_pass": true,
  "reason": "测试通过",
  "violations": []
}
</FinalAnswer>"""
    
    def get_config(self):
        return {}


class TestParameterNameConsistency(unittest.TestCase):
    """参数名一致性测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_git_service = Mock()
        self.mock_llm_client = MockLLMClient()
    
    def test_parameter_names_in_llm_service(self):
        """
        测试LLMService中的参数名一致性
        
        验证目标：确保LLMService使用正确的参数名
        """
        # 检查LLMService的方法签名
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        # 检查analyze_mr方法的参数
        analyze_mr_sig = inspect.signature(llm_service.analyze_mr)
        analyze_mr_params = list(analyze_mr_sig.parameters.keys())
        
        # 验证参数名（不包含'self'，与实际一致）
        expected_params = ['check_mr_result', 'max_workers']
        for param in expected_params:
            self.assertIn(param, analyze_mr_params, f"缺少参数: {param}")
        
        # 检查是否有旧的参数名
        old_params = ['call_chain_radius']  # 旧参数名
        for param in old_params:
            self.assertNotIn(param, analyze_mr_params, f"不应该包含旧参数名: {param}")
    
    def test_parameter_names_in_analyze_usecase(self):
        """
        测试AnalyzeMRUsecase中的参数名一致性
        
        验证目标：确保AnalyzeMRUsecase使用正确的参数名
        """
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        usecase = AnalyzeMRUseCase(
            git_service=self.mock_git_service,
            llm_service=llm_service,
            repo_analyzer=Mock()
        )
        
        # 检查execute方法的参数
        execute_sig = inspect.signature(usecase.execute)
        execute_params = list(execute_sig.parameters.keys())
        
        # 验证参数名（不包含'self'，与实际一致）
        expected_params = ['repo_dir', 'mr_id', 'base_branch', 'dev_branch', 'max_context_chain_depth', 'base_commit_sha', 'dev_commit_sha', 'remote', 'exclude_patterns', 'max_diff_results']
        for param in expected_params:
            self.assertIn(param, execute_params, f"缺少参数: {param}")
        
        # 检查是否有旧的参数名
        old_params = ['call_chain_radius']
        for param in old_params:
            self.assertNotIn(param, execute_params, f"不应该包含旧参数名: {param}")
    
    def test_parameter_names_in_report_usecase(self):
        """
        测试AnalyzeMRAndReportUsecase中的参数名一致性
        
        验证目标：确保AnalyzeMRAndReportUsecase使用正确的参数名
        """
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        # 创建服务编排器
        from gate_keeper.application.service.service_orchestrator import \
            ServiceOrchestrator
        orchestrator = ServiceOrchestrator(
            git_service=self.mock_git_service,
            llm_client=self.mock_llm_client
        )
        
        usecase = AnalyzeMRAndReportUsecase(orchestrator)
        
        # 检查execute方法的参数
        execute_sig = inspect.signature(usecase.execute)
        execute_params = list(execute_sig.parameters.keys())
        
        # 验证参数名（不包含'self'，与实际一致）
        expected_params = ['repo_dir', 'project_id', 'mr_id', 'base_branch', 'dev_branch', 'max_context_chain_depth', 'base_commit_sha', 'dev_commit_sha', 'exclude_patterns', 'max_diff_results']
        for param in expected_params:
            self.assertIn(param, execute_params, f"缺少参数: {param}")
        
        # 检查是否有旧的参数名
        old_params = ['call_chain_radius']
        for param in old_params:
            self.assertNotIn(param, execute_params, f"不应该包含旧参数名: {param}")
    
    def test_config_parameter_names(self):
        """
        测试配置项参数名一致性
        
        验证目标：确保配置项使用正确的参数名
        """
        with patch('gate_keeper.config.config') as mock_config:
            # 设置新配置项
            mock_config.context_manager.max_chain_depth = 3
            mock_config.context_manager.config.max_chains = 5
            mock_config.use_optimized_context = True
            
            # 创建LLM服务
            llm_service = LLMService(
                client=self.mock_llm_client,
                max_workers=1
            )
            
            # 验证新配置项存在
            self.assertTrue(hasattr(llm_service, 'context_manager'))
            if llm_service.context_manager:
                self.assertTrue(hasattr(llm_service.context_manager, 'max_chain_depth'))
                self.assertTrue(hasattr(llm_service.context_manager.config, 'max_chains'))
            self.assertTrue(hasattr(llm_service, 'use_optimized_context'))
            
            # 验证旧配置项不存在或已废弃
            # 注意：这里检查的是属性名，不是配置项名
            self.assertFalse(hasattr(llm_service, 'call_chain_radius'))
    
    def test_parameter_mapping_consistency(self):
        """
        测试参数映射一致性
        
        验证目标：确保参数在不同层级之间的映射正确
        """
        # 定义参数映射关系
        parameter_mapping = {
            'max_context_chain_depth': {
                'cli': 'max_context_chain_depth',
                'usecase': 'max_context_chain_depth',
                'service': 'max_context_chain_depth',
                'config': 'max_context_chain_depth'
            },
            'max_context_chains': {
                'cli': 'max_context_chains',
                'usecase': 'max_context_chains',
                'service': 'max_context_chains',
                'config': 'max_context_chains'
            },
            'use_optimized_context': {
                'cli': 'use_optimized_context',
                'usecase': 'use_optimized_context',
                'service': 'use_optimized_context',
                'config': 'use_optimized_context'
            }
        }
        
        # 验证映射一致性
        for param_name, mappings in parameter_mapping.items():
            # 检查所有映射是否一致
            values = list(mappings.values())
            self.assertTrue(all(v == values[0] for v in values), 
                          f"参数 {param_name} 的映射不一致: {mappings}")
    
    def test_legacy_parameter_handling(self):
        """
        测试旧参数处理
        
        验证目标：确保旧参数被正确处理，不会影响新功能
        """
        with patch('gate_keeper.config.config') as mock_config:
            # 同时设置新旧配置项
            mock_config.max_context_chain_depth = 2  # 新配置项
            mock_config.max_context_chains = 5  # 新配置项
            
            # Mock ContextManager配置验证
            with patch('gate_keeper.application.service.context_management.context_manager_config.ContextManagerConfig.validate') as mock_validate:
                mock_validate.return_value = []
                
                # 创建LLM服务
                llm_service = LLMService(
                    client=self.mock_llm_client,
                    max_workers=1,
                    use_optimized_context=True,
                    static_analyzer=Mock()
                )
                
                # 验证新配置项优先
                self.assertEqual(llm_service.context_manager.max_chain_depth, 2)
            
            # 验证旧配置项不会影响新功能
            # 旧配置项在新逻辑中已废弃，不会影响新功能
    
    def test_method_signature_consistency(self):
        """
        测试方法签名一致性
        
        验证目标：确保相关方法使用一致的参数名
        """
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        # 检查相关方法的参数名一致性
        methods_to_check = [
            'analyze_mr',
            '_analyze_function',
            '_generate_prompt',
            '_generate_simple_prompt_content'
        ]
        
        for method_name in methods_to_check:
            if hasattr(llm_service, method_name):
                method = getattr(llm_service, method_name)
                sig = inspect.signature(method)
                params = list(sig.parameters.keys())
                
                # 检查是否包含正确的参数名
                if 'max_context_chain_depth' in params:
                    # 如果方法包含新参数名，确保不包含旧参数名
                    self.assertNotIn('call_chain_radius', params, 
                                   f"方法 {method_name} 不应该包含旧参数名")
    
    def test_parameter_documentation_consistency(self):
        """
        测试参数文档一致性
        
        验证目标：确保代码中的参数名与文档一致
        """
        # 检查LLMService的文档字符串
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        # 检查analyze_mr方法的文档
        if llm_service.analyze_mr.__doc__:
            doc = llm_service.analyze_mr.__doc__
            
            # 验证文档中包含新参数名
            self.assertIn('max_context_chain_depth', doc, 
                         "文档中应该包含新参数名 max_context_chain_depth")
            
            # 验证文档中不包含旧参数名
            self.assertNotIn('call_chain_radius', doc, 
                           "文档中不应该包含旧参数名 call_chain_radius")
    
    def test_parameter_type_consistency(self):
        """
        测试参数类型一致性
        
        验证目标：确保相同参数在不同地方的类型一致
        """
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        # 检查参数类型
        analyze_mr_sig = inspect.signature(llm_service.analyze_mr)
        
        # 验证max_context_chain_depth参数类型
        if 'max_context_chain_depth' in analyze_mr_sig.parameters:
            param = analyze_mr_sig.parameters['max_context_chain_depth']
            # 检查参数类型（应该是int）
            if param.annotation != inspect.Parameter.empty:
                self.assertEqual(param.annotation, int, 
                               "max_context_chain_depth 参数类型应该是 int")
    
    def test_parameter_default_value_consistency(self):
        """
        测试参数默认值一致性
        
        验证目标：确保相同参数在不同地方有合理的默认值
        """
        llm_service = LLMService(
            client=self.mock_llm_client,
            max_workers=1
        )
        
        # 检查analyze_mr方法的默认值
        analyze_mr_sig = inspect.signature(llm_service.analyze_mr)
        
        # 验证max_context_chain_depth的默认值
        if 'max_context_chain_depth' in analyze_mr_sig.parameters:
            param = analyze_mr_sig.parameters['max_context_chain_depth']
            if param.default != inspect.Parameter.empty:
                # 默认值应该是合理的正整数
                self.assertIsInstance(param.default, int)
                self.assertGreater(param.default, 0)


if __name__ == '__main__':
    unittest.main() 