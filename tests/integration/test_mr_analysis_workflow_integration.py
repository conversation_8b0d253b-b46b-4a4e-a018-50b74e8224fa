"""
MR分析工作流集成测试

测试目标：验证MR分析工作流的完整流程，包括：
- Git服务调用
- 代码分析
- LLM分析
- 结果处理

验证重点：
- 工作流各阶段的正确执行
- 参数传递的正确性
- 错误处理机制
- 性能表现
"""

import os
import tempfile
from unittest.mock import Mock, patch

import pytest

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.interfaces.git_intf import ICodeRepositoryService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.external.code_analyzer import RepositoryIndex


class TestMRAnalysisWorkflowIntegration:
    """MR分析工作流集成测试类"""
    
    @pytest.fixture
    def temp_repo_dir(self):
        """创建临时仓库目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def mock_git_service(self):
        """模拟Git服务"""
        service = Mock(spec=ICodeRepositoryService)
        
        # 模拟差异文件 - 使用简单的Mock对象列表
        mock_patched_file = Mock()
        mock_patched_file.path = "test_file.py"
        mock_patched_file.is_removed_file = False
        mock_patched_file.is_added_file = False
        mock_patched_file.__iter__ = lambda self: iter([])
        service.get_diff_by_mr_id.return_value = [mock_patched_file]
        
        # 模拟文件内容
        service.get_file_content_by_ref.return_value = """
def test_function():
    # 测试函数
    x = 1
    y = 2
    return x + y

def another_function():
    # 另一个函数
    pass
"""
        
        return service
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        service = Mock(spec=LLMService)
        
        # 模拟LLM响应 - LLMService使用analyze_mr方法
        from gate_keeper.application.dto.result import CheckMRResult
        mock_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            diffs=[]
        )
        service.analyze_mr.return_value = mock_result
        
        return service
    
    @pytest.fixture
    def mock_repo_analyzer(self):
        """模拟仓库分析器"""
        analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 模拟受影响函数
        mock_affected_function = AffectedFunction(
            name="test_function",
            filepath="test_file.py",
            start_line=1,
            end_line=10,
            changed_lines=[5, 6, 7],
            code="def test_function():\n    pass",
            call_chains=[["main", "test_function"]],
            related_definitions=[],
            related_calls=[]
        )
        analyzer.analyze_functions.return_value = [mock_affected_function]
        
        return analyzer
    
    def test_complete_mr_analysis_workflow(self, 
                                         temp_repo_dir,
                                         mock_git_service,
                                         mock_llm_service,
                                         mock_repo_analyzer):
        """测试完整的MR分析流程"""
        
        # 1. 创建UseCase实例
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 2. 执行分析
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            max_context_chain_depth=3,
            exclude_patterns=[],
            max_diff_results=None
        )
        
        # 3. 验证Git操作阶段
        mock_git_service.get_diff_by_mr_id.assert_called_once()
        
        # 4. 验证代码分析阶段
        mock_repo_analyzer.analyze_functions.assert_called()
        
        # 5. 验证结果
        assert analyze_results is not None
    
    def test_workflow_with_empty_diff(self, temp_repo_dir):
        """测试空差异的工作流"""
        
        # 创建模拟服务
        mock_git_service = Mock(spec=ICodeRepositoryService)
        mock_llm_service = Mock(spec=LLMService)
        mock_repo_analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 设置空差异
        mock_git_service.get_diff_by_mr_id.return_value = []
        
        # 模拟LLM响应
        from gate_keeper.application.dto.result import CheckMRResult
        mock_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            diffs=[]
        )
        mock_llm_service.analyze_mr.return_value = mock_result
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 执行分析
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            max_context_chain_depth=3,
            exclude_patterns=[],
            max_diff_results=None
        )
        
        # 验证结果
        assert analyze_results is not None
        assert len(analyze_results.diffs) == 0
    
    def test_error_handling_in_workflow(self, temp_repo_dir):
        """测试工作流中的错误处理"""
        
        # 创建模拟服务
        mock_git_service = Mock(spec=ICodeRepositoryService)
        mock_llm_service = Mock(spec=LLMService)
        mock_repo_analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 模拟Git服务异常
        mock_git_service.get_diff_by_mr_id.side_effect = Exception("Git service error")
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 执行分析，应该抛出异常
        with pytest.raises(Exception):
            usecase.execute(
                repo_dir=temp_repo_dir,
                mr_id=123,
                base_branch="main",
                dev_branch="feature/test",
                max_context_chain_depth=3,
                exclude_patterns=[],
                max_diff_results=None
            ) 