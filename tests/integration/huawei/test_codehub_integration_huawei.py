"""
华为CodeHub集成测试
"""
import os
from unittest.mock import MagicMock, patch

import pytest

from gate_keeper.infrastructure.git.client_factory import GitClientFactory
from gate_keeper.infrastructure.git.codehub.client import CodeHub


@pytest.mark.huawei
@pytest.mark.integration
class TestHuaweiCodeHubIntegration:
    """华为CodeHub集成测试"""
    
    def test_codehub_integration_basic(self):
        """基本的CodeHub集成测试"""
        with patch.object(GitClientFactory, 'create_comment_service') as mock_factory:
            mock_factory.return_value = CodeHub("test_token")
            
            client = GitClientFactory.create_comment_service(
                access_token="test_token"
            )
            
            assert isinstance(client, CodeHub)

    def test_codehub_mr_operations_mocked(self):
        """测试CodeHub MR相关操作（使用mock）"""
        client = CodeHub("test_token")
        
        # Mock API响应
        mock_mr_data = {
            "id": 1,
            "title": "Test MR",
            "state": "opened",
            "source_branch": "feature/test",
            "target_branch": "main",
            "author": {"username": "testuser", "name": "Test User"},
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "web_url": "https://codehub.test.com/test/repo/merge_requests/1"
        }
        
        mock_discussion_data = {
            "id": "d1",
            "individual_note": False,
            "notes": [{
                "id": 101,
                "body": "Test comment",
                "author": {"username": "testuser"},
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "noteable_id": 1,
                "noteable_type": "MergeRequest",
                "review_categories": "design",
                "severity": "suggestion",
                "file_path": "test.py",
                "line": 10
            }],
            "project_id": 1,
            "noteable_type": "MergeRequest",
            "resolved": False,
            "project_full_path": "test/repo",
            "review_categories": "design",
            "severity": "suggestion"
        }
        
        # Mock HTTP请求
        with patch('requests.get') as mock_get, \
             patch('requests.post') as mock_post:
            
            # Mock get_mr_info
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = mock_mr_data
            
            mr_info = client.get_mr_info(
                project_id="1",
                mr_id="1"
            )
            assert mr_info is not None
            assert mr_info.id == "1"
            assert mr_info.title == "Test MR"
            
            # Mock post_discussion_to_mr
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = mock_discussion_data
            
            comment_result = client.post_discussion_to_mr(
                project_id=1,
                mr_id=1,
                comment="Test comment"
            )
            assert comment_result is not None
            assert comment_result.id == "d1"

    def test_codehub_update_discussion_mocked(self):
        """测试CodeHub更新讨论操作（使用mock）"""
        client = CodeHub("test_token")
        
        # Mock API响应
        mock_discussion_data = {
            "id": "d1",
            "resolved": False,
            "notes": [],
            "project_id": 1,
            "noteable_type": "MergeRequest",
            "project_full_path": "test/repo",
            "review_categories": "design",
            "severity": "suggestion"
        }
        
        # Mock HTTP请求
        with patch('requests.put') as mock_put:
            mock_put.return_value.status_code = 200
            mock_put.return_value.json.return_value = mock_discussion_data
            
            # 测试使用dict参数
            result = client.update_discussion_to_mr(
                project_id="1",
                mr_id="1", 
                discussion_id="d1",
                discussion_data={"is_resolved": False}
            )
            assert result is not None
            assert mock_put.called
            
            # 验证PUT请求的参数
            call_args = mock_put.call_args
            assert "data" in call_args[1]
            assert call_args[1]["data"]["resolved"] == False 