"""
增强规则检查集成测试

测试评测集更新后的完整检查流程，包括regex匹配和建议生成
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch

from gate_keeper.application.service.rule.rule_service.manager import RuleManager
from gate_keeper.application.service.rule.regex_matcher import RegexMatcher
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestEnhancedRuleCheckingIntegration(unittest.TestCase):
    """测试增强规则检查的集成功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_yaml_file = os.path.join(self.temp_dir, "test_rules.yaml")
        self._create_test_yaml()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_yaml_file):
            os.remove(self.test_yaml_file)
        os.rmdir(self.temp_dir)
    
    def _create_test_yaml(self):
        """创建测试用的YAML文件"""
        yaml_content = """
categories:
  - name: "内存安全"
    rule_groups:
      - name: "内存拷贝"
        principles:
          - content: "使用memcpy前必须检查边界"
            match_regex: "memcpy\\s*\\("
            suggestion: "检查所有内存拷贝操作前是否有边界校验，重点关注：1)源数据长度是否超过目标缓冲区大小；2)目标缓冲区是否有足够空间"
            pos_examples:
              - code: |
                  if (src_len <= dst_size) {
                      memcpy(dst, src, src_len);
                  }
                think: "正确检查了边界"
            neg_examples:
              - code: |
                  memcpy(dst, src, len);
                think: "未检查边界，可能溢出"
          - content: "使用malloc后必须检查返回值"
            match_regex: "malloc\\s*\\("
            suggestion: "检查malloc返回值，重点关注：1)返回值是否为NULL；2)NULL指针的错误处理；3)避免使用未检查的指针"
            pos_examples:
              - code: |
                  ptr = malloc(size);
                  if (ptr != NULL) {
                      // 使用ptr
                  }
                think: "检查了返回值"
            neg_examples:
              - code: |
                  ptr = malloc(size);
                  *ptr = value;
                think: "未检查返回值"
  - name: "数组操作"
    rule_groups:
      - name: "数组访问"
        principles:
          - content: "数组访问前必须检查边界"
            match_regex: "\\[\\s*\\w+\\s*\\]"
            suggestion: "检查所有数组下标访问是否有边界校验，重点关注：1)下标变量是否在有效范围内；2)是否检查了下标小于数组长度"
            pos_examples:
              - code: |
                  if (index >= 0 && index < array_size) {
                      value = array[index];
                  }
                think: "正确的边界检查"
            neg_examples:
              - code: |
                  value = array[index];
                think: "未检查边界"
  - name: "通用规则"
    rule_groups:
      - name: "代码风格"
        principles:
          - content: "函数名应使用小写字母和下划线"
            suggestion: "检查函数命名规范，确保使用snake_case风格"
            pos_examples:
              - code: "void process_data() {}"
                think: "正确的命名"
            neg_examples:
              - code: "void ProcessData() {}"
                think: "使用了驼峰命名"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
    
    def test_rule_loading_with_regex_fields(self):
        """测试规则加载包含regex字段"""
        rule_manager = RuleManager(
            rule_file_path=self.test_yaml_file,
            enable_regex_matching=True
        )
        
        rules = rule_manager.load_rules()
        
        # 验证规则数量
        self.assertEqual(len(rules), 4)
        
        # 验证包含regex的规则
        rules_with_regex = [r for r in rules if r.match_regex]
        self.assertEqual(len(rules_with_regex), 3)
        
        # 验证包含suggestion的规则
        rules_with_suggestion = [r for r in rules if r.suggestion]
        self.assertEqual(len(rules_with_suggestion), 4)
        
        # 验证具体规则内容
        memcpy_rule = next((r for r in rules if "memcpy" in r.rule_value), None)
        self.assertIsNotNone(memcpy_rule)
        self.assertEqual(memcpy_rule.match_regex, "memcpy\\s*\\(")
        self.assertIn("边界校验", memcpy_rule.suggestion)
    
    def test_regex_based_rule_filtering(self):
        """测试基于regex的规则筛选"""
        rule_manager = RuleManager(
            rule_file_path=self.test_yaml_file,
            enable_regex_matching=True
        )
        
        # 创建包含memcpy的代码元素
        memcpy_code = Mock(spec=CodeElement)
        memcpy_code.name = "unsafe_copy"
        memcpy_code.type = "function"
        memcpy_code.filepath = "test.c"
        memcpy_code.code = """
        void unsafe_copy(char *dst, char *src, int len) {
            memcpy(dst, src, len);  // 未检查边界
        }
        """
        
        # 获取适用规则
        applicable_rules = rule_manager.get_applicable_rules_with_regex(memcpy_code)
        
        # 验证筛选结果
        total_applicable = sum(len(rules) for rules in applicable_rules.values())
        self.assertGreater(total_applicable, 0)
        
        # 验证memcpy规则被包含
        all_rules = []
        for rule_list in applicable_rules.values():
            all_rules.extend(rule_list)
        
        memcpy_rules = [r for r in all_rules if "memcpy" in r.rule_value]
        self.assertGreater(len(memcpy_rules), 0)
    
    def test_regex_matching_accuracy(self):
        """测试regex匹配的准确性"""
        rule_manager = RuleManager(
            rule_file_path=self.test_yaml_file,
            enable_regex_matching=True
        )
        
        test_cases = [
            {
                "name": "memcpy代码",
                "code": "memcpy(dst, src, len);",
                "should_match": ["memcpy"],
                "should_not_match": ["malloc", "数组访问"]
            },
            {
                "name": "malloc代码",
                "code": "ptr = malloc(size);",
                "should_match": ["malloc"],
                "should_not_match": ["memcpy", "数组访问"]
            },
            {
                "name": "数组访问代码",
                "code": "value = array[index];",
                "should_match": ["数组访问"],
                "should_not_match": ["memcpy", "malloc"]
            },
            {
                "name": "普通代码",
                "code": "int result = a + b;",
                "should_match": [],
                "should_not_match": ["memcpy", "malloc", "数组访问"]
            }
        ]
        
        for case in test_cases:
            with self.subTest(case=case["name"]):
                code_element = Mock(spec=CodeElement)
                code_element.name = case["name"]
                code_element.type = "function"
                code_element.filepath = "test.c"
                code_element.code = case["code"]
                
                applicable_rules = rule_manager.get_applicable_rules_with_regex(code_element)
                all_rules = []
                for rule_list in applicable_rules.values():
                    all_rules.extend(rule_list)
                
                # 检查应该匹配的规则
                for should_match in case["should_match"]:
                    matching_rules = [r for r in all_rules if should_match in r.rule_value]
                    self.assertGreater(len(matching_rules), 0, 
                                     f"代码 '{case['code']}' 应该匹配 '{should_match}' 规则")
                
                # 检查不应该匹配的规则
                for should_not_match in case["should_not_match"]:
                    non_matching_rules = [r for r in all_rules if should_not_match in r.rule_value]
                    self.assertEqual(len(non_matching_rules), 0, 
                                   f"代码 '{case['code']}' 不应该匹配 '{should_not_match}' 规则")
    
    def test_suggestion_field_availability(self):
        """测试suggestion字段的可用性"""
        rule_manager = RuleManager(
            rule_file_path=self.test_yaml_file,
            enable_regex_matching=True
        )
        
        rules = rule_manager.load_rules()
        
        # 验证所有规则都有suggestion
        for rule in rules:
            self.assertIsInstance(rule.suggestion, str)
            if rule.suggestion:  # 如果有suggestion内容
                self.assertGreater(len(rule.suggestion.strip()), 0)
        
        # 验证特定规则的suggestion内容
        memcpy_rule = next((r for r in rules if "memcpy" in r.rule_value), None)
        self.assertIsNotNone(memcpy_rule)
        self.assertIn("边界校验", memcpy_rule.suggestion)
        self.assertIn("目标缓冲区", memcpy_rule.suggestion)
        
        malloc_rule = next((r for r in rules if "malloc" in r.rule_value), None)
        self.assertIsNotNone(malloc_rule)
        self.assertIn("返回值", malloc_rule.suggestion)
        self.assertIn("NULL", malloc_rule.suggestion)
    
    def test_performance_comparison(self):
        """测试性能对比（传统方法 vs regex方法）"""
        rule_manager = RuleManager(
            rule_file_path=self.test_yaml_file,
            enable_regex_matching=True
        )
        
        # 创建测试代码元素
        code_element = Mock(spec=CodeElement)
        code_element.name = "test_function"
        code_element.type = "function"
        code_element.filepath = "test.c"
        code_element.code = """
        void test_function() {
            char buffer[100];
            char *ptr = malloc(200);
            if (ptr != NULL) {
                memcpy(buffer, ptr, 100);
                buffer[0] = 'A';
                free(ptr);
            }
        }
        """
        
        import time
        
        # 测试传统方法
        start_time = time.time()
        for _ in range(10):
            traditional_rules = rule_manager.get_applicable_rules(code_element)
        traditional_time = time.time() - start_time
        
        # 测试regex方法
        start_time = time.time()
        for _ in range(10):
            regex_rules = rule_manager.get_applicable_rules_with_regex(code_element)
        regex_time = time.time() - start_time
        
        # 验证结果一致性（至少regex方法不应该遗漏重要规则）
        traditional_count = sum(len(rules) for rules in traditional_rules.values())
        regex_count = sum(len(rules) for rules in regex_rules.values())
        
        # regex方法可能筛选掉一些不相关的规则，所以数量可能更少
        self.assertLessEqual(regex_count, traditional_count)
        
        # 性能应该有所改善（允许一些误差）
        print(f"传统方法: {traditional_time:.4f}s, Regex方法: {regex_time:.4f}s")
    
    def test_error_handling_and_fallback(self):
        """测试错误处理和降级机制"""
        # 创建包含无效regex的规则管理器
        invalid_yaml_content = """
categories:
  - name: "错误测试"
    rule_groups:
      - name: "无效regex"
        principles:
          - content: "无效正则表达式测试"
            match_regex: "[unclosed"
            suggestion: "测试无效regex"
            pos_examples:
              - code: "test();"
                think: "测试"
        """
        
        invalid_yaml_file = os.path.join(self.temp_dir, "invalid_rules.yaml")
        with open(invalid_yaml_file, 'w', encoding='utf-8') as f:
            f.write(invalid_yaml_content)
        
        try:
            rule_manager = RuleManager(
                rule_file_path=invalid_yaml_file,
                enable_regex_matching=True
            )
            
            code_element = Mock(spec=CodeElement)
            code_element.name = "test"
            code_element.type = "function"
            code_element.filepath = "test.c"
            code_element.code = "test();"
            
            # 应该能够处理无效regex而不崩溃
            try:
                applicable_rules = rule_manager.get_applicable_rules_with_regex(code_element)
                # 如果没有抛出异常，验证返回了合理的结果
                self.assertIsInstance(applicable_rules, dict)
            except Exception:
                # 如果抛出异常，应该能够降级到传统方法
                traditional_rules = rule_manager.get_applicable_rules(code_element)
                self.assertIsInstance(traditional_rules, dict)
                
        finally:
            if os.path.exists(invalid_yaml_file):
                os.remove(invalid_yaml_file)
    
    def test_regex_matching_stats(self):
        """测试regex匹配统计信息"""
        rule_manager = RuleManager(
            rule_file_path=self.test_yaml_file,
            enable_regex_matching=True
        )
        
        # 加载规则以初始化统计
        rules = rule_manager.load_rules()
        
        # 获取统计信息
        stats = rule_manager.get_regex_matching_stats()
        
        # 验证统计信息
        self.assertTrue(stats["enabled"])
        self.assertEqual(stats["total_rules"], len(rules))
        self.assertGreater(stats["rules_with_regex"], 0)
        self.assertGreater(stats["regex_coverage"], 0)
        self.assertLessEqual(stats["regex_coverage"], 1.0)
        
        # 验证缓存统计
        self.assertIn("cache_stats", stats)
        cache_stats = stats["cache_stats"]
        self.assertIn("cache_size", cache_stats)
        self.assertIn("max_cache_size", cache_stats)


if __name__ == '__main__':
    unittest.main()
