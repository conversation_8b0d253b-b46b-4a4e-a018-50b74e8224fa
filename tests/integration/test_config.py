"""
集成测试配置文件

用于管理MR检测、结果提交、重复检测和排重测试的参数
"""

# 测试MR配置
from gate_keeper.config import config
from gate_keeper.domain.value_objects.analysis_result import (AnalyzeLLMResult,
                                                              ViolationItem)

TEST_MR_CONFIG = {
    "mr_id": config.mr_id,
    "project_id":config.repo_project_id,
    "base_branch": config.baseBranch,
    "dev_branch": config.devBranch,
}

# 评论排重测试配置
COMMENT_DEDUPLICATION_CONFIG = {
    "similarity_threshold": 0.8,  # 相似度阈值
    "max_retries": 3,  # 最大重试次数
    "retry_delay": 1.0,  # 重试延时（秒）
    "api_rate_limit_delay": 0.5,  # API限流延时（秒）
}

# 测试数据配置
TEST_DATA_CONFIG = {
    "test_comments": [
        {
            "title": "代码无问题情况",
            "llm_results": [
                AnalyzeLLMResult(
                    is_pass=True,
                    reason="代码检查通过，函数命名规范符合要求",
                    violations=[]
                ),
                AnalyzeLLMResult(
                    is_pass=True,
                    reason="代码结构检查通过，注释充分",
                    violations=[]
                )
            ],
            "expected_report": "",
            "expected_deduplication": False  # 无问题时不应该生成报告
        },
        {
            "title": "代码有问题情况",
            "llm_results": [
                AnalyzeLLMResult(
                    is_pass=False,
                    reason="检测到代码风格问题",
                    violations=[
                        ViolationItem(
                            rule_id="STYLE001",
                            severity="warning",
                            rule_content="函数命名应使用小写字母和下划线",
                            location={"file_path": "src/main.py", "line": 10},
                            message="函数名使用了驼峰命名法，应改为下划线命名"
                        )
                    ]
                ),
                AnalyzeLLMResult(
                    is_pass=True,
                    reason="代码结构检查通过",
                    violations=[]
                )
            ],
            "expected_report": "## 代码规范检查报告",  # 有问题时应该生成详细报告
            "expected_deduplication": True  # 有问题时应该生成报告
        },
        {
            "title": "代码有严重问题情况",
            "llm_results": [
                AnalyzeLLMResult(
                    is_pass=False,
                    reason="检测到安全问题",
                    violations=[
                        ViolationItem(
                            rule_id="SECURITY001",
                            severity="high",
                            rule_content="禁止使用字符串拼接构造SQL查询",
                            location={"file_path": "src/db.py", "line": 25},
                            message="存在SQL注入风险"
                        )
                    ]
                ),
                AnalyzeLLMResult(
                    is_pass=False,
                    reason="检测到性能问题",
                    violations=[
                        ViolationItem(
                            rule_id="PERFORMANCE001",
                            severity="medium",
                            rule_content="数据库连接必须正确关闭",
                            location={"file_path": "src/db.py", "line": 42},
                            message="数据库连接未在finally块中关闭"
                        )
                    ]
                )
            ],
            "expected_report": "## 代码规范检查报告",  # 有问题时应该生成详细报告
            "expected_deduplication": False  # 不同问题不应该被排重
        }
    ],
    
    "batch_test_llm_results": [
        # 第一个：有问题
        [
            AnalyzeLLMResult(
                is_pass=False,
                reason="检测到代码风格问题",
                violations=[
                    ViolationItem(
                        rule_id="STYLE001",
                        severity="warning",
                        rule_content="函数命名规范",
                        location={"file_path": "test1.py", "line": 10},
                        message="函数命名不规范"
                    )
                ]
            )
        ],
        # 第二个：无问题
        [
            AnalyzeLLMResult(
                is_pass=True,
                reason="代码检查通过",
                violations=[]
            )
        ],
        # 第三个：有问题（与第一个相同）
        [
            AnalyzeLLMResult(
                is_pass=False,
                reason="检测到代码风格问题",
                violations=[
                    ViolationItem(
                        rule_id="STYLE001",
                        severity="warning",
                        rule_content="函数命名规范",
                        location={"file_path": "test1.py", "line": 10},
                        message="函数命名不规范"
                    )
                ]
            )
        ],
        # 第四个：有不同问题
        [
            AnalyzeLLMResult(
                is_pass=False,
                reason="检测到安全问题",
                violations=[
                    ViolationItem(
                        rule_id="SECURITY001",
                        severity="high",
                        rule_content="安全规范",
                        location={"file_path": "test2.py", "line": 15},
                        message="存在安全风险"
                    )
                ]
            )
        ],
        # 第五个：无问题
        [
            AnalyzeLLMResult(
                is_pass=True,
                reason="代码检查通过",
                violations=[]
            )
        ]
    ]
}

# 性能测试配置
PERFORMANCE_CONFIG = {
    "max_response_time": 5.0,  # 最大响应时间（秒）
    "min_rate_limit_delay": 1.0,  # 最小限流延时（秒）
    "batch_size": 100,  # 批量操作大小
}

# 错误处理测试配置
ERROR_HANDLING_CONFIG = {
    "invalid_mr_id": 99999,  # 无效MR ID
    "network_error_timeout": 10,  # 网络错误超时（秒）
    "api_error_retries": 3,  # API错误重试次数
}

# 测试标记配置
TEST_MARKS = {
    "integration": "集成测试",
    "gitee": "Gitee平台测试",
    "deduplication": "评论排重测试",
    "performance": "性能测试",
    "error_handling": "错误处理测试",
}

# 测试环境配置
ENVIRONMENT_CONFIG = {
    "cleanup_enabled": True,  # 是否启用测试清理
    "debug_mode": False,  # 调试模式
    "verbose_output": True,  # 详细输出
    "save_test_results": True,  # 保存测试结果
}

# 测试结果验证配置
VALIDATION_CONFIG = {
    "required_mr_fields": ['id', 'number', 'title', 'state', 'head', 'base'],
    "required_comment_fields": ['id', 'body', 'user', 'created_at'],
    "expected_unique_comments": 3,  # 批量测试期望的唯一评论数
} 