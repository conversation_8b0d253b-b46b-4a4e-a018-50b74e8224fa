"""
Test Context Size Filtering Workflow Integration

测试上下文大小过滤在整个工作流程中的集成表现
"""

import os
import shutil
import tempfile
import unittest
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.service_orchestrator import \
    ServiceOrchestrator
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.external.code_analyzer import StaticAnalyzer
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestContextSizeFilteringWorkflow(unittest.TestCase):
    """测试上下文大小过滤工作流程集成"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.repo_path = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_path)
        
        # 创建模拟的Git服务
        self.mock_git_service = Mock()
        
        # 创建模拟的LLM客户端
        self.mock_llm_client = Mock()
        
        # 创建服务配置
        from gate_keeper.application.service.service_orchestrator_v2 import \
            ServiceConfig
        config = ServiceConfig(
            git_token="test_token",
            git_platform="gitee",
            llm_endpoint="http://localhost:11434",
            llm_model="qwen2.5:7b",
            use_static_analysis=True,
            use_optimized_context=True,
            max_workers=5,
            max_context_chain_depth=3,
            max_context_chains=2,  # 设置为较小的值用于测试
            max_context_size=1500  # 设置为较小的值用于测试
        )
        
        # 创建服务编排器
        self.orchestrator = ServiceOrchestrator(config)
        
        # 创建UseCase
        self.usecase = AnalyzeMRAndReportUsecase(self.orchestrator)
        
        # 创建模拟的静态分析器
        self.mock_static_analyzer = Mock()
        self.mock_static_analyzer.repo_index = Mock()
        self.mock_static_analyzer.repo_index.function_definitions = {}
        self.mock_static_analyzer.repo_index.function_calls = []
        
        # 设置静态分析器到编排器
        self.orchestrator.set_static_analyzer(self.mock_static_analyzer)
        
        # 注意：上下文大小限制现在通过ServiceConfig管理，已在上面设置
        
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_files(self):
        """创建测试文件"""
        # 创建小函数文件
        small_func_content = """
def small_function():
    return "small"
"""
        with open(os.path.join(self.repo_path, "small.py"), "w") as f:
            f.write(small_func_content)
        
        # 创建中等函数文件
        medium_func_content = """
def medium_function():
    result = []
    for i in range(50):
        result.append(i * 2)
    return result
"""
        with open(os.path.join(self.repo_path, "medium.py"), "w") as f:
            f.write(medium_func_content)
        
        # 创建大函数文件
        large_func_content = """
def large_function():
    result = []
    for i in range(200):
        result.append(i * 3)
        if i % 10 == 0:
            result.append(i * 4)
        if i % 20 == 0:
            result.append(i * 5)
    return result
"""
        with open(os.path.join(self.repo_path, "large.py"), "w") as f:
            f.write(large_func_content)
        
        # 创建超大函数文件
        huge_func_content = """
def huge_function():
    result = []
    for i in range(500):
        result.append(i * 2)
        if i % 5 == 0:
            result.append(i * 3)
        if i % 10 == 0:
            result.append(i * 4)
        if i % 20 == 0:
            result.append(i * 5)
        if i % 50 == 0:
            result.append(i * 6)
    return result
"""
        with open(os.path.join(self.repo_path, "huge.py"), "w") as f:
            f.write(huge_func_content)
    
    def setup_mock_static_analyzer(self):
        """设置模拟的静态分析器"""
        # 创建函数定义
        small_func = Function.create_simple(
            name="small_function",
            start_line=1,
            end_line=3,
            filepath="small.py",
            signature=FunctionSignature(name="small_function", parameters=[]),
            code="def small_function():\n    return \"small\""
        )
        
        medium_func = Function.create_simple(
            name="medium_function",
            start_line=1,
            end_line=6,
            filepath="medium.py",
            signature=FunctionSignature(name="medium_function", parameters=[]),
            code="def medium_function():\n    result = []\n    for i in range(50):\n        result.append(i * 2)\n    return result"
        )
        
        large_func = Function.create_simple(
            name="large_function",
            start_line=1,
            end_line=15,
            filepath="large.py",
            signature=FunctionSignature(name="large_function", parameters=[]),
            code="def large_function():\n    result = []\n    for i in range(200):\n        result.append(i * 3)\n        if i % 10 == 0:\n            result.append(i * 4)\n        if i % 20 == 0:\n            result.append(i * 5)\n    return result"
        )
        
        huge_func = Function.create_simple(
            name="huge_function",
            start_line=1,
            end_line=25,
            filepath="huge.py",
            signature=FunctionSignature(name="huge_function", parameters=[]),
            code="def huge_function():\n    result = []\n    for i in range(500):\n        result.append(i * 2)\n        if i % 5 == 0:\n            result.append(i * 3)\n        if i % 10 == 0:\n            result.append(i * 4)\n        if i % 20 == 0:\n            result.append(i * 5)\n        if i % 50 == 0:\n            result.append(i * 6)\n    return result"
        )
        
        # 设置函数定义映射
        self.orchestrator.static_analyzer.repo_index.function_definitions = {
            "small_function": [small_func],
            "medium_function": [medium_func],
            "large_function": [large_func],
            "huge_function": [huge_func]
        }
        
        # 模拟调用链
        self.orchestrator.static_analyzer.get_bidirectional_call_chains.return_value = [
            ["main", "test_function", "small_function"],
            ["init", "test_function", "medium_function"],
            ["process", "test_function", "large_function"],
            ["analyze", "test_function", "huge_function"]
        ]
    
    def test_context_size_filtering_in_complete_workflow(self):
        """测试完整工作流程中的上下文大小过滤"""
        # 创建测试文件
        self.create_test_files()
        
        # 设置模拟的静态分析器
        self.setup_mock_static_analyzer()
        
        # 创建模拟的变更函数
        test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        # 直接测试上下文大小过滤功能
        selected_contexts = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        
        # 验证选择的上下文数量合理
        self.assertLessEqual(len(selected_contexts), 2)
        
        # 验证总大小不超过限制
        if selected_contexts:
            total_size = sum(ctx.context_size for ctx in selected_contexts)
            self.assertLessEqual(total_size, 1500)
            
            # 验证选择了最优的上下文组合
            for context in selected_contexts:
                self.assertGreater(context.relevance_score, 0)
                self.assertLessEqual(context.context_size, 1500)
    
    def test_context_size_filtering_with_extremely_large_contexts(self):
        """测试包含极大上下文的过滤"""
        # 创建测试文件
        self.create_test_files()
        
        # 设置模拟的静态分析器，只包含大函数
        huge_func = Function.create_simple(
            name="huge_function",
            start_line=1,
            end_line=1000,
            filepath="huge.py",
            signature=FunctionSignature(name="huge_function", parameters=[]),
            code="def huge_function():\n    " + "\n    ".join([f"x = {i}" for i in range(1000)])
        )
        
        self.orchestrator.static_analyzer.repo_index.function_definitions = {
            "huge_function": [huge_func]
        }
        
        # 模拟调用链
        self.orchestrator.static_analyzer.get_bidirectional_call_chains.return_value = [
            ["main", "test_function", "huge_function"]
        ]
        
        # 创建测试函数
        test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        # 测试上下文生成
        contexts = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        
        # 验证极大上下文被过滤掉
        self.assertEqual(len(contexts), 0)
    
    def test_context_size_filtering_performance_in_workflow(self):
        """测试工作流程中的性能表现"""
        # 创建大量测试文件
        for i in range(20):
            func_content = f"""
def func_{i}():
    result = []
    for j in range({10 + i * 5}):
        result.append(j * {i + 1})
    return result
"""
            with open(os.path.join(self.repo_path, f"func_{i}.py"), "w") as f:
                f.write(func_content)
        
        # 设置模拟的静态分析器
        func_definitions = {}
        for i in range(20):
            func = Function.create_simple(
                name=f"func_{i}",
                start_line=1,
                end_line=10 + i * 2,
                filepath=f"func_{i}.py",
                signature=FunctionSignature(name=f"func_{i}", parameters=[]),
                code=f"def func_{i}():\n    result = []\n    for j in range({10 + i * 5}):\n        result.append(j * {i + 1})\n    return result"
            )
            func_definitions[f"func_{i}"] = [func]
        
        self.orchestrator.static_analyzer.repo_index.function_definitions = func_definitions
        
        # 创建大量调用链
        large_chains = []
        for i in range(30):
            chain = [f"func_{j}" for j in range(i + 1)]
            large_chains.append(chain)
        
        self.orchestrator.static_analyzer.get_bidirectional_call_chains.return_value = large_chains
        
        # 创建测试函数
        test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        # 测试性能
        import time
        start_time = time.time()
        
        contexts = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能
        self.assertLess(execution_time, 5.0)  # 5秒内完成
        
        # 验证结果
        self.assertLessEqual(len(contexts), 2)
        if contexts:
            total_size = sum(ctx.context_size for ctx in contexts)
            self.assertLessEqual(total_size, 1500)
    
    def test_context_size_filtering_with_different_strategies_in_workflow(self):
        """测试工作流程中不同策略的效果"""
        # 创建测试文件
        self.create_test_files()
        
        # 设置模拟的静态分析器
        self.setup_mock_static_analyzer()
        
        # 创建测试函数
        test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        results = {}
        
        # 测试相关性优先策略
        self.orchestrator.context_manager.config.context_selection_strategy = "relevance_first"
        contexts_relevance = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        results["relevance_first"] = {
            "count": len(contexts_relevance),
            "total_size": sum(ctx.context_size for ctx in contexts_relevance) if contexts_relevance else 0
        }
        
        # 测试大小优先策略
        self.orchestrator.context_manager.config.context_selection_strategy = "size_first"
        contexts_size = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        results["size_first"] = {
            "count": len(contexts_size),
            "total_size": sum(ctx.context_size for ctx in contexts_size) if contexts_size else 0
        }
        
        # 测试平衡策略
        self.orchestrator.context_manager.config.context_selection_strategy = "balanced"
        contexts_balanced = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        results["balanced"] = {
            "count": len(contexts_balanced),
            "total_size": sum(ctx.context_size for ctx in contexts_balanced) if contexts_balanced else 0
        }
        
        # 验证所有策略都遵守大小限制
        for strategy, result in results.items():
            self.assertLessEqual(result["total_size"], 1500)
            self.assertGreaterEqual(result["count"], 0)
    
    def test_context_size_filtering_logging_in_workflow(self):
        """测试工作流程中的日志记录"""
        # 创建测试文件
        self.create_test_files()
        
        # 设置模拟的静态分析器
        self.setup_mock_static_analyzer()
        
        # 启用详细日志
        self.orchestrator.context_manager.config.log_context_building = True
        
        # 创建测试函数
        test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        # 使用patch捕获日志
        with patch('gate_keeper.application.service.context_management.context_selector.logger') as mock_logger:
            contexts = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
            
            # 验证日志被调用
            mock_logger.info.assert_called()
            
            # 验证选择了正确的上下文数量
            self.assertGreater(len(contexts), 0)
            self.assertLessEqual(len(contexts), 2)
    
    def test_context_size_filtering_edge_cases_in_workflow(self):
        """测试工作流程中的边界情况"""
        # 创建测试文件
        self.create_test_files()
        
        # 设置模拟的静态分析器，包含各种边界情况
        empty_func = Function.create_simple(
            name="empty_function",
            start_line=1,
            end_line=1,
            filepath="empty.py",
            signature=FunctionSignature(name="empty_function", parameters=[]),
            code=""
        )
        
        tiny_func = Function.create_simple(
            name="tiny_function",
            start_line=1,
            end_line=2,
            filepath="tiny.py",
            signature=FunctionSignature(name="tiny_function", parameters=[]),
            code="def tiny_function():\n    pass"
        )
        
        self.orchestrator.static_analyzer.repo_index.function_definitions = {
            "empty_function": [empty_func],
            "tiny_function": [tiny_func]
        }
        
        # 模拟调用链
        self.orchestrator.static_analyzer.get_bidirectional_call_chains.return_value = [
            ["main", "test_function", "empty_function"],
            ["init", "test_function", "tiny_function"]
        ]
        
        # 创建测试函数
        test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        # 测试上下文生成
        contexts = self.orchestrator.context_manager.generate_optimized_contexts(test_af)
        
        # 验证边界情况处理
        self.assertGreaterEqual(len(contexts), 0)
        self.assertLessEqual(len(contexts), 2)
        
        # 验证空函数和极小函数也能正确处理
        for context in contexts:
            self.assertGreaterEqual(context.relevance_score, 0)
            self.assertLessEqual(context.context_size, 1500)


if __name__ == '__main__':
    unittest.main()