# UseCase集成测试指南

## 概述

本指南介绍如何使用UseCase全量集成测试，这些测试使用真实配置和真实代码，不进行mock，用于验证整个系统的端到端功能。

## 测试文件说明

### 1. 全量集成测试
- **文件**: `test_usecase_full_integration.py`
- **用途**: 完整的UseCase功能测试，包括所有UseCase类型
- **特点**: 使用完整配置，测试所有功能点
- **执行时间**: 较长（可能需要几分钟到十几分钟）

### 2. 快速集成测试
- **文件**: `test_usecase_quick_integration.py`
- **用途**: 快速验证基本功能
- **特点**: 使用最小配置，快速执行
- **执行时间**: 较短（通常1-3分钟）

### 3. 测试运行器
- **文件**: `run_usecase_full_integration_test.py`
- **用途**: 提供便捷的测试执行方式
- **特点**: 包含环境检查、错误处理、结果验证

## 测试覆盖范围

### 全量集成测试覆盖
1. **AnalyzeMRUseCase基本功能**
   - 基本MR分析
   - 使用commit SHA的分析
   - 结果结构验证
   - 结果内容验证

2. **AnalyzeMRAndReportUsecase**
   - MR分析并生成报告
   - 报告内容验证
   - 违规项排重

3. **AnalyzeBranchUseCase**
   - 分支分析
   - 使用commit SHA的分支分析
   - 分支分析特定验证

4. **错误处理测试**
   - 无效MR ID处理
   - 无效分支名处理
   - 异常恢复能力

5. **性能测试**
   - 执行时间监控
   - 性能基准验证

### 快速集成测试覆盖
1. **最小配置测试**
   - 使用最小参数执行MR分析
   - 基本结果验证

2. **超时处理测试**
   - 执行时间监控
   - 超时限制验证

3. **错误恢复测试**
   - 无效参数处理
   - 异常恢复验证

## 环境要求

### 必要配置
确保以下配置项已正确设置（在`config_test.py`中）：

```python
# 基本配置
repo_dir = "/path/to/test/project"  # 测试项目路径
mr_id = 123  # 测试MR ID
repo_project_id = "project_id"  # 项目ID
baseBranch = "main"  # 基础分支
devBranch = "dev"  # 开发分支
token = "your_token"  # 访问令牌
rule_file_path = "resource/编码规范_python.md"  # 规则文件路径

# LLM配置
ollama_endpoint = "http://127.0.0.1:11434"  # Ollama服务地址
llm_model_id = "qwen2.5-coder:3b"  # 模型ID
llm_concurrent = 1  # 并发数
max_llm_calls_per_check_mr_result = 2  # 最大调用次数
```

### 外部服务要求
1. **Ollama服务**: 确保Ollama服务正在运行
2. **Git仓库**: 确保测试项目路径存在且可访问
3. **网络连接**: 确保可以访问Git平台API

## 运行方式

### 方式1: 使用测试运行器（推荐）

#### 运行所有测试
```bash
cd /path/to/git_keeper
python tests/integration/run_usecase_full_integration_test.py
```

#### 运行特定测试方法
```bash
# 列出所有可用测试方法
python tests/integration/run_usecase_full_integration_test.py --list-methods

# 运行特定测试方法
python tests/integration/run_usecase_full_integration_test.py --test-method test_analyze_mr_usecase_basic
```

### 方式2: 使用pytest直接运行

#### 运行全量测试
```bash
cd /path/to/git_keeper
python -m pytest tests/integration/test_usecase_full_integration.py -v -s
```

#### 运行快速测试
```bash
cd /path/to/git_keeper
python -m pytest tests/integration/test_usecase_quick_integration.py -v -s
```

#### 运行特定测试方法
```bash
# 运行特定测试方法
python -m pytest tests/integration/test_usecase_full_integration.py::TestUseCaseFullIntegration::test_analyze_mr_usecase_basic -v -s

# 运行快速测试的特定方法
python -m pytest tests/integration/test_usecase_quick_integration.py::TestUseCaseQuickIntegration::test_analyze_mr_usecase_minimal -v -s
```

### 方式3: 直接运行测试文件

```bash
cd /path/to/git_keeper
python tests/integration/test_usecase_full_integration.py
python tests/integration/test_usecase_quick_integration.py
```

## 测试结果验证

### 成功标准
1. **所有测试方法通过**: 没有断言失败
2. **服务初始化成功**: Git服务、LLM服务、仓库分析器正常初始化
3. **结果结构正确**: 返回的结果包含所有必要字段
4. **执行时间合理**: 在预期的时间范围内完成

### 常见问题排查

#### 1. 配置问题
```
错误: 缺少必要的测试配置
解决: 检查config_test.py中的配置项是否正确设置
```

#### 2. 服务连接问题
```
错误: 无法连接到Ollama服务
解决: 确保Ollama服务正在运行，检查endpoint配置
```

#### 3. 项目路径问题
```
错误: 测试项目路径不存在
解决: 确保测试项目路径正确，项目可访问
```

#### 4. 权限问题
```
错误: 访问令牌无效
解决: 检查token配置，确保有足够的权限
```

## 性能基准

### 快速测试性能基准
- **执行时间**: 1-3分钟
- **内存使用**: 合理范围内
- **网络请求**: 最小化

### 全量测试性能基准
- **执行时间**: 5-15分钟
- **内存使用**: 合理范围内
- **网络请求**: 根据配置的并发数和调用次数

## 测试数据管理

### 临时文件
- 测试过程中会创建临时输出目录
- 测试完成后会自动清理
- 临时目录路径会记录在日志中

### 缓存管理
- 仓库索引缓存会自动管理
- 规则分组缓存会自动管理
- 测试完成后缓存会保留（用于后续测试）

## 扩展测试

### 添加新的测试方法
1. 在测试类中添加新的测试方法
2. 遵循命名规范：`test_<功能名>`
3. 添加适当的断言和验证
4. 更新测试运行器中的方法列表

### 自定义测试配置
1. 修改`config_test.py`中的配置
2. 在测试方法中使用自定义参数
3. 确保配置的合理性

## 注意事项

1. **真实环境**: 这些测试使用真实配置和真实代码，会消耗实际的API配额
2. **执行时间**: 全量测试可能需要较长时间，请耐心等待
3. **网络依赖**: 测试需要网络连接，确保网络稳定
4. **资源消耗**: 测试会消耗CPU和内存资源，确保系统资源充足
5. **数据安全**: 测试会访问真实的Git仓库，确保测试数据的安全性

## 故障排除

### 日志查看
测试过程中会输出详细的日志信息，包括：
- 服务初始化状态
- 执行进度
- 错误详情
- 性能统计

### 调试模式
可以通过修改日志级别来获取更详细的信息：
```python
# 在测试开始前设置
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

### 手动验证
如果测试失败，可以手动验证各个组件：
1. 验证Git服务连接
2. 验证LLM服务连接
3. 验证仓库分析器功能
4. 验证UseCase执行流程 