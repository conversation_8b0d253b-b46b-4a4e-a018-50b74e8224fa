import json
import os
import subprocess
# 导入统一的测试配置
import sys

import pytest

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_config import TEST_MR_CONFIG


def skip_test_external_project_mr():
    # 使用统一的测试配置
    repo_dir = "test_external_projects/git_keeper"
    mr_id = TEST_MR_CONFIG["mr_id"]  # 使用配置中的MR ID
    base_branch = TEST_MR_CONFIG["base_branch"]
    dev_branch = TEST_MR_CONFIG["dev_branch"]
    output_file = "mr_check_result.json"
    rule_file_path="resource/编码规范_python.md"
    cmd = [
        "python", "gate_keeper/interfaces/cli/main.py",
        "--repo-dir", repo_dir,
        "--mr-id", str(mr_id),
        "--base-branch", base_branch,
        "--dev-branch", dev_branch,
        "--output-file-name", output_file,
        "--dest-dir", repo_dir,
        "--rule-file-path", rule_file_path
    ]

    env = os.environ.copy()
    env["PYTHONPATH"] = os.getcwd()

    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    print("STDOUT:\n", result.stdout)
    print("STDERR:\n", result.stderr)
    assert result.returncode == 0, "CLI 执行失败"

    output_path = os.path.join(repo_dir, output_file)
    assert os.path.exists(output_path), "未生成输出报告"

    with open(output_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # 关键：断言有检查结果
    # 1. 顶层是 dict，且有 diffs
    assert isinstance(data, dict), "输出不是 dict"
    assert "diffs" in data, "报告中没有 diffs 字段"
    assert isinstance(data["diffs"], list) and len(data["diffs"]) > 0, "diffs 为空"

    # 2. 每个 diff 至少有 affected_functions
    for diff in data["diffs"]:
        assert "affected_functions" in diff, "diff 缺少 affected_functions"
        # 3. 每个 affected_function 至少有 check_results 或类似字段
        for func in diff["affected_functions"]:
            assert "check_results" in func, "函数缺少 check_results"
            assert isinstance(func["check_results"], list), "check_results 不是列表"
            # 4. 至少有一条检查结果
            assert len(func["check_results"]) > 0, "没有任何检查结果"