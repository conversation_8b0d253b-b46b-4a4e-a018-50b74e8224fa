#!/usr/bin/env python3
"""
UseCase全量集成测试运行脚本

用于执行usecase全量集成测试，包括环境检查和结果验证
"""

import os
import subprocess
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.config import config
from gate_keeper.shared.log import app_logger as logger


def check_test_environment():
    """检查测试环境"""
    logger.info("检查测试环境...")
    
    # 检查必要的配置
    required_configs = {
        'repo_dir': '测试项目路径',
        'mr_id': 'MR ID',
        'repo_project_id': '项目ID',
        'baseBranch': '基础分支',
        'devBranch': '开发分支',
        'token': '访问令牌',
        'rule_file_path': '规则文件路径'
    }
    
    missing_configs = []
    for config_name, description in required_configs.items():
        config_value = getattr(config, config_name, None)
        if config_value is None or config_value == "not_set":
            missing_configs.append(f"{description}({config_name})")
    
    if missing_configs:
        logger.error(f"缺少必要的测试配置: {', '.join(missing_configs)}")
        return False
    
    # 检查测试项目路径
    if not os.path.exists(config.repo_dir):
        logger.error(f"测试项目路径不存在: {config.repo_dir}")
        return False
    
    # 检查规则文件
    if not os.path.exists(config.rule_file_path):
        logger.error(f"规则文件不存在: {config.rule_file_path}")
        return False
    
    # 检查Ollama服务
    try:
        import requests
        ollama_endpoint = getattr(config, 'ollama_endpoint', 'http://127.0.0.1:11434')
        response = requests.get(f"{ollama_endpoint}/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("Ollama服务连接正常")
        else:
            logger.warning(f"Ollama服务响应异常: {response.status_code}")
    except Exception as e:
        logger.warning(f"无法连接到Ollama服务: {e}")
        logger.warning("请确保Ollama服务正在运行")
    
    logger.info("测试环境检查完成")
    return True


def run_single_test(test_name, test_file):
    """运行单个测试"""
    logger.info(f"开始运行测试: {test_name}")
    
    start_time = time.time()
    
    try:
        # 使用pytest运行测试
        cmd = [
            sys.executable, "-m", "pytest", test_file,
            "-v", "-s", "--tb=short",
            "--log-cli-level=INFO"
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=project_root,
            env=dict(os.environ, PYTHONPATH=str(project_root))
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"测试 {test_name} 执行时间: {execution_time:.2f}秒")
        
        if result.returncode == 0:
            logger.info(f"测试 {test_name} 通过")
            return True, result.stdout
        else:
            logger.error(f"测试 {test_name} 失败")
            logger.error(f"错误输出: {result.stderr}")
            return False, result.stderr
            
    except Exception as e:
        logger.error(f"运行测试 {test_name} 时发生异常: {e}")
        return False, str(e)


def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行UseCase全量集成测试")
    
    # 检查环境
    if not check_test_environment():
        logger.error("测试环境检查失败，退出测试")
        return False
    
    # 测试文件路径
    test_file = "tests/integration/test_usecase_full_integration.py"
    
    if not os.path.exists(test_file):
        logger.error(f"测试文件不存在: {test_file}")
        return False
    
    # 运行测试
    success, output = run_single_test("UseCase全量集成测试", test_file)
    
    if success:
        logger.info("所有测试通过！")
        return True
    else:
        logger.error("测试失败！")
        return False


def run_specific_test_method(test_method):
    """运行特定的测试方法"""
    logger.info(f"开始运行特定测试方法: {test_method}")
    
    # 检查环境
    if not check_test_environment():
        logger.error("测试环境检查失败，退出测试")
        return False
    
    # 测试文件路径
    test_file = "tests/integration/test_usecase_full_integration.py"
    
    if not os.path.exists(test_file):
        logger.error(f"测试文件不存在: {test_file}")
        return False
    
    # 运行特定测试方法
    cmd = [
        sys.executable, "-m", "pytest", test_file,
        f"TestUseCaseFullIntegration::{test_method}",
        "-v", "-s", "--tb=short",
        "--log-cli-level=INFO"
    ]
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=project_root,
            env=dict(os.environ, PYTHONPATH=str(project_root))
        )
        
        if result.returncode == 0:
            logger.info(f"测试方法 {test_method} 通过")
            return True
        else:
            logger.error(f"测试方法 {test_method} 失败")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"运行测试方法 {test_method} 时发生异常: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="UseCase全量集成测试运行器")
    parser.add_argument(
        "--test-method", 
        type=str, 
        help="运行特定的测试方法"
    )
    parser.add_argument(
        "--list-methods", 
        action="store_true", 
        help="列出所有可用的测试方法"
    )
    
    args = parser.parse_args()
    
    if args.list_methods:
        # 列出所有测试方法
        test_methods = [
            "test_analyze_mr_usecase_basic",
            "test_analyze_mr_usecase_with_commit_sha",
            "test_analyze_mr_report_usecase",
            "test_analyze_branch_usecase",
            "test_analyze_branch_usecase_with_commit_sha",
            "test_usecase_error_handling",
            "test_usecase_performance"
        ]
        
        logger.info("可用的测试方法:")
        for method in test_methods:
            logger.info(f"  - {method}")
        return
    
    if args.test_method:
        # 运行特定测试方法
        success = run_specific_test_method(args.test_method)
        sys.exit(0 if success else 1)
    else:
        # 运行所有测试
        success = run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 