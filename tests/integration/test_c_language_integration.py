"""
C语言代码仓库集成测试

测试C语言代码仓库的完整分析流程，包括：
- C语言特有的语法结构（宏、结构体、指针、头文件等）
- 跨文件调用关系分析
- 宏定义和宏调用处理
- 头文件包含关系
- 条件编译处理
- 复杂C语言项目的代码图构建
"""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.external.code_analyzer import RepositoryIndex, StaticAnalyzer
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)
from gate_keeper.external.code_analyzer.models.macro import Macro
from gate_keeper.external.code_analyzer.parsers.c_parser import CParser


class TestCLanguageIntegration(unittest.TestCase):
    """C语言代码仓库集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模拟的git服务
        self.mock_git_service = Mock()
        self.mock_git_service.checkout_to_ref.return_value.__enter__ = Mock()
        self.mock_git_service.checkout_to_ref.return_value.__exit__ = Mock()
        
        # 创建测试C语言项目文件
        self.create_test_c_project()
        
        # 创建RepositoryAnalyzer实例
        self.repo_analyzer = RepositoryAnalyzer(self.mock_git_service)
        
        # 创建静态分析器（需要先获取索引）
        self.repo_index = self.repo_analyzer.get_index(self.temp_dir, "main")
        self.static_analyzer = StaticAnalyzer(self.repo_index)
        self.context_manager = ContextManager(
            static_analyzer=self.static_analyzer)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_c_project(self):
        """创建测试C语言项目"""
        # 主程序文件
        main_c = """
#include "utils.h"
#include "config.h"

#define DEBUG_MODE 1

#ifdef DEBUG_MODE
    #define LOG(msg) printf("DEBUG: %s\\n", msg)
#else
    #define LOG(msg)
#endif

int main(int argc, char* argv[]) {
    LOG("Starting application");
    
    if (argc < 2) {
        print_usage();
        return 1;
    }
    
    config_t config = load_config(argv[1]);
    if (!config.valid) {
        LOG("Invalid configuration");
        return 1;
    }
    
    process_data(config);
    LOG("Application finished");
    return 0;
}
"""
        
        # 工具函数头文件
        utils_h = """
#ifndef UTILS_H
#define UTILS_H

#include <stdio.h>
#include <stdlib.h>

typedef struct {
    int id;
    char* name;
    float value;
} data_item_t;

typedef struct {
    char* filename;
    int max_items;
    int valid;
} config_t;

void print_usage(void);
config_t load_config(const char* filename);
void process_data(config_t config);
data_item_t* parse_data_item(const char* line);

#endif
"""
        
        # 工具函数实现
        utils_c = """
#include "utils.h"
#include "config.h"

#define MAX_LINE_LENGTH 1024
#define DEFAULT_MAX_ITEMS 100

void print_usage(void) {
    printf("Usage: program <config_file>\\n");
}

config_t load_config(const char* filename) {
    config_t config = {0};
    config.filename = (char*)filename;
    config.max_items = DEFAULT_MAX_ITEMS;
    
    FILE* file = fopen(filename, "r");
    if (!file) {
        config.valid = 0;
        return config;
    }
    
    char line[MAX_LINE_LENGTH];
    while (fgets(line, sizeof(line), file)) {
        if (strncmp(line, "max_items=", 10) == 0) {
            config.max_items = atoi(line + 10);
        }
    }
    
    fclose(file);
    config.valid = 1;
    return config;
}

void process_data(config_t config) {
    printf("Processing data with config: %s\\n", config.filename);
    
    FILE* file = fopen(config.filename, "r");
    if (!file) {
        printf("Cannot open file: %s\\n", config.filename);
        return;
    }
    
    data_item_t* items = malloc(config.max_items * sizeof(data_item_t));
    int item_count = 0;
    
    char line[MAX_LINE_LENGTH];
    while (fgets(line, sizeof(line), file) && item_count < config.max_items) {
        items[item_count] = parse_data_item(line);
        item_count++;
    }
    
    fclose(file);
    free(items);
}

data_item_t parse_data_item(const char* line) {
    data_item_t item = {0};
    sscanf(line, "%d,%s,%f", &item.id, item.name, &item.value);
    return item;
}
"""
        
        # 配置文件
        config_h = """
#ifndef CONFIG_H
#define CONFIG_H

#define VERSION "1.0.0"
#define AUTHOR "Test Author"

#endif
"""
        
        # 写入文件
        with open(os.path.join(self.temp_dir, "main.c"), "w") as f:
            f.write(main_c)
        
        with open(os.path.join(self.temp_dir, "utils.h"), "w") as f:
            f.write(utils_h)
        
        with open(os.path.join(self.temp_dir, "utils.c"), "w") as f:
            f.write(utils_c)
        
        with open(os.path.join(self.temp_dir, "config.h"), "w") as f:
            f.write(config_h)
    
    def test_c_project_indexing(self):
        """测试C语言项目索引"""
        # 索引整个项目
        self.repo_index.build()
        
        # 验证函数定义
        self.assertIn("main", self.repo_index.function_definitions)
        self.assertIn("print_usage", self.repo_index.function_definitions)
        self.assertIn("load_config", self.repo_index.function_definitions)
        self.assertIn("process_data", self.repo_index.function_definitions)
        self.assertIn("parse_data_item", self.repo_index.function_definitions)
        
        # 验证宏定义
        macro_names = [m.name for m in self.repo_index.macro_definitions]
        self.assertIn("DEBUG_MODE", macro_names)
        self.assertIn("LOG", macro_names)
        self.assertIn("MAX_LINE_LENGTH", macro_names)
        self.assertIn("DEFAULT_MAX_ITEMS", macro_names)
        self.assertIn("VERSION", macro_names)
        self.assertIn("AUTHOR", macro_names)
        
        # 验证调用关系
        call_relations = [call for call in self.repo_index.function_calls 
                         if call.caller == "main"]
        self.assertGreater(len(call_relations), 0)
        
        # 验证main函数调用了其他函数
        called_functions = [call.callee for call in call_relations]
        self.assertIn("print_usage", called_functions)
        self.assertIn("load_config", called_functions)
        self.assertIn("process_data", called_functions)
    
    def test_c_macro_analysis(self):
        """测试C语言宏分析"""
        self.repo_index.build()
        
        # 验证对象宏
        debug_macro = next((m for m in self.repo_index.macro_definitions 
                           if m.name == "DEBUG_MODE" and m.type == "object"), None)
        self.assertIsNotNone(debug_macro)
        self.assertEqual(debug_macro.type, "object")
        self.assertEqual(debug_macro.value.strip(), "1")
        
        # 验证函数宏
        log_macro = next((m for m in self.repo_index.macro_definitions 
                         if m.name == "LOG" and m.type == "function"), None)
        self.assertIsNotNone(log_macro)
        self.assertEqual(log_macro.type, "function")
        self.assertIn("msg", log_macro.parameters)
        
        # 验证条件编译宏
        version_macro = next((m for m in self.repo_index.macro_definitions 
                             if m.name == "VERSION" and m.type == "object"), None)
        self.assertIsNotNone(version_macro)
        self.assertEqual(version_macro.type, "object")
        self.assertEqual(version_macro.value.strip(), '"1.0.0"')
    
    def test_c_cross_file_analysis(self):
        """测试C语言跨文件分析"""
        self.repo_index.build()
        
        # 验证跨文件调用关系
        cross_file_calls = []
        for call in self.repo_index.function_calls:
            caller_func = next((f for f in self.repo_index.function_definitions[call.caller] 
                               if f.name == call.caller), None)
            callee_func = next((f for f in self.repo_index.function_definitions[call.callee] 
                               if f.name == call.callee), None)
            
            if caller_func and callee_func and caller_func.filepath != callee_func.filepath:
                cross_file_calls.append(call)
        
        # 应该存在跨文件调用
        self.assertGreater(len(cross_file_calls), 0)
        
        # 验证main.c调用了utils.c中的函数
        main_calls = [call for call in cross_file_calls 
                     if any(f.filepath.endswith("main.c") 
                           for f in self.repo_index.function_definitions[call.caller])]
        self.assertGreater(len(main_calls), 0)
    
    def test_c_structure_analysis(self):
        """测试C语言结构体分析"""
        self.repo_index.build()
        
        # 验证结构体定义被识别
        # 注意：当前实现可能不直接支持结构体分析，这里测试相关函数
        config_funcs = self.repo_index.function_definitions.get("load_config", [])
        self.assertGreater(len(config_funcs), 0)
        
        # 验证结构体相关函数
        process_funcs = self.repo_index.function_definitions.get("process_data", [])
        self.assertGreater(len(process_funcs), 0)
    
    def test_c_pointer_analysis(self):
        """测试C语言指针分析"""
        self.repo_index.build()
        
        # 验证指针参数函数
        parse_funcs = self.repo_index.function_definitions.get("parse_data_item", [])
        self.assertGreater(len(parse_funcs), 0)
        
        # 验证函数被正确识别
        self.assertIsNotNone(parse_funcs[0])
        # 注意：当前CParser实现不提取函数参数，这里只验证函数存在
        # TODO: 改进CParser以支持参数提取
    
    def test_c_header_file_analysis(self):
        """测试C语言头文件分析"""
        self.repo_index.build()
        
        # 验证头文件中的函数声明
        # 头文件中的函数声明应该被正确识别
        header_functions = []
        for func_name, funcs in self.repo_index.function_definitions.items():
            for func in funcs:
                if func.filepath.endswith(".h"):
                    header_functions.append(func)
        
        # 验证头文件中的宏定义
        header_macros = [m for m in self.repo_index.macro_definitions 
                        if m.filepath.endswith(".h")]
        self.assertGreater(len(header_macros), 0)
    
    def test_c_conditional_compilation(self):
        """测试C语言条件编译分析"""
        self.repo_index.build()
        
        # 验证条件编译宏
        conditional_macros = [m for m in self.repo_index.macro_definitions 
                             if m.type == "conditional"]
        
        # 应该识别到条件编译相关的宏
        self.assertGreater(len(conditional_macros), 0)
    
    def test_c_context_generation(self):
        """测试C语言上下文生成"""
        self.repo_index.build()
        
        # 创建受影响的函数
        affected_func = AffectedFunction(
            name="main",
            type="function",
            start_line=1,
            end_line=30,
            changed_lines=[15, 16],
            code="int main(int argc, char* argv[]) { ... }",
            related_calls=[],
            related_definitions=[],
            filepath=os.path.join(self.temp_dir, "main.c")
        )
        
        # 生成上下文
        contexts = self.context_manager.generate_optimized_contexts(affected_func, max_chains=3)
        
        # 验证上下文生成
        self.assertGreater(len(contexts), 0)
        
        # 验证上下文包含相关函数
        for context in contexts:
            self.assertTrue(any("main" in node for node in context.chain))
            self.assertGreater(len(context.functions), 0)
    
    def test_c_complex_call_chains(self):
        """测试C语言复杂调用链分析"""
        self.repo_index.build()
        
        # 分析main函数的调用链
        main_chains = self.static_analyzer.get_bidirectional_call_chains(
            "main", "main.c", max_depth=3
        )
        
        # 验证调用链
        self.assertGreater(len(main_chains), 0)
        
        # 验证调用链包含main函数
        for chain in main_chains:
            self.assertTrue(any("main" in node for node in chain))
            # 注意：当前调用链分析可能只包含直接调用，不包含完整的调用链
            # TODO: 改进调用链分析以包含完整的调用关系
    
    def test_c_error_handling(self):
        """测试C语言错误处理"""
        # 测试语法错误的C代码
        invalid_c_code = """
int invalid_function( {
    return 1;
}
"""
        
        with open(os.path.join(self.temp_dir, "invalid.c"), "w") as f:
            f.write(invalid_c_code)
        
        # 应该能够处理语法错误而不崩溃
        try:
            self.repo_index.build()
            # 如果成功，验证错误文件没有被索引
            self.assertNotIn("invalid_function", self.repo_index.function_definitions)
        except Exception as e:
            # 如果抛出异常，应该是预期的
            self.assertIsInstance(e, Exception)
    
    def test_c_performance_with_large_project(self):
        """测试C语言大项目性能"""
        # 创建大量C文件来模拟大项目
        for i in range(10):
            file_content = f"""
#include "utils.h"

int function_{i}() {{
    return {i};
}}

void process_{i}() {{
    int result = function_{i}();
    printf("Processed: %d\\n", result);
}}
"""
            with open(os.path.join(self.temp_dir, f"module_{i}.c"), "w") as f:
                f.write(file_content)
        
        # 测试索引性能
        import time
        start_time = time.time()
        
        self.repo_index.build()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能合理（小于10秒）
        self.assertLess(execution_time, 10.0)
        
        # 验证所有函数都被索引
        expected_functions = [f"function_{i}" for i in range(10)] + [f"process_{i}" for i in range(10)]
        for func_name in expected_functions:
            self.assertIn(func_name, self.repo_index.function_definitions)


class TestCLanguageSpecificFeatures(unittest.TestCase):
    """测试C语言特有功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.parser = CParser()
    
    def test_c_function_pointers(self):
        """测试C语言函数指针"""
        code = """
typedef int (*operation_func)(int, int);

int add(int a, int b) {
    return a + b;
}

int multiply(int a, int b) {
    return a * b;
}

int calculate(int x, int y, operation_func op) {
    return op(x, y);
}

int main() {
    int result1 = calculate(5, 3, add);
    int result2 = calculate(5, 3, multiply);
    return 0;
}
"""
        
        # 创建临时文件
        import os
        import tempfile
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        try:
            functions = self.parser.extract_functions(temp_file)
        finally:
            os.unlink(temp_file)
        
        # 验证函数提取
        self.assertGreater(len(functions), 0)
        function_names = [f.name for f in functions]
        self.assertIn("add", function_names)
        self.assertIn("multiply", function_names)
        self.assertIn("calculate", function_names)
        self.assertIn("main", function_names)
    
    def test_c_typedef_analysis(self):
        """测试C语言typedef分析"""
        code = """
typedef struct {
    int x;
    int y;
} Point;

typedef int* IntPtr;
typedef char String[100];

Point create_point(int x, int y) {
    Point p = {x, y};
    return p;
}

void process_string(String str) {
    printf("%s\\n", str);
}
"""
        
        # 创建临时文件
        import os
        import tempfile
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        try:
            functions = self.parser.extract_functions(temp_file)
        finally:
            os.unlink(temp_file)
        
        # 验证typedef相关函数
        self.assertGreater(len(functions), 0)
        function_names = [f.name for f in functions]
        self.assertIn("create_point", function_names)
        self.assertIn("process_string", function_names)
    
    def test_c_preprocessor_directives(self):
        """测试C语言预处理器指令"""
        code = """
#define MAX_SIZE 100
#define MIN(a, b) ((a) < (b) ? (a) : (b))

#include <stdio.h>
#include <stdlib.h>

#ifdef DEBUG
    #define DEBUG_PRINT(msg) printf("DEBUG: %s\\n", msg)
#else
    #define DEBUG_PRINT(msg)
#endif

#ifndef VERSION
    #define VERSION "1.0"
#endif

int main() {
    DEBUG_PRINT("Starting");
    int max = MAX_SIZE;
    int min_val = MIN(10, 20);
    printf("Version: %s\\n", VERSION);
    return 0;
}
"""
        
        macros = self.parser.extract_macros("test.c", file_content=code)
        
        # 验证宏提取
        self.assertGreater(len(macros), 0)
        macro_names = [m.name for m in macros]
        self.assertIn("MAX_SIZE", macro_names)
        self.assertIn("MIN", macro_names)
        self.assertIn("DEBUG_PRINT", macro_names)
        self.assertIn("VERSION", macro_names)
    
    def test_c_complex_macros(self):
        """测试C语言复杂宏"""
        code = """
#define CONCAT(a, b) a##b
#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

#define FOREACH(i, list) for(int i = 0; i < sizeof(list)/sizeof(list[0]); i++)

#define ASSERT(condition) \\
    do { \\
        if (!(condition)) { \\
            printf("Assertion failed: %s\\n", #condition); \\
            exit(1); \\
        } \\
    } while(0)

int main() {
    int var1 = 10;
    int var2 = 20;
    int CONCAT(var, 1) = 30;  // 展开为 var1 = 30
    
    printf("File: %s\\n", TOSTRING(__FILE__));
    
    int numbers[] = {1, 2, 3, 4, 5};
    FOREACH(i, numbers) {
        printf("%d ", numbers[i]);
    }
    
    ASSERT(var1 > 0);
    return 0;
}
"""
        
        macros = self.parser.extract_macros("test.c", file_content=code)
        
        # 验证复杂宏提取
        self.assertGreater(len(macros), 0)
        macro_names = [m.name for m in macros]
        self.assertIn("CONCAT", macro_names)
        self.assertIn("STRINGIFY", macro_names)
        self.assertIn("TOSTRING", macro_names)
        self.assertIn("FOREACH", macro_names)
        self.assertIn("ASSERT", macro_names)

    def test_macro_integration_with_call_graph(self):
        """测试宏定义在调用图中的集成"""
        # 创建包含宏定义的C代码
        c_code_with_macros = '''
#include <stdio.h>

#define DEBUG_PRINT(msg) printf("DEBUG: %s\\n", msg)
#define MAX(a, b) ((a) > (b) ? (a) : (b))

void test_function() {
    DEBUG_PRINT("test message");
    int result = MAX(10, 20);
    printf("Result: %d\\n", result);
}

void another_function() {
    test_function();
    DEBUG_PRINT("another message");
}
'''
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
            f.write(c_code_with_macros)
            temp_file = f.name
        
        try:
            # 创建RepositoryIndex并构建
            repo_index = RepositoryIndex(
                repo_dir=os.path.dirname(temp_file),
                branch="main",
                commit_sha="test_commit"
            )
            repo_index.build()
            
            # 验证宏定义被提取
            self.assertGreater(len(repo_index.macro_definitions), 0)
            
            # 验证宏定义包含预期的宏
            macro_names = [macro.name for macro in repo_index.macro_definitions]
            self.assertIn("DEBUG_PRINT", macro_names)
            self.assertIn("MAX", macro_names)
            
            # 验证调用图包含宏节点
            call_graph = repo_index.get_call_graph()
            graph_nodes = list(call_graph.nodes())
            
            # 查找宏节点
            macro_nodes = [node for node in graph_nodes if ":macro:" in node]
            self.assertGreater(len(macro_nodes), 0)
            
            # 验证宏节点格式正确
            for macro_node in macro_nodes:
                self.assertIn(":macro:", macro_node)
                macro_name = macro_node.split(":macro:", 1)[1]
                self.assertIn(macro_name, macro_names)
            
            # 验证函数调用宏的关系
            function_nodes = [node for node in graph_nodes if ":function:" in node]
            test_function_node = f"{os.path.basename(temp_file)}:function:test_function"
            self.assertIn(test_function_node, function_nodes)
            
            # 验证test_function的后继节点包含宏
            successors = list(call_graph.successors(test_function_node))
            macro_successors = [node for node in successors if ":macro:" in node]
            self.assertGreater(len(macro_successors), 0)
            
            # 创建StaticAnalyzer实例
            static_analyzer = StaticAnalyzer(repo_index)
            
            # 获取test_function的相关元素
            related_elements = static_analyzer.get_related_elements("test_function", os.path.basename(temp_file))
            
            # 验证相关元素包含宏
            self.assertGreater(len(related_elements["macros"]), 0)
            related_macro_names = [macro.name for macro in related_elements["macros"]]
            self.assertIn("DEBUG_PRINT", related_macro_names)
            
        finally:
            # 清理临时文件
            os.unlink(temp_file)


if __name__ == '__main__':
    unittest.main() 