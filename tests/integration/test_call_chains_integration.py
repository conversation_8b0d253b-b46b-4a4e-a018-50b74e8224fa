"""
调用链统计集成测试

验证目标：
1. 从RepositoryAnalyzer到LLMService的完整调用链统计流程
2. 确保related_calls字段在整个流程中被正确填充和统计
3. 验证修复后的代码能正确工作
"""

import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.external.code_analyzer import RepositoryIndex, StaticAnalyzer
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.infrastructure.git.gitee.client import Gitee
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """模拟LLM客户端"""
    
    def __init__(self, base_url="http://mock-llm"):
        self.responses = []
        self.call_count = 0
        super().__init__(base_url=base_url)
        
    def chat_completion(self, messages, **kwargs):
        """模拟LLM响应"""
        self.call_count += 1
        if self.responses:
            return self.responses.pop(0)
        return '<FinalAnswer>{"is_pass": true, "reason": "测试通过", "violations": []}</FinalAnswer>'

    def generate(self, *args, **kwargs):
        return self.chat_completion(*args, **kwargs)

    def get_config(self):
        return {}


class TestCallChainsIntegration(unittest.TestCase):
    """调用链统计集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的GitService
        self.mock_git_service = Mock(spec=GitService)
        
        # 创建RepositoryAnalyzer
        self.repo_analyzer = RepositoryAnalyzer(self.mock_git_service)
        
        # 创建LLMService
        self.mock_llm_client = MockLLMClient()
        self.llm_service = LLMService(self.mock_llm_client)
        
        # 准备测试规则
        self.test_rules = [
            CodeCheckRule(
                id="R1",
                name="函数命名规范",
                description="函数名应使用小写字母和下划线",
                category=["命名规范"],
                enabled=True,
                languages=["python"]
            )
        ]
        
        # 模拟测试数据
        self.test_file_content = """
def main():
    result = process_data()
    return result

def process_data():
    data = get_data()
    return transform_data(data)

def get_data():
    return {'key': 'value'}

def transform_data(data):
    return data.get('key', 'default')
"""
        
        self.test_file_path = "test.py"
        self.test_changed_lines = [2, 3, 4]  # main函数的变更行
        
    def test_end_to_end_call_chains_statistics(self):
        """测试端到端的调用链统计流程"""
        # 创建模拟的RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)
        
        # 模拟get_changed_functions返回的AffectedFunction
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    result = process_data()\n    return result",
            filepath="test.py",
            related_calls=[],  # 初始为空
            related_definitions=[],
            call_chains=[]
        )
        
        mock_repo_index.get_changed_functions.return_value = [mock_af]
        
        # 模拟get_related_calls返回调用关系
        mock_call = FunctionCall(
            caller="main",
            callee="process_data",
            file_path="test.py",
            line=[3],
            code="process_data()"
        )
        mock_repo_index.get_related_calls.return_value = [mock_call]
        
        # 设置有效的LLM响应
        self.mock_llm_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, "get_index", return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[["main", "process_data"]]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):
            
            # 步骤1: 使用RepositoryAnalyzer分析函数
            affected_functions = self.repo_analyzer.analyze_functions(repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )
            
            # 验证RepositoryAnalyzer正确填充了related_calls
            self.assertEqual(len(affected_functions), 1)
            self.assertGreater(len(affected_functions[0].related_calls), 0)
            
            # 步骤2: 创建CheckMRResult
            diff_result = DiffResult(
                filepath=self.test_file_path,
                origin_file_content="",
                new_file_content="",
                affected_functions=affected_functions,
                file_status="modified"
            )
            
            check_mr_result = CheckMRResult(
                mr_id=1,
                base_branch="main",
                dev_branch="feature",
                diffs=[diff_result]
            )
            
            # 步骤3: 使用LLMService分析MR
            result = self.llm_service.analyze_mr(check_mr_result)
            
            # 验证调用链统计
            total_call_chains = sum(len(af.related_calls) for af in affected_functions)
            self.assertEqual(total_call_chains, 1)
            
            # 验证调用关系数据
            call_info = affected_functions[0].related_calls[0]
            self.assertIsInstance(call_info, FunctionCall)
            self.assertEqual(call_info.caller, "main")
            self.assertEqual(call_info.callee, "process_data")
            self.assertEqual(call_info.line, [3])
            
    def test_multiple_functions_call_chains_statistics(self):
        """测试多个函数的调用链统计"""
        # 创建模拟的RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)
        
        # 模拟多个AffectedFunction
        mock_af1 = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    result = process_data()\n    return result",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )
        
        mock_af2 = AffectedFunction(
            name="process_data",
            start_line=6,
            end_line=10,
            changed_lines=[7, 8],
            code="def process_data():\n    data = get_data()\n    return transform_data(data)",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )
        
        mock_repo_index.get_changed_functions.return_value = [mock_af1, mock_af2]
        
        # 模拟不同函数的调用关系
        def mock_get_related_calls(func_name):
            if func_name == "main":
                mock_call = FunctionCall(
                    caller="main",
                    callee="process_data",
                    file_path="test.py",
                    line=[3],
                    code="process_data()"
                )
                return [mock_call]
            elif func_name == "process_data":
                mock_call1 = FunctionCall(
                    caller="process_data",
                    callee="get_data",
                    file_path="test.py",
                    line=[7],
                    code="get_data()"
                )
                mock_call2 = FunctionCall(
                    caller="process_data",
                    callee="transform_data",
                    file_path="test.py",
                    line=[8],
                    code="transform_data(data)"
                )
                return [mock_call1, mock_call2]
            return []
        
        mock_repo_index.get_related_calls.side_effect = mock_get_related_calls
        
        # 设置有效的LLM响应
        self.mock_llm_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>',
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, "get_index", return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):
            
            # 步骤1: 使用RepositoryAnalyzer分析函数
            affected_functions = self.repo_analyzer.analyze_functions(repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )
            
            # 验证RepositoryAnalyzer正确填充了related_calls
            self.assertEqual(len(affected_functions), 2)
            self.assertEqual(len(affected_functions[0].related_calls), 1)  # main函数有1个调用
            self.assertEqual(len(affected_functions[1].related_calls), 2)  # process_data函数有2个调用
            
            # 步骤2: 创建CheckMRResult
            diff_result = DiffResult(
                filepath=self.test_file_path,
                origin_file_content="",
                new_file_content="",
                affected_functions=affected_functions,
                file_status="modified"
            )
            
            check_mr_result = CheckMRResult(
                mr_id=1,
                base_branch="main",
                dev_branch="feature",
                diffs=[diff_result]
            )
            
            # 步骤3: 使用LLMService分析MR
            result = self.llm_service.analyze_mr(check_mr_result)
            
            # 验证调用链统计
            total_call_chains = sum(len(af.related_calls) for af in affected_functions)
            self.assertEqual(total_call_chains, 3)  # 总共3个调用关系
            
            # 验证每个函数的调用关系
            main_calls = affected_functions[0].related_calls
            process_calls = affected_functions[1].related_calls
            
            self.assertIsInstance(main_calls[0], FunctionCall)
            self.assertEqual(main_calls[0].caller, "main")
            self.assertEqual(main_calls[0].callee, "process_data")
            
            self.assertIsInstance(process_calls[0], FunctionCall)
            self.assertEqual(process_calls[0].caller, "process_data")
            self.assertEqual(process_calls[0].callee, "get_data")
            self.assertIsInstance(process_calls[1], FunctionCall)
            self.assertEqual(process_calls[1].caller, "process_data")
            self.assertEqual(process_calls[1].callee, "transform_data")
            
    def test_empty_call_chains_handling(self):
        """测试没有调用关系时的处理"""
        # 创建模拟的RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)
        
        # 模拟AffectedFunction，没有调用关系
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )
        
        mock_repo_index.get_changed_functions.return_value = [mock_af]
        mock_repo_index.get_related_calls.return_value = []  # 没有调用关系
        
        # 设置有效的LLM响应
        self.mock_llm_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, "get_index", return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):
            
            # 步骤1: 使用RepositoryAnalyzer分析函数
            affected_functions = self.repo_analyzer.analyze_functions(repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )
            
            # 验证related_calls为空
            self.assertEqual(len(affected_functions), 1)
            self.assertEqual(len(affected_functions[0].related_calls), 0)
            
            # 步骤2: 创建CheckMRResult
            diff_result = DiffResult(
                filepath=self.test_file_path,
                origin_file_content="",
                new_file_content="",
                affected_functions=affected_functions,
                file_status="modified"
            )
            
            check_mr_result = CheckMRResult(
                mr_id=1,
                base_branch="main",
                dev_branch="feature",
                diffs=[diff_result]
            )
            
            # 步骤3: 使用LLMService分析MR
            result = self.llm_service.analyze_mr(check_mr_result)
            
            # 验证调用链统计为0
            total_call_chains = sum(len(af.related_calls) for af in affected_functions)
            self.assertEqual(total_call_chains, 0)
            
    def test_call_chains_data_structure_validation(self):
        """测试调用链数据结构的验证"""
        # 创建模拟的RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)
        
        # 模拟AffectedFunction
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    result = process_data()\n    return result",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )
        
        mock_repo_index.get_changed_functions.return_value = [mock_af]
        
        # 模拟调用关系
        mock_call = FunctionCall(
            caller="main",
            callee="process_data",
            file_path="test.py",
            line=[3],
            code="process_data()"
        )
        mock_repo_index.get_related_calls.return_value = [mock_call]
        
        # 设置有效的LLM响应
        self.mock_llm_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, "get_index", return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):
            
            # 步骤1: 使用RepositoryAnalyzer分析函数
            affected_functions = self.repo_analyzer.analyze_functions(repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )
            
            # 步骤2: 创建CheckMRResult
            diff_result = DiffResult(
                filepath=self.test_file_path,
                origin_file_content="",
                new_file_content="",
                affected_functions=affected_functions,
                file_status="modified"
            )
            
            check_mr_result = CheckMRResult(
                mr_id=1,
                base_branch="main",
                dev_branch="feature",
                diffs=[diff_result]
            )
            
            # 步骤3: 使用LLMService分析MR
            result = self.llm_service.analyze_mr(check_mr_result)
            
            # 验证调用关系数据结构
            call_info = affected_functions[0].related_calls[0]
            
            # 验证是FunctionCall对象
            self.assertIsInstance(call_info, FunctionCall)
            
            # 验证字段值
            self.assertEqual(call_info.caller, "main")
            self.assertEqual(call_info.callee, "process_data")
            self.assertEqual(call_info.line, [3])
            self.assertEqual(call_info.file_path, "test.py")


if __name__ == "__main__":
    unittest.main() 