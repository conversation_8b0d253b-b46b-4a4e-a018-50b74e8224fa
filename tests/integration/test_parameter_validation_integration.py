"""
参数验证集成测试

测试目标：验证参数验证功能，包括：
- 参数存在性检查
- 参数值验证
- 参数依赖关系

验证重点：
- 参数验证的正确性
- 错误处理的准确性
- 配置加载的可靠性
"""

import tempfile
from unittest.mock import Mock

import pytest

from gate_keeper.application.dto.result import CheckMRResult
from gate_keeper.application.interfaces.git_intf import ICodeRepositoryService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.external.code_analyzer import RepositoryIndex


class TestParameterValidationIntegration:
    """参数验证集成测试类"""
    
    @pytest.fixture
    def temp_repo_dir(self):
        """创建临时仓库目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def mock_services(self):
        """创建模拟服务"""
        mock_git_service = Mock(spec=ICodeRepositoryService)
        mock_llm_service = Mock(spec=LLMService)
        mock_repo_analyzer = Mock(spec=RepositoryAnalyzer)
        
        # 设置基本返回值
        mock_patched_file = Mock()
        mock_patched_file.path = "test_file.py"
        mock_patched_file.is_removed_file = False
        mock_patched_file.is_added_file = False
        mock_patched_file.__iter__ = lambda self: iter([])
        mock_git_service.get_diff_by_mr_id.return_value = [mock_patched_file]
        mock_git_service.get_file_content_by_ref.return_value = "def test(): pass"
        
        mock_repo_analyzer.analyze_functions.return_value = []
        
        mock_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            diffs=[]
        )
        mock_llm_service.analyze_mr.return_value = mock_result
        
        return mock_git_service, mock_llm_service, mock_repo_analyzer
    
    def test_valid_parameters_accepted(self, temp_repo_dir, mock_services):
        """测试有效参数被接受"""
        mock_git_service, mock_llm_service, mock_repo_analyzer = mock_services
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 执行分析，使用有效参数
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            max_context_chain_depth=3,
            exclude_patterns=[],
            max_diff_results=None
        )
        
        # 验证结果
        assert analyze_results is not None
        assert mock_git_service.get_diff_by_mr_id.called
        assert mock_repo_analyzer.analyze_functions.called
    
    def test_invalid_parameters_rejected(self, temp_repo_dir, mock_services):
        """测试无效参数被拒绝"""
        mock_git_service, mock_llm_service, mock_repo_analyzer = mock_services
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 测试无效参数 - 模拟Git服务异常
        mock_git_service.get_diff_by_mr_id.side_effect = Exception("Invalid repository")
        
        with pytest.raises(Exception):
            usecase.execute(
                repo_dir=temp_repo_dir,
                mr_id=123,
                base_branch="main",
                dev_branch="feature/test",
                max_context_chain_depth=3,
                exclude_patterns=[],
                max_diff_results=None
            )
    
    def test_parameter_bounds_validation(self, temp_repo_dir, mock_services):
        """测试参数边界验证"""
        mock_git_service, mock_llm_service, mock_repo_analyzer = mock_services
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 测试边界值
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test",
            max_context_chain_depth=1,  # 最小值
            exclude_patterns=[],
            max_diff_results=1  # 最小值
        )
        
        # 验证结果
        assert analyze_results is not None
    
    def test_optional_parameters_handling(self, temp_repo_dir, mock_services):
        """测试可选参数处理"""
        mock_git_service, mock_llm_service, mock_repo_analyzer = mock_services
        
        # 创建UseCase
        usecase = AnalyzeMRUseCase(
            git_service=mock_git_service,
            llm_service=mock_llm_service,
            repo_analyzer=mock_repo_analyzer
        )
        
        # 执行分析，不提供可选参数
        analyze_results = usecase.execute(
            repo_dir=temp_repo_dir,
            mr_id=123,
            base_branch="main",
            dev_branch="feature/test"
            # 不提供其他可选参数
        )
        
        # 验证结果
        assert analyze_results is not None 