#!/usr/bin/env python3
"""
RepositoryIndex 相对路径处理集成测试

测试相对路径处理在实际场景中的表现，包括：
1. 完整的索引流程
2. 缓存机制
3. 跨平台兼容性
4. 实际文件系统操作
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.git import GitService
from gate_keeper.external.code_analyzer import (RepositoryIndex,
                                                RepositoryIndexFactory)
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)
from gate_keeper.external.code_analyzer.utils.file_utils import \
    collect_source_files


class TestRelativePathIntegration(unittest.TestCase):
    """
    相对路径处理集成测试
    
    验证目标：
    1. 完整的索引流程中相对路径处理正确
    2. 缓存机制与相对路径兼容
    3. 跨平台路径处理一致
    4. 实际文件系统操作正确
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_dir)
        
        # 创建测试项目结构
        self._create_test_project_structure()
        
        # Mock GitService
        self.mock_git_service = MagicMock(spec=GitService)
        self.mock_git_service.get_branch_commit_sha.return_value = "test_commit_sha"
        self.mock_git_service.get_file_content_by_ref.return_value = "# Test file content"
        
        # 创建RepositoryIndex实例
        self.repo_index = RepositoryIndex(repo_dir=self.repo_dir,
            branch="main"
        )
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_test_project_structure(self):
        """创建测试项目结构"""
        # 创建目录结构
        directories = [
            "src",
            "src/utils",
            "src/models",
            "tests",
            "tests/unit",
            "tests/integration",
            "docs",
            "docs/api",
            "config"
        ]
        
        for directory in directories:
            os.makedirs(os.path.join(self.repo_dir, directory), exist_ok=True)
        
        # 创建Python文件
        python_files = {
            "src/main.py": '''
def main():
    """Main entry point"""
    print("Hello, World!")
    utils.helper_function()
    return True

def process_data(data):
    """Process input data"""
    return data.upper()
''',
            "src/utils/helper.py": '''
def helper_function():
    """Helper function"""
    print("Helper function called")
    return "helper"

def validate_input(data):
    """Validate input data"""
    return isinstance(data, str)
''',
            "src/models/user.py": '''
class User:
    def __init__(self, name, email):
        self.name = name
        self.email = email
    
    def get_info(self):
        """Get user information"""
        return f"{self.name} ({self.email})"
''',
            "tests/unit/test_main.py": '''
import unittest
from src.main import main, process_data

class TestMain(unittest.TestCase):
    def test_main(self):
        """Test main function"""
        result = main()
        self.assertTrue(result)
    
    def test_process_data(self):
        """Test process_data function"""
        result = process_data("hello")
        self.assertEqual(result, "HELLO")
''',
            "tests/integration/test_workflow.py": '''
import unittest
from src.main import main
from src.models.user import User

class TestWorkflow(unittest.TestCase):
    def test_user_workflow(self):
        """Test user workflow"""
        user = User("Test User", "<EMAIL>")
        info = user.get_info()
        self.assertIn("Test User", info)
'''
        }
        
        # 创建C文件
        c_files = {
            "src/core.c": '''
#include <stdio.h>

int main() {
    printf("Hello from C!\\n");
    helper_function();
    return 0;
}

void helper_function() {
    printf("Helper function from C\\n");
}
''',
            "src/utils/math.c": '''
#include <math.h>

double calculate_sqrt(double x) {
    return sqrt(x);
}

int add_numbers(int a, int b) {
    return a + b;
}
'''
        }
        
        # 创建配置文件
        config_files = {
            "config/settings.py": '''
# Application settings
DEBUG = True
DATABASE_URL = "sqlite:///test.db"
''',
            "requirements.txt": '''
pytest==7.4.3
requests==2.31.0
''',
            "README.md": '''
# Test Project

This is a test project for relative path handling.
'''
        }
        
        # 写入所有文件
        all_files = {**python_files, **c_files, **config_files}
        for file_path, content in all_files.items():
            full_path = os.path.join(self.repo_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
    
    def test_complete_indexing_workflow(self):
        """
        测试完整的索引工作流
        
        验证目的：确保在完整的索引流程中，相对路径处理正确
        """
        # 执行完整索引
        self.repo_index.build()
        
        # 验证索引结果
        self.assertIsNotNone(self.repo_index.repo)
        self.assertGreater(len(self.repo_index.repo.modules), 0)
        
        # 验证所有模块路径都是相对路径
        for module_path in self.repo_index.repo.modules.keys():
            self.assertFalse(Path(module_path).is_absolute(), 
                           f"模块路径 {module_path} 是绝对路径")
            self.assertNotIn('\\', module_path, 
                           f"模块路径 {module_path} 包含反斜杠")
        
        # 验证函数定义中的路径
        for func_name, funcs in self.repo_index.function_definitions.items():
            for func in funcs:
                self.assertFalse(Path(func.filepath).is_absolute(), 
                               f"函数 {func_name} 的文件路径 {func.filepath} 是绝对路径")
                self.assertNotIn('\\', func.filepath, 
                               f"函数 {func_name} 的文件路径 {func.filepath} 包含反斜杠")
        
        # 验证函数调用中的路径
        for call in self.repo_index.function_calls:
            self.assertFalse(Path(call.file_path).is_absolute(), 
                           f"调用关系的文件路径 {call.file_path} 是绝对路径")
            self.assertNotIn('\\', call.file_path, 
                           f"调用关系的文件路径 {call.file_path} 包含反斜杠")
        
        # 验证调用图节点中的路径
        for node_id, node_data in self.repo_index.call_graph.nodes(data=True):
            filepath = node_data.get("filepath")
            if filepath and filepath != "<external>":
                self.assertFalse(Path(filepath).is_absolute(), 
                               f"节点 {node_id} 的文件路径 {filepath} 是绝对路径")
                self.assertNotIn('\\', filepath, 
                               f"节点 {node_id} 的文件路径 {filepath} 包含反斜杠")
        
        # 验证调用图边中的路径
        for edge in self.repo_index.call_graph.edges(data=True):
            file_path = edge[2].get("file_path")
            if file_path:
                self.assertFalse(Path(file_path).is_absolute(), 
                               f"边的文件路径 {file_path} 是绝对路径")
                self.assertNotIn('\\', file_path, 
                               f"边的文件路径 {file_path} 包含反斜杠")
    
    def test_file_collection_integration(self):
        """
        测试文件收集集成
        
        验证目的：确保文件收集功能与相对路径处理正确集成
        """
        # 测试无过滤的文件收集
        all_files = collect_source_files(self.repo_dir)
        self.assertGreater(len(all_files), 0)
        
        # 验证所有收集的文件都是相对路径
        for file_path in all_files:
            self.assertFalse(Path(file_path).is_absolute(), 
                           f"收集的文件路径 {file_path} 是绝对路径")
            self.assertNotIn('\\', file_path, 
                           f"收集的文件路径 {file_path} 包含反斜杠")
        
        # 测试带过滤的文件收集
        exclude_patterns = ["tests/*", "*.md", "requirements.txt"]
        filtered_files = collect_source_files(self.repo_dir, exclude_patterns)
        
        # 验证过滤结果
        for file_path in filtered_files:
            self.assertFalse(file_path.startswith("tests/"), 
                           f"排除的文件 {file_path} 仍在结果中")
            self.assertFalse(file_path.endswith(".md"), 
                           f"排除的文件 {file_path} 仍在结果中")
            self.assertNotEqual(file_path, "requirements.txt", 
                              f"排除的文件 {file_path} 仍在结果中")
    
    def test_cache_integration(self):
        """
        测试缓存集成
        
        验证目的：确保缓存机制与相对路径处理正确集成
        """
        # 执行索引
        self.repo_index.build()
        
        # 测试序列化
        repo_dict = self.repo_index.model_dump()
        
        # 验证序列化结果中的路径
        for func_name, funcs in repo_dict["function_definitions"].items():
            for func_data in funcs:
                self.assertFalse(Path(func_data["filepath"]).is_absolute(), 
                               f"序列化后的函数文件路径 {func_data['filepath']} 是绝对路径")
        
        for call_data in repo_dict["function_calls"]:
            self.assertFalse(Path(call_data["file_path"]).is_absolute(), 
                           f"序列化后的调用文件路径 {call_data['file_path']} 是绝对路径")
        
        # 测试反序列化
        restored_repo_index = RepositoryIndex.from_dict(repo_dict)
        
        # 验证反序列化后的路径处理
        self.assertEqual(len(restored_repo_index.function_definitions), 
                        len(self.repo_index.function_definitions))
        self.assertEqual(len(restored_repo_index.function_calls), 
                        len(self.repo_index.function_calls))
        
        # 验证节点中的路径
        for node_id, node_data in restored_repo_index.call_graph.nodes(data=True):
            filepath = node_data.get("filepath")
            if filepath and filepath != "<external>":
                self.assertFalse(Path(filepath).is_absolute(), 
                               f"反序列化后节点 {node_id} 的文件路径 {filepath} 是绝对路径")
    
    def test_factory_integration(self):
        """
        测试工厂集成
        
        验证目的：确保RepositoryIndexFactory与相对路径处理正确集成
        """
        # 测试工厂创建
        with patch('gate_keeper.config.config.repo_cache_dir', None):
            repo_index = RepositoryIndexFactory.get_or_build(
                repo_dir=self.repo_dir,
                branch="main"
            )
        
        # 验证工厂创建的实例
        self.assertIsInstance(repo_index, RepositoryIndex)
        self.assertTrue(os.path.samefile(str(repo_index.repo_dir), self.repo_dir))
        self.assertEqual(repo_index.branch, "main")
        
        # 验证路径处理正确
        test_path = os.path.join(self.repo_dir, "src", "main.py")
        rel_path = repo_index.to_rel_path(test_path)
        self.assertFalse(Path(rel_path).is_absolute())
        self.assertEqual(rel_path, "src/main.py")
    
    def test_cross_platform_path_handling_integration(self):
        """
        测试跨平台路径处理集成
        
        验证目的：确保在不同平台风格的路径下，相对路径处理一致
        """
        # 测试Unix风格路径
        unix_paths = [
            "/test/repo/src/main.py",
            "/home/<USER>/project/src/utils/helper.py",
            "/var/www/app/models/user.py"
        ]
        
        for unix_path in unix_paths:
            rel_path = self.repo_index.to_rel_path(unix_path)
            self.assertNotIn('\\', rel_path, 
                           f"Unix路径 {unix_path} 处理后包含反斜杠")
            
            node_id = self.repo_index._get_node_id(unix_path, "test_func")
            self.assertNotIn('\\', node_id, 
                           f"Unix路径生成的节点ID {node_id} 包含反斜杠")
        
        # 测试Windows风格路径
        windows_paths = [
            "C:\\test\\repo\\src\\main.py",
            "D:\\Users\\user\\project\\src\\utils\\helper.py",
            "E:\\var\\www\\app\\models\\user.py"
        ]
        
        for windows_path in windows_paths:
            rel_path = self.repo_index.to_rel_path(windows_path)
            self.assertNotIn('\\', rel_path, 
                           f"Windows路径 {windows_path} 处理后包含反斜杠")
            
            node_id = self.repo_index._get_node_id(windows_path, "test_func")
            self.assertNotIn('\\', node_id, 
                           f"Windows路径生成的节点ID {node_id} 包含反斜杠")
        
        # 测试混合路径
        mixed_paths = [
            "src\\main.py",  # Windows风格相对路径
            "tests/unit/test_main.py",  # Unix风格相对路径
            "src/utils\\helper.py"  # 混合风格
        ]
        
        for mixed_path in mixed_paths:
            rel_path = self.repo_index.to_rel_path(mixed_path)
            self.assertNotIn('\\', rel_path, 
                           f"混合路径 {mixed_path} 处理后包含反斜杠")
            
            node_id = self.repo_index._get_node_id(mixed_path, "test_func")
            self.assertNotIn('\\', node_id, 
                           f"混合路径生成的节点ID {node_id} 包含反斜杠")
    
    def test_real_file_system_operations(self):
        """
        测试实际文件系统操作
        
        验证目的：确保在实际文件系统操作中，相对路径处理正确
        """
        # 创建实际的文件结构
        test_file = os.path.join(self.repo_dir, "src", "test_file.py")
        with open(test_file, 'w') as f:
            f.write("def test_function():\n    pass\n")
        
        # 测试实际文件的路径转换
        abs_path = os.path.abspath(test_file)
        rel_path = self.repo_index.to_rel_path(abs_path)
        
        # 验证相对路径正确
        expected_rel_path = "src/test_file.py"
        self.assertEqual(rel_path, expected_rel_path)
        
        # 测试转换回绝对路径
        back_to_abs = self.repo_index.to_abs_path(rel_path)
        self.assertTrue(os.path.samefile(abs_path, back_to_abs))
        
        # 测试节点ID生成
        node_id = self.repo_index._get_node_id(abs_path, "test_function")
        expected_node_id = "src/test_file.py::test_function"
        self.assertEqual(node_id, expected_node_id)
    
    def test_error_recovery_integration(self):
        """
        测试错误恢复集成
        
        验证目的：确保在错误情况下，相对路径处理能够正确恢复
        """
        # 测试不存在的文件
        non_existent_file = os.path.join(self.repo_dir, "non_existent", "file.py")
        rel_path = self.repo_index.to_rel_path(non_existent_file)
        
        # 应该返回标准化后的相对路径
        self.assertFalse(Path(rel_path).is_absolute())
        self.assertNotIn('\\', rel_path)
        
        # 测试权限错误（模拟）
        with patch('pathlib.Path.resolve', side_effect=PermissionError("Permission denied")):
            protected_path = "/protected/path/file.py"
            try:
                rel_path = self.repo_index.to_rel_path(protected_path)
                # 如果权限错误被正确处理，应该返回原始路径
                self.assertEqual(rel_path, "/protected/path/file.py")
            except PermissionError:
                # 如果权限错误被抛出，这也是可以接受的
                pass
        
        # 测试符号链接（如果支持）
        try:
            link_target = os.path.join(self.repo_dir, "src", "main.py")
            link_path = os.path.join(self.repo_dir, "link.py")
            os.symlink(link_target, link_path)
            
            # 测试符号链接的路径处理
            rel_path = self.repo_index.to_rel_path(link_path)
            self.assertFalse(Path(rel_path).is_absolute())
            self.assertNotIn('\\', rel_path)
            
            # 清理
            os.unlink(link_path)
        except OSError:
            # 在不支持符号链接的系统上跳过
            pass


if __name__ == "__main__":
    unittest.main() 