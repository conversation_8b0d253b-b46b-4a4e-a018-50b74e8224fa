"""
Ollama + Gitee 组合集成测试

测试Ollama LLM与Gitee Git平台的完整工作流程。
使用真实配置和真实代码，不进行mock。
适用于：home环境，测试环境
不适用于：dev环境（不包含ollama和gitee）
"""

import os
import time
import unittest
from pathlib import Path

import pytest

from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.config import config
from gate_keeper.domain.value_objects.git import (CodeCheckDiscussion,
                                                  MergeRequest)
from gate_keeper.external.code_analyzer import RepositoryIndex
from gate_keeper.infrastructure.git.gitee.client import Gitee
from gate_keeper.infrastructure.llm.client.ollama import OllamaClient
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters
from gate_keeper.shared.log import app_logger as logger


@pytest.mark.ollama
@pytest.mark.gitee
class TestOllamaGiteeWorkflow(unittest.TestCase):
    """Ollama + Gitee完整工作流测试"""

    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        logger.info("开始Ollama + Gitee完整工作流测试")
        logger.info(f"当前环境: {os.environ.get('GK_ENV', '未设置')}")
        
        # 验证配置
        cls._validate_config()
        
        # 从config读取所有测试数据
        cls.repo_url = getattr(config, 'repo_url', None)
        cls.repo_dir = config.repo_dir
        cls.mr_id = config.mr_id
        cls.project_id = config.repo_project_id
        cls.token = config.token
        cls.base_branch = config.baseBranch
        cls.dev_branch = config.devBranch
        cls.ollama_endpoint = config.ollama_endpoint
        cls.llm_model_id = getattr(config, 'llm_model_id', 'qwen2.5-coder:3b')
        cls.llm_concurrent = getattr(config, 'llm_concurrent', 1)
        cls.max_llm_calls_per_result = getattr(config, 'max_llm_calls_per_check_mr_result', 2)
        cls.max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
        cls.max_diff_results = getattr(config, 'max_diff_results', 5)
        cls.exclude_patterns = getattr(config, 'exclude_patterns', [])
        cls.rule_file_path = config.rule_file_path
        
        # 打印配置信息用于调试
        logger.info(f"测试配置信息:")
        logger.info(f"  repo_dir: {cls.repo_dir}")
        logger.info(f"  mr_id: {cls.mr_id}")
        logger.info(f"  project_id: {cls.project_id}")
        logger.info(f"  base_branch: {cls.base_branch}")
        logger.info(f"  dev_branch: {cls.dev_branch}")
        logger.info(f"  ollama_endpoint: {cls.ollama_endpoint}")
        logger.info(f"  llm_model_id: {cls.llm_model_id}")
        logger.info(f"  rule_file_path: {cls.rule_file_path}")
        
        # 初始化服务
        cls._init_services()
    
    @classmethod
    def teardown_class(cls):
        """测试类清理"""
        logger.info("清理Ollama + Gitee完整工作流测试")
        cls._cleanup_test_comments()
    
    @classmethod
    def _validate_config(cls):
        """验证配置"""
        logger.info("验证Ollama + Gitee测试配置...")
        
        # 检查必要的配置项
        required_configs = {
            'token': 'Gitee访问令牌',
            'ollama_endpoint': 'Ollama服务地址',
            'repo_dir': '测试项目路径',
            'mr_id': 'MR ID',
            'repo_project_id': '项目ID',
            'baseBranch': '基础分支',
            'devBranch': '开发分支',
            'rule_file_path': '规则文件路径'
        }
        
        missing_configs = []
        for config_name, description in required_configs.items():
            config_value = getattr(config, config_name, None)
            if config_value is None or config_value == "not_set":
                missing_configs.append(f"{description}({config_name})")
        
        if missing_configs:
            raise ValueError(f"缺少必要的测试配置: {', '.join(missing_configs)}")
        
        # 检查测试项目路径
        if not os.path.exists(config.repo_dir):
            raise ValueError(f"测试项目路径不存在: {config.repo_dir}")
        
        # 检查规则文件
        if not os.path.exists(config.rule_file_path):
            raise ValueError(f"规则文件不存在: {config.rule_file_path}")
        
        logger.info("Ollama + Gitee测试配置验证通过")
    
    @classmethod
    def _init_services(cls):
        """初始化服务"""
        logger.info("初始化Ollama + Gitee服务...")
        
        try:
            # 1. 初始化Git服务
            cls.git_client = Gitee(cls.token)
            cls.git_service = GitService(comment_service=cls.git_client)
            logger.info("Git服务初始化成功")
            
            # 2. 初始化LLM服务
            cls.llm_client = OllamaClient(base_url=cls.ollama_endpoint)
            cls.llm_service = LLMService(
                client=cls.llm_client,
                max_calls_per_task=cls.max_llm_calls_per_result
            )
            logger.info("LLM服务初始化成功")
            
            # 3. 初始化仓库分析器
            cls.repo_analyzer = RepositoryAnalyzer(git_service=cls.git_service)
            logger.info("仓库分析器初始化成功")
            
            # 4. 初始化UseCase
            cls.analyze_mr_usecase = AnalyzeMRUseCase(
                git_service=cls.git_service,
                llm_service=cls.llm_service,
                repo_analyzer=cls.repo_analyzer
            )
            
            # 创建服务编排器
            from gate_keeper.application.service.service_orchestrator import \
                ServiceOrchestrator
            cls.orchestrator = ServiceOrchestrator(
                git_service=cls.git_service,
                llm_client=cls.llm_service.client
            )
            
            cls.analyze_mr_report_usecase = AnalyzeMRAndReportUsecase(cls.orchestrator)
            
            logger.info("UseCase初始化成功")
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            raise
    
    @classmethod
    def _cleanup_test_comments(cls):
        """清理测试期间创建的评论"""
        try:
            if hasattr(cls, 'git_client') and cls.git_client:
                comments = cls.git_client.get_discussions_of_mr(cls.repo_url, cls.mr_id)
                
                for comment in comments:
                    if "🔍 代码质量检测报告" in comment.get('body', ''):
                        cls.git_client.delete_discussion_of_mr(cls.repo_url, cls.mr_id, comment['id'])
                        logger.info(f"已清理测试评论: {comment['id']}")
        except Exception as e:
            logger.warning(f"清理测试评论失败: {e}")

    def test_ollama_gitee_complete_workflow(self):
        """测试Ollama + Gitee完整MR检测工作流（usecase入口）"""
        logger.info("开始Ollama + Gitee完整工作流测试（usecase入口）")
        
        try:
            # 执行MR分析
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.repo_dir,
                mr_id=self.mr_id,
                base_branch=self.base_branch,
                dev_branch=self.dev_branch,
                max_context_chain_depth=self.max_context_chain_depth,
                exclude_patterns=self.exclude_patterns,
                max_diff_results=self.max_diff_results
            )
            
            # 验证结果
            assert result is not None, "usecase执行无返回"
            assert hasattr(result, 'mr_id'), "结果缺少mr_id字段"
            assert hasattr(result, 'diffs'), "结果缺少diffs字段"
            assert isinstance(result.diffs, list), "diffs字段必须是列表"
            
            logger.info(f"✅ usecase执行成功: mr_id={result.mr_id}, diffs数量={len(result.diffs)}")
            
        except Exception as e:
            logger.error(f"Ollama + Gitee完整工作流测试失败: {e}")
            raise

    def test_ollama_gitee_report_workflow(self):
        """测试Ollama + Gitee报告生成工作流"""
        logger.info("开始Ollama + Gitee报告生成工作流测试")
        
        try:
            # 执行MR分析并生成报告
            analyze_results, report_content = self.analyze_mr_report_usecase.execute(
                repo_dir=self.repo_dir,
                project_id=self.project_id,
                mr_id=self.mr_id,
                base_branch=self.base_branch,
                dev_branch=self.dev_branch,
                max_context_chain_depth=self.max_context_chain_depth,
                exclude_patterns=self.exclude_patterns,
                max_diff_results=self.max_diff_results
            )
            
            # 验证分析结果
            assert analyze_results is not None, "分析结果不能为空"
            assert hasattr(analyze_results, 'diffs'), "分析结果缺少diffs字段"
            
            # 验证报告内容
            assert report_content is not None, "报告内容不能为空"
            assert isinstance(report_content, str), "报告内容必须是字符串"
            # 报告内容可以为空，当没有问题被检查出来的时候，报告就是空的
            
            logger.info(f"✅ 报告生成成功: 分析结果diffs数量={len(analyze_results.diffs)}, 报告长度={len(report_content)}")
            
        except Exception as e:
            logger.error(f"Ollama + Gitee报告生成工作流测试失败: {e}")
            raise

    def test_ollama_gitee_connection_verification(self):
        """测试Ollama + Gitee连接验证"""
        logger.info("验证Ollama + Gitee连接")
        
        # 验证Gitee连接
        try:
            user_info = self.git_client._get(f"{self.git_client.api_base}/user", {"access_token": self.token})
            assert user_info is not None, "Gitee用户信息不能为空"
            logger.info(f"✅ Gitee连接验证通过: {user_info.get('login')}")
        except Exception as e:
            logger.error(f"Gitee连接失败: {e}")
            raise
        
        # 验证Ollama连接
        try:
            # 使用真实的Ollama连接测试
            parameters = LLMParameters(
                model_id=self.llm_model_id,
                stream=False,
                max_tokens=100
            )
            
            # 发送简单的测试请求
            test_prompt = "Hello, this is a connection test."
            result = self.llm_client.generate(test_prompt, parameters)
            
            assert result is not None, "Ollama响应不能为空"
            logger.info("✅ Ollama连接验证通过")
            
        except Exception as e:
            logger.error(f"Ollama连接失败: {e}")
            raise

    def test_ollama_gitee_mr_info_retrieval(self):
        """测试Ollama + Gitee MR信息获取"""
        logger.info("测试Ollama + Gitee MR信息获取")
        
        try:
            # 获取MR信息
            mr_info = self.git_client.get_mr_info(self.repo_url, self.mr_id)
            
            # 验证MR信息
            self.assertIsNotNone(mr_info)
            self.assertIsInstance(mr_info, MergeRequest)
            self.assertIsNotNone(mr_info.id)
            
            logger.info(f"✅ MR信息获取成功: {mr_info.title if hasattr(mr_info, 'title') else 'Unknown'}")
            
        except Exception as e:
            logger.error(f"MR信息获取失败: {e}")
            raise

    def test_ollama_gitee_diff_analysis(self):
        """测试Ollama + Gitee差异分析"""
        logger.info("测试Ollama + Gitee差异分析")
        
        try:
            # 获取MR差异
            diffs = self.git_service.get_diff_by_mr_id(
                self.repo_dir, 
                self.mr_id, 
                self.base_branch, 
                self.dev_branch
            )
            
            assert diffs is not None, "差异信息不能为空"
            assert isinstance(diffs, list), "差异信息必须是列表"
            
            logger.info(f"✅ 差异分析成功: 差异文件数量={len(diffs)}")
            
            # 验证差异内容
            for diff in diffs:
                assert hasattr(diff, 'path'), "差异对象缺少path字段"
                assert hasattr(diff, 'is_added_file'), "差异对象缺少is_added_file字段"
                assert hasattr(diff, 'is_removed_file'), "差异对象缺少is_removed_file字段"
            
        except Exception as e:
            logger.error(f"差异分析失败: {e}")
            raise

    @pytest.mark.performance
    def test_ollama_gitee_performance(self):
        """测试Ollama + Gitee性能"""
        logger.info("开始Ollama + Gitee性能测试")
        
        try:
            start_time = time.time()
            
            # 执行多次分析以测试性能
            for i in range(2):  # 减少次数以避免过长时间
                logger.info(f"执行第{i+1}次性能测试")
                
                result = self.analyze_mr_usecase.execute(
                    repo_dir=self.repo_dir,
                    mr_id=self.mr_id,
                    base_branch=self.base_branch,
                    dev_branch=self.dev_branch,
                    max_context_chain_depth=1,  # 减少深度以加快测试
                    exclude_patterns=self.exclude_patterns,
                    max_diff_results=1  # 限制结果数量
                )
                
                assert result is not None, f"第{i+1}次分析结果不能为空"
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 性能断言（根据实际情况调整）
            max_duration = 300  # 5分钟
            assert duration < max_duration, f"性能测试超时: {duration:.2f}s > {max_duration}s"
            
            logger.info(f"✅ Ollama + Gitee性能测试通过: {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Ollama + Gitee性能测试失败: {e}")
            raise

    def test_ollama_gitee_error_handling(self):
        """测试Ollama + Gitee错误处理"""
        logger.info("测试Ollama + Gitee错误处理")
        
        # 测试无效MR ID的处理
        try:
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.repo_dir,
                mr_id=99999,  # 无效MR ID
                base_branch=self.base_branch,
                dev_branch=self.dev_branch,
                max_diff_results=1
            )
            
            # 即使MR不存在，也应该返回结果而不是抛出异常
            assert result is not None, "即使MR不存在也应该返回结果"
            assert hasattr(result, 'diffs'), "结果应该包含diffs字段"
            
            logger.info("✅ 无效MR ID错误处理测试通过")
            
        except Exception as e:
            logger.warning(f"无效MR ID测试出现异常（可能是预期的）: {e}")
        
        # 测试无效分支名的处理
        try:
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.repo_dir,
                mr_id=self.mr_id,
                base_branch="invalid_branch",
                dev_branch="invalid_branch",
                max_diff_results=1
            )
            
            # 应该能够处理无效分支
            assert result is not None, "即使分支无效也应该返回结果"
            
            logger.info("✅ 无效分支名错误处理测试通过")
            
        except Exception as e:
            logger.warning(f"无效分支名测试出现异常（可能是预期的）: {e}")

    def test_ollama_gitee_config_validation(self):
        """测试Ollama + Gitee配置验证"""
        logger.info("测试Ollama + Gitee配置验证")
        
        # 验证所有必要的配置项
        config_items = {
            'repo_url': self.repo_url,
            'repo_dir': self.repo_dir,
            'mr_id': self.mr_id,
            'project_id': self.project_id,
            'token': self.token,
            'base_branch': self.base_branch,
            'dev_branch': self.dev_branch,
            'ollama_endpoint': self.ollama_endpoint,
            'llm_model_id': self.llm_model_id,
            'rule_file_path': self.rule_file_path
        }
        
        for name, value in config_items.items():
            assert value is not None, f"配置项 {name} 不能为空"
            if name in ['repo_dir', 'rule_file_path']:
                assert os.path.exists(value), f"路径配置项 {name} 不存在: {value}"
        
        logger.info("✅ 配置验证通过")


# 组合标记测试示例
def test_ollama_gitee_combination_marker():
    """测试Ollama + Gitee组合标记"""
    # 这个测试验证组合标记正确工作
    # 只有同时支持ollama和gitee的环境才应该运行
    assert True, "Ollama + Gitee组合标记测试" 