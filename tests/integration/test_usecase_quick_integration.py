#!/usr/bin/env python3
"""
UseCase快速集成测试

用于快速验证usecase基本功能，使用最小配置和限制
"""

import os
import sys
import tempfile
import time
from pathlib import Path

import pytest

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.config import config
from gate_keeper.external.code_analyzer import RepositoryIndex
from gate_keeper.infrastructure.git.gitee.client import Gitee
from gate_keeper.infrastructure.llm.client.client_factory import LLMFactory
from gate_keeper.shared.log import app_logger as logger


class TestUseCaseQuickIntegration:
    """UseCase快速集成测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        logger.info("开始UseCase快速集成测试")
        
        # 验证基本配置
        cls._validate_basic_config()
        
        # 初始化测试数据
        cls.test_project_path = config.repo_dir
        cls.test_mr_id = config.mr_id
        cls.test_token = config.token
        
        # 初始化服务依赖
        cls._init_services()
    
    @classmethod
    def _validate_basic_config(cls):
        """验证基本配置"""
        logger.info("验证基本配置...")
        
        # 检查最基本的配置项
        if not os.path.exists(config.repo_dir):
            raise ValueError(f"测试项目路径不存在: {config.repo_dir}")
        
        if not os.path.exists(config.rule_file_path):
            raise ValueError(f"规则文件不存在: {config.rule_file_path}")
        
        logger.info("基本配置验证通过")
    
    @classmethod
    def _init_services(cls):
        """初始化服务依赖"""
        logger.info("初始化服务依赖...")
        
        try:
            # 1. 初始化Git服务
            git_client = Gitee(cls.test_token)
            cls.git_service = GitService(comment_service=git_client)
            logger.info("Git服务初始化成功")
            
            # 2. 初始化LLM服务（使用最小配置）
            llm_client = LLMFactory.create("ollama")
            cls.llm_service = LLMService(
                client=llm_client,
                max_workers=1,  # 最小并发数
                max_calls_per_task=1  # 最小调用次数
            )
            logger.info("LLM服务初始化成功")
            
            # 3. 初始化仓库分析器
            cls.repo_analyzer = RepositoryAnalyzer(cls.git_service, cls.llm_service)
            logger.info("仓库分析器初始化成功")
            
            # 4. 初始化UseCase
            cls.analyze_mr_usecase = AnalyzeMRUseCase(
                git_service=cls.git_service,
                llm_service=cls.llm_service,
                repo_analyzer=cls.repo_analyzer
            )
            
            logger.info("UseCase初始化成功")
            
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            raise
    
    def test_analyze_mr_usecase_minimal(self):
        """测试AnalyzeMRUseCase最小配置"""
        logger.info("开始测试AnalyzeMRUseCase最小配置")
        
        try:
            # 执行MR分析（使用最小配置）
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=self.test_mr_id,
                base_branch=config.baseBranch,
                dev_branch=config.devBranch,
                max_context_chain_depth=1,  # 最小深度
                exclude_patterns=config.exclude_patterns,
                max_diff_results=1  # 最小结果数
            )
            
            # 验证基本结果结构
            assert result is not None, "结果不能为空"
            assert hasattr(result, 'mr_id'), "结果缺少mr_id字段"
            assert hasattr(result, 'diffs'), "结果缺少diffs字段"
            assert isinstance(result.diffs, list), "diffs字段必须是列表"
            
            logger.info(f"MR分析完成: mr_id={result.mr_id}, diffs数量={len(result.diffs)}")
            logger.info("AnalyzeMRUseCase最小配置测试通过")
            
        except Exception as e:
            logger.error(f"AnalyzeMRUseCase最小配置测试失败: {e}")
            raise
    
    def test_usecase_with_timeout(self):
        """测试UseCase超时处理"""
        logger.info("开始测试UseCase超时处理")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 执行MR分析（设置超时限制）
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=self.test_mr_id,
                base_branch=config.baseBranch,
                dev_branch=config.devBranch,
                max_context_chain_depth=1,
                exclude_patterns=config.exclude_patterns,
                max_diff_results=1
            )
            
            # 记录结束时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"UseCase执行时间: {execution_time:.2f}秒")
            
            # 验证结果
            assert result is not None, "结果不能为空"
            assert hasattr(result, 'diffs'), "结果缺少diffs字段"
            
            # 超时断言（5分钟）
            assert execution_time < 300, f"执行时间过长: {execution_time}秒"
            
            logger.info("UseCase超时处理测试通过")
            
        except Exception as e:
            logger.error(f"UseCase超时处理测试失败: {e}")
            raise
    
    def test_usecase_error_recovery(self):
        """测试UseCase错误恢复"""
        logger.info("开始测试UseCase错误恢复")
        
        # 测试无效参数的处理
        try:
            result = self.analyze_mr_usecase.execute(
                repo_dir=self.test_project_path,
                mr_id=99999,  # 无效MR ID
                base_branch=config.baseBranch,
                dev_branch=config.devBranch,
                max_diff_results=1
            )
            
            # 应该返回空结果而不是抛出异常
            assert result is not None, "即使MR不存在也应该返回结果"
            assert hasattr(result, 'diffs'), "结果应该包含diffs字段"
            
            logger.info("UseCase错误恢复测试通过")
            
        except Exception as e:
            logger.warning(f"UseCase错误恢复测试出现异常（可能是预期的）: {e}")
            # 不抛出异常，因为某些错误可能是预期的


if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v", "-s"]) 