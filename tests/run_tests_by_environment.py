#!/usr/bin/env python3
"""
按环境运行测试脚本

根据不同环境的需求，过滤和运行相应的测试。

使用方法：
    python tests/run_tests_by_environment.py --env dev
    python tests/run_tests_by_environment.py --env home
    python tests/run_tests_by_environment.py --env test
"""

import argparse
import os
import subprocess
import sys


def get_test_filters_by_env(env):
    """根据环境获取测试过滤规则"""
    filters = {
        'dev': {
            'description': 'dev环境：排除ollama和gitee',
            'exclude_marks': ['ollama', 'gitee'],
            'include_marks': ['huawei'],
            'exclude_paths': [
                'tests/infrastructure/llm_ollama/',
                'tests/infrastructure/git_gitee/',
                'tests/integration/ollama_gitee/',
                'tests/integration/test_gitee_*'
            ]
        },
        'home': {
            'description': 'home环境：排除华为相关',
            'exclude_marks': ['huawei', 'fuyao', 'codehub'],
            'include_marks': ['ollama', 'gitee'],
            'exclude_paths': [
                'tests/infrastructure/huawei/',
                'tests/integration/huawei/'
            ]
        },
        'test': {
            'description': 'test环境：运行兼容的测试（主要是ollama+gitee）',
            'exclude_marks': ['huawei', 'fuyao', 'codehub'],
            'include_marks': ['ollama', 'gitee'],
            'exclude_paths': [
                'tests/infrastructure/huawei/',
                'tests/integration/huawei/'
            ]
        },
        'all': {
            'description': '所有环境：运行全部测试',
            'exclude_marks': [],
            'include_marks': [],
            'exclude_paths': []
        }
    }
    return filters.get(env, filters['all'])


def build_pytest_command(env, extra_args=None):
    """构建pytest命令"""
    filters = get_test_filters_by_env(env)
    
    cmd = ['python', '-m', 'pytest']
    
    # 添加标记过滤
    if filters['exclude_marks']:
        exclude_exmr = ' and '.join([f'not {mark}' for mark in filters['exclude_marks']])
        cmd.extend(['-m', exclude_exmr])
    
    # 添加路径排除（使用--ignore）
    for path in filters['exclude_paths']:
        if os.path.exists(path):
            cmd.extend(['--ignore', path])
    
    # 默认参数
    cmd.extend([
        '-v',  # 详细输出
        '--tb=short',  # 简短的traceback
        'tests/'  # 测试目录
    ])
    
    # 添加额外参数
    if extra_args:
        cmd.extend(extra_args)
    
    return cmd, filters


def main():
    parser = argparse.ArgumentParser(description='按环境运行测试')
    parser.add_argument('--env', choices=['dev', 'home', 'test', 'all'], 
                       default='test', help='目标环境')
    parser.add_argument('--dry-run', action='store_true', 
                       help='只显示命令，不执行')
    parser.add_argument('--pattern', help='测试文件模式匹配')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='显示详细信息')
    
    args, extra_args = parser.parse_known_args()
    
    # 设置环境变量
    os.environ['GK_ENV'] = args.env
    
    # 构建命令
    cmd, filters = build_pytest_command(args.env, extra_args)
    
    # 添加模式匹配
    if args.pattern:
        cmd.extend(['-k', args.pattern])
    
    print(f"🚀 环境: {args.env}")
    print(f"📝 描述: {filters['description']}")
    
    if args.verbose:
        print(f"❌ 排除标记: {filters['exclude_marks']}")
        print(f"✅ 包含标记: {filters['include_marks']}")
        print(f"🚫 排除路径: {filters['exclude_paths']}")
    
    print(f"🔧 命令: {' '.join(cmd)}")
    
    if args.dry_run:
        print("🔍 预览模式：不执行测试")
        return 0
    
    print("\n" + "="*50)
    
    # 执行测试
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 执行测试时出错: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main()) 