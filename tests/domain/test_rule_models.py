"""
领域规则模型单元测试

测试领域层的规则相关模型，包括CodeCheckRule等。
"""

import unittest
from typing import List

from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestCodeCheckRule(unittest.TestCase):
    """测试代码检查规则模型"""
    
    def test_code_check_rule_creation(self):
        """测试代码检查规则创建"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        self.assertEqual(rule.id, "RULE_001")
        self.assertEqual(rule.name, "测试规则")
        self.assertEqual(rule.description, "测试描述")
        self.assertEqual(rule.category, ["style"])
        self.assertTrue(rule.enabled)
        self.assertEqual(rule.languages, ["python"])
        self.assertEqual(rule.rule_value, "test_value")
        self.assertEqual(rule.severity, "warning")
    
    def test_code_check_rule_validation(self):
        """测试代码检查规则验证"""
        # 正常情况
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        # 验证基本属性
        self.assertIsNotNone(rule.id)
        self.assertIsNotNone(rule.name)
        self.assertIsNotNone(rule.category)
        self.assertIsNotNone(rule.languages)
    
    def test_code_check_rule_applies_to_language(self):
        """测试规则是否适用于指定语言"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python", "c"],
            rule_value="test_value",
            severity="warning"
        )
        
        # 直接检查languages列表
        self.assertIn("python", rule.languages)
        self.assertIn("c", rule.languages)
        self.assertNotIn("java", rule.languages)
    
    def test_code_check_rule_has_category(self):
        """测试规则是否属于指定分类"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style", "complexity", "security"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        # 直接检查category列表
        self.assertIn("style", rule.category)
        self.assertIn("complexity", rule.category)
        self.assertIn("security", rule.category)
        self.assertNotIn("performance", rule.category)
    
    def test_code_check_rule_severity_levels(self):
        """测试规则严重程度级别"""
        # 不同严重程度的规则
        error_rule = CodeCheckRule(
            id="RULE_ERROR",
            name="错误规则",
            description="错误级别规则",
            category=["security"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="error"
        )
        
        warning_rule = CodeCheckRule(
            id="RULE_WARNING",
            name="警告规则",
            description="警告级别规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        info_rule = CodeCheckRule(
            id="RULE_INFO",
            name="信息规则",
            description="信息级别规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="info"
        )
        
        # 验证严重程度
        self.assertEqual(error_rule.severity, "error")
        self.assertEqual(warning_rule.severity, "warning")
        self.assertEqual(info_rule.severity, "info")
        
        # 验证严重程度比较（基于字符串比较）
        severity_order = ["error", "warning", "info"]
        error_index = severity_order.index(error_rule.severity)
        warning_index = severity_order.index(warning_rule.severity)
        self.assertLess(error_index, warning_index)  # error比warning更严重
    
    def test_code_check_rule_enabled_disabled(self):
        """测试规则启用/禁用状态"""
        enabled_rule = CodeCheckRule(
            id="RULE_ENABLED",
            name="启用规则",
            description="启用的规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        disabled_rule = CodeCheckRule(
            id="RULE_DISABLED",
            name="禁用规则",
            description="禁用的规则",
            category=["style"],
            enabled=False,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        self.assertTrue(enabled_rule.enabled)
        self.assertFalse(disabled_rule.enabled)
        
        # 验证启用状态影响
        self.assertTrue(enabled_rule.enabled)  # 直接检查enabled属性
        self.assertFalse(disabled_rule.enabled)
    
    def test_code_check_rule_rule_value_parsing(self):
        """测试规则值解析"""
        # 数值规则
        numeric_rule = CodeCheckRule(
            id="RULE_NUMERIC",
            name="数值规则",
            description="数值类型规则",
            category=["complexity"],
            enabled=True,
            languages=["python"],
            rule_value="max_complexity:10",
            severity="warning"
        )
        
        # 字符串规则
        string_rule = CodeCheckRule(
            id="RULE_STRING",
            name="字符串规则",
            description="字符串类型规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="naming_convention:snake_case",
            severity="warning"
        )
        
        # 布尔规则
        boolean_rule = CodeCheckRule(
            id="RULE_BOOLEAN",
            name="布尔规则",
            description="布尔类型规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="require_docstring:true",
            severity="warning"
        )
        
        # 验证规则值存储
        self.assertEqual(numeric_rule.rule_value, "max_complexity:10")
        self.assertEqual(string_rule.rule_value, "naming_convention:snake_case")
        self.assertEqual(boolean_rule.rule_value, "require_docstring:true")
        
        # 手动解析规则值
        def parse_rule_value(rule_value: str, key: str) -> str:
            """简单的规则值解析函数"""
            for item in rule_value.split(';'):
                if ':' in item:
                    k, v = item.split(':', 1)
                    if k.strip() == key:
                        return v.strip()
            return None
        
        self.assertEqual(parse_rule_value(numeric_rule.rule_value, "max_complexity"), "10")
        self.assertEqual(parse_rule_value(string_rule.rule_value, "naming_convention"), "snake_case")
        self.assertEqual(parse_rule_value(boolean_rule.rule_value, "require_docstring"), "true")
    
    def test_code_check_rule_equality(self):
        """测试规则相等性"""
        rule1 = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        rule2 = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        rule3 = CodeCheckRule(
            id="RULE_002",
            name="不同规则",
            description="不同描述",
            category=["complexity"],
            enabled=False,
            languages=["c"],
            rule_value="different_value",
            severity="error"
        )
        
        # 相同属性的规则应该相等
        self.assertEqual(rule1.id, rule2.id)
        self.assertEqual(rule1.name, rule2.name)
        self.assertEqual(rule1.category, rule2.category)
        
        # 不同属性的规则应该不相等
        self.assertNotEqual(rule1.id, rule3.id)
        self.assertNotEqual(rule1.name, rule3.name)
        self.assertNotEqual(rule1.category, rule3.category)
    
    def test_code_check_rule_hash(self):
        """测试规则哈希值"""
        rule1 = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        rule2 = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        # Pydantic模型默认不可哈希，但我们可以比较关键属性
        self.assertEqual(rule1.id, rule2.id)
        self.assertEqual(rule1.name, rule2.name)
        self.assertEqual(rule1.category, rule2.category)


class TestCodeCheckRuleEdgeCases(unittest.TestCase):
    """测试代码检查规则边界情况"""
    
    def test_code_check_rule_case_insensitive_severity(self):
        """测试大小写不敏感的严重程度"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="WARNING"  # 大写
        )
        
        self.assertEqual(rule.severity, "WARNING")
    
    def test_code_check_rule_complex_rule_value(self):
        """测试复杂规则值"""
        complex_rule = CodeCheckRule(
            id="RULE_COMPLEX",
            name="复杂规则",
            description="复杂规则值",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="max_lines:50;min_lines:1;require_docstring:true;naming_convention:snake_case",
            severity="warning"
        )
        
        # 验证复杂规则值存储
        self.assertEqual(complex_rule.rule_value, "max_lines:50;min_lines:1;require_docstring:true;naming_convention:snake_case")
        
        # 手动解析复杂规则值
        def parse_complex_rule_value(rule_value: str, key: str) -> str:
            """解析复杂规则值"""
            for item in rule_value.split(';'):
                if ':' in item:
                    k, v = item.split(':', 1)
                    if k.strip() == key:
                        return v.strip()
            return None
        
        self.assertEqual(parse_complex_rule_value(complex_rule.rule_value, "max_lines"), "50")
        self.assertEqual(parse_complex_rule_value(complex_rule.rule_value, "min_lines"), "1")
        self.assertEqual(parse_complex_rule_value(complex_rule.rule_value, "require_docstring"), "true")
        self.assertEqual(parse_complex_rule_value(complex_rule.rule_value, "naming_convention"), "snake_case")
    
    def test_code_check_rule_empty_categories(self):
        """测试空分类列表"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=[],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        self.assertEqual(rule.category, [])
        self.assertNotIn("style", rule.category)
    
    def test_code_check_rule_empty_languages(self):
        """测试空语言列表"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="测试规则",
            description="测试描述",
            category=["style"],
            enabled=True,
            languages=[],
            rule_value="test_value",
            severity="warning"
        )
        
        self.assertEqual(rule.languages, [])
        self.assertNotIn("python", rule.languages)


class TestCodeCheckRuleIntegration(unittest.TestCase):
    """测试代码检查规则集成"""
    
    def test_rule_filtering_by_language(self):
        """测试按语言过滤规则"""
        python_rule = CodeCheckRule(
            id="PYTHON_RULE",
            name="Python规则",
            description="Python专用规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        c_rule = CodeCheckRule(
            id="C_RULE",
            name="C规则",
            description="C专用规则",
            category=["style"],
            enabled=True,
            languages=["c"],
            rule_value="test_value",
            severity="warning"
        )
        
        multi_lang_rule = CodeCheckRule(
            id="MULTI_RULE",
            name="多语言规则",
            description="多语言通用规则",
            category=["style"],
            enabled=True,
            languages=["python", "c"],
            rule_value="test_value",
            severity="warning"
        )
        
        all_rules = [python_rule, c_rule, multi_lang_rule]
        
        # 过滤Python规则
        python_rules = [rule for rule in all_rules if "python" in rule.languages]
        
        self.assertEqual(len(python_rules), 2)  # python_rule 和 multi_lang_rule
        self.assertIn(python_rule, python_rules)
        self.assertIn(multi_lang_rule, python_rules)
        self.assertNotIn(c_rule, python_rules)
    
    def test_rule_filtering_by_category(self):
        """测试按分类过滤规则"""
        style_rule = CodeCheckRule(
            id="STYLE_RULE",
            name="样式规则",
            description="样式相关规则",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        complexity_rule = CodeCheckRule(
            id="COMPLEXITY_RULE",
            name="复杂度规则",
            description="复杂度相关规则",
            category=["complexity"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        multi_category_rule = CodeCheckRule(
            id="MULTI_CAT_RULE",
            name="多分类规则",
            description="多分类规则",
            category=["style", "complexity"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        all_rules = [style_rule, complexity_rule, multi_category_rule]
        
        # 过滤样式规则
        style_rules = [rule for rule in all_rules if "style" in rule.category]
        
        self.assertEqual(len(style_rules), 2)  # style_rule 和 multi_category_rule
        self.assertIn(style_rule, style_rules)
        self.assertIn(multi_category_rule, style_rules)
        self.assertNotIn(complexity_rule, style_rules)
    
    def test_rule_prioritization_by_severity(self):
        """测试按严重程度排序规则"""
        info_rule = CodeCheckRule(
            id="INFO_RULE",
            name="信息规则",
            description="信息级别",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="info"
        )
        
        warning_rule = CodeCheckRule(
            id="WARNING_RULE",
            name="警告规则",
            description="警告级别",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="warning"
        )
        
        error_rule = CodeCheckRule(
            id="ERROR_RULE",
            name="错误规则",
            description="错误级别",
            category=["style"],
            enabled=True,
            languages=["python"],
            rule_value="test_value",
            severity="error"
        )
        
        rules = [info_rule, warning_rule, error_rule]
        
        # 按严重程度排序（从高到低）
        severity_order = {"error": 3, "warning": 2, "info": 1}
        sorted_rules = sorted(rules, key=lambda r: severity_order.get(r.severity, 0), reverse=True)
        
        self.assertEqual(sorted_rules[0].severity, "error")
        self.assertEqual(sorted_rules[1].severity, "warning")
        self.assertEqual(sorted_rules[2].severity, "info")


if __name__ == '__main__':
    unittest.main() 