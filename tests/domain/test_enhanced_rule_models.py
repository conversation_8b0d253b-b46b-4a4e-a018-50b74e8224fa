"""
增强的规则模型测试

测试评测集更新后新增的match_regex和suggestion字段功能
"""

import unittest
from gate_keeper.domain.rule.check_rule import CodeCheckRule, TestCase, PromptTemplate


class TestEnhancedCodeCheckRule(unittest.TestCase):
    """测试增强的CodeCheckRule模型"""
    
    def test_code_check_rule_with_match_regex(self):
        """测试包含match_regex字段的规则创建"""
        rule = CodeCheckRule(
            id="RULE_001",
            name="内存拷贝检查",
            description="检查内存拷贝操作的安全性",
            category=["内存操作", "安全"],
            enabled=True,
            languages=["c"],
            rule_value="检查memcpy边界",
            severity="高",
            match_regex=r"memcpy\s*\(|memcpy_s\s*\(",
            suggestion="检查所有内存拷贝操作前是否有边界校验"
        )
        
        self.assertEqual(rule.match_regex, r"memcpy\s*\(|memcpy_s\s*\(")
        self.assertEqual(rule.suggestion, "检查所有内存拷贝操作前是否有边界校验")
    
    def test_code_check_rule_without_new_fields(self):
        """测试不包含新字段的规则创建（向后兼容）"""
        rule = CodeCheckRule(
            id="RULE_002",
            name="传统规则",
            description="不包含新字段的传统规则",
            category=["样式"],
            enabled=True,
            languages=["python"],
            rule_value="传统检查",
            severity="中"
        )
        
        # 新字段应该有默认值
        self.assertEqual(rule.match_regex, "")
        self.assertEqual(rule.suggestion, "")
    
    def test_code_check_rule_empty_new_fields(self):
        """测试新字段为空的情况"""
        rule = CodeCheckRule(
            id="RULE_003",
            name="空字段规则",
            description="新字段为空的规则",
            category=["测试"],
            enabled=True,
            languages=["c"],
            rule_value="测试",
            severity="低",
            match_regex="",
            suggestion=""
        )
        
        self.assertEqual(rule.match_regex, "")
        self.assertEqual(rule.suggestion, "")
    
    def test_code_check_rule_complex_regex(self):
        """测试复杂的正则表达式"""
        complex_regex = r"(memcpy|strcpy|strcat)\s*\(.*\)|malloc\s*\(.*\)|free\s*\("
        rule = CodeCheckRule(
            id="RULE_004",
            name="复杂内存操作检查",
            description="检查多种内存操作",
            category=["内存操作"],
            enabled=True,
            languages=["c"],
            rule_value="复杂内存检查",
            severity="高",
            match_regex=complex_regex,
            suggestion="检查内存操作的安全性，包括边界检查和内存泄漏"
        )
        
        self.assertEqual(rule.match_regex, complex_regex)
        self.assertIn("边界检查", rule.suggestion)
        self.assertIn("内存泄漏", rule.suggestion)
    
    def test_code_check_rule_multiline_suggestion(self):
        """测试多行建议"""
        multiline_suggestion = """检查数组访问的安全性，重点关注：
1) 数组下标是否在有效范围内
2) 是否检查了下标小于数组长度
3) 动态计算的下标是否有溢出风险
4) 循环中的数组访问边界条件"""
        
        rule = CodeCheckRule(
            id="RULE_005",
            name="数组访问检查",
            description="检查数组访问安全性",
            category=["数组操作"],
            enabled=True,
            languages=["c"],
            rule_value="数组边界检查",
            severity="高",
            match_regex=r"\[\s*\w+\s*\]",
            suggestion=multiline_suggestion
        )
        
        self.assertEqual(rule.suggestion, multiline_suggestion)
        self.assertIn("数组下标", rule.suggestion)
        self.assertIn("边界条件", rule.suggestion)


class TestEnhancedTestCase(unittest.TestCase):
    """测试增强的TestCase模型"""
    
    def test_test_case_with_new_fields(self):
        """测试包含新字段的TestCase创建"""
        test_case = TestCase(
            id="TEST_001",
            name="内存拷贝测试用例",
            description="测试内存拷贝规则",
            category=["内存操作"],
            enabled=True,
            languages=["c"],
            rule_value="测试memcpy",
            severity="高",
            match_regex=r"memcpy\s*\(",
            suggestion="检查memcpy的边界",
            case_name="测试memcpy边界检查",
            expected_answer="违规：未检查边界",
            think="memcpy可能导致缓冲区溢出",
            code="memcpy(dst, src, len);",
            type="negative"
        )
        
        self.assertEqual(test_case.match_regex, r"memcpy\s*\(")
        self.assertEqual(test_case.suggestion, "检查memcpy的边界")
        self.assertEqual(test_case.case_name, "测试memcpy边界检查")
        self.assertEqual(test_case.expected_answer, "违规：未检查边界")
    
    def test_test_case_to_check_rule_conversion(self):
        """测试TestCase到CodeCheckRule的转换"""
        test_case = TestCase(
            id="TEST_002",
            name="转换测试",
            description="测试转换功能",
            category=["测试"],
            enabled=True,
            languages=["c"],
            rule_value="转换测试",
            severity="中",
            match_regex=r"test\s*\(",
            suggestion="测试建议",
            case_name="转换测试用例",
            expected_answer="正常",
            think="测试转换",
            code="test();",
            type="positive"
        )
        
        rule = test_case.to_check_rule()
        
        # 验证基本字段
        self.assertEqual(rule.id, test_case.id)
        self.assertEqual(rule.name, test_case.name)
        self.assertEqual(rule.match_regex, test_case.match_regex)
        self.assertEqual(rule.suggestion, test_case.suggestion)
        
        # 验证类型
        self.assertIsInstance(rule, CodeCheckRule)
    
    def test_test_case_from_check_rule_conversion(self):
        """测试从CodeCheckRule创建TestCase"""
        rule = CodeCheckRule(
            id="RULE_006",
            name="源规则",
            description="用于创建TestCase的源规则",
            category=["测试"],
            enabled=True,
            languages=["c"],
            rule_value="源规则值",
            severity="中",
            match_regex=r"source\s*\(",
            suggestion="源规则建议"
        )
        
        test_case = TestCase.from_check_rule(
            rule,
            case_name="从规则创建的测试用例",
            expected_answer="测试答案",
            think="测试思考",
            code="source();",
            type="positive"
        )
        
        # 验证继承的字段
        self.assertEqual(test_case.id, rule.id)
        self.assertEqual(test_case.name, rule.name)
        self.assertEqual(test_case.match_regex, rule.match_regex)
        self.assertEqual(test_case.suggestion, rule.suggestion)
        
        # 验证新增的字段
        self.assertEqual(test_case.case_name, "从规则创建的测试用例")
        self.assertEqual(test_case.expected_answer, "测试答案")


class TestRuleFieldValidation(unittest.TestCase):
    """测试规则字段验证"""
    
    def test_match_regex_validation(self):
        """测试match_regex字段的有效性"""
        # 有效的正则表达式
        valid_regexes = [
            r"memcpy\s*\(",
            r"\[\s*\w+\s*\]",
            r"(malloc|free)\s*\(",
            r"if\s*\(\s*.*\s*==\s*NULL\s*\)",
            ""  # 空字符串也是有效的
        ]
        
        for regex in valid_regexes:
            rule = CodeCheckRule(
                id=f"RULE_{hash(regex)}",
                name="正则测试",
                description="测试正则表达式",
                category=["测试"],
                enabled=True,
                languages=["c"],
                rule_value="测试",
                severity="中",
                match_regex=regex,
                suggestion="测试建议"
            )
            self.assertEqual(rule.match_regex, regex)
    
    def test_suggestion_content_validation(self):
        """测试suggestion字段内容的合理性"""
        suggestions = [
            "简单建议",
            "多行建议\n包含换行符",
            "包含特殊字符的建议：1) 检查点1；2) 检查点2",
            "",  # 空建议
            "很长的建议" * 100  # 长建议
        ]
        
        for suggestion in suggestions:
            rule = CodeCheckRule(
                id=f"RULE_{hash(suggestion)}",
                name="建议测试",
                description="测试建议字段",
                category=["测试"],
                enabled=True,
                languages=["c"],
                rule_value="测试",
                severity="中",
                match_regex=r"test\s*\(",
                suggestion=suggestion
            )
            self.assertEqual(rule.suggestion, suggestion)


class TestRuleCompatibility(unittest.TestCase):
    """测试规则兼容性"""
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 创建不包含新字段的规则（模拟旧版本数据）
        old_style_rule = CodeCheckRule(
            id="OLD_RULE",
            name="旧式规则",
            description="不包含新字段的规则",
            category=["兼容性"],
            enabled=True,
            languages=["c"],
            rule_value="旧式检查",
            severity="中"
            # 注意：没有match_regex和suggestion字段
        )
        
        # 验证默认值
        self.assertEqual(old_style_rule.match_regex, "")
        self.assertEqual(old_style_rule.suggestion, "")
        
        # 验证其他字段正常
        self.assertEqual(old_style_rule.id, "OLD_RULE")
        self.assertEqual(old_style_rule.name, "旧式规则")
        self.assertTrue(old_style_rule.enabled)
    
    def test_mixed_rules_compatibility(self):
        """测试新旧规则混合使用的兼容性"""
        old_rule = CodeCheckRule(
            id="OLD_RULE",
            name="旧规则",
            description="旧式规则",
            category=["兼容性"],
            enabled=True,
            languages=["c"],
            rule_value="旧检查",
            severity="中"
        )
        
        new_rule = CodeCheckRule(
            id="NEW_RULE",
            name="新规则",
            description="新式规则",
            category=["兼容性"],
            enabled=True,
            languages=["c"],
            rule_value="新检查",
            severity="中",
            match_regex=r"new\s*\(",
            suggestion="新式建议"
        )
        
        rules = [old_rule, new_rule]
        
        # 验证可以正常处理混合规则
        for rule in rules:
            self.assertIsNotNone(rule.id)
            self.assertIsNotNone(rule.name)
            self.assertIsInstance(rule.match_regex, str)
            self.assertIsInstance(rule.suggestion, str)


if __name__ == '__main__':
    unittest.main()
