"""
测试自定义prompt模板功能
"""
import unittest

from gate_keeper.domain.rule.check_rule import CodeCheckRule, PromptTemplate


class TestCustomPromptRules(unittest.TestCase):
    
    def setUp(self):
        """设置测试环境"""
        # 创建一个带有自定义prompt模板的规则
        self.prompt_template = PromptTemplate(
            instruction="请特别关注内存复制操作，检查是否存在重叠内存区域的情况。",
            requirement_template="规则ID: {rule_id}\n规则名称: {rule_name}\n重点关注：{rule_value}",
            example_template="错误示例：使用memcpy复制重叠区域\n正确示例：使用memmove_s",
            output_format="请输出JSON格式的检测结果",
            context_requirements="检查内存操作的源地址和目标地址",
            focus_points=["memcpy函数调用", "重叠区域检测", "缓冲区大小检查"]
        )
        
        self.custom_rule = CodeCheckRule(
            id="1.2",
            name="内存搬移",
            description="内存复制安全规范",
            category=["内存管理", "安全"],
            enabled=True,
            severity="高",
            rule_value="禁止使用memcpy进行重叠内存区域的复制",
            example="使用memmove_s替代memcpy",
            languages=["C"],
            prompt_template=self.prompt_template
        )
        
        # 创建一个没有自定义prompt模板的规则
        self.default_rule = CodeCheckRule(
            id="2.1",
            name="字符串格式化",
            description="字符串格式化安全规范",
            category=["字符串操作", "安全"],
            enabled=True,
            severity="中",
            rule_value="禁止使用sprintf进行字符串格式化",
            example="使用snprintf替代sprintf",
            languages=["C"]
        )
    
    def test_has_custom_prompt(self):
        """测试has_custom_prompt方法"""
        self.assertTrue(self.custom_rule.has_custom_prompt())
        self.assertFalse(self.default_rule.has_custom_prompt())
    
    def test_get_effective_instruction(self):
        """测试get_effective_instruction方法"""
        instruction = self.custom_rule.get_effective_instruction()
        self.assertEqual(instruction, "请特别关注内存复制操作，检查是否存在重叠内存区域的情况。")
        
        # 测试没有自定义指令的情况
        instruction = self.default_rule.get_effective_instruction()
        self.assertEqual(instruction, "")
    
    def test_get_effective_requirement_format(self):
        """测试get_effective_requirement_format方法"""
        format_str = self.custom_rule.get_effective_requirement_format()
        self.assertEqual(format_str, "规则ID: {rule_id}\n规则名称: {rule_name}\n重点关注：{rule_value}")
        
        # 测试没有自定义格式的情况
        format_str = self.default_rule.get_effective_requirement_format()
        self.assertEqual(format_str, "")
    
    def test_format_requirement_with_variables(self):
        """测试format_requirement_with_variables方法"""
        formatted = self.custom_rule.format_requirement_with_variables(
            rule_id="1.2",
            rule_name="内存搬移",
            rule_value="禁止使用memcpy"
        )
        expected = "规则ID: 1.2\n规则名称: 内存搬移\n重点关注：禁止使用memcpy"
        self.assertEqual(formatted, expected)
        
        # 测试默认格式
        formatted = self.default_rule.format_requirement_with_variables()
        self.assertIn("规则: 2.1", formatted)
        self.assertIn("字符串格式化", formatted)
    
    def test_get_focus_points(self):
        """测试get_focus_points方法"""
        focus_points = self.custom_rule.get_focus_points()
        expected = ["memcpy函数调用", "重叠区域检测", "缓冲区大小检查"]
        self.assertEqual(focus_points, expected)
        
        # 测试没有重点关注点的情况
        focus_points = self.default_rule.get_focus_points()
        self.assertEqual(focus_points, [])
    
    def test_get_effective_example_format(self):
        """测试get_effective_example_format方法"""
        example_format = self.custom_rule.get_effective_example_format()
        self.assertEqual(example_format, "错误示例：使用memcpy复制重叠区域\n正确示例：使用memmove_s")
        
        # 测试没有自定义示例格式的情况
        example_format = self.default_rule.get_effective_example_format()
        self.assertEqual(example_format, "")
    
    def test_get_effective_output_format(self):
        """测试get_effective_output_format方法"""
        output_format = self.custom_rule.get_effective_output_format()
        self.assertEqual(output_format, "请输出JSON格式的检测结果")
        
        # 测试没有自定义输出格式的情况
        output_format = self.default_rule.get_effective_output_format()
        self.assertEqual(output_format, "")
    
    def test_prompt_template_creation(self):
        """测试PromptTemplate对象创建"""
        template = PromptTemplate(
            instruction="测试指令",
            requirement_template="测试要求模板",
            example_template="测试示例模板",
            output_format="测试输出格式",
            context_requirements="测试上下文要求",
            focus_points=["测试点1", "测试点2"]
        )
        
        self.assertEqual(template.instruction, "测试指令")
        self.assertEqual(template.requirement_template, "测试要求模板")
        self.assertEqual(template.example_template, "测试示例模板")
        self.assertEqual(template.output_format, "测试输出格式")
        self.assertEqual(template.context_requirements, "测试上下文要求")
        self.assertEqual(template.focus_points, ["测试点1", "测试点2"])
    
    def test_rule_with_simple_custom_fields(self):
        """测试使用简化自定义字段的规则"""
        simple_rule = CodeCheckRule(
            id="3.1",
            name="简单自定义规则",
            category=["测试"],
            rule_value="测试规则内容",
            custom_instruction="简单自定义指令",
            custom_requirement_format="简单要求格式",
            focus_keywords=["关键词1", "关键词2"]
        )
        
        self.assertTrue(simple_rule.has_custom_prompt())
        self.assertEqual(simple_rule.get_effective_instruction(), "简单自定义指令")
        self.assertEqual(simple_rule.get_effective_requirement_format(), "简单要求格式")
        self.assertEqual(simple_rule.get_focus_points(), ["关键词1", "关键词2"])


if __name__ == '__main__':
    unittest.main() 