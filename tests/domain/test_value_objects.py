"""
值对象单元测试

测试目的：验证值对象的不可变性、业务行为和验证逻辑
测试范围：AnalyzeLLMResult、ViolationItem、AggregatedAnalyzeLLMResult
"""

import unittest
from typing import List

from gate_keeper.domain.value_objects.analysis_result import (
    AggregatedAnalyzeLLMResult, AnalyzeLLMResult, ViolationItem)


class TestViolationItem(unittest.TestCase):
    """测试违规项值对象"""
    
    def test_violation_item_creation(self):
        """测试违规项创建"""
        violation = ViolationItem(
            rule_id="RULE_001",
            rule_content="函数长度不应超过50行",
            location={"file": "test.py", "line": 10},
            severity="warning",
            message="函数main过长，建议拆分"
        )
        
        self.assertEqual(violation.rule_id, "RULE_001")
        self.assertEqual(violation.rule_content, "函数长度不应超过50行")
        self.assertEqual(violation.location, {"file": "test.py", "line": 10})
        self.assertEqual(violation.severity, "warning")
        self.assertEqual(violation.message, "函数main过长，建议拆分")
    
    def test_violation_item_optional_fields(self):
        """测试违规项可选字段"""
        violation = ViolationItem(
            rule_id="RULE_002",
            location={"file": "test.py", "line": 15},
            message="缺少函数文档"
        )
        
        self.assertIsNone(violation.rule_content)
        self.assertIsNone(violation.severity)
    
    def test_violation_item_immutability(self):
        """测试违规项不可变性"""
        violation = ViolationItem(
            rule_id="RULE_003",
            location={"file": "test.py", "line": 20},
            message="测试消息"
        )
        
        # 验证字段值正确
        self.assertEqual(violation.rule_id, "RULE_003")
        self.assertEqual(violation.location, {"file": "test.py", "line": 20})
        self.assertEqual(violation.message, "测试消息")
        
        # 注意：Pydantic模型默认是可变的，这里测试字段访问而不是不可变性
        # 如果需要不可变性，应该使用frozen=True配置


class TestAnalyzeLLMResult(unittest.TestCase):
    """测试LLM分析结果值对象"""
    
    def test_analyze_llm_result_creation(self):
        """测试LLM分析结果创建"""
        violations = [
            ViolationItem(
                rule_id="RULE_001",
                location={"file": "test.py", "line": 10},
                message="测试违规1"
            ),
            ViolationItem(
                rule_id="RULE_002", 
                location={"file": "test.py", "line": 15},
                message="测试违规2"
            )
        ]
        
        result = AnalyzeLLMResult(
            is_pass=False,
            reason="发现2个违规",
            violations=violations,
            code="def test(): pass",
            prompt="分析以下代码",
            response="发现违规",
            call_id="call_123"
        )
        
        self.assertFalse(result.is_pass)
        self.assertEqual(result.reason, "发现2个违规")
        self.assertEqual(len(result.violations), 2)
        self.assertEqual(result.code, "def test(): pass")
        self.assertEqual(result.prompt, "分析以下代码")
        self.assertEqual(result.response, "发现违规")
        self.assertEqual(result.call_id, "call_123")
    
    def test_analyze_llm_result_pass_case(self):
        """测试通过的分析结果"""
        result = AnalyzeLLMResult(
            is_pass=True,
            reason="代码检查通过",
            violations=[],
            code="def good_function(): pass"
        )
        
        self.assertTrue(result.is_pass)
        self.assertEqual(len(result.violations), 0)
        self.assertEqual(result.reason, "代码检查通过")
    
    def test_analyze_llm_result_optional_fields(self):
        """测试可选字段"""
        result = AnalyzeLLMResult(
            is_pass=True,
            reason="通过",
            violations=[]
        )
        
        self.assertEqual(result.code, "")
        self.assertIsNone(result.prompt)
        self.assertIsNone(result.response)
        self.assertIsNone(result.call_id)


class TestAggregatedAnalyzeLLMResult(unittest.TestCase):
    """测试聚合LLM分析结果值对象"""
    
    def test_aggregated_result_creation(self):
        """测试聚合结果创建"""
        violations = [
            ViolationItem(
                rule_id="RULE_001",
                location={"file": "test.py", "line": 10},
                message="违规1"
            )
        ]
        
        result = AggregatedAnalyzeLLMResult(
            is_pass=False,
            reason="聚合违规",
            violations=violations,
            code="def test(): pass",
            call_ids=["call_1", "call_2"],
            function_name="test_function",
            file_path="test.py"
        )
        
        self.assertFalse(result.is_pass)
        self.assertEqual(result.reason, "聚合违规")
        self.assertEqual(len(result.violations), 1)
        self.assertEqual(result.call_ids, ["call_1", "call_2"])
        self.assertEqual(result.function_name, "test_function")
        self.assertEqual(result.file_path, "test.py")
    
    def test_aggregated_result_empty_call_ids(self):
        """测试空调用ID列表"""
        result = AggregatedAnalyzeLLMResult(
            is_pass=True,
            reason="通过",
            violations=[],
            code="def test(): pass"
        )
        
        self.assertEqual(result.call_ids, [])
        self.assertIsNone(result.function_name)
        self.assertIsNone(result.file_path)
    
    def test_aggregated_result_immutability(self):
        """测试聚合结果不可变性"""
        result = AggregatedAnalyzeLLMResult(
            is_pass=True,
            reason="测试",
            violations=[],
            code="def test(): pass"
        )
        
        # 验证字段值正确
        self.assertTrue(result.is_pass)
        self.assertEqual(result.reason, "测试")
        self.assertEqual(result.violations, [])
        self.assertEqual(result.code, "def test(): pass")
        
        # 注意：Pydantic模型默认是可变的，这里测试字段访问而不是不可变性
        # 如果需要不可变性，应该使用frozen=True配置


class TestValueObjectsIntegration(unittest.TestCase):
    """测试值对象集成场景"""
    
    def test_violation_in_llm_result(self):
        """测试违规项在LLM结果中的使用"""
        violation = ViolationItem(
            rule_id="RULE_001",
            location={"file": "test.py", "line": 10},
            message="测试违规"
        )
        
        result = AnalyzeLLMResult(
            is_pass=False,
            reason="发现违规",
            violations=[violation],
            code="def test(): pass"
        )
        
        self.assertFalse(result.is_pass)
        self.assertEqual(len(result.violations), 1)
        self.assertEqual(result.violations[0].rule_id, "RULE_001")
    
    def test_aggregate_multiple_llm_results(self):
        """测试聚合多个LLM结果"""
        # 创建多个LLM结果
        result1 = AnalyzeLLMResult(
            is_pass=False,
            reason="违规1",
            violations=[
                ViolationItem(
                    rule_id="RULE_001",
                    location={"file": "test.py", "line": 10},
                    message="违规1"
                )
            ],
            call_id="call_1"
        )
        
        result2 = AnalyzeLLMResult(
            is_pass=False,
            reason="违规2", 
            violations=[
                ViolationItem(
                    rule_id="RULE_002",
                    location={"file": "test.py", "line": 15},
                    message="违规2"
                )
            ],
            call_id="call_2"
        )
        
        # 模拟聚合逻辑
        all_violations = result1.violations + result2.violations
        all_call_ids = [result1.call_id, result2.call_id]
        
        aggregated = AggregatedAnalyzeLLMResult(
            is_pass=False,
            reason="聚合了2个违规",
            violations=all_violations,
            code="def test(): pass",
            call_ids=all_call_ids,
            function_name="test_function"
        )
        
        self.assertFalse(aggregated.is_pass)
        self.assertEqual(len(aggregated.violations), 2)
        self.assertEqual(len(aggregated.call_ids), 2)
        self.assertEqual(aggregated.function_name, "test_function")


if __name__ == '__main__':
    unittest.main() 