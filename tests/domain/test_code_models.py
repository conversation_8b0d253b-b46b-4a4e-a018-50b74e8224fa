"""
领域代码模型单元测试

测试领域层的代码相关模型，包括CodeRange、Function、CodeElement等。
"""

import unittest
from unittest.mock import Mock

from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.external.code_analyzer.models.function import (Function, FunctionSignature,
                                              Parameter)


class TestCodeRange(unittest.TestCase):
    """测试代码范围模型"""
    
    def test_code_range_creation(self):
        """测试代码范围创建"""
        code_range = CodeRange(start_line=10, end_line=20)
        self.assertEqual(code_range.start_line, 10)
        self.assertEqual(code_range.end_line, 20)
    
    def test_code_range_validation(self):
        """测试代码范围验证"""
        # 正常情况
        code_range = CodeRange(start_line=1, end_line=10)
        self.assertEqual(code_range.start_line, 1)
        self.assertEqual(code_range.end_line, 10)
        
        # 测试无效的起始行
        with self.assertRaises(ValueError):
            CodeRange(start_line=0, end_line=10)
        
        # 测试结束行小于起始行
        with self.assertRaises(ValueError):
            CodeRange(start_line=20, end_line=10)
    
    def test_code_range_line_count(self):
        """测试代码范围行数计算"""
        code_range = CodeRange(start_line=10, end_line=20)
        self.assertEqual(code_range.line_count, 11)  # 包含起始和结束行
    
    def test_code_range_overlaps_with(self):
        """测试代码范围重叠"""
        range1 = CodeRange(start_line=10, end_line=20)
        range2 = CodeRange(start_line=15, end_line=25)
        range3 = CodeRange(start_line=25, end_line=30)
        
        self.assertTrue(range1.overlaps_with(range2))
        self.assertFalse(range1.overlaps_with(range3))
    
    def test_code_range_contains_line(self):
        """测试代码范围包含行"""
        code_range = CodeRange(start_line=10, end_line=20)
        
        self.assertTrue(code_range.contains_line(10))  # 起始行
        self.assertTrue(code_range.contains_line(15))  # 中间行
        self.assertTrue(code_range.contains_line(20))  # 结束行
        self.assertFalse(code_range.contains_line(9))   # 范围外
        self.assertFalse(code_range.contains_line(21))  # 范围外
    
    def test_code_range_get_affected_lines(self):
        """测试获取受影响的变更行"""
        code_range = CodeRange(start_line=10, end_line=20)
        changed_lines = [5, 10, 15, 25, 30]
        
        affected_lines = code_range.get_affected_lines(changed_lines)
        self.assertEqual(affected_lines, [10, 15])
    
    def test_code_range_string_representation(self):
        """测试代码范围字符串表示"""
        code_range = CodeRange(start_line=10, end_line=20)
        self.assertEqual(str(code_range), "10-20")


class TestFunction(unittest.TestCase):
    """测试函数模型"""
    
    def setUp(self):
        """设置测试环境"""
        self.code_range = CodeRange(start_line=10, end_line=20)
        self.signature = FunctionSignature(
            name="test_function",
            parameters=[Parameter("x", "int"), Parameter("y", "str")],
            return_type="bool"
        )
        self.function = Function(
            name="test_function",
            filepath="test.py",
            range=self.code_range,
            signature=self.signature,
            code="def test_function(x: int, y: str) -> bool:\n    pass",
            is_declaration=False
        )
    
    def test_function_creation(self):
        """测试函数创建"""
        self.assertEqual(self.function.name, "test_function")
        self.assertEqual(self.function.filepath, "test.py")
        self.assertEqual(self.function.range, self.code_range)
        self.assertEqual(self.function.signature, self.signature)
        self.assertFalse(self.function.is_declaration)
    
    def test_function_validation(self):
        """测试函数验证"""
        # 测试必需字段
        with self.assertRaises(Exception):
            Function(
                name="test_function",
                filepath="test.py",
                range=self.code_range,
                # 缺少signature字段
            )
    
    def test_function_contains_line(self):
        """测试函数包含行"""
        self.assertTrue(self.function.range.contains_line(15))
        self.assertFalse(self.function.range.contains_line(25))
    
    def test_function_complexity_analysis(self):
        """测试函数复杂度分析"""
        # 简单函数
        simple_function = Function(
            name="simple_func",
            filepath="test.py",
            range=CodeRange(start_line=1, end_line=5),
            signature=FunctionSignature(name="simple_func", parameters=[]),
            code="def simple_func():\n    pass"
        )
        
        # 复杂函数
        complex_function = Function(
            name="complex_func",
            filepath="test.py",
            range=CodeRange(start_line=1, end_line=50),
            signature=FunctionSignature(name="complex_func", parameters=[]),
            code="def complex_func():\n    # 很多行代码..."
        )
        
        # 验证行数差异
        self.assertLess(simple_function.range.line_count, complex_function.range.line_count)
    
    def test_function_is_compliant(self):
        """测试函数合规性"""
        # 这里可以添加合规性检查逻辑
        # 目前只是基本的结构测试
        self.assertIsNotNone(self.function.name)
        self.assertIsNotNone(self.function.filepath)
    
    def test_function_should_be_reviewed(self):
        """测试函数是否需要审查"""
        # 长函数可能需要审查
        long_function = Function(
            name="long_func",
            filepath="test.py",
            range=CodeRange(start_line=1, end_line=100),
            signature=FunctionSignature(name="long_func", parameters=[]),
            code="def long_func():\n    # 很长的函数..."
        )
        
        # 短函数可能不需要审查
        short_function = Function(
            name="short_func",
            filepath="test.py",
            range=CodeRange(start_line=1, end_line=5),
            signature=FunctionSignature(name="short_func", parameters=[]),
            code="def short_func():\n    pass"
        )
        
        # 验证行数差异
        self.assertGreater(long_function.range.line_count, short_function.range.line_count)
    
    def test_function_violation_management(self):
        """测试函数违规管理"""
        # 测试函数的基本属性
        self.assertIsNotNone(self.function.name)
        self.assertIsNotNone(self.function.filepath)
        self.assertIsNotNone(self.function.range)
    
    def test_function_create_simple(self):
        """测试简单函数创建方法"""
        signature = FunctionSignature(name="simple", parameters=[])
        function = Function.create_simple(
            name="simple",
            start_line=1,
            end_line=10,
            filepath="test.py",
            signature=signature,
            code="def simple():\n    pass"
        )
        
        self.assertEqual(function.name, "simple")
        self.assertEqual(function.range.start_line, 1)
        self.assertEqual(function.range.end_line, 10)
        self.assertEqual(function.filepath, "test.py")


class TestCodeElement(unittest.TestCase):
    """测试代码元素模型"""
    
    def test_code_element_creation(self):
        """测试代码元素创建"""
        element = CodeElement(
            name="test_element",
            type="function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test(): pass"
        )
        
        self.assertEqual(element.name, "test_element")
        self.assertEqual(element.type, "function")
        self.assertEqual(element.start_line, 10)
        self.assertEqual(element.end_line, 20)
        self.assertEqual(element.filepath, "test.py")
    
    def test_code_element_validation(self):
        """测试代码元素验证"""
        # 正常情况
        element = CodeElement(
            name="test",
            type="function",
            start_line=1,
            end_line=10,
            filepath="test.py",
            code="def test(): pass"
        )
        
        self.assertEqual(element.name, "test")
        self.assertEqual(element.type, "function")
        
        # 测试必需字段
        with self.assertRaises(Exception):
            CodeElement(
                name="test",
                type="function",
                # 缺少start_line和end_line
            )
    
    def test_code_element_equality(self):
        """测试代码元素相等性"""
        element1 = CodeElement(
            name="test",
            type="function",
            start_line=1,
            end_line=10,
            filepath="test.py",
            code="def test(): pass"
        )
        
        element2 = CodeElement(
            name="test",
            type="function",
            start_line=1,
            end_line=10,
            filepath="test.py",
            code="def test(): pass"
        )
        
        element3 = CodeElement(
            name="different",
            type="function",
            start_line=1,
            end_line=10,
            filepath="test.py",
            code="def different(): pass"
        )
        
        # 相同属性的元素应该相等
        self.assertEqual(element1.name, element2.name)
        self.assertEqual(element1.type, element2.type)
        self.assertEqual(element1.start_line, element2.start_line)
        
        # 不同名称的元素应该不相等
        self.assertNotEqual(element1.name, element3.name)
    
    def test_code_element_with_parent(self):
        """测试带父级的代码元素"""
        element = CodeElement(
            name="method",
            type="method",
            start_line=10,
            end_line=20,
            parent_name="MyClass",
            filepath="test.py",
            code="def method(self): pass"
        )
        
        self.assertEqual(element.name, "method")
        self.assertEqual(element.parent_name, "MyClass")
        self.assertEqual(element.type, "method")


class TestCodeModelsIntegration(unittest.TestCase):
    """测试代码模型集成"""
    
    def test_function_with_code_range(self):
        """测试函数与代码范围的集成"""
        code_range = CodeRange(start_line=10, end_line=20)
        signature = FunctionSignature(name="test_function", parameters=[])
        
        function = Function(
            name="test_function",
            filepath="test.py",
            range=code_range,
            signature=signature,
            code="def test_function():\n    pass",
            is_declaration=False
        )
        
        # 验证集成
        self.assertEqual(function.range, code_range)
        self.assertTrue(function.range.contains_line(15))
        self.assertEqual(function.range.line_count, 11)
    
    def test_function_compliance_with_violations(self):
        """测试函数合规性与违规的集成"""
        signature = FunctionSignature(name="test_function", parameters=[])
        function = Function(
            name="test_function",
            filepath="test.py",
            range=CodeRange(start_line=1, end_line=10),
            signature=signature,
            code="def test_function():\n    pass",
            is_declaration=False
        )
        
        # 验证基本合规性
        self.assertIsNotNone(function.name)
        self.assertIsNotNone(function.filepath)
        self.assertIsNotNone(function.range)
        self.assertIsNotNone(function.signature)
        
        # 验证范围有效性
        self.assertGreater(function.range.end_line, function.range.start_line)
        self.assertGreater(function.range.start_line, 0)


if __name__ == '__main__':
    unittest.main() 