"""
Repository领域模型单元测试

测试Repository模型的核心功能：
- 跨模块函数查找
- 跨模块调用映射
- 模块添加与查找
"""

import unittest

from gate_keeper.external.code_analyzer.models.call_relation import FunctionCall
from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.function import Function, FunctionSignature
from gate_keeper.external.code_analyzer.models.module import CodeModule
from gate_keeper.external.code_analyzer.models.repository import Repository


class TestRepositoryModel(unittest.TestCase):
    def setUp(self):
        self.repo = Repository(repo_dir="/repo", branch="main", commit_sha="abc123", modules={})
        # 创建两个模块
        self.module1 = CodeModule(
            path="mod1.c",
            functions=[
                Function(
                    name="foo",
                    range=CodeRange(start_line=1, end_line=10),
                    filepath="mod1.c",
                    signature=FunctionSignature(name="foo", parameters=[]),
                    code="void foo() {}",
                    is_declaration=False
                ),
                Function(
                    name="bar",
                    range=CodeRange(start_line=11, end_line=20),
                    filepath="mod1.c",
                    signature=FunctionSignature(name="bar", parameters=[]),
                    code="void bar() {}",
                    is_declaration=False
                )
            ],
            calls=[
                FunctionCall(
                    caller="foo",
                    callee="baz",
                    line=(5, 5),
                    file_path="mod1.c",
                    code="baz();"
                )
            ]
        )
        self.module2 = CodeModule(
            path="mod2.c",
            functions=[
                Function(
                    name="baz",
                    range=CodeRange(start_line=1, end_line=10),
                    filepath="mod2.c",
                    signature=FunctionSignature(name="baz", parameters=[]),
                    code="void baz() {}",
                    is_declaration=False
                )
            ],
            calls=[]
        )
        self.repo.add_module(self.module1)
        self.repo.add_module(self.module2)

    def test_get_module(self):
        mod = self.repo.get_module("mod1.c")
        self.assertIsNotNone(mod)
        self.assertEqual(mod.path, "mod1.c")
        self.assertEqual(len(mod.functions), 2)
        self.assertEqual(mod.functions[0].name, "foo")
        self.assertIsNone(self.repo.get_module("not_exist.c"))

    def test_find_function_definition(self):
        # 存在的函数
        path = self.repo.find_function_definition("baz")
        self.assertEqual(path, "mod2.c")
        # 不存在的函数
        self.assertIsNone(self.repo.find_function_definition("not_exist"))

    def test_add_module(self):
        new_mod = CodeModule(path="mod3.c", functions=[], calls=[])
        self.repo.add_module(new_mod)
        self.assertIn("mod3.c", self.repo.modules)
        self.assertEqual(self.repo.get_module("mod3.c"), new_mod)

    def test_build_cross_module_call_map(self):
        call_map = self.repo.build_cross_module_call_map()
        self.assertEqual(len(call_map), 1)
        cross_call = call_map[0]
        self.assertEqual(cross_call["caller"], "foo")
        self.assertEqual(cross_call["caller_file"], "mod1.c")
        self.assertEqual(cross_call["callee"], "baz")
        self.assertEqual(cross_call["callee_file"], "mod2.c")

if __name__ == '__main__':
    unittest.main() 