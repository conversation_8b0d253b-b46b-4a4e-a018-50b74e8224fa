"""
RepositoryIndex单元测试

测试仓库索引系统的核心功能：
- 调用图构建
- 函数索引管理
- 路径转换
- 序列化/反序列化
"""

import os
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

import networkx as nx

from gate_keeper.external.code_analyzer import RepositoryIndex
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestRepositoryIndex(unittest.TestCase):
    """RepositoryIndex测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.repo_dir = "/test/repo"
        self.branch = "main"
        self.mock_git_service = Mock()
        
        self.repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch=self.branch
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.repo_index.repo_dir, self.repo_dir)
        self.assertEqual(self.repo_index.branch, self.branch)
        # git_service 参数已移除，不再测试
        self.assertIsInstance(self.repo_index.call_graph, nx.DiGraph)
        self.assertEqual(len(self.repo_index.function_definitions), 0)
        self.assertEqual(len(self.repo_index.function_calls), 0)
    
    def test_to_rel_path(self):
        """测试路径转换为相对路径"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 测试绝对路径转换
        abs_path = "/tmp/test_repo/src/main.py"
        rel_path = repo_index.to_rel_path(abs_path)
        self.assertEqual(rel_path, "src/main.py")
        
        # 测试不在仓库内的路径返回原路径
        outside_path = "/other/path/file.py"
        outside_rel_path = repo_index.to_rel_path(outside_path)
        self.assertEqual(outside_rel_path, outside_path)
    
    def test_normalize_path(self):
        """测试路径标准化函数"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 测试绝对路径标准化
        abs_path = "/tmp/test_repo/src/main.py"
        norm_path = repo_index.normalize_path(abs_path)
        self.assertEqual(norm_path, "src/main.py")
        
        # 测试相对路径保持不变
        rel_path = "src/main.py"
        norm_path = repo_index.normalize_path(rel_path)
        self.assertEqual(norm_path, "src/main.py")
        
        # 测试空路径
        empty_path = ""
        norm_path = repo_index.normalize_path(empty_path)
        self.assertEqual(norm_path, "")
        
        # 测试None路径
        none_path = None
        norm_path = repo_index.normalize_path(none_path)
        self.assertEqual(norm_path, None)
    
    def test_paths_equal(self):
        """测试路径比较函数"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 测试相同路径的不同表示形式
        abs_path = "/tmp/test_repo/src/main.py"
        rel_path = "src/main.py"
        self.assertTrue(repo_index.paths_equal(abs_path, rel_path))
        self.assertTrue(repo_index.paths_equal(rel_path, abs_path))
        
        # 测试相同相对路径
        rel_path1 = "src/main.py"
        rel_path2 = "src/main.py"
        self.assertTrue(repo_index.paths_equal(rel_path1, rel_path2))
        
        # 测试不同路径
        path1 = "src/main.py"
        path2 = "src/other.py"
        self.assertFalse(repo_index.paths_equal(path1, path2))
        
        # 测试空路径
        self.assertTrue(repo_index.paths_equal("", ""))
        self.assertFalse(repo_index.paths_equal("src/main.py", ""))
        self.assertFalse(repo_index.paths_equal("", "src/main.py"))
        
        # 测试None路径
        self.assertTrue(repo_index.paths_equal(None, None))
        self.assertFalse(repo_index.paths_equal("src/main.py", None))
        self.assertFalse(repo_index.paths_equal(None, "src/main.py"))
    
    def test_path_bug_fix(self):
        """测试路径bug修复：确保绝对路径和相对路径的函数匹配都能正常工作"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
                # 模拟函数定义（使用相对路径）
        from gate_keeper.external.code_analyzer.models.code_range import \
            CodeRange
        from gate_keeper.external.code_analyzer.models.function import (
            Function, FunctionSignature, Parameter)

        # 创建测试函数
        func_range = CodeRange(start_line=1, end_line=10)
        signature = FunctionSignature(
            name="test_function",
            parameters=[Parameter(name="param1", type_hint="str")],
            return_type="None"
        )
        
        # 函数使用相对路径
        func = Function(
            name="test_function",
            range=func_range,
            signature=signature,
            filepath="src/main.py",  # 相对路径
            code="def test_function(param1): pass",
            is_declaration=False
        )
        
        # 添加到函数定义中
        repo_index.function_definitions["test_function"] = [func]
        
        # 测试1：使用绝对路径查找函数
        abs_filepath = "/tmp/test_repo/src/main.py"
        found_func = repo_index.find_function_definition("test_function", abs_filepath)
        self.assertIsNotNone(found_func)
        self.assertEqual(found_func.name, "test_function")
        
        # 测试2：使用相对路径查找函数
        rel_filepath = "src/main.py"
        found_func = repo_index.find_function_definition("test_function", rel_filepath)
        self.assertIsNotNone(found_func)
        self.assertEqual(found_func.name, "test_function")
        
        # 测试3：使用绝对路径获取受影响的函数
        affected_functions = repo_index.get_changed_functions("", abs_filepath)
        self.assertEqual(len(affected_functions), 1)
        self.assertEqual(affected_functions[0].name, "test_function")
        
        # 测试4：使用相对路径获取受影响的函数
        affected_functions = repo_index.get_changed_functions("", rel_filepath)
        self.assertEqual(len(affected_functions), 1)
        self.assertEqual(affected_functions[0].name, "test_function")
    
    def test_to_abs_path(self):
        """测试相对路径转绝对路径"""
        # 正常情况
        rel_path = "src/main.c"
        abs_path = self.repo_index.to_abs_path(rel_path)
        expected_path = str(Path(self.repo_dir) / rel_path)
        self.assertEqual(abs_path, expected_path)
        
        # 已经是绝对路径
        abs_input = "/test/repo/src/main.c"
        abs_output = self.repo_index.to_abs_path(abs_input)
        self.assertEqual(abs_output, abs_input)
    
    def test_get_node_id(self):
        """测试节点ID生成"""
        filepath = "src/main.c"
        func_name = "main"
        node_id = self.repo_index._get_node_id(filepath, func_name)
        self.assertEqual(node_id, f"{filepath}::{func_name}")
        
        # 测试绝对路径也会被正确转换为相对路径
        abs_filepath = os.path.join(self.repo_dir, "src", "main.c")
        node_id_abs = self.repo_index._get_node_id(abs_filepath, func_name)
        self.assertEqual(node_id_abs, f"{filepath}::{func_name}")
    
    def test_index_module(self):
        """测试模块索引"""
        # 创建测试函数
        func1 = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="/test/repo/src/main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { return 0; }"
        )
        
        func2 = Function.create_simple(
            name="helper",
            start_line=15,
            end_line=25,
            filepath="/test/repo/src/helper.c",
            signature=FunctionSignature(name="helper", parameters=[]),
            code="void helper() {}"
        )
        
        # 创建测试调用关系
        call1 = FunctionCall(
            caller="main",
            callee="helper",
            line=(5, 5),
            file_path="/test/repo/src/main.c",
            code="helper();"
        )
        
        # 使用新的构建方法
        # 注意：新的RepositoryIndex使用build()方法，而不是index_module
        # 这里我们直接设置内部状态来模拟索引结果
        self.repo_index.function_definitions["main"] = [func1]
        self.repo_index.function_calls = [call1]
        
        # 手动构建调用图
        main_node_id = "src/main.c:function:main"
        helper_node_id = "src/helper.c:function:helper"
        self.repo_index.call_graph.add_node(main_node_id, name="main", filepath="src/main.c")
        self.repo_index.call_graph.add_node(helper_node_id, name="helper", filepath="src/helper.c")
        self.repo_index.call_graph.add_edge(main_node_id, helper_node_id)
        
        # 验证函数定义
        self.assertIn("main", self.repo_index.function_definitions)
        self.assertEqual(len(self.repo_index.function_definitions["main"]), 1)
        
        # 验证调用关系
        self.assertEqual(len(self.repo_index.function_calls), 1)
        self.assertEqual(self.repo_index.function_calls[0].caller, "main")
        self.assertEqual(self.repo_index.function_calls[0].callee, "helper")
        
        # 验证调用图节点
        main_node_id = "src/main.c:function:main"
        self.assertTrue(self.repo_index.call_graph.has_node(main_node_id))
        node_data = self.repo_index.call_graph.nodes[main_node_id]
        self.assertEqual(node_data["name"], "main")
        self.assertEqual(node_data["filepath"], "src/main.c")
    
    def test_find_function_definitions(self):
        """测试函数定义查找"""
        # 添加测试函数
        func1 = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="/test/repo/src/main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { return 0; }"
        )
        
        func2 = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="/test/repo/src/main.h",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main();"
        )
        
        self.repo_index.function_definitions = {"main": [func1, func2]}
        
        # 查找函数定义
        definition = self.repo_index.find_function_definition("main")
        self.assertIsNotNone(definition)
        self.assertEqual(definition.name, "main")
        
        # 查找不存在的函数
        not_found = self.repo_index.find_function_definition("nonexistent")
        self.assertIsNone(not_found)
    
    def test_get_related_calls(self):
        """测试相关调用查找"""
        # 添加测试调用关系
        call1 = FunctionCall(
            caller="main",
            callee="helper",
            line=(5, 5),
            file_path="/test/repo/src/main.c",
            code="helper();"
        )
        
        call2 = FunctionCall(
            caller="helper",
            callee="util",
            line=(10, 10),
            file_path="/test/repo/src/helper.c",
            code="util();"
        )
        
        self.repo_index.function_calls = [call1, call2]
        
        # 查找main函数的调用
        main_calls = self.repo_index.get_related_calls("main")
        self.assertEqual(len(main_calls), 1)
        self.assertEqual(main_calls[0].caller, "main")
        self.assertEqual(main_calls[0].callee, "helper")
        
        # 查找helper函数的调用
        helper_calls = self.repo_index.get_related_calls("helper")
        self.assertEqual(len(helper_calls), 2)  # helper作为caller和callee
        # 验证helper作为caller的调用
        caller_calls = [c for c in helper_calls if c.caller == "helper"]
        self.assertEqual(len(caller_calls), 1)
        self.assertEqual(caller_calls[0].callee, "util")
        # 验证helper作为callee的调用
        callee_calls = [c for c in helper_calls if c.callee == "helper"]
        self.assertEqual(len(callee_calls), 1)
        self.assertEqual(callee_calls[0].caller, "main")
    
    def test_serialization_deserialization(self):
        """测试序列化和反序列化"""
        # 创建测试数据
        func = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="/test/repo/src/main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { return 0; }"
        )
        
        call = FunctionCall(
            caller="main",
            callee="helper",
            line=(5, 5),
            file_path="/test/repo/src/main.c",
            code="helper();"
        )
        
        self.repo_index.function_definitions = {"main": [func]}
        self.repo_index.function_calls = [call]
        self.repo_index.commit_sha = "abc123"
        self.repo_index.exclude_patterns = ["*.tmp", "*.log"]
        
        # 验证基本属性
        self.assertEqual(self.repo_index.repo_dir, self.repo_dir)
        self.assertEqual(self.repo_index.branch, self.branch)
        self.assertEqual(self.repo_index.commit_sha, "abc123")
        self.assertEqual(self.repo_index.exclude_patterns, ["*.tmp", "*.log"])
        self.assertIn("main", self.repo_index.function_definitions)
        self.assertEqual(len(self.repo_index.function_calls), 1)
        self.assertIsInstance(self.repo_index.call_graph, nx.DiGraph)
    
    def test_call_graph_building(self):
        """测试调用图构建"""
        # 创建测试函数和调用关系
        func1 = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="/test/repo/src/main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { helper(); return 0; }"
        )
        
        func2 = Function.create_simple(
            name="helper",
            start_line=15,
            end_line=25,
            filepath="/test/repo/src/helper.c",
            signature=FunctionSignature(name="helper", parameters=[]),
            code="void helper() { util(); }"
        )
        
        func3 = Function.create_simple(
            name="util",
            start_line=30,
            end_line=35,
            filepath="/test/repo/src/util.c",
            signature=FunctionSignature(name="util", parameters=[]),
            code="void util() {}"
        )
        
        # 使用新的API直接设置数据
        self.repo_index.function_definitions = {
            "main": [func1],
            "helper": [func2],
            "util": [func3]
        }
        
        # 添加调用关系
        call1 = FunctionCall(
            caller="main",
            callee="helper",
            line=(5, 5),
            file_path="/test/repo/src/main.c",
            code="helper();"
        )
        
        call2 = FunctionCall(
            caller="helper",
            callee="util",
            line=(20, 20),
            file_path="/test/repo/src/helper.c",
            code="util();"
        )
        
        self.repo_index.function_calls = [call1, call2]
        
        # 手动构建调用图
        main_node_id = "src/main.c:function:main"
        helper_node_id = "src/helper.c:function:helper"
        util_node_id = "src/util.c:function:util"
        
        self.repo_index.call_graph.add_node(main_node_id, name="main", filepath="src/main.c", start_line=1, end_line=10, external=False)
        self.repo_index.call_graph.add_node(helper_node_id, name="helper", filepath="src/helper.c", start_line=15, end_line=25, external=False)
        self.repo_index.call_graph.add_node(util_node_id, name="util", filepath="src/util.c", start_line=30, end_line=35, external=False)
        
        self.repo_index.call_graph.add_edge(main_node_id, helper_node_id)
        self.repo_index.call_graph.add_edge(helper_node_id, util_node_id)
        
        # 验证调用图
        graph = self.repo_index.get_call_graph()
        

        
        # 检查节点
        self.assertTrue(graph.has_node("src/main.c:function:main"))
        self.assertTrue(graph.has_node("src/helper.c:function:helper"))
        self.assertTrue(graph.has_node("src/util.c:function:util"))
        
        # 检查边
        self.assertTrue(graph.has_edge("src/main.c:function:main", "src/helper.c:function:helper"))
        self.assertTrue(graph.has_edge("src/helper.c:function:helper", "src/util.c:function:util"))
        
        # 检查节点属性
        main_data = graph.nodes["src/main.c:function:main"]
        self.assertEqual(main_data["name"], "main")
        self.assertEqual(main_data["filepath"], "src/main.c")
        self.assertEqual(main_data["start_line"], 1)
        self.assertEqual(main_data["end_line"], 10)
        self.assertFalse(main_data["external"])
    
    def test_get_changed_functions(self):
        """测试获取变更函数"""
        # 添加测试函数到调用图
        func = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="src/main.c",
            signature=FunctionSignature(name="main", parameters=[]),
            code="int main() { return 0; }"
        )
        
        self.repo_index.function_definitions = {"main": [func]}
        
        # 测试文件内容
        file_content = """int main() {
    return 0;
}"""
        
        # 测试变更行
        changed_lines = [3, 4]
        affected_functions = self.repo_index.get_changed_functions(
            file_content=file_content,
            file_path="/test/repo/src/main.c",
            changed_lines=changed_lines
        )
        
        self.assertEqual(len(affected_functions), 1)
        af = affected_functions[0]
        self.assertEqual(af.name, "main")
        self.assertEqual(af.start_line, 1)
        self.assertEqual(af.end_line, 10)
        self.assertEqual(af.changed_lines, [3, 4])
        self.assertEqual(af.filepath, "src/main.c")
        
        # 测试无变更行
        affected_functions = self.repo_index.get_changed_functions(
            file_content=file_content,
            file_path="/test/repo/src/main.c",
            changed_lines=[15, 16]  # 不在函数范围内
        )
        self.assertEqual(len(affected_functions), 0)

    def test_cross_platform_path_handling(self):
        """测试跨平台路径处理"""
        # 测试Unix风格路径
        repo_index_unix = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # Unix风格的绝对路径
        unix_abs_path = "/tmp/test_repo/src/main.py"
        unix_rel_path = repo_index_unix.normalize_path(unix_abs_path)
        self.assertEqual(unix_rel_path, "src/main.py")
        
        # Unix风格的相对路径
        unix_rel_input = "src/main.py"
        unix_rel_output = repo_index_unix.normalize_path(unix_rel_input)
        self.assertEqual(unix_rel_output, "src/main.py")
        
        # 测试Windows风格路径（注意：这些路径不存在，所以会返回原路径）
        repo_index_windows = RepositoryIndex(repo_dir="C:\\tmp\\test_repo")
        
        # Windows风格的绝对路径（在仓库内，应该返回相对路径）
        windows_abs_path = "C:\\tmp\\test_repo\\src\\main.py"
        windows_rel_path = repo_index_windows.normalize_path(windows_abs_path)
        # 在仓库内的路径应该返回相对路径
        self.assertEqual(windows_rel_path, "src/main.py")
        
        # Windows风格的相对路径
        windows_rel_input = "src\\main.py"
        windows_rel_output = repo_index_windows.normalize_path(windows_rel_input)
        self.assertEqual(windows_rel_output, "src/main.py")
        
        # 测试混合路径比较
        self.assertTrue(repo_index_unix.paths_equal("/tmp/test_repo/src/main.py", "src/main.py"))
        # Windows路径比较（由于路径不存在，比较的是标准化后的路径）
        self.assertTrue(repo_index_windows.paths_equal("C:\\tmp\\test_repo\\src\\main.py", "C:/tmp/test_repo/src/main.py"))
    
    def test_path_consistency_protection(self):
        """测试路径一致性保护，防止路径格式不一致导致的bug"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 模拟函数定义（使用相对路径）
        from gate_keeper.external.code_analyzer.models.code_range import \
            CodeRange
        from gate_keeper.external.code_analyzer.models.function import (
            Function, FunctionSignature, Parameter)

        # 创建测试函数，使用相对路径
        func_range = CodeRange(start_line=1, end_line=10)
        signature = FunctionSignature(
            name="test_function",
            parameters=[Parameter(name="param1", type_hint="str")],
            return_type="None"
        )
        
        func = Function(
            name="test_function",
            range=func_range,
            signature=signature,
            filepath="src/main.py",  # 相对路径
            code="def test_function(param1): pass",
            is_declaration=False
        )
        
        repo_index.function_definitions["test_function"] = [func]
        
        # 测试各种路径格式都能正确匹配
        test_paths = [
            "src/main.py",                    # 相对路径
            "/tmp/test_repo/src/main.py",     # Unix绝对路径
            "src\\main.py",                   # Windows相对路径
            "./src/main.py",                  # 带./的相对路径
            "src//main.py",                   # 双斜杠路径
            "src\\\\main.py",                 # Windows双反斜杠路径
        ]
        
        for test_path in test_paths:
            # 测试函数查找
            found_func = repo_index.find_function_definition("test_function", test_path)
            self.assertIsNotNone(found_func, f"路径 {test_path} 应该能找到函数")
            self.assertEqual(found_func.name, "test_function")
            
            # 测试受影响函数检测
            affected_functions = repo_index.get_changed_functions("", test_path)
            self.assertEqual(len(affected_functions), 1, f"路径 {test_path} 应该能找到1个受影响函数")
            self.assertEqual(affected_functions[0].name, "test_function")
        
        # 测试Windows绝对路径（这个路径不在仓库内，所以不会匹配）
        windows_abs_path = "C:\\other\\repo\\src\\main.py"
        # 测试路径相等性（由于路径不在仓库内，应该不相等）
        self.assertFalse(repo_index.paths_equal("src/main.py", windows_abs_path), 
                        f"Windows绝对路径 {windows_abs_path} 不应该与相对路径相等")
        
        # 测试受影响函数检测（应该找不到，因为路径不匹配）
        affected_functions = repo_index.get_changed_functions("", windows_abs_path)
        self.assertEqual(len(affected_functions), 0, f"Windows绝对路径 {windows_abs_path} 不在仓库内，不应该找到受影响函数")
    
    def test_edge_case_path_handling(self):
        """测试边界情况的路径处理"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 测试空路径
        self.assertEqual(repo_index.normalize_path(""), "")
        self.assertEqual(repo_index.normalize_path(None), None)
        
        # 测试None路径比较
        self.assertTrue(repo_index.paths_equal(None, None))
        self.assertFalse(repo_index.paths_equal(None, "src/main.py"))
        self.assertFalse(repo_index.paths_equal("src/main.py", None))
        
        # 测试空字符串路径比较
        self.assertTrue(repo_index.paths_equal("", ""))
        self.assertFalse(repo_index.paths_equal("", "src/main.py"))
        self.assertFalse(repo_index.paths_equal("src/main.py", ""))
        
        # 测试根路径
        root_path = "/tmp/test_repo"
        root_rel = repo_index.normalize_path(root_path)
        self.assertEqual(root_rel, ".")
        
        # 测试当前目录
        current_dir = "/tmp/test_repo/."
        current_rel = repo_index.normalize_path(current_dir)
        self.assertEqual(current_rel, ".")
        
        # 测试上级目录
        parent_path = "/tmp/test_repo/../other"
        parent_rel = repo_index.normalize_path(parent_path)
        # 由于路径不在仓库内，应该返回原路径
        self.assertEqual(parent_rel, "/tmp/test_repo/../other")
    
    def test_path_normalization_edge_cases(self):
        """测试路径标准化的边界情况"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 测试带点号的路径
        dot_path = "/tmp/test_repo/src/./main.py"
        dot_rel = repo_index.normalize_path(dot_path)
        self.assertEqual(dot_rel, "src/main.py")
        
        # 测试带双点号的路径
        double_dot_path = "/tmp/test_repo/src/../src/main.py"
        double_dot_rel = repo_index.normalize_path(double_dot_path)
        self.assertEqual(double_dot_rel, "src/main.py")
        
        # 测试带多个斜杠的路径
        multi_slash_path = "/tmp/test_repo//src///main.py"
        multi_slash_rel = repo_index.normalize_path(multi_slash_path)
        self.assertEqual(multi_slash_rel, "src/main.py")
        
        # 测试带尾随斜杠的路径
        trailing_slash_path = "/tmp/test_repo/src/main.py/"
        trailing_slash_rel = repo_index.normalize_path(trailing_slash_path)
        self.assertEqual(trailing_slash_rel, "src/main.py")
        
        # 测试带前导斜杠的相对路径
        leading_slash_rel_path = "/src/main.py"
        leading_slash_rel = repo_index.normalize_path(leading_slash_rel_path)
        self.assertEqual(leading_slash_rel, "/src/main.py")  # 不在仓库内，返回原路径
    
    def test_windows_path_handling(self):
        """专门测试Windows路径处理"""
        repo_index = RepositoryIndex(repo_dir="C:\\tmp\\test_repo")
        
        # Windows绝对路径
        windows_paths = [
            "C:\\tmp\\test_repo\\src\\main.py",
            "C:/tmp/test_repo/src/main.py",  # 混合斜杠
            "C:\\tmp\\test_repo\\src\\\\main.py",  # 双反斜杠
            "C:\\tmp\\test_repo\\src\\subdir\\..\\main.py",  # 带..的路径
        ]
        
        for path in windows_paths:
            normalized = repo_index.normalize_path(path)
            self.assertEqual(normalized, "src/main.py", f"路径 {path} 应该标准化为 src/main.py")
        
        # Windows相对路径
        windows_rel_paths = [
            "src\\main.py",
            "src/main.py",  # 混合斜杠
            ".\\src\\main.py",
            "src\\\\main.py",  # 双反斜杠
        ]
        
        for path in windows_rel_paths:
            normalized = repo_index.normalize_path(path)
            self.assertEqual(normalized, "src/main.py", f"相对路径 {path} 应该标准化为 src/main.py")
    
    def test_unix_path_handling(self):
        """专门测试Unix路径处理"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # Unix绝对路径
        unix_paths = [
            "/tmp/test_repo/src/main.py",
            "/tmp/test_repo//src//main.py",  # 双斜杠
            "/tmp/test_repo/src/./main.py",  # 带.的路径
            "/tmp/test_repo/src/subdir/../main.py",  # 带..的路径
        ]
        
        for path in unix_paths:
            normalized = repo_index.normalize_path(path)
            self.assertEqual(normalized, "src/main.py", f"路径 {path} 应该标准化为 src/main.py")
        
        # Unix相对路径
        unix_rel_paths = [
            "src/main.py",
            "./src/main.py",
            "src//main.py",  # 双斜杠
        ]
        
        for path in unix_rel_paths:
            normalized = repo_index.normalize_path(path)
            self.assertEqual(normalized, "src/main.py", f"相对路径 {path} 应该标准化为 src/main.py")
    
    def test_path_equality_comprehensive(self):
        """综合测试路径相等性比较"""
        repo_index = RepositoryIndex(repo_dir="/tmp/test_repo")
        
        # 测试相同路径的不同表示形式
        path_variations = [
            "src/main.py",
            "/tmp/test_repo/src/main.py",
            "C:\\tmp\\test_repo\\src\\main.py",
            "src\\main.py",
            "./src/main.py",
            "src//main.py",
            "src\\\\main.py",
        ]
        
        # 所有变体都应该相等
        for i, path1 in enumerate(path_variations):
            for j, path2 in enumerate(path_variations):
                if i != j:
                    self.assertTrue(
                        repo_index.paths_equal(path1, path2),
                        f"路径 {path1} 和 {path2} 应该相等"
                    )
        
        # 测试不同路径不应该相等
        different_paths = [
            "src/main.py",
            "src/other.py",
            "other/main.py",
            "src/main.py.bak",
        ]
        
        for i, path1 in enumerate(different_paths):
            for j, path2 in enumerate(different_paths):
                if i != j:
                    self.assertFalse(
                        repo_index.paths_equal(path1, path2),
                        f"路径 {path1} 和 {path2} 不应该相等"
                    )


if __name__ == '__main__':
    unittest.main() 