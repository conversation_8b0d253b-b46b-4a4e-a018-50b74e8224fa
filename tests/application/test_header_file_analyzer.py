#!/usr/bin/env python3
"""
头文件分析器测试
"""

import os
import tempfile
import unittest
from pathlib import Path

from gate_keeper.application.service.analysis.header_file_analyzer import (
    HeaderFileAnalyzer, HeaderFileContent)


class TestHeaderFileAnalyzer(unittest.TestCase):
    """头文件分析器测试类"""
    
    def setUp(self):
        self.analyzer = HeaderFileAnalyzer()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_analyze_header_file_basic(self):
        """测试基本的头文件分析"""
        # 创建测试头文件
        header_content = """
#ifndef TEST_HEADER_H
#define TEST_HEADER_H

#include <stdio.h>
#include <stdlib.h>

// 常量定义
#define MAX_SIZE 100
#define DEFAULT_TIMEOUT 30

// 类型定义
typedef struct {
    int id;
    char name[50];
} User;

// 函数声明
void init_user(User* user, int id, const char* name);
void print_user(const User* user);
int validate_user(const User* user);

// 内联函数
static inline int is_valid_id(int id) {
    return id > 0 && id < MAX_SIZE;
}

// 宏函数
#define SQUARE(x) ((x) * (x))
#define MAX(a, b) ((a) > (b) ? (a) : (b))

#endif
"""
        
        header_file = os.path.join(self.temp_dir, "test.h")
        with open(header_file, 'w') as f:
            f.write(header_content)
        
        # 分析头文件
        result = self.analyzer.analyze_header_file(header_file)
        
        # 验证结果
        self.assertIsInstance(result, HeaderFileContent)
        self.assertEqual(result.file_path, header_file)
        
        # 验证函数声明
        self.assertEqual(len(result.function_declarations), 3)
        function_names = [f.name for f in result.function_declarations]
        self.assertIn("init_user", function_names)
        self.assertIn("print_user", function_names)
        self.assertIn("validate_user", function_names)
        
        # 验证内联函数
        self.assertEqual(len(result.inline_functions), 1)
        self.assertEqual(result.inline_functions[0].name, "is_valid_id")
        
        # 验证宏定义
        self.assertGreater(len(result.macros), 0)
        macro_names = [m.name for m in result.macros]
        self.assertIn("MAX_SIZE", macro_names)
        self.assertIn("DEFAULT_TIMEOUT", macro_names)
        self.assertIn("SQUARE", macro_names)
        self.assertIn("MAX", macro_names)
        
        # 验证结构体定义
        self.assertEqual(len(result.structs), 1)
        self.assertEqual(result.structs[0].name, "User")
        
        # 验证头文件包含
        self.assertIn("stdio.h", result.includes)
        self.assertIn("stdlib.h", result.includes)
    
    def test_get_checkable_content(self):
        """测试获取可检查内容"""
        # 创建测试头文件
        header_content = """
#ifndef TEST_HEADER_H
#define TEST_HEADER_H

#include <stdio.h>

#define MAX_SIZE 100
#define SQUARE(x) ((x) * (x))

typedef struct {
    int id;
    char name[50];
} User;

static inline int is_valid_id(int id) {
    return id > 0 && id < MAX_SIZE;
}

void test_function(void);

#endif
"""
        
        header_file = os.path.join(self.temp_dir, "test.h")
        with open(header_file, 'w') as f:
            f.write(header_content)
        
        # 分析头文件
        header_content_obj = self.analyzer.analyze_header_file(header_file)
        
        # 获取可检查内容
        checkable_items = self.analyzer.get_checkable_content(header_content_obj)
        
        # 验证结果
        self.assertGreater(len(checkable_items), 0)
        
        # 验证内容类型
        item_types = [item['type'] for item in checkable_items]
        self.assertIn('inline_function', item_types)
        self.assertIn('macro', item_types)
        self.assertIn('struct', item_types)
        self.assertIn('include', item_types)
        
        # 验证不应该包含函数声明
        self.assertNotIn('test_function', [item['name'] for item in checkable_items])
    
    def test_should_skip_function(self):
        """测试函数跳过判断"""
        # 创建测试头文件
        header_content = """
#ifndef TEST_HEADER_H
#define TEST_HEADER_H

// 函数声明
void test_declaration(void);

// 内联函数
static inline int test_inline(int x) {
    return x * 2;
}

// 普通函数定义（在头文件中较少见）
int test_definition(int x) {
    return x + 1;
}

#endif
"""
        
        header_file = os.path.join(self.temp_dir, "test.h")
        with open(header_file, 'w') as f:
            f.write(header_content)
        
        # 分析头文件
        header_content_obj = self.analyzer.analyze_header_file(header_file)
        

        
        # 验证函数声明应该被跳过
        for func in header_content_obj.function_declarations:
            self.assertTrue(self.analyzer.should_skip_function(func))
        
        # 验证真正的内联函数不应该被跳过
        for func in header_content_obj.inline_functions:
            if self.analyzer._is_inline_function(func):
                self.assertFalse(self.analyzer.should_skip_function(func))
            else:
                # 非内联函数在头文件中应该被跳过
                self.assertTrue(self.analyzer.should_skip_function(func))
    
    def test_non_header_file_error(self):
        """测试非头文件错误处理"""
        # 创建非头文件
        c_file = os.path.join(self.temp_dir, "test.c")
        with open(c_file, 'w') as f:
            f.write("int main() { return 0; }")
        
        # 应该抛出错误
        with self.assertRaises(ValueError):
            self.analyzer.analyze_header_file(c_file)
    
    def test_extract_includes(self):
        """测试头文件包含提取"""
        content = """
#include <stdio.h>
#include <stdlib.h>
#include "local_header.h"
#include "another_header.h"
#include <system/header.h>

#define MAX_SIZE 100

int main() {
    return 0;
}
"""
        
        includes = self.analyzer._extract_includes(content)
        
        # 验证结果
        self.assertIn("stdio.h", includes)
        self.assertIn("stdlib.h", includes)
        self.assertIn("local_header.h", includes)
        self.assertIn("another_header.h", includes)
        self.assertIn("system/header.h", includes)
        self.assertEqual(len(includes), 5)


if __name__ == '__main__':
    unittest.main() 