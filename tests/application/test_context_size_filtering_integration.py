"""
Test Context Size Filtering Integration

测试上下文大小过滤的集成场景
"""

import unittest
from unittest.mock import Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import (
    CallChainContext, ContextManager, ContextManagerConfig)
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestContextSizeFilteringIntegration(unittest.TestCase):
    """测试上下文大小过滤的集成场景"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的静态分析器
        self.mock_static_analyzer = Mock()
        self.mock_static_analyzer.repo_index = Mock()
        self.mock_static_analyzer.repo_index.function_definitions = {}
        self.mock_static_analyzer.repo_index.function_calls = []
        
        # 创建测试配置
        self.config = ContextManagerConfig(
            max_context_size=2000,  # 设置较小的上下文大小限制
            max_chains=3,
            log_context_building=True
        )
        
        # 创建上下文管理器
        self.context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            config=self.config
        )
        
        # 创建测试的受影响函数
        self.test_af = AffectedFunction(
            name="test_function",
            start_line=10,
            end_line=20,
            filepath="test.py",
            code="def test_function():\n    pass",
            changed_lines=[15],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
    
    def test_context_size_filtering_in_real_scenario(self):
        """测试真实场景中的上下文大小过滤"""
        # 模拟调用链
        mock_chains = [
            ["main", "test_function", "helper"],
            ["init", "test_function", "validate"],
            ["process", "test_function", "format"]
        ]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 创建不同大小的函数定义
        small_func = Function.create_simple(
            name="small_func",
            start_line=1,
            end_line=5,
            filepath="small.py",
            signature=FunctionSignature(name="small_func", parameters=[]),
            code="def small_func():\n    pass"
        )
        
        medium_func = Function.create_simple(
            name="medium_func",
            start_line=1,
            end_line=20,
            filepath="medium.py",
            signature=FunctionSignature(name="medium_func", parameters=[]),
            code="def medium_func():\n    " + "\n    ".join([f"x = {i}" for i in range(20)])
        )
        
        large_func = Function.create_simple(
            name="large_func",
            start_line=1,
            end_line=100,
            filepath="large.py",
            signature=FunctionSignature(name="large_func", parameters=[]),
            code="def large_func():\n    " + "\n    ".join([f"x = {i}" for i in range(100)])
        )
        
        # 设置函数定义映射
        self.mock_static_analyzer.repo_index.function_definitions = {
            "main": [small_func],
            "helper": [small_func],
            "init": [medium_func],
            "validate": [medium_func],
            "process": [large_func],
            "format": [large_func]
        }
        
        # 测试生成优化上下文
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=3)
        
        # 验证结果
        self.assertGreater(len(contexts), 0)
        self.assertLessEqual(len(contexts), 3)
        
        # 验证总大小不超过限制
        total_size = sum(ctx.context_size for ctx in contexts)
        self.assertLessEqual(total_size, 2000)
        
        # 验证选择了最优的上下文组合
        for context in contexts:
            self.assertGreater(context.relevance_score, 0)
    
    def test_context_size_filtering_with_extremely_large_functions(self):
        """测试包含极大函数的上下文大小过滤"""
        # 模拟调用链
        mock_chains = [
            ["main", "test_function", "huge_func"],
            ["init", "test_function", "small_func"]
        ]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 创建极大函数
        huge_code = "def huge_func():\n    " + "\n    ".join([f"x = {i}" for i in range(1000)])
        huge_func = Function.create_simple(
            name="huge_func",
            start_line=1,
            end_line=1000,
            filepath="huge.py",
            signature=FunctionSignature(name="huge_func", parameters=[]),
            code=huge_code
        )
        
        small_func = Function.create_simple(
            name="small_func",
            start_line=1,
            end_line=5,
            filepath="small.py",
            signature=FunctionSignature(name="small_func", parameters=[]),
            code="def small_func():\n    pass"
        )
        
        # 设置函数定义映射
        self.mock_static_analyzer.repo_index.function_definitions = {
            "main": [small_func],
            "huge_func": [huge_func],
            "init": [small_func],
            "small_func": [small_func]
        }
        
        # 测试生成优化上下文
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=2)
        
        # 验证结果
        self.assertGreater(len(contexts), 0)
        
        # 验证没有包含极大函数的上下文
        for context in contexts:
            total_size = sum(len(func.code) for func in context.functions)
            self.assertLessEqual(total_size, 2000)
    
    def test_context_size_filtering_with_mixed_sizes(self):
        """测试混合大小函数的上下文大小过滤"""
        # 模拟调用链
        mock_chains = [
            ["main", "test_function", "func1"],
            ["init", "test_function", "func2"],
            ["process", "test_function", "func3"]
        ]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 创建不同大小的函数
        func1 = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=10,
            filepath="func1.py",
            signature=FunctionSignature(name="func1", parameters=[]),
            code="def func1():\n    " + "\n    ".join([f"x = {i}" for i in range(10)])
        )
        
        func2 = Function.create_simple(
            name="func2",
            start_line=1,
            end_line=50,
            filepath="func2.py",
            signature=FunctionSignature(name="func2", parameters=[]),
            code="def func2():\n    " + "\n    ".join([f"x = {i}" for i in range(50)])
        )
        
        func3 = Function.create_simple(
            name="func3",
            start_line=1,
            end_line=100,
            filepath="func3.py",
            signature=FunctionSignature(name="func3", parameters=[]),
            code="def func3():\n    " + "\n    ".join([f"x = {i}" for i in range(100)])
        )
        
        # 设置函数定义映射
        self.mock_static_analyzer.repo_index.function_definitions = {
            "main": [func1],
            "func1": [func1],
            "init": [func2],
            "func2": [func2],
            "process": [func3],
            "func3": [func3]
        }
        
        # 测试生成优化上下文
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=3)
        
        # 验证结果
        self.assertGreater(len(contexts), 0)
        self.assertLessEqual(len(contexts), 3)
        
        # 验证总大小不超过限制
        total_size = sum(ctx.context_size for ctx in contexts)
        self.assertLessEqual(total_size, 2000)
        
        # 验证选择了最优的组合
        selected_sizes = [ctx.context_size for ctx in contexts]
        self.assertTrue(all(size <= 2000 for size in selected_sizes))
    
    def test_context_size_filtering_edge_case_all_large(self):
        """测试边界情况：所有函数都很大"""
        # 模拟调用链
        mock_chains = [
            ["main", "test_function", "large_func1"],
            ["init", "test_function", "large_func2"]
        ]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 创建大函数
        large_code = "def large_func():\n    " + "\n    ".join([f"x = {i}" for i in range(500)])
        large_func1 = Function.create_simple(
            name="large_func1",
            start_line=1,
            end_line=500,
            filepath="large1.py",
            signature=FunctionSignature(name="large_func1", parameters=[]),
            code=large_code
        )
        
        large_func2 = Function.create_simple(
            name="large_func2",
            start_line=1,
            end_line=500,
            filepath="large2.py",
            signature=FunctionSignature(name="large_func2", parameters=[]),
            code=large_code
        )
        
        # 设置函数定义映射
        self.mock_static_analyzer.repo_index.function_definitions = {
            "main": [large_func1],
            "large_func1": [large_func1],
            "init": [large_func2],
            "large_func2": [large_func2]
        }
        
        # 测试生成优化上下文
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=2)
        
        # 验证结果
        # 由于所有函数都很大，可能返回空列表或者只包含部分上下文
        if contexts:
            total_size = sum(ctx.context_size for ctx in contexts)
            self.assertLessEqual(total_size, 2000)
    
    def test_context_size_filtering_performance_with_large_chains(self):
        """测试大调用链的性能和大小过滤"""
        # 创建大量调用链
        large_chains = []
        for i in range(50):
            chain = [f"func{j}" for j in range(i + 1)]
            large_chains.append(chain)
        
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = large_chains
        
        # 创建不同大小的函数
        small_func = Function.create_simple(
            name="small_func",
            start_line=1,
            end_line=5,
            filepath="small.py",
            signature=FunctionSignature(name="small_func", parameters=[]),
            code="def small_func():\n    pass"
        )
        
        medium_func = Function.create_simple(
            name="medium_func",
            start_line=1,
            end_line=30,
            filepath="medium.py",
            signature=FunctionSignature(name="medium_func", parameters=[]),
            code="def medium_func():\n    " + "\n    ".join([f"x = {i}" for i in range(30)])
        )
        
        # 设置函数定义映射
        self.mock_static_analyzer.repo_index.function_definitions = {
            f"func{i}": [small_func if i % 2 == 0 else medium_func] for i in range(100)
        }
        
        # 测试性能
        import time
        start_time = time.time()
        
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=5)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证执行时间合理（小于3秒）
        self.assertLess(execution_time, 3.0)
        
        # 验证结果正确
        self.assertLessEqual(len(contexts), 5)
        if contexts:
            total_size = sum(ctx.context_size for ctx in contexts)
            self.assertLessEqual(total_size, 2000)
    
    def test_context_size_filtering_with_different_strategies(self):
        """测试不同策略下的上下文大小过滤效果"""
        # 模拟调用链
        mock_chains = [
            ["main", "test_function", "func1"],
            ["init", "test_function", "func2"],
            ["process", "test_function", "func3"]
        ]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 创建不同大小的函数
        func1 = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=20,
            filepath="func1.py",
            signature=FunctionSignature(name="func1", parameters=[]),
            code="def func1():\n    " + "\n    ".join([f"x = {i}" for i in range(20)])
        )
        
        func2 = Function.create_simple(
            name="func2",
            start_line=1,
            end_line=40,
            filepath="func2.py",
            signature=FunctionSignature(name="func2", parameters=[]),
            code="def func2():\n    " + "\n    ".join([f"x = {i}" for i in range(40)])
        )
        
        func3 = Function.create_simple(
            name="func3",
            start_line=1,
            end_line=60,
            filepath="func3.py",
            signature=FunctionSignature(name="func3", parameters=[]),
            code="def func3():\n    " + "\n    ".join([f"x = {i}" for i in range(60)])
        )
        
        # 设置函数定义映射
        self.mock_static_analyzer.repo_index.function_definitions = {
            "main": [func1],
            "func1": [func1],
            "init": [func2],
            "func2": [func2],
            "process": [func3],
            "func3": [func3]
        }
        
        results = {}
        
        # 测试相关性优先策略
        self.config.context_selection_strategy = "relevance_first"
        contexts_relevance = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=3)
        results["relevance_first"] = {
            "count": len(contexts_relevance),
            "total_size": sum(ctx.context_size for ctx in contexts_relevance) if contexts_relevance else 0
        }
        
        # 测试大小优先策略
        self.config.context_selection_strategy = "size_first"
        contexts_size = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=3)
        results["size_first"] = {
            "count": len(contexts_size),
            "total_size": sum(ctx.context_size for ctx in contexts_size) if contexts_size else 0
        }
        
        # 测试平衡策略
        self.config.context_selection_strategy = "balanced"
        contexts_balanced = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=3)
        results["balanced"] = {
            "count": len(contexts_balanced),
            "total_size": sum(ctx.context_size for ctx in contexts_balanced) if contexts_balanced else 0
        }
        
        # 验证所有策略都遵守大小限制
        for strategy, result in results.items():
            self.assertLessEqual(result["total_size"], 2000)
            self.assertGreaterEqual(result["count"], 0)
        
        # 验证大小优先策略的总大小最小
        if results["size_first"]["count"] > 0 and results["relevance_first"]["count"] > 0:
            self.assertLessEqual(
                results["size_first"]["total_size"],
                results["relevance_first"]["total_size"]
            )


if __name__ == '__main__':
    unittest.main() 