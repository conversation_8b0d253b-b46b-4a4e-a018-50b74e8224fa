"""
Test Context Size Filtering

测试上下文大小过滤功能
"""

import unittest
from unittest.mock import Mock, patch

from gate_keeper.application.service.context_management import (
    CallChainContext, ContextManagerConfig, ContextSelector)


class TestContextSizeFiltering(unittest.TestCase):
    """测试上下文大小过滤功能"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试配置
        self.config = ContextManagerConfig(
            max_context_size=1000,  # 设置较小的上下文大小限制
            max_chains=3,
            log_context_building=True
        )
        
        # 创建上下文选择器
        self.selector = ContextSelector(self.config)
    
    def test_context_size_filtering_relevance_first(self):
        """测试相关性优先策略下的上下文大小过滤"""
        # 创建测试上下文，包含不同大小的上下文
        contexts = [
            CallChainContext(
                chain=["func1", "func2"],
                functions=[],
                relevance_score=0.9,  # 高相关性
                context_size=800      # 大上下文
            ),
            CallChainContext(
                chain=["func3", "func4"],
                functions=[],
                relevance_score=0.7,  # 中等相关性
                context_size=300      # 中等上下文
            ),
            CallChainContext(
                chain=["func5", "func6"],
                functions=[],
                relevance_score=0.8,  # 高相关性
                context_size=400      # 中等上下文
            ),
            CallChainContext(
                chain=["func7", "func8"],
                functions=[],
                relevance_score=0.6,  # 低相关性
                context_size=200      # 小上下文
            )
        ]
        
        # 设置相关性优先策略
        self.config.context_selection_strategy = "relevance_first"
        
        # 测试选择（总大小限制1000）
        selected = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        
        # 验证结果
        self.assertEqual(len(selected), 2)  # 只能选择2个上下文（800 + 200 = 1000）
        
        # 验证选择了相关性最高的上下文
        self.assertEqual(selected[0].relevance_score, 0.9)  # func1->func2
        self.assertEqual(selected[1].relevance_score, 0.6)  # func7->func8 (因为func5->func6被大小限制过滤了)
        
        # 验证总大小不超过限制
        total_size = sum(ctx.context_size for ctx in selected)
        self.assertLessEqual(total_size, 1000)
    
    def test_context_size_filtering_size_first(self):
        """测试大小优先策略下的上下文大小过滤"""
        # 创建测试上下文
        contexts = [
            CallChainContext(
                chain=["func1", "func2"],
                functions=[],
                relevance_score=0.9,
                context_size=800
            ),
            CallChainContext(
                chain=["func3", "func4"],
                functions=[],
                relevance_score=0.7,
                context_size=300
            ),
            CallChainContext(
                chain=["func5", "func6"],
                functions=[],
                relevance_score=0.8,
                context_size=400
            ),
            CallChainContext(
                chain=["func7", "func8"],
                functions=[],
                relevance_score=0.6,
                context_size=200
            )
        ]
        
        # 设置大小优先策略
        self.config.context_selection_strategy = "size_first"
        
        # 测试选择
        selected = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        
        # 验证结果
        self.assertEqual(len(selected), 3)  # 可以选择3个小上下文
        
        # 验证按大小排序（从小到大）
        self.assertEqual(selected[0].context_size, 200)  # 最小
        self.assertEqual(selected[1].context_size, 300)
        self.assertEqual(selected[2].context_size, 400)
        
        # 验证总大小不超过限制
        total_size = sum(ctx.context_size for ctx in selected)
        self.assertLessEqual(total_size, 1000)
    
    def test_context_size_filtering_balanced(self):
        """测试平衡策略下的上下文大小过滤"""
        # 创建测试上下文
        contexts = [
            CallChainContext(
                chain=["func1", "func2"],
                functions=[],
                relevance_score=0.9,
                context_size=800
            ),
            CallChainContext(
                chain=["func3", "func4"],
                functions=[],
                relevance_score=0.7,
                context_size=300
            ),
            CallChainContext(
                chain=["func5", "func6"],
                functions=[],
                relevance_score=0.8,
                context_size=400
            ),
            CallChainContext(
                chain=["func7", "func8"],
                functions=[],
                relevance_score=0.6,
                context_size=200
            )
        ]
        
        # 设置平衡策略
        self.config.context_selection_strategy = "balanced"
        
        # 测试选择
        selected = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        
        # 验证结果
        self.assertGreater(len(selected), 0)
        
        # 验证总大小不超过限制
        total_size = sum(ctx.context_size for ctx in selected)
        self.assertLessEqual(total_size, 1000)
    
    def test_context_size_filtering_edge_cases(self):
        """测试上下文大小过滤的边界情况"""
        # 测试单个超大上下文
        large_context = CallChainContext(
            chain=["large_func"],
            functions=[],
            relevance_score=0.9,
            context_size=1500  # 超过限制
        )
        
        selected = self.selector.select_optimal_contexts([large_context], max_chains=1, max_context_size=1000)
        self.assertEqual(len(selected), 0)  # 应该被过滤掉
        
        # 测试空上下文列表
        selected = self.selector.select_optimal_contexts([], max_chains=3, max_context_size=1000)
        self.assertEqual(len(selected), 0)
        
        # 测试所有上下文都超过大小限制
        large_contexts = [
            CallChainContext(chain=["func1"], functions=[], relevance_score=0.8, context_size=1200),
            CallChainContext(chain=["func2"], functions=[], relevance_score=0.7, context_size=1100),
        ]
        
        selected = self.selector.select_optimal_contexts(large_contexts, max_chains=2, max_context_size=1000)
        self.assertEqual(len(selected), 0)  # 所有上下文都被过滤掉
    
    def test_context_size_filtering_with_logging(self):
        """测试上下文大小过滤的日志记录"""
        # 创建测试上下文
        contexts = [
            CallChainContext(
                chain=["func1", "func2"],
                functions=[],
                relevance_score=0.9,
                context_size=800
            ),
            CallChainContext(
                chain=["func3", "func4"],
                functions=[],
                relevance_score=0.7,
                context_size=300
            ),
            CallChainContext(
                chain=["func5", "func6"],
                functions=[],
                relevance_score=0.8,
                context_size=400
            )
        ]
        
        # 启用日志记录
        self.config.log_context_building = True
        
        # 使用patch捕获日志
        with patch('gate_keeper.application.service.context_management.context_selector.logger') as mock_logger:
            selected = self.selector.select_optimal_contexts(contexts, max_chains=2, max_context_size=1000)
            
            # 验证日志被调用
            mock_logger.info.assert_called()
            
            # 验证选择了正确的上下文数量（由于大小限制，可能只选择1个）
            self.assertGreater(len(selected), 0)
            self.assertLessEqual(len(selected), 2)
    
    def test_context_size_filtering_performance(self):
        """测试上下文大小过滤的性能"""
        # 创建大量测试上下文
        contexts = []
        for i in range(100):
            context = CallChainContext(
                chain=[f"func{i}"],
                functions=[],
                relevance_score=0.5 + (i % 10) * 0.05,  # 0.5-0.95的评分
                context_size=100 + (i % 20) * 50  # 100-1000的大小
            )
            contexts.append(context)
        
        # 测试性能
        import time
        start_time = time.time()
        
        selected = self.selector.select_optimal_contexts(contexts, max_chains=10, max_context_size=5000)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证执行时间合理（小于1秒）
        self.assertLess(execution_time, 1.0)
        
        # 验证结果正确
        self.assertLessEqual(len(selected), 10)
        total_size = sum(ctx.context_size for ctx in selected)
        self.assertLessEqual(total_size, 5000)
    
    def test_context_size_filtering_statistics(self):
        """测试上下文大小过滤的统计信息"""
        # 创建测试上下文
        contexts = [
            CallChainContext(chain=["func1"], functions=[], relevance_score=0.9, context_size=800),
            CallChainContext(chain=["func2"], functions=[], relevance_score=0.7, context_size=300),
            CallChainContext(chain=["func3"], functions=[], relevance_score=0.8, context_size=400),
            CallChainContext(chain=["func4"], functions=[], relevance_score=0.6, context_size=200),
        ]
        
        # 执行选择
        selected = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        
        # 获取统计信息
        stats = self.selector.get_selection_statistics(contexts, selected)
        
        # 验证统计信息
        self.assertEqual(stats["original_count"], 4)
        self.assertLessEqual(stats["selected_count"], 3)
        self.assertLessEqual(stats["selection_ratio"], 1.0)
        self.assertGreaterEqual(stats["selection_ratio"], 0.0)
        self.assertLessEqual(stats["selected_total_size"], 1000)
        # 相关性改进可能为0（浮点数精度问题），所以使用近似比较
        self.assertGreaterEqual(stats["relevance_improvement"], -0.001)
    
    def test_context_size_filtering_different_strategies_comparison(self):
        """测试不同策略下的上下文大小过滤效果对比"""
        # 创建测试上下文
        contexts = [
            CallChainContext(chain=["func1"], functions=[], relevance_score=0.9, context_size=800),
            CallChainContext(chain=["func2"], functions=[], relevance_score=0.7, context_size=300),
            CallChainContext(chain=["func3"], functions=[], relevance_score=0.8, context_size=400),
            CallChainContext(chain=["func4"], functions=[], relevance_score=0.6, context_size=200),
        ]
        
        results = {}
        
        # 测试相关性优先策略
        self.config.context_selection_strategy = "relevance_first"
        selected_relevance = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        results["relevance_first"] = {
            "count": len(selected_relevance),
            "total_size": sum(ctx.context_size for ctx in selected_relevance),
            "avg_relevance": sum(ctx.relevance_score for ctx in selected_relevance) / len(selected_relevance) if selected_relevance else 0
        }
        
        # 测试大小优先策略
        self.config.context_selection_strategy = "size_first"
        selected_size = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        results["size_first"] = {
            "count": len(selected_size),
            "total_size": sum(ctx.context_size for ctx in selected_size),
            "avg_relevance": sum(ctx.relevance_score for ctx in selected_size) / len(selected_size) if selected_size else 0
        }
        
        # 测试平衡策略
        self.config.context_selection_strategy = "balanced"
        selected_balanced = self.selector.select_optimal_contexts(contexts, max_chains=3, max_context_size=1000)
        results["balanced"] = {
            "count": len(selected_balanced),
            "total_size": sum(ctx.context_size for ctx in selected_balanced),
            "avg_relevance": sum(ctx.relevance_score for ctx in selected_balanced) / len(selected_balanced) if selected_balanced else 0
        }
        
        # 验证所有策略都遵守大小限制
        for strategy, result in results.items():
            self.assertLessEqual(result["total_size"], 1000)
            self.assertGreater(result["count"], 0)
        
        # 验证相关性优先策略的相关性最高
        self.assertGreaterEqual(
            results["relevance_first"]["avg_relevance"],
            results["size_first"]["avg_relevance"]
        )
        
        # 验证大小优先策略的总大小最小
        self.assertLessEqual(
            results["size_first"]["total_size"],
            results["relevance_first"]["total_size"]
        )


if __name__ == '__main__':
    unittest.main() 