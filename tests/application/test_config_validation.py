"""
配置验证和错误处理测试

目标：验证规则分组配置的验证机制和错误处理能力，确保系统在配置错误或异常情况下能够稳定运行。

测试覆盖：
- 配置参数有效性验证
- 错误配置的降级处理
- 异常情况的恢复机制
- 配置缺失的默认值处理
- 边界值的处理能力

验证重点：
- 配置验证的完整性
- 错误处理的健壮性
- 降级策略的合理性
- 异常恢复的有效性
"""

import sys
import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.rule import RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """模拟LLM客户端"""
    
    def __init__(self):
        self.call_count = 0
        
    def generate(self, prompt, parameters, token=None):
        self.call_count += 1
        return '<Finalanswer>{"is_pass": true, "reason": "配置测试", "violations": []}</Finalanswer>'
    
    def get_config(self):
        return {"provider": "ConfigTest"}


class TestConfigValidation(unittest.TestCase):
    """
    测试配置验证和错误处理
    
    验证目标：
    1. 配置参数的有效性验证
    2. 错误配置的优雅降级
    3. 异常情况的恢复处理
    4. 边界值的正确处理
    """
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockLLMClient()
        self.llm_service = LLMService(self.mock_client)
        
        self.test_rules = [
            CodeCheckRule(
                id="R1", name="测试规则1", category=["测试"], 
                description="测试用规则", enabled=True
            ),
            CodeCheckRule(
                id="R2", name="测试规则2", category=["测试"],
                description="测试用规则", enabled=True  
            )
        ]
        
        self.test_function = AffectedFunction(
            name="test_func", start_line=1, end_line=10, changed_lines=[5],
            code="def test_func():\n    pass", filepath="test.py",
            related_calls=[], related_definitions=[], llm_results=[]
        )

    @patch('gate_keeper.config.config')
    def test_invalid_similarity_threshold(self, mock_config):
        """
        测试无效相似度阈值的处理
        
        验证目的：确保无效的相似度阈值被正确处理
        """
        invalid_thresholds = [
            -0.5,      # 负数
            1.5,       # 大于1
            "0.7",     # 字符串
            None,      # None值
            [],        # 列表
            {},        # 字典
        ]
        
        mock_config.rule_grouping_strategy = "similarity"
        mock_config.rule_file_path = "test.md"
        mock_config.rule_merge_on = None
        
        for threshold in invalid_thresholds:
            with self.subTest(threshold=threshold):
                # 重置调用计数
                self.mock_client.call_count = 0
                
                # 设置无效阈值
                mock_config.rule_similarity_threshold = threshold
                
                # 模拟similarity分组方法
                with patch.object(RuleManager, 'group_rules_by_similarity') as mock_similarity:
                    mock_similarity.return_value = {"similarity_group": self.test_rules}
                    
                    try:
                        # 构造RuleManager实例并mock get_applicable_rules
                        rule_manager = RuleManager(rule_file_path="dummy.md")
                        rule_manager.get_applicable_rules = lambda af: {"similarity_group": self.test_rules}

                        # 执行分析
                        result = self.llm_service._analyze_single_function(
                            self.test_function, "test.py", rule_manager
                        )
                        
                        # 验证有结果
                        self.assertIsNotNone(result)
                        
                        # 验证similarity方法被调用时使用了合理的阈值
                        if mock_similarity.called:
                            call_args = mock_similarity.call_args[0]
                            actual_threshold = call_args[0] if call_args else 0.7
                            
                            # 验证阈值在合理范围内
                            self.assertIsInstance(actual_threshold, (int, float))
                            self.assertGreaterEqual(actual_threshold, 0.0)
                            self.assertLessEqual(actual_threshold, 1.0)
                            
                    except Exception as e:
                        self.fail(f"无效相似度阈值 {threshold} 导致异常: {e}")

    @patch('gate_keeper.config.config')
    def test_missing_config_attributes(self, mock_config):
        """
        测试配置属性缺失的处理
        
        验证目的：确保配置属性缺失时使用合理默认值
        """
        # 模拟配置对象缺少某些属性
        mock_config.rule_file_path = "test.md"
        mock_config.rule_merge_on = None
        
        # 删除分组相关配置属性
        if hasattr(mock_config, 'rule_grouping_strategy'):
            delattr(mock_config, 'rule_grouping_strategy')
        if hasattr(mock_config, 'min_rule_group_size'):
            delattr(mock_config, 'min_rule_group_size')
        if hasattr(mock_config, 'max_rule_group_size'):
            delattr(mock_config, 'max_rule_group_size')
        if hasattr(mock_config, 'target_rule_group_size'):
            delattr(mock_config, 'target_rule_group_size')
        if hasattr(mock_config, 'rule_similarity_threshold'):
            delattr(mock_config, 'rule_similarity_threshold')
        
        # 模拟adaptive分组方法
        with patch.object(RuleManager, 'group_rules_adaptive') as mock_adaptive:
            mock_adaptive.return_value = {"default_group": self.test_rules}
            # 构造RuleManager实例并mock get_applicable_rules
            rule_manager = RuleManager(rule_file_path="dummy.md")
            rule_manager.get_applicable_rules = lambda af: {"default_group": self.test_rules}
            # 执行分析，应该使用默认值
            result = self.llm_service._analyze_single_function(
                self.test_function, "test.py", rule_manager
            )
            # 验证有结果
            self.assertIsNotNone(result)
            self.assertGreater(self.mock_client.call_count, 0)
            # 验证使用了默认参数
            if mock_adaptive.called:
                call_kwargs = mock_adaptive.call_args[1]
                self.assertIn('min_group_size', call_kwargs)
                self.assertIn('max_group_size', call_kwargs)
                self.assertIn('target_group_size', call_kwargs)

    @patch('gate_keeper.config.config')
    def test_rule_manager_creation_failure(self, mock_config):
        """
        测试RuleManager创建失败的处理
        
        验证目的：确保RuleManager创建失败时能正确降级到无分组分析
        """
        # 设置配置
        mock_config.rule_grouping_strategy = "adaptive"
        mock_config.rule_file_path = "non_existent_file.md"
        mock_config.rule_merge_on = None
        
        # 模拟RuleManager构造函数抛出异常，但仍然能够创建实例用于测试
        with patch.object(RuleManager, '__init__', return_value=None):
            # 构造RuleManager实例并mock get_applicable_rules
            rule_manager = RuleManager.__new__(RuleManager)  # 绕过__init__
            rule_manager.get_applicable_rules = lambda af: {"default_group": self.test_rules}

            # 执行分析
            result = self.llm_service._analyze_single_function(
                self.test_function, "test.py", rule_manager
            )
            
            # 验证结果
            self.assertIsNotNone(result, "应该返回降级分析结果而不是None")
            self.assertIsInstance(result, list, "结果应该是列表类型")
            
            # 如果mock_client配置了返回响应，验证结果内容
            if hasattr(self, 'mock_client') and hasattr(self.mock_client, 'responses'):
                self.assertEqual(len(result), 1, "应该只有一个分析结果（无分组）")
                if result:
                    self.assertIn('is_pass', result[0], "结果应该包含is_pass字段")
                    self.assertIn('violations', result[0], "结果应该包含violations字段")

    @patch('gate_keeper.config.config')
    def test_grouping_method_exception_handling(self, mock_config):
        """
        测试分组方法异常的处理
        
        验证目的：确保分组方法抛出异常时有合理的降级处理
        """
        mock_config.rule_file_path = "test.md"
        mock_config.rule_merge_on = None
        
        grouping_methods = [
            ("category", "group_rules_by_category"),
            ("similarity", "group_rules_by_similarity"), 
            ("adaptive", "group_rules_adaptive")
        ]
        
        for strategy, method_name in grouping_methods:
            with self.subTest(strategy=strategy):
                # 重置调用计数
                self.mock_client.call_count = 0
                
                mock_config.rule_grouping_strategy = strategy
                
                # 模拟对应的分组方法抛出异常
                with patch.object(RuleManager, method_name, side_effect=Exception(f"{method_name}异常")):
                    with patch('gate_keeper.shared.log.app_logger') as mock_logger:
                        try:
                            # 构造RuleManager实例并mock get_applicable_rules
                            rule_manager = RuleManager(rule_file_path="dummy.md")
                            rule_manager.get_applicable_rules = lambda af: {"default_group": self.test_rules}

                            # 执行分析
                            result = self.llm_service._analyze_single_function(
                                self.test_function, "test.py", rule_manager
                            )
                            
                            # 验证异常被记录
                            error_logged = any(
                                "LLM analyze error" in str(call) or "error" in str(call).lower()
                                for call in mock_logger.error.call_args_list
                            )
                            
                            # 可能的结果：
                            # 1. 返回None（分析失败）
                            # 2. 返回降级结果（使用默认分组）
                            # 两种情况都是可接受的错误处理方式
                            
                        except Exception as e:
                            self.fail(f"{strategy}策略分组方法异常未被正确处理: {e}")

    @patch('gate_keeper.config.config')
    def test_concurrent_config_access_safety(self, mock_config):
        """
        测试并发配置访问的安全性
        
        验证目的：确保并发访问配置时不会导致竞争条件
        """
        import threading
        import time
        
        mock_config.rule_grouping_strategy = "adaptive"
        mock_config.min_rule_group_size = 3
        mock_config.max_rule_group_size = 8
        mock_config.target_rule_group_size = 5
        mock_config.rule_file_path = "test.md"
        mock_config.rule_merge_on = None
        
        results = []
        errors = []
        
        def concurrent_analysis():
            try:
                # 模拟临时改变配置（模拟并发修改）
                if threading.current_thread().name == "Thread-1":
                    mock_config.rule_grouping_strategy = "category"
                elif threading.current_thread().name == "Thread-2":
                    mock_config.rule_grouping_strategy = "similarity"
                
                with patch.object(RuleManager, 'group_rules_adaptive') as mock_adaptive:
                    mock_adaptive.return_value = {"group": self.test_rules}
                    
                    # 构造RuleManager实例并mock get_applicable_rules
                    rule_manager = RuleManager(rule_file_path="dummy.md")
                    rule_manager.get_applicable_rules = lambda af: {"group": self.test_rules}

                    result = self.llm_service._analyze_single_function(
                        self.test_function, "test.py", rule_manager
                    )
                    results.append(result)
                    
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程并发执行
        threads = []
        for i in range(3):
            t = threading.Thread(target=concurrent_analysis, name=f"Thread-{i}")
            threads.append(t)
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证没有发生严重错误
        self.assertEqual(len(errors), 0, f"并发配置访问产生错误: {errors}")
        
        # 验证所有分析都产生了结果
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertIsNotNone(result)

    @patch('gate_keeper.config.config')
    def test_extreme_boundary_values(self, mock_config):
        """
        测试极端边界值的处理
        
        验证目的：确保极端配置值不会导致系统异常
        """
        extreme_configs = [
            # (strategy, config_overrides, description)
            ("adaptive", {"min_rule_group_size": 1, "max_rule_group_size": 1000, "target_rule_group_size": 500}, "极大目标值"),
            ("adaptive", {"min_rule_group_size": 100, "max_rule_group_size": 100, "target_rule_group_size": 100}, "极大固定值"),
            ("similarity", {"rule_similarity_threshold": 0.0001}, "极小相似度"),
            ("similarity", {"rule_similarity_threshold": 0.9999}, "极高相似度"),
        ]
        
        mock_config.rule_file_path = "test.md" 
        mock_config.rule_merge_on = None
        
        for strategy, config_overrides, description in extreme_configs:
            with self.subTest(desc=description):
                # 重置调用计数
                self.mock_client.call_count = 0
                
                # 设置策略和极端配置
                mock_config.rule_grouping_strategy = strategy
                for key, value in config_overrides.items():
                    setattr(mock_config, key, value)
                
                # 根据策略模拟对应的分组方法
                if strategy == "adaptive":
                    with patch.object(RuleManager, 'group_rules_adaptive') as mock_method:
                        mock_method.return_value = {"extreme_group": self.test_rules}
                        
                        try:
                            # 构造RuleManager实例并mock get_applicable_rules
                            rule_manager = RuleManager(rule_file_path="dummy.md")
                            rule_manager.get_applicable_rules = lambda af: {"extreme_group": self.test_rules}

                            result = self.llm_service._analyze_single_function(
                                self.test_function, "test.py", rule_manager
                            )
                            
                            # 验证极端配置不会导致崩溃
                            self.assertIsNotNone(result)
                            
                            # 验证方法被调用时参数合理
                            if mock_method.called:
                                kwargs = mock_method.call_args[1]
                                # 即使配置极端，传递的参数也应该合理
                                for param_name, param_value in kwargs.items():
                                    self.assertIsInstance(param_value, (int, float))
                                    self.assertGreater(param_value, 0)
                                    
                        except Exception as e:
                            self.fail(f"极端配置 {description} 导致异常: {e}")
                
                elif strategy == "similarity":
                    with patch.object(RuleManager, 'group_rules_by_similarity') as mock_method:
                        mock_method.return_value = {"similarity_extreme": self.test_rules}
                        
                        try:
                            # 构造RuleManager实例并mock get_applicable_rules
                            rule_manager = RuleManager(rule_file_path="dummy.md")
                            rule_manager.get_applicable_rules = lambda af: {"similarity_extreme": self.test_rules}

                            result = self.llm_service._analyze_single_function(
                                self.test_function, "test.py", rule_manager
                            )
                            
                            self.assertIsNotNone(result)
                            
                            # 验证阈值在合理范围内
                            if mock_method.called:
                                threshold = mock_method.call_args[0][0]
                                self.assertGreaterEqual(threshold, 0.0)
                                self.assertLessEqual(threshold, 1.0)
                                
                        except Exception as e:
                            self.fail(f"极端相似度配置 {description} 导致异常: {e}")

    def test_config_type_validation(self):
        """
        测试配置类型验证
        
        验证目的：确保配置类型错误时有合理的处理
        """
        # 这个测试验证配置类型转换和验证的健壮性
        type_test_cases = [
            (3.14, int, "浮点数转整数"),
            ("5", int, "字符串数字转整数"),
            (True, int, "布尔值转整数"),
            (False, float, "布尔值转浮点数"),
        ]
        
        for value, expected_type, description in type_test_cases:
            with self.subTest(desc=description):
                # 模拟类型转换逻辑
                try:
                    if expected_type == int:
                        converted = int(float(value)) if value is not None else 0
                    elif expected_type == float:
                        converted = float(value) if value is not None else 0.0
                    else:
                        converted = value
                    
                    # 验证转换成功且合理
                    self.assertIsInstance(converted, expected_type)
                    if expected_type in [int, float]:
                        self.assertGreaterEqual(converted, 0)
                        
                except (ValueError, TypeError) as e:
                    # 类型转换失败是可以接受的，应该使用默认值
                    pass


if __name__ == '__main__':
    unittest.main() 