"""
Test Refactored Context Manager

测试重构后的上下文管理器
"""

import unittest
from unittest.mock import Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import (
    CallChainContext, ContextManager, ContextManagerConfig,
    ContextRelevanceCalculator, ContextSelector)
from gate_keeper.external.code_analyzer.models.function import Function


class TestContextManagerRefactored(unittest.TestCase):
    """测试重构后的上下文管理器"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试配置
        self.config = ContextManagerConfig()
        
        # 创建模拟的静态分析器
        self.mock_static_analyzer = Mock()
        self.mock_static_analyzer.repo_index = Mock()
        self.mock_static_analyzer.repo_index.function_definitions = {}
        self.mock_static_analyzer.repo_index.function_calls = []
        
        # 创建上下文管理器
        self.context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            config=self.config
        )
    
    def test_context_manager_initialization(self):
        """测试上下文管理器初始化"""
        # 验证组件已正确初始化
        self.assertIsInstance(self.context_manager.relevance_calculator, ContextRelevanceCalculator)
        self.assertIsInstance(self.context_manager.context_selector, ContextSelector)
        self.assertIsInstance(self.context_manager.config.max_context_size, int)
        self.assertIsInstance(self.context_manager.config.max_chains, int)
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 使用旧的初始化方式
        context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            max_context_size=6000,
            max_chain_depth=4
        )
        
        # 验证参数被正确应用
        self.assertEqual(context_manager.config.max_context_size, 6000)
        self.assertEqual(context_manager.config.max_chain_depth, 4)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 创建无效配置
        invalid_config = ContextManagerConfig(
            max_context_size=-1,
            max_chain_depth=0,
            relevance_weights={"chain_length": 0.5, "function_position": 0.3}  # 权重总和不为1
        )
        
        errors = invalid_config.validate()
        self.assertGreater(len(errors), 0)
        self.assertIn("max_context_size 必须大于 0", errors)
        self.assertIn("max_chain_depth 必须大于 0", errors)
        self.assertTrue(any("relevance_weights 权重总和必须为 1.0" in error for error in errors))
    
    def test_relevance_calculator(self):
        """测试相关性计算器"""
        calculator = ContextRelevanceCalculator(self.config)
        
        # 创建测试数据
        af = AffectedFunction(
            name="test_func",
            start_line=1,
            end_line=10,
            filepath="test.c",
            code="void test_func() {}",
            changed_lines=[5],
            call_chains=[],
            related_definitions=[],
            related_calls=[]
        )
        
        chain = ["main", "test_func", "helper"]
        functions = [Mock(spec=Function)]
        
        # 测试相关性计算
        relevance = calculator.calculate_chain_relevance(af, chain, functions)
        self.assertGreaterEqual(relevance, 0.0)
        self.assertLessEqual(relevance, 1.0)
    
    def test_context_selector(self):
        """测试上下文选择器"""
        selector = ContextSelector(self.config)
        
        # 创建测试上下文
        contexts = [
            CallChainContext(chain=["a"], functions=[], relevance_score=0.8, context_size=1000),
            CallChainContext(chain=["b"], functions=[], relevance_score=0.6, context_size=2000),
            CallChainContext(chain=["c"], functions=[], relevance_score=0.9, context_size=3000),
        ]
        
        # 测试相关性优先选择
        selected = selector._select_relevance_first(contexts, max_chains=2, max_context_size=4000)
        self.assertEqual(len(selected), 2)
        self.assertEqual(selected[0].relevance_score, 0.9)  # 最高相关性
        self.assertEqual(selected[1].relevance_score, 0.8)
    
    def test_context_selector_size_first(self):
        """测试大小优先选择策略"""
        selector = ContextSelector(self.config)
        selector.config.context_selection_strategy = "size_first"
        
        # 创建测试上下文
        contexts = [
            CallChainContext(chain=["a"], functions=[], relevance_score=0.8, context_size=3000),
            CallChainContext(chain=["b"], functions=[], relevance_score=0.6, context_size=1000),
            CallChainContext(chain=["c"], functions=[], relevance_score=0.9, context_size=2000),
        ]
        
        # 测试大小优先选择
        selected = selector._select_size_first(contexts, max_chains=2, max_context_size=4000)
        self.assertEqual(len(selected), 2)
        self.assertEqual(selected[0].context_size, 1000)  # 最小大小
        self.assertEqual(selected[1].context_size, 2000)
    
    def test_context_selector_balanced(self):
        """测试平衡选择策略"""
        selector = ContextSelector(self.config)
        selector.config.context_selection_strategy = "balanced"
        
        # 创建测试上下文
        contexts = [
            CallChainContext(chain=["a"], functions=[], relevance_score=0.8, context_size=1000),
            CallChainContext(chain=["b"], functions=[], relevance_score=0.6, context_size=2000),
            CallChainContext(chain=["c"], functions=[], relevance_score=0.9, context_size=3000),
        ]
        
        # 测试平衡选择
        selected = selector._select_balanced(contexts, max_chains=2, max_context_size=4000)
        self.assertEqual(len(selected), 2)
    
    def test_selection_statistics(self):
        """测试选择统计信息"""
        selector = ContextSelector(self.config)
        
        # 创建测试上下文
        original_contexts = [
            CallChainContext(chain=["a"], functions=[], relevance_score=0.5, context_size=1000),
            CallChainContext(chain=["b"], functions=[], relevance_score=0.8, context_size=2000),
        ]
        
        selected_contexts = [
            CallChainContext(chain=["b"], functions=[], relevance_score=0.8, context_size=2000),
        ]
        
        # 获取统计信息
        stats = selector.get_selection_statistics(original_contexts, selected_contexts)
        
        self.assertEqual(stats["original_count"], 2)
        self.assertEqual(stats["selected_count"], 1)
        self.assertEqual(stats["selection_ratio"], 0.5)
        self.assertEqual(stats["original_total_size"], 3000)
        self.assertEqual(stats["selected_total_size"], 2000)
        self.assertGreater(stats["relevance_improvement"], 0)
    
    def test_relevance_filtering(self):
        """测试相关性过滤"""
        calculator = ContextRelevanceCalculator(self.config)
        
        # 创建测试上下文
        contexts = [
            CallChainContext(chain=["a"], functions=[], relevance_score=0.05, context_size=1000),
            CallChainContext(chain=["b"], functions=[], relevance_score=0.15, context_size=2000),
            CallChainContext(chain=["c"], functions=[], relevance_score=0.25, context_size=3000),
        ]
        
        # 测试过滤
        filtered = calculator.filter_by_relevance_threshold(contexts, threshold=0.1)
        self.assertEqual(len(filtered), 2)
        self.assertGreaterEqual(min(c.relevance_score for c in filtered), 0.1)
    
    def test_config_factory_methods(self):
        """测试配置工厂方法"""
        # 测试默认配置
        default_config = ContextManagerConfig()
        self.assertIsInstance(default_config.max_context_size, int)
        self.assertIsInstance(default_config.max_chains, int)
        
        # 测试自定义配置
        custom_config = ContextManagerConfig(
            max_context_size=4000,
            max_chains=2,
            enable_detailed_logging=True
        )
        self.assertEqual(custom_config.max_context_size, 4000)
        self.assertEqual(custom_config.max_chains, 2)
        self.assertTrue(custom_config.enable_detailed_logging)
        
        # 测试从配置创建
        from_config = ContextManagerConfig.create_from_config(
            max_context_size=12000,
            max_chains=5,
            enable_detailed_logging=False
        )
        self.assertEqual(from_config.max_context_size, 12000)
        self.assertEqual(from_config.max_chains, 5)
        self.assertFalse(from_config.enable_detailed_logging)
    
    def test_effective_max_context_size(self):
        """测试有效最大上下文大小计算"""
        config = ContextManagerConfig(max_context_size=10000, max_context_size_ratio=0.8)
        effective_size = config.get_effective_max_context_size()
        self.assertEqual(effective_size, 8000)


if __name__ == "__main__":
    unittest.main() 