"""
LLMService模块补充测试

目标：补充测试LLMService的各种新功能和边界情况，包括：
- 优化的上下文管理功能
- 配置参数处理
- 并发处理边界情况
- 错误处理和恢复
- 性能测试

测试覆盖：
- 上下文优化功能
- 配置参数验证
- 并发处理边界
- 错误处理机制
- 性能测试
"""

import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.llm import LLMService
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMR<PERSON>ult
from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature, Parameter)
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """模拟LLM客户端，用于测试"""
    
    def __init__(self, responses=None, should_fail=False):
        self.responses = responses or ["模拟LLM响应"]
        self.call_count = 0
        self.should_fail = should_fail
        
    def generate(self, prompt, parameters, token=None):
        """模拟LLM响应"""
        self.call_count += 1
        if self.should_fail:
            raise Exception("模拟LLM调用失败")
        if self.call_count <= len(self.responses):
            return self.responses[self.call_count - 1]
        return '<FinalAnswer>{"is_pass": true, "reason": "默认响应", "violations": []}</FinalAnswer>'
    
    def get_config(self):
        return {"provider": "Mock"}


class FakeFuncDict:
    def __init__(self, func_obj):
        self.func_obj = func_obj
    def values(self):
        return [self.func_obj]
    def items(self):
        return [self.func_obj]
    def get(self, key, default=None):
        if key == "test_function":
            return self.func_obj
        return default
    def __iter__(self):
        yield "test_function"
    def __getitem__(self, key):
        if key == "test_function":
            return self.func_obj
        raise KeyError(key)

class MockStaticAnalyzer:
    """模拟静态分析器"""
    
    def __init__(self):
        self.call_chains = {
            "test_function": [
                {"caller": "main", "callee": "test_function", "file": "test.py"},
                {"caller": "test_function", "callee": "helper", "file": "test.py"}
            ]
        }
        from unittest.mock import Mock

        from gate_keeper.external.code_analyzer.models.code_range import \
            CodeRange
        from gate_keeper.external.code_analyzer.models.function import (
            Function, FunctionSignature, Parameter)
        func_obj = Function.create_simple(
            name="test_function",
            start_line=1,
            end_line=10,
            filepath="test.py",
            signature=FunctionSignature(name="test_function", parameters=[Parameter(name="a", type_hint="int")]),
            code="def test_function(a): pass"
        )
        self.repo_index = Mock()
        self.repo_index.function_definitions = {"test_function": [func_obj]}
        self.repo_index.function_calls = []
    
    def get_call_chains(self, function_name):
        return self.call_chains.get(function_name, [])
    
    def get_function_context(self, function_name):
        return f"函数 {function_name} 的上下文"
    
    def get_bidirectional_call_chains(self, name, filepath, max_depth=3):
        # 返回一个简单的双向调用链
        return [["main", name, "helper"]]


class TestLLMServiceExtended(unittest.TestCase):
    """LLMService扩展测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockLLMClient()
        self.mock_static_analyzer = MockStaticAnalyzer()
        self.mock_rule = CodeCheckRule(
            id="R1", name="test", description="desc", category=["cat"], enabled=True, languages=["python"]
        )
        self.mock_rule2 = CodeCheckRule(
            id="R2", name="test2", description="desc2", category=["cat2"], enabled=True, languages=["python"]
        )
        self.function = Function.create_simple(
            name="helper_function",
            start_line=1,
            end_line=5,
            filepath="helper.py",
            signature=FunctionSignature(name="helper_function", parameters=[Parameter(name="a", type_hint="int")]),
            code="def helper_function(a): pass"
        )
        
    @patch('gate_keeper.config.config.max_context_chain_depth', 3)
    @patch('gate_keeper.config.config.max_context_chains', 3)
    def test_llm_service_initialization_with_optimized_context(self, *_):
        """测试带优化上下文的LLMService初始化"""
        service = LLMService(
            client=self.mock_client,
            static_analyzer=self.mock_static_analyzer,
            use_optimized_context=True
        )
        self.assertIsNotNone(service.context_manager)
        self.assertTrue(service.use_optimized_context)
        self.assertEqual(service.context_manager.max_chain_depth, 3)
        self.assertEqual(service.context_manager.config.max_chains, 3)
        
    def test_llm_service_initialization_without_optimized_context(self):
        """测试不带优化上下文的LLMService初始化"""
        service = LLMService(
            client=self.mock_client,
            use_optimized_context=False
        )
        self.assertIsNone(service.context_manager)
        self.assertFalse(service.use_optimized_context)
        
    def test_llm_service_config_parameter_handling(self):
        """测试配置参数处理"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=10,
            use_optimized_context=True
        )
        self.assertEqual(service.max_calls_per_affected_function, 10)
        self.assertTrue(service.use_optimized_context)
        
    @patch('gate_keeper.config.config.max_context_chain_depth', 'invalid')
    @patch('gate_keeper.config.config.max_context_chains', None)
    def test_llm_service_invalid_config_parameters(self, *_):
        """测试无效配置参数的处理"""
        # 测试无效的max_context_chain_depth
        service = LLMService(client=self.mock_client, static_analyzer=self.mock_static_analyzer, use_optimized_context=True)
        self.assertEqual(service.context_manager.max_chain_depth, 3)  # 应该使用默认值
            
        # 测试无效的max_context_chains
        self.assertEqual(service.context_manager.config.max_chains, 3)  # 应该使用默认值
            
    @patch('gate_keeper.config.config.max_context_chain_depth', 3)
    @patch('gate_keeper.config.config.max_context_chains', 3)
    def test_generate_optimized_prompt_content(self, *_):
        """测试生成优化的prompt内容"""
        # 创建LLMService
        service = LLMService(
            client=self.mock_client,
            static_analyzer=self.mock_static_analyzer,
            use_optimized_context=True
        )
        
        # 创建测试数据
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            filepath="test.py",
            code="def test_function(): pass"
        )
        
        # 测试生成prompt内容（使用新的方法）
        content = service.context_manager.generate_prompt_content(af, "test.py")
        
        # 验证结果
        self.assertIsNotNone(content)
        self.assertIn("test_function", content)
        self.assertIn("def test_function(): pass", content)
        
    def test_generate_simple_prompt_content(self):
        """测试生成简单的prompt内容"""
        service = LLMService(client=self.mock_client)
        
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        content = service._generate_simple_prompt_content(af, "test.py")
        self.assertIn("待检查内容", content)
        self.assertIn("def test_function(): pass", content)
        
    def test_generate_simple_prompt_content_with_related_definitions(self):
        """测试生成带相关定义的简单prompt内容"""
        service = LLMService(client=self.mock_client)
        
        # 创建带相关定义的函数
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[self.function],
            llm_results=[]
        )
        
        content = service._generate_simple_prompt_content(af, "test.py")
        self.assertIn("参考上下文", content)
        self.assertIn("def helper_function(a): pass", content)
        self.assertIn("def test_function(): pass", content)
        
    def test_call_llm_success(self):
        """测试成功的LLM调用"""
        # mock_client 返回合法JSON
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        service = LLMService(client=self.mock_client)
        
        prompt = "测试prompt"
        result = service._call_llm(prompt)
        
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertTrue(result.is_pass)
        self.assertIsNotNone(result.call_id)
        self.assertEqual(result.prompt, prompt)
        self.assertIsNotNone(result.response)
        
    def test_call_llm_failure(self):
        """测试失败的LLM调用"""
        failing_client = MockLLMClient(should_fail=True)
        service = LLMService(client=failing_client)
        
        prompt = "测试prompt"
        result = service._call_llm(prompt)
        
        self.assertIsNone(result)
        
    def test_call_llm_empty_response(self):
        """测试空响应的LLM调用"""
        empty_client = MockLLMClient(responses=[None])
        service = LLMService(client=empty_client)
        
        prompt = "测试prompt"
        result = service._call_llm(prompt)
        
        self.assertIsNone(result)
        
    def test_analyze_single_function_with_max_calls_limit(self):
        """测试单个函数分析的最大调用次数限制"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=2
        )
        
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        # 模拟多个规则组
        rule_manager = Mock()
        rule_manager.get_applicable_rules.return_value = {
            "group1": [self.mock_rule],
            "group2": [self.mock_rule2],
            "group3": [self.mock_rule],
            "group4": [self.mock_rule2]
        }
        
        result = service._analyze_single_function(
            af, "test.py", rule_manager
        )
        
        # 应该只调用2次（受max_calls_per_affected_function限制）
        self.assertEqual(self.mock_client.call_count, 2)
        self.assertEqual(len(result), 2)
        
    def test_analyze_single_function_with_rule_grouping(self):
        """测试带规则分组的单个函数分析"""
        service = LLMService(client=self.mock_client)
        
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        # 模拟预计算的分组规则
        grouped_rules = {
            "命名规范": [self.mock_rule],
            "代码质量": [self.mock_rule2]
        }
        
        rule_manager = Mock()
        
        result = service._analyze_single_function(
            af, "test.py", rule_manager, grouped_rules
        )
        
        # 应该调用2次（每个分组一次）
        self.assertEqual(self.mock_client.call_count, 2)
        self.assertEqual(len(result), 2)
        
    def test_analyze_single_function_rule_group_exception(self):
        """测试规则组分析异常处理"""
        service = LLMService(client=self.mock_client)
        
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        # 只保留一个分组
        def mock_call_llm(prompt):
            return AnalyzeLLMResult(is_pass=True, reason="成功", violations=[])
        service._call_llm = mock_call_llm
        grouped_rules = {
            "命名规范": [self.mock_rule]
        }
        rule_manager = Mock()
        result = service._analyze_single_function(
            af, "test.py", rule_manager, grouped_rules
        )
        self.assertEqual(len(result), 1)
        self.assertTrue(result[0].is_pass)
        
    def test_analyze_mr_with_empty_diffs(self):
        """测试空差异的MR分析"""
        service = LLMService(client=self.mock_client)
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[],
            affected_functions=[]
        )
        
        result = service.analyze_mr(mr_result)
        
        self.assertEqual(result, mr_result)
        self.assertEqual(self.mock_client.call_count, 0)
        
    def test_analyze_mr_with_empty_affected_functions(self):
        """测试空受影响函数的MR分析"""
        service = LLMService(client=self.mock_client)
        
        diff = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=[],
            ungrouped_lines=[2, 5],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=[]
        )
        
        result = service.analyze_mr(mr_result)
        
        self.assertEqual(result, mr_result)
        self.assertEqual(self.mock_client.call_count, 0)
        
    @patch('gate_keeper.application.service.llm.llm_service.RuleManager')
    def test_analyze_mr_with_progress_bar(self, mock_rule_manager_class):
        """测试带进度条的MR分析"""
        service = LLMService(client=self.mock_client)
        
        # 创建多个受影响函数
        affected_functions = [
            AffectedFunction(
                name=f"func{i}",
                start_line=1,
                end_line=10,
                changed_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            ) for i in range(3)
        ]
        
        diff = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=affected_functions,
            ungrouped_lines=[2, 5],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        
        # 模拟规则管理器
        mock_rule_manager = Mock()
        mock_rule_manager.get_applicable_rules.return_value = {"default": [self.mock_rule]}
        mock_rule_manager_class.return_value = mock_rule_manager
            
        result = service.analyze_mr(mr_result)
            
        self.assertEqual(result, mr_result)
        # 每个函数应该有LLM结果（可能有多个，因为规则分组）
        for af in result.diffs[0].affected_functions:
            self.assertGreater(len(af.llm_results), 0)
                
    @patch('gate_keeper.application.service.llm.llm_service.RuleManager')
    def test_analyze_mr_concurrent_exception_handling(self, mock_rule_manager_class):
        """测试并发处理中的异常处理"""
        service = LLMService(client=self.mock_client)
        
        # 创建一个会抛出异常的受影响函数
        problematic_af = AffectedFunction(
            name="problematic_func",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def problematic_func(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        # 创建一个正常的受影响函数
        normal_af = AffectedFunction(
            name="normal_func",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def normal_func(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        diff = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=[problematic_af, normal_af],
            ungrouped_lines=[2, 5],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=[problematic_af, normal_af]
        )
        
        # 模拟规则管理器
        mock_rule_manager = Mock()
        mock_rule_manager.get_applicable_rules.return_value = {"default": [self.mock_rule]}
        mock_rule_manager_class.return_value = mock_rule_manager
            
            # 模拟分析函数抛出异常
        def mock_analyze_single_function(af, file_path, rule_manager, grouped_rules_dict=None, pbar=None, pbar_lock=None, task_call_count=None, rule_group_call_count=None):
            if af.name == "problematic_func":
                raise Exception("模拟分析异常")
            return [AnalyzeLLMResult(is_pass=True, reason="正常分析", violations=[])]
            
        service._analyze_single_function = mock_analyze_single_function
            
        result = service.analyze_mr(mr_result)
            
        # 正常函数应该有结果，问题函数应该有空结果
        afs = result.diffs[0].affected_functions
        normal_func = next(af for af in afs if af.name == "normal_func")
        problematic_func = next(af for af in afs if af.name == "problematic_func")
            
        self.assertEqual(len(normal_func.llm_results), 1)
        self.assertEqual(len(problematic_func.llm_results), 0)
            
    @patch('gate_keeper.config.config.max_context_chain_depth', 3)
    @patch('gate_keeper.config.config.max_context_chains', 3)
    def test_generate_prompt_with_optimized_context(self, *_):
        """测试带优化上下文的prompt生成"""
        service = LLMService(
            client=self.mock_client,
            static_analyzer=self.mock_static_analyzer,
            use_optimized_context=True
        )
        
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        rules = [self.mock_rule]
        
        prompt = service._generate_prompt(af, "test.py", rules)
        
        self.assertIsInstance(prompt, str)
        self.assertIn("调用链上下文", prompt)
        self.assertIn("def test_function(): pass", prompt)
        
    def test_generate_prompt_without_optimized_context(self):
        """测试不带优化上下文的prompt生成"""
        service = LLMService(
            client=self.mock_client,
            use_optimized_context=False
        )
        
        af = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        rules = [self.mock_rule]
        
        prompt = service._generate_prompt(af, "test.py", rules)
        
        self.assertIsInstance(prompt, str)
        self.assertIn("def test_function(): pass", prompt)
        # 不应该包含调用链上下文
        self.assertNotIn("调用链上下文", prompt)


if __name__ == '__main__':
    unittest.main() 