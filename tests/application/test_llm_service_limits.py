#!/usr/bin/env python3
"""
测试LLM服务的多层调用限制功能
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch

# 设置环境变量
os.environ["GK_ENV"] = "test"

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.llm import LLMService
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters
from gate_keeper.shared.log import app_logger as logger


class MockLLMClient:
    """模拟LLM客户端"""
    def __init__(self, responses=None):
        self.responses = responses or ['<FinalAnswer>{"is_pass": true, "reason": "测试通过"}</FinalAnswer>']
        self.call_count = 0
    
    def generate(self, prompt, parameters):
        self.call_count += 1
        if self.call_count <= len(self.responses):
            return self.responses[self.call_count - 1]
        return None


class TestLLMServiceLimits(unittest.TestCase):
    """测试LLM服务的多层调用限制功能"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_client = MockLLMClient()
        self.mock_rule = CodeCheckRule(
            id="1.1",
            name="函数命名规范",
            description="函数名应该使用小写字母和下划线",
            category=["基础规范", "命名规范"],
            enabled=True,
            languages=["python"]
        )
        self.mock_rule2 = CodeCheckRule(
            id="1.2",
            name="变量命名规范",
            description="变量名应该使用小写字母和下划线",
            category=["基础规范", "命名规范"],
            enabled=True,
            languages=["python"]
        )
    
    def test_task_level_limit(self):
        """测试任务级限制"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=10,
            max_calls_per_rule_group=10,
            max_calls_per_task=5  # 任务级限制
        )
        
        # 创建测试数据：3个函数，每个函数有10个规则组
        affected_functions = []
        for i in range(3):
            af = AffectedFunction(changed_lines=[], 
                name=f"func{i}",
                start_line=1,
                end_line=10,
                ungrouped_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            )
            affected_functions.append(af)
        
        diff = DiffResult(
            filepath="test.py",
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            ungrouped_lines=[2, 5],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 每个函数返回10个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(10)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用7次
            self.assertLessEqual(self.mock_client.call_count, 7)
            self.assertGreaterEqual(self.mock_client.call_count, 1)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_function_level_limit(self):
        """测试函数级限制"""
        # 创建LLM服务，设置函数级限制为3次
        service = LLMService(
            client=self.mock_client,
            max_workers=1
        )
        service.max_calls_per_task = None    # 任务级不限制
        service.max_calls_per_affected_function = 3   # 被修改函数级限制3次
        service.max_calls_per_rule_group = None    # 规则组级不限制
        
        # 创建测试数据：2个函数，每个函数有10个规则组
        affected_functions = []
        for i in range(2):
            af = AffectedFunction(changed_lines=[], 
                name=f"func{i}",
                start_line=1,
                end_line=10,
                ungrouped_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            )
            affected_functions.append(af)
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 每个函数返回10个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(10)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 验证：应该调用6次LLM（2个函数 × 3次限制）
            self.assertEqual(self.mock_client.call_count, 6)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_item_level_limit(self):
        """测试检查项级限制"""
        # 创建LLM服务，设置检查项级限制为2次
        service = LLMService(
            client=self.mock_client,
            max_workers=1
        )
        service.max_calls_per_task = None    # 任务级不限制
        service.max_calls_per_affected_function = 10  # 被修改函数级限制足够大
        service.max_calls_per_rule_group = 2       # 规则组级限制2次
        
        # 创建测试数据：1个函数，有10个规则组
        af = AffectedFunction(changed_lines=[], 
            name="test_func",
            start_line=1,
            end_line=10,
            ungrouped_lines=[2, 5],
            code="def test_func(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def test_func(): pass\n",
            new_file_content="def test_func(): pass\n",
            affected_functions=[af],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=[af]
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 返回10个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(10)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用2次
            self.assertEqual(self.mock_client.call_count, 2)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_multiple_limits_priority(self):
        """测试多重限制优先级"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=10,  # 被修改函数级限制足够大
            max_calls_per_rule_group=2,          # 每个规则组最多2次
            max_calls_per_task=None              # 任务级不限制
        )
        
        # 创建测试数据：2个函数，每个函数有10个规则组
        affected_functions = []
        for i in range(2):
            af = AffectedFunction(changed_lines=[], 
                name=f"func{i}",
                start_line=1,
                end_line=10,
                ungrouped_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            )
            affected_functions.append(af)
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 每个函数返回10个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(10)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用2次
            self.assertLessEqual(self.mock_client.call_count, 3)
            self.assertGreaterEqual(self.mock_client.call_count, 1)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试新的配置名
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=3  # 使用新的参数名
        )
        
        # 验证新的属性名有效
        self.assertEqual(service.max_calls_per_affected_function, 3)
        
        # 验证默认值（从配置文件获取）
        self.assertEqual(service.max_calls_per_task, 50)  # 测试环境配置值
        self.assertEqual(service.max_calls_per_rule_group, 10)  # 测试环境配置值


class TestLLMServiceNewParameters(unittest.TestCase):
    """测试新的LLM调用限制参数逻辑"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_client = MockLLMClient()
        self.mock_rule = CodeCheckRule(
            id="1.1",
            name="函数命名规范",
            description="函数名应该使用小写字母和下划线",
            category=["基础规范", "命名规范"],
            enabled=True,
            languages=["python"]
        )
    
    def test_affected_function_level_limit_with_call_chains(self):
        """测试被修改函数级限制与调用链结合"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=3,  # 每个被修改函数最多3次
            max_calls_per_rule_group=2,         # 每个规则组最多2次
            max_calls_per_task=None             # 任务级不限制
        )
        
        # 创建测试数据：1个函数，有5个规则组
        af = AffectedFunction(changed_lines=[], 
            name="test_func",
            start_line=1,
            end_line=10,
            ungrouped_lines=[2, 5],
            code="def test_func(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def test_func(): pass\n",
            new_file_content="def test_func(): pass\n",
            affected_functions=[af],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=[af]
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 返回5个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(5)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用2次
            self.assertEqual(self.mock_client.call_count, 2)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_rule_group_level_limit_priority(self):
        """测试规则组级限制优先级"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=10,  # 被修改函数级限制足够大
            max_calls_per_rule_group=2,          # 每个规则组最多2次
            max_calls_per_task=None              # 任务级不限制
        )
        
        # 创建测试数据：1个函数，有3个规则组
        af = AffectedFunction(changed_lines=[], 
            name="test_func",
            start_line=1,
            end_line=10,
            ungrouped_lines=[2, 5],
            code="def test_func(): pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def test_func(): pass\n",
            new_file_content="def test_func(): pass\n",
            affected_functions=[af],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=[af]
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 返回3个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(3)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 验证：应该调用2次LLM（受规则组级限制）
            # 实际调用次数可能因限制而减少
            self.assertLessEqual(self.mock_client.call_count, 2)
            self.assertGreaterEqual(self.mock_client.call_count, 1)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_multiple_affected_functions_with_limits(self):
        """测试多个被修改函数的限制逻辑"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=2,  # 每个被修改函数最多2次
            max_calls_per_rule_group=None,      # 规则组级不限制
            max_calls_per_task=None             # 任务级不限制
        )
        
        # 创建测试数据：2个函数，每个函数有4个规则组
        affected_functions = []
        for i in range(2):
            af = AffectedFunction(changed_lines=[], 
                name=f"func{i}",
                start_line=1,
                end_line=10,
                ungrouped_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            )
            affected_functions.append(af)
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 每个函数返回4个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(4)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用4次
            self.assertLessEqual(self.mock_client.call_count, 4)
            self.assertGreaterEqual(self.mock_client.call_count, 1)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_call_chain_integration(self):
        """测试调用链集成逻辑"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=5,  # 每个被修改函数最多5次
            max_calls_per_rule_group=2,         # 每个规则组最多2次
            max_calls_per_task=None             # 任务级不限制
        )
        
        # 创建带调用链的测试数据
        from gate_keeper.external.code_analyzer.models.call_relation import \
            FunctionCall
        
        af = AffectedFunction(changed_lines=[], 
            name="test_func",
            start_line=1,
            end_line=10,
            ungrouped_lines=[2, 5],
            code="def test_func(): pass",
            filepath="test.py",
            related_calls=[
                FunctionCall(caller="main", callee="test_func", file_path="test.py", line=[1]),
                FunctionCall(caller="test_func", callee="helper", file_path="test.py", line=[5])
            ],
            related_definitions=[],
            llm_results=[]
        )
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def test_func(): pass\n",
            new_file_content="def test_func(): pass\n",
            affected_functions=[af],
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=[af]
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 返回3个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(3)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用2次
            self.assertLessEqual(self.mock_client.call_count, 2)
            self.assertGreaterEqual(self.mock_client.call_count, 1)
            self.assertIsInstance(result, CheckMRResult)
    
    def test_parameter_initialization(self):
        """测试新参数的正确初始化"""
        # 测试从配置加载
        service = LLMService(client=self.mock_client)
        
        # 验证实际配置值
        self.assertEqual(service.max_calls_per_affected_function, 15)  # 测试环境配置值
        self.assertEqual(service.max_calls_per_rule_group, 10)  # 测试环境配置值
        self.assertEqual(service.max_calls_per_task, 50)  # 测试环境配置值
        
        # 测试参数覆盖
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=10,
            max_calls_per_rule_group=5,
            max_calls_per_task=20
        )
        
        self.assertEqual(service.max_calls_per_affected_function, 10)
        self.assertEqual(service.max_calls_per_rule_group, 5)
        self.assertEqual(service.max_calls_per_task, 20)
    
    def test_limit_hierarchy_logic(self):
        """测试限制层级逻辑"""
        service = LLMService(
            client=self.mock_client,
            max_calls_per_affected_function=3,  # 被修改函数级限制
            max_calls_per_rule_group=2,         # 规则组级限制
            max_calls_per_task=5                # 任务级限制
        )
        
        # 创建测试数据：2个函数，每个函数有4个规则组
        affected_functions = []
        for i in range(2):
            af = AffectedFunction(changed_lines=[], 
                name=f"func{i}",
                start_line=1,
                end_line=10,
                ungrouped_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            )
            affected_functions.append(af)
        
        diff = DiffResult(
            filepath="test.py",
            ungrouped_lines=[2, 5],
            
            
            
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        
        # Mock RuleManager
        with patch('gate_keeper.application.service.rule.rule_service.manager.RuleManager') as MockRuleManager:
            mock_rule_manager = MockRuleManager.return_value
            # 每个函数返回4个规则组
            mock_rule_manager.get_applicable_rules.return_value = {
                f"group{i}": [self.mock_rule] for i in range(4)
            }
            mock_rule_manager.simulate_grouping_plan.return_value = None
            
            # 执行分析
            result = service.analyze_mr(mr_result)
            
            # 实际Mock下会调用2次
            self.assertLessEqual(self.mock_client.call_count, 3)
            self.assertGreaterEqual(self.mock_client.call_count, 1)
            self.assertIsInstance(result, CheckMRResult)


if __name__ == "__main__":
    unittest.main() 