"""
规则服务模块单元测试

目标：验证规则分组检查系统的核心功能，确保AI驱动的代码规范检查能够正确按规则组执行，
并将各组结果按检查项进行合并，支持MR自动化代码检查流程。

测试覆盖：
- RuleManager: 规则加载、分组、筛选功能
- CheckExecutor: 规则执行器，模拟LLM调用  
- ResultAggregator: 结果聚合器，按检查项合并结果
- group_check: 主流程，规则组检查与结果合并

验证重点：
- 规则按category分组正确性
- 检查项与规则类型匹配逻辑
- 禁用规则过滤机制
- 结果聚合的完整性
"""

import unittest
from unittest.mock import MagicMock

from gate_keeper.application.service.rule import RuleManager


class DummyRuleManager(RuleManager):
    """
    模拟规则管理器，用于测试场景
    
    构造测试数据：
    - 组A: 2条函数类型规则 (R1, R2)
    - 组B: 2条类类型规则 (R3, R4) 
    - 组C: 1条禁用规则 (R5)
    
    目的：验证规则分组、类型筛选、启用状态过滤等核心逻辑
    """
    def __init__(self):
        self._rules = []
        self._loaded = True
        
        from gate_keeper.domain.rule.check_rule import CodeCheckRule
        self._rules = [
            # 组A：函数类型规则
            CodeCheckRule(
                id="R1", name="函数命名规范", 
                description="函数名应使用小写字母和下划线", 
                category=["组A"], enabled=True, languages=["function"]
            ),
            CodeCheckRule(
                id="R2", name="函数参数检查", 
                description="函数参数不应超过5个", 
                category=["组A"], enabled=True, languages=["function"]
            ),
            # 组B：类类型规则
            CodeCheckRule(
                id="R3", name="类命名规范", 
                description="类名应使用驼峰命名法", 
                category=["组B"], enabled=True, languages=["class"]
            ),
            CodeCheckRule(
                id="R4", name="类方法检查", 
                description="类应包含__init__方法", 
                category=["组B"], enabled=True, languages=["class"]
            ),
            # 组C：禁用规则
            CodeCheckRule(
                id="R5", name="禁用规则", 
                description="此规则被禁用，不应出现在检查结果中", 
                category=["组C"], enabled=False, languages=["function"]
            ),
        ]
    
    def load_rules(self):
        return self._rules


class TestRuleManagerNewFeatures(unittest.TestCase):
    """
    测试 RuleManager 的新功能
    
    验证目标：
    1. 缓存机制正常工作
    2. 规则管理功能（启用/禁用）
    3. 规则筛选功能（按分类、严重程度）
    4. 规则分组功能
    5. 统计摘要功能
    """
    
    def setUp(self):
        from gate_keeper.domain.rule.check_rule import CodeCheckRule
        self.test_rules = [
            CodeCheckRule(
                id="R1", name="规则1", 
                description="测试规则1", 
                category=["命名规范", "函数"], 
                enabled=True, 
                severity="高",
                languages=["function"]
            ),
            CodeCheckRule(
                id="R2", name="规则2", 
                description="测试规则2", 
                category=["命名规范", "变量"], 
                enabled=True, 
                severity="中",
                languages=["variable"]
            ),
            CodeCheckRule(
                id="R3", name="规则3", 
                description="测试规则3", 
                category=["代码结构"], 
                enabled=False, 
                severity="低",
                languages=["class"]
            ),
        ]

    def test_rule_manager_cache_functionality(self):
        """测试缓存功能"""
        # 创建一个模拟的 RuleManager
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = False
                self._cache_key = None
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 第一次加载
        rules1 = rule_manager.load_rules()
        self.assertEqual(len(rules1), 3)
        self.assertTrue(rule_manager._loaded)
        
        # 第二次加载（应该使用缓存）
        rules2 = rule_manager.load_rules()
        self.assertEqual(len(rules2), 3)
        self.assertEqual(rules1, rules2)

    def test_rule_manager_force_reload(self):
        """测试强制重新加载"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
                self.load_count = 0
            
            def _load_from_file(self):
                self.load_count += 1
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 强制重新加载
        rules = rule_manager.reload_rules()
        self.assertEqual(len(rules), 3)
        self.assertEqual(rule_manager.load_count, 1)

    def test_rule_manager_enable_disable_rules(self):
        """测试规则启用/禁用功能"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 测试禁用规则
        result = rule_manager.disable_rule("R1")
        self.assertTrue(result)
        rule = rule_manager.get_rule_by_id("R1")
        self.assertFalse(rule.enabled)
        
        # 测试启用规则
        result = rule_manager.enable_rule("R1")
        self.assertTrue(result)
        rule = rule_manager.get_rule_by_id("R1")
        self.assertTrue(rule.enabled)
        
        # 测试不存在的规则
        result = rule_manager.disable_rule("NONEXISTENT")
        self.assertFalse(result)

    def test_rule_manager_filter_by_category(self):
        """测试按分类筛选规则"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 按分类筛选（只返回启用的规则）
        naming_rules = rule_manager.get_rules_by_category("命名规范")
        self.assertEqual(len(naming_rules), 2)
        
        # 代码结构规则被禁用，所以不会返回
        structure_rules = rule_manager.get_rules_by_category("代码结构")
        self.assertEqual(len(structure_rules), 0)
        
        # 不存在的分类
        empty_rules = rule_manager.get_rules_by_category("不存在的分类")
        self.assertEqual(len(empty_rules), 0)
        
        # 测试获取所有规则（包括禁用的）
        all_rules = rule_manager.load_rules()
        structure_rules_all = [r for r in all_rules if "代码结构" in r.category]
        self.assertEqual(len(structure_rules_all), 1)

    def test_rule_manager_filter_by_severity(self):
        """测试按严重程度筛选规则"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 按严重程度筛选
        high_rules = rule_manager.get_rules_by_severity("高")
        self.assertEqual(len(high_rules), 1)
        self.assertEqual(high_rules[0].id, "R1")
        
        medium_rules = rule_manager.get_rules_by_severity("中")
        self.assertEqual(len(medium_rules), 1)
        self.assertEqual(medium_rules[0].id, "R2")

    def test_rule_manager_group_rules(self):
        """测试规则分组功能"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 按分类分组
        grouped = rule_manager.group_rules_by_category()
        
        # 应该有2个分组
        self.assertEqual(len(grouped), 2)
        
        # 检查命名规范分组
        naming_group = ("命名规范",)
        self.assertIn(naming_group, grouped)
        self.assertEqual(len(grouped[naming_group]), 2)
        
        # 检查代码结构分组
        structure_group = ("代码结构",)
        self.assertIn(structure_group, grouped)
        self.assertEqual(len(grouped[structure_group]), 1)

    def test_rule_manager_summary(self):
        """测试规则统计摘要"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 获取统计摘要
        summary = rule_manager.get_rules_summary()
        
        # 验证基本统计
        self.assertEqual(summary["total_rules"], 3)
        self.assertEqual(summary["enabled_rules"], 2)
        self.assertEqual(summary["disabled_rules"], 1)
        
        # 验证严重程度统计
        self.assertEqual(summary["severity_stats"]["高"], 1)
        self.assertEqual(summary["severity_stats"]["中"], 1)
        self.assertEqual(summary["severity_stats"]["低"], 1)
        
        # 验证分类统计
        self.assertEqual(summary["category_stats"]["命名规范"], 2)
        self.assertEqual(summary["category_stats"]["代码结构"], 1)

    def test_rule_manager_get_rule_by_id(self):
        """测试根据ID获取规则"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self._cache_key = "test"
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def _load_from_file(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 获取存在的规则
        rule = rule_manager.get_rule_by_id("R1")
        self.assertIsNotNone(rule)
        self.assertEqual(rule.name, "规则1")
        
        # 获取不存在的规则
        rule = rule_manager.get_rule_by_id("NONEXISTENT")
        self.assertIsNone(rule)


if __name__ == "__main__":
    unittest.main() 