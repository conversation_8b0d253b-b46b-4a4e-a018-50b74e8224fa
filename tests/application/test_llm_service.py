"""
LLMService模块单元测试

目标：验证LLM服务系统的核心功能，确保能够正确调用LLM后端、
并发处理多个检查任务，解析LLM响应，支持多种模型后端。

测试覆盖：
- LLMService: LLM服务核心功能
- 并发任务处理
- Prompt构建和响应解析
- 多种LLM后端支持

验证重点：
- LLM调用正确性
- 并发处理安全性
- 响应解析准确性
- 错误处理和异常恢复
"""

import unittest
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.llm import LLMService
from gate_keeper.config import config
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature, Parameter)
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """模拟LLM客户端，用于测试"""
    
    def __init__(self, responses=None):
        self.responses = responses or ["模拟LLM响应"]
        self.call_count = 0
        
    def generate(self, prompt, parameters, token=None):
        """始终返回结构化JSON字符串，保证ResultParser能正确解析"""
        self.call_count += 1
        if self.call_count <= len(self.responses):
            return self.responses[self.call_count - 1]
        # 默认返回结构化JSON
        return '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
    
    def get_config(self):
        return {"provider": "Mock"}


class TestLLMService(unittest.TestCase):
    """
    测试LLMService：LLM服务核心功能
    
    验证目标：
    1. 能正确初始化LLMService实例
    2. 能正确调用LLM进行代码分析
    3. 能正确处理并发任务
    4. 能正确解析LLM响应
    """
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockLLMClient()
        self.llm_service = LLMService(self.mock_client)
        
        # 准备测试数据
        self.rules = [
            CodeCheckRule(
                id="R1",
                name="函数命名规范",
                description="函数名应使用小写字母和下划线",
                category=["命名规范"],
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="R2", 
                name="函数参数检查",
                description="函数参数不应超过5个",
                category=["代码质量"],
                enabled=True,
                languages=["python"]
            )
        ]
        
        self.affected_function = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(a, b, c):\n    return a + b + c",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        
        # 设置有效的LLM响应
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]

    def test_llm_service_initialization(self):
        """
        测试LLMService初始化
        
        验证目的：确保LLMService能正确初始化，设置LLM客户端
        
        失败理解：如果测试失败，说明LLMService的构造函数存在问题
        """
        self.assertEqual(self.llm_service.client, self.mock_client)
        self.assertIsInstance(self.llm_service.client, LLMClient)

    def test_analyze_single_function_success(self):
        """
        测试分析单个函数：成功情况
        """
        # 构造RuleManager（可用真实或mock规则文件）
        from gate_keeper.application.service.rule import RuleManager
        rule_manager = RuleManager(rule_file_path="dummy.md")  # 路径可mock
        # mock规则加载，直接返回self.rules
        rule_manager.load_rules = lambda: self.rules

        # 执行分析
        result = self.llm_service._analyze_single_function(
            self.affected_function, "test.py", rule_manager
        )

        # 验证结果
        self.assertIsInstance(result, list)
        self.assertGreaterEqual(len(result), 1)
        for r in result:
            self.assertIsInstance(r, AnalyzeLLMResult)

        # 验证LLM客户端被调用
        self.assertEqual(self.mock_client.call_count, len(result))

    def test_analyze_single_function_empty_code(self):
        """
        测试分析单个函数：空代码情况
        
        验证目的：确保LLMService能正确处理空代码的情况
        
        失败理解：如果测试失败，说明空代码处理逻辑存在问题
        """
        empty_function = AffectedFunction(
            name="empty_function",
            start_line=1,
            end_line=1,
            changed_lines=[],
            code="",
            filepath="empty.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
        # mock RuleManager
        from gate_keeper.application.service.rule import RuleManager
        rule_manager = RuleManager(rule_file_path="dummy.md")
        rule_manager.get_applicable_rules = lambda af: {"default": self.rules}
        result = self.llm_service._analyze_single_function(
            empty_function, "empty.py", rule_manager
        )
        self.assertIsNone(result)
        self.assertEqual(self.mock_client.call_count, 0)

    def test_analyze_single_function_llm_exception(self):
        """
        测试分析单个函数：LLM调用异常
        """
        self.mock_client.generate = MagicMock(side_effect=Exception("LLM调用失败"))
        from gate_keeper.application.service.rule import RuleManager
        rule_manager = RuleManager(rule_file_path="dummy.md")
        rule_manager.get_applicable_rules = lambda af, **kwargs: {"default": self.rules}
        result = self.llm_service._analyze_single_function(
            self.affected_function, "test.py", rule_manager
        )
        self.assertEqual(result, [])

    def test_analyze_mr_concurrent_processing(self):
        """
        测试MR分析：并发处理
        """
        affected_functions = [
            AffectedFunction(
                name=f"func{i}",
                start_line=1,
                end_line=10,
                changed_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            ) for i in range(3)
        ]
        max_calls = getattr(config, 'max_llm_calls_per_function', 3)
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ] * max_calls
        diff = DiffResult(
            filepath="test.py",
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            ungrouped_lines=[2, 5],
            file_status="modified"
        )
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        # mock RuleManager.get_applicable_rules 只分1组
        from gate_keeper.application.service.rule import RuleManager
        orig_init = RuleManager.__init__
        def fake_init(self, *args, **kwargs):
            orig_init(self, *args, **kwargs)
            self.get_applicable_rules = lambda af, **kwargs: {"default": self.rules}
        RuleManager.__init__ = fake_init
        # 注入self.rules
        RuleManager.rules = self.rules
        result = self.llm_service.analyze_mr(mr_result)
        RuleManager.__init__ = orig_init
        del RuleManager.rules
        self.assertIsInstance(result, CheckMRResult)
        total_llm_results = sum(len(af.llm_results) for d in result.diffs for af in d.affected_functions)
        self.assertEqual(total_llm_results, len(affected_functions))
        for d in result.diffs:
            for af in d.affected_functions:
                self.assertIsInstance(af.llm_results, list)
                self.assertEqual(len(af.llm_results), 1)

    def test_analyze_mr_concurrent_workers_limit(self):
        """
        测试MR分析：并发worker限制
        """
        affected_functions = [
            AffectedFunction(
                name=f"func{i}",
                start_line=1,
                end_line=10,
                changed_lines=[2, 5],
                code=f"def func{i}(): pass",
                filepath="test.py",
                related_calls=[],
                related_definitions=[],
                llm_results=[]
            ) for i in range(5)
        ]
        from gate_keeper.config import config
        max_calls = getattr(config, 'max_llm_calls_per_function', 3)
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ] * max_calls
        diff = DiffResult(
            filepath="test.py",
            origin_file_content="def func0(): pass\n",
            new_file_content="def func0(): pass\n",
            affected_functions=affected_functions,
            ungrouped_lines=[2, 5],
            file_status="modified"
        )
        mr_result = CheckMRResult(
            mr_id=123,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff],
            affected_functions=affected_functions
        )
        # mock RuleManager.get_applicable_rules 只分1组
        from gate_keeper.application.service.rule import RuleManager
        orig_init = RuleManager.__init__
        def fake_init(self, *args, **kwargs):
            orig_init(self, *args, **kwargs)
            self.get_applicable_rules = lambda af, **kwargs: {"default": self.rules}
        RuleManager.__init__ = fake_init
        RuleManager.rules = self.rules
        result = self.llm_service.analyze_mr(mr_result,  max_workers=2)
        RuleManager.__init__ = orig_init
        del RuleManager.rules
        self.assertIsInstance(result, CheckMRResult)
        total_llm_results = sum(len(af.llm_results) for d in result.diffs for af in d.affected_functions)
        # 验证并发限制生效 - 每个函数都会调用LLM，所以总数应该等于函数数量
        self.assertEqual(total_llm_results, 5)  # 5个函数，每个函数1次调用
        for d in result.diffs:
            for af in d.affected_functions:
                self.assertIsInstance(af.llm_results, list)
                # 由于规则组级限制，部分函数可能没有结果
                if len(af.llm_results) > 0:
                    self.assertEqual(len(af.llm_results), 1)

    def test_analyze_mr_empty_diffs(self):
        """
        测试MR分析：空diffs
        """
        mr_result = CheckMRResult(
            mr_id=3,
            base_branch="main",
            dev_branch="dev",
            diffs=[]
        )
        result = self.llm_service.analyze_mr(mr_result)
        self.assertIsInstance(result, CheckMRResult)
        self.assertEqual(result.diffs, [])

    @unittest.skip("mock与新主流程不兼容，已移除")
    def test_analyze_mr_with_related_context(self):
        """
        测试MR分析：带有上下文
        """
        from gate_keeper.external.code_analyzer.models.function import Function
        related_def = Function.create_simple(
            name="helper_function",
            filepath="helper.py",
            start_line=1,
            end_line=2,
            signature=FunctionSignature(name="helper_function", parameters=[], return_type=None)
        )
        affected_function = AffectedFunction(
            name="main_function",
            start_line=1,
            end_line=5,
            changed_lines=[2],
            code="def main_function():\n    helper_function()",
            filepath="main.py",
            related_calls=[],
            related_definitions=[related_def],
            llm_results=[]
        )
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        diff = DiffResult(
            filepath="main.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=[affected_function],
            ungrouped_lines=[],
            file_status="modified"
        )
        mr_result = CheckMRResult(
            mr_id=4,
            base_branch="main",
            dev_branch="dev",
            diffs=[diff]
        )
        results = self.llm_service.analyze_mr(mr_result)
        assert "main_function" in results
        assert results["main_function"][0]["is_pass"] is True
        assert results["main_function"][0]["reason"] == "检查通过"
        assert results["main_function"][0]["violations"] == []


if __name__ == "__main__":
    unittest.main() 