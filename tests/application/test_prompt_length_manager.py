import time
import unittest
from typing import List
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import CallChainContext
from gate_keeper.application.service.prompt import (
    ContentSegment, ContentType, PromptAllocation, PromptLengthManager)
from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestPromptLengthManager(unittest.TestCase):
    """测试提示词长度管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.manager = PromptLengthManager(max_prompt_length=10000)
        
        # 创建测试规则
        self.test_rules = [
            CodeCheckRule(
                id="rule1",
                name="安全规则",
                description="这是一个安全相关的规则，非常重要",
                category=["安全", "security"],
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="rule2",
                name="性能规则",
                description="这是一个性能相关的规则",
                category=["性能", "performance"],
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="rule3",
                name="命名规则",
                description="这是一个命名规范规则",
                category=["命名", "naming"],
                enabled=True,
                languages=["python"]
            )
        ]
        
        # 创建测试上下文
        self.test_contexts = [
            CallChainContext(
                chain=["main", "process_data", "validate_input"],
                functions=[],
                relevance_score=0.9,
                context_size=1000
            ),
            CallChainContext(
                chain=["main", "process_data"],
                functions=[],
                relevance_score=0.7,
                context_size=800
            ),
            CallChainContext(
                chain=["main", "process_data", "validate_input", "sanitize_data"],
                functions=[],
                relevance_score=0.5,
                context_size=1200
            )
        ]
        
        # 创建测试函数
        self.test_function = AffectedFunction(
            name="test_function",
            filepath="test.py",
            code="def test_function():\n    pass",
            start_line=1,
            end_line=2,
            changed_lines=[1, 2],
            related_definitions=[]
        )
    
    # ==================== 基本功能测试 ====================
    
    def test_calculate_prompt_allocation(self):
        """测试提示词分配计算"""
        system_prompt = "你是一个代码质量检查助手"
        target_code = "def test(): pass"
        output_format = "请返回JSON格式"
        
        allocation = self.manager.calculate_prompt_allocation(
            system_prompt, target_code, output_format
        )
        
        # 验证分配结果
        self.assertEqual(allocation.total_limit, 10000)
        self.assertGreater(allocation.rules_length, 0)
        self.assertGreater(allocation.context_length, 0)
        self.assertGreaterEqual(allocation.remaining_length, 0)
        
        # 验证必需内容长度
        required_length = (allocation.system_prompt_length + 
                          allocation.target_code_length + 
                          allocation.output_format_length)
        self.assertLessEqual(required_length, allocation.total_limit)
    
    def test_select_optimal_rules(self):
        """测试最优规则选择"""
        max_length = 1000
        selected_rules = self.manager.select_optimal_rules(self.test_rules, max_length)
        
        # 验证选择结果
        self.assertLessEqual(len(selected_rules), len(self.test_rules))
        
        # 验证优先级排序（安全规则应该优先）
        if len(selected_rules) > 1:
            first_rule = selected_rules[0]
            self.assertIn("安全", first_rule.category or [])
    
    def test_select_optimal_contexts(self):
        """测试最优上下文选择"""
        max_length = 1000
        selected_contexts = self.manager.select_optimal_contexts(self.test_contexts, max_length)
        
        # 验证选择结果
        self.assertLessEqual(len(selected_contexts), len(self.test_contexts))
        
        # 验证相关性排序（评分高的应该优先）
        if len(selected_contexts) > 1:
            first_context = selected_contexts[0]
            self.assertEqual(first_context.relevance_score, 0.9)
    
    def test_generate_optimized_prompt(self):
        """测试优化提示词生成"""
        system_prompt = "你是一个代码质量检查助手"
        output_format = "请返回JSON格式"
        
        optimized_prompt, allocation = self.manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=self.test_rules,
            affected_function=self.test_function,
            contexts=self.test_contexts,
            output_format=output_format
        )
        
        # 验证提示词长度
        self.assertLessEqual(len(optimized_prompt), self.manager.max_prompt_length)
        
        # 验证提示词内容
        self.assertIn(system_prompt, optimized_prompt)
        self.assertIn(self.test_function.code, optimized_prompt)
        self.assertIn(output_format, optimized_prompt)
        
        # 验证分配方案
        self.assertIsInstance(allocation, PromptAllocation)
    
    # ==================== 边界条件测试 ====================
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空规则列表
        empty_rules = self.manager.select_optimal_rules([], 1000)
        self.assertEqual(len(empty_rules), 0)
        
        # 测试空上下文列表
        empty_contexts = self.manager.select_optimal_contexts([], 1000)
        self.assertEqual(len(empty_contexts), 0)
        
        # 测试长度限制为0
        zero_rules = self.manager.select_optimal_rules(self.test_rules, 0)
        self.assertEqual(len(zero_rules), 0)
        
        # 测试必需内容超过总限制
        long_system_prompt = "x" * 15000
        allocation = self.manager.calculate_prompt_allocation(
            long_system_prompt, "def test(): pass"
        )
        self.assertEqual(allocation.remaining_length, 0)
    
    def test_content_truncation(self):
        """测试内容截断功能"""
        # 测试规则截断
        long_rule = CodeCheckRule(
            id="long_rule",
            name="很长的规则",
            description="这是一个非常非常长的规则描述，包含了很多很多的内容，"
                       "这个描述会超过长度限制，需要被截断处理。",
            category=["测试"],
            enabled=True,
            languages=["python"]
        )
        
        max_length = 50
        selected_rules = self.manager.select_optimal_rules([long_rule], max_length)
        
        # 验证截断结果
        self.assertEqual(len(selected_rules), 1)
        self.assertLessEqual(len(selected_rules[0].description), max_length)
    
    def test_very_small_limits(self):
        """测试很小的长度限制"""
        # 测试极小的长度限制
        tiny_manager = PromptLengthManager(max_prompt_length=100)
        
        system_prompt = "测试"
        target_code = "def test(): pass"
        
        allocation = tiny_manager.calculate_prompt_allocation(system_prompt, target_code)
        
        # 验证分配结果合理
        self.assertGreaterEqual(allocation.remaining_length, 0)
        # 注意：分配的长度可能超过剩余长度，因为分配是基于比例的
        # 实际使用时会在选择阶段进行限制
    
    def test_negative_lengths(self):
        """测试负数长度处理"""
        # 测试负数长度限制
        selected_rules = self.manager.select_optimal_rules(self.test_rules, -100)
        self.assertEqual(len(selected_rules), 0)
        
        selected_contexts = self.manager.select_optimal_contexts(self.test_contexts, -100)
        self.assertEqual(len(selected_contexts), 0)
    
    # ==================== 分配策略测试 ====================
    
    def test_allocation_strategies(self):
        """测试不同分配策略"""
        # 测试平衡策略
        balanced_manager = PromptLengthManager(
            max_prompt_length=10000,
            allocation_strategy="balanced"
        )
        
        # 测试上下文重点策略
        context_heavy_manager = PromptLengthManager(
            max_prompt_length=10000,
            allocation_strategy="context_heavy"
        )
        
        # 测试规则重点策略
        rules_heavy_manager = PromptLengthManager(
            max_prompt_length=10000,
            allocation_strategy="rules_heavy"
        )
        
        system_prompt = "测试"
        target_code = "def test(): pass"
        
        balanced_allocation = balanced_manager.calculate_prompt_allocation(
            system_prompt, target_code
        )
        context_heavy_allocation = context_heavy_manager.calculate_prompt_allocation(
            system_prompt, target_code
        )
        rules_heavy_allocation = rules_heavy_manager.calculate_prompt_allocation(
            system_prompt, target_code
        )
        
        # 验证策略差异
        self.assertGreater(context_heavy_allocation.context_length, 
                          balanced_allocation.context_length)
        self.assertGreater(rules_heavy_allocation.rules_length, 
                          balanced_allocation.rules_length)
    
    def test_invalid_strategy(self):
        """测试无效策略处理"""
        # 测试无效策略（应该回退到平衡策略）
        invalid_manager = PromptLengthManager(
            max_prompt_length=10000,
            allocation_strategy="invalid_strategy"
        )
        
        system_prompt = "测试"
        target_code = "def test(): pass"
        
        allocation = invalid_manager.calculate_prompt_allocation(system_prompt, target_code)
        
        # 验证使用默认策略
        self.assertGreater(allocation.rules_length, 0)
        self.assertGreater(allocation.context_length, 0)
    
    # ==================== 规则优先级测试 ====================
    
    def test_rule_priority_calculation(self):
        """测试规则优先级计算"""
        # 安全规则应该有更高优先级
        security_rule = CodeCheckRule(
            id="security",
            name="安全规则",
            description="安全相关",
            category=["安全"],
            enabled=True,
            languages=["python"]
        )
        
        naming_rule = CodeCheckRule(
            id="naming",
            name="命名规则",
            description="命名相关",
            category=["命名"],
            enabled=True,
            languages=["python"]
        )
        
        # 验证优先级计算
        security_priority = self.manager._calculate_rule_priority(security_rule)
        naming_priority = self.manager._calculate_rule_priority(naming_rule)
        
        self.assertGreater(security_priority, naming_priority)
    
    def test_rule_priority_edge_cases(self):
        """测试规则优先级边界情况"""
        # 测试空类别
        empty_category_rule = CodeCheckRule(
            id="empty",
            name="空类别规则",
            description="测试",
            category=[],
            enabled=True,
            languages=["python"]
        )
        
        # 测试空语言
        empty_language_rule = CodeCheckRule(
            id="empty_lang",
            name="空语言规则",
            description="测试",
            category=["测试"],
            enabled=True,
            languages=[]
        )
        
        # 测试None值（使用空列表代替）
        none_category_rule = CodeCheckRule(
            id="none",
            name="None类别规则",
            description="测试",
            category=[],
            enabled=True,
            languages=[]
        )
        
        # 验证不会抛出异常
        self.manager._calculate_rule_priority(empty_category_rule)
        self.manager._calculate_rule_priority(empty_language_rule)
        self.manager._calculate_rule_priority(none_category_rule)
    
    # ==================== 性能测试 ====================
    
    def test_performance_with_large_lists(self):
        """测试大量数据的性能"""
        # 创建大量规则
        large_rules = []
        for i in range(100):
            rule = CodeCheckRule(
                id=f"rule_{i}",
                name=f"规则{i}",
                description=f"这是第{i}个规则的描述",
                category=["测试"],
                enabled=True,
                languages=["python"]
            )
            large_rules.append(rule)
        
        # 创建大量上下文
        large_contexts = []
        for i in range(50):
            context = CallChainContext(
                chain=[f"func_{j}" for j in range(i % 5 + 1)],
                functions=[],
                relevance_score=0.1 + (i * 0.01),
                context_size=100 + i * 10
            )
            large_contexts.append(context)
        
        # 测试性能
        start_time = time.time()
        
        selected_rules = self.manager.select_optimal_rules(large_rules, 5000)
        selected_contexts = self.manager.select_optimal_contexts(large_contexts, 3000)
        
        end_time = time.time()
        
        # 验证性能（应该在1秒内完成）
        self.assertLess(end_time - start_time, 1.0)
        
        # 验证选择结果合理
        self.assertLessEqual(len(selected_rules), len(large_rules))
        self.assertLessEqual(len(selected_contexts), len(large_contexts))
    
    # ==================== 错误处理测试 ====================
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试None输入（现在会返回空列表而不是抛出异常）
        none_rules = self.manager.select_optimal_rules(None, 1000)
        self.assertEqual(len(none_rules), 0)
        
        none_contexts = self.manager.select_optimal_contexts(None, 1000)
        self.assertEqual(len(none_contexts), 0)
        
        # 测试无效的规则对象
        invalid_rules = [Mock(), Mock()]
        selected_rules = self.manager.select_optimal_rules(invalid_rules, 1000)
        self.assertEqual(len(selected_rules), 0)
    
    def test_malformed_data(self):
        """测试畸形数据处理"""
        # 测试包含None的规则列表
        mixed_rules = [self.test_rules[0], None, self.test_rules[1]]
        selected_rules = self.manager.select_optimal_rules(mixed_rules, 1000)
        
        # 应该跳过None值
        self.assertLessEqual(len(selected_rules), len([r for r in mixed_rules if r is not None]))
    
    # ==================== 模块间关系测试 ====================
    
    def test_integration_with_context_manager(self):
        """测试与上下文管理器的集成"""
        # 模拟上下文管理器
        mock_context_manager = Mock()
        manager_with_context = PromptLengthManager(
            max_prompt_length=10000,
            context_manager=mock_context_manager
        )
        
        # 验证上下文管理器被正确设置
        self.assertEqual(manager_with_context.context_manager, mock_context_manager)
    
    def test_rule_selection_consistency(self):
        """测试规则选择的一致性"""
        # 多次运行相同的选择，结果应该一致
        max_length = 1000
        result1 = self.manager.select_optimal_rules(self.test_rules, max_length)
        result2 = self.manager.select_optimal_rules(self.test_rules, max_length)
        
        # 验证结果一致性
        self.assertEqual(len(result1), len(result2))
        if result1:
            self.assertEqual(result1[0].id, result2[0].id)
    
    def test_context_selection_consistency(self):
        """测试上下文选择的一致性"""
        # 多次运行相同的选择，结果应该一致
        max_length = 1000
        result1 = self.manager.select_optimal_contexts(self.test_contexts, max_length)
        result2 = self.manager.select_optimal_contexts(self.test_contexts, max_length)
        
        # 验证结果一致性
        self.assertEqual(len(result1), len(result2))
        if result1:
            self.assertEqual(result1[0].relevance_score, result2[0].relevance_score)
    
    def test_prompt_building_consistency(self):
        """测试提示词构建的一致性"""
        system_prompt = "测试系统提示词"
        output_format = "测试输出格式"
        
        # 多次构建相同的提示词
        prompt1, allocation1 = self.manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=self.test_rules,
            affected_function=self.test_function,
            contexts=self.test_contexts,
            output_format=output_format
        )
        
        prompt2, allocation2 = self.manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=self.test_rules,
            affected_function=self.test_function,
            contexts=self.test_contexts,
            output_format=output_format
        )
        
        # 验证结果一致性
        self.assertEqual(len(prompt1), len(prompt2))
        self.assertEqual(allocation1.total_limit, allocation2.total_limit)
    
    # ==================== 配置测试 ====================
    
    def test_configuration_validation(self):
        """测试配置验证"""
        # 测试无效的最大长度
        with self.assertRaises(ValueError):
            PromptLengthManager(max_prompt_length=-1000)
        
        # 测试零长度
        zero_manager = PromptLengthManager(max_prompt_length=0)
        self.assertEqual(zero_manager.max_prompt_length, 0)
    
    def test_default_configuration(self):
        """测试默认配置"""
        # 测试默认构造函数
        default_manager = PromptLengthManager()
        
        # 验证默认值
        self.assertEqual(default_manager.max_prompt_length, 32000)
        self.assertEqual(default_manager.allocation_strategy, "balanced")
        self.assertIsNone(default_manager.context_manager)


class TestContentSegment(unittest.TestCase):
    """测试内容片段"""
    
    def test_content_segment_creation(self):
        """测试内容片段创建"""
        segment = ContentSegment(
            content_type=ContentType.RULES,
            content="测试内容",
            length=10,
            priority=0.8,
            is_required=False
        )
        
        self.assertEqual(segment.content_type, ContentType.RULES)
        self.assertEqual(segment.content, "测试内容")
        self.assertEqual(segment.length, 10)
        self.assertEqual(segment.priority, 0.8)
        self.assertFalse(segment.is_required)
    
    def test_content_segment_defaults(self):
        """测试内容片段默认值"""
        segment = ContentSegment(
            content_type=ContentType.CONTEXT,
            content="测试",
            length=5,
            priority=0.5
        )
        
        # 验证默认值
        self.assertTrue(segment.is_required)  # 默认值应该是True


class TestPromptAllocation(unittest.TestCase):
    """测试提示词分配方案"""
    
    def test_prompt_allocation_creation(self):
        """测试分配方案创建"""
        allocation = PromptAllocation(
            total_limit=10000,
            system_prompt_length=100,
            rules_length=2000,
            context_length=2000,
            target_code_length=500,
            output_format_length=50,
            remaining_length=5450
        )
        
        # 验证所有字段
        self.assertEqual(allocation.total_limit, 10000)
        self.assertEqual(allocation.system_prompt_length, 100)
        self.assertEqual(allocation.rules_length, 2000)
        self.assertEqual(allocation.context_length, 2000)
        self.assertEqual(allocation.target_code_length, 500)
        self.assertEqual(allocation.output_format_length, 50)
        self.assertEqual(allocation.remaining_length, 5450)
    
    def test_prompt_allocation_validation(self):
        """测试分配方案验证"""
        # 验证总长度计算
        allocation = PromptAllocation(
            total_limit=10000,
            system_prompt_length=100,
            rules_length=2000,
            context_length=2000,
            target_code_length=500,
            output_format_length=50,
            remaining_length=5450
        )
        
        # 验证各部分长度之和不超过总限制
        total_used = (allocation.system_prompt_length + 
                     allocation.rules_length + 
                     allocation.context_length + 
                     allocation.target_code_length + 
                     allocation.output_format_length)
        
        self.assertLessEqual(total_used, allocation.total_limit)


if __name__ == "__main__":
    unittest.main() 