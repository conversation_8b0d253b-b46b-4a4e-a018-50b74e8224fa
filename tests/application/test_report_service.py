"""
ReportService模块单元测试

目标：验证报告服务系统的核心功能，确保能够正确生成Markdown格式报告、
发布MR评论，支持多种输出格式和错误处理。

测试覆盖：
- ReportService: 报告服务核心功能
- Markdown报告生成
- MR评论发布
- 错误处理和异常恢复

验证重点：
- 报告格式正确性
- MR评论发布准确性
- 错误处理健壮性
- 多种输入格式支持
"""

import unittest
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.service.report import ReportService
from gate_keeper.domain.value_objects.analysis_result import (
    AggregatedAnalyzeLLMResult, AnalyzeLLMResult, ViolationItem)


class TestReportService(unittest.TestCase):
    """
    测试ReportService：报告服务核心功能
    
    验证目标：
    1. 能正确初始化ReportService实例
    2. 能正确生成Markdown格式报告
    3. 能正确发布MR评论
    4. 能正确处理各种输入格式
    """
    
    def setUp(self):
        """设置测试环境"""
        self.token = "test_token"
        
        # 创建mock的Git客户端，正确继承接口以通过Pydantic验证
        from gate_keeper.application.interfaces.git_intf import \
            IGitPlatformService
        
        self.mock_git_client = Mock(spec=IGitPlatformService)
        self.mock_git_client.get_discussions_of_mr = MagicMock(return_value=[])
        self.mock_git_client.post_discussion_to_mr = MagicMock(return_value=True)
        self.mock_git_client.comment_mr = MagicMock(return_value=True)
        self.mock_git_client.get_mr_info = MagicMock(return_value={})
        
        # 使用mock客户端创建ReportService
        self.report_service = ReportService(
            access_token=self.token,
            git_client=self.mock_git_client
        )
        
        # 准备测试数据
        self.sample_results = [
            AnalyzeLLMResult(
                is_pass=False,
                reason="检测到多个代码风格违规",
                violations=[
                    ViolationItem(
                        rule_id="STYLE001",
                        location={"file_path": "src/main.py"},
                        message="变量命名不规范",
                        severity="high",
                        rule_content="变量命名应使用snake_case"
                    ),
                    ViolationItem(
                        rule_id="STYLE002",
                        location={"file_path": "src/utils.py"},
                        message="缺少函数注释",
                        severity="medium",
                        rule_content="函数必须有文档字符串"
                    )
                ],
                code="print('hi')"
            ),
            AnalyzeLLMResult(
                is_pass=True,
                reason="代码检查通过",
                violations=[]
            )
        ]

    def test_report_service_initialization(self):
        """
        测试ReportService初始化
        
        验证目的：确保ReportService能正确初始化，设置token
        
        失败理解：如果测试失败，说明ReportService的构造函数存在问题
        """
        self.assertEqual(self.report_service.token, self.token)

    def test_generate_markdown_report_with_violations(self):
        """
        测试生成Markdown报告：包含违规情况
        
        验证目的：确保ReportService能正确生成包含违规信息的Markdown报告
        
        失败理解：如果测试失败，说明Markdown报告生成逻辑存在问题
        """
        # 执行报告生成
        report_content = self.report_service.generate_markdown_report(self.sample_results)
        
        # 验证报告内容
        self.assertIsNotNone(report_content)
        self.assertIsInstance(report_content, str)
        
        # 验证报告包含必要元素
        self.assertIn("[编码守则]", report_content)
        self.assertIn("### 检测批次1: ❌ 未通过", report_content)
        self.assertIn("检测到多个代码风格违规", report_content)
        self.assertIn("| 规则ID | 优先级 | 规则内容 | 文件路径 | 违规信息 |", report_content)
        self.assertIn("STYLE001", report_content)
        self.assertIn("STYLE002", report_content)
        self.assertIn("变量命名不规范", report_content)
        self.assertIn("缺少函数注释", report_content)

    def test_generate_markdown_report_all_passed(self):
        """
        测试生成Markdown报告：全部通过
        
        验证目的：确保ReportService能正确处理全部检查通过的情况
        
        失败理解：如果测试失败，说明全部通过情况的处理逻辑存在问题
        """
        # 准备全部通过的测试数据
        all_passed_results = [
            AnalyzeLLMResult(
                is_pass=True,
                reason="代码检查通过",
                violations=[]
            ),
            AnalyzeLLMResult(
                is_pass=True,
                reason="所有规则检查通过",
                violations=[]
            )
        ]
        
        # 执行报告生成
        report_content = self.report_service.generate_markdown_report(all_passed_results)
        
        # 验证报告内容
        self.assertIsNotNone(report_content)
        self.assertEqual(report_content, "")

    def test_generate_markdown_report_empty_results(self):
        """
        测试生成Markdown报告：空结果列表
        
        验证目的：确保ReportService能正确处理空结果列表
        
        失败理解：如果测试失败，说明空结果处理逻辑存在问题
        """
        # 执行报告生成
        report_content = self.report_service.generate_markdown_report([])
        
        # 验证报告内容
        self.assertIsNotNone(report_content)
        self.assertEqual(report_content, "")

    def test_generate_markdown_report_none_results(self):
        """
        测试生成Markdown报告：None结果
        
        验证目的：确保ReportService能正确处理None结果
        
        失败理解：如果测试失败，说明None结果处理逻辑存在问题
        """
        # 执行报告生成
        report_content = self.report_service.generate_markdown_report(None)
        
        # 验证报告内容
        self.assertIsNotNone(report_content)
        self.assertEqual(report_content, "")

    def test_generate_markdown_report_malformed_data(self):
        """
        测试生成Markdown报告：格式错误的数据
        
        验证目的：确保ReportService能正确处理格式错误的数据
        
        失败理解：如果测试失败，说明错误数据处理逻辑存在问题
        """
        # 准备格式错误的测试数据
        malformed_results = [
            AnalyzeLLMResult(
                is_pass=False,
                reason="测试违规",
                violations=[]  # 空违规列表
            ),
            AnalyzeLLMResult(
                is_pass=True,
                reason="",  # 空原因字段
                violations=[]
            )
        ]
        
        # 执行报告生成
        report_content = self.report_service.generate_markdown_report(malformed_results)
        
        # 验证报告内容 - 由于所有结果都是通过或没有有效违规项，报告应该只包含头部信息
        self.assertIn("## 代码规范检查报告", report_content)
        self.assertIn("[编码守则]", report_content)

    @patch('gate_keeper.application.service.report.report_service.requests.post')
    def test_post_mr_comment_success(self, mock_post):
        """
        测试发布MR评论：成功情况
        
        验证目的：确保ReportService能正确发布MR评论
        
        失败理解：如果测试失败，说明MR评论发布逻辑存在问题
        """
        # 模拟成功的HTTP响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"id": 123}
        mock_post.return_value = mock_response
        
        # 准备测试数据
        repo_url = "https://gitee.com/test/repo"
        mr_id = 456
        content = "测试评论内容"
        
        # 执行评论发布
        result = self.report_service.post_mr_comment(repo_url, mr_id, content)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证HTTP请求
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertIn("headers", kwargs)
        self.assertIn("Authorization", kwargs["headers"])
        self.assertEqual(kwargs["headers"]["Authorization"], f"Bearer {self.token}")

    @patch('gate_keeper.application.service.report.report_service.requests.post')
    def test_post_mr_comment_http_error(self, mock_post):
        """
        测试发布MR评论：HTTP错误
        
        验证目的：确保ReportService能正确处理HTTP错误
        
        失败理解：如果测试失败，说明HTTP错误处理逻辑存在问题
        """
        # 模拟HTTP错误响应
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.text = "Not Found"
        mock_post.return_value = mock_response
        
        # 准备测试数据
        repo_url = "https://gitee.com/test/repo"
        mr_id = 456
        content = "测试评论内容"
        
        # 执行评论发布
        result = self.report_service.post_mr_comment(repo_url, mr_id, content)
        
        # 验证结果
        self.assertFalse(result)

    @patch('gate_keeper.application.service.report.report_service.requests.post')
    def test_post_mr_comment_network_exception(self, mock_post):
        """
        测试发布MR评论：网络异常
        
        验证目的：确保ReportService能正确处理网络异常
        
        失败理解：如果测试失败，说明网络异常处理逻辑存在问题
        """
        # 模拟网络异常
        mock_post.side_effect = Exception("网络连接失败")
        
        # 准备测试数据
        repo_url = "https://gitee.com/test/repo"
        mr_id = 456
        content = "测试评论内容"
        
        # 执行评论发布
        result = self.report_service.post_mr_comment(repo_url, mr_id, content)
        
        # 验证结果
        self.assertFalse(result)

    def test_post_mr_comment_invalid_url(self):
        """
        测试发布MR评论：无效URL
        
        验证目的：确保ReportService能正确处理无效URL
        
        失败理解：如果测试失败，说明URL验证逻辑存在问题
        """
        # 准备无效URL的测试数据
        invalid_url = "invalid_url"
        mr_id = 456
        content = "测试评论内容"
        
        # 执行评论发布
        result = self.report_service.post_mr_comment(invalid_url, mr_id, content)
        
        # 验证结果
        self.assertFalse(result)

    def test_post_mr_comment_empty_content(self):
        """
        测试发布MR评论：空内容
        
        验证目的：确保ReportService能正确处理空内容
        
        失败理解：如果测试失败，说明空内容处理逻辑存在问题
        """
        # 准备空内容的测试数据
        repo_url = "https://gitee.com/test/repo"
        mr_id = 456
        content = ""
        
        # 执行评论发布
        result = self.report_service.post_mr_comment(repo_url, mr_id, content)
        
        # 验证结果
        self.assertFalse(result)

    def test_generate_markdown_report_with_complex_violations(self):
        """
        测试生成Markdown报告：复杂违规情况
        
        验证目的：确保ReportService能正确处理复杂的违规信息
        
        失败理解：如果测试失败，说明复杂违规处理逻辑存在问题
        """
        # 准备复杂违规的测试数据
        complex_results = [
            AnalyzeLLMResult(
                is_pass=False,
                reason="检测到严重代码质量问题",
                violations=[
                    ViolationItem(
                        rule_id="SECURITY001",
                        location={"file_path": "src/auth.py", "line": 15},
                        message="存在SQL注入风险",
                        severity="high",
                        rule_content="禁止使用字符串拼接构造SQL查询"
                    ),
                    ViolationItem(
                        rule_id="PERFORMANCE001",
                        location={"file_path": "src/db.py", "line": 42},
                        message="数据库连接未正确关闭",
                        severity="medium",
                        rule_content="数据库连接必须在finally块中关闭"
                    )
                ],
                code="query = f\"SELECT * FROM users WHERE id = {user_id}\""
            )
        ]
        
        # 执行报告生成
        report_content = self.report_service.generate_markdown_report(complex_results)
        
        # 验证报告内容
        self.assertIsNotNone(report_content)
        self.assertIn("[编码守则]", report_content)
        self.assertIn("### 检测批次1: ❌ 未通过", report_content)
        self.assertIn("检测到严重代码质量问题", report_content)
        self.assertIn("| 规则ID | 优先级 | 规则内容 | 文件路径 | 违规信息 |", report_content)
        self.assertIn("SECURITY001", report_content)
        self.assertIn("PERFORMANCE001", report_content)
        self.assertIn("存在SQL注入风险", report_content)
        self.assertIn("数据库连接未正确关闭", report_content)
        self.assertIn("src/auth.py", report_content)
        self.assertIn("src/db.py", report_content)

    def test_generate_markdown_report_with_call_id(self):
        """
        测试生成Markdown报告时包含call_id（调试ID）
        """
        result_with_call_id = AnalyzeLLMResult(
            is_pass=False,
            reason="检测到代码缺陷",
            violations=[
                ViolationItem(
                    rule_id="BUG001",
                    location={"file_path": "src/bug.py"},
                    message="存在bug",
                    severity="critical",
                    rule_content="禁止出现bug"
                )
            ],
            code="def buggy(): pass",
            call_id="test-call-id-123"
        )
        report_content = self.report_service.generate_markdown_report([result_with_call_id])
        self.assertIn("调试ID", report_content)
        self.assertIn("test-call-id-123", report_content)
        self.assertIn("BUG001", report_content)
        self.assertIn("存在bug", report_content)

    def test_generate_markdown_report_with_aggregated_call_ids(self):
        """
        测试生成Markdown报告时包含聚合的call_ids（调试ID列表）
        """
        aggregated_result = AggregatedAnalyzeLLMResult(
            is_pass=False,
            reason="函数 'test_function' 存在代码规范问题",
            violations=[
                ViolationItem(
                    rule_id="STYLE001",
                    location={"file_path": "src/test.py"},
                    message="变量命名不规范",
                    severity="high",
                    rule_content="变量命名应使用snake_case"
                ),
                ViolationItem(
                    rule_id="STYLE002",
                    location={"file_path": "src/test.py"},
                    message="缺少函数注释",
                    severity="medium",
                    rule_content="函数必须有文档字符串"
                )
            ],
            code="def test_function():\n    return True",
            call_ids=["call-id-1", "call-id-2", "call-id-3"],
            function_name="test_function",
            file_path="src/test.py"
        )
        report_content = self.report_service.generate_markdown_report([aggregated_result])
        
        # 验证聚合的call_ids
        self.assertIn("调试ID", report_content)
        self.assertIn("call-id-1, call-id-2, call-id-3", report_content)
        
        # 验证函数信息
        self.assertIn("函数", report_content)
        self.assertIn("test_function", report_content)
        self.assertIn("文件", report_content)
        self.assertIn("src/test.py", report_content)
        
        # 验证违规信息
        self.assertIn("STYLE001", report_content)
        self.assertIn("STYLE002", report_content)
        self.assertIn("变量命名不规范", report_content)
        self.assertIn("缺少函数注释", report_content)


def test_markdown_table_with_long_violation_message():
    """测试包含换行符和长违规信息的markdown表格生成"""
    from gate_keeper.application.service.report import ReportService
    from gate_keeper.domain.value_objects.analysis_result import (
        AnalyzeLLMResult, ViolationItem)

    # 创建一个包含换行符的违规信息测试结果
    long_message = """结果无法解析 1 validation error for AnalyzeLLMResult reason Input should be a valid string [type=string_type, input_value=None, input_type=NoneType] For further information visit https://errors.pydantic.dev/2.11/v/string_type

这里有一些代码示例：
def bad_function():
    pass

还有一些其他的内容，比如：
1. 第一点说明
2. 第二点说明
3. 第三点说明

最后还有一些总结性的内容。"""
    violation = ViolationItem(
        rule_id="RULE001",
        severity="high",
        rule_content="函数命名规范",
        location={"file_path": "test_file.py"},
        message=long_message
    )
    result = AnalyzeLLMResult(
        is_pass=False,
        reason="发现代码质量问题",
        violations=[violation],
        code="def bad_function():\n    pass",
        call_id="test_call_123"
    )
    report_service = ReportService()
    markdown_report = report_service.generate_violation_markdown_report(result, report_id="TEST001", is_independent=True)
    # 检查是否包含HTML换行标签而不是普通换行符
    assert '<br>' in markdown_report, "违规信息没有使用HTML换行标签"
    # 检查表格行数
    lines = markdown_report.split('\n')
    table_lines = [line for line in lines if line.startswith('|')]
    assert len(table_lines) == 3, f"表格行数异常: {len(table_lines)}"
    # 检查是否有被分割的表格行
    has_split_table = any('|' in line and line.count('|') < 5 for line in lines if line.strip() and not line.startswith('|'))
    assert not has_split_table, "表格被错误分割"


class TestReportServiceFormatHandling(unittest.TestCase):
    """测试ReportService的格式处理功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.report_service = ReportService()
    
    def test_markdown_table_format_handling(self):
        """测试markdown表格格式处理"""
        # 测试数据：包含各种可能导致表格错乱的内容
        test_violations = [
            ViolationItem(
                rule_id="STYLE001",
                location={"file_path": "src/test.py"},
                message="变量命名不规范\n应该使用snake_case格式",  # 包含换行符
                severity="high",
                rule_content="变量命名应使用snake_case格式"
            ),
            ViolationItem(
                rule_id="STYLE002", 
                location={"file_path": "src/example.py"},
                message="缺少函数注释，应该添加```docstring```",  # 包含代码块标记
                severity="medium",
                rule_content="函数必须有文档字符串"
            ),
            ViolationItem(
                rule_id="STYLE003",
                location={"file_path": "src/very_long_path/very_deep_directory/very_long_filename_that_might_cause_issues.py"},
                message="这是一个非常长的违规信息，可能会超出表格的合理宽度，导致表格渲染出现问题，应该被截断处理",  # 超长文本
                severity="low", 
                rule_content="这是一个非常长的规则内容描述，用于测试表格单元格的长度限制功能是否正常工作"
            ),
            ViolationItem(
                rule_id="STYLE004",
                location={"file_path": "src/normal.py"},
                message="正常的违规信息",  # 正常文本
                severity="critical",
                rule_content="正常的规则内容"
            )
        ]
        
        # 创建测试结果
        test_result = AnalyzeLLMResult(
            is_pass=False,
            reason="检测到多种代码风格问题",
            violations=test_violations,
            code="def test_function():\n    return True",
            call_id="test-call-123"
        )
        
        # 生成报告
        report_content = self.report_service.generate_violation_markdown_report(
            test_result, 
            report_id="test-format", 
            is_independent=True
        )
        
        # 验证表格格式
        lines = report_content.split('\n')
        table_started = False
        table_ended = False
        table_lines = []
        
        for line in lines:
            if "| 规则ID | 优先级 | 规则内容 | 文件路径 | 违规信息 |" in line:
                table_started = True
                table_lines.append(line)
            elif table_started and line.strip() == "":
                table_ended = True
            elif table_started and not table_ended:
                table_lines.append(line)
        
        # 检查表格行数（表头 + 分隔线 + 数据行）
        expected_rows = 2 + len(test_violations)  # 表头 + 分隔线 + 数据行
        actual_rows = len([line for line in table_lines if line.strip().startswith('|')])
        
        self.assertEqual(actual_rows, expected_rows, f"期望表格行数: {expected_rows}, 实际: {actual_rows}")
        
        # 检查每行的列数
        for i, line in enumerate(table_lines):
            if line.strip().startswith('|'):
                columns = line.count('|') - 1  # 减去开头和结尾的|
                self.assertEqual(columns, 5, f"行 {i+1}: 期望5列，实际{columns}列")
        
        # 检查是否包含换行符
        has_newlines = any('\n' in line for line in table_lines)
        self.assertFalse(has_newlines, "表格中不应该包含换行符")
        
        # 检查是否包含代码块标记
        has_code_blocks = any('```' in line for line in table_lines)
        self.assertFalse(has_code_blocks, "表格中不应该包含代码块标记")
        
        # 检查超长内容是否被截断
        long_content_truncated = any('...' in line for line in table_lines)
        self.assertTrue(long_content_truncated, "超长内容应该被截断")
    
    def test_violation_message_complete_display(self):
        """测试违规信息完整显示"""
        # 测试数据：包含长违规信息的测试用例
        test_violations = [
            ViolationItem(
                rule_id="STYLE001",
                location={"file_path": "src/test.py"},
                message="这是一个非常详细的违规信息，包含了具体的代码问题描述。该函数存在多个代码风格问题：1. 变量命名不符合snake_case规范，应该使用更清晰的命名；2. 函数缺少适当的文档字符串注释；3. 代码行过长，超过了PEP8建议的79字符限制；4. 存在未使用的导入语句，应该清理掉。这些问题会影响代码的可读性和维护性。",
                severity="high",
                rule_content="代码风格规范检查"
            ),
            ViolationItem(
                rule_id="SECURITY001",
                location={"file_path": "src/auth.py"},
                message="存在SQL注入安全风险。直接使用字符串拼接构造SQL查询是危险的，攻击者可能通过输入恶意数据来执行未授权的数据库操作。建议使用参数化查询或ORM框架来防止SQL注入攻击。",
                severity="critical",
                rule_content="SQL注入防护"
            ),
            ViolationItem(
                rule_id="PERFORMANCE001",
                location={"file_path": "src/db.py"},
                message="数据库连接未正确关闭。在finally块中应该确保数据库连接被正确关闭，否则可能导致连接泄漏，影响系统性能。",
                severity="medium",
                rule_content="资源管理规范"
            ),
            ViolationItem(
                rule_id="SHORT001",
                location={"file_path": "src/simple.py"},
                message="简单的违规信息",
                severity="low",
                rule_content="简单规则"
            )
        ]
        
        # 创建测试结果
        test_result = AnalyzeLLMResult(
            is_pass=False,
            reason="检测到多个代码规范问题，需要修复",
            violations=test_violations,
            code="def test_function():\n    return True",
            call_id="test-call-123"
        )
        
        # 生成报告
        report_content = self.report_service.generate_violation_markdown_report(
            test_result, 
            report_id="test-complete", 
            is_independent=True
        )
        
        # 验证违规信息是否完整
        lines = report_content.split('\n')
        table_started = False
        violation_messages = []
        
        for line in lines:
            if "| 规则ID | 优先级 | 规则内容 | 文件路径 | 违规信息 |" in line:
                table_started = True
                continue
            elif table_started and line.strip() == "":
                break
            elif table_started and line.strip().startswith('|'):
                # 提取违规信息列（第5列）
                parts = line.split('|')
                if len(parts) >= 6:
                    violation_message = parts[5].strip()
                    violation_messages.append(violation_message)
        
        # 检查是否有违规信息被截断
        truncated_count = sum(1 for msg in violation_messages if '...' in msg and len(msg) < 100)
        
        # 验证结果（排除分隔线）
        data_rows = [msg for msg in violation_messages if msg and not msg.startswith('---')]
        self.assertEqual(len(data_rows), 4, f"期望4个违规信息，实际{len(data_rows)}个")
        self.assertEqual(truncated_count, 0, f"期望没有违规信息被截断，实际{truncated_count}个被截断")


if __name__ == "__main__":
    unittest.main() 