"""
评论去重服务简化测试

测试评论去重服务的核心功能。
"""

import unittest
from unittest.mock import MagicMock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.deduplication import \
    CommentDeduplicationService
from gate_keeper.domain.value_objects.git import CodeCheckDiscussion


class TestCommentDeduplicationServiceSimple(unittest.TestCase):
    """评论去重服务简化测试类"""

    def setUp(self):
        """测试初始化"""
        self.service = CommentDeduplicationService()
        self.project_id = "test_project"
        self.mr_id = 123
        
        # 模拟已存在的评论
        self.existing_comments = [
            CodeCheckDiscussion(
                id="1",
                body="函数命名不规范，建议使用动词+名词的形式",
                created_at="2024-03-20T10:00:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False
            ),
            CodeCheckDiscussion(
                id="2",
                body="缺少参数验证",
                created_at="2024-03-20T10:01:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False
            )
        ]
        
        # 模拟受影响的函数
        self.affected_function = AffectedFunction(
            name="process_data",
            filepath="src/main.py",
            start_line=10,
            end_line=30,
            code="def process_data(data):\n    return data",
            changed_lines=[15, 16],
            related_calls=[],
            related_definitions=[]
        )

    def test_should_skip_comment_exact_match(self):
        """测试should_skip_comment方法 - 完全匹配的评论"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 测试完全相同的评论
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project",
            self.mr_id,
            "函数命名不规范，建议使用动词+名词的形式",
            affected_function=self.affected_function
        )
        
        self.assertTrue(should_skip)
        self.assertEqual(reason, "重复未解决")

    def test_should_skip_comment_no_match(self):
        """测试should_skip_comment方法 - 无匹配"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 测试完全不同的评论
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project", 
            self.mr_id, 
            "建议添加单元测试以提高代码覆盖率"
        )
        
        self.assertFalse(should_skip)
        self.assertEqual(reason, "无重复")

    def test_should_skip_comment_empty_input(self):
        """测试should_skip_comment方法 - 空输入"""
        # 测试空评论
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project", 
            self.mr_id, 
            ""
        )
        
        self.assertTrue(should_skip)
        self.assertEqual(reason, "评论内容为空")

        # 测试None评论
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project", 
            self.mr_id, 
            None
        )
        
        self.assertTrue(should_skip)
        self.assertEqual(reason, "评论内容为空")

    def test_should_skip_comment_with_function_context(self):
        """测试should_skip_comment方法 - 使用函数上下文"""
        # 设置第一个评论的代码哈希，让它匹配
        new_comment = "函数命名不规范，建议使用动词+名词的形式"
        expected_hash = self.service._calculate_code_hash(new_comment, self.affected_function)
        self.existing_comments[0].checked_code = expected_hash
        
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 测试带有受影响函数的情况
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project",
            self.mr_id,
            new_comment,
            affected_function=self.affected_function
        )
        self.assertTrue(should_skip)
        self.assertEqual(reason, "重复未解决")

        # 测试不带受影响函数的情况
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project",
            self.mr_id,
            new_comment
        )
        self.assertTrue(should_skip)
        self.assertEqual(reason, "重复未解决")

    def test_extract_code_blocks_simple(self):
        """测试代码块提取功能 - 简单版本"""
        # 测试简单的行内代码
        simple_comment = "这里有行内代码 `print('hello')`"
        code_blocks = self.service._extract_code_blocks(simple_comment)
        
        # 验证行内代码提取
        self.assertGreater(len(code_blocks), 0)
        self.assertIn("print('hello')", code_blocks)

    def test_extract_code_blocks_empty(self):
        """测试提取代码块 - 空输入"""
        # 测试空字符串
        code_blocks = self.service._extract_code_blocks("")
        self.assertEqual(len(code_blocks), 0)
        
        # 测试None
        code_blocks = self.service._extract_code_blocks(None)
        self.assertEqual(len(code_blocks), 0)
        
        # 测试没有代码块的评论
        code_blocks = self.service._extract_code_blocks("这是一个普通的评论，没有代码。")
        self.assertEqual(len(code_blocks), 0)

    def test_calculate_similarity_basic(self):
        """测试相似度计算的基础功能"""
        # 测试相同文本
        similarity = self.service._calculate_similarity("hello world", "hello world")
        self.assertEqual(similarity, 1.0)
        
        # 测试完全不同的文本
        similarity = self.service._calculate_similarity("apple", "orange")
        self.assertLess(similarity, 0.5)
        
        # 测试空字符串
        similarity = self.service._calculate_similarity("", "hello")
        self.assertEqual(similarity, 0.0)

    def test_calculate_code_hash_basic(self):
        """测试代码哈希计算基础功能"""
        # 测试基本哈希计算
        comment = "这是一个测试评论"
        hash1 = self.service._calculate_code_hash(comment)
        hash2 = self.service._calculate_code_hash(comment)
        self.assertEqual(hash1, hash2)  # 相同输入应该产生相同哈希
        
        # 测试不同评论产生不同哈希
        hash3 = self.service._calculate_code_hash("这是另一个评论")
        self.assertNotEqual(hash1, hash3)

    def test_calculate_code_hash_with_function(self):
        """测试带有受影响函数的代码哈希计算"""
        comment = "函数需要优化"
        
        # 不带受影响函数的哈希
        hash1 = self.service._calculate_code_hash(comment)
        
        # 带受影响函数的哈希
        hash2 = self.service._calculate_code_hash(comment, self.affected_function)
        
        # 应该产生不同的哈希
        self.assertNotEqual(hash1, hash2)

    def test_duplicate_analysis_basic(self):
        """测试基本的重复分析功能"""
        # 计算新评论的代码哈希并设置到第一个已存在评论中
        new_comment = "函数命名不规范，建议使用动词+名词的形式"
        expected_hash = self.service._calculate_code_hash(new_comment, self.affected_function)
        self.existing_comments[0].checked_code = expected_hash
        
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 测试与现有评论完全相同的新评论
        result = self.service.get_duplicate_analysis(
            "test_project",
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(result["total_existing"], 2)
        self.assertEqual(len(result["code_hash_matches"]), 1)
        self.assertEqual(result["code_hash_matches"][0]["id"], "1")

    def test_duplicate_analysis_no_match(self):
        """测试重复分析 - 无匹配"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 测试完全不同的评论
        result = self.service.get_duplicate_analysis(
            "test_project",
            self.mr_id,
            "建议添加单元测试",
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(result["total_existing"], 2)
        self.assertEqual(len(result["code_hash_matches"]), 0)

    def test_duplicate_analysis_empty_input(self):
        """测试重复分析 - 空输入"""
        # 测试空评论
        result = self.service.get_duplicate_analysis(
            "test_project",
            self.mr_id,
            "",
            self.affected_function
        )
        self.assertIn("error", result)
        self.assertEqual(result["error"], "评论内容为空")

    def test_error_handling(self):
        """测试错误处理"""
        # 模拟get_existing_comments抛出异常
        self.service.get_existing_comments = MagicMock(side_effect=Exception("网络错误"))
        
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project",
            self.mr_id,
            "测试评论"
        )
        
        self.assertFalse(should_skip)
        self.assertIn("排重检测失败", reason)

    def test_similarity_threshold_configuration(self):
        """测试相似度阈值配置"""
        self.service.similarity_threshold = 0.9  # 设置很高的阈值
        
        # 即使是比较相似的文本也不应该被认为是重复
        is_duplicate = self.service._is_duplicate_by_similarity(
            "函数命名规范需要改进",
            self.existing_comments
        )
        
        # 由于阈值很高，应该不认为是重复
        self.assertFalse(is_duplicate)

    def test_feature_toggles(self):
        """测试功能开关"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 禁用代码哈希，启用相似度
        self.service.code_hash_enabled = False
        self.service.text_similarity_enabled = True
        
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project",
            self.mr_id,
            "函数命名不规范，建议使用动词+名词的形式"
        )
        
        self.assertTrue(should_skip)
        self.assertEqual(reason, "重复未解决")
        
        # 禁用相似度，启用代码哈希
        self.service.code_hash_enabled = True
        self.service.text_similarity_enabled = False
        
        should_skip, reason, reopen_ids = self.service.should_skip_comment(
            "test_project",
            self.mr_id,
            "函数命名不规范，建议使用动词+名词的形式",
            affected_function=self.affected_function
        )
        
        self.assertTrue(should_skip)
        self.assertEqual(reason, "重复未解决")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2) 