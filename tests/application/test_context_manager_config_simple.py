"""
Test ContextManagerConfig Simple

测试上下文管理器配置的核心功能
"""

import unittest

from gate_keeper.application.service.context_management.context_manager_config import \
    ContextManagerConfig


class TestContextManagerConfigSimple(unittest.TestCase):
    """测试上下文管理器配置的核心功能"""
    
    def test_basic_config_loading(self):
        """测试基本配置加载"""
        config = ContextManagerConfig()
        
        # 验证基础配置
        self.assertEqual(config.max_context_size, 8000)
        self.assertEqual(config.max_chain_depth, 3)
        self.assertEqual(config.max_chains, 3)
        
        # 验证权重配置
        expected_weights = {
            "chain_length": 0.3,
            "function_position": 0.3,
            "call_density": 0.2,
            "file_relevance": 0.2
        }
        self.assertEqual(config.relevance_weights, expected_weights)
        
        # 验证策略配置
        self.assertEqual(config.context_selection_strategy, "relevance_first")
        self.assertEqual(config.function_selection_strategy, "balanced")
    
    def test_custom_config_creation(self):
        """测试自定义配置创建"""
        custom_config = ContextManagerConfig(
            max_context_size=5000,
            max_chains=2,
            context_selection_strategy="size_first"
        )
        
        # 验证自定义值
        self.assertEqual(custom_config.max_context_size, 5000)
        self.assertEqual(custom_config.max_chains, 2)
        self.assertEqual(custom_config.context_selection_strategy, "size_first")
        
        # 验证其他值仍然从配置模块加载
        self.assertEqual(custom_config.max_chain_depth, 3)
        self.assertEqual(custom_config.function_selection_strategy, "balanced")
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = ContextManagerConfig()
        errors = valid_config.validate()
        self.assertEqual(len(errors), 0)
        
        # 测试无效配置
        invalid_config = ContextManagerConfig(max_context_size=-1)
        errors = invalid_config.validate()
        self.assertGreater(len(errors), 0)
        self.assertIn("max_context_size 必须大于 0", errors)
    
    def test_effective_max_context_size(self):
        """测试有效最大上下文大小计算"""
        config = ContextManagerConfig(max_context_size=10000, max_context_size_ratio=0.8)
        effective_size = config.get_effective_max_context_size()
        self.assertEqual(effective_size, 8000)
    
    def test_config_summary(self):
        """测试配置摘要"""
        config = ContextManagerConfig()
        summary = config.get_config_summary()
        
        # 验证摘要包含关键信息
        self.assertIn("max_context_size", summary)
        self.assertIn("context_selection_strategy", summary)
        self.assertIn("effective_max_context_size", summary)
        
        # 验证摘要值正确
        self.assertEqual(summary["max_context_size"], 8000)
        self.assertEqual(summary["context_selection_strategy"], "relevance_first")
    
    def test_create_from_config_method(self):
        """测试create_from_config类方法"""
        config = ContextManagerConfig.create_from_config(
            max_context_size=6000,
            context_selection_strategy="balanced"
        )
        
        # 验证自定义值被正确设置
        self.assertEqual(config.max_context_size, 6000)
        self.assertEqual(config.context_selection_strategy, "balanced")
        
        # 验证其他值仍然从配置模块加载
        self.assertEqual(config.max_chain_depth, 3)
        self.assertEqual(config.function_selection_strategy, "balanced")


if __name__ == '__main__':
    unittest.main() 