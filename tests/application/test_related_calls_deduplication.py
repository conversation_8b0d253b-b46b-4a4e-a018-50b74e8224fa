"""
测试related_calls的排重功能

验证get_related_calls方法能正确去除重复的调用关系
"""

import unittest
from unittest.mock import Mock

from gate_keeper.external.code_analyzer.core.repository_index import \
    RepositoryIndex
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall


class TestRelatedCallsDeduplication(unittest.TestCase):
    """测试related_calls的排重功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.repo_index = RepositoryIndex(repo_dir="/test/repo", branch="main")
    
    def test_related_calls_deduplication(self):
        """测试related_calls的排重功能"""
        # 创建一些重复的调用关系
        call1 = FunctionCall(
            caller="main",
            callee="helper",
            file_path="main.c",
            line=[10],
            code="helper();"
        )
        
        call2 = FunctionCall(
            caller="main",
            callee="helper",
            file_path="main.c",
            line=[15],
            code="helper();"
        )
        
        call3 = FunctionCall(
            caller="helper",
            callee="utility",
            file_path="helper.c",
            line=[5],
            code="utility();"
        )
        
        call4 = FunctionCall(
            caller="main",
            callee="utility",
            file_path="main.c",
            line=[20],
            code="utility();"
        )
        
        # 设置function_calls（包含重复）
        self.repo_index.function_calls = [call1, call2, call3, call4]
        
        # 测试获取main函数的相关调用
        related_calls = self.repo_index.get_related_calls("main")
        
        # 验证结果：应该只有2个唯一的调用关系（main->helper, main->utility）
        # 而不是3个（因为main->helper出现了两次，helper->utility与main无关）
        expected_count = 2
        actual_count = len(related_calls)
        
        self.assertEqual(actual_count, expected_count, 
                        f"期望{expected_count}个调用关系，实际得到{actual_count}个")
        
        # 验证具体的调用关系
        call_keys = set()
        for call in related_calls:
            call_key = f"{call.caller}->{call.callee}@{call.file_path}"
            call_keys.add(call_key)
        
        expected_keys = {
            "main->helper@main.c",
            "main->utility@main.c"
        }
        
        self.assertEqual(call_keys, expected_keys, 
                        f"调用关系不匹配: 期望{expected_keys}，实际{call_keys}")
        
        # 测试helper函数的相关调用
        helper_calls = self.repo_index.get_related_calls("helper")
        
        # helper函数应该有2个相关调用：main->helper 和 helper->utility
        expected_helper_count = 2
        actual_helper_count = len(helper_calls)
        
        self.assertEqual(actual_helper_count, expected_helper_count,
                        f"helper函数期望{expected_helper_count}个调用关系，实际得到{actual_helper_count}个")
    
    def test_related_calls_no_duplicates(self):
        """测试没有重复调用的情况"""
        # 创建没有重复的调用关系
        call1 = FunctionCall(
            caller="main",
            callee="helper",
            file_path="main.c",
            line=[10],
            code="helper();"
        )
        
        call2 = FunctionCall(
            caller="helper",
            callee="utility",
            file_path="helper.c",
            line=[5],
            code="utility();"
        )
        
        # 设置function_calls
        self.repo_index.function_calls = [call1, call2]
        
        # 测试获取main函数的相关调用
        related_calls = self.repo_index.get_related_calls("main")
        
        # 验证结果
        expected_count = 1  # main->helper
        actual_count = len(related_calls)
        
        self.assertEqual(actual_count, expected_count,
                        f"期望{expected_count}个调用关系，实际得到{actual_count}个")
    
    def test_related_calls_empty(self):
        """测试空调用列表的情况"""
        # 设置空的function_calls
        self.repo_index.function_calls = []
        
        # 测试获取main函数的相关调用
        related_calls = self.repo_index.get_related_calls("main")
        
        # 验证结果
        self.assertEqual(len(related_calls), 0, "空调用列表应该返回空结果")
    
    def test_related_calls_different_files(self):
        """测试不同文件的相同调用关系"""
        # 创建不同文件的相同调用关系
        call1 = FunctionCall(
            caller="main",
            callee="helper",
            file_path="main.c",
            line=[10],
            code="helper();"
        )
        
        call2 = FunctionCall(
            caller="main",
            callee="helper",
            file_path="other.c",  # 不同文件
            line=[5],
            code="helper();"
        )
        
        # 设置function_calls
        self.repo_index.function_calls = [call1, call2]
        
        # 测试获取main函数的相关调用
        related_calls = self.repo_index.get_related_calls("main")
        
        # 验证结果：不同文件的相同调用关系应该保留
        expected_count = 2  # main->helper@main.c 和 main->helper@other.c
        actual_count = len(related_calls)
        
        self.assertEqual(actual_count, expected_count,
                        f"期望{expected_count}个调用关系，实际得到{actual_count}个")
    
    def test_related_calls_multiple_duplicates(self):
        """测试多个重复调用的情况"""
        # 创建多个重复的调用关系
        calls = [
            FunctionCall(caller="main", callee="helper", file_path="main.c", line=[10], code="helper();"),
            FunctionCall(caller="main", callee="helper", file_path="main.c", line=[15], code="helper();"),
            FunctionCall(caller="main", callee="helper", file_path="main.c", line=[20], code="helper();"),
            FunctionCall(caller="main", callee="utility", file_path="main.c", line=[25], code="utility();"),
            FunctionCall(caller="main", callee="utility", file_path="main.c", line=[30], code="utility();"),
        ]
        
        # 设置function_calls
        self.repo_index.function_calls = calls
        
        # 测试获取main函数的相关调用
        related_calls = self.repo_index.get_related_calls("main")
        
        # 验证结果：应该只有2个唯一的调用关系
        expected_count = 2  # main->helper@main.c 和 main->utility@main.c
        actual_count = len(related_calls)
        
        self.assertEqual(actual_count, expected_count,
                        f"期望{expected_count}个调用关系，实际得到{actual_count}个")
        
        # 验证具体的调用关系
        call_keys = set()
        for call in related_calls:
            call_key = f"{call.caller}->{call.callee}@{call.file_path}"
            call_keys.add(call_key)
        
        expected_keys = {
            "main->helper@main.c",
            "main->utility@main.c"
        }
        
        self.assertEqual(call_keys, expected_keys,
                        f"调用关系不匹配: 期望{expected_keys}，实际{call_keys}")


if __name__ == "__main__":
    unittest.main() 