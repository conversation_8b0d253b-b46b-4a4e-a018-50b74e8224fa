"""
服务编排器测试

测试覆盖：
1. 服务编排器的基本功能
2. 延迟初始化机制
3. 依赖关系管理
4. 上下文管理器初始化
5. 错误处理和恢复
6. 端到端工作流
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicM<PERSON>, Mock, patch

from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.service_orchestrator import (
    ServiceConfig, ServiceOrchestrator, ServiceOrchestratorFactoryV2)
from gate_keeper.external.code_analyzer import StaticAnalyzer
from gate_keeper.infrastructure.llm.client.base import LLMClient
from gate_keeper.shared.log import app_logger as logger


class TestServiceOrchestrator(unittest.TestCase):
    """服务编排器测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建服务配置
        self.config = ServiceConfig(
            git_token="test_token",
            git_platform="gitee",
            llm_endpoint="http://localhost:11434",
            llm_model="qwen2.5:7b",
            use_static_analysis=True,
            use_optimized_context=True,
            max_workers=5
        )

        # 创建服务编排器
        self.orchestrator = ServiceOrchestrator(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        # 验证配置
        self.assertEqual(self.orchestrator.config.git_token, "test_token")
        self.assertEqual(self.orchestrator.config.git_platform, "gitee")

        # 验证服务注册表存在
        self.assertIsNotNone(self.orchestrator.service_registry)

        # 验证依赖图存在
        self.assertIsNotNone(self.orchestrator.dependency_graph)
        self.assertIsNone(self.orchestrator._llm_service)
        self.assertIsNone(self.orchestrator._static_analyzer)

        # 验证状态
        status = self.orchestrator.get_service_status()

        # 验证状态字典不为空
        self.assertIsInstance(status, dict)
        self.assertGreater(len(status), 0)

        # 验证所有服务都是未初始化状态（延迟初始化）
        for service_name, service_status in status.items():
            self.assertEqual(service_status['state'], 'uninitialized')
            self.assertFalse(service_status['initialized'])
            self.assertIsNone(service_status['error'])
    
    def test_code_analyzer_lazy_initialization(self):
        """测试代码分析器延迟初始化"""
        # 初始状态 - 服务未初始化
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'uninitialized')

        # 第一次访问，应该创建实例
        code_analyzer = self.orchestrator.get_service('code_analyzer')

        # 验证创建成功
        self.assertIsNotNone(code_analyzer)
        self.assertIsInstance(code_analyzer, RepositoryAnalyzer)

        # 验证状态更新
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'ready')
        self.assertTrue(status['code_analyzer']['initialized'])

        # 第二次访问，应该返回相同实例
        code_analyzer2 = self.orchestrator.get_service('code_analyzer')
        self.assertIs(code_analyzer, code_analyzer2)
    
    def test_llm_service_lazy_initialization(self):
        """测试LLM服务延迟初始化"""
        # 初始状态
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['llm_service']['state'], 'uninitialized')

        # 第一次访问，应该创建实例
        llm_service = self.orchestrator.get_service('llm_service')

        # 验证创建成功
        self.assertIsNotNone(llm_service)
        self.assertIsInstance(llm_service, LLMService)

        # 验证状态更新
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['llm_service']['state'], 'ready')
        self.assertTrue(status['llm_service']['initialized'])
    
    def test_static_analyzer_setup(self):
        """测试静态分析器设置"""
        # 创建模拟的StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)

        # 设置静态分析器
        self.orchestrator.set_static_analyzer(mock_static_analyzer)

        # 验证设置成功
        self.assertEqual(self.orchestrator.static_analyzer, mock_static_analyzer)

        # 验证静态分析器已设置（新架构中没有static_analyzer_available状态）
        self.assertIsNotNone(self.orchestrator.static_analyzer)
    
    def test_context_manager_initialization_with_static_analyzer(self):
        """测试有静态分析器时的上下文管理器初始化"""
        # 创建模拟的StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []

        # 设置静态分析器
        self.orchestrator.set_static_analyzer(mock_static_analyzer)

        # 验证静态分析器已设置
        self.assertEqual(self.orchestrator.static_analyzer, mock_static_analyzer)

        # 验证上下文管理器已被重新初始化（通过set_static_analyzer触发）
        self.assertIsNotNone(self.orchestrator._context_manager)
    
    def test_context_manager_initialization_without_static_analyzer(self):
        """测试没有静态分析器时的上下文管理器初始化"""
        # 没有设置静态分析器
        self.assertIsNone(self.orchestrator._static_analyzer)

        # 验证上下文管理器服务状态（应该是条件性的，依赖于use_optimized_context配置）
        status = self.orchestrator.get_service_status()
        # 由于config.use_optimized_context=True，上下文管理器应该可以初始化
        # 但没有静态分析器时可能会有不同的行为
        self.assertIn('context_manager', status)
    
    def test_llm_service_context_manager_integration(self):
        """测试LLM服务与上下文管理器的集成"""
        # 创建模拟的StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []

        # 先获取LLM服务（触发初始化）
        llm_service = self.orchestrator.get_service('llm_service')

        # 然后设置静态分析器
        self.orchestrator.set_static_analyzer(mock_static_analyzer)

        # 验证LLM服务已设置StaticAnalyzer
        self.assertEqual(llm_service._static_analyzer, mock_static_analyzer)

        # 验证上下文管理器已被重新初始化
        self.assertIsNotNone(self.orchestrator._context_manager)
    
    def test_full_workflow_simulation(self):
        """测试完整工作流模拟"""
        # 1. 获取代码分析器
        code_analyzer = self.orchestrator.get_service('code_analyzer')
        self.assertIsNotNone(code_analyzer)

        # 2. 设置模拟的静态分析器
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []

        self.orchestrator.set_static_analyzer(mock_static_analyzer)

        # 3. 获取LLM服务
        llm_service = self.orchestrator.get_service('llm_service')
        self.assertIsNotNone(llm_service)

        # 4. 验证所有服务都已正确初始化
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'ready')
        self.assertEqual(status['llm_service']['state'], 'ready')
        # 上下文管理器通过set_static_analyzer重新初始化，不是通过服务注册表
        self.assertIsNotNone(self.orchestrator._context_manager)

    
    def test_error_handling_static_analyzer_creation_failure(self):
        """测试静态分析器创建失败的错误处理"""
        # 获取代码分析器
        code_analyzer = self.orchestrator.get_service('code_analyzer')

        # 模拟analyze_functions调用失败
        with patch.object(code_analyzer, 'analyze_functions') as mock_analyze:
            mock_analyze.side_effect = Exception("RepositoryIndex creation failed")
            with self.assertRaises(Exception):
                code_analyzer.analyze_functions(
                    repo_dir="/invalid/path",
                    file_path='test.py',
                    file_content='def test(): pass',
                    branch='main'
                )

        # 验证StaticAnalyzer未设置
        self.assertIsNone(self.orchestrator.static_analyzer)
    
    def test_clear_cache(self):
        """测试缓存清理"""
        # 初始化一些服务
        code_analyzer = self.orchestrator.get_service('code_analyzer')
        llm_service = self.orchestrator.get_service('llm_service')

        # 验证服务已初始化
        self.assertIsNotNone(code_analyzer)
        self.assertIsNotNone(llm_service)

        # 验证服务状态
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'ready')
        self.assertEqual(status['llm_service']['state'], 'ready')
    
    def test_service_status_tracking(self):
        """测试服务状态跟踪"""
        # 初始状态
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'uninitialized')
        self.assertEqual(status['llm_service']['state'], 'uninitialized')
        self.assertEqual(status['context_manager']['state'], 'uninitialized')

        # 初始化代码分析器
        self.orchestrator.get_service('code_analyzer')
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'ready')
        self.assertEqual(status['llm_service']['state'], 'uninitialized')

        # 初始化LLM服务
        self.orchestrator.get_service('llm_service')
        status = self.orchestrator.get_service_status()
        self.assertEqual(status['code_analyzer']['state'], 'ready')
        self.assertEqual(status['llm_service']['state'], 'ready')

        # 设置静态分析器
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        self.orchestrator.set_static_analyzer(mock_static_analyzer)

        # 验证静态分析器已设置
        self.assertIsNotNone(self.orchestrator.static_analyzer)



class TestServiceOrchestratorFactory(unittest.TestCase):
    """服务编排器工厂测试类"""

    def test_create_from_config(self):
        """测试从配置创建服务编排器"""
        # 创建服务编排器
        orchestrator = ServiceOrchestratorFactoryV2.create_from_config('test_token')

        # 验证创建成功
        self.assertIsNotNone(orchestrator)
        self.assertIsInstance(orchestrator, ServiceOrchestrator)
        self.assertEqual(orchestrator.config.git_token, 'test_token')
        self.assertEqual(orchestrator.config.git_platform, 'gitee')
    
    def test_create_from_config_with_custom_llm(self):
        """测试从配置创建服务编排器（自定义LLM）"""
        # 创建服务编排器（自定义LLM）
        orchestrator = ServiceOrchestratorFactoryV2.create_from_config(
            git_token='test_token',
            llm_endpoint='http://custom-llm:8080',
            llm_model='custom-model'
        )

        # 验证创建成功
        self.assertIsNotNone(orchestrator)
        self.assertIsInstance(orchestrator, ServiceOrchestrator)
        self.assertEqual(orchestrator.config.git_token, 'test_token')
        self.assertEqual(orchestrator.config.llm_endpoint, 'http://custom-llm:8080')
        self.assertEqual(orchestrator.config.llm_model, 'custom-model')


if __name__ == '__main__':
    unittest.main() 