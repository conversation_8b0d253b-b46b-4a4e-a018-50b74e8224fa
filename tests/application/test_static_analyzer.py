"""
StaticAnalyzer测试

目标：验证静态分析器的所有功能
- 调用链分析
- 上下文提取
- 相关函数查找
- 边界条件处理
"""

import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

import networkx as nx

from gate_keeper.external.code_analyzer import RepositoryIndex, StaticAnalyzer
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.external.code_analyzer.models.code_range import CodeRange
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestStaticAnalyzer(unittest.TestCase):
    """StaticAnalyzer测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的RepositoryIndex
        self.mock_repo_index = Mock(spec=RepositoryIndex)
        
        # 创建测试用的调用图
        self.test_graph = nx.DiGraph()
        
        # 添加测试节点
        self.test_graph.add_node("main.py:function:main", name="main", filepath="main.py")
        self.test_graph.add_node("main.py:function:process_data", name="process_data", filepath="main.py")
        self.test_graph.add_node("utils.py:function:validate_input", name="validate_input", filepath="utils.py")
        self.test_graph.add_node("utils.py:function:format_output", name="format_output", filepath="utils.py")
        self.test_graph.add_node("db.py:function:save_data", name="save_data", filepath="db.py")
        self.test_graph.add_node("api.py:function:handle_request", name="handle_request", filepath="api.py")
        
        # 添加调用关系
        self.test_graph.add_edge("api.py:function:handle_request", "main.py:function:main")
        self.test_graph.add_edge("main.py:function:main", "main.py:function:process_data")
        self.test_graph.add_edge("main.py:function:process_data", "utils.py:function:validate_input")
        self.test_graph.add_edge("main.py:function:process_data", "utils.py:function:format_output")
        self.test_graph.add_edge("main.py:function:process_data", "db.py:function:save_data")
        
        # 设置mock返回值
        self.mock_repo_index.get_call_graph.return_value = self.test_graph
        self.mock_repo_index._get_node_id.side_effect = lambda filepath, func_name: f"{filepath}:function:{func_name}"
        self.mock_repo_index.graph_builder = Mock()
        
        # 创建StaticAnalyzer实例
        self.analyzer = StaticAnalyzer(self.mock_repo_index)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.analyzer.repo_index, self.mock_repo_index)
        self.assertEqual(self.analyzer.graph, self.test_graph)
    
    def test_get_upstream_call_chains(self):
        """测试获取上游调用链"""
        # 测试process_data的上游调用链
        chains = self.analyzer.get_upstream_call_chains("process_data", "main.py", max_depth=3)
        
        # 验证结果
        self.assertGreater(len(chains), 0)
        
        # 检查是否包含正确的调用链
        expected_chains = [
            ["main.py:function:main", "main.py:function:process_data"],
            ["api.py:function:handle_request", "main.py:function:main", "main.py:function:process_data"]
        ]
        
        for expected_chain in expected_chains:
            self.assertIn(expected_chain, chains)
    
    def test_get_downstream_call_chains(self):
        """测试获取下游调用链"""
        # 测试process_data的下游调用链
        chains = self.analyzer.get_downstream_call_chains("process_data", "main.py", max_depth=3)
        
        # 验证结果
        self.assertGreater(len(chains), 0)
        
        # 检查是否包含正确的调用链
        expected_chains = [
            ["main.py:function:process_data", "utils.py:function:validate_input"],
            ["main.py:function:process_data", "utils.py:function:format_output"],
            ["main.py:function:process_data", "db.py:function:save_data"]
        ]
        
        for expected_chain in expected_chains:
            self.assertIn(expected_chain, chains)
    
    def test_get_bidirectional_call_chains(self):
        """测试获取双向调用链"""
        # 测试process_data的双向调用链
        chains = self.analyzer.get_bidirectional_call_chains("process_data", "main.py", max_depth=3)
        
        # 验证结果
        self.assertGreater(len(chains), 0)
        
        # 检查是否包含完整的调用链（上游+下游）
        expected_chain = ["api.py:function:handle_request", "main.py:function:main", "main.py:function:process_data", "utils.py:function:validate_input"]
        self.assertIn(expected_chain, chains)
    
    def test_get_bidirectional_call_chains_with_isolated_function(self):
        """测试孤立函数的双向调用链"""
        # 添加一个孤立的函数（没有调用关系）
        self.test_graph.add_node("isolated.py:function:isolated_func", name="isolated_func", filepath="isolated.py")
        
        # 测试孤立函数的调用链
        chains = self.analyzer.get_bidirectional_call_chains("isolated_func", "isolated.py", max_depth=3)
        
        # 应该只包含函数本身
        self.assertEqual(len(chains), 1)
        self.assertEqual(chains[0], ["isolated.py:function:isolated_func"])
    
    def test_get_bidirectional_call_chains_with_nonexistent_function(self):
        """测试不存在函数的双向调用链"""
        chains = self.analyzer.get_bidirectional_call_chains("nonexistent", "nonexistent.py", max_depth=3)
        self.assertEqual(len(chains), 0)
    
    def test_call_chain_depth_limit(self):
        """测试调用链深度限制"""
        # 创建一个深度很长的调用链
        deep_graph = nx.DiGraph()
        for i in range(10):
            node_id = f"file{i}.py:function:func{i}"
            deep_graph.add_node(node_id, name=f"func{i}", filepath=f"file{i}.py")
            if i > 0:
                deep_graph.add_edge(f"file{i-1}.py:function:func{i-1}", node_id)
        
        self.mock_repo_index.get_call_graph.return_value = deep_graph
        
        # 测试深度限制
        chains = self.analyzer.get_downstream_call_chains("func0", "file0.py", max_depth=3)
        
        # 验证所有链的长度都不超过max_depth
        for chain in chains:
            self.assertLessEqual(len(chain), 3)
    
    def test_cyclic_call_detection(self):
        """测试循环调用检测"""
        # 创建一个包含循环的调用图
        cyclic_graph = nx.DiGraph()
        cyclic_graph.add_node("file1.py:function:func1", name="func1", filepath="file1.py")
        cyclic_graph.add_node("file2.py:function:func2", name="func2", filepath="file2.py")
        cyclic_graph.add_node("file3.py:function:func3", name="func3", filepath="file3.py")
        
        # 创建循环：func1 -> func2 -> func3 -> func1
        cyclic_graph.add_edge("file1.py:function:func1", "file2.py:function:func2")
        cyclic_graph.add_edge("file2.py:function:func2", "file3.py:function:func3")
        cyclic_graph.add_edge("file3.py:function:func3", "file1.py:function:func1")
        
        # 重新设置mock
        self.mock_repo_index.get_call_graph.return_value = cyclic_graph
        self.mock_repo_index._get_node_id.side_effect = lambda filepath, func_name: f"{filepath}:function:{func_name}"
        
        # 重新创建analyzer以使用新的mock
        self.analyzer = StaticAnalyzer(self.mock_repo_index)
        
        # 测试循环调用处理
        chains = self.analyzer.get_downstream_call_chains("func1", "file1.py", max_depth=5)
        
        # 验证没有无限循环（应该返回有限数量的链）
        self.assertGreater(len(chains), 0)
        
        # 验证所有链都是有限的
        for chain in chains:
            self.assertLessEqual(len(chain), 5)
            
        # 验证循环被正确处理（不会出现重复节点）
        for chain in chains:
            # 检查是否有重复节点（除了循环本身）
            node_counts = {}
            for node in chain:
                node_counts[node] = node_counts.get(node, 0) + 1
                # 允许最多出现2次（循环的起点和终点）
                self.assertLessEqual(node_counts[node], 2)
    
    def test_complex_call_graph_analysis(self):
        """测试复杂调用图分析"""
        # 创建一个更复杂的调用图
        complex_graph = nx.DiGraph()
        
        # 添加节点
        nodes = [
            ("entry.py:function:entry", "entry"),
            ("main.py:function:main", "main"),
            ("process.py:function:process", "process"),
            ("validate.py:function:validate", "validate"),
            ("format.py:function:format", "format"),
            ("db.py:function:save", "save"),
            ("log.py:function:log", "log"),
            ("error.py:function:handle_error", "handle_error")
        ]
        
        for node_id, name in nodes:
            filepath = node_id.split("::")[0]
            complex_graph.add_node(node_id, name=name, filepath=filepath)
        
        # 添加调用关系
        edges = [
            ("entry.py:function:entry", "main.py:function:main"),
            ("main.py:function:main", "process.py:function:process"),
            ("main.py:function:main", "log.py:function:log"),
            ("process.py:function:process", "validate.py:function:validate"),
            ("process.py:function:process", "format.py:function:format"),
            ("process.py:function:process", "db.py:function:save"),
            ("validate.py:function:validate", "error.py:function:handle_error"),
            ("format.py:function:format", "error.py:function:handle_error"),
            ("db.py:function:save", "error.py:function:handle_error")
        ]
        
        for edge in edges:
            complex_graph.add_edge(*edge)
        
        # 重新设置mock
        self.mock_repo_index.get_call_graph.return_value = complex_graph
        self.mock_repo_index._get_node_id.side_effect = lambda filepath, func_name: f"{filepath}:function:{func_name}"
        
        # 重新创建analyzer以使用新的mock
        self.analyzer = StaticAnalyzer(self.mock_repo_index)
        
        # 测试process函数的双向调用链
        chains = self.analyzer.get_bidirectional_call_chains("process", "process.py", max_depth=4)
        
        # 验证结果
        self.assertGreater(len(chains), 0)
        
        # 检查是否包含预期的调用链（process有多个后继节点，所以会有多个组合）
        # 验证包含上游路径
        upstream_found = any("entry.py:function:entry" in chain and "main.py:function:main" in chain for chain in chains)
        self.assertTrue(upstream_found)
        
        # 验证包含下游路径
        downstream_found = any("validate.py:function:validate" in chain or "format.py:function:format" in chain or "db.py:function:save" in chain for chain in chains)
        self.assertTrue(downstream_found)
    
    def test_backward_compatibility_methods(self):
        """测试向后兼容的方法"""
        # 测试get_call_chains_for_function（向后兼容）
        upstream_chains = self.analyzer.get_call_chains_for_function("process_data", "main.py", max_depth=3)
        self.assertGreater(len(upstream_chains), 0)
        
        # 测试get_call_chains_from_function（向后兼容）
        downstream_chains = self.analyzer.get_call_chains_from_function("process_data", "main.py", max_depth=3)
        self.assertGreater(len(downstream_chains), 0)
        
        # 验证向后兼容方法与新方法结果一致
        new_upstream_chains = self.analyzer.get_upstream_call_chains("process_data", "main.py", max_depth=3)
        new_downstream_chains = self.analyzer.get_downstream_call_chains("process_data", "main.py", max_depth=3)
        
        self.assertEqual(upstream_chains, new_upstream_chains)
        self.assertEqual(downstream_chains, new_downstream_chains)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空图
        empty_graph = nx.DiGraph()
        self.mock_repo_index.get_call_graph.return_value = empty_graph
        
        chains = self.analyzer.get_bidirectional_call_chains("any_func", "any_file.py", max_depth=3)
        self.assertEqual(len(chains), 0)
        
        # 测试只有一个节点的图（孤立节点，没有前驱和后继）
        single_node_graph = nx.DiGraph()
        single_node_graph.add_node("single.py:function:single_func", name="single_func", filepath="single.py")
        # 重新设置mock
        self.mock_repo_index.get_call_graph.return_value = single_node_graph
        self.mock_repo_index._get_node_id.side_effect = lambda filepath, func_name: f"{filepath}:function:{func_name}"
        
        # 重新创建analyzer以使用新的mock
        self.analyzer = StaticAnalyzer(self.mock_repo_index)
        
        chains = self.analyzer.get_bidirectional_call_chains("single_func", "single.py", max_depth=3)
        self.assertEqual(len(chains), 1)
        self.assertEqual(chains[0], ["single.py:function:single_func"])
        
        # 测试max_depth为0的情况
        chains = self.analyzer.get_downstream_call_chains("process_data", "main.py", max_depth=0)
        self.assertEqual(len(chains), 0)
    
    def test_performance_with_large_graph(self):
        """测试大图的性能"""
        # 创建一个较大的调用图
        large_graph = nx.DiGraph()
        
        # 添加100个节点
        for i in range(100):
            node_id = f"file{i}.py:function:func{i}"
            large_graph.add_node(node_id, name=f"func{i}", filepath=f"file{i}.py")
        
        # 添加一些调用关系（避免完全连接，保持合理的稀疏性）
        for i in range(99):
            large_graph.add_edge(f"file{i}.py:function:func{i}", f"file{i+1}.py:function:func{i+1}")
        
        # 重新设置mock
        self.mock_repo_index.get_call_graph.return_value = large_graph
        self.mock_repo_index._get_node_id.side_effect = lambda filepath, func_name: f"{filepath}:function:{func_name}"
        
        # 重新创建analyzer以使用新的mock
        self.analyzer = StaticAnalyzer(self.mock_repo_index)
        
        # 测试性能（不应该超时）
        import time
        start_time = time.time()
        
        chains = self.analyzer.get_bidirectional_call_chains("func0", "file0.py", max_depth=5)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证执行时间合理（小于1秒）
        self.assertLess(execution_time, 1.0)
        
        # 验证结果不为空（func0有后继节点func1）
        self.assertGreater(len(chains), 0)


if __name__ == '__main__':
    unittest.main() 