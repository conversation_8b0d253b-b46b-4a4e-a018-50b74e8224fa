"""
ContextManager单元测试

测试智能上下文管理器的核心功能：
- 调用链上下文生成
- 相关性评分计算
- 上下文优化选择
- 上下文大小控制
"""

import unittest
from unittest.mock import Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import Context<PERSON>anager
from gate_keeper.external.code_analyzer import StaticAnalyzer
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestContextManager(unittest.TestCase):
    """ContextManager测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的StaticAnalyzer
        self.mock_static_analyzer = Mock(spec=StaticAnalyzer)
        
        # 添加repo_index属性
        self.mock_static_analyzer.repo_index = Mock()
        self.mock_static_analyzer.repo_index.function_definitions = {}
        self.mock_static_analyzer.repo_index.function_calls = []
        
        # 创建ContextManager实例
        self.context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            max_context_size=1000,
            max_chain_depth=3
        )
        
        # 创建测试用的AffectedFunction
        self.test_af = AffectedFunction(
            name="test_function",
            type="function",
            start_line=10,
            end_line=20,
            changed_lines=[15, 16],
            code="def test_function():\n    pass",
            related_calls=[],
            related_definitions=[],
            filepath="test.py"
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.context_manager.static_analyzer, self.mock_static_analyzer)
        self.assertEqual(self.context_manager.max_context_size, 1000)
        self.assertEqual(self.context_manager.max_chain_depth, 3)
        self.assertIsNotNone(self.context_manager.chain_selector)
    
    def test_get_call_chains_for_function(self):
        """测试获取调用链功能"""
        # 模拟静态分析器的返回值
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = [
            ['main', 'process_data', 'validate_input']
        ]
        
        # 模拟函数定义
        mock_func = Function.create_simple(
            name="main",
            start_line=1,
            end_line=10,
            filepath="main.py",
            signature=FunctionSignature(name="main", parameters=[]),
            code="def main():\n    pass"
        )
        self.mock_static_analyzer.repo_index.function_definitions = {
            "main": [mock_func],
            "process_data": [mock_func],
            "validate_input": [mock_func]
        }
        
        # 直接测试generate_optimized_contexts方法
        contexts = self.context_manager.generate_optimized_contexts(self.test_af)
        
        # 验证结果
        self.assertIsNotNone(contexts)
        self.assertGreater(len(contexts), 0)
        self.assertEqual(contexts[0].chain, ['main', 'process_data', 'validate_input'])
    
    def test_get_call_chains_for_function_no_chains(self):
        """测试获取调用链功能（无调用链）"""
        # 模拟静态分析器返回空结果
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = []
        
        # 模拟函数定义
        mock_func = Function.create_simple(
            name="test_function",
            start_line=1,
            end_line=10,
            filepath="test.py",
            signature=FunctionSignature(name="test_function", parameters=[]),
            code="def test_function():\n    pass"
        )
        self.mock_static_analyzer.repo_index.function_definitions = {
            "test_function": [mock_func]
        }
        
        # 直接测试generate_optimized_contexts方法
        contexts = self.context_manager.generate_optimized_contexts(self.test_af)
        
        # 验证结果（应该创建单函数链）
        self.assertIsNotNone(contexts)
        self.assertGreater(len(contexts), 0)
        self.assertEqual(contexts[0].chain, ['test_function'])
    
    def test_build_chain_context(self):
        """测试构建调用链上下文"""
        # 模拟函数定义
        mock_func1 = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=10,
            filepath="func1.py",
            signature=FunctionSignature(name="func1", parameters=[]),
            code="def func1():\n    pass"
        )
        
        mock_func2 = Function.create_simple(
            name="func2",
            start_line=1,
            end_line=15,
            filepath="func2.py",
            signature=FunctionSignature(name="func2", parameters=[]),
            code="def func2():\n    pass"
        )
        
        # 模拟函数定义查找
        self.mock_static_analyzer.repo_index.function_definitions = {
            "func1": [mock_func1],
            "func2": [mock_func2]
        }
        
        # 测试构建上下文
        chain = ["func1", "func2"]
        context = self.context_manager._build_chain_context(self.test_af, chain)
        
        # 验证结果
        self.assertIsNotNone(context)
        self.assertEqual(context.chain, chain)
        self.assertEqual(len(context.functions), 2)
        self.assertIn(mock_func1, context.functions)
        self.assertIn(mock_func2, context.functions)
        self.assertGreater(context.relevance_score, 0)
        self.assertGreater(context.context_size, 0)
    
    def test_build_chain_context_no_functions(self):
        """测试没有函数定义的情况"""
        # 模拟空的函数定义
        self.mock_static_analyzer.repo_index.function_definitions = {}
        
        # 测试构建上下文
        chain = ["nonexistent_func"]
        context = self.context_manager._build_chain_context(self.test_af, chain)
        
        # 应该返回None
        self.assertIsNone(context)
    
    def test_build_chain_context_size_limit(self):
        """测试上下文大小限制"""
        # 创建大代码的函数
        large_code = "def large_function():\n" + "    pass\n" * 1000  # 超过1000字符
        
        mock_large_func = Function.create_simple(
            name="large_func",
            start_line=1,
            end_line=1000,
            filepath="large.py",
            signature=FunctionSignature(name="large_func", parameters=[]),
            code=large_code
        )
        
        self.mock_static_analyzer.repo_index.function_definitions = {
            "large_func": [mock_large_func]
        }
        
        # 测试构建上下文
        chain = ["large_func"]
        context = self.context_manager._build_chain_context(self.test_af, chain)
        
        # 应该返回None（超过大小限制）
        self.assertIsNone(context)
    
    def test_calculate_chain_relevance(self):
        """测试调用链相关性评分"""
        # 创建测试函数
        func1 = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=10,
            filepath="test.py",  # 同文件
            signature=FunctionSignature(name="func1", parameters=[]),
            code="def func1():\n    pass"
        )
        
        func2 = Function.create_simple(
            name="func2",
            start_line=1,
            end_line=10,
            filepath="other.py",  # 不同文件
            signature=FunctionSignature(name="func2", parameters=[]),
            code="def func2():\n    pass"
        )
        
        # 测试相关性评分
        chain = ["func1", "func2"]
        functions = [func1, func2]
        
        score = self.context_manager._calculate_chain_relevance(self.test_af, chain, functions)
        
        # 验证评分在合理范围内
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)
    
    def test_select_best_function_definition(self):
        """测试选择最佳函数定义"""
        # 创建多个函数定义
        func1 = Function.create_simple(
            name="test_func",
            start_line=1,
            end_line=10,
            filepath="test.py",  # 同文件
            signature=FunctionSignature(name="test_func", parameters=[]),
            code="def test_func():\n    pass"
        )
        
        func2 = Function.create_simple(
            name="test_func",
            start_line=1,
            end_line=10,
            filepath="other.py",  # 不同文件
            signature=FunctionSignature(name="test_func", parameters=[]),
            code="def test_func():\n    pass"
        )
        
        func3 = Function.create_simple(
            name="test_func",
            start_line=1,
            end_line=10,
            filepath="header.h",  # 头文件
            signature=FunctionSignature(name="test_func", parameters=[]),
            code="def test_func();"
        )
        
        # 测试选择最佳定义
        best_func = self.context_manager._select_best_function_definition(
            [func1, func2, func3], "test.py"
        )
        
        # 应该选择同文件的定义
        self.assertEqual(best_func, func1)
    
    def test_select_optimal_contexts(self):
        """测试选择最优上下文"""
        # 创建测试上下文
        from gate_keeper.application.service.context_management import \
            CallChainContext
        
        context1 = CallChainContext(
            chain=["func1", "func2"],
            functions=[],
            relevance_score=0.8,
            context_size=500
        )
        
        context2 = CallChainContext(
            chain=["func3", "func4"],
            functions=[],
            relevance_score=0.6,
            context_size=300
        )
        
        context3 = CallChainContext(
            chain=["func5", "func6"],
            functions=[],
            relevance_score=0.9,
            context_size=800
        )
        
        # 创建临时ContextManager，增加大小限制
        temp_context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            max_context_size=2000,  # 增加大小限制以容纳所有上下文
            max_chain_depth=3
        )
        
        # 测试选择最优上下文
        contexts = [context1, context2, context3]
        selected = temp_context_manager._select_optimal_contexts(contexts, max_chains=2)
        
        # 应该选择评分最高的上下文
        self.assertEqual(len(selected), 2)
        self.assertEqual(selected[0].relevance_score, 0.9)  # context3
        self.assertEqual(selected[1].relevance_score, 0.8)  # context1
    
    def test_generate_optimized_contexts(self):
        """测试生成优化上下文"""
        # 模拟调用链
        mock_chains = [
            ["func1", "test_function"],
            ["func2", "test_function", "func3"]
        ]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 模拟函数定义
        mock_func = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=10,
            filepath="func1.py",
            signature=FunctionSignature(name="func1", parameters=[]),
            code="def func1():\n    pass"
        )
        
        self.mock_static_analyzer.repo_index.function_definitions = {
            "func1": [mock_func],
            "func2": [mock_func],
            "func3": [mock_func]
        }
        
        # 测试生成优化上下文
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=2)
        
        # 验证结果
        self.assertGreater(len(contexts), 0)
        self.assertLessEqual(len(contexts), 2)
        
        # 验证上下文按相关性排序
        for i in range(len(contexts) - 1):
            self.assertGreaterEqual(contexts[i].relevance_score, contexts[i + 1].relevance_score)
    
    def test_generate_optimized_contexts_with_size_limit(self):
        """测试带大小限制的上下文生成"""
        # 设置较小的上下文大小限制
        small_context_manager = ContextManager(
            static_analyzer=self.mock_static_analyzer,
            max_context_size=100,  # 很小的限制
            max_chain_depth=3
        )
        
        # 模拟调用链
        mock_chains = [["func1", "test_function"]]
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = mock_chains
        
        # 模拟大代码函数
        large_code = "def large_function():\n" + "    pass\n" * 50  # 超过100字符
        mock_large_func = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=50,
            filepath="large.py",
            signature=FunctionSignature(name="func1", parameters=[]),
            code=large_code
        )
        
        self.mock_static_analyzer.repo_index.function_definitions = {
            "func1": [mock_large_func]
        }
        
        # 测试生成上下文
        contexts = small_context_manager.generate_optimized_contexts(self.test_af, max_chains=1)
        
        # 应该返回空列表（超过大小限制）
        self.assertEqual(len(contexts), 0)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空的AffectedFunction
        empty_af = AffectedFunction(
            name="",
            type="function",
            start_line=0,
            end_line=0,
            changed_lines=[],
            code="",
            related_calls=[],
            related_definitions=[],
            filepath=""
        )
        
        # 模拟空调用链
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = []
        
        # 测试生成上下文
        contexts = self.context_manager.generate_optimized_contexts(empty_af, max_chains=1)
        
        # 应该返回包含空函数名的上下文
        self.assertEqual(len(contexts), 1)
        self.assertEqual(contexts[0].chain, [""])
    
    def test_performance_with_large_chains(self):
        """测试大调用链的性能"""
        # 创建大量调用链
        large_chains = []
        for i in range(100):
            chain = [f"func{j}" for j in range(i + 1)]
            large_chains.append(chain)
        
        self.mock_static_analyzer.get_bidirectional_call_chains.return_value = large_chains
        
        # 模拟函数定义
        mock_func = Function.create_simple(
            name="func1",
            start_line=1,
            end_line=10,
            filepath="func1.py",
            signature=FunctionSignature(name="func1", parameters=[]),
            code="def func1():\n    pass"
        )
        
        self.mock_static_analyzer.repo_index.function_definitions = {
            f"func{i}": [mock_func] for i in range(100)
        }
        
        # 测试性能（不应该超时）
        import time
        start_time = time.time()
        
        contexts = self.context_manager.generate_optimized_contexts(self.test_af, max_chains=10)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证执行时间合理（小于5秒）
        self.assertLess(execution_time, 5.0)
        
        # 验证结果数量正确
        self.assertLessEqual(len(contexts), 10)


if __name__ == '__main__':
    unittest.main() 