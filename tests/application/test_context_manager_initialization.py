"""
上下文管理器初始化问题测试

专门测试之前遗漏的上下文管理器初始化问题：
1. LLMService初始化时context_manager为None的情况
2. StaticAnalyzer未正确设置导致上下文管理器无法开启的情况
3. 服务编排器中的依赖关系问题
4. 端到端依赖关系验证
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.service_orchestrator import (
    ServiceConfig, ServiceOrchestrator)
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.external.code_analyzer import StaticAnalyzer
from gate_keeper.infrastructure.llm.client.base import LLMClient
from gate_keeper.shared.log import app_logger as logger


class TestContextManagerInitializationIssues(unittest.TestCase):
    """测试上下文管理器初始化问题"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的LLM客户端
        self.mock_llm_client = Mock(spec=LLMClient)
        
        # 创建模拟的Git服务
        self.mock_git_service = Mock()
    
    def test_llm_service_context_manager_none_on_init(self):
        """测试LLMService初始化时context_manager为None的情况"""
        # 创建LLMService，不传入StaticAnalyzer
        llm_service = LLMService(client=self.mock_llm_client)
        
        # 验证context_manager为None
        self.assertIsNone(llm_service.context_manager)
        
        # 验证_static_analyzer为None
        self.assertIsNone(llm_service._static_analyzer)
        
        # 验证use_optimized_context为True（默认启用）
        self.assertTrue(llm_service.use_optimized_context)
    
    def test_llm_service_context_manager_initialization_with_static_analyzer(self):
        """测试LLMService在有StaticAnalyzer时正确初始化context_manager"""
        # 创建模拟的StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        
        # 创建LLMService，传入StaticAnalyzer
        llm_service = LLMService(
            client=self.mock_llm_client,
            static_analyzer=mock_static_analyzer
        )
        
        # 验证context_manager已初始化
        self.assertIsNotNone(llm_service.context_manager)
        self.assertIsInstance(llm_service.context_manager, ContextManager)
        self.assertEqual(llm_service.context_manager.static_analyzer, mock_static_analyzer)
    
    def test_llm_service_delayed_context_manager_initialization(self):
        """测试LLMService延迟初始化context_manager"""
        # 创建LLMService，不传入StaticAnalyzer
        llm_service = LLMService(client=self.mock_llm_client)
        
        # 验证初始状态
        self.assertIsNone(llm_service.context_manager)
        
        # 创建模拟的StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        
        # 延迟设置StaticAnalyzer
        llm_service.set_static_analyzer(mock_static_analyzer)
        
        # 验证context_manager已初始化
        self.assertIsNotNone(llm_service.context_manager)
        self.assertIsInstance(llm_service.context_manager, ContextManager)
        self.assertEqual(llm_service.context_manager.static_analyzer, mock_static_analyzer)
    
    def test_llm_service_context_manager_disabled(self):
        """测试LLMService禁用优化上下文时的情况"""
        # 创建LLMService，禁用优化上下文
        llm_service = LLMService(
            client=self.mock_llm_client,
            use_optimized_context=False
        )
        
        # 验证context_manager为None（因为禁用了优化上下文）
        self.assertIsNone(llm_service.context_manager)
        
        # 即使设置StaticAnalyzer，context_manager也应该为None
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        llm_service.set_static_analyzer(mock_static_analyzer)
        
        self.assertIsNone(llm_service.context_manager)
    
    def test_orchestrator_context_manager_initialization_flow(self):
        """测试服务编排器中上下文管理器的初始化流程"""
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)

        # 1. 初始状态验证
        status = orchestrator.get_service_status()
        self.assertEqual(status['llm_service']['state'], 'uninitialized')

        # 2. 获取LLM服务（此时context_manager应该为None）
        llm_service = orchestrator.get_service('llm_service')
        self.assertIsNone(llm_service.context_manager)

        # 3. 设置StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        
        orchestrator.set_static_analyzer(mock_static_analyzer)
        
        # 4. 验证静态分析器已设置
        self.assertIsNotNone(orchestrator.static_analyzer)

        # 5. 验证上下文管理器已自动初始化（通过set_static_analyzer）
        self.assertIsNotNone(orchestrator._context_manager)
        self.assertIsInstance(orchestrator._context_manager, ContextManager)

        # 6. 验证LLM服务的context_manager也已更新
        self.assertIsNotNone(llm_service.context_manager)

    def test_orchestrator_context_manager_without_static_analyzer(self):
        """测试服务编排器在没有StaticAnalyzer时上下文管理器的行为"""
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)

        # 不设置StaticAnalyzer，验证上下文管理器为None
        self.assertIsNone(orchestrator._context_manager)

        # 验证静态分析器未设置
        self.assertIsNone(orchestrator.static_analyzer)

        # 验证LLM服务的context_manager也为None
        llm_service = orchestrator.get_service('llm_service')
        self.assertIsNone(llm_service.context_manager)

    def test_usecase_context_manager_initialization(self):
        """测试UseCase中上下文管理器的初始化"""
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)

        # 创建UseCase
        usecase = AnalyzeMRAndReportUsecase(orchestrator)

        # 验证UseCase中的服务引用
        self.assertIsNotNone(usecase.git_service)
        self.assertIsNotNone(usecase.llm_service)
        self.assertIsNotNone(usecase.repo_analyzer)
        
        # 验证repo_analyzer的编排器引用已设置
        self.assertEqual(usecase.repo_analyzer.orchestrator, orchestrator)
        
        # 验证初始状态（还没有StaticAnalyzer）
        self.assertIsNone(orchestrator.static_analyzer)
        self.assertIsNone(orchestrator._context_manager)
        self.assertIsNone(usecase.llm_service.context_manager)
    
    def test_full_workflow_context_manager_initialization(self):
        """测试完整工作流中上下文管理器的初始化"""
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)

        # 创建UseCase
        usecase = AnalyzeMRAndReportUsecase(orchestrator)

        # 验证初始状态
        self.assertIsNone(orchestrator.static_analyzer)
        self.assertIsNone(orchestrator._context_manager)

        # 模拟设置静态分析器（模拟分析过程中的设置）
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []

        orchestrator.set_static_analyzer(mock_static_analyzer)

        # 验证StaticAnalyzer已设置
        self.assertIsNotNone(orchestrator.static_analyzer)

        # 验证上下文管理器已初始化
        self.assertIsNotNone(orchestrator._context_manager)

        # 验证LLM服务的上下文管理器已设置
        self.assertIsNotNone(usecase.llm_service.context_manager)

    def test_context_manager_initialization_error_handling(self):
        """测试上下文管理器初始化错误处理"""
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)
        
        # 创建有问题的StaticAnalyzer（缺少必要属性）
        problematic_static_analyzer = Mock(spec=StaticAnalyzer)
        # 不设置repo_index属性，这会导致ContextManager初始化失败
        
        # 设置有问题的StaticAnalyzer
        orchestrator.set_static_analyzer(problematic_static_analyzer)
        
        # 尝试获取上下文管理器，应该处理错误
        try:
            context_manager = orchestrator.context_manager
            # 如果成功，验证结果
            if context_manager is not None:
                self.assertIsInstance(context_manager, ContextManager)
        except Exception as e:
            # 如果失败，验证错误处理
            self.assertIsNone(orchestrator.context_manager)
    
    def test_context_manager_config_validation(self):
        """测试上下文管理器配置验证"""
        # 创建模拟的StaticAnalyzer
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        
        # 测试无效配置（现在只是警告，不抛出异常）
        context_manager = ContextManager(
            static_analyzer=mock_static_analyzer,
            max_context_size=-1,  # 无效值
            max_chain_depth=0     # 无效值
        )
        
        # 验证ContextManager仍然创建成功（向后兼容）
        self.assertIsNotNone(context_manager)
        self.assertEqual(context_manager.config.max_context_size, -1)
        self.assertEqual(context_manager.config.max_chain_depth, 0)
    
    def test_context_manager_performance_impact(self):
        """测试上下文管理器对性能的影响"""
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)
        
        # 测量初始化时间
        import time
        
        start_time = time.time()
        llm_service = orchestrator.get_service('llm_service')
        llm_init_time = time.time() - start_time
        
        # 验证LLM服务初始化时间合理（应该很快）
        self.assertLess(llm_init_time, 1.0)  # 应该在1秒内完成
        
        # 测量上下文管理器初始化时间
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        
        orchestrator.set_static_analyzer(mock_static_analyzer)
        
        start_time = time.time()
        context_manager = orchestrator._context_manager
        context_init_time = time.time() - start_time
        
        # 验证上下文管理器初始化时间合理
        self.assertLess(context_init_time, 1.0)  # 应该在1秒内完成


class TestContextManagerIntegrationScenarios(unittest.TestCase):
    """测试上下文管理器集成场景"""
    
    def test_scenario_context_manager_not_initialized(self):
        """测试场景：上下文管理器未初始化的情况"""
        # 这个场景模拟了之前发现的问题
        # LLMService依赖StaticAnalyzer，但StaticAnalyzer没有正确设置
        
        # 创建LLMService，不传入StaticAnalyzer
        mock_llm_client = Mock(spec=LLMClient)
        llm_service = LLMService(client=mock_llm_client)
        
        # 验证问题：context_manager为None，但use_optimized_context为True
        self.assertIsNone(llm_service.context_manager)
        self.assertTrue(llm_service.use_optimized_context)
        
        # 这会导致后续使用context_manager时出现问题
        # 例如在LLMService.analyze_function中调用context_manager.generate_optimized_contexts
        
        # 模拟这种情况下的调用
        try:
            # 这应该会失败或返回空结果
            result = llm_service.context_manager.generate_prompt_content(
                af=Mock(),
                file_path="test.py"
            )
            # 如果成功，验证结果
            self.assertIsNotNone(result)
        except Exception as e:
            # 如果失败，这是预期的行为
            # 错误消息应该包含NoneType或context_manager相关信息
            error_msg = str(e).lower()
            self.assertTrue(
                "nonetype" in error_msg or "context_manager" in error_msg or "none" in error_msg,
                f"错误消息应该包含NoneType或context_manager相关信息，但实际是: {error_msg}"
            )
    
    def test_scenario_context_manager_properly_initialized(self):
        """测试场景：上下文管理器正确初始化的情况"""
        # 创建完整的依赖链
        mock_llm_client = Mock(spec=LLMClient)
        mock_git_service = Mock()
        
        # 创建服务编排器
        config = ServiceConfig(
            git_token='test_token',
            git_platform='gitee',
            llm_endpoint='http://localhost:11434',
            llm_model='test_model'
        )
        orchestrator = ServiceOrchestrator(config)
        
        # 创建UseCase
        usecase = AnalyzeMRAndReportUsecase(orchestrator)
        
        # 模拟StaticAnalyzer设置
        mock_static_analyzer = Mock(spec=StaticAnalyzer)
        mock_static_analyzer.repo_index = Mock()
        mock_static_analyzer.repo_index.function_definitions = {}
        mock_static_analyzer.repo_index.function_calls = []
        
        orchestrator.set_static_analyzer(mock_static_analyzer)
        
        # 验证所有组件都正确初始化
        self.assertIsNotNone(orchestrator._context_manager)

        # 重新获取llm_service以确保context_manager被设置
        llm_service = orchestrator.get_service('llm_service')
        self.assertIsNotNone(llm_service.context_manager)

        # 验证usecase中的llm_service也有context_manager
        self.assertIsNotNone(usecase.llm_service.context_manager)

        # 验证两个context_manager都使用相同的静态分析器
        self.assertEqual(llm_service.context_manager.static_analyzer, mock_static_analyzer)
        self.assertEqual(usecase.llm_service.context_manager.static_analyzer, mock_static_analyzer)

        # 验证可以正常使用context_manager
        context_manager = usecase.llm_service.context_manager
        self.assertIsInstance(context_manager, ContextManager)
        self.assertEqual(context_manager.static_analyzer, mock_static_analyzer)


if __name__ == '__main__':
    unittest.main() 