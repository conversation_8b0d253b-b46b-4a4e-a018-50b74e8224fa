#!/usr/bin/env python3
"""
测试ViolationItem相似度排重服务
"""

import unittest

from gate_keeper.application.service.deduplication import \
    ViolationDeduplicationService
from gate_keeper.domain.value_objects.analysis_result import ViolationItem


class TestViolationDeduplicationService(unittest.TestCase):
    """测试ViolationItem相似度排重服务"""
    
    def setUp(self):
        """设置测试环境"""
        self.deduplication_service = ViolationDeduplicationService(similarity_threshold=0.8)
    
    def test_deduplicate_identical_violations(self):
        """测试完全相同的违规项排重"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            )
        ]
        
        result = self.deduplication_service.deduplicate_violations(violations)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].rule_id, "STYLE001")
    
    def test_deduplicate_similar_violations(self):
        """测试相似的违规项排重"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case命名法"
            ),
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 15},
                severity="high",
                message="函数名应该使用snake_case命名方式"
            )
        ]
        
        result = self.deduplication_service.deduplicate_violations(violations)
        self.assertEqual(len(result), 1)
    
    def test_deduplicate_different_violations(self):
        """测试不同的违规项不排重"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="SECURITY001",
                rule_content="SQL注入防护",
                location={"file_path": "auth.py", "line": 20},
                severity="critical",
                message="存在SQL注入风险"
            )
        ]
        
        result = self.deduplication_service.deduplicate_violations(violations)
        self.assertEqual(len(result), 2)
    
    def test_deduplicate_by_rule_id(self):
        """测试按规则ID分组的排重"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 15},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="SECURITY001",
                rule_content="SQL注入防护",
                location={"file_path": "auth.py", "line": 20},
                severity="critical",
                message="存在SQL注入风险"
            )
        ]
        
        result = self.deduplication_service.deduplicate_violations(violations)
        # STYLE001的两个违规项应该被排重，SECURITY001保留
        self.assertEqual(len(result), 2)
        
        rule_ids = [v.rule_id for v in result]
        self.assertIn("STYLE001", rule_ids)
        self.assertIn("SECURITY001", rule_ids)
    
    def test_deduplicate_with_code_blocks(self):
        """测试包含代码块的违规项排重"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case ```def badFunction(): pass```"
            ),
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 15},
                severity="high",
                message="函数名应该使用snake_case ```def anotherBadFunction(): pass```"
            )
        ]
        
        result = self.deduplication_service.deduplicate_violations(violations)
        # 代码块不同，但核心信息相似，应该被排重
        self.assertEqual(len(result), 1)
    
    def test_deduplicate_empty_list(self):
        """测试空列表排重"""
        result = self.deduplication_service.deduplicate_violations([])
        self.assertEqual(result, [])
    
    def test_deduplicate_single_item(self):
        """测试单个项目排重"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            )
        ]
        
        result = self.deduplication_service.deduplicate_violations(violations)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].rule_id, "STYLE001")
    
    def test_deduplication_stats(self):
        """测试排重统计信息"""
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 15},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="SECURITY001",
                rule_content="SQL注入防护",
                location={"file_path": "auth.py", "line": 20},
                severity="critical",
                message="存在SQL注入风险"
            )
        ]
        
        deduplicated = self.deduplication_service.deduplicate_violations(violations)
        stats = self.deduplication_service.get_deduplication_stats(violations, deduplicated)
        
        self.assertEqual(stats["original_count"], 3)
        self.assertEqual(stats["deduplicated_count"], 2)
        self.assertEqual(stats["removed_count"], 1)
        self.assertAlmostEqual(stats["reduction_rate"], 1/3, places=2)
        
        # 检查规则统计
        self.assertIn("STYLE001", stats["rule_stats"])
        self.assertIn("SECURITY001", stats["rule_stats"])
        self.assertEqual(stats["rule_stats"]["STYLE001"]["original"], 2)
        self.assertEqual(stats["rule_stats"]["STYLE001"]["deduplicated"], 1)
        self.assertEqual(stats["rule_stats"]["SECURITY001"]["original"], 1)
        self.assertEqual(stats["rule_stats"]["SECURITY001"]["deduplicated"], 1)
    
    def test_similarity_calculation(self):
        """测试相似度计算"""
        violation1 = ViolationItem(
            rule_id="STYLE001",
            rule_content="函数命名规范",
            location={"file_path": "test.py", "line": 10},
            severity="high",
            message="函数名应该使用snake_case"
        )
        
        violation2 = ViolationItem(
            rule_id="STYLE001",
            rule_content="函数命名规范",
            location={"file_path": "test.py", "line": 15},
            severity="high",
            message="函数名应该使用snake_case命名法"
        )
        
        similarity = self.deduplication_service._calculate_violation_similarity(violation1, violation2)
        self.assertGreater(similarity, 0.8)  # 应该很高相似度
    
    def test_text_cleaning(self):
        """测试文本清理功能"""
        message = "这是一个违规信息 ```def bad(): pass``` 包含代码块"
        cleaned = self.deduplication_service._clean_message_text(message)
        self.assertNotIn("```", cleaned)
        self.assertNotIn("def bad(): pass", cleaned)
    
    def test_lower_similarity_threshold(self):
        """测试降低相似度阈值的效果"""
        # 使用更低的相似度阈值
        service = ViolationDeduplicationService(similarity_threshold=0.6)
        
        violations = [
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 10},
                severity="high",
                message="函数名应该使用snake_case"
            ),
            ViolationItem(
                rule_id="STYLE001",
                rule_content="函数命名规范",
                location={"file_path": "test.py", "line": 15},
                severity="high",
                message="函数名应该使用snake_case命名法"
            )
        ]
        
        result = service.deduplicate_violations(violations)
        # 更低的阈值应该更容易排重
        self.assertEqual(len(result), 1)


if __name__ == "__main__":
    unittest.main() 