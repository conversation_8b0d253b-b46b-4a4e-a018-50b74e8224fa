"""
规则分组优化测试

测试目标：
1. 验证智能分组算法的有效性
2. 确保分组大小在合理范围内
3. 验证配置参数的生效
4. 测试不同分组策略的效果
"""

import unittest
from unittest.mock import MagicMock, patch

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule


class TestRuleGroupingOptimization(unittest.TestCase):
    """测试规则分组优化功能"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试规则 - 模拟不同大小的分组
        self.test_rules = [
            # 大组：命名规范 (6个规则)
            CodeCheckRule(id="N1", name="函数命名", category=["命名规范", "函数"], enabled=True),
            CodeCheckRule(id="N2", name="变量命名", category=["命名规范", "变量"], enabled=True),
            CodeCheckRule(id="N3", name="类命名", category=["命名规范", "类"], enabled=True),
            CodeCheckRule(id="N4", name="常量命名", category=["命名规范", "常量"], enabled=True),
            CodeCheckRule(id="N5", name="模块命名", category=["命名规范", "模块"], enabled=True),
            CodeCheckRule(id="N6", name="包命名", category=["命名规范", "包"], enabled=True),
            
            # 小组：性能优化 (2个规则)
            CodeCheckRule(id="P1", name="循环优化", category=["性能优化", "循环"], enabled=True),
            CodeCheckRule(id="P2", name="内存优化", category=["性能优化", "内存"], enabled=True),
            
            # 单个规则：文档 (1个规则)
            CodeCheckRule(id="D1", name="注释规范", category=["文档", "注释"], enabled=True),
            
            # 中等组：代码结构 (4个规则)
            CodeCheckRule(id="S1", name="函数长度", category=["代码结构", "函数"], enabled=True),
            CodeCheckRule(id="S2", name="类结构", category=["代码结构", "类"], enabled=True),
            CodeCheckRule(id="S3", name="模块结构", category=["代码结构", "模块"], enabled=True),
            CodeCheckRule(id="S4", name="代码复杂度", category=["代码结构", "复杂度"], enabled=True),
        ]

    def test_rule_manager_adaptive_grouping(self):
        """测试RuleManager的自适应分组"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def load_rules(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 测试自适应分组
        adaptive_groups = rule_manager.group_rules_adaptive_for_list(
            self.test_rules,
            3,
            5,
            4
        )
        
        # 验证分组结果
        self.assertGreater(len(adaptive_groups), 0)
        
        # 统计分组大小
        group_sizes = [len(rules) for rules in adaptive_groups.values()]
        
        # 验证大部分组的大小在合理范围内
        reasonable_groups = [size for size in group_sizes if 3 <= size <= 5]
        self.assertGreater(len(reasonable_groups), 0)

    def test_rule_manager_similarity_grouping(self):
        """测试基于相似度的分组"""
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def load_rules(self):
                return self._rules
        
        rule_manager = MockRuleManager(self.test_rules)
        
        # 测试基于相似度的分组
        similarity_groups = rule_manager.group_rules_by_similarity(
            similarity_threshold=0.3
        )
        
        # 验证分组结果
        self.assertGreater(len(similarity_groups), 0)
        
        # 验证所有规则都被分组
        total_rules_in_groups = sum(len(rules) for rules in similarity_groups.values())
        self.assertEqual(total_rules_in_groups, len(self.test_rules))

    def test_adaptive_grouping_min_size_enforcement(self):
        """测试adaptive分组策略是否正确执行最小分组大小限制"""
        # 创建大量小分组的测试数据，模拟实际场景
        small_group_rules = []
        
        # 创建多个只有1-2条规则的小分组
        for i in range(10):
            category = f"小分组{i}"
            small_group_rules.append(
                CodeCheckRule(id=f"SG{i}.1", name=f"规则{i}.1", category=[category], enabled=True)
            )
            if i % 2 == 0:  # 每两个分组添加第二条规则
                small_group_rules.append(
                    CodeCheckRule(id=f"SG{i}.2", name=f"规则{i}.2", category=[category], enabled=True)
                )
        
        # 添加几个大分组作为对比
        for i in range(3):
            category = f"大分组{i}"
            for j in range(5):  # 每个大分组5条规则
                small_group_rules.append(
                    CodeCheckRule(id=f"LG{i}.{j}", name=f"大规则{i}.{j}", category=[category], enabled=True)
                )
        
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def load_rules(self):
                return self._rules
        
        rule_manager = MockRuleManager(small_group_rules)
        
        # 测试不同的最小分组大小设置
        test_cases = [
            (3, "最小分组大小3"),
            (4, "最小分组大小4"),
            (5, "最小分组大小5"),
        ]
        
        for min_size, description in test_cases:
            with self.subTest(description=description):
                adaptive_groups = rule_manager.group_rules_adaptive_for_list(
                    small_group_rules,
                    min_size,  # 最小分组大小
                    8,         # 最大分组大小
                    6          # 目标分组大小
                )
                
                # 验证所有分组都不小于最小分组大小
                group_sizes = [len(rules) for rules in adaptive_groups.values()]
                min_actual_size = min(group_sizes) if group_sizes else 0
                
                self.assertGreaterEqual(min_actual_size, min_size, 
                    f"存在小于最小分组大小{min_size}的分组，实际最小分组大小: {min_actual_size}")
                
                # 验证所有规则都被分组
                total_rules = sum(group_sizes)
                self.assertEqual(total_rules, len(small_group_rules))
                
                print(f"\n{description}:")
                print(f"  分组数: {len(adaptive_groups)}")
                print(f"  组大小分布: {sorted(group_sizes)}")
                print(f"  最小分组大小: {min_actual_size} (要求: {min_size})")

    def test_adaptive_grouping_edge_cases(self):
        """测试adaptive分组的边界情况"""
        # 测试规则总数小于最小分组大小的情况
        few_rules = [
            CodeCheckRule(id="R1", name="规则1", category=["类别1"], enabled=True),
            CodeCheckRule(id="R2", name="规则2", category=["类别2"], enabled=True),
        ]
        
        class MockRuleManager(RuleManager):
            def __init__(self, test_rules):
                self._rules = test_rules
                self._loaded = True
                self.rule_file_path = "test.md"
                self.merge_on = None
                self.include_sheets = None
            
            def load_rules(self):
                return self._rules
        
        rule_manager = MockRuleManager(few_rules)
        
        # 当规则总数小于最小分组大小时，应该合并成一个分组
        adaptive_groups = rule_manager.group_rules_adaptive_for_list(
            few_rules,
            5,  # 最小分组大小大于规则总数
            8,
            6
        )
        
        # 应该只有一个分组包含所有规则
        self.assertEqual(len(adaptive_groups), 1)
        self.assertEqual(len(list(adaptive_groups.values())[0]), 2)


if __name__ == '__main__':
    unittest.main() 