"""
测试RepositoryAnalyzer的related_calls填充逻辑

验证目标：
1. analyze_functions方法正确填充related_calls字段
2. 调用链统计功能正常工作
3. 不同方法返回的AffectedFunction数据完整性
"""

import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.external.code_analyzer import RepositoryIndex, StaticAnalyzer
from gate_keeper.external.code_analyzer.models.call_relation import \
    FunctionCall
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)
from gate_keeper.infrastructure.git.gitee.client import Gitee


class TestRepositoryAnalyzerRelatedCalls(unittest.TestCase):
    """测试RepositoryAnalyzer的related_calls填充逻辑"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的GitService
        self.mock_git_service = Mock(spec=GitService)
        
        # 创建RepositoryAnalyzer
        self.repo_analyzer = RepositoryAnalyzer(self.mock_git_service)
        
        # 模拟测试数据
        self.test_file_content = """
def main():
    result = process_data()
    return result

def process_data():
    data = get_data()
    return transform_data(data)

def get_data():
    return {'key': 'value'}

def transform_data(data):
    return data.get('key', 'default')
"""
        
        self.test_file_path = "test.py"
        self.test_changed_lines = [2, 3, 4]  # main函数的变更行
        
    def test_analyze_functions_fills_related_calls(self):
        """测试analyze_functions方法正确填充related_calls字段"""
        # 创建模拟的增强RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)

        # 模拟get_changed_functions返回的AffectedFunction
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    result = process_data()\n    return result",
            filepath="test.py",
            related_calls=[],  # 初始为空
            related_definitions=[],
            call_chains=[]
        )

        mock_repo_index.get_changed_functions.return_value = [mock_af]

        # 模拟get_related_calls返回调用关系
        mock_call = FunctionCall(
            caller="main",
            callee="process_data",
            file_path="test.py",
            line=[3],
            code="process_data()"
        )
        mock_repo_index.get_related_calls.return_value = [mock_call]

        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, 'get_index', return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[["main", "process_data"]]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):

            # 执行分析
            result = self.repo_analyzer.analyze_functions(
                repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )

            # 验证结果
            self.assertEqual(len(result), 1)
            affected_function = result[0]

            # 验证related_calls被正确填充
            self.assertIsInstance(affected_function.related_calls, list)
            self.assertGreater(len(affected_function.related_calls), 0)

            # 验证调用关系数据
            call_info = affected_function.related_calls[0]
            self.assertIsInstance(call_info, FunctionCall)
            self.assertEqual(call_info.caller, "main")
            self.assertEqual(call_info.callee, "process_data")
            self.assertEqual(call_info.line, [3])

            # 验证调用链统计
            total_call_chains = len(affected_function.related_calls)
            self.assertEqual(total_call_chains, 1)

    def test_related_calls_statistics_integration(self):
        """测试调用链统计的集成功能"""
        # 创建模拟的增强RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)

        # 模拟多个调用关系
        mock_calls = []
        for i in range(3):
            mock_call = FunctionCall(
                caller=f"func_{i}",
                callee=f"func_{i+1}",
                file_path="test.py",
                line=[i + 1],
                code=f"func_{i+1}()"
            )
            mock_calls.append(mock_call)

        mock_repo_index.get_related_calls.return_value = mock_calls

        # 模拟AffectedFunction
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )

        mock_repo_index.get_changed_functions.return_value = [mock_af]

        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, 'get_index', return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):

            # 执行分析
            result = self.repo_analyzer.analyze_functions(
                repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )

            # 验证调用链统计
            total_call_chains = sum(len(af.related_calls) for af in result)
            self.assertEqual(total_call_chains, 3)

    def test_empty_related_calls_handling(self):
        """测试没有调用关系时的处理"""
        # 创建模拟的增强RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)

        # 模拟没有调用关系
        mock_repo_index.get_related_calls.return_value = []

        # 模拟AffectedFunction
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )

        mock_repo_index.get_changed_functions.return_value = [mock_af]

        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, 'get_index', return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):

            # 执行分析
            result = self.repo_analyzer.analyze_functions(
                repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )

            # 验证结果
            self.assertEqual(len(result), 1)
            affected_function = result[0]

            # 验证related_calls为空列表
            self.assertIsInstance(affected_function.related_calls, list)
            self.assertEqual(len(affected_function.related_calls), 0)

            # 验证调用链统计为0
            total_call_chains = len(affected_function.related_calls)
            self.assertEqual(total_call_chains, 0)

    def test_related_calls_data_structure(self):
        """测试related_calls数据结构"""
        # 创建模拟的增强RepositoryIndex
        mock_repo_index = Mock(spec=RepositoryIndex)

        # 模拟调用关系
        mock_call = FunctionCall(
            caller="main",
            callee="process_data",
            file_path="test.py",
            line=[3],
            code="process_data()"
        )
        mock_repo_index.get_related_calls.return_value = [mock_call]

        # 模拟AffectedFunction
        mock_af = AffectedFunction(
            name="main",
            start_line=1,
            end_line=5,
            changed_lines=[2, 3, 4],
            code="def main():\n    pass",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            call_chains=[]
        )

        mock_repo_index.get_changed_functions.return_value = [mock_af]

        # 模拟RepositoryAnalyzer的方法
        with patch.object(self.repo_analyzer, 'get_index', return_value=mock_repo_index), \
             patch.object(StaticAnalyzer, '__init__', return_value=None), \
             patch.object(StaticAnalyzer, 'get_bidirectional_call_chains', return_value=[]), \
             patch.object(StaticAnalyzer, 'get_related_functions', return_value=[]):

            # 执行分析
            result = self.repo_analyzer.analyze_functions(
                repo_dir=".",
                file_path=self.test_file_path,
                file_content=self.test_file_content,
                branch="main",
                changed_lines=self.test_changed_lines,
                depth=3
            )

            # 验证数据结构
            affected_function = result[0]
            call_info = affected_function.related_calls[0]

            # 验证是FunctionCall对象
            self.assertIsInstance(call_info, FunctionCall)

            # 验证字段值
            self.assertEqual(call_info.caller, "main")
            self.assertEqual(call_info.callee, "process_data")
            self.assertEqual(call_info.line, [3])
            self.assertEqual(call_info.file_path, "test.py")


if __name__ == "__main__":
    unittest.main() 