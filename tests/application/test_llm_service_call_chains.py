"""
测试LLMService的调用链统计功能

验证目标：
1. analyze_mr方法正确统计调用链总数
2. 不同AffectedFunction的related_calls被正确累加
3. 统计逻辑的边界情况处理
"""

import unittest
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import (AffectedFunction,
                                                CheckMRResult, DiffResult)
from gate_keeper.application.service.llm import LLMService
from gate_keeper.external.code_analyzer.models.call_relation import FunctionCall
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """模拟LLM客户端"""
    
    def __init__(self, base_url="http://mock-llm"):  # 增加 base_url 默认参数
        self.responses = []
        self.call_count = 0
        super().__init__(base_url=base_url)
        
    def chat_completion(self, messages, **kwargs):
        """模拟LLM响应"""
        self.call_count += 1
        if self.responses:
            return self.responses.pop(0)
        return '<FinalAnswer>{"is_pass": true, "reason": "测试通过", "violations": []}</FinalAnswer>'

    def generate(self, *args, **kwargs):
        return self.chat_completion(*args, **kwargs)

    def get_config(self):
        return {}


class TestLLMServiceCallChains(unittest.TestCase):
    """测试LLMService的调用链统计功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockLLMClient()
        self.llm_service = LLMService(self.mock_client)
        
        # 准备测试规则
        self.test_rules = [
            CodeCheckRule(
                id="R1",
                name="函数命名规范",
                description="函数名应使用小写字母和下划线",
                category=["命名规范"],
                enabled=True,
                languages=["python"]
            )
        ]
        
    def test_call_chains_statistics_basic(self):
        """测试基本的调用链统计功能"""
        # 创建测试用的AffectedFunction，包含调用关系
        affected_functions = [
            AffectedFunction(
                name="main",
                start_line=1,
                end_line=10,
                changed_lines=[2, 3],
                code="def main():\n    result = process_data()\n    return result",
                filepath="test.py",
                related_calls=[
                    FunctionCall(caller="main", callee="process_data", file_path="test.py", line=[3]),
                    FunctionCall(caller="main", callee="validate_input", file_path="test.py", line=[2])
                ],
                related_definitions=[],
                call_chains=[]
            ),
            AffectedFunction(
                name="process_data",
                start_line=11,
                end_line=20,
                changed_lines=[15, 16],
                code="def process_data():\n    data = get_data()\n    return transform_data(data)",
                filepath="test.py",
                related_calls=[
                    FunctionCall(caller="process_data", callee="get_data", file_path="test.py", line=[15]),
                    FunctionCall(caller="process_data", callee="transform_data", file_path="test.py", line=[16])
                ],
                related_definitions=[],
                call_chains=[]
            )
        ]
        
        # 创建测试用的CheckMRResult
        diff_result = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        check_mr_result = CheckMRResult(
            mr_id=1,
            base_branch="main",
            dev_branch="feature",
            diffs=[diff_result]
        )
        
        # 设置有效的LLM响应
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>',
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 执行分析
        result = self.llm_service.analyze_mr(check_mr_result)
        
        # 验证调用链统计
        # 第一个函数有2个调用关系，第二个函数有2个调用关系，总共4个
        expected_total_call_chains = 4
        actual_total_call_chains = sum(len(af.related_calls) for af in affected_functions)
        self.assertEqual(actual_total_call_chains, expected_total_call_chains)
        
        # 验证每个函数的调用关系数量
        self.assertEqual(len(affected_functions[0].related_calls), 2)
        self.assertEqual(len(affected_functions[1].related_calls), 2)
        
    def test_call_chains_statistics_empty(self):
        """测试没有调用关系时的统计"""
        # 创建测试用的AffectedFunction，不包含调用关系
        affected_functions = [
            AffectedFunction(
                name="main",
                start_line=1,
                end_line=10,
                changed_lines=[2, 3],
                code="def main():\n    pass",
                filepath="test.py",
                related_calls=[],  # 空列表
                related_definitions=[],
                call_chains=[]
            )
        ]
        
        # 创建测试用的CheckMRResult
        diff_result = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        check_mr_result = CheckMRResult(
            mr_id=1,
            base_branch="main",
            dev_branch="feature",
            diffs=[diff_result]
        )
        
        # 设置有效的LLM响应
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 执行分析
        result = self.llm_service.analyze_mr(check_mr_result)
        
        # 验证调用链统计为0
        total_call_chains = sum(len(af.related_calls) for af in affected_functions)
        self.assertEqual(total_call_chains, 0)
        
    def test_call_chains_statistics_mixed(self):
        """测试混合情况下的调用链统计"""
        # 创建测试用的AffectedFunction，部分有调用关系，部分没有
        affected_functions = [
            AffectedFunction(
                name="main",
                start_line=1,
                end_line=10,
                changed_lines=[2, 3],
                code="def main():\n    result = process_data()\n    return result",
                filepath="test.py",
                related_calls=[
                    FunctionCall(caller="main", callee="process_data", file_path="test.py", line=[3])
                ],
                related_definitions=[],
                call_chains=[]
            ),
            AffectedFunction(
                name="helper",
                start_line=11,
                end_line=15,
                changed_lines=[12],
                code="def helper():\n    pass",
                filepath="test.py",
                related_calls=[],  # 没有调用关系
                related_definitions=[],
                call_chains=[]
            ),
            AffectedFunction(
                name="process_data",
                start_line=16,
                end_line=25,
                changed_lines=[20, 21],
                code="def process_data():\n    data = get_data()\n    return transform_data(data)",
                filepath="test.py",
                related_calls=[
                    FunctionCall(caller="process_data", callee="get_data", file_path="test.py", line=[20]),
                    FunctionCall(caller="process_data", callee="transform_data", file_path="test.py", line=[21]),
                    FunctionCall(caller="process_data", callee="validate_data", file_path="test.py", line=[22])
                ],
                related_definitions=[],
                call_chains=[]
            )
        ]
        
        # 创建测试用的CheckMRResult
        diff_result = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        check_mr_result = CheckMRResult(
            mr_id=1,
            base_branch="main",
            dev_branch="feature",
            diffs=[diff_result]
        )
        
        # 设置有效的LLM响应
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>',
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>',
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 执行分析
        result = self.llm_service.analyze_mr(check_mr_result)
        
        # 验证调用链统计
        # 第一个函数有1个调用关系，第二个函数有0个，第三个函数有3个，总共4个
        expected_total_call_chains = 4
        actual_total_call_chains = sum(len(af.related_calls) for af in affected_functions)
        self.assertEqual(actual_total_call_chains, expected_total_call_chains)
        
        # 验证每个函数的调用关系数量
        self.assertEqual(len(affected_functions[0].related_calls), 1)
        self.assertEqual(len(affected_functions[1].related_calls), 0)
        self.assertEqual(len(affected_functions[2].related_calls), 3)
        
    def test_call_chains_statistics_multiple_files(self):
        """测试多文件情况下的调用链统计"""
        # 创建多个文件的AffectedFunction
        file1_functions = [
            AffectedFunction(
                name="main",
                start_line=1,
                end_line=10,
                changed_lines=[2, 3],
                code="def main():\n    result = process_data()\n    return result",
                filepath="file1.py",
                related_calls=[
                    FunctionCall(caller="main", callee="process_data", file_path="file1.py", line=[3])
                ],
                related_definitions=[],
                call_chains=[]
            )
        ]
        
        file2_functions = [
            AffectedFunction(
                name="process_data",
                start_line=1,
                end_line=15,
                changed_lines=[5, 6],
                code="def process_data():\n    data = get_data()\n    return transform_data(data)",
                filepath="file2.py",
                related_calls=[
                    FunctionCall(caller="process_data", callee="get_data", file_path="file2.py", line=[5]),
                    FunctionCall(caller="process_data", callee="transform_data", file_path="file2.py", line=[6])
                ],
                related_definitions=[],
                call_chains=[]
            )
        ]
        
        # 创建测试用的CheckMRResult
        diff_result1 = DiffResult(
            filepath="file1.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=file1_functions,
            file_status="modified"
        )
        diff_result2 = DiffResult(
            filepath="file2.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=file2_functions,
            file_status="modified"
        )
        
        check_mr_result = CheckMRResult(
            mr_id=1,
            base_branch="main",
            dev_branch="feature",
            diffs=[diff_result1, diff_result2]
        )
        
        # 设置有效的LLM响应
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>',
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 执行分析
        result = self.llm_service.analyze_mr(check_mr_result)
        
        # 验证调用链统计
        # 第一个文件有1个调用关系，第二个文件有2个调用关系，总共3个
        all_functions = file1_functions + file2_functions
        expected_total_call_chains = 3
        actual_total_call_chains = sum(len(af.related_calls) for af in all_functions)
        self.assertEqual(actual_total_call_chains, expected_total_call_chains)
        
    def test_call_chains_data_integrity(self):
        """测试调用链数据的完整性"""
        # 创建测试用的AffectedFunction，包含完整的调用关系数据
        affected_functions = [
            AffectedFunction(
                name="main",
                start_line=1,
                end_line=10,
                changed_lines=[2, 3],
                code="def main():\n    result = process_data()\n    return result",
                filepath="test.py",
                related_calls=[
                    FunctionCall(
                        caller="main",
                        callee="process_data", 
                        file_path="test.py",
                        line=[3]
                    )
                ],
                related_definitions=[],
                call_chains=[]
            )
        ]
        
        # 创建测试用的CheckMRResult
        diff_result = DiffResult(
            filepath="test.py",
            origin_file_content="",
            new_file_content="",
            affected_functions=affected_functions,
            file_status="modified"
        )
        
        check_mr_result = CheckMRResult(
            mr_id=1,
            base_branch="main",
            dev_branch="feature",
            diffs=[diff_result]
        )
        
        # 设置有效的LLM响应
        self.mock_client.responses = [
            '<FinalAnswer>{"is_pass": true, "reason": "检查通过", "violations": []}</FinalAnswer>'
        ]
        
        # 执行分析
        result = self.llm_service.analyze_mr(check_mr_result)
        
        # 验证调用关系数据的完整性
        call_info = affected_functions[0].related_calls[0]
        
        # 验证是FunctionCall对象
        self.assertIsInstance(call_info, FunctionCall)
        
        # 验证字段值
        self.assertEqual(call_info.caller, "main")
        self.assertEqual(call_info.callee, "process_data")
        self.assertEqual(call_info.line, [3])
        self.assertEqual(call_info.file_path, "test.py")


if __name__ == "__main__":
    unittest.main() 