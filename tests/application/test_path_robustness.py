"""
路径处理健壮性测试

测试路径处理在各种异常情况下的表现
"""

import os
import tempfile
import unittest
from pathlib import Path

from gate_keeper.external.code_analyzer.core.repository_index import \
    RepositoryIndex


class TestPathRobustness(unittest.TestCase):
    """路径处理健壮性测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_malformed_paths(self):
        """测试畸形路径处理"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试各种畸形路径
        malformed_paths = [
            "",  # 空字符串
            None,  # None值
            "   ",  # 空白字符串
            "///",  # 多个斜杠
            "\\\\\\",  # 多个反斜杠
            "..",  # 上级目录
            "...",  # 多个点
            "file with spaces.py",  # 带空格的路径
            "file\twith\ttabs.py",  # 带制表符的路径
            "file\nwith\nnewlines.py",  # 带换行符的路径
            "file\0with\0nulls.py",  # 带空字符的路径
            "file\u0000with\u0000nulls.py",  # Unicode空字符
        ]
        
        for path in malformed_paths:
            try:
                normalized = repo_index.normalize_path(path)
                # 不应该抛出异常
                self.assertIsInstance(normalized, (str, type(None)))
            except Exception as e:
                self.fail(f"路径 {repr(path)} 应该被正确处理，但抛出了异常: {e}")
    
    def test_very_long_paths(self):
        """测试超长路径处理"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 创建超长路径
        long_filename = "a" * 1000 + ".py"
        long_path = os.path.join(self.repo_dir, "src", long_filename)
        
        try:
            normalized = repo_index.normalize_path(long_path)
            self.assertIsInstance(normalized, str)
        except Exception as e:
            self.fail(f"超长路径应该被正确处理，但抛出了异常: {e}")
    
    def test_unicode_paths(self):
        """测试Unicode路径处理"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试各种Unicode字符
        unicode_paths = [
            "file_中文.py",
            "file_日本語.py",
            "file_한국어.py",
            "file_русский.py",
            "file_العربية.py",
            "file_עברית.py",
            "file_ไทย.py",
            "file_हिन्दी.py",
            "file_🌍.py",  # Emoji
            "file_🏳️‍🌈.py",  # 复杂Emoji
        ]
        
        for path in unicode_paths:
            try:
                normalized = repo_index.normalize_path(path)
                self.assertIsInstance(normalized, str)
            except Exception as e:
                self.fail(f"Unicode路径 {path} 应该被正确处理，但抛出了异常: {e}")
    
    def test_special_characters_in_paths(self):
        """测试特殊字符路径处理"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试各种特殊字符
        special_chars = [
            "file!@#$%^&*().py",
            "file()[]{}.py",
            "file+-=<>?.py",
            "file|\\:;\"'.py",
            "file,~`@#$%^&*()_+-={}[]|\\:;\"'<>?,./.py",
        ]
        
        for path in special_chars:
            try:
                normalized = repo_index.normalize_path(path)
                self.assertIsInstance(normalized, str)
            except Exception as e:
                self.fail(f"特殊字符路径 {path} 应该被正确处理，但抛出了异常: {e}")
    
    def test_path_traversal_attempts(self):
        """测试路径遍历攻击防护"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试路径遍历尝试
        traversal_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "..%2F..%2F..%2Fetc%2Fpasswd",  # URL编码
            "..%5C..%5C..%5Cwindows%5Csystem32%5Cconfig%5Csam",  # URL编码
        ]
        
        for path in traversal_paths:
            try:
                normalized = repo_index.normalize_path(path)
                # 应该返回原路径或安全的相对路径
                self.assertIsInstance(normalized, str)
                # 不应该包含危险的路径遍历
                self.assertNotIn("../../../", normalized)
                self.assertNotIn("..\\..\\..\\", normalized)
            except Exception as e:
                self.fail(f"路径遍历尝试 {path} 应该被安全处理，但抛出了异常: {e}")
    
    def test_concurrent_path_operations(self):
        """测试并发路径操作"""
        import threading
        import time
        
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 创建多个线程同时进行路径操作
        results = []
        errors = []
        
        def path_operation(thread_id):
            try:
                for i in range(100):
                    path = f"src/file_{thread_id}_{i}.py"
                    normalized = repo_index.normalize_path(path)
                    results.append((thread_id, i, normalized))
                    time.sleep(0.001)  # 模拟工作负载
            except Exception as e:
                errors.append((thread_id, e))
        
        # 启动多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=path_operation, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        self.assertEqual(len(errors), 0, f"并发操作中出现了错误: {errors}")
        self.assertEqual(len(results), 500)  # 5个线程 * 100次操作
    
    def test_memory_efficient_path_handling(self):
        """测试内存高效的路径处理"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试大量路径处理的内存使用
        import gc
        import sys

        # 记录初始内存使用
        gc.collect()
        initial_memory = sys.getsizeof(repo_index.function_definitions)
        
        # 处理大量路径
        for i in range(10000):
            path = f"src/file_{i}.py"
            repo_index.normalize_path(path)
            repo_index.paths_equal(path, f"src/file_{i+1}.py")
        
        # 记录最终内存使用
        gc.collect()
        final_memory = sys.getsizeof(repo_index.function_definitions)
        
        # 内存使用不应该显著增加（因为路径处理不应该存储大量数据）
        memory_increase = final_memory - initial_memory
        self.assertLess(memory_increase, 1000000)  # 1MB限制
    
    def test_path_caching_behavior(self):
        """测试路径缓存行为"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试相同路径的重复处理
        test_path = "/tmp/test_repo/src/main.py"
        
        # 第一次处理
        result1 = repo_index.normalize_path(test_path)
        
        # 第二次处理（应该得到相同结果）
        result2 = repo_index.normalize_path(test_path)
        
        self.assertEqual(result1, result2)
        
        # 测试不同路径格式的一致性
        unix_path = "/tmp/test_repo/src/main.py"
        windows_path = "C:\\tmp\\test_repo\\src\\main.py"
        
        unix_result = repo_index.normalize_path(unix_path)
        windows_result = repo_index.normalize_path(windows_path)
        
        # 在相同仓库目录下，应该得到相同的结果
        if repo_index.repo_dir in unix_path:
            self.assertEqual(unix_result, windows_result)
    
    def test_error_recovery(self):
        """测试错误恢复能力"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        # 测试在异常情况下的恢复
        problematic_paths = [
            None,
            "",
            "invalid/path/with/../..",
            "path/with/../../too/many/../..",
        ]
        
        for path in problematic_paths:
            try:
                # 这些操作不应该抛出异常
                normalized = repo_index.normalize_path(path)
                self.assertIsInstance(normalized, (str, type(None)))
                
                # 路径比较也应该工作
                result = repo_index.paths_equal(path, path)
                self.assertIsInstance(result, bool)
            except Exception as e:
                self.fail(f"路径 {repr(path)} 应该被安全处理，但抛出了异常: {e}")
    
    def test_performance_under_load(self):
        """测试负载下的性能"""
        repo_index = RepositoryIndex(repo_dir=self.repo_dir)
        
        import time

        # 测试大量路径处理的性能
        start_time = time.time()
        
        for i in range(1000):
            path = f"src/file_{i}.py"
            repo_index.normalize_path(path)
            repo_index.paths_equal(path, f"src/file_{i+1}.py")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 1000次操作应该在合理时间内完成（比如1秒内）
        self.assertLess(duration, 1.0, f"1000次路径操作耗时 {duration} 秒，超过预期")


if __name__ == '__main__':
    unittest.main() 