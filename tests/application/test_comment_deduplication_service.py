import unittest
from unittest.mock import MagicMock, patch

import pytest

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.deduplication import \
    CommentDeduplicationService
from gate_keeper.external.code_analyzer.models.call_relation import FunctionCall
from gate_keeper.external.code_analyzer.models.function import Function, FunctionSignature
from gate_keeper.domain.value_objects.git import CodeCheckDiscussion


class TestCommentDeduplicationService(unittest.TestCase):
    def setUp(self):
        """测试初始化"""
        self.service = CommentDeduplicationService()
        self.repo_url = "https://example.com/repo"
        self.project_id = "test_project"
        self.mr_id = 123
        
        # 模拟一些已存在的评论
        self.existing_comments = [
            CodeCheckDiscussion(
                id="1",
                body="函数命名不规范，建议使用动词+名词的形式",
                created_at="2024-03-20T10:00:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False
            ),
            CodeCheckDiscussion(
                id="2",
                body="缺少函数文档注释，请添加参数说明",
                created_at="2024-03-20T10:01:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False
            ),
            CodeCheckDiscussion(
                id="3",
                body="这个函数太长了，建议拆分成多个小函数",
                created_at="2024-03-20T10:02:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False
            )
        ]
        
        # 模拟受影响的函数
        self.affected_function = AffectedFunction(
            name="process_data",
            filepath="src/main.py",
            start_line=10,
            end_line=30,
            code="""def process_data(data):
    # 处理数据
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.transform())
    return result""",
            changed_lines=[15, 16],
            related_calls=[],
            related_definitions=[]
        )

    def test_exact_duplicate_comment(self):
        """测试完全相同的评论去重"""
        # 计算新评论的代码哈希
        new_comment = "函数命名不规范，建议使用动词+名词的形式"
        expected_hash = self.service._calculate_code_hash(new_comment, self.affected_function)
        
        # 设置第一个评论有相同的代码哈希
        self.existing_comments[0].checked_code = expected_hash
        
        # 模拟获取已存在评论
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 使用已存在的评论进行测试
        result = self.service.get_duplicate_analysis(
            self.project_id, 
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(len(result["code_hash_matches"]), 1)
        self.assertEqual(result["code_hash_matches"][0]["id"], "1")

    def test_similar_comment(self):
        """测试相似评论的去重"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 使用相似但不完全相同的评论
        new_comment = "函数的命名不规范，应该使用动词+名词的形式来命名"
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        self.assertTrue(len(result["similarity_matches"]) > 0)
        self.assertTrue(result["similarity_matches"][0]["similarity"] > 0.7)

    def test_code_context_duplicate(self):
        """测试基于代码上下文的去重"""
        # 创建带有代码上下文的评论
        comments_with_context = [
            CodeCheckDiscussion(
                id="4",
                body="这个函数需要添加错误处理",
                created_at="2024-03-20T10:00:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False,
                affected_function=self.affected_function
            )
        ]
        self.service.get_existing_comments = MagicMock(return_value=comments_with_context)
        
        # 测试针对相同函数的类似评论
        new_comment = "函数缺少异常处理机制"
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(len(result["code_hash_matches"]), 0)

    def test_no_duplicates(self):
        """测试没有重复的情况"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 使用完全不同的评论
        new_comment = "建议添加单元测试覆盖这段代码"
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(len(result["code_hash_matches"]), 0)
        self.assertEqual(len([m for m in result["similarity_matches"] if m["similarity"] > 0.7]), 0)

    def test_empty_comments(self):
        """测试空评论的情况"""
        self.service.get_existing_comments = MagicMock(return_value=[])
        
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            "新的评论",
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(result["total_existing"], 0)
        self.assertEqual(len(result["code_hash_matches"]), 0)
        self.assertEqual(len(result["similarity_matches"]), 0)

    def test_invalid_input(self):
        """测试无效输入的处理"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        
        # 测试空评论
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            "",
            self.affected_function
        )
        self.assertIn("error", result)
        
        # 测试None评论
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            None,
            self.affected_function
        )
        self.assertIn("error", result)

    def test_similarity_threshold(self):
        """测试相似度阈值的影响"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        self.service.similarity_threshold = 0.8  # 设置较高的相似度阈值
        
        # 测试中等相似度的评论
        new_comment = "函数的命名方式需要改进"  # 与原评论相似度适中
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        high_similarity_matches = [m for m in result["similarity_matches"] if m["similarity"] > 0.8]
        self.assertEqual(len(high_similarity_matches), 0)  # 不应该有高于阈值的匹配

    def test_code_hash_disabled(self):
        """测试禁用代码哈希匹配的情况"""
        self.service.get_existing_comments = MagicMock(return_value=self.existing_comments)
        self.service.code_hash_enabled = False
        
        new_comment = "函数命名不规范，建议使用动词+名词的形式"  # 完全相同的评论
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            new_comment,
            self.affected_function
        )
        
        # 验证结果
        self.assertEqual(len(result["code_hash_matches"]), 0)  # 不应该有哈希匹配

    def test_performance(self):
        """测试性能表现"""
        # 生成大量评论
        many_comments = []
        for i in range(1000):
            many_comments.append(CodeCheckDiscussion(
                id=str(i),
                body=f"Comment {i}: This is a test comment with some variations {i}",
                created_at="2024-03-20T10:00:00Z",
                mr_id="",
                project_id="",
                commit_id="",
                checked_code="",
                is_resolved=False
            ))
        
        self.service.get_existing_comments = MagicMock(return_value=many_comments)
        
        import time
        start_time = time.time()
        
        result = self.service.get_duplicate_analysis(
            self.project_id,
            self.mr_id,
            "This is a new comment",
            self.affected_function
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证执行时间在可接受范围内（例如小于1秒）
        self.assertLess(execution_time, 1.0)

def test_format_comment_with_context():
    """测试评论格式化功能"""
    service = CommentDeduplicationService()
    
    # 准备测试数据
    comment = "这是一个测试评论"
    affected_function = AffectedFunction(
        name="test_function",
        filepath="test.py",
        start_line=1,
        end_line=10,
        code="def test_function():\n    pass",
        changed_lines=[1, 2],
        related_calls=[FunctionCall(
            caller="main_function",
            callee="test_function",
            file_path="caller.py",
            line=[5],
            code="test_function()"
        )],
        related_definitions=[
            Function.create_simple(
                name="helper_function",
                start_line=1,
                end_line=5,
                filepath="helper.py",
                signature=FunctionSignature(name="helper_function", parameters=[]),
                code="def helper_function():\n    pass"
            )
        ]
    )
    
    # 执行格式化
    formatted = service.format_comment_with_context(comment, affected_function)
    
    # 验证结果
    assert "这是一个测试评论" in formatted
    assert "test_function" in formatted
    assert "test.py:1-10" in formatted
    assert "def test_function()" in formatted
    assert "caller.py:5" in formatted
    assert "test_function()" in formatted
    assert "helper_function" in formatted
    assert "def helper_function()" in formatted
    assert "变更的行号: 1, 2" in formatted  # 修改期望文本
    assert "<details>" in formatted
    assert "</details>" in formatted

def test_context_fingerprint():
    """测试上下文指纹计算"""
    service = CommentDeduplicationService()
    
    # 准备测试数据
    af1 = AffectedFunction(
        name="test_function",
        filepath="test.py",
        start_line=1,
        end_line=10,
        code="def test_function():\n    pass",
        related_calls=[FunctionCall(
            caller="main_function",
            callee="test_function",
            file_path="caller.py",
            line=[5],
            code="test_function()"
        )],
        related_definitions=[
            Function.create_simple(
                name="helper",
                start_line=1,
                end_line=5,
                filepath="helper.py",
                signature=FunctionSignature(name="helper", parameters=[]),
                code="def helper():\n    pass"
            )
        ],
        changed_lines=[1, 2]
    )
    
    af2 = AffectedFunction(
        name="test_function",
        filepath="test.py",
        start_line=1,
        end_line=10,
        code="def test_function():\n    pass",
        related_calls=[FunctionCall(
            caller="main_function",
            callee="test_function",
            file_path="caller.py",
            line=[5],
            code="test_function()"
        )],
        related_definitions=[
            Function.create_simple(
                name="helper",
                start_line=1,
                end_line=5,
                filepath="helper.py",
                signature=FunctionSignature(name="helper", parameters=[]),
                code="def helper():\n    pass"
            )
        ],
        changed_lines=[1, 2]
    )
    
    # 计算指纹
    fp1 = service._calculate_context_fingerprint(af1)
    fp2 = service._calculate_context_fingerprint(af2)
    
    # 验证相同上下文生成相同指纹
    assert fp1 == fp2
    
    # 修改上下文
    af2.code = "def test_function():\n    return True"
    fp3 = service._calculate_context_fingerprint(af2)
    
    # 验证不同上下文生成不同指纹
    assert fp1 != fp3

def test_same_issue_detection():
    """测试相同问题检测"""
    service = CommentDeduplicationService()
    
    # 准备测试数据
    comment1 = """
    函数缺少错误处理
    ```python
    def test():
        pass
    ```
    """
    
    comment2 = """
    函数缺少错误处理机制
    ```python
    def other():
        pass
    ```
    """
    
    comment3 = "完全不同的问题描述"
    
    # 验证相似问题检测
    assert service._is_same_issue(comment1, comment2) == True
    assert service._is_same_issue(comment1, comment3) == False

def test_code_block_removal():
    """测试代码块移除"""
    service = CommentDeduplicationService()
    
    # 准备测试数据
    text = """
    这是一段描述
    ```python
    def test():
        pass
    ```
    这是行内代码 `var = 1`
    <details>
    一些细节
    </details>
    结束
    """
    
    # 移除代码块
    cleaned = service._remove_code_blocks(text)
    
    # 验证结果
    assert "这是一段描述" in cleaned
    assert "def test():" not in cleaned
    assert "var = 1" not in cleaned
    assert "一些细节" not in cleaned
    assert "结束" in cleaned

def test_duplicate_detection_with_context():
    """测试基于上下文的重复检测"""
    service = CommentDeduplicationService()
    
    # 准备测试数据
    af1 = AffectedFunction(
        name="test_function",
        filepath="test.py",
        start_line=1,
        end_line=10,
        code="def test_function():\n    pass",
        related_calls=[FunctionCall(
            caller="main_function",
            callee="test_function",
            file_path="caller.py",
            line=[5],
            code="test_function()"
        )],
        changed_lines=[1, 2]
    )
    
    af2 = AffectedFunction(
        name="test_function",
        filepath="test.py",
        start_line=1,
        end_line=10,
        code="def test_function():\n    pass",
        related_calls=[FunctionCall(
            caller="main_function",
            callee="test_function",
            file_path="caller.py",
            line=[5],
            code="test_function()"
        )],
        changed_lines=[1, 2]
    )
    
    comment1 = "函数缺少错误处理"
    comment2 = "函数需要添加错误处理"
    
    # 模拟现有评论
    existing_comments = [{
        "body": comment1,
        "affected_function": {
            "name": "test_function",
            "filepath": "test.py",
            "start_line": 1,
            "end_line": 10,
            "code": af1.code,
            "related_calls": af1.related_calls,
            "changed_lines": [1, 2]
        }
    }]
    
    # 验证相同上下文+相似问题被检测为重复
    assert service._is_duplicate_by_code(comment2, existing_comments, af2) == True
    
    # 修改上下文
    af2.code = "def test_function():\n    return True"
    
    # 验证不同上下文+相似问题不被检测为重复
    assert service._is_duplicate_by_code(comment2, existing_comments, af2) == False

if __name__ == '__main__':
    unittest.main() 