"""
正则表达式匹配器测试

测试RegexMatcher和RegexCache的功能
"""

import unittest
import time
from unittest.mock import Mock, patch

from gate_keeper.application.service.rule.regex_matcher import RegexMatcher, RegexCache
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.external.code_analyzer.models.element import CodeElement


class TestRegexCache(unittest.TestCase):
    """测试正则表达式缓存"""
    
    def setUp(self):
        """每个测试前清空缓存"""
        RegexCache.clear_cache()
    
    def tearDown(self):
        """每个测试后清空缓存"""
        RegexCache.clear_cache()
    
    def test_regex_cache_basic_functionality(self):
        """测试缓存基本功能"""
        pattern = r"test\s*\("
        
        # 第一次获取，应该编译并缓存
        regex1 = RegexCache.get_compiled_regex(pattern)
        self.assertIsNotNone(regex1)
        
        # 第二次获取，应该从缓存返回
        regex2 = RegexCache.get_compiled_regex(pattern)
        self.assertIsNotNone(regex2)
        self.assertIs(regex1, regex2)  # 应该是同一个对象
    
    def test_regex_cache_invalid_pattern(self):
        """测试无效正则表达式"""
        invalid_patterns = [
            r"[unclosed",
            r"(?P<invalid",
            r"*invalid",
            r"(?invalid)"
        ]
        
        for pattern in invalid_patterns:
            regex = RegexCache.get_compiled_regex(pattern)
            self.assertIsNone(regex)
    
    def test_regex_cache_empty_pattern(self):
        """测试空模式"""
        regex = RegexCache.get_compiled_regex("")
        self.assertIsNone(regex)
        
        regex = RegexCache.get_compiled_regex(None)
        self.assertIsNone(regex)
    
    def test_regex_cache_eviction(self):
        """测试缓存淘汰机制"""
        # 设置较小的缓存大小进行测试
        original_max_size = RegexCache._max_cache_size
        RegexCache._max_cache_size = 3
        
        try:
            patterns = [f"pattern{i}" for i in range(5)]
            
            # 添加模式到缓存
            for pattern in patterns:
                RegexCache.get_compiled_regex(pattern)
            
            # 缓存大小应该不超过最大值
            self.assertLessEqual(len(RegexCache._cache), 3)
            
        finally:
            RegexCache._max_cache_size = original_max_size
    
    def test_regex_cache_stats(self):
        """测试缓存统计"""
        patterns = [r"test\d+", r"func\s*\(", r"\w+\s*="]
        
        for pattern in patterns:
            RegexCache.get_compiled_regex(pattern)
        
        stats = RegexCache.get_cache_stats()
        self.assertEqual(stats["cache_size"], 3)
        self.assertEqual(stats["total_patterns"], 3)


class TestRegexMatcher(unittest.TestCase):
    """测试正则表达式匹配器"""
    
    def setUp(self):
        """设置测试环境"""
        RegexCache.clear_cache()
        self.matcher = RegexMatcher(timeout=1.0)
    
    def tearDown(self):
        """清理测试环境"""
        RegexCache.clear_cache()
    
    def test_match_code_basic(self):
        """测试基本代码匹配"""
        test_cases = [
            {
                "code": "memcpy(dst, src, len);",
                "pattern": r"memcpy\s*\(",
                "expected": True
            },
            {
                "code": "int arr[10]; arr[i] = value;",
                "pattern": r"\[\s*\w+\s*\]",
                "expected": True
            },
            {
                "code": "result = a / b;",
                "pattern": r"\w+\s*/\s*\w+",
                "expected": True
            },
            {
                "code": "printf('hello world');",
                "pattern": r"memcpy\s*\(",
                "expected": False
            }
        ]
        
        for case in test_cases:
            with self.subTest(case=case):
                result = self.matcher.match_code(case["code"], case["pattern"])
                self.assertEqual(result, case["expected"])
    
    def test_match_code_edge_cases(self):
        """测试边界情况"""
        # 空代码
        result = self.matcher.match_code("", r"test\s*\(")
        self.assertFalse(result)
        
        # 空模式
        result = self.matcher.match_code("test();", "")
        self.assertFalse(result)
        
        # 无效模式
        result = self.matcher.match_code("test();", r"[invalid")
        self.assertFalse(result)
    
    def test_get_matching_rules(self):
        """测试获取匹配的规则"""
        rules = [
            CodeCheckRule(
                id="R1",
                name="内存拷贝检查",
                description="检查memcpy",
                category=["内存"],
                enabled=True,
                languages=["c"],
                rule_value="memcpy检查",
                severity="高",
                match_regex=r"memcpy\s*\("
            ),
            CodeCheckRule(
                id="R2",
                name="数组访问检查",
                description="检查数组访问",
                category=["数组"],
                enabled=True,
                languages=["c"],
                rule_value="数组检查",
                severity="中",
                match_regex=r"\[\s*\w+\s*\]"
            ),
            CodeCheckRule(
                id="R3",
                name="通用规则",
                description="没有regex的规则",
                category=["通用"],
                enabled=True,
                languages=["c"],
                rule_value="通用检查",
                severity="低"
                # 没有match_regex字段
            )
        ]
        
        # 测试包含memcpy的代码
        code = "memcpy(dst, src, len);"
        matching_rules = self.matcher.get_matching_rules(code, rules)
        
        # 应该匹配R1和R3（R3没有regex，所以总是匹配）
        self.assertEqual(len(matching_rules), 2)
        rule_ids = [r.id for r in matching_rules]
        self.assertIn("R1", rule_ids)
        self.assertIn("R3", rule_ids)
        self.assertNotIn("R2", rule_ids)
    
    def test_batch_match_rules(self):
        """测试批量匹配规则"""
        rules = [
            CodeCheckRule(
                id="R1",
                name="内存拷贝",
                description="memcpy检查",
                category=["内存"],
                enabled=True,
                languages=["c"],
                rule_value="memcpy",
                severity="高",
                match_regex=r"memcpy\s*\("
            ),
            CodeCheckRule(
                id="R2",
                name="数组访问",
                description="数组检查",
                category=["数组"],
                enabled=True,
                languages=["c"],
                rule_value="数组",
                severity="中",
                match_regex=r"\[\s*\w+\s*\]"
            ),
            CodeCheckRule(
                id="R3",
                name="通用规则",
                description="通用检查",
                category=["通用"],
                enabled=True,
                languages=["c"],
                rule_value="通用",
                severity="低"
            )
        ]
        
        code = "memcpy(dst, src, len);"
        result = self.matcher.batch_match_rules(code, rules)
        
        self.assertEqual(len(result["matched"]), 1)  # R1
        self.assertEqual(len(result["unmatched"]), 1)  # R2
        self.assertEqual(len(result["no_regex"]), 1)  # R3
        
        self.assertEqual(result["matched"][0].id, "R1")
        self.assertEqual(result["unmatched"][0].id, "R2")
        self.assertEqual(result["no_regex"][0].id, "R3")
    
    def test_extract_code_content(self):
        """测试代码内容提取"""
        # 创建模拟的CodeElement
        code_element = Mock(spec=CodeElement)
        code_element.code = "test code content"
        code_element.filepath = "/path/to/test.c"
        code_element.start_line = 1
        code_element.end_line = 5
        
        # 测试从code字段提取
        content = self.matcher.extract_code_content(code_element)
        self.assertEqual(content, "test code content")
    
    @patch('builtins.open')
    def test_extract_code_content_from_file(self, mock_open):
        """测试从文件提取代码内容"""
        # 模拟文件内容
        file_lines = [
            "line 1\n",
            "line 2\n",
            "line 3\n",
            "line 4\n",
            "line 5\n"
        ]
        mock_open.return_value.__enter__.return_value.readlines.return_value = file_lines
        
        # 创建没有code字段的CodeElement
        code_element = Mock(spec=CodeElement)
        code_element.code = None
        code_element.filepath = "/path/to/test.c"
        code_element.start_line = 2
        code_element.end_line = 4
        
        content = self.matcher.extract_code_content(code_element)
        expected = "line 2\nline 3\nline 4\n"
        self.assertEqual(content, expected)
    
    def test_get_applicable_rules_with_regex(self):
        """测试基于regex的规则筛选"""
        rules = [
            CodeCheckRule(
                id="R1",
                name="内存操作",
                description="内存相关",
                category=["内存"],
                enabled=True,
                languages=["c"],
                rule_value="内存",
                severity="高",
                match_regex=r"(memcpy|malloc|free)\s*\("
            ),
            CodeCheckRule(
                id="R2",
                name="字符串操作",
                description="字符串相关",
                category=["字符串"],
                enabled=True,
                languages=["c"],
                rule_value="字符串",
                severity="中",
                match_regex=r"(strcpy|strcat|strlen)\s*\("
            )
        ]
        
        # 创建包含内存操作的代码元素
        code_element = Mock(spec=CodeElement)
        code_element.code = "ptr = malloc(size); memcpy(dst, src, len);"
        
        applicable_rules = self.matcher.get_applicable_rules_with_regex(code_element, rules)
        
        # 应该只匹配内存操作规则
        self.assertEqual(len(applicable_rules), 1)
        self.assertEqual(applicable_rules[0].id, "R1")


class TestRegexMatcherPerformance(unittest.TestCase):
    """测试正则匹配器性能"""
    
    def setUp(self):
        RegexCache.clear_cache()
        self.matcher = RegexMatcher(timeout=0.1)  # 短超时用于测试
    
    def tearDown(self):
        RegexCache.clear_cache()
    
    def test_regex_caching_performance(self):
        """测试正则缓存对性能的影响"""
        pattern = r"(memcpy|strcpy|malloc|free)\s*\("
        code = "memcpy(dst, src, len); strcpy(dst, src); malloc(size);" * 100
        
        # 第一次匹配（需要编译正则）
        start_time = time.time()
        for _ in range(10):
            self.matcher.match_code(code, pattern)
        first_time = time.time() - start_time
        
        # 第二次匹配（使用缓存）
        start_time = time.time()
        for _ in range(10):
            self.matcher.match_code(code, pattern)
        second_time = time.time() - start_time
        
        # 缓存应该提高性能（第二次应该更快）
        self.assertLess(second_time, first_time * 1.5)  # 允许一些误差
    
    def test_timeout_handling(self):
        """测试超时处理"""
        # 创建一个可能导致超时的复杂正则表达式
        complex_pattern = r"(a+)+b"
        problematic_code = "a" * 1000 + "c"  # 不匹配的长字符串
        
        # 设置很短的超时
        matcher = RegexMatcher(timeout=0.001)
        
        # 应该能处理超时情况而不崩溃
        result = matcher.match_code(problematic_code, complex_pattern)
        # 结果可能是True或False，重要的是不抛出异常


class TestRegexMatcherIntegration(unittest.TestCase):
    """测试正则匹配器集成"""
    
    def test_real_world_patterns(self):
        """测试真实世界的代码模式"""
        matcher = RegexMatcher()
        
        real_patterns = [
            {
                "name": "内存拷贝",
                "pattern": r"memcpy\s*\(|memcpy_s\s*\(|memmove\s*\(|memmove_s\s*\(",
                "positive_code": "memcpy(dst, src, len);",
                "negative_code": "printf('hello');"
            },
            {
                "name": "数组访问",
                "pattern": r"\[\s*\w+\s*\]",
                "positive_code": "arr[index] = value;",
                "negative_code": "func(param);"
            },
            {
                "name": "除法运算",
                "pattern": r"\w+\s*/\s*\w+|\w+\s*%\s*\w+",
                "positive_code": "result = a / b;",
                "negative_code": "result = a + b;"
            },
            {
                "name": "指针操作",
                "pattern": r"\*\s*\w+|\w+\s*->\s*\w+",
                "positive_code": "*ptr = value; obj->field = value;",
                "negative_code": "value = 42;"
            }
        ]
        
        for pattern_info in real_patterns:
            with self.subTest(pattern=pattern_info["name"]):
                # 测试正匹配
                result = matcher.match_code(
                    pattern_info["positive_code"],
                    pattern_info["pattern"]
                )
                self.assertTrue(result, f"Pattern '{pattern_info['name']}' should match positive code")
                
                # 测试负匹配
                result = matcher.match_code(
                    pattern_info["negative_code"],
                    pattern_info["pattern"]
                )
                self.assertFalse(result, f"Pattern '{pattern_info['name']}' should not match negative code")


if __name__ == '__main__':
    unittest.main()
