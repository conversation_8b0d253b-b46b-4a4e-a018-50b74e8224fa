"""
ResultParser模块单元测试

目标：验证ResultParser能够正确解析各种格式的LLM响应，包括：
- FinalAnswer标签（大小写变体）
- 代码块包裹的JSON
- 截断的JSON
- 缺少字段的数据
- 各种异常情况

测试覆盖：
- __extract_answer: JSON提取功能
- safe_json_loads: JSON解析和修复功能
- process_result: 完整的结果处理流程
- 各种边界情况和异常处理

验证重点：
- 解析准确性
- 错误恢复能力
- 数据完整性
- 性能表现
"""

import json
import unittest
from unittest.mock import patch

from gate_keeper.application.service.llm import ResultParser
from gate_keeper.domain.value_objects.analysis_result import (AnalyzeLLMResult,
                                                              ViolationItem)


class TestResultParser(unittest.TestCase):
    """ResultParser单元测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.parser = ResultParser()
        
    def test_extract_answer_finalanswer_uppercase(self):
        """测试提取FinalAnswer标签（大写A）"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "发现内存泄漏问题",
          "violations": []
        }
        </FinalAnswer>
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_finalanswer_lowercase(self):
        """测试提取Finalanswer标签（小写a）"""
        text = """
        <Finalanswer>
        {
          "is_pass": true,
          "reason": "代码符合规范",
          "violations": []
        }
        </Finalanswer>
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_finalanswer_all_lowercase(self):
        """测试提取finalanswer标签（全小写）"""
        text = """
        <finalanswer>
        {
          "is_pass": false,
          "reason": "发现违规",
          "violations": []
        }
        </finalanswer>
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_mixed_case(self):
        """测试提取混合大小写的FinalAnswer标签"""
        text = """
        <FinalAnswer>
        {
          "is_pass": true,
          "reason": "测试混合大小写",
          "violations": []
        }
        </Finalanswer>
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_truncated_finalanswer(self):
        """测试提取截断的FinalAnswer标签"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "在生成流式响应时，代码中存在不安全的字符串替换操作和潜在的内存泄漏风险。",
          "violations": [
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        
    def test_extract_answer_json_code_block(self):
        """测试提取JSON代码块"""
        text = """
        ```json
        {
          "is_pass": true,
          "reason": "代码符合规范",
          "violations": []
        }
        ```
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_xml_code_block(self):
        """测试提取XML代码块"""
        text = """
        ```xml
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "发现违规",
          "violations": []
        }
        </FinalAnswer>
        ```
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_no_language_code_block(self):
        """测试提取无语言标记的代码块"""
        text = """
        ```
        {
          "is_pass": true,
          "reason": "无标记代码块",
          "violations": []
        }
        ```
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_inline_code(self):
        """测试提取内联代码"""
        text = """
        分析结果：`{"is_pass": false, "reason": "内联代码测试", "violations": []}`
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_plain_json(self):
        """测试提取纯JSON"""
        text = """
        {
          "is_pass": true,
          "reason": "纯JSON测试",
          "violations": []
        }
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('{'))
        self.assertTrue(result.endswith('}'))
        
    def test_extract_answer_multiple_json_objects(self):
        """测试提取多个JSON对象中的最后一个"""
        text = """
        第一个JSON: {"test": "first"}
        第二个JSON: {"is_pass": true, "reason": "第二个", "violations": []}
        第三个JSON: {"is_pass": false, "reason": "第三个", "violations": []}
        """
        result = self.parser._ResultParser__extract_answer(text)
        self.assertIsInstance(result, str)
        # 应该提取最后一个JSON对象
        self.assertIn("第三个", result)
        
    def test_extract_answer_empty_text(self):
        """测试空文本"""
        result = self.parser._ResultParser__extract_answer("")
        self.assertEqual(result, "")
        
    def test_extract_answer_no_json(self):
        """测试没有JSON的文本"""
        text = "这是一段没有JSON的普通文本"
        result = self.parser._ResultParser__extract_answer(text)
        self.assertEqual(result, "")
        
    def test_safe_json_loads_normal_json(self):
        """测试正常JSON解析"""
        json_str = '{"is_pass": true, "reason": "正常JSON", "violations": []}'
        result = self.parser.safe_json_loads(json_str)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["is_pass"], True)
        self.assertEqual(result["reason"], "正常JSON")
        
    def test_safe_json_loads_with_code_block(self):
        """测试带代码块标记的JSON解析"""
        json_str = '```json\n{"is_pass": false, "reason": "代码块JSON", "violations": []}\n```'
        result = self.parser.safe_json_loads(json_str)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["is_pass"], False)
        self.assertEqual(result["reason"], "代码块JSON")
        
    def test_safe_json_loads_single_quotes(self):
        """测试单引号JSON解析"""
        json_str = "{'is_pass': true, 'reason': '单引号JSON', 'violations': []}"
        result = self.parser.safe_json_loads(json_str)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["is_pass"], True)
        self.assertEqual(result["reason"], "单引号JSON")
        
    def test_safe_json_loads_trailing_comma(self):
        """测试尾随逗号JSON解析"""
        json_str = '{"is_pass": true, "reason": "尾随逗号", "violations": [],}'
        result = self.parser.safe_json_loads(json_str)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["is_pass"], True)
        
    def test_safe_json_loads_truncated_json(self):
        """测试截断JSON解析"""
        json_str = '{"is_pass": false, "reason": "截断JSON", "violations": ['
        result = self.parser.safe_json_loads(json_str)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["is_pass"], False)
        self.assertEqual(result["reason"], "截断JSON")
        
    def test_safe_json_loads_control_characters(self):
        """测试包含控制字符的JSON解析"""
        json_str = '{"is_pass": true, "reason": "控制字符", "violations": []}'
        result = self.parser.safe_json_loads(json_str)
        self.assertIsInstance(result, dict)
        self.assertEqual(result["is_pass"], True)
        
    def test_safe_json_loads_empty_string(self):
        """测试空字符串JSON解析"""
        with self.assertRaises(ValueError):
            self.parser.safe_json_loads("")
            
    def test_process_result_complete_json(self):
        """测试完整JSON结果处理"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "发现内存泄漏",
          "violations": [
            {
              "rule_id": "MEM001",
              "rule_content": "内存管理规范",
              "location": {"line": 10},
              "severity": "高",
              "message": "内存未释放"
            }
          ]
        }
        </FinalAnswer>
        """
        result = self.parser.process_result(text, "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(result.is_pass, False)
        self.assertEqual(result.reason, "发现内存泄漏")
        self.assertEqual(len(result.violations), 1)
        self.assertEqual(result.violations[0].rule_id, "MEM001")
        
    def test_process_result_no_violations(self):
        """测试没有violations字段的结果处理"""
        text = """
        <FinalAnswer>
        {
          "is_pass": true,
          "reason": "代码符合规范"
        }
        </FinalAnswer>
        """
        result = self.parser.process_result(text, "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(result.is_pass, True)
        self.assertEqual(result.reason, "代码符合规范")
        self.assertEqual(len(result.violations), 0)
        
    def test_process_result_incomplete_violations(self):
        """测试不完整的violations字段处理"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "发现违规",
          "violations": [
            {
              "rule_id": "R001",
              "message": "违规详情"
            }
          ]
        }
        </FinalAnswer>
        """
        result = self.parser.process_result(text, "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(len(result.violations), 1)
        # 应该自动补全缺少的字段
        self.assertEqual(result.violations[0].location, {"line": None})
        self.assertEqual(result.violations[0].message, "违规详情")
        
    def test_process_result_truncated_json(self):
        """测试截断JSON结果处理"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "截断的JSON",
          "violations": [
        """
        result = self.parser.process_result(text, "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(result.is_pass, False)
        self.assertEqual(result.reason, "截断的JSON")
        self.assertEqual(len(result.violations), 0)
        
    def test_process_result_invalid_json(self):
        """测试无效JSON结果处理"""
        text = "这不是一个有效的JSON"
        result = self.parser.process_result(text, "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(result.is_pass, False)
        self.assertIn("无法提取到JSON内容", result.reason)
        
    def test_process_result_empty_text(self):
        """测试空文本结果处理"""
        result = self.parser.process_result("", "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(result.is_pass, False)
        self.assertEqual(result.reason, "结果无法解析：无法提取到JSON内容")
        
    def test_clean_code_block(self):
        """测试代码块清理功能"""
        # 测试JSON代码块
        text = "```json\n{\"test\": \"value\"}\n```"
        result = self.parser._clean_code_block(text)
        self.assertEqual(result, '{"test": "value"}')
        
        # 测试XML代码块
        text = "```xml\n<test>value</test>\n```"
        result = self.parser._clean_code_block(text)
        self.assertEqual(result, "<test>value</test>")
        
        # 测试无标记代码块
        text = "```\nplain text\n```"
        result = self.parser._clean_code_block(text)
        self.assertEqual(result, "plain text")
        
        # 测试内联代码
        text = "`inline code`"
        result = self.parser._clean_code_block(text)
        self.assertEqual(result, "inline code")
        
    def test_looks_like_json(self):
        """测试JSON格式判断功能"""
        # 有效的JSON
        self.assertTrue(self.parser._looks_like_json('{"key": "value"}'))
        self.assertTrue(self.parser._looks_like_json('{"is_pass": true}'))
        
        # 无效的JSON
        self.assertFalse(self.parser._looks_like_json('not json'))
        self.assertFalse(self.parser._looks_like_json('{"incomplete'))
        self.assertFalse(self.parser._looks_like_json(''))
        
    def test_complex_nested_json(self):
        """测试复杂嵌套JSON解析"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "复杂嵌套测试",
          "violations": [
            {
              "rule_id": "R001",
              "rule_content": "嵌套规则",
              "location": {
                "file": "test.py",
                "line": 10,
                "column": 5,
                "details": {
                  "start": {"line": 10, "column": 5},
                  "end": {"line": 15, "column": 10}
                }
              },
              "severity": "高",
              "message": "复杂的违规信息"
            }
          ]
        }
        </FinalAnswer>
        """
        result = self.parser.process_result(text, "")
        self.assertIsInstance(result, AnalyzeLLMResult)
        self.assertEqual(len(result.violations), 1)
        violation = result.violations[0]
        self.assertEqual(violation.rule_id, "R001")
        self.assertEqual(violation.location["file"], "test.py")
        self.assertEqual(violation.location["line"], 10)
        
    def test_multiple_violations(self):
        """测试多个违规项的处理"""
        text = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "多个违规",
          "violations": [
            {
              "rule_id": "R001",
              "message": "第一个违规"
            },
            {
              "rule_id": "R002", 
              "message": "第二个违规"
            }
          ]
        }
        </FinalAnswer>
        """
        result = self.parser.process_result(text, "")
        self.assertEqual(len(result.violations), 2)
        self.assertEqual(result.violations[0].rule_id, "R001")
        self.assertEqual(result.violations[1].rule_id, "R002")

    def test_reason_field_null_handling(self):
        """测试reason字段为null时的处理"""
        # 测试用例1：reason为null，is_pass为true
        text1 = """
        <FinalAnswer>
        {
          "is_pass": true,
          "reason": null,
          "violations": []
        }
        </FinalAnswer>
        """
        result1 = self.parser.process_result(text1, "test code", "test-call-1")
        self.assertIsInstance(result1, AnalyzeLLMResult)
        self.assertTrue(result1.is_pass)
        self.assertEqual(result1.reason, "代码检查通过")
        
        # 测试用例2：reason为null，is_pass为false
        text2 = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": null,
          "violations": [
            {
              "rule_id": "STYLE001",
              "rule_content": "函数命名规范",
              "location": {"file_path": "test.py", "line": 10},
              "severity": "high",
              "message": "函数名应该使用snake_case"
            }
          ]
        }
        </FinalAnswer>
        """
        result2 = self.parser.process_result(text2, "test code", "test-call-2")
        self.assertIsInstance(result2, AnalyzeLLMResult)
        self.assertFalse(result2.is_pass)
        self.assertEqual(result2.reason, "代码检查未通过")
        self.assertEqual(len(result2.violations), 1)
        
        # 测试用例3：reason字段缺失
        text3 = """
        <FinalAnswer>
        {
          "is_pass": true,
          "violations": []
        }
        </FinalAnswer>
        """
        result3 = self.parser.process_result(text3, "test code", "test-call-3")
        self.assertIsInstance(result3, AnalyzeLLMResult)
        self.assertTrue(result3.is_pass)
        self.assertEqual(result3.reason, "代码检查通过")
        
        # 测试用例4：reason字段有值（正常情况）
        text4 = """
        <FinalAnswer>
        {
          "is_pass": false,
          "reason": "发现代码风格问题",
          "violations": []
        }
        </FinalAnswer>
        """
        result4 = self.parser.process_result(text4, "test code", "test-call-4")
        self.assertIsInstance(result4, AnalyzeLLMResult)
        self.assertFalse(result4.is_pass)
        self.assertEqual(result4.reason, "发现代码风格问题")


if __name__ == '__main__':
    unittest.main() 