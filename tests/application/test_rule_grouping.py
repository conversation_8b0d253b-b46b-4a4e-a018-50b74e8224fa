"""
规则分组策略测试

目标：验证所有分组策略在不同场景下的表现
- minimize_calls: 最小调用次数策略
- adaptive: 自适应分组策略  
- similarity: 相似度分组策略
- category: 按类别分组策略

测试覆盖：
- 不同规则数量的分组效果
- 边界条件处理
- 配置参数影响
- 分组结果验证
"""

import unittest
from pathlib import Path
from unittest.mock import Mock, patch

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.external.code_analyzer.models.element import CodeElement


class TestRuleGrouping(unittest.TestCase):
    """规则分组策略测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的规则文件路径
        self.mock_rule_file = Path("test_rules.md")
        
        # 创建测试用的规则列表
        self.test_rules = self._create_test_rules()
        
        # 创建模拟的代码元素
        self.test_element = Mock(spec=CodeElement)
        self.test_element.name = "test_function"
        self.test_element.type = "function"
        self.test_element.filepath = "test.py"
        
    def _create_test_rules(self, count: int = 10) -> list:
        """创建测试规则"""
        rules = []
        categories = ["命名规范", "代码风格", "性能优化", "安全规范"]
        severities = ["high", "medium", "low"]
        
        for i in range(count):
            category = categories[i % len(categories)]
            severity = severities[i % len(severities)]
            
            rule = CodeCheckRule(
                id=f"RULE_{i:03d}",
                name=f"测试规则{i}",
                category=[category, f"子类别{i}", f"详细分类{i}"],
                rule_value=f"这是第{i}条规则的详细内容，包含具体的检查要求和示例代码。",
                description=f"第{i}条规则的描述信息",
                enabled=True,
                severity=severity,
                file_path=str(self.mock_rule_file),
                contact_info=None,
                raw={}
            )
            rules.append(rule)
        
        return rules

    def test_minimize_calls_strategy(self):
        """测试minimize_calls策略"""
        print("\n🧪 测试minimize_calls策略")
        rule_manager = RuleManager(self.mock_rule_file)
        rule_manager._rules = self.test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=self.test_rules):
            # 场景1
            print("\n📋 场景1：规则数量 < min_size")
            small_rules = self._create_test_rules(3)
            rule_manager._rules = small_rules
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                mock_group.return_value = {"group1": small_rules}
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(len(result), 1)
                self.assertEqual(len(result["group1"]), 3)
            # 场景2
            print("\n📋 场景2：规则数量 = min_size")
            min_size_rules = self._create_test_rules(5)
            rule_manager._rules = min_size_rules
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                mock_group.return_value = {"group1": min_size_rules}
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(len(result), 1)
            # 场景3
            print("\n📋 场景3：规则数量 > max_size")
            large_rules = self._create_test_rules(20)
            rule_manager._rules = large_rules
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                group1 = large_rules[:7]
                group2 = large_rules[7:14]
                group3 = large_rules[14:]
                mock_group.return_value = {"group1": group1, "group2": group2, "group3": group3}
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(len(result), 3)
                self.assertGreater(len(result["group1"]), 0)
                self.assertGreater(len(result["group2"]), 0)
                self.assertGreater(len(result["group3"]), 0)

    def test_adaptive_strategy(self):
        print("\n🧪 测试adaptive策略")
        rule_manager = RuleManager(self.mock_rule_file)
        rule_manager._rules = self.test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=self.test_rules):
            print("\n📋 场景1：自适应分组")
            adaptive_rules = self._create_test_rules(15)
            rule_manager._rules = adaptive_rules
            with patch.object(rule_manager, 'group_rules_adaptive_for_list') as mock_group:
                mock_group.return_value = {
                    "命名规范": adaptive_rules[:4],
                    "代码风格": adaptive_rules[4:8],
                    "性能优化": adaptive_rules[8:12],
                    "安全规范": adaptive_rules[12:]
                }
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='adaptive')
                self.assertEqual(len(result), 4)
                for group_name, rules in result.items():
                    self.assertGreater(len(rules), 0)
                    self.assertLessEqual(len(rules), 8)

    def test_similarity_strategy(self):
        print("\n🧪 测试similarity策略")
        rule_manager = RuleManager(self.mock_rule_file)
        rule_manager._rules = self.test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=self.test_rules):
            print("\n📋 场景1：相似度分组")
            similarity_rules = self._create_test_rules(12)
            rule_manager._rules = similarity_rules
            with patch.object(rule_manager, 'group_rules_by_similarity_for_list') as mock_group:
                mock_group.return_value = {
                    "similar_group_1": similarity_rules[:3],
                    "similar_group_2": similarity_rules[3:6],
                    "similar_group_3": similarity_rules[6:9],
                    "similar_group_4": similarity_rules[9:]
                }
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='similarity')
                self.assertEqual(len(result), 4)
                for group_name, rules in result.items():
                    self.assertGreater(len(rules), 0)

    def test_category_strategy(self):
        print("\n🧪 测试category策略")
        rule_manager = RuleManager(self.mock_rule_file)
        rule_manager._rules = self.test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=self.test_rules):
            print("\n📋 场景1：按类别分组")
            category_rules = self._create_test_rules(16)
            rule_manager._rules = category_rules
            with patch.object(rule_manager, 'group_rules_by_category_for_list') as mock_group:
                mock_group.return_value = {
                    ("命名规范",): category_rules[:4],
                    ("代码风格",): category_rules[4:8],
                    ("性能优化",): category_rules[8:12],
                    ("安全规范",): category_rules[12:]
                }
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='category')
                self.assertEqual(len(result), 4)
                for group_name, rules in result.items():
                    self.assertGreater(len(rules), 0)

    def test_edge_cases(self):
        print("\n🧪 测试边界情况")
        rule_manager = RuleManager(self.mock_rule_file)
        with patch.object(rule_manager, 'load_rules', return_value=[]):
            print("\n📋 场景1：空规则列表")
            rule_manager._rules = []
            rule_manager._loaded = True
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                mock_group.return_value = {}
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(sum(len(rules) for rules in result.values()), 0)
        with patch.object(rule_manager, 'load_rules', return_value=self.test_rules):
            print("\n📋 场景2：单条规则")
            single_rule = self._create_test_rules(1)
            rule_manager._rules = single_rule
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                mock_group.return_value = {"group1": single_rule}
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(len(result), 1)
                self.assertEqual(len(result["group1"]), 1)
            print("\n📋 场景3：大量规则")
            many_rules = self._create_test_rules(100)
            rule_manager._rules = many_rules
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                groups = {}
                group_size = 10
                for i in range(0, len(many_rules), group_size):
                    group_name = f"group{i//group_size + 1}"
                    groups[group_name] = many_rules[i:i+group_size]
                mock_group.return_value = groups
                rule_manager._grouping_cache.clear()
                result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(len(result), 10)
                for group_name, rules in result.items():
                    self.assertGreater(len(rules), 0)

    def test_configuration_impact(self):
        print("\n🧪 测试配置参数影响")
        rule_manager = RuleManager(self.mock_rule_file)
        test_rules = self._create_test_rules(20)
        rule_manager._rules = test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=test_rules):
            print("\n📋 场景1：不同的min_size配置")
            with patch('gate_keeper.application.service.rule.rule_service.manager._get_config') as mock_config:
                config1 = Mock()
                config1.rule_grouping_strategy = 'minimize_calls'
                config1.min_rule_group_size = 5
                config1.max_rule_group_size = 10
                config1.target_rule_group_size = 7
                mock_config.return_value = config1
                with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                    mock_group.return_value = {
                        "group1": test_rules[:7],
                        "group2": test_rules[7:14],
                        "group3": test_rules[14:]
                    }
                    rule_manager._grouping_cache.clear()
                    result1 = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                    self.assertEqual(result1, {
                        "group1": test_rules[:7],
                        "group2": test_rules[7:14],
                        "group3": test_rules[14:]
                    })

    def test_grouping_cache(self):
        print("\n🧪 测试分组缓存功能")
        rule_manager = RuleManager(self.mock_rule_file)
        test_rules = self._create_test_rules(10)
        rule_manager._rules = test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=test_rules):
            with patch.object(rule_manager, 'group_rules_minimize_calls') as mock_group:
                mock_group.return_value = {"group1": test_rules}
                rule_manager._grouping_cache.clear()
                result1 = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                rule_manager._grouping_cache.clear()
                result2 = rule_manager.get_applicable_rules(self.test_element, grouping_strategy='minimize_calls')
                self.assertEqual(mock_group.call_count, 2)
                self.assertEqual(result1, result2)

    def test_different_strategies_comparison(self):
        print("\n🧪 测试不同策略对比")
        rule_manager = RuleManager(self.mock_rule_file)
        test_rules = self._create_test_rules(15)
        rule_manager._rules = test_rules
        rule_manager._loaded = True
        with patch.object(rule_manager, 'load_rules', return_value=test_rules):
            strategies = [
                ('minimize_calls', 'group_rules_minimize_calls'),
                ('adaptive', 'group_rules_adaptive_for_list'),
                ('similarity', 'group_rules_by_similarity_for_list'),
                ('category', 'group_rules_by_category_for_list'),
            ]
            results = {}
            for strategy, method_name in strategies:
                print(f"\n📋 测试策略: {strategy}")
                with patch.object(rule_manager, method_name) as mock_group:
                    if strategy == 'minimize_calls':
                        mock_group.return_value = {
                            "group1": test_rules[:8],
                            "group2": test_rules[8:]
                        }
                    elif strategy == 'adaptive':
                        mock_group.return_value = {
                            "命名规范": test_rules[:4],
                            "代码风格": test_rules[4:8],
                            "性能优化": test_rules[8:12],
                            "安全规范": test_rules[12:]
                        }
                    elif strategy == 'similarity':
                        mock_group.return_value = {
                            "similar_1": test_rules[:5],
                            "similar_2": test_rules[5:10],
                            "similar_3": test_rules[10:]
                        }
                    elif strategy == 'category':
                        mock_group.return_value = {
                            ("命名规范",): test_rules[:4],
                            ("代码风格",): test_rules[4:8],
                            ("性能优化",): test_rules[8:12],
                            ("安全规范",): test_rules[12:]
                        }
                    rule_manager._grouping_cache.clear()
                    result = rule_manager.get_applicable_rules(self.test_element, grouping_strategy=strategy)
                    results[strategy] = result
                    self.assertGreater(len(result), 0)
                    total_rules = sum(len(rules) for rules in result.values())
                    self.assertEqual(total_rules, len(test_rules))
            group_counts = {strategy: len(result) for strategy, result in results.items()}
            print(f"\n📊 不同策略的分组数量对比:")
            for strategy, count in group_counts.items():
                print(f"  {strategy}: {count} 组")
            self.assertNotEqual(group_counts['minimize_calls'], group_counts['category'])


if __name__ == '__main__':
    unittest.main() 