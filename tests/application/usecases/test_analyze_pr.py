import unittest
from unittest.mock import MagicMock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.usecases.analyze import (AnalyzeBranchUseCase,
                                                      CheckMRResult)


class TestAnalyzeBranchUseCase(unittest.TestCase):
    @patch('gate_keeper.application.usecases.analyze.determine_language_by_filename')
    def test_execute_basic(self, mock_determine_language):
        # mock git_service, ast_service, llm_service
        git_service_mock = MagicMock()
        ast_service_mock = MagicMock()
        llm_service_mock = MagicMock()

        # 创建 mock repo_analyzer
        repo_analyzer_mock = MagicMock()

        # 实例化 usecase
        analyze_usecase = AnalyzeBranchUseCase(
            git_service=git_service_mock,
            llm_service=llm_service_mock,
            repo_analyzer=repo_analyzer_mock,
        )

        # 模拟 get_all_functions 返回值（AnalyzeBranchUseCase实际调用的方法）
        repo_analyzer_mock.get_all_functions.return_value = [
            AffectedFunction(
                name='func1',
                start_line=1,
                end_line=10,
                changed_lines=[2, 5],
                code='def func1(): pass',
                filepath='some/file.py'
            ),
            AffectedFunction(
                name='func2',
                start_line=11,
                end_line=20,
                changed_lines=[15],
                code='def func2(): pass',
                filepath='some/file.py'
            ),
        ]

        # 模拟 git_service.get_file_content_by_ref 返回文件内容
        git_service_mock.get_file_content_by_ref.return_value = 'file content here'

        # 模拟 analyze_mr 返回值
        analyze_usecase.analyze_mr = MagicMock(return_value='analyze_mr_result')

        # 执行 usecase
        result = analyze_usecase.execute('repo_dir', 'main')

        # 校验调用行为 - 修正为实际调用的方法
        git_service_mock.keep_branch_update_if_needed.assert_called_once_with('repo_dir', 'main')
        repo_analyzer_mock.get_all_functions.assert_called_once_with(
            repo_dir='repo_dir',
            branch='main',
            commit_sha=None,
            file_pattern="*.c"
        )

        # 校验返回值与 analyze_mr 参数
        self.assertEqual(result, 'analyze_mr_result')
        analyze_usecase.analyze_mr.assert_called_once()
        args, _ = analyze_usecase.analyze_mr.call_args
        mr_result = args[0]
        self.assertIsInstance(mr_result, CheckMRResult)
        self.assertEqual(len(mr_result.diffs), 1)  # 同一文件的函数会被合并到一个diff中
        # 验证diff
        diff = mr_result.diffs[0]
        self.assertEqual(diff.filepath, 'some/file.py')
        self.assertEqual(diff.new_file_content, 'file content here')
        self.assertEqual(len(diff.affected_functions), 2)  # 包含两个函数
        self.assertEqual(diff.affected_functions[0].name, 'func1')
        self.assertEqual(diff.affected_functions[1].name, 'func2')
        self.assertEqual(len(diff.affected_functions), 2)
        self.assertEqual(diff.affected_functions[0].name, 'func1')
        self.assertEqual(diff.affected_functions[1].name, 'func2')
        self.assertEqual(diff.file_status, 'unchanged')

if __name__ == '__main__':
    unittest.main()
