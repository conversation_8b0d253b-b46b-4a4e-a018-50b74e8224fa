"""
配置驱动规则分组功能测试

目标：验证LLMService中基于配置的规则分组功能，确保能够根据配置参数正确选择和执行分组策略。

测试覆盖：
- 配置驱动的分组策略选择
- 不同分组策略的参数传递
- 配置缺失时的默认行为
- 分组结果的正确性验证
- 并发环境下的分组安全性

验证重点：
- 配置参数正确读取和使用
- 分组策略正确切换
- 分组结果符合预期
- 错误配置的降级处理
"""

import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.service.rule import RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.client.base import LLMClient


class MockLLMClient(LLMClient):
    """模拟LLM客户端，用于测试"""
    
    def __init__(self):
        self.call_count = 0
        
    def generate(self, prompt, parameters, token=None):
        """返回模拟的结构化响应"""
        self.call_count += 1
        return '<Finalanswer>{"is_pass": true, "reason": "测试通过", "violations": []}</Finalanswer>'
    
    def get_config(self):
        return {"provider": "Mock"}


class TestRuleGroupingConfig(unittest.TestCase):
    """
    测试配置驱动的规则分组功能
    
    验证目标：
    1. 能根据配置正确选择分组策略
    2. 不同策略的参数正确传递
    3. 配置缺失时有合理默认值
    4. 分组结果符合配置要求
    """
    
    def setUp(self):
        """设置测试环境"""
        self.mock_client = MockLLMClient()
        self.llm_service = LLMService(self.mock_client)
        
        # 准备测试规则
        self.test_rules = [
            CodeCheckRule(
                id="R1.1",
                name="变量命名规范",
                category=["命名规范", "变量"],
                description="变量名应使用小写字母和下划线",
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="R1.2", 
                name="函数命名规范",
                category=["命名规范", "函数"],
                description="函数名应使用小写字母和下划线",
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="R2.1",
                name="函数参数数量",
                category=["代码质量", "函数"],
                description="函数参数不应超过5个",
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="R2.2",
                name="函数长度限制",
                category=["代码质量", "函数"],
                description="函数不应超过50行",
                enabled=True,
                languages=["python"]
            ),
            CodeCheckRule(
                id="R3.1",
                name="内存释放检查",
                category=["安全规范"],
                description="动态分配的内存必须释放",
                enabled=True,
                languages=["c", "cpp"]
            )
        ]
        
        # 准备测试函数
        self.test_function = AffectedFunction(
            name="test_function",
            start_line=1,
            end_line=10,
            changed_lines=[2, 5],
            code="def test_function(a, b):\n    return a + b",
            filepath="test.py",
            related_calls=[],
            related_definitions=[],
            llm_results=[]
        )
    
    def setup_basic_config(self, mock_config):
        """设置基础配置"""
        mock_config.rule_file_path = "test_rules.md"
        mock_config.rule_merge_on = None
        mock_config.llm_model_id = "test-model"
        mock_config.DEBUG = False

    @patch('gate_keeper.config.config')
    def test_grouping_strategy_category(self, mock_config):
        self.setup_basic_config(mock_config)
        mock_config.rule_grouping_strategy = "category"
        mock_config.rule_similarity_threshold = 0.8
        mock_config.min_rule_group_size = 2
        mock_config.max_rule_group_size = 4
        mock_config.target_rule_group_size = 3
        with patch.object(RuleManager, 'load_rules') as mock_load_rules:
            mock_load_rules.return_value = self.test_rules
            with patch.object(RuleManager, 'group_rules_by_category_for_list') as mock_group_category:
                mock_group_category.return_value = {
                    ("命名规范", "变量"): [self.test_rules[0]],
                    ("命名规范", "函数"): [self.test_rules[1]],
                    ("代码质量", "函数"): [self.test_rules[2], self.test_rules[3]],
                    ("安全规范",): [self.test_rules[4]]
                }
                rule_manager = RuleManager(rule_file_path="dummy.md")
                # 直接测试分组方法
                result = rule_manager.get_applicable_rules(self.test_function)
                mock_group_category.assert_called_once()
                self.assertEqual(sum(len(v) for v in result.values()), 5)

    @patch('gate_keeper.config.config')
    def test_grouping_strategy_similarity(self, mock_config):
        self.setup_basic_config(mock_config)
        mock_config.rule_grouping_strategy = "similarity"
        mock_config.rule_similarity_threshold = 0.8
        mock_config.min_rule_group_size = 2
        mock_config.max_rule_group_size = 4
        mock_config.target_rule_group_size = 3
        with patch.object(RuleManager, 'load_rules') as mock_load_rules:
            mock_load_rules.return_value = self.test_rules
            with patch.object(RuleManager, 'group_rules_by_similarity_for_list') as mock_group_similarity:
                mock_group_similarity.return_value = {
                    "similarity_group_0": [self.test_rules[0], self.test_rules[1]],
                    "similarity_group_1": [self.test_rules[2]],
                    "similarity_group_2": [self.test_rules[3], self.test_rules[4]]
                }
                rule_manager = RuleManager(rule_file_path="dummy.md")
                result = rule_manager.get_applicable_rules(self.test_function)
                mock_group_similarity.assert_called_once_with(self.test_rules, 0.8)
                self.assertEqual(sum(len(v) for v in result.values()), 5)

    @patch('gate_keeper.config.config')
    def test_grouping_strategy_adaptive(self, mock_config):
        self.setup_basic_config(mock_config)
        mock_config.rule_grouping_strategy = "adaptive"
        mock_config.rule_similarity_threshold = 0.8
        mock_config.min_rule_group_size = 2
        mock_config.max_rule_group_size = 4
        mock_config.target_rule_group_size = 3
        with patch.object(RuleManager, 'load_rules') as mock_load_rules:
            mock_load_rules.return_value = self.test_rules
            with patch.object(RuleManager, 'group_rules_adaptive_for_list') as mock_group_adaptive:
                mock_group_adaptive.return_value = {
                    "adaptive_group_0": [self.test_rules[0], self.test_rules[1], self.test_rules[2]],
                    "adaptive_group_1": [self.test_rules[3], self.test_rules[4]]
                }
                rule_manager = RuleManager(rule_file_path="dummy.md")
                result = rule_manager.get_applicable_rules(self.test_function)
                mock_group_adaptive.assert_called_once_with(self.test_rules, 2, 4, 3)
                self.assertEqual(sum(len(v) for v in result.values()), 5)


if __name__ == '__main__':
    unittest.main() 