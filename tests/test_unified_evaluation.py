"""
统一评估系统测试

测试重构后的统一评估系统功能
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from eval.evaluation_coordinator import EvaluationCoordinator
from eval.models import EvaluationMetrics
from gate_keeper.domain.rule.check_rule import CodeCheckRule, GitInfo, TestCase
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.shared.log import app_logger as logger


class TestUnifiedEvaluation(unittest.TestCase):
    """统一评估系统测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的服务编排器
        self.mock_orchestrator = Mock()
        self.mock_git_service = Mock()
        self.mock_llm_service = Mock()
        self.mock_repo_analyzer = Mock()
        self.mock_rule_manager = Mock()
        
        # 设置服务编排器的_services属性
        self.mock_orchestrator._services = {
            "git_service": self.mock_git_service,
            "llm_service": self.mock_llm_service,
            "code_analyzer": self.mock_repo_analyzer,
            "rule_manager": self.mock_rule_manager
        }
        
        # 设置服务编排器返回的服务
        self.mock_orchestrator.get_service.side_effect = lambda service_name: {
            "git_service": self.mock_git_service,
            "llm_service": self.mock_llm_service,
            "code_analyzer": self.mock_repo_analyzer,
            "rule_manager": self.mock_rule_manager
        }.get(service_name)
        
        # 创建评估服务
        self.evaluation_service = EvaluationCoordinator(self.mock_orchestrator)
        
        # 创建测试用例
        self.test_cases = self._create_test_cases()
    
    def _create_test_cases(self):
        """创建测试用例"""
        return [
            TestCase(
                id="TC_001",
                name="测试规则1",
                description="测试用规则",
                category=["测试"],
                enabled=True,
                case_name="正面示例1",
                expected_answer="通过",
                code="void test_function() { }",
                type="positive"
            ),
            TestCase(
                id="TC_002",
                name="测试规则2",
                description="测试用规则",
                category=["测试"],
                enabled=True,
                case_name="负面示例1",
                expected_answer="不通过",
                code="void bad_function() { }",
                type="negative"
            )
        ]
    
    def test_evaluation_service_initialization(self):
        """测试评估服务初始化"""
        self.assertIsNotNone(self.evaluation_service.orchestrator)
        self.assertIsNotNone(self.evaluation_service.git_service)
        self.assertIsNotNone(self.evaluation_service.llm_service)
        self.assertIsNotNone(self.evaluation_service.code_analyzer)
    
    def test_convert_test_case_to_check_rule(self):
        """测试TestCase转换为CodeCheckRule"""
        test_case = self.test_cases[0]
        check_rule = test_case.to_check_rule()
        
        self.assertEqual(check_rule.id, test_case.id)
        self.assertEqual(check_rule.name, test_case.name)
        self.assertEqual(check_rule.description, test_case.description)
        self.assertEqual(check_rule.category, test_case.category)
        self.assertEqual(check_rule.enabled, test_case.enabled)
    
    def test_create_test_case_from_check_rule(self):
        """测试从CodeCheckRule创建TestCase"""
        check_rule = CodeCheckRule(
            id="R001",
            name="测试规则",
            description="测试用规则",
            category=["测试"],
            enabled=True
        )
        
        test_case = TestCase.from_check_rule(
            check_rule,
            case_name="测试用例",
            expected_answer="通过",
            code="void test() { }",
            type="positive"
        )
        
        self.assertEqual(test_case.id, check_rule.id)
        self.assertEqual(test_case.name, check_rule.name)
        self.assertEqual(test_case.case_name, "测试用例")
        self.assertEqual(test_case.expected_answer, "通过")
        self.assertEqual(test_case.code, "void test() { }")
        self.assertEqual(test_case.type, "positive")
    
    def test_test_case_with_git_info(self):
        """测试带Git信息的TestCase"""
        git_info = GitInfo(
            repo_url="https://github.com/test/repo",
            branch="main",
            commit="abc123",
            file_path="test.c",
            function_name="test_function"
        )
        
        test_case = TestCase(
            id="TC_003",
            name="测试规则3",
            description="测试用规则",
            category=["测试"],
            enabled=True,
            case_name="Git示例",
            expected_answer="通过",
            code="void test_function() { }",
            git=git_info,
            type="positive"
        )
        
        self.assertIsNotNone(test_case.git)
        self.assertEqual(test_case.git.repo_url, "https://github.com/test/repo")
        self.assertEqual(test_case.git.branch, "main")
        self.assertEqual(test_case.git.file_path, "test.c")
    
    @patch('eval.evaluation_coordinator.AnalyzeMRUseCase')
    def test_evaluate_mr_mode(self, mock_analyze_mr_usecase_class):
        """测试MR模式评估"""
        # 模拟AnalyzeMRUseCase的execute方法
        mock_usecase = Mock()
        mock_result = Mock()
        mock_result.diffs = []
        mock_usecase.execute.return_value = mock_result
        mock_analyze_mr_usecase_class.return_value = mock_usecase

        # 执行测试
        metrics, results = self.evaluation_service.evaluate_mr_mode(
            self.test_cases, "/test/repo", 123, "master", "develop"
        )

        # 验证调用
        mock_usecase.execute.assert_called_once()
        self.assertIsInstance(metrics, EvaluationMetrics)
        self.assertEqual(len(results), 0)  # 没有diff，所以没有结果
    
    @patch('eval.evaluation_coordinator.AnalyzeBranchUseCase')
    def test_evaluate_branch_mode(self, mock_analyze_branch_usecase_class):
        """测试Branch模式评估"""
        # 模拟AnalyzeBranchUseCase的execute方法
        mock_usecase = Mock()
        mock_result = Mock()
        mock_result.diffs = []
        mock_usecase.execute.return_value = mock_result
        mock_analyze_branch_usecase_class.return_value = mock_usecase

        # 执行测试
        metrics, results = self.evaluation_service.evaluate_branch_mode(
            self.test_cases, "/test/repo", "develop"
        )

        # 验证调用
        mock_usecase.execute.assert_called_once()
        self.assertIsInstance(metrics, EvaluationMetrics)
        self.assertEqual(len(results), 0)  # 没有diff，所以没有结果
    
    @patch('eval.evaluation_coordinator.EvalUseCase')
    def test_evaluate_dataset_mode(self, mock_eval_usecase_class):
        """测试数据集模式评估"""
        # 模拟EvalUseCase的execute方法
        mock_usecase = Mock()
        mock_results = [
            AnalyzeLLMResult(
                is_pass=True,
                reason="检查通过",
                violations=[],
                code="void test_function() { }",
                test_case_info={"rule_id": "TC_001", "rule_name": "测试规则1"}
            )
        ]
        mock_usecase.execute.return_value = mock_results
        mock_eval_usecase_class.return_value = mock_usecase

        # 执行测试
        metrics, results = self.evaluation_service.evaluate_dataset_mode(
            self.test_cases, "/test/repo", "master"
        )

        # 验证调用
        mock_usecase.execute.assert_called_once()
        self.assertIsInstance(metrics, EvaluationMetrics)
        self.assertEqual(len(results), 1)
        # 验证test_case_info中的rule_id
        self.assertEqual(results[0].test_case_info["rule_id"], "TC_001")
    
    def test_extract_llm_results_from_check_mr_result(self):
        """测试从CheckMRResult中提取LLM分析结果"""
        # 创建模拟的CheckMRResult
        mock_check_mr_result = Mock()
        mock_diff = Mock()
        mock_affected_function = Mock()
        mock_llm_result = AnalyzeLLMResult(
            is_pass=True,
            reason="测试通过",
            violations=[],
            code="void test() { }"
        )
        mock_affected_function.llm_results = [mock_llm_result]
        mock_diff.affected_functions = [mock_affected_function]
        mock_check_mr_result.diffs = [mock_diff]

        # 执行测试
        results = self.evaluation_service._extract_llm_results_from_check_mr_result(mock_check_mr_result)

        # 验证结果
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], mock_llm_result)


if __name__ == "__main__":
    unittest.main()