# Git Keeper 测试套件

这里包含Git Keeper项目的完整测试套件，确保代码质量和功能稳定性。

## 📊 最新测试状态

- **总测试数量**: 159个
- **通过率**: **100%** (159/159)
- **执行时间**: 约4分钟

## 🚀 快速运行测试

```bash
# 运行所有测试（推荐）
python -m pytest tests/ -v --tb=short

# 快速测试（排除集成测试）
python -m pytest tests/ -m "not integration" -v

# 带覆盖率的测试
python -m pytest tests/ --cov=gate_keeper --cov-report=html
```

## 📁 测试目录结构

```
tests/
├── application/          # 应用层测试
├── infrastructure/       # 基础设施层测试
│   ├── gitee/            # Gitee相关基础设施测试
│   │   └── test_gitee_client.py
│   ├── ollama/           # Ollama相关基础设施测试
│   │   ├── test_ollama.py
│   │   └── test_ollama_client.py
│   ├── huawei/           # 华为相关基础设施测试（已被pytest忽略）
│   │   ├── test_codehub_client_huawei.py
│   │   ├── test_git_client_factory_huawei.py
│   │   ├── test_platform_support_huawei.py
│   │   └── test_fuyao_llm_huawei.py
│   ├── test_ast_parser.py
│   ├── test_repository_index_infrastructure.py
│   ├── test_git_clients.py
│   ├── test_git_client_factory.py
│   └── test_git_client_factory_common.py
├── integration/          # 集成测试
│   ├── gitee/            # Gitee相关集成测试
│   │   ├── test_gitee_integration.py
│   │   └── test_gitee_mr_detection_deduplication.py
│   ├── ollama/           # Ollama相关集成测试
│   │   └── test_ollama_gitee_workflow.py
│   ├── huawei/           # 华为相关集成测试（已被pytest忽略）
│   │   └── test_codehub_integration_huawei.py
│   ├── test_rule_grouping_end_to_end.py
│   ├── test_config.py
│   └── test_external_mr.py
├── shared/               # 共享组件测试
├── examples/             # 测试示例和配置
├── COMPONENT_TESTING_GUIDE.md  # 测试指导原则与实践指南
├── TESTING_BEST_PRACTICES.md   # 测试最佳实践总结
└── run_tests_by_environment.py
```

> **说明：**
> - Gitee、Ollama、Huawei等特定基础设施/平台的测试已独立到对应子目录。
> - 当前所有`huawei`相关测试已被pytest配置忽略，不会被自动执行。
> - 详细测试指导请参考：[测试指导原则](./COMPONENT_TESTING_GUIDE.md) 和 [测试最佳实践](./TESTING_BEST_PRACTICES.md)

## 🏗️ 测试组件分离架构

为了支持不同环境的组件需求，测试已按组件和组合进行分离：

### 组件分类
- **Gitee**: `tests/infrastructure/gitee/`, `tests/integration/gitee/`
- **Ollama**: `tests/infrastructure/ollama/`, `tests/integration/ollama/`
- **Huawei**: `tests/infrastructure/huawei/`, `tests/integration/huawei/`（已被pytest忽略）

### 组合集成测试
- **Ollama + Gitee**: `tests/integration/ollama/`
- **其它组合**: 可扩展

### 环境组件支持
- **dev环境**: 支持 CodeHub + Fuyao，排除 Ollama + Gitee
- **home环境**: 支持 Ollama + Gitee，排除 CodeHub + Fuyao  
- **test环境**: 支持 Ollama + Gitee（默认测试组合）

### 按环境运行测试

```bash
# dev环境：只运行CodeHub + Fuyao相关测试
python tests/run_tests_by_environment.py --env dev

# home环境：只运行Ollama + Gitee相关测试  
python tests/run_tests_by_environment.py --env home

# test环境：运行兼容的测试（默认）
python tests/run_tests_by_environment.py --env test

# 查看将要执行的命令（不实际运行）
python tests/run_tests_by_environment.py --env dev --dry-run
```

## 🏷️ 测试标记

我们使用pytest标记来分类测试，支持环境过滤：

### 组件标记
- `@pytest.mark.ollama` - Ollama LLM相关测试
- `@pytest.mark.gitee` - Gitee Git相关测试
- `@pytest.mark.huawei` - Huawei相关测试（已被pytest忽略）

### 功能标记
- `@pytest.mark.integration` - 集成测试
- `@pytest.mark.deduplication` - 去重功能测试
- `@pytest.mark.performance` - 性能测试
- `@pytest.mark.error_handling` - 错误处理测试

### 按标记运行测试

```bash
# 按组件运行测试
python -m pytest -m "ollama" tests/ -v          # Ollama相关测试
python -m pytest -m "gitee" tests/ -v           # Gitee相关测试
python -m pytest -m "huawei" tests/ -v          # Huawei相关测试（需手动移除pytest.ini中的ignore后才会生效）

# 按组合运行测试
python -m pytest -m "ollama and gitee" tests/ -v    # Ollama + Gitee组合

# 按环境排除测试
python -m pytest -m "not ollama and not gitee" tests/ -v    # 排除Ollama和Gitee（dev环境）

# 按功能运行测试
python -m pytest -m "deduplication" tests/ -v   # 去重功能测试
python -m pytest -m "not integration" tests/ -v # 排除集成测试（快速验证）
```

> **注意：**
> - 若需运行Huawei相关测试，请手动修改pytest.ini，移除`--ignore=tests/integration/huawei --ignore=tests/infrastructure/huawei`。

## ⚡ 性能优化

### 并行测试

```bash
# 安装并行插件
pip install pytest-xdist

# 并行运行测试
python -m pytest tests/ -n auto
```

### 测试选择

```bash
# 只运行失败的测试
python -m pytest --lf tests/

# 在第一个失败处停止
python -m pytest -x tests/

# 运行特定测试文件
python -m pytest tests/application/test_report_service.py -v
```

## 🔧 开发工作流

### 1. 日常开发

```bash
# 修改代码后快速验证
python -m pytest tests/ -m "not integration" -x --tb=short
```

### 2. 提交前检查

```bash
# 运行完整测试套件
python -m pytest tests/ -v --tb=short
```

### 3. 发布前验证

```bash
# 生成覆盖率报告
python -m pytest tests/ --cov=gate_keeper --cov-report=html
open htmlcov/index.html
```

## 🐛 调试测试

```bash
# 详细错误信息
python -m pytest tests/ -v --tb=long

# 进入调试器
python -m pytest tests/ --pdb

# 捕获输出
python -m pytest tests/ -s
```

## 📈 测试覆盖率

当前测试覆盖了以下关键功能：

### 应用层 (48个测试)
- ✅ LLM服务：分析、并发处理、错误处理
- ✅ 报告服务：Markdown生成、评论发布、去重
- ✅ 规则服务：规则加载、分组、缓存
- ✅ 仓库索引：函数提取、调用图构建、序列化

### 基础设施层 (60个测试)
- ✅ AST解析：Python/C代码解析、函数提取、调用关系
- ✅ Git客户端：Gitee/CodeHub集成、URL解析
- ✅ LLM客户端：Ollama集成、流式处理、配置管理
- ✅ 文档读取：Markdown/Excel规则解析

### 集成测试 (44个测试)
- ✅ Gitee完整工作流：MR检测、评论发布、去重
- ✅ MR检测：代码分析、报告生成、批量处理
- ✅ 错误处理：网络异常、API限流、数据一致性
- ✅ 性能测试：大量数据处理、并发场景

## 📚 更多文档

- [完整测试规范](../docs/testing.md) - 详细的测试策略和规范
- [测试最佳实践](../docs/testing.md#测试最佳实践) - 编写和维护测试的指南
- [CI/CD集成](../docs/testing.md#持续集成配置) - 持续集成配置
- [跨平台兼容性](../docs/testing/cross-platform-compatibility.md) - 跨平台测试兼容性说明

## ❓ 常见问题

**Q: 测试运行时间太长怎么办？**
A: 使用 `-m "not integration"` 跳过集成测试，或使用 `-n auto` 并行运行。

**Q: 如何只运行特定模块的测试？**
A: 使用 `python -m pytest tests/application/ -v` 运行应用层测试。

**Q: 测试失败如何调试？**
A: 使用 `--tb=long` 查看详细错误，或使用 `--pdb` 进入调试器。

**Q: 如何查看测试覆盖率？**
A: 运行 `python -m pytest tests/ --cov=gate_keeper --cov-report=html`，然后打开 `htmlcov/index.html`。

**Q: 测试在Windows和macOS上结果不一致怎么办？**
A: 我们已实现跨平台路径处理，确保路径分隔符统一为 `/`。如果仍有问题，请检查是否使用了平台特定的绝对路径。

---

🎯 **目标**: 保持100%测试通过率，确保代码质量和功能稳定性！

# 测试环境配置指南

## 默认测试配置

所有测试默认使用 `config_test.py` 配置文件，这是通过项目根目录的 `conftest.py` 设置的：

```python
# conftest.py 自动设置
if "GK_ENV" not in os.environ:
    os.environ["GK_ENV"] = "test"
```

## 配置环境说明

### 可用的配置环境

| 环境 | 配置文件 | 用途 | Git平台 |
|------|----------|------|---------|
| `test` | `config_test.py` | **默认测试环境** | gitee |
| `home` | `config_home.py` | 本地开发环境 | codehub |
| `dev` | `config_dev.py` | 开发环境 | codehub |
| `prod` | `config_prod.py` | 生产环境 | 待配置 |

### 测试配置特点

`config_test.py` 的主要特点：
- `git_platform="gitee"` - 适合测试
- `ollama_token="test_ollama_token"` - 测试专用token
- `max_diff_results=10` - 适中的测试数据量
- `rule_file_url="resource/编码规范_python.md"` - 本地规范文件

## 如何在特定测试中覆盖配置

### 方法1：环境变量覆盖（推荐）

如果需要在特定测试中使用不同的配置环境：

```python
import os
import pytest

class TestWithSpecificConfig:
    @pytest.fixture(autouse=True)
    def setup_env(self):
        # 保存原始环境
        original_env = os.environ.get("GK_ENV")
        
        # 设置特定环境
        os.environ["GK_ENV"] = "home"  # 或 "dev", "prod"
        
        # 重新导入配置以应用新环境
        import importlib
        from gate_keeper.config import config
        importlib.reload(config)
        
        yield
        
        # 恢复原始环境
        if original_env:
            os.environ["GK_ENV"] = original_env
        else:
            os.environ.pop("GK_ENV", None)
```

### 方法2：pytest标记配置

为特定类型的测试定义标记：

```python
# 在测试文件中
@pytest.mark.production
def test_production_workflow():
    """需要生产环境配置的测试"""
    pass

@pytest.mark.integration_dev
def test_dev_integration():
    """需要开发环境配置的集成测试"""
    pass
```

然后可以通过命令行指定环境：

```bash
# 运行生产环境测试
GK_ENV=prod python -m pytest -m production

# 运行开发环境集成测试  
GK_ENV=dev python -m pytest -m integration_dev
```

### 方法3：测试类级别的配置覆盖

```python
class TestProductionScenario:
    @classmethod
    def setup_class(cls):
        """类级别的环境设置"""
        os.environ["GK_ENV"] = "prod"
        
        # 重新加载配置
        import importlib
        from gate_keeper.config import config
        importlib.reload(config)
        
        cls.original_config = config
    
    @classmethod
    def teardown_class(cls):
        """恢复默认环境"""
        os.environ["GK_ENV"] = "test"
```

## 常用测试命令

```bash
# 运行所有测试（使用默认test配置）
python -m pytest

# 运行特定环境的测试
GK_ENV=home python -m pytest tests/integration/
GK_ENV=dev python -m pytest tests/application/

# 运行特定标记的测试
python -m pytest -m "integration and gitee"
python -m pytest -m "not integration"

# 检查当前使用的配置
python -c "from gate_keeper.config import config; print('Git platform:', config.git_platform)"
```

## 配置验证

在测试中验证配置是否正确：

```python
def test_config_verification():
    """验证测试配置是否正确加载"""
    from gate_keeper.config import config
    
    # 验证测试环境配置
    assert config.git_platform == "gitee"
    assert config.ollama_token == "test_ollama_token"
    assert config.rule_file_url == "resource/编码规范_python.md"
```

## 最佳实践

1. **默认使用test配置**：大部分测试应该使用默认的test配置
2. **明确标记特殊需求**：如果测试需要特定配置，使用pytest标记明确标识
3. **环境隔离**：确保配置覆盖不影响其他测试
4. **文档化特殊需求**：在测试函数的docstring中说明为什么需要特定配置

## 示例

### 需要特定Git平台的测试

```python
@pytest.mark.codehub
def test_codehub_specific_feature():
    """测试CodeHub特定功能（需要codehub配置）"""
    # 这个测试需要在运行时指定：
    # GK_ENV=home python -m pytest -m codehub
    pass

@pytest.mark.gitee  
def test_gitee_integration():
    """测试Gitee集成（使用默认test配置即可）"""
    pass
```

### 需要生产配置的测试

```python
@pytest.mark.production
@pytest.mark.slow
def test_production_deployment():
    """生产环境部署测试（需要prod配置）"""
    # 运行方式：GK_ENV=prod python -m pytest -m production
    pass
``` 