"""
增强的评测集解析测试

测试评测集更新后的YAML解析功能，包括match_regex和suggestion字段
"""

import os
import tempfile
import unittest
from pathlib import Path

from eval.datasets.models import Example, Principle
from eval.datasets.yaml_dataset import YamlDatasetLoader
from gate_keeper.application.service.rule.rule_service.manager import \
    RuleManager


class TestEnhancedYamlDatasetParsing(unittest.TestCase):
    """测试增强的YAML数据集解析"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_yaml_file = os.path.join(self.temp_dir, "test_dataset.yaml")
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_yaml_file):
            os.remove(self.test_yaml_file)
        os.rmdir(self.temp_dir)
    
    def test_parse_principle_with_new_fields(self):
        """测试解析包含新字段的principle"""
        yaml_content = """
name: "测试数据集"
version: "1.0"
description: "测试增强字段的数据集"
total_rules: 1
coverage: "100%"
rule_categories:
  - name: "内存操作"
    description: "内存操作相关规则"
    rule_groups:
      - name: "内存拷贝"
        principles:
          - content: "使用memcpy时必须检查边界"
            match_regex: 'memcpy.*\\('
            suggestion: "检查所有内存拷贝操作前是否有边界校验，重点关注：1)源数据长度是否超过目标缓冲区大小；2)目标缓冲区是否有足够空间"
            pos_examples:
              - code: |
                  if (src_len <= dst_size) {
                      memcpy(dst, src, src_len);
                  }
                think: "正确检查了边界"
            neg_examples:
              - code: |
                  memcpy(dst, src, len);
                think: "未检查边界，可能溢出"
        """

        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        dataset = YamlDatasetLoader.load_from_file(self.test_yaml_file)
        categories = dataset.rule_categories

        self.assertEqual(len(categories), 1)
        category = categories[0]
        self.assertEqual(category.name, "内存操作")

        rule_group = category.rule_groups[0]
        self.assertEqual(rule_group.name, "内存拷贝")

        principle = rule_group.principles[0]
        self.assertEqual(principle.content, "使用memcpy时必须检查边界")
        self.assertEqual(principle.match_regex, "memcpy.*\\(")
        self.assertIn("边界校验", principle.suggestion)
        self.assertIn("目标缓冲区", principle.suggestion)
    
    def test_parse_principle_without_new_fields(self):
        """测试解析不包含新字段的principle（向后兼容）"""
        yaml_content = """
categories:
  - name: "样式规范"
    rule_groups:
      - name: "命名规范"
        principles:
          - content: "函数名应使用小写字母和下划线"
            pos_examples:
              - code: "void process_data() {}"
                think: "正确的函数命名"
            neg_examples:
              - code: "void ProcessData() {}"
                think: "使用了驼峰命名"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        dataset = YamlDatasetLoader.load_from_file(self.test_yaml_file)
        categories = dataset.categories
        
        principle = categories[0].rule_groups[0].principles[0]
        self.assertEqual(principle.content, "函数名应使用小写字母和下划线")
        self.assertEqual(principle.match_regex, "")  # 默认值
        self.assertEqual(principle.suggestion, "")   # 默认值
    
    def test_parse_mixed_principles(self):
        """测试解析混合的principles（有些有新字段，有些没有）"""
        yaml_content = """
categories:
  - name: "混合测试"
    rule_groups:
      - name: "混合规则"
        principles:
          - content: "新式规则"
            match_regex: "new\\s*\\("
            suggestion: "新式建议"
            pos_examples:
              - code: "new();"
                think: "新式调用"
          - content: "旧式规则"
            pos_examples:
              - code: "old();"
                think: "旧式调用"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        dataset = YamlDatasetLoader.load_from_file(self.test_yaml_file)
        categories = dataset.categories
        
        principles = categories[0].rule_groups[0].principles
        self.assertEqual(len(principles), 2)
        
        # 新式规则
        new_principle = principles[0]
        self.assertEqual(new_principle.match_regex, "new\\s*\\(")
        self.assertEqual(new_principle.suggestion, "新式建议")
        
        # 旧式规则
        old_principle = principles[1]
        self.assertEqual(old_principle.match_regex, "")
        self.assertEqual(old_principle.suggestion, "")
    
    def test_parse_complex_regex_patterns(self):
        """测试解析复杂的正则表达式模式"""
        yaml_content = """
categories:
  - name: "复杂模式"
    rule_groups:
      - name: "复杂正则"
        principles:
          - content: "复杂内存操作检查"
            match_regex: "(memcpy|strcpy|strcat)\\s*\\(.*\\)|malloc\\s*\\(.*\\)|free\\s*\\("
            suggestion: "检查复杂内存操作"
            pos_examples:
              - code: "memcpy(a, b, c);"
                think: "内存拷贝"
          - content: "数组和指针操作"
            match_regex: "\\[\\s*\\w+\\s*\\]|\\*\\s*\\w+|\\w+\\s*->\\s*\\w+"
            suggestion: "检查数组和指针"
            pos_examples:
              - code: "arr[i] = *ptr;"
                think: "数组和指针"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        dataset = YamlDatasetLoader.load_from_file(self.test_yaml_file)
        categories = dataset.categories
        
        principles = categories[0].rule_groups[0].principles
        
        # 复杂内存操作
        memory_principle = principles[0]
        self.assertIn("memcpy", memory_principle.match_regex)
        self.assertIn("malloc", memory_principle.match_regex)
        
        # 数组和指针操作
        array_principle = principles[1]
        self.assertIn("\\[", array_principle.match_regex)
        self.assertIn("\\*", array_principle.match_regex)
    
    def test_parse_multiline_suggestions(self):
        """测试解析多行建议"""
        yaml_content = """
categories:
  - name: "多行建议"
    rule_groups:
      - name: "详细建议"
        principles:
          - content: "数组访问安全检查"
            match_regex: "\\[\\s*\\w+\\s*\\]"
            suggestion: |
              检查数组访问的安全性，重点关注：
              1) 数组下标是否在有效范围内
              2) 是否检查了下标小于数组长度
              3) 动态计算的下标是否有溢出风险
              4) 循环中的数组访问边界条件
            pos_examples:
              - code: |
                  if (index >= 0 && index < array_size) {
                      value = array[index];
                  }
                think: "正确的边界检查"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        dataset = YamlDatasetLoader.load_from_file(self.test_yaml_file)
        categories = dataset.categories
        
        principle = categories[0].rule_groups[0].principles[0]
        suggestion = principle.suggestion
        
        self.assertIn("数组下标", suggestion)
        self.assertIn("边界条件", suggestion)
        self.assertIn("1)", suggestion)
        self.assertIn("4)", suggestion)


class TestEnhancedRuleManagerParsing(unittest.TestCase):
    """测试增强的规则管理器解析"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_yaml_file = os.path.join(self.temp_dir, "test_rules.yaml")
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_yaml_file):
            os.remove(self.test_yaml_file)
        os.rmdir(self.temp_dir)
    
    def test_rule_manager_parse_enhanced_yaml(self):
        """测试规则管理器解析增强的YAML"""
        yaml_content = """
categories:
  - name: "内存安全"
    rule_groups:
      - name: "内存拷贝"
        principles:
          - content: "使用memcpy前检查边界"
            match_regex: "memcpy\\s*\\("
            suggestion: "检查memcpy边界，防止缓冲区溢出"
            pos_examples:
              - code: "if (len <= size) memcpy(dst, src, len);"
                think: "有边界检查"
            neg_examples:
              - code: "memcpy(dst, src, len);"
                think: "无边界检查"
          - content: "使用malloc后检查返回值"
            match_regex: "malloc\\s*\\("
            suggestion: "检查malloc返回值，防止空指针解引用"
            pos_examples:
              - code: |
                  ptr = malloc(size);
                  if (ptr != NULL) {
                      // 使用ptr
                  }
                think: "检查了返回值"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        rule_manager = RuleManager(rule_file_path=self.test_yaml_file)
        rules = rule_manager.load_rules()
        
        # 应该有2条规则
        self.assertEqual(len(rules), 2)
        
        # 检查第一条规则
        memcpy_rule = next((r for r in rules if "memcpy" in r.rule_value), None)
        self.assertIsNotNone(memcpy_rule)
        self.assertEqual(memcpy_rule.match_regex, "memcpy\\s*\\(")
        self.assertIn("边界", memcpy_rule.suggestion)
        
        # 检查第二条规则
        malloc_rule = next((r for r in rules if "malloc" in r.rule_value), None)
        self.assertIsNotNone(malloc_rule)
        self.assertEqual(malloc_rule.match_regex, "malloc\\s*\\(")
        self.assertIn("返回值", malloc_rule.suggestion)
    
    def test_rule_manager_backward_compatibility(self):
        """测试规则管理器的向后兼容性"""
        yaml_content = """
categories:
  - name: "兼容性测试"
    rule_groups:
      - name: "混合规则"
        principles:
          - content: "新式规则"
            match_regex: "new\\s*\\("
            suggestion: "新式建议"
            pos_examples:
              - code: "new();"
                think: "新式"
          - content: "旧式规则"
            pos_examples:
              - code: "old();"
                think: "旧式"
        """
        
        with open(self.test_yaml_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        
        rule_manager = RuleManager(rule_file_path=self.test_yaml_file)
        rules = rule_manager.load_rules()
        
        self.assertEqual(len(rules), 2)
        
        # 检查新式规则
        new_rule = next((r for r in rules if "新式" in r.rule_value), None)
        self.assertIsNotNone(new_rule)
        self.assertEqual(new_rule.match_regex, "new\\s*\\(")
        self.assertEqual(new_rule.suggestion, "新式建议")
        
        # 检查旧式规则
        old_rule = next((r for r in rules if "旧式" in r.rule_value), None)
        self.assertIsNotNone(old_rule)
        self.assertEqual(old_rule.match_regex, "")  # 默认值
        self.assertEqual(old_rule.suggestion, "")   # 默认值


class TestRealDatasetParsing(unittest.TestCase):
    """测试真实评测集的解析"""
    
    def test_parse_real_c_evalset(self):
        """测试解析真实的C语言评测集"""
        # 检查真实评测集文件是否存在
        real_dataset_path = Path("eval/datasets/c_evalset_real.yaml")
        if not real_dataset_path.exists():
            self.skipTest("真实评测集文件不存在")
        
        try:
            rule_manager = RuleManager(rule_file_path=str(real_dataset_path))
            rules = rule_manager.load_rules()
            
            # 基本验证
            self.assertGreater(len(rules), 0, "应该加载到规则")
            
            # 检查是否有规则包含新字段
            rules_with_regex = [r for r in rules if r.match_regex]
            rules_with_suggestion = [r for r in rules if r.suggestion]
            
            self.assertGreater(len(rules_with_regex), 0, "应该有包含match_regex的规则")
            self.assertGreater(len(rules_with_suggestion), 0, "应该有包含suggestion的规则")
            
            # 验证字段内容的合理性
            for rule in rules_with_regex[:5]:  # 检查前5个
                self.assertIsInstance(rule.match_regex, str)
                self.assertGreater(len(rule.match_regex), 0)
            
            for rule in rules_with_suggestion[:5]:  # 检查前5个
                self.assertIsInstance(rule.suggestion, str)
                self.assertGreater(len(rule.suggestion), 0)
                
        except Exception as e:
            self.fail(f"解析真实评测集失败: {e}")
    
    def test_validate_regex_patterns(self):
        """验证真实评测集中的正则表达式模式"""
        real_dataset_path = Path("eval/datasets/c_evalset_real.yaml")
        if not real_dataset_path.exists():
            self.skipTest("真实评测集文件不存在")
        
        try:
            from gate_keeper.application.service.rule.regex_matcher import \
                RegexCache
            
            rule_manager = RuleManager(rule_file_path=str(real_dataset_path))
            rules = rule_manager.load_rules()
            
            invalid_patterns = []
            for rule in rules:
                if rule.match_regex:
                    compiled_regex = RegexCache.get_compiled_regex(rule.match_regex)
                    if compiled_regex is None:
                        invalid_patterns.append((rule.id, rule.match_regex))
            
            if invalid_patterns:
                self.fail(f"发现无效的正则表达式模式: {invalid_patterns}")
                
        except Exception as e:
            self.fail(f"验证正则表达式模式失败: {e}")


if __name__ == '__main__':
    unittest.main()
if __name__ == '__main__':
    unittest.main()
if __name__ == '__main__':
    unittest.main()
