#!/usr/bin/env python3
"""
测试YAML数据集加载器

验证新的YAML格式评测数据集加载功能
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from eval.datasets import YamlDataset


def test_yaml_dataset_loading(dataset_path="eval/datasets/coding_standards_eval.yaml"):
    """测试YAML数据集加载"""
    print("🧪 测试YAML数据集加载功能")
    print("=" * 50)
    
    print(f"📂 加载数据集: {dataset_path}")
    
    try:
        # 加载数据集
        print(f"📂 加载数据集: {dataset_path}")
        dataset = YamlDataset.load(dataset_path)
        
        # 显示基本信息
        print(f"📊 数据集名称: {dataset.dataset.name}")
        print(f"📊 版本: {dataset.dataset.version}")
        print(f"📊 描述: {dataset.dataset.description}")
        print(f"📊 创建时间: {dataset.dataset.created_at}")
        print(f"📊 总规则数: {dataset.dataset.total_rules}")
        print(f"📊 覆盖率: {dataset.dataset.coverage}")
        
        # 显示统计信息
        stats = dataset.get_statistics()
        print("\n📈 统计信息:")
        print(f"  - 规则分类数: {stats['total_categories']}")
        print(f"  - 编码原则数: {stats['total_principles']}")
        print(f"  - 正面示例数: {stats['total_pos_examples']}")
        print(f"  - 负面示例数: {stats['total_neg_examples']}")
        print(f"  - 总示例数: {stats['total_examples']}")
        
        # 显示规则分类
        print("\n📋 规则分类:")
        for category in dataset.dataset.rule_categories:
            print(f"  - {category.id}: {category.name} ({len(category.principles)} 个原则)")
            
            for principle in category.principles:
                print(f"    * {principle.content[:60]}...")
                print(f"      正面示例: {len(principle.pos_examples)} 个")
                print(f"      负面示例: {len(principle.neg_examples)} 个")
        
        # 测试获取所有测试用例
        print("\n🔍 测试用例转换:")
        test_cases = dataset.get_all_test_cases()
        print(f"  转换后的测试用例数: {len(test_cases)}")
        
        # 显示前几个测试用例
        print("\n📝 前3个测试用例:")
        for i, test_case in enumerate(test_cases[:3]):
            print(f"  {i+1}. {test_case['id']} - {test_case['case_name']}")
            print(f"     类型: {test_case['type']}")
            print(f"     分类: {test_case['category_name']}")
            print(f"     期望结果: {test_case['expected_answer']}")
            print(f"     代码: {test_case['code'][:50]}...")
        
        # 测试导出为JSON格式
        print("\n💾 测试JSON格式导出:")
        json_data = dataset.export_to_json_format()
        print(f"  JSON数据大小: {len(json.dumps(json_data, ensure_ascii=False))} 字符")
        
        # 保存JSON文件用于验证
        output_path = "tests/evaluation/reports/yaml_dataset_export.json"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        print(f"  JSON文件已保存: {output_path}")
        
        # 测试按分类获取示例
        print("\n🎯 测试按分类获取示例:")
        for category in dataset.dataset.rule_categories:
            examples = dataset.get_examples_by_category(category.id)
            print(f"  {category.id}: {len(examples)} 个示例")
        
        print("\n✅ YAML数据集加载测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_yaml_dataset_validation(dataset_path="eval/datasets/coding_standards_eval.yaml"):
    """测试YAML数据集验证"""
    print("\n🔍 测试YAML数据集验证")
    print("=" * 50)
    
    print(f"📂 验证数据集: {dataset_path}")
    
    try:
        dataset = YamlDataset.load(dataset_path)
        
        # 验证数据集完整性
        validation_errors = []
        
        # 检查基本信息
        if not dataset.dataset.name:
            validation_errors.append("数据集名称为空")
        
        if not dataset.dataset.version:
            validation_errors.append("数据集版本为空")
        
        # 检查规则分类
        if not dataset.dataset.rule_categories:
            validation_errors.append("没有规则分类")
        
        # 检查每个分类
        for category in dataset.dataset.rule_categories:
            if not category.id:
                validation_errors.append(f"分类ID为空: {category.name}")
            
            all_principles = category.get_all_principles()
            if not all_principles:
                validation_errors.append(f"分类没有原则: {category.id}")
            
            # 检查每个原则
            for principle in all_principles:
                if not principle.content:
                    validation_errors.append(f"原则内容为空: {category.id}")
                
                # 检查示例
                for pos_example in principle.pos_examples:
                    if not pos_example.case_name:
                        validation_errors.append(f"正面示例名称为空: {category.id}")
                    if not pos_example.expected_answer:
                        validation_errors.append(f"正面示例期望结果为空: {category.id}")
                
                for neg_example in principle.neg_examples:
                    if not neg_example.case_name:
                        validation_errors.append(f"负面示例名称为空: {category.id}")
                    # 允许期望结果为空，因为有些示例可能没有明确的期望结果
        
        if validation_errors:
            print("❌ 数据集验证失败:")
            for error in validation_errors:
                print(f"  - {error}")
            return False
        else:
            print("✅ 数据集验证通过!")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="测试YAML数据集加载和验证")
    parser.add_argument("dataset_path", nargs="?", default="eval/datasets/coding_standards_eval.yaml",
                       help="YAML数据集文件路径")
    args = parser.parse_args()
    
    print("🚀 开始YAML数据集测试")
    print("=" * 60)
    
    # 测试数据集加载
    loading_success = test_yaml_dataset_loading(args.dataset_path)
    
    # 测试数据集验证
    validation_success = test_yaml_dataset_validation(args.dataset_path)
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 60)
    print(f"数据集加载: {'✅ 通过' if loading_success else '❌ 失败'}")
    print(f"数据集验证: {'✅ 通过' if validation_success else '❌ 失败'}")
    
    if loading_success and validation_success:
        print("\n🎉 所有测试通过!")
        return 0
    else:
        print("\n💥 部分测试失败!")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 