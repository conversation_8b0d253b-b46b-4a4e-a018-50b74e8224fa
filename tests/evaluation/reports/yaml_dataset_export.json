{"metadata": {"name": "通用编码守则-基础篇", "version": 202507072020, "description": "基于编码规范_c.md构建的C语言编码规范评测集", "created_at": "2025-01-23T11:00:00+00:00", "total_cases": 42, "total_rules": 35, "coverage": "35/35 rules covered (100%)"}, "test_cases": [{"id": "TC001", "type": "positive", "category_id": "MEM-01-COPY", "category_name": "内存拷贝", "principle_content": "内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。", "case_name": "拷贝前检查边界", "description": "先确认源长度不大于目标剩余空间", "expected_answer": "没有违规", "think": "显式校验避免越界", "code": "if (srcLen <= dstMax) {\n    memcpy(dst, src, srcLen);\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC002", "type": "negative", "category_id": "MEM-01-COPY", "category_name": "内存拷贝", "principle_content": "内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。", "case_name": "直接拷贝", "description": "未检查长度越界", "expected_answer": "违规：可能发生缓冲区越界", "think": "未进行边界检查，可能导致缓冲区溢出", "code": "memcpy(dst, src, srcLen); /* dst可能不足 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC003", "type": "positive", "category_id": "MEM-02-MOVE", "category_name": "内存搬移", "principle_content": "不要使用memcpy_s函数操作有地址重叠的内存，行为结果未知，推荐使用memmove_s。", "case_name": "使用memmove处理重叠内存", "description": "正确处理内存重叠情况", "expected_answer": "没有违规", "think": "使用memmove正确处理重叠内存", "code": "memmove(buffer + 5, buffer, 11);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC004", "type": "negative", "category_id": "MEM-02-MOVE", "category_name": "内存搬移", "principle_content": "不要使用memcpy_s函数操作有地址重叠的内存，行为结果未知，推荐使用memmove_s。", "case_name": "使用memcpy处理重叠内存", "description": "错误使用memcpy处理重叠内存", "expected_answer": "违规：使用memcpy操作有地址重叠的内存", "think": "memcpy不能正确处理重叠内存，应使用memmove", "code": "memcpy(buffer + 5, buffer, 11);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC005", "type": "positive", "category_id": "STR-01-BINARY", "category_name": "二进制数据处理", "principle_content": "不要将二进制码流当做字符串操作，可能不存在'\\0'结束符，造成访问越界", "case_name": "正确处理二进制数据", "description": "使用memcpy处理二进制数据", "expected_answer": "没有违规", "think": "使用memcpy处理二进制数据，避免字符串函数", "code": "memcpy(dst, binary_data, data_len);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC006", "type": "negative", "category_id": "STR-01-BINARY", "category_name": "二进制数据处理", "principle_content": "不要将二进制码流当做字符串操作，可能不存在'\\0'结束符，造成访问越界", "case_name": "错误使用字符串函数处理二进制数据", "description": "将二进制数据当作字符串处理", "expected_answer": "违规：将二进制数据当作字符串处理", "think": "二进制数据可能没有'\u0000'结束符，使用字符串函数会越界", "code": "printf(\"%s\\n\", (char*)binary_data);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC007", "type": "positive", "category_id": "STR-02-FORMAT", "category_name": "字符串格式化", "principle_content": "使用memcpy_s接口操作字符串时要考虑'\\0'携带结束符", "case_name": "正确处理字符串结束符", "description": "考虑'\\0'结束符的字符串操作", "expected_answer": "没有违规", "think": "正确计算字符串长度，包含结束符", "code": "strcpy_s(dst, dst_size, src);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC008", "type": "negative", "category_id": "STR-02-FORMAT", "category_name": "字符串格式化", "principle_content": "使用memcpy_s接口操作字符串时要考虑'\\0'携带结束符", "case_name": "忽略字符串结束符", "description": "未考虑'\\0'结束符的字符串操作", "expected_answer": "违规：使用memcpy操作字符串时未考虑'\u0000'结束符", "think": "字符串操作必须考虑结束符，否则可能越界", "code": "memcpy(dst, src, strlen(src));\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC009", "type": "positive", "category_id": "ARR-01-BOUNDS", "category_name": "数组边界检查", "principle_content": "数组下标访问前做有效性校验，确保访问不越界。", "case_name": "数组边界检查", "description": "访问数组前进行边界检查", "expected_answer": "没有违规", "think": "显式边界检查确保安全访问", "code": "if (index < array_size) {\n    value = array[index];\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC010", "type": "negative", "category_id": "ARR-01-BOUNDS", "category_name": "数组边界检查", "principle_content": "数组下标访问前做有效性校验，确保访问不越界。", "case_name": "直接访问数组", "description": "未进行边界检查的数组访问", "expected_answer": "违规：数组下标访问前未做有效性校验", "think": "未检查边界可能导致数组越界访问", "code": "value = array[index];\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC011", "type": "positive", "category_id": "ARR-02-SIZE", "category_name": "大数组定义", "principle_content": "禁止在函数中定义占用超过500字节的数组，该数组会占用栈空间，在多级调用叠加后可能导致栈溢出。", "case_name": "合理大小的栈数组", "description": "使用合理大小的栈数组", "expected_answer": "没有违规", "think": "栈数组大小合理，不会导致栈溢出", "code": "char small_array[100];\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC012", "type": "negative", "category_id": "ARR-02-SIZE", "category_name": "大数组定义", "principle_content": "禁止在函数中定义占用超过500字节的数组，该数组会占用栈空间，在多级调用叠加后可能导致栈溢出。", "case_name": "超大栈数组", "description": "在函数中定义超过500字节的数组", "expected_answer": "违规：在函数中定义了超过500字节的栈数组", "think": "大数组占用栈空间，可能导致栈溢出", "code": "char large_array[1000];\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC013", "type": "positive", "category_id": "ARR-03-TYPE", "category_name": "数组类型转换", "principle_content": "C语言不同于高级语言，不同类型的数组指针不能通过强转使用，会造成数据错误或者越界读写", "case_name": "正确使用数组类型", "description": "使用正确的数组类型", "expected_answer": "没有违规", "think": "使用正确的数组类型，避免类型转换错误", "code": "int array[10];\nint value = array[index];\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC014", "type": "negative", "category_id": "ARR-03-TYPE", "category_name": "数组类型转换", "principle_content": "C语言不同于高级语言，不同类型的数组指针不能通过强转使用，会造成数据错误或者越界读写", "case_name": "错误的数组类型转换", "description": "强制转换不同类型的数组指针", "expected_answer": "违规：不同类型的数组指针不能通过强转使用", "think": "类型转换可能导致数据错误或越界读写", "code": "int array[10];\nchar* ptr = (char*)array;\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC015", "type": "positive", "category_id": "ARR-04-PARAM", "category_name": "数组参数传递", "principle_content": "数组作为函数参数时，禁止在函数内使用sizeof参数计算数组占用空间，必须同时将数组长度作为函数参数传入。", "case_name": "正确传递数组长度", "description": "将数组长度作为参数传递", "expected_answer": "没有违规", "think": "显式传递数组长度，避免使用sizeof", "code": "void process_array(int* array, int length) {\n    for (int i = 0; i < length; i++) {\n        // 处理数组元素\n    }\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC016", "type": "negative", "category_id": "ARR-04-PARAM", "category_name": "数组参数传递", "principle_content": "数组作为函数参数时，禁止在函数内使用sizeof参数计算数组占用空间，必须同时将数组长度作为函数参数传入。", "case_name": "使用sizeof计算数组长度", "description": "在函数内使用sizeof计算数组长度", "expected_answer": "违规：在函数内使用sizeof参数计算数组占用空间", "think": "sizeof在函数内无法正确计算数组大小", "code": "void process_array(int* array) {\n    int length = sizeof(array) / sizeof(array[0]);\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC017", "type": "positive", "category_id": "RES-01-MALLOC", "category_name": "内存申请接口", "principle_content": "不要使用malloc和free等申请系统内存，系统内存预留有限，并且出现内存泄漏时难以定位。", "case_name": "使用推荐的内存接口", "description": "使用mem_api.h中的接口", "expected_answer": "没有违规", "think": "使用带有内存统计的接口，便于定位问题", "code": "void* ptr = UTIL_MallocDyn(100);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC018", "type": "negative", "category_id": "RES-01-MALLOC", "category_name": "内存申请接口", "principle_content": "不要使用malloc和free等申请系统内存，系统内存预留有限，并且出现内存泄漏时难以定位。", "case_name": "使用禁止的系统内存接口", "description": "使用malloc申请系统内存", "expected_answer": "违规：使用了禁止的系统内存接口malloc", "think": "系统内存接口难以定位内存泄漏问题", "code": "void* ptr = malloc(100);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC019", "type": "positive", "category_id": "RES-02-LEAK", "category_name": "内存泄漏防护", "principle_content": "申请临时内存资源注意在函数执行完成后释放，提前退出的分支也要考虑释放。", "case_name": "正确释放内存", "description": "在所有退出路径都释放内存", "expected_answer": "没有违规", "think": "确保所有分支都正确释放内存", "code": "void* ptr = UTIL_MallocDyn(100);\nif (error) {\n    UTIL_FreeDyn(ptr);\n    return;\n}\n// 使用ptr\nUTIL_FreeDyn(ptr);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC020", "type": "negative", "category_id": "RES-02-LEAK", "category_name": "内存泄漏防护", "principle_content": "申请临时内存资源注意在函数执行完成后释放，提前退出的分支也要考虑释放。", "case_name": "提前退出未释放内存", "description": "提前退出分支未释放内存", "expected_answer": "违规：申请的内存未在提前退出分支中释放", "think": "提前退出时未释放内存，造成内存泄漏", "code": "void* ptr = UTIL_MallocDyn(100);\nif (error) {\n    return; /* 未释放内存 */\n}\nUTIL_FreeDyn(ptr);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC021", "type": "positive", "category_id": "FUN-01-RECURSION", "category_name": "递归函数限制", "principle_content": "禁止使用递归函数，递归函数调用栈深度不可控，容易造成栈溢出。", "case_name": "使用迭代替代递归", "description": "使用循环实现相同功能", "expected_answer": "没有违规", "think": "使用迭代避免递归调用栈问题", "code": "int factorial(int n) {\n    int result = 1;\n    for (int i = 1; i <= n; i++) {\n        result *= i;\n    }\n    return result;\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC022", "type": "negative", "category_id": "FUN-01-RECURSION", "category_name": "递归函数限制", "principle_content": "禁止使用递归函数，递归函数调用栈深度不可控，容易造成栈溢出。", "case_name": "使用递归函数", "description": "使用递归实现功能", "expected_answer": "违规：使用了递归函数", "think": "递归函数调用栈深度不可控，容易造成栈溢出", "code": "int factorial(int n) {\n    if (n <= 1) return 1;\n    return n * factorial(n - 1);\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC023", "type": "positive", "category_id": "FUN-02-POINTER", "category_name": "指针参数检查", "principle_content": "函数参数为指针类型时，必须检查指针的有效性，避免空指针访问。", "case_name": "检查指针有效性", "description": "函数开始检查指针参数", "expected_answer": "没有违规", "think": "检查指针有效性避免空指针访问", "code": "void process_data(int* data) {\n    if (data == NULL) {\n        return;\n    }\n    // 处理数据\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC024", "type": "negative", "category_id": "FUN-02-POINTER", "category_name": "指针参数检查", "principle_content": "函数参数为指针类型时，必须检查指针的有效性，避免空指针访问。", "case_name": "未检查指针有效性", "description": "直接使用指针参数", "expected_answer": "违规：函数参数为指针类型时未检查指针的有效性", "think": "未检查指针可能导致空指针访问", "code": "void process_data(int* data) {\n    *data = 100; /* 可能访问空指针 */\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC025", "type": "positive", "category_id": "ARITH-01-OVERFLOW", "category_name": "算术溢出检查", "principle_content": "进行算术运算前，必须检查操作数是否会发生溢出。", "case_name": "检查算术溢出", "description": "运算前检查溢出", "expected_answer": "没有违规", "think": "检查溢出避免计算结果错误", "code": "if (a > 0 && b > 0 && a > INT_MAX - b) {\n    // 处理溢出\n} else {\n    result = a + b;\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC026", "type": "negative", "category_id": "ARITH-01-OVERFLOW", "category_name": "算术溢出检查", "principle_content": "进行算术运算前，必须检查操作数是否会发生溢出。", "case_name": "未检查算术溢出", "description": "直接进行算术运算", "expected_answer": "违规：进行算术运算前未检查操作数是否会发生溢出", "think": "未检查溢出可能导致计算结果错误", "code": "int result = a + b; /* 可能溢出 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC027", "type": "positive", "category_id": "ARITH-02-DIVISION", "category_name": "除零检查", "principle_content": "进行除法运算前，必须检查除数是否为零。", "case_name": "检查除零", "description": "除法前检查除数", "expected_answer": "没有违规", "think": "检查除零避免程序崩溃", "code": "if (divisor != 0) {\n    result = dividend / divisor;\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC028", "type": "negative", "category_id": "ARITH-02-DIVISION", "category_name": "除零检查", "principle_content": "进行除法运算前，必须检查除数是否为零。", "case_name": "未检查除零", "description": "直接进行除法运算", "expected_answer": "违规：进行除法运算前未检查除数是否为零", "think": "未检查除零可能导致程序崩溃", "code": "int result = dividend / divisor; /* 可能除零 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC029", "type": "positive", "category_id": "SEC-01-BUFFER", "category_name": "缓冲区安全", "principle_content": "使用安全的字符串函数，避免缓冲区溢出。", "case_name": "使用安全字符串函数", "description": "使用strcpy_s等安全函数", "expected_answer": "没有违规", "think": "使用安全函数避免缓冲区溢出", "code": "strcpy_s(dst, dst_size, src);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC030", "type": "negative", "category_id": "SEC-01-BUFFER", "category_name": "缓冲区安全", "principle_content": "使用安全的字符串函数，避免缓冲区溢出。", "case_name": "使用不安全字符串函数", "description": "使用strcpy等不安全函数", "expected_answer": "违规：使用了不安全的字符串函数", "think": "不安全函数可能导致缓冲区溢出", "code": "strcpy(dst, src); /* 可能溢出 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC031", "type": "positive", "category_id": "SEC-02-INPUT", "category_name": "输入验证", "principle_content": "对用户输入进行严格验证，防止恶意输入。", "case_name": "验证用户输入", "description": "对输入进行验证", "expected_answer": "没有违规", "think": "验证输入防止恶意数据", "code": "if (input_length > 0 && input_length < MAX_LENGTH) {\n    process_input(input);\n}\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC032", "type": "negative", "category_id": "SEC-02-INPUT", "category_name": "输入验证", "principle_content": "对用户输入进行严格验证，防止恶意输入。", "case_name": "未验证用户输入", "description": "直接使用用户输入", "expected_answer": "违规：对用户输入未进行严格验证", "think": "未验证输入可能导致安全问题", "code": "process_input(user_input); /* 未验证 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC033", "type": "positive", "category_id": "PRIV-01-LOG", "category_name": "隐私日志保护", "principle_content": "日志中不得包含用户隐私信息，如密码、身份证号等。", "case_name": "保护隐私信息", "description": "日志中不包含敏感信息", "expected_answer": "没有违规", "think": "保护用户隐私信息", "code": "LOG_INFO(\"用户登录成功，用户ID: %d\", user_id);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC034", "type": "negative", "category_id": "PRIV-01-LOG", "category_name": "隐私日志保护", "principle_content": "日志中不得包含用户隐私信息，如密码、身份证号等。", "case_name": "泄露隐私信息", "description": "日志中包含敏感信息", "expected_answer": "违规：日志中包含用户隐私信息", "think": "日志中不应包含密码等敏感信息", "code": "LOG_INFO(\"用户登录，密码: %s\", password);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC035", "type": "positive", "category_id": "COMP-01-MACRO", "category_name": "编译宏使用", "principle_content": "使用编译宏时，必须考虑不同平台的兼容性。", "case_name": "平台兼容的宏使用", "description": "考虑平台兼容性", "expected_answer": "没有违规", "think": "使用平台兼容的宏定义", "code": "#ifdef _WIN32\n    #define PATH_SEPARATOR \"\\\\\"\n#else\n    #define PATH_SEPARATOR \"/\"\n#endif\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC036", "type": "negative", "category_id": "COMP-01-MACRO", "category_name": "编译宏使用", "principle_content": "使用编译宏时，必须考虑不同平台的兼容性。", "case_name": "平台相关的宏使用", "description": "未考虑平台兼容性", "expected_answer": "违规：使用编译宏时未考虑不同平台的兼容性", "think": "硬编码平台相关宏可能导致兼容性问题", "code": "#define PATH_SEPARATOR \"\\\\\" /* Windows专用 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC037", "type": "positive", "category_id": "LOG-01-LEVEL", "category_name": "日志级别使用", "principle_content": "根据日志的重要程度选择合适的日志级别。", "case_name": "正确使用日志级别", "description": "根据重要性选择级别", "expected_answer": "没有违规", "think": "使用合适的日志级别", "code": "LOG_ERROR(\"系统错误: %s\", error_msg);\nLOG_INFO(\"用户操作: %s\", action);\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC038", "type": "negative", "category_id": "LOG-01-LEVEL", "category_name": "日志级别使用", "principle_content": "根据日志的重要程度选择合适的日志级别。", "case_name": "错误使用日志级别", "description": "使用不合适的日志级别", "expected_answer": "违规：未根据日志的重要程度选择合适的日志级别", "think": "错误使用日志级别影响日志分析", "code": "LOG_DEBUG(\"系统错误: %s\", error_msg); /* 错误应该用ERROR级别 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC039", "type": "positive", "category_id": "STRUCT-01-ALIGN", "category_name": "结构体对齐", "principle_content": "结构体成员排列时，考虑内存对齐，提高访问效率。", "case_name": "合理的内存对齐", "description": "考虑内存对齐的结构体", "expected_answer": "没有违规", "think": "合理排列成员提高访问效率", "code": "struct aligned_struct {\n    int a;      /* 4字节 */\n    char b;     /* 1字节 */\n    int c;      /* 4字节 */\n};\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC040", "type": "negative", "category_id": "STRUCT-01-ALIGN", "category_name": "结构体对齐", "principle_content": "结构体成员排列时，考虑内存对齐，提高访问效率。", "case_name": "不合理的内存对齐", "description": "未考虑内存对齐的结构体", "expected_answer": "违规：结构体成员排列时未考虑内存对齐", "think": "不合理的内存对齐影响访问效率", "code": "struct unaligned_struct {\n    char a;     /* 1字节 */\n    int b;      /* 4字节，可能不对齐 */\n    char c;     /* 1字节 */\n};\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC041", "type": "positive", "category_id": "VAR-01-TYPE", "category_name": "变量类型选择", "principle_content": "根据变量的取值范围选择合适的类型，避免溢出。", "case_name": "合适的变量类型", "description": "根据取值范围选择类型", "expected_answer": "没有违规", "think": "选择合适的类型避免溢出", "code": "uint32_t counter = 0; /* 无符号32位 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}, {"id": "TC042", "type": "negative", "category_id": "VAR-01-TYPE", "category_name": "变量类型选择", "principle_content": "根据变量的取值范围选择合适的类型，避免溢出。", "case_name": "不合适的变量类型", "description": "使用可能溢出的类型", "expected_answer": "违规：未根据变量的取值范围选择合适的类型", "think": "类型选择不当可能导致溢出", "code": "char counter = 0; /* 可能溢出 */\n", "git": {"repo_url": "", "branch": "", "commit": "", "mr_id": "", "file_path": "", "function_name": ""}}]}