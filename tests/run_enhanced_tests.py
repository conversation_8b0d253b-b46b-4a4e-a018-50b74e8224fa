#!/usr/bin/env python3
"""
运行增强功能测试

执行所有与评测集更新相关的测试，验证match_regex和suggestion字段的功能
"""

import os
import sys
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_enhanced_tests():
    """运行所有增强功能测试"""
    
    print("=" * 60)
    print("运行评测集增强功能测试")
    print("=" * 60)
    
    # 测试套件列表
    test_modules = [
        'tests.domain.test_enhanced_rule_models',
        'tests.application.test_regex_matcher',
        'tests.evaluation.test_enhanced_dataset_parsing',
        'tests.integration.test_enhanced_rule_checking'
    ]
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 加载所有测试模块
    for module_name in test_modules:
        try:
            print(f"\n加载测试模块: {module_name}")
            module_suite = loader.loadTestsFromName(module_name)
            suite.addTest(module_suite)
            print(f"✓ 成功加载 {module_name}")
        except Exception as e:
            print(f"✗ 加载失败 {module_name}: {e}")
            continue
    
    # 运行测试
    print("\n" + "=" * 60)
    print("开始执行测试...")
    print("=" * 60)
    
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    # 返回是否所有测试都通过
    return len(result.failures) == 0 and len(result.errors) == 0


def run_specific_test_category(category):
    """运行特定类别的测试"""
    
    category_modules = {
        'domain': ['tests.domain.test_enhanced_rule_models'],
        'application': ['tests.application.test_regex_matcher'],
        'evaluation': ['tests.evaluation.test_enhanced_dataset_parsing'],
        'integration': ['tests.integration.test_enhanced_rule_checking']
    }
    
    if category not in category_modules:
        print(f"未知的测试类别: {category}")
        print(f"可用类别: {', '.join(category_modules.keys())}")
        return False
    
    print(f"运行 {category} 类别的测试...")
    
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    for module_name in category_modules[category]:
        try:
            module_suite = loader.loadTestsFromName(module_name)
            suite.addTest(module_suite)
        except Exception as e:
            print(f"加载测试模块失败 {module_name}: {e}")
            return False
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0


def run_quick_validation():
    """运行快速验证测试"""
    
    print("运行快速验证测试...")
    
    # 只运行关键的测试用例
    quick_tests = [
        'tests.domain.test_enhanced_rule_models.TestEnhancedCodeCheckRule.test_code_check_rule_with_match_regex',
        'tests.application.test_regex_matcher.TestRegexMatcher.test_match_code_basic'
    ]
    
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    for test_name in quick_tests:
        try:
            test = loader.loadTestsFromName(test_name)
            suite.addTest(test)
        except Exception as e:
            print(f"加载测试失败 {test_name}: {e}")
            continue
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0


def check_test_environment():
    """检查测试环境"""
    
    print("检查测试环境...")
    
    # 检查必要的文件和目录
    required_paths = [
        'gate_keeper/domain/rule/check_rule.py',
        'gate_keeper/application/service/rule/regex_matcher.py',
        'gate_keeper/application/service/rule/rule_service/manager.py',
        'eval/datasets/models.py',
        'eval/datasets/yaml_dataset.py'
    ]
    
    missing_paths = []
    for path in required_paths:
        if not Path(path).exists():
            missing_paths.append(path)
    
    if missing_paths:
        print("缺少必要的文件:")
        for path in missing_paths:
            print(f"  - {path}")
        return False
    
    # 检查评测集文件
    evalset_path = Path('eval/datasets/c_evalset_real.yaml')
    if evalset_path.exists():
        print("✓ 找到真实评测集文件")
    else:
        print("⚠ 未找到真实评测集文件，部分测试可能被跳过")
    
    print("✓ 测试环境检查完成")
    return True


def main():
    """主函数"""
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'quick':
            success = run_quick_validation()
        elif command == 'check':
            success = check_test_environment()
        elif command in ['domain', 'application', 'evaluation', 'integration']:
            success = run_specific_test_category(command)
        else:
            print(f"未知命令: {command}")
            print("可用命令:")
            print("  quick      - 运行快速验证测试")
            print("  check      - 检查测试环境")
            print("  domain     - 运行领域模型测试")
            print("  application - 运行应用层测试")
            print("  evaluation - 运行评测集测试")
            print("  integration - 运行集成测试")
            print("  (无参数)    - 运行所有测试")
            return 1
    else:
        # 检查环境
        if not check_test_environment():
            print("测试环境检查失败，退出")
            return 1
        
        # 运行所有测试
        success = run_enhanced_tests()
    
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
