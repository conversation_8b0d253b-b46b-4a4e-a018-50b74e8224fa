"""
文件过滤器单元测试
"""

import os
import tempfile
from pathlib import Path
from unittest import TestCase

from gate_keeper.shared.file_filter import FileFilter


class TestFileFilter(TestCase):
    """文件过滤器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.create_test_files()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def create_test_files(self):
        """创建测试文件"""
        # 创建目录结构
        os.makedirs(os.path.join(self.temp_dir, "src"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "tests"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "docs"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "node_modules"), exist_ok=True)
        
        # 创建测试文件
        test_files = [
            "src/main.py",
            "src/utils.py",
            "src/config.py",
            "tests/test_main.py",
            "tests/test_utils.py",
            "docs/README.md",
            "docs/API.md",
            "node_modules/package.json",
            "requirements.txt",
            "setup.py",
            ".gitignore",
            "main.py",
            "utils.py"
        ]
        
        for file_path in test_files:
            full_path = os.path.join(self.temp_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(f"# {file_path}")
    
    def test_empty_patterns(self):
        """测试空模式列表"""
        filter_obj = FileFilter()
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 应该返回所有文件
        self.assertGreater(len(files), 0)
        self.assertTrue(any("main.py" in f for f in files))
        self.assertTrue(any("test_main.py" in f for f in files))
    
    def test_glob_patterns(self):
        """测试glob模式"""
        patterns = ["tests/*"]  # 只排除tests目录下的文件
        filter_obj = FileFilter(patterns)
        files = filter_obj.filter_directory(self.temp_dir)
        
        print(f"Debug: temp_dir = {self.temp_dir}")
        print(f"Debug: patterns = {patterns}")
        print(f"Debug: all files = {files}")
        
        # 应该包含Python文件
        self.assertTrue(any("main.py" in f for f in files))
        self.assertTrue(any("utils.py" in f for f in files))
        
        # 不应该包含测试文件
        self.assertFalse(any("test_main.py" in f for f in files))
        self.assertFalse(any("test_utils.py" in f for f in files))
    
    def test_directory_patterns(self):
        """测试目录模式"""
        patterns = ["tests/**", "docs/**", "node_modules/**"]
        filter_obj = FileFilter(patterns)
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 不应该包含测试目录文件
        self.assertFalse(any("test_main.py" in f for f in files))
        self.assertFalse(any("test_utils.py" in f for f in files))
        
        # 不应该包含文档目录文件
        self.assertFalse(any("README.md" in f for f in files))
        self.assertFalse(any("API.md" in f for f in files))
        
        # 不应该包含node_modules文件
        self.assertFalse(any("package.json" in f for f in files))
        
        # 应该包含src目录文件
        self.assertTrue(any("src/main.py" in f for f in files))
    
    def test_regex_patterns(self):
        """测试正则表达式模式"""
        patterns = [r"test.*\.py$"]  # 只排除以test开头的Python文件
        filter_obj = FileFilter(patterns)
        
        # 手动测试should_exclude
        print(f"Debug: should_exclude('tests/test_main.py') = {filter_obj.should_exclude('tests/test_main.py')}")
        print(f"Debug: should_exclude('test_main.py') = {filter_obj.should_exclude('test_main.py')}")
        print(f"Debug: should_exclude('main.py') = {filter_obj.should_exclude('main.py')}")
        
        files = filter_obj.filter_directory(self.temp_dir)
        
        print(f"Debug: patterns = {patterns}")
        print(f"Debug: all files = {files}")
        
        # 应该包含普通Python文件
        self.assertTrue(any("main.py" in f for f in files))
        self.assertTrue(any("utils.py" in f for f in files))
        
        # 不应该包含测试文件
        self.assertFalse(any("test_main.py" in f for f in files))
        self.assertFalse(any("test_utils.py" in f for f in files))
    
    def test_should_exclude(self):
        """测试should_exclude方法"""
        patterns = ["tests/*", "*.md"]
        filter_obj = FileFilter(patterns)
        
        # 测试应该排除的文件
        self.assertTrue(filter_obj.should_exclude("tests/test_main.py"))
        self.assertTrue(filter_obj.should_exclude("docs/README.md"))
        
        # 测试不应该排除的文件
        self.assertFalse(filter_obj.should_exclude("src/main.py"))
        self.assertFalse(filter_obj.should_exclude("utils.py"))
    
    def test_filter_files(self):
        """测试filter_files方法"""
        patterns = ["tests/*", "*.md"]
        filter_obj = FileFilter(patterns)
        
        file_list = [
            "src/main.py",
            "tests/test_main.py",
            "docs/README.md",
            "utils.py"
        ]
        
        filtered = filter_obj.filter_files(file_list)
        
        # 应该包含这些文件
        self.assertIn("src/main.py", filtered)
        self.assertIn("utils.py", filtered)
        
        # 不应该包含这些文件
        self.assertNotIn("tests/test_main.py", filtered)
        self.assertNotIn("docs/README.md", filtered)
    
    def test_from_file(self):
        """测试从文件创建过滤器"""
        # 创建临时文件
        patterns_file = os.path.join(self.temp_dir, "exclude.txt")
        with open(patterns_file, 'w') as f:
            f.write("# 这是注释\n")
            f.write("tests/*\n")
            f.write("*.md\n")
            f.write("  # 另一个注释\n")
            f.write("node_modules/**\n")
        
        filter_obj = FileFilter.from_file(patterns_file)
        
        # 验证模式
        expected_patterns = ["tests/*", "*.md", "node_modules/**"]
        self.assertEqual(set(filter_obj.get_patterns()), set(expected_patterns))
    
    def test_from_environment(self):
        """测试从环境变量创建过滤器"""
        import os

        # 设置环境变量
        os.environ['EXCLUDE_PATTERNS'] = "tests/*,*.md,node_modules/**"
        
        filter_obj = FileFilter.from_environment()
        
        # 验证模式
        expected_patterns = ["tests/*", "*.md", "node_modules/**"]
        self.assertEqual(set(filter_obj.get_patterns()), set(expected_patterns))
        
        # 清理环境变量
        del os.environ['EXCLUDE_PATTERNS']
    
    def test_add_remove_patterns(self):
        """测试添加和移除模式"""
        filter_obj = FileFilter()
        
        # 添加模式
        filter_obj.add_pattern("*.py")
        self.assertIn("*.py", filter_obj.get_patterns())
        
        # 移除模式
        filter_obj.remove_pattern("*.py")
        self.assertNotIn("*.py", filter_obj.get_patterns())
        
        # 清空模式
        filter_obj.add_pattern("tests/*")
        filter_obj.clear_patterns()
        self.assertEqual(len(filter_obj.get_patterns()), 0)
    
    def test_complex_patterns(self):
        """测试复杂模式组合"""
        patterns = [
            "tests/**",
            "docs/**", 
            "*.md",
            "node_modules/**",
            "__pycache__/**",
            "*.pyc",
            ".git/**"
        ]
        filter_obj = FileFilter(patterns)
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 验证过滤结果
        for file_path in files:
            # 不应该包含被排除的文件
            self.assertFalse("tests/" in file_path)
            self.assertFalse("docs/" in file_path)
            self.assertFalse("node_modules/" in file_path)
            self.assertFalse(file_path.endswith(".md"))
            self.assertFalse(file_path.endswith(".pyc"))
        
        # 应该包含未被排除的文件
        self.assertTrue(any("src/main.py" in f for f in files))
        self.assertTrue(any("requirements.txt" in f for f in files))
    
    def test_path_normalization(self):
        """测试路径标准化"""
        patterns = ["tests/*"]
        filter_obj = FileFilter(patterns)
        
        # 测试相对路径
        rel_path = os.path.join("tests", "test_main.py")
        self.assertTrue(filter_obj.should_exclude(rel_path))
        
        # 测试Path对象
        path_obj = Path("tests") / "test_main.py"
        self.assertTrue(filter_obj.should_exclude(path_obj))
    
    def test_ai_debug_exclusion(self):
        """测试ai_debug目录排除"""
        patterns = ["ai_debug/**"]
        filter_obj = FileFilter(patterns)
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 创建ai_debug目录和文件
        ai_debug_dir = os.path.join(self.temp_dir, "ai_debug")
        os.makedirs(ai_debug_dir, exist_ok=True)
        with open(os.path.join(ai_debug_dir, "debug.py"), 'w') as f:
            f.write("# debug file")
        
        # 重新过滤
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 不应该包含ai_debug目录下的文件
        self.assertFalse(any("ai_debug/debug.py" in f for f in files))
        
        # 应该包含其他文件
        self.assertTrue(any("src/main.py" in f for f in files))
    
    def test_multiple_exclude_sources(self):
        """测试多个排除来源的合并"""
        import os

        # 设置环境变量
        os.environ['EXCLUDE_PATTERNS'] = "env_pattern1,env_pattern2"
        
        # 创建临时文件
        patterns_file = os.path.join(self.temp_dir, "exclude.txt")
        with open(patterns_file, 'w') as f:
            f.write("file_pattern1\n")
            f.write("file_pattern2\n")
        
        # 从多个来源创建过滤器
        filter_obj = FileFilter.from_file(patterns_file)
        env_filter = FileFilter.from_environment()
        
        # 合并模式
        all_patterns = filter_obj.get_patterns() + env_filter.get_patterns()
        combined_filter = FileFilter(all_patterns)
        
        expected_patterns = ["file_pattern1", "file_pattern2", "env_pattern1", "env_pattern2"]
        self.assertEqual(set(combined_filter.get_patterns()), set(expected_patterns))
        
        # 清理环境变量
        del os.environ['EXCLUDE_PATTERNS']
    
    def test_pattern_priority(self):
        """测试模式优先级"""
        # 测试更具体的模式优先
        patterns = ["*.py", "src/*.py"]  # src/*.py 更具体
        filter_obj = FileFilter(patterns)
        
        # 测试文件
        test_files = ["main.py", "src/main.py", "utils.py"]
        filtered = filter_obj.filter_files(test_files)
        
        # 所有Python文件都应该被排除
        self.assertEqual(len(filtered), 0)
    
    def test_case_sensitivity(self):
        """测试大小写敏感性"""
        patterns = ["*.PY", "Tests/*"]
        filter_obj = FileFilter(patterns)
        
        # 测试文件
        test_files = ["main.py", "MAIN.PY", "tests/test.py", "Tests/test.py"]
        filtered = filter_obj.filter_files(test_files)
        
        # 应该排除匹配的文件
        self.assertNotIn("MAIN.PY", filtered)
        self.assertNotIn("Tests/test.py", filtered)
        # 不匹配的应该保留
        self.assertIn("main.py", filtered)
        self.assertIn("tests/test.py", filtered)
    
    def test_empty_file_patterns(self):
        """测试空文件模式"""
        # 创建空文件
        empty_file = os.path.join(self.temp_dir, "empty.txt")
        with open(empty_file, 'w') as f:
            pass
        
        filter_obj = FileFilter.from_file(empty_file)
        self.assertEqual(len(filter_obj.get_patterns()), 0)
    
    def test_invalid_regex_pattern(self):
        """测试无效的正则表达式模式"""
        patterns = ["[invalid", "*.py"]  # 无效的正则表达式
        filter_obj = FileFilter(patterns)
        
        # 应该能正常创建，无效的正则会被当作glob处理
        self.assertEqual(len(filter_obj.get_patterns()), 2)
        
        # 测试过滤功能
        test_files = ["main.py", "utils.py"]
        filtered = filter_obj.filter_files(test_files)
        
        # 所有Python文件都应该被排除
        self.assertEqual(len(filtered), 0)
    
    def test_unicode_patterns(self):
        """测试Unicode模式"""
        patterns = ["中文/*", "*.中文"]
        filter_obj = FileFilter(patterns)
        
        # 创建包含中文的文件
        chinese_dir = os.path.join(self.temp_dir, "中文")
        os.makedirs(chinese_dir, exist_ok=True)
        with open(os.path.join(chinese_dir, "test.中文"), 'w') as f:
            f.write("# 中文文件")
        
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 中文文件应该被排除
        self.assertFalse(any("中文/test.中文" in f for f in files))
    
    def test_symlink_handling(self):
        """测试符号链接处理"""
        import os

        # 创建符号链接
        link_target = os.path.join(self.temp_dir, "src", "main.py")
        link_path = os.path.join(self.temp_dir, "link.py")
        
        try:
            os.symlink(link_target, link_path)
            
            patterns = ["*.py"]
            filter_obj = FileFilter(patterns)
            files = filter_obj.filter_directory(self.temp_dir)
            
            # 符号链接应该被正确处理
            self.assertFalse(any("link.py" in f for f in files))
        except OSError:
            # 在不支持符号链接的系统上跳过
            self.skipTest("Symbolic links not supported on this system")
    
    def test_hidden_files(self):
        """测试隐藏文件处理"""
        # 创建隐藏文件
        hidden_file = os.path.join(self.temp_dir, ".hidden")
        with open(hidden_file, 'w') as f:
            f.write("# hidden file")
        
        patterns = [".*"]
        filter_obj = FileFilter(patterns)
        files = filter_obj.filter_directory(self.temp_dir)
        
        # 隐藏文件应该被排除
        self.assertFalse(any(".hidden" in f for f in files))
    
    def test_performance_large_directory(self):
        """测试大目录的性能"""
        import time

        # 创建大量文件
        large_dir = os.path.join(self.temp_dir, "large")
        os.makedirs(large_dir, exist_ok=True)
        
        for i in range(100):
            with open(os.path.join(large_dir, f"file_{i}.py"), 'w') as f:
                f.write(f"# file {i}")
        
        patterns = ["large/*.py"]
        filter_obj = FileFilter(patterns)
        
        start_time = time.time()
        files = filter_obj.filter_directory(self.temp_dir)
        end_time = time.time()
        
        # 性能测试：应该在合理时间内完成
        self.assertLess(end_time - start_time, 1.0)  # 1秒内完成
        
        # 验证结果
        self.assertFalse(any("large/file_" in f for f in files)) 