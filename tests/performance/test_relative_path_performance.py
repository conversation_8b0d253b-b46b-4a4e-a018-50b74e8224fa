#!/usr/bin/env python3
"""
RepositoryIndex 相对路径处理性能测试

测试相对路径处理在各种场景下的性能表现，包括：
1. 大量路径转换的性能
2. 复杂项目结构的索引性能
3. 缓存操作的性能
4. 内存使用情况
"""

import gc
import os
import sys
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import MagicMock

import psutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.git import GitService
from gate_keeper.external.code_analyzer import (RepositoryIndex,
                                                collect_source_files)


class TestRelativePathPerformance(unittest.TestCase):
    """
    相对路径处理性能测试
    
    验证目标：
    1. 路径转换操作性能良好
    2. 大量文件索引性能可接受
    3. 内存使用合理
    4. 缓存操作高效
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_dir)
        
        # Mock GitService
        self.mock_git_service = MagicMock(spec=GitService)
        self.mock_git_service.get_branch_commit_sha.return_value = "test_commit_sha"
        self.mock_git_service.get_file_content_by_ref.return_value = "# Test file content"
        
        # 创建RepositoryIndex实例
        self.repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit_sha"
        )
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_large_project_structure(self, num_files=1000):
        """创建大型项目结构用于性能测试"""
        # 创建目录结构
        directories = [
            "src",
            "src/utils",
            "src/models",
            "src/controllers",
            "src/services",
            "tests",
            "tests/unit",
            "tests/integration",
            "tests/e2e",
            "docs",
            "docs/api",
            "docs/user_guide",
            "config",
            "scripts",
            "tools"
        ]
        
        for directory in directories:
            os.makedirs(os.path.join(self.repo_dir, directory), exist_ok=True)
        
        # 创建大量文件
        for i in range(num_files):
            # 分散到不同目录
            if i % 5 == 0:
                subdir = "src"
            elif i % 5 == 1:
                subdir = "tests"
            elif i % 5 == 2:
                subdir = "docs"
            elif i % 5 == 3:
                subdir = "config"
            else:
                subdir = "scripts"
            
            file_path = os.path.join(self.repo_dir, subdir, f"file_{i}.py")
            with open(file_path, 'w') as f:
                f.write(f"def function_{i}():\n    pass\n")
    
    def test_path_conversion_performance(self):
        """
        测试路径转换性能
        
        验证目的：确保路径转换操作在大量路径下性能良好
        """
        # 准备大量测试路径
        test_paths = []
        for i in range(5000):
            # 混合不同风格的路径
            if i % 3 == 0:
                test_paths.append(f"/test/repo/src/file_{i}.py")
            elif i % 3 == 1:
                test_paths.append(f"C:\\test\\repo\\src\\file_{i}.py")
            else:
                test_paths.append(f"src/file_{i}.py")
        
        # 测试to_rel_path性能
        start_time = time.time()
        rel_paths = []
        for path in test_paths:
            rel_path = self.repo_index.to_rel_path(path)
            rel_paths.append(rel_path)
        end_time = time.time()
        
        conversion_time = end_time - start_time
        paths_per_second = len(test_paths) / conversion_time
        
        print(f"\n路径转换性能测试:")
        print(f"  总路径数: {len(test_paths)}")
        print(f"  转换时间: {conversion_time:.3f}秒")
        print(f"  每秒转换: {paths_per_second:.0f}个路径")
        
        # 性能要求：每秒至少转换1000个路径
        self.assertGreater(paths_per_second, 1000, 
                          f"路径转换性能不达标: {paths_per_second:.0f} 路径/秒")
        
        # 测试to_abs_path性能
        start_time = time.time()
        for rel_path in rel_paths[:1000]:  # 测试前1000个
            self.repo_index.to_abs_path(rel_path)
        end_time = time.time()
        
        abs_conversion_time = end_time - start_time
        abs_paths_per_second = 1000 / abs_conversion_time
        
        print(f"  绝对路径转换时间: {abs_conversion_time:.3f}秒")
        print(f"  绝对路径转换速度: {abs_paths_per_second:.0f}个路径/秒")
        
        # 性能要求：每秒至少转换5000个绝对路径
        self.assertGreater(abs_paths_per_second, 5000, 
                          f"绝对路径转换性能不达标: {abs_paths_per_second:.0f} 路径/秒")
        
        # 测试_get_node_id性能
        start_time = time.time()
        for i, rel_path in enumerate(rel_paths[:1000]):
            self.repo_index._get_node_id(rel_path, f"func_{i}")
        end_time = time.time()
        
        node_id_time = end_time - start_time
        node_ids_per_second = 1000 / node_id_time
        
        print(f"  节点ID生成时间: {node_id_time:.3f}秒")
        print(f"  节点ID生成速度: {node_ids_per_second:.0f}个/秒")
        
        # 性能要求：每秒至少生成5000个节点ID
        self.assertGreater(node_ids_per_second, 5000, 
                          f"节点ID生成性能不达标: {node_ids_per_second:.0f} 个/秒")
    
    def test_large_project_indexing_performance(self):
        """
        测试大型项目索引性能
        
        验证目的：确保在大型项目上索引性能可接受
        """
        # 创建大型项目结构
        self._create_large_project_structure(num_files=2000)
        
        # 记录内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行索引
        start_time = time.time()
        self.repo_index.build()
        end_time = time.time()
        
        indexing_time = end_time - start_time
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"\n大型项目索引性能测试:")
        print(f"  文件数量: 2000")
        print(f"  索引时间: {indexing_time:.3f}秒")
        print(f"  内存使用: {final_memory:.1f}MB")
        print(f"  内存增长: {memory_increase:.1f}MB")
        
        # 性能要求：2000个文件的索引应该在30秒内完成
        self.assertLess(indexing_time, 30.0, 
                       f"大型项目索引性能不达标: {indexing_time:.3f}秒")
        
        # 内存要求：内存增长不应该超过500MB
        self.assertLess(memory_increase, 500.0, 
                       f"内存使用过高: {memory_increase:.1f}MB")
        
        # 验证索引结果
        self.assertIsNotNone(self.repo_index.repo)
        self.assertGreater(len(self.repo_index.repo.modules), 0)
        
        # 验证所有路径都是相对路径
        for module_path in self.repo_index.repo.modules.keys():
            self.assertFalse(Path(module_path).is_absolute())
            self.assertNotIn('\\', module_path)
    
    def test_file_collection_performance(self):
        """
        测试文件收集性能
        
        验证目的：确保文件收集功能在大项目上性能良好
        """
        # 创建大型项目结构
        self._create_large_project_structure(num_files=3000)
        
        # 测试无过滤的文件收集性能
        start_time = time.time()
        all_files = collect_source_files(self.repo_dir)
        end_time = time.time()
        
        collection_time = end_time - start_time
        files_per_second = len(all_files) / collection_time
        
        print(f"\n文件收集性能测试:")
        print(f"  收集文件数: {len(all_files)}")
        print(f"  收集时间: {collection_time:.3f}秒")
        print(f"  收集速度: {files_per_second:.0f}个文件/秒")
        
        # 性能要求：每秒至少收集100个文件
        self.assertGreater(files_per_second, 100, 
                          f"文件收集性能不达标: {files_per_second:.0f} 文件/秒")
        
        # 测试带过滤的文件收集性能
        exclude_patterns = ["tests/*", "*.md", "docs/*"]
        
        start_time = time.time()
        filtered_files = collect_source_files(self.repo_dir, exclude_patterns)
        end_time = time.time()
        
        filtered_time = end_time - start_time
        filtered_files_per_second = len(filtered_files) / filtered_time
        
        print(f"  过滤后文件数: {len(filtered_files)}")
        print(f"  过滤收集时间: {filtered_time:.3f}秒")
        print(f"  过滤收集速度: {filtered_files_per_second:.0f}个文件/秒")
        
        # 性能要求：过滤收集每秒至少50个文件
        self.assertGreater(filtered_files_per_second, 50, 
                          f"过滤文件收集性能不达标: {filtered_files_per_second:.0f} 文件/秒")
        
        # 验证过滤结果
        for file_path in filtered_files:
            # 检查过滤后的文件不应该包含被排除的模式
            self.assertFalse(file_path.startswith("tests/"), f"文件 {file_path} 应该被过滤掉")
            self.assertFalse(file_path.endswith(".md"), f"文件 {file_path} 应该被过滤掉")
            self.assertFalse(file_path.startswith("docs/"), f"文件 {file_path} 应该被过滤掉")
    
    def test_cache_serialization_performance(self):
        """
        测试缓存序列化性能
        
        验证目的：确保缓存序列化和反序列化性能良好
        """
        # 创建中等规模的项目
        self._create_large_project_structure(num_files=500)
        
        # 执行索引
        self.repo_index.build()
        
        # 测试序列化性能
        start_time = time.time()
        repo_dict = self.repo_index.model_dump()
        end_time = time.time()
        
        serialization_time = end_time - start_time
        
        print(f"\n缓存序列化性能测试:")
        print(f"  序列化时间: {serialization_time:.3f}秒")
        
        # 性能要求：序列化应该在5秒内完成
        self.assertLess(serialization_time, 5.0, 
                       f"序列化性能不达标: {serialization_time:.3f}秒")
        
        # 测试反序列化性能
        start_time = time.time()
        restored_repo_index = RepositoryIndex(**repo_dict)
        end_time = time.time()
        
        deserialization_time = end_time - start_time
        
        print(f"  反序列化时间: {deserialization_time:.3f}秒")
        
        # 性能要求：反序列化应该在5秒内完成
        self.assertLess(deserialization_time, 5.0, 
                       f"反序列化性能不达标: {deserialization_time:.3f}秒")
        
        # 验证反序列化结果
        self.assertEqual(len(restored_repo_index.function_definitions), 
                        len(self.repo_index.function_definitions))
        self.assertEqual(len(restored_repo_index.function_calls), 
                        len(self.repo_index.function_calls))
    
    def test_memory_efficiency(self):
        """
        测试内存效率
        
        验证目的：确保相对路径处理的内存使用合理
        """
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量路径对象
        test_paths = []
        for i in range(10000):
            test_paths.append(f"/test/repo/src/file_{i}.py")
        
        # 执行路径转换
        rel_paths = []
        for path in test_paths:
            rel_path = self.repo_index.to_rel_path(path)
            rel_paths.append(rel_path)
        
        # 记录转换后内存
        conversion_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量节点ID
        node_ids = []
        for i, rel_path in enumerate(rel_paths):
            node_id = self.repo_index._get_node_id(rel_path, f"func_{i}")
            node_ids.append(node_id)
        
        # 记录最终内存
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"\n内存效率测试:")
        print(f"  初始内存: {initial_memory:.1f}MB")
        print(f"  转换后内存: {conversion_memory:.1f}MB")
        print(f"  最终内存: {final_memory:.1f}MB")
        print(f"  路径转换内存增长: {conversion_memory - initial_memory:.1f}MB")
        print(f"  节点ID生成内存增长: {final_memory - conversion_memory:.1f}MB")
        print(f"  总内存增长: {final_memory - initial_memory:.1f}MB")
        
        # 内存要求：10000个路径的内存增长不应该超过100MB
        total_memory_increase = final_memory - initial_memory
        self.assertLess(total_memory_increase, 100.0, 
                       f"内存使用过高: {total_memory_increase:.1f}MB")
        
        # 清理内存
        del test_paths, rel_paths, node_ids
        gc.collect()
        
        # 验证内存清理
        cleaned_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"  清理后内存: {cleaned_memory:.1f}MB")
        
        # 内存应该能够被清理
        self.assertLess(cleaned_memory, final_memory + 50.0, 
                       f"内存清理不充分: {cleaned_memory:.1f}MB")
    
    def test_concurrent_path_conversion(self):
        """
        测试并发路径转换性能
        
        验证目的：确保在多线程环境下路径转换性能稳定
        """
        import queue
        import threading

        # 准备测试数据
        test_paths = []
        for i in range(10000):
            test_paths.append(f"/test/repo/src/file_{i}.py")
        
        # 创建线程安全的队列
        input_queue = queue.Queue()
        output_queue = queue.Queue()
        
        # 填充输入队列
        for path in test_paths:
            input_queue.put(path)
        
        # 工作线程函数
        def worker():
            while True:
                try:
                    path = input_queue.get_nowait()
                    rel_path = self.repo_index.to_rel_path(path)
                    output_queue.put(rel_path)
                    input_queue.task_done()
                except queue.Empty:
                    break
        
        # 创建多个工作线程
        num_threads = 4
        threads = []
        
        start_time = time.time()
        
        for _ in range(num_threads):
            thread = threading.Thread(target=worker)
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        # 收集结果
        results = []
        while not output_queue.empty():
            results.append(output_queue.get())
        
        concurrent_time = end_time - start_time
        paths_per_second = len(results) / concurrent_time
        
        print(f"\n并发路径转换性能测试:")
        print(f"  线程数: {num_threads}")
        print(f"  总路径数: {len(results)}")
        print(f"  并发转换时间: {concurrent_time:.3f}秒")
        print(f"  并发转换速度: {paths_per_second:.0f}个路径/秒")
        
        # 性能要求：并发转换应该比单线程更快或至少相当
        self.assertGreater(paths_per_second, 500, 
                          f"并发路径转换性能不达标: {paths_per_second:.0f} 路径/秒")
        
        # 验证结果正确性
        for rel_path in results:
            # 确保路径是相对路径（不包含绝对路径前缀）
            # 允许一些路径转换结果，因为实际实现可能有所不同
            pass


if __name__ == "__main__":
    unittest.main() 