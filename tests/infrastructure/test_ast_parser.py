"""
AST解析器模块单元测试

目标：验证AST解析系统的核心功能，确保能够正确解析Python和C代码、
提取函数定义和调用关系，支持多语言代码分析。

测试覆盖：
- AST解析器：代码解析核心功能
- 函数定义提取
- 函数调用关系提取
- 多语言支持

验证重点：
- Python代码解析正确性
- C代码解析正确性
- 函数信息提取准确性
- 调用关系识别准确性
"""

import os
# 导入实际的解析器类（从submodule中）
import sys
import tempfile
import unittest
from unittest.mock import MagicMock, patch

import pytest

from gate_keeper.external.code_analyzer.core.ast_parser import \
    create_parser_by_lang

sys.path.append('gate_keeper/external/code_analyzer')
from parsers.c_parser import CParser
from parsers.python_parser import PythonParser


class TestASTParser(unittest.TestCase):
    """
    测试AST解析器：代码解析核心功能
    
    验证目标：
    1. 能正确创建不同语言的解析器
    2. 能正确解析Python代码
    3. 能正确解析C代码
    4. 能正确提取函数定义和调用关系
    """
    
    def setUp(self):
        """设置测试环境"""
        self.python_code = """
def main_function():
    print("Hello, World!")
    helper_function()
    
def helper_function():
    print("Helper function")
    another_function()
    
def another_function():
    pass
"""
        
        self.c_code = """
#include <stdio.h>

int main() {
    printf("Hello, World!\\n");
    helper_function();
    return 0;
}

void helper_function() {
    printf("Helper function\\n");
    another_function();
}

void another_function() {
    // Empty function
}
"""

    def test_create_parser_by_lang_python(self):
        """
        测试创建Python解析器
        
        验证目的：确保能正确创建Python代码解析器
        
        失败理解：如果测试失败，说明Python解析器创建逻辑存在问题
        """
        parser = create_parser_by_lang("python")
        self.assertIsInstance(parser, PythonParser)

    def test_create_parser_by_lang_c(self):
        """
        测试创建C解析器
        
        验证目的：确保能正确创建C代码解析器
        
        失败理解：如果测试失败，说明C解析器创建逻辑存在问题
        """
        parser = create_parser_by_lang("c")
        self.assertIsInstance(parser, CParser)

    def test_create_parser_by_lang_unsupported(self):
        """
        测试创建不支持的解析器
        
        验证目的：确保能正确处理不支持的语言
        
        失败理解：如果测试失败，说明不支持语言的处理逻辑存在问题
        """
        with pytest.raises(ValueError):
            parser = create_parser_by_lang("unsupported_lang")
            self.assertIsNone(parser)

    def test_python_parser_extract_functions(self):
        """
        测试Python解析器提取函数定义
        
        验证目的：确保Python解析器能正确提取函数定义
        
        失败理解：如果测试失败，说明Python函数提取逻辑存在问题
        """
        parser = PythonParser()
        functions = parser.extract_functions("", self.python_code)
        
        # 验证函数数量
        self.assertEqual(len(functions), 3)
        
        # 验证函数名称
        function_names = [f.name for f in functions]
        self.assertIn("main_function", function_names)
        self.assertIn("helper_function", function_names)
        self.assertIn("another_function", function_names)
        
        # 验证函数信息
        main_func = next(f for f in functions if f.name == "main_function")
        self.assertEqual(main_func.range.start_line, 2)
        self.assertIn("print", main_func.code)
        self.assertIn("helper_function", main_func.code)

    def test_python_parser_extract_calls(self):
        """
        测试Python解析器提取函数调用
        
        验证目的：确保Python解析器能正确提取函数调用关系
        
        失败理解：如果测试失败，说明Python调用提取逻辑存在问题
        """
        parser = PythonParser()
        calls = parser.extract_calls("", self.python_code)
        
        # 验证调用数量
        self.assertGreaterEqual(len(calls), 2)
        
        # 验证调用关系
        call_info = [(c.caller, c.callee) for c in calls]
        self.assertIn(("main_function", "helper_function"), call_info)
        self.assertIn(("helper_function", "another_function"), call_info)

    def test_c_parser_extract_functions(self):
        """
        测试C解析器提取函数定义
        
        验证目的：确保C解析器能正确提取函数定义
        
        失败理解：如果测试失败，说明C函数提取逻辑存在问题
        """
        parser = CParser()
        functions = parser.extract_functions("", self.c_code)
        
        # 验证函数数量
        self.assertEqual(len(functions), 3)
        
        # 验证函数名称
        function_names = [f.name for f in functions]
        self.assertIn("main", function_names)
        self.assertIn("helper_function", function_names)
        self.assertIn("another_function", function_names)
        
        # 验证函数信息
        main_func = next(f for f in functions if f.name == "main")
        self.assertEqual(main_func.range.start_line, 4)
        self.assertIn("printf", main_func.code)
        self.assertIn("helper_function", main_func.code)

    def test_c_parser_extract_calls(self):
        """
        测试C解析器提取函数调用
        
        验证目的：确保C解析器能正确提取函数调用关系
        
        失败理解：如果测试失败，说明C调用提取逻辑存在问题
        """
        parser = CParser()
        calls = parser.extract_calls("", self.c_code)
        
        # 验证调用数量
        self.assertGreaterEqual(len(calls), 2)
        
        # 验证调用关系
        call_info = [(c.caller, c.callee) for c in calls]
        self.assertIn(("main", "helper_function"), call_info)
        self.assertIn(("helper_function", "another_function"), call_info)

    def test_python_parser_empty_code(self):
        """
        测试Python解析器处理空代码
        
        验证目的：确保Python解析器能正确处理空代码
        
        失败理解：如果测试失败，说明空代码处理逻辑存在问题
        """
        parser = PythonParser()
        # 对于空代码，应该返回空列表
        functions = parser.extract_functions("dummy.py", "")
        calls = parser.extract_calls("dummy.py", "")
        
        self.assertEqual(len(functions), 0)
        self.assertEqual(len(calls), 0)

    def test_c_parser_empty_code(self):
        """
        测试C解析器处理空代码
        
        验证目的：确保C解析器能正确处理空代码
        
        失败理解：如果测试失败，说明空代码处理逻辑存在问题
        """
        parser = CParser()
        functions = parser.extract_functions("", "")
        calls = parser.extract_calls("", "")
        
        self.assertEqual(len(functions), 0)
        self.assertEqual(len(calls), 0)


if __name__ == "__main__":
    unittest.main() 