"""Git客户端测试"""
from unittest.mock import patch

import pytest

from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.infrastructure.git.gitee.client import Gitee


class TestGiteeClient:
    """Gitee客户端测试"""

    def test_gitee_client_implements_interface(self):
        """测试Gitee客户端实现了IGitCommentService接口"""
        gitee = Gitee("test_token")
        assert isinstance(gitee, IGitPlatformService)

    def test_gitee_client_initialization(self):
        """测试Gitee客户端初始化"""
        token = "test_token"
        gitee = Gitee(token)
        assert gitee.access_token == token
        assert gitee.api_base == "https://gitee.com/api/v5"
    
    def test_parse_repo_url_https(self):
        """测试HTTPS格式仓库URL解析"""
        gitee = Gitee("test_token")
        owner, repo = gitee._parse_repo_url("https://gitee.com/owner/repo.git")
        assert owner == "owner"
        assert repo == "repo"
    
    def test_parse_repo_url_ssh(self):
        """测试SSH格式仓库URL解析"""
        gitee = Gitee("test_token")
        owner, repo = gitee._parse_repo_url("*************:owner/repo.git")
        assert owner == "owner"
        assert repo == "repo"
    
    def test_parse_repo_url_invalid(self):
        """测试无效仓库URL解析"""
        gitee = Gitee("test_token")
        with pytest.raises(ValueError):
            gitee._parse_repo_url("invalid_url")

    def test_gitee_client_creation(self):
        """测试Gitee客户端创建"""
        gitee = Gitee("test_token")
        assert isinstance(gitee, Gitee)
        assert gitee.access_token == "test_token"

    def test_gitee_interface_compliance(self):
        """测试Gitee客户端接口合规性"""
        gitee = Gitee("test_token")
        interface_methods = [
            'post_discussion_to_mr',
            'get_discussions_of_mr',
            'delete_discussion_of_mr',
            'get_mr_info'
        ]

        for method_name in interface_methods:
            assert hasattr(gitee, method_name), f"Gitee客户端缺少方法: {method_name}"
            method = getattr(gitee, method_name)
            assert callable(method), f"Gitee客户端方法不可调用: {method_name}" 