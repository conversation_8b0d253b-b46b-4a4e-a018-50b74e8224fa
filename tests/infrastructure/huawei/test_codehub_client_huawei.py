"""
华为CodeHub客户端测试
"""
from unittest.mock import MagicMock, patch

import pytest

from gate_keeper.application.interfaces.git_intf import IGitCommentService
from gate_keeper.infrastructure.git.client_factory import GitClientFactory
from gate_keeper.infrastructure.git.codehub.client import CodeHub
from gate_keeper.infrastructure.git.codehub.models.dto import \
    UpdateDiscussionNoteRequest


@pytest.fixture(autouse=True)
def mock_config():
    with patch('gate_keeper.config.config') as mock:
        mock.git_platform = "codehub"
        yield mock


@pytest.mark.huawei
class TestHuaweiCodeHubClient:
    """华为CodeHub客户端测试"""

    def test_codehub_client_implements_interface(self):
        codehub = CodeHub("test_token")
        # 只测试接口实现
        assert hasattr(codehub, "update_discussion_to_mr")
        assert hasattr(codehub, "post_discussion_to_mr")

    def test_update_discussion_to_mr_put_called(self):
        client = CodeHub(access_token="dummy_token")
        project_id = 1
        mr_id = 2
        discussion_id = "abc123"
        update_req = UpdateDiscussionNoteRequest(resolved=False)
        
        # 创建完整的mock响应数据
        mock_user_data = {
            "id": 3,
            "name": "test user",
            "username": "testuser",
            "state": "active",
            "avatar_url": "https://example.com/avatar.jpg",
            "email": "<EMAIL>",
            "name_cn": "测试用户",
            "web_url": "https://example.com/testuser"
        }
        
        mock_note = {
            "id": 101,
            "body": "test note",
            "author": mock_user_data,
            "created_at": "2021-01-04T14:45:29.054+08:00",
            "updated_at": "2021-01-09T09:25:31.010+08:00",
            "noteable_id": 60,
            "noteable_type": "MergeRequest",
            "review_categories": "design",
            "severity": "suggestion",
            "review_modules": "m1,m2",
            "commit_id": None,
            "line": None,
            "file_path": None,
            "assignee": mock_user_data,
            "proposer": mock_user_data,
            "system": False,
            "position": None,
            "resolvable": True,
            "resolved": False,
            "resolved_by": mock_user_data,
            "discussion_id": "abc123"
        }
        
        mock_discussion_data = {
            "id": "abc123",
            "project_id": 1,
            "resolved": False,
            "notes": [mock_note],
            "noteable_type": "MergeRequest",
            "project_full_path": "test-mrs",
            "review_categories": "design",
            "severity": "suggestion",
            "assignee": mock_user_data
        }
        
        mock_get_resp = MagicMock()
        mock_get_resp.status_code = 200
        mock_get_resp.json.return_value = [mock_discussion_data]  # get返回列表
        
        mock_put_resp = MagicMock()
        mock_put_resp.status_code = 200
        mock_put_resp.json.return_value = mock_discussion_data  # put返回单个对象
        
        with patch("requests.get", return_value=mock_get_resp) as mock_get, \
             patch("requests.put", return_value=mock_put_resp) as mock_put:
            result = client.update_discussion_to_mr(project_id, mr_id, discussion_id, update_req)
            assert result is not None
            assert mock_put.called

    @pytest.mark.usefixtures("mock_config")
    def test_post_discussion_to_mr_post_called(self):
        client = CodeHub(access_token="dummy_token")
        project_id = 1
        mr_id = 2
        comment = "test comment"
        
        # 创建完整的mock响应数据
        mock_user_data = {
            "id": 3,
            "name": "test user",
            "username": "testuser",
            "state": "active",
            "avatar_url": "https://example.com/avatar.jpg",
            "email": "<EMAIL>",
            "name_cn": "测试用户",
            "web_url": "https://example.com/testuser"
        }
        
        mock_note = {
            "id": 101,
            "body": comment,
            "author": mock_user_data,
            "created_at": "2021-01-04T14:45:29.054+08:00",
            "updated_at": "2021-01-09T09:25:31.010+08:00",
            "noteable_id": 60,
            "noteable_type": "MergeRequest",
            "review_categories": "design",
            "severity": "suggestion",
            "review_modules": "m1,m2",
            "commit_id": None,
            "line": None,
            "file_path": None,
            "assignee": mock_user_data,
            "proposer": mock_user_data,
            "system": False,
            "position": None,
            "resolvable": True,
            "resolved": False,
            "resolved_by": mock_user_data,
            "discussion_id": "abc123"
        }
        
        mock_response_data = {
            "id": "d1",
            "individual_note": False,
            "notes": [mock_note],
            "project_id": project_id,
            "noteable_type": "MergeRequest",
            "commit_id": None,
            "project_full_path": "test-mrs",
            "a_mode": None,
            "b_mode": None,
            "deleted_file": False,
            "new_file": False,
            "renamed_file": False,
            "resolved": False,
            "archived": False,
            "review_categories": "design",
            "review_categories_cn": "设计",
            "review_categories_en": "design",
            "review_modules": "m1,m2",
            "severity": "suggestion",
            "severity_cn": "建议",
            "severity_en": "suggestion",
            "assignee": mock_user_data,
            "proposer": mock_user_data,
            "issue": None,
            "merge_request_version_params": None,
            "diff_file": None,
            "added_lines": [],
            "removed_lines": []
        }
        
        mock_post_resp = MagicMock()
        mock_post_resp.status_code = 200
        mock_post_resp.json.return_value = mock_response_data
        
        with patch("requests.post", return_value=mock_post_resp) as mock_post, \
             patch("gate_keeper.config.config") as mock_config:
            mock_config.git_platform = "codehub"
            resp = client.post_discussion_to_mr(project_id, mr_id, comment)
            assert resp is not None
            assert resp.id == "d1"
            assert mock_post.called

    @pytest.mark.usefixtures("mock_config")
    def test_codehub_client_creation(self):
        """测试CodeHub客户端创建"""
        codehub = CodeHub("test_token")
        assert isinstance(codehub, CodeHub)
        assert codehub.access_token == "test_token"

    @pytest.mark.usefixtures("mock_config")
    def test_codehub_interface_compliance(self):
        """测试CodeHub接口兼容性"""
        codehub = CodeHub("test_token")
        interface_methods = [
            'get_mr_info',
            'get_discussions_of_mr',
            'post_discussion_to_mr',
            'update_discussion_to_mr',
            'delete_discussion_of_mr'
        ]

        for method_name in interface_methods:
            assert hasattr(codehub, method_name), f"缺少方法: {method_name}"
            method = getattr(codehub, method_name)
            assert callable(method), f"方法不可调用: {method_name}"

    def test_create_comment_service_with_platform_codehub(self):
        """测试明确指定CodeHub平台"""
        import importlib
        with patch('gate_keeper.config.config') as mock_config:
            mock_config.git_platform = "codehub"
            importlib.reload(__import__('gate_keeper.config', fromlist=['config']))
            importlib.reload(__import__('gate_keeper.infrastructure.git.client_factory', fromlist=['GitClientFactory']))
            from gate_keeper.infrastructure.git.client_factory import \
                GitClientFactory
            from gate_keeper.infrastructure.git.codehub.client import CodeHub
            client = GitClientFactory.create_comment_service(
                access_token="test_token"
            )
            assert isinstance(client, CodeHub)

    def test_codehub_platform_supported(self):
        """测试CodeHub平台支持"""
        import importlib
        with patch('gate_keeper.config.config') as mock_config:
            mock_config.git_platform = "codehub"
            importlib.reload(__import__('gate_keeper.config', fromlist=['config']))
            importlib.reload(__import__('gate_keeper.infrastructure.git.client_factory', fromlist=['GitClientFactory']))
            from gate_keeper.infrastructure.git.client_factory import \
                GitClientFactory
            from gate_keeper.infrastructure.git.codehub.client import CodeHub
            client = GitClientFactory.create_comment_service(access_token="test_token")
            assert isinstance(client, CodeHub) 