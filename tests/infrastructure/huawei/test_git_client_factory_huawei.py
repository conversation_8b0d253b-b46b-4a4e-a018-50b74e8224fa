"""
华为CodeHub Git客户端工厂测试
"""

import unittest
from unittest.mock import patch

import pytest

from gate_keeper.infrastructure.git.client_factory import GitClientFactory
from gate_keeper.infrastructure.git.codehub.client import CodeHub


@pytest.mark.huawei
class TestHuaweiGitClientFactory(unittest.TestCase):
    """华为CodeHub Git客户端工厂测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_token = "test_token"
    
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_create_comment_service_with_platform_codehub(self, mock_config):
        """测试明确指定CodeHub平台"""
        mock_config.token = self.test_token
        
        client = GitClientFactory.create_comment_service(
            platform="codehub"
        )
        
        self.assertIsInstance(client, CodeHub)
    
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_create_comment_service_by_codehub_url(self, mock_config):
        """测试根据CodeHub URL自动检测"""
        mock_config.token = self.test_token
        
        codehub_test_urls = [
            "https://codehub.test.com/user/repo",
            "https://codehub.example.com/user/repo"
        ]
        
        for url in codehub_test_urls:
            with self.subTest(url=url):
                try:
                    client = GitClientFactory.create_comment_service(
                        repo_url=url
                    )
                    if client:
                        self.assertIsInstance(client, (CodeHub, type(None)))
                except Exception:
                    pass
    
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_create_comment_service_config_platform_codehub(self, mock_config):
        """测试配置文件指定CodeHub平台"""
        mock_config.token = self.test_token
        mock_config.git_platform = "codehub"
        
        client = GitClientFactory.create_comment_service()
        
        self.assertIsInstance(client, CodeHub)
    
    def test_detect_platform_from_codehub_url(self):
        """测试CodeHub URL平台检测方法"""
        test_cases = [
            ("https://codehub.test.com/user/repo", "codehub"),
            ("https://codehub.example.com/user/repo", "codehub"),
        ]
        
        for url, expected in test_cases:
            with self.subTest(url=url):
                result = GitClientFactory._detect_platform_from_url(url)
                self.assertEqual(result, expected, f"URL {url} 应该返回平台 {expected}，但返回了 {result}")
    
    def test_codehub_platform_supported(self):
        """测试CodeHub平台支持检查"""
        self.assertTrue(GitClientFactory.is_platform_supported("codehub"))
        self.assertTrue(GitClientFactory.is_platform_supported("CODEHUB"))
        
        platforms = GitClientFactory.get_supported_platforms()
        self.assertIn("codehub", platforms)
    
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_create_comment_service_with_custom_token_codehub(self, mock_config):
        """测试使用自定义token创建CodeHub客户端"""
        mock_config.token = "config_token"
        custom_token = "custom_token"
        
        with patch.object(CodeHub, '__init__', return_value=None) as mock_init:
            GitClientFactory.create_comment_service(
                access_token=custom_token,
                platform="codehub"
            )
            
            mock_init.assert_called_with(access_token=custom_token)
    
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_platform_priority_with_codehub(self, mock_config):
        """测试平台选择优先级"""
        mock_config.token = self.test_token
        mock_config.git_platform = "gitee"
        
        client = GitClientFactory.create_comment_service(
            platform="codehub",
            repo_url="https://gitee.com/user/repo"
        )
        
        self.assertIsInstance(client, CodeHub)


if __name__ == "__main__":
    unittest.main() 