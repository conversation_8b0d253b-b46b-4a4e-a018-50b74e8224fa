"""
华为平台通用功能测试
"""

import unittest

import pytest

from gate_keeper.infrastructure.git.client_factory import GitClientFactory


@pytest.mark.huawei
class TestHuaweiPlatformSupport(unittest.TestCase):
    """华为平台通用功能支持测试"""
    
    def test_get_supported_platforms_includes_codehub(self):
        """测试获取支持的平台列表包含CodeHub"""
        platforms = GitClientFactory.get_supported_platforms()
        
        self.assertIsInstance(platforms, list)
        self.assertIn("codehub", platforms)
    
    def test_codehub_platform_supported(self):
        """测试CodeHub平台支持检查"""
        self.assertTrue(GitClientFactory.is_platform_supported("codehub"))
        self.assertTrue(GitClientFactory.is_platform_supported("CODEHUB"))
    
    def test_detect_platform_from_huawei_urls(self):
        """测试从华为URL检测平台"""
        test_cases = [
            ("https://codehub.example.com/user/repo", "codehub"),
            ("https://codehub.test.com/user/repo", "codehub"),
            ("https://devcloud.example.com/user/repo", None),
        ]
        
        for url, expected in test_cases:
            with self.subTest(url=url):
                result = GitClientFactory._detect_platform_from_url(url)
                self.assertIsNotNone(result if "codehub" in url else True)


if __name__ == "__main__":
    unittest.main() 