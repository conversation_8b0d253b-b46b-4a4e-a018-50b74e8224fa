"""
RepositoryIndex模块单元测试

目标：验证仓库索引系统的核心功能，确保能够正确构建代码索引、
提取函数定义和调用关系，构建调用图。

测试覆盖：
- RepositoryIndex：仓库索引核心功能
- 函数定义索引构建
- 函数调用关系索引构建
- 调用图构建
- 缓存机制

验证重点：
- 索引构建正确性
- 调用图构建正确性
- 函数信息提取准确性
- 调用关系识别准确性
- 缓存加载和保存
"""

import os
import tempfile
import unittest
from unittest.mock import MagicMock, patch

from gate_keeper.external.code_analyzer import \
    CacheManager as RepositoryIndexCacheManager
from gate_keeper.external.code_analyzer import (RepositoryIndex,
                                                RepositoryIndexFactory)
from gate_keeper.external.code_analyzer.models.function import (
    Function, FunctionSignature)


class TestRepositoryIndex(unittest.TestCase):
    """
    测试RepositoryIndex：仓库索引核心功能
    
    验证目标：
    1. 能正确构建代码索引
    2. 能正确提取函数定义和调用关系
    3. 能正确构建调用图
    4. 能正确处理缓存机制
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_dir)
        
        # 创建测试文件
        self.python_file = os.path.join(self.repo_dir, "test.py")
        self.python_code = '''
def main_function():
    """Main function docstring"""
    print("Hello, World!")
    helper_function()
    return True

def helper_function():
    """Helper function docstring"""
    print("Helper function")
    another_function()
    return "helper"

def another_function():
    """Another function docstring"""
    print("Another function")
    return 42

class TestClass:
    def class_method(self):
        """Class method docstring"""
        print("Class method")
        return "class"
'''
        
        with open(self.python_file, 'w', encoding='utf-8') as f:
            f.write(self.python_code)
        
        # 创建C测试文件
        self.c_file = os.path.join(self.repo_dir, "test.c")
        self.c_code = '''
#include <stdio.h>

int main() {
    printf("Hello, World!\\n");
    helper_function();
    return 0;
}

void helper_function() {
    printf("Helper function\\n");
    another_function();
}

void another_function() {
    printf("Another function\\n");
}
'''
        
        with open(self.c_file, 'w', encoding='utf-8') as f:
            f.write(self.c_code)
        
        # Mock GitService
        self.mock_git_service = MagicMock()
        self.mock_git_service.get_file_content_by_ref.return_value = self.python_code

    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_repository_index_initialization(self):
        """
        测试RepositoryIndex初始化
        
        验证目的：确保RepositoryIndex能正确初始化
        
        失败理解：如果测试失败，说明RepositoryIndex初始化逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        self.assertTrue(
            os.path.samefile(str(repo_index.repo_dir), self.repo_dir),
            f"repo_dir路径不一致: {repo_index.repo_dir} vs {self.repo_dir}"
        )
        self.assertEqual(repo_index.branch, "main")
        self.assertEqual(repo_index.commit_sha, "test_commit")
        self.assertIsNotNone(repo_index.call_graph)

    def test_index_repo_python_files(self):
        """
        测试索引Python文件
        
        验证目的：确保能正确索引Python文件并提取函数定义
        
        失败理解：如果测试失败，说明Python文件索引逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 验证函数定义
        self.assertGreater(len(repo_index.function_definitions), 0)
        
        # 验证Python函数
        python_functions = []
        for func_list in repo_index.function_definitions.values():
            python_functions.extend([f for f in func_list if f.filepath.endswith('.py')])
        
        self.assertGreater(len(python_functions), 0)
        
        # 验证函数名称
        function_names = [f.name for f in python_functions]
        self.assertIn("main_function", function_names)
        self.assertIn("helper_function", function_names)
        self.assertIn("another_function", function_names)

    def test_index_repo_c_files(self):
        """
        测试索引C文件
        
        验证目的：确保能正确索引C文件并提取函数定义
        
        失败理解：如果测试失败，说明C文件索引逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 验证C函数
        c_functions = []
        for func_list in repo_index.function_definitions.values():
            c_functions.extend([f for f in func_list if f.filepath.endswith('.c')])
        
        self.assertGreater(len(c_functions), 0)
        
        # 验证函数名称
        function_names = [f.name for f in c_functions]
        self.assertIn("main", function_names)
        self.assertIn("helper_function", function_names)
        self.assertIn("another_function", function_names)

    def test_call_graph_construction(self):
        """
        测试调用图构建
        
        验证目的：确保能正确构建调用图
        
        失败理解：如果测试失败，说明调用图构建逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 验证调用图节点
        nodes = list(repo_index.call_graph.nodes())
        self.assertGreater(len(nodes), 0)
        
        # 验证调用图边
        edges = list(repo_index.call_graph.edges())
        self.assertGreater(len(edges), 0)

    def test_get_changed_functions(self):
        """
        测试获取变更函数
        
        验证目的：确保能正确获取变更的函数
        
        失败理解：如果测试失败，说明变更函数获取逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 测试获取变更函数
        changed_functions = repo_index.get_changed_functions(
            file_content=self.python_code,
            file_path=self.python_file,
            changed_lines=[2, 3, 4]  # main_function的行
        )
        
        self.assertGreater(len(changed_functions), 0)
        
        # 验证变更函数包含main_function
        function_names = [f.name for f in changed_functions]
        self.assertIn("main_function", function_names)

    def test_get_affected_functions_by_repo_module(self):
        """
        测试通过模块获取受影响函数
        
        验证目的：确保能正确获取受影响的函数
        
        失败理解：如果测试失败，说明受影响函数获取逻辑存在问题
        """
        from gate_keeper.external.code_analyzer.models.code_range import \
            CodeRange
        from gate_keeper.external.code_analyzer.models.module import CodeModule
        
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 创建测试模块，使用正确的 Function 类
        test_functions = [
            Function.create_simple(
                name="test_func",
                start_line=1,
                end_line=5,
                filepath=self.python_file,
                signature=FunctionSignature(name="test_func", parameters=[], return_type=None)
            )
        ]
        
        # 确保函数有 range 属性
        for func in test_functions:
            if not hasattr(func, 'range'):
                func.range = CodeRange(start_line=func.start_line, end_line=func.end_line)
        
        module = CodeModule(
            path=self.python_file,
            functions=test_functions,
            calls=[]
        )
        
        # 测试获取受影响函数
        affected_functions = repo_index.get_changed_functions(
            file_content=self.python_code,
            file_path=self.python_file,
            changed_lines=[1, 2, 3]
        )
        
        self.assertGreater(len(affected_functions), 0)

    def test_find_function_definition(self):
        """
        测试查找函数定义
        
        验证目的：确保能正确查找函数定义
        
        失败理解：如果测试失败，说明函数定义查找逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 测试查找函数定义
        func_def = repo_index.find_function_definition("main_function")
        self.assertIsNotNone(func_def)
        self.assertEqual(func_def.name, "main_function")

    def test_get_related_calls(self):
        """
        测试获取相关调用
        
        验证目的：确保能正确获取相关调用
        
        失败理解：如果测试失败，说明相关调用获取逻辑存在问题
        """
        repo_index = RepositoryIndex(
            repo_dir=self.repo_dir,
            branch="main",
            commit_sha="test_commit"
        )
        
        repo_index.build()
        
        # 测试获取相关调用
        related_calls = repo_index.get_related_calls("main_function")
        self.assertIsInstance(related_calls, list)

    # def test_to_dict_and_from_dict(self):
    #     """
    #     测试to_dict和from_dict方法
    #     """
    #     repo_index = RepositoryIndex(
    #         repo_dir=self.repo_dir,
    #         branch="main",
    #         commit_sha="test_commit"
    #     )
    #     repo_index.build()
    #     data = repo_index.to_dict()
    #     self.assertIsInstance(data, dict)
    #     repo_index2 = RepositoryIndex.from_dict(data)
    #     self.assertEqual(repo_index2.repo_dir, self.repo_dir)
    #     self.assertEqual(repo_index2.branch, "main")
    #     self.assertEqual(repo_index2.commit_sha, "test_commit")
    #     self.assertIsNotNone(repo_index2.call_graph)


class TestRepositoryIndexFactory(unittest.TestCase):
    """
    测试RepositoryIndexFactory：仓库索引工厂
    
    验证目标：
    1. 能正确创建RepositoryIndex
    2. 能正确处理缓存机制
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_dir)
        
        # Mock GitService
        self.mock_git_service = MagicMock()
        self.mock_git_service.get_branch_commit_sha.return_value = "test_commit"

    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch('gate_keeper.config.config.repo_cache_dir', None)
    def test_get_or_build_no_cache(self):
        """
        测试无缓存时的索引构建
        
        验证目的：确保无缓存时能正确构建索引
        
        失败理解：如果测试失败，说明无缓存时的索引构建逻辑存在问题
        """
        repo_index = RepositoryIndexFactory.get_or_build(
            repo_dir=self.repo_dir,
            branch="main"
        )
        
        self.assertIsInstance(repo_index, RepositoryIndex)
        self.assertTrue(
            os.path.samefile(str(repo_index.repo_dir), self.repo_dir),
            f"repo_dir路径不一致: {repo_index.repo_dir} vs {self.repo_dir}"
        )
        self.assertEqual(repo_index.branch, "main")

    def test_get_or_build_with_cache(self):
        """
        测试有缓存时的索引构建
        
        验证目的：确保有缓存时能正确加载缓存
        
        失败理解：如果测试失败，说明缓存机制存在问题
        """
        # 创建缓存目录
        cache_dir = os.path.join(self.temp_dir, "cache")
        os.makedirs(cache_dir)
        
        with patch('gate_keeper.config.config.repo_cache_dir', cache_dir):
            # 第一次构建，应该创建新索引
            repo_index1 = RepositoryIndexFactory.get_or_build(
                repo_dir=self.repo_dir,
                branch="main"
            )
            
            # 第二次构建，应该从缓存加载
            repo_index2 = RepositoryIndexFactory.get_or_build(
                repo_dir=self.repo_dir,
                branch="main"
            )
            
            self.assertIsInstance(repo_index1, RepositoryIndex)
            self.assertIsInstance(repo_index2, RepositoryIndex)


class TestRepositoryIndexCacheManager(unittest.TestCase):
    """
    测试RepositoryIndexCacheManager：仓库索引缓存管理器
    
    验证目标：
    1. 能正确保存和加载缓存
    2. 能正确处理缓存限制
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_dir = os.path.join(self.temp_dir, "cache")
        os.makedirs(self.cache_dir)
        
        self.cache_manager = RepositoryIndexCacheManager(
            cache_root=self.cache_dir,
            max_repos=2,
            max_branches_per_repo=2,
            max_commits_per_branch=2
        )
        
        # Mock GitService
        self.mock_git_service = MagicMock()
        
        # 创建测试RepositoryIndex
        self.repo_index = RepositoryIndex(
            repo_dir="/test/repo",
            branch="main",
            commit_sha="test_commit"
        )

    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_save_and_load_cache(self):
        """
        测试缓存保存和加载
        
        验证目的：确保能正确保存和加载缓存
        
        失败理解：如果测试失败，说明缓存保存和加载逻辑存在问题
        """
        with tempfile.TemporaryDirectory() as tmpdir:
            repo_path = os.path.join(tmpdir, "repo")
            os.makedirs(repo_path)
            
            # 创建新的 RepositoryIndex 实例，使用正确的路径
            repo_index = RepositoryIndex(
                repo_dir=repo_path,
                branch="main",
                commit_sha="test_commit"
            )
            
            self.cache_manager.save(repo_index)
            loaded_repo_index = self.cache_manager.load(
                repo_dir=repo_path,
                branch="main",
                commit_sha="test_commit"
            )
            self.assertIsNotNone(loaded_repo_index)
            # 在 macOS 上，/var/folders 和 /private/var/folders 是同一个路径
            self.assertTrue(
                os.path.samefile(str(loaded_repo_index.repo_dir), str(repo_index.repo_dir)) or
                str(loaded_repo_index.repo_dir) == str(repo_index.repo_dir),
                f"repo_dir路径不一致: {loaded_repo_index.repo_dir} vs {repo_index.repo_dir}"
            )

    def test_load_nonexistent_cache(self):
        """
        测试加载不存在的缓存
        
        验证目的：确保能正确处理不存在的缓存
        
        失败理解：如果测试失败，说明缓存加载逻辑存在问题
        """
        loaded_repo_index = self.cache_manager.load(
            repo_dir="/nonexistent/repo",
            branch="main",
            commit_sha="nonexistent_commit"
        )
        
        self.assertIsNone(loaded_repo_index)

    def test_cache_limits(self):
        """
        测试缓存限制
        
        验证目的：确保能正确处理缓存限制
        
        失败理解：如果测试失败，说明缓存限制逻辑存在问题
        """
        # 创建多个RepositoryIndex来测试限制
        for i in range(3):
            repo_index = RepositoryIndex(
                repo_dir=f"/test/repo_{i}",
                branch="main",
                commit_sha=f"test_commit_{i}"
            )
            self.cache_manager.save(repo_index)
        
        # 验证缓存目录数量不超过限制
        repo_dirs = [d for d in os.listdir(self.cache_dir) 
                    if os.path.isdir(os.path.join(self.cache_dir, d))]
        self.assertLessEqual(len(repo_dirs), 2)  # max_repos=2


class TestRepositoryIndexFactoryMainFlow(unittest.TestCase):
    """
    RepositoryIndexFactory主流程端到端测试
    - 覆盖缓存miss/hit、结构一致性、exclude_patterns生效等典型场景
    """
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.repo_dir = os.path.join(self.temp_dir, "test_repo")
        os.makedirs(self.repo_dir)
        self.python_file = os.path.join(self.repo_dir, "test.py")
        self.python_code = '''
def foo():
    print("foo")

def bar():
    print("bar")
'''
        with open(self.python_file, 'w', encoding='utf-8') as f:
            f.write(self.python_code)
        self.mock_git_service = MagicMock()
        self.mock_git_service.get_file_content_by_ref.return_value = self.python_code
        self.branch = "main"
        self.commit_sha = "test_commit"

    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_factory_cache_miss_and_hit(self):
        # 保证缓存目录唯一
        import uuid
        cache_dir = os.path.join(self.temp_dir, f"cache_{uuid.uuid4().hex}")
        with patch('gate_keeper.config.config.repo_cache_dir', cache_dir):
            # 1. 首次构建（无缓存）
            repo_index = RepositoryIndexFactory.get_or_build(
                repo_dir=self.repo_dir,
                branch=self.branch,
                commit_sha=self.commit_sha
            )
            self.assertIsNotNone(repo_index)
            self.assertGreater(len(repo_index.function_definitions), 0)
            # 2. 再次构建（应命中缓存）
            repo_index2 = RepositoryIndexFactory.get_or_build(
                repo_dir=self.repo_dir,
                branch=self.branch,
                commit_sha=self.commit_sha
            )
            self.assertIsNotNone(repo_index2)
            # 3. 结构一致
            # self.assertEqual(repo_index.to_dict(), repo_index2.to_dict())

    def test_factory_exclude_patterns(self):
        import uuid
        cache_dir = os.path.join(self.temp_dir, f"cache_{uuid.uuid4().hex}")
        # 新增一个应被排除的文件
        excluded_file = os.path.join(self.repo_dir, "ignore_me.py")
        with open(excluded_file, 'w', encoding='utf-8') as f:
            f.write("def should_not_be_indexed(): pass\n")
        with patch('gate_keeper.config.config.repo_cache_dir', cache_dir):
            repo_index = RepositoryIndexFactory.get_or_build(
                repo_dir=self.repo_dir,
                branch=self.branch,
                commit_sha=self.commit_sha,
                exclude_patterns=["ignore_me.py"]
            )
            # 所有被索引的函数都不应来自 ignore_me.py
            for func_list in repo_index.function_definitions.values():
                for func in func_list:
                    self.assertFalse(func.filepath.endswith("ignore_me.py"))


if __name__ == '__main__':
    unittest.main() 