"""
LLM客户端单元测试

测试目的：验证LLM客户端的基础功能、错误处理和参数验证
测试范围：BaseLLMClient、OllamaClient、OpenAIClient等
"""

import json
import unittest
from typing import Any, Dict
from unittest.mock import MagicMock, Mock, patch

import requests

from gate_keeper.infrastructure.llm.client.base import LLMClient
from gate_keeper.infrastructure.llm.client.ollama import OllamaClient
from gate_keeper.infrastructure.llm.client.openai import OpenAIClient
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters


class TestLLMClient(unittest.TestCase):
    """测试LLM客户端基类"""
    
    def test_llm_client_is_abstract(self):
        """测试LLMClient是抽象基类"""
        # 不能直接实例化抽象基类
        with self.assertRaises(TypeError):
            LLMClient("http://test.com")
    
    def test_llm_client_inheritance(self):
        """测试具体客户端继承自LLMClient"""
        # 测试OpenAI客户端继承
        client = OpenAIClient(api_key="test")
        self.assertIsInstance(client, LLMClient)
        
        # 测试Ollama客户端继承
        client = OllamaClient(base_url="http://localhost:11434")
        self.assertIsInstance(client, LLMClient)


class TestLLMClientIntegration(unittest.TestCase):
    """测试LLM客户端集成功能"""
    
    def test_client_factory_pattern(self):
        """测试客户端工厂模式"""
        from gate_keeper.infrastructure.llm.client.client_factory import \
            LLMFactory

        # 测试创建OpenAI客户端
        openai_client = LLMFactory.create("openai", api_key="test")
        self.assertIsInstance(openai_client, OpenAIClient)
        
        # 测试创建Ollama客户端
        ollama_client = LLMFactory.create("ollama", base_url="http://localhost:11434")
        self.assertIsInstance(ollama_client, OllamaClient)
        
        # 测试无效客户端类型
        with self.assertRaises(ValueError):
            LLMFactory.create("invalid", api_key="test")


if __name__ == '__main__':
    unittest.main() 