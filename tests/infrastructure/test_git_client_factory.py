"""
Git客户端工厂测试模块
"""

import unittest
from unittest.mock import patch

import pytest

from gate_keeper.infrastructure.git.client_factory import GitClientFactory
from gate_keeper.infrastructure.git.gitee.client import Gitee


class TestGitClientFactory(unittest.TestCase):
    """测试GitClientFactory"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_token = "test_token"
    
    @pytest.mark.gitee
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_create_comment_service_config_platform(self, mock_config):
        """测试配置文件指定平台"""
        mock_config.token = self.test_token
        mock_config.git_platform = "gitee"
        
        client = GitClientFactory.create_comment_service()
        
        self.assertIsInstance(client, Gitee)
    
    @pytest.mark.gitee
    @patch('gate_keeper.infrastructure.git.client_factory.config')
    def test_create_comment_service_with_custom_token(self, mock_config):
        """测试使用自定义token"""
        mock_config.token = "config_token"
        mock_config.git_platform = "gitee"  # 设置平台为 gitee
        custom_token = "custom_token"
        
        client = GitClientFactory.create_comment_service(access_token=custom_token)
        
        self.assertIsInstance(client, Gitee)
        self.assertEqual(client.access_token, custom_token)


if __name__ == "__main__":
    unittest.main() 