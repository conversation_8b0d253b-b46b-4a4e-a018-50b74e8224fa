# tests/test_ollama.py
import json
from unittest.mock import Mock, patch

import pytest

from gate_keeper.config import config
from gate_keeper.infrastructure.llm.client.ollama import OllamaClient
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters


@pytest.fixture
def ollama_client():
    return OllamaClient(base_url="http://example.com")

@pytest.fixture
def default_parameters():
    return LLMParameters(model_id="test-model", temperature=0.5, stream=False)

@pytest.fixture
def mock_response():
    response = Mock()
    response.json.return_value = {"response": "test response"}
    return response

def test_initialization_sets_base_url(ollama_client):
    assert ollama_client.base_url == "http://example.com"

@patch("gate_keeper.infrastructure.llm.client.base.LLMClient.send_request")
def test_generate_non_streaming(mock_send_request, ollama_client, mock_response, default_parameters):
    mock_send_request.return_value = mock_response
    result = ollama_client.generate("test prompt", default_parameters)
    assert result == "test response"

@patch("gate_keeper.infrastructure.llm.client.base.LLMClient.send_request")
def test_generate_streaming(mock_send_request, ollama_client):
    mock_response = Mock()
    mock_response.iter_lines.return_value = [b'{"response": "first"}', b'{"response": "second"}']
    mock_send_request.return_value = mock_response

    parameters = LLMParameters(model_id="test-model", temperature=0.5, stream=True)
    result = list(ollama_client.generate("test prompt", parameters))
    assert result == ["first", "second"]

@patch("gate_keeper.infrastructure.llm.client.base.LLMClient.send_request")
def test_generate_streaming_with_json_error(mock_send_request, ollama_client):
    mock_response = Mock()
    mock_response.iter_lines.return_value = [b'invalid json', b'{"response": "valid"}']
    mock_send_request.return_value = mock_response

    parameters = LLMParameters(model_id="test-model", stream=True)
    result = list(ollama_client.generate("test prompt", parameters))
    assert result == ["valid"]

@patch("gate_keeper.infrastructure.llm.client.base.LLMClient.send_request")
def test_generate_with_extra_parameters(mock_send_request, ollama_client):
    mock_response = Mock()
    mock_response.json.return_value = {"response": "test response"}
    mock_send_request.return_value = mock_response

    parameters = LLMParameters(model_id="test-model", extra={"option1": "value1"}, stream=False)
    result = ollama_client.generate("test prompt", parameters)
    assert result == "test response"

@patch("gate_keeper.infrastructure.llm.client.base.LLMClient.send_request")
@pytest.mark.parametrize("debug_mode,expected_log_calls", [(True, 2), (False, 0)])  # 这里改为2
def test_generate_logs_prompt_based_on_debug_mode(mock_send_request, ollama_client, debug_mode, expected_log_calls):
    with patch("gate_keeper.infrastructure.llm.client.ollama.logger") as mock_logger:
        mock_response = Mock()
        mock_response.json.return_value = {"response": "test response"}
        mock_send_request.return_value = mock_response

        parameters = LLMParameters(model_id="test-model", stream=False)
        config.DEBUG = debug_mode
        ollama_client.generate("test prompt", parameters)

        assert mock_logger.debug.call_count == expected_log_calls

@patch("gate_keeper.infrastructure.llm.client.base.LLMClient.send_request")
def test_generate_with_token(mock_send_request, ollama_client):
    mock_response = Mock()
    mock_response.json.return_value = {"response": "test response"}
    mock_send_request.return_value = mock_response

    parameters = LLMParameters(model_id="test-model", stream=False)
    token = "test_token"
    result = ollama_client.generate("test prompt", parameters, token=token)
    assert result == "test response"

    mock_send_request.assert_called_once()
    args, kwargs = mock_send_request.call_args
    assert kwargs['headers']["Authorization"] == f"Bearer {token}"

def test_get_config_returns_correct_provider(ollama_client):
    assert ollama_client.get_config() == {"provider": "Ollama"}
