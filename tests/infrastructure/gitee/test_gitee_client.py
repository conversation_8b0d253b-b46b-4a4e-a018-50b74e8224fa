"""
Gitee客户端测试

测试Gitee Git平台相关功能，包括客户端初始化、接口实现、URL解析等。
"""

from unittest.mock import patch

import pytest

from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.infrastructure.git.client_factory import GitClientFactory
from gate_keeper.infrastructure.git.gitee.client import Gitee


@pytest.mark.gitee
class TestGiteeClient:
    """Gitee客户端测试"""

    def test_gitee_client_implements_interface(self):
        """测试Gitee客户端实现了IGitCommentService接口"""
        gitee = Gitee("test_token")
        assert isinstance(gitee, IGitPlatformService)

    def test_gitee_client_initialization(self):
        """测试Gitee客户端初始化"""
        token = "test_access_token"
        gitee = Gitee(token)
        assert gitee.access_token == token
        assert gitee.api_base == "https://gitee.com/api/v5"

    def test_parse_repo_url_https(self):
        """测试解析HTTPS仓库URL"""
        gitee = Gitee("test_token")
        owner, repo = gitee._parse_repo_url("https://gitee.com/owner/repo.git")
        assert owner == "owner"
        assert repo == "repo"

    def test_parse_repo_url_ssh(self):
        """测试解析SSH仓库URL"""
        gitee = Gitee("test_token")
        owner, repo = gitee._parse_repo_url("*************:owner/repo.git")
        assert owner == "owner"
        assert repo == "repo"

    def test_parse_repo_url_invalid(self):
        """测试解析无效URL"""
        gitee = Gitee("test_token")
        with pytest.raises(ValueError):
            gitee._parse_repo_url("invalid_url")


@pytest.mark.gitee
def test_gitee_platform_supported():
    """测试Gitee平台支持"""
    from gate_keeper.config import config
    config.git_platform = "gitee"
    
    client = GitClientFactory.create_comment_service()
    assert isinstance(client, Gitee) 