#!/usr/bin/env python3
"""
测试Gitee更新评论接口功能
"""

from unittest.mock import Mock, patch

import pytest

from gate_keeper.infrastructure.git.gitee.client import Gitee


class TestGiteeUpdateComment:
    """测试Gitee更新评论功能"""
    
    def setup_method(self):
        """设置测试环境"""
        self.gitee_client = Gitee(access_token="test_token")
    
    def test_update_checkbox_status_unchecked_to_checked(self):
        """测试将未选中的checkbox改为选中状态"""
        original_body = """这是一个评论内容
- [ ] 解决
还有一些其他内容"""
        
        expected_body = """这是一个评论内容
- [x] 解决
还有一些其他内容"""
        
        result = self.gitee_client._update_checkbox_status(original_body, True)
        assert result == expected_body
    
    def test_update_checkbox_status_checked_to_unchecked(self):
        """测试将选中的checkbox改为未选中状态"""
        original_body = """这是一个评论内容
- [x] 解决
还有一些其他内容"""
        
        expected_body = """这是一个评论内容
- [ ] 解决
还有一些其他内容"""
        
        result = self.gitee_client._update_checkbox_status(original_body, False)
        assert result == expected_body
    
    def test_update_checkbox_status_add_new_checkbox(self):
        """测试在没有checkbox的情况下添加新的checkbox"""
        original_body = """这是一个评论内容
还有一些其他内容"""
        
        result = self.gitee_client._update_checkbox_status(original_body, True)
        assert result.endswith("- [x] 解决")
        assert "这是一个评论内容" in result
    
    def test_update_checkbox_status_multiple_checkboxes(self):
        """测试多个checkbox的情况，只更新第一个"""
        original_body = """这是一个评论内容
- [ ] 解决
- [ ] 其他任务
还有一些其他内容"""
        
        result = self.gitee_client._update_checkbox_status(original_body, True)
        lines = result.split('\n')
        
        # 检查第一个checkbox被更新
        assert "- [x] 解决" in lines
        # 检查第二个checkbox保持不变
        assert "- [ ] 其他任务" in lines
    
    def test_update_checkbox_status_with_indentation(self):
        """测试带缩进的checkbox"""
        original_body = """这是一个评论内容
    - [ ] 解决
还有一些其他内容"""
        
        result = self.gitee_client._update_checkbox_status(original_body, True)
        assert "    - [x] 解决" in result
    
    @patch.object(Gitee, '_get')
    @patch.object(Gitee, '_patch')
    def test_update_discussion_to_mr_success(self, mock_patch, mock_get):
        """测试成功更新评论"""
        # 模拟获取当前评论
        mock_get.return_value = {
            'body': '这是一个评论内容\n- [ ] 解决\n还有一些其他内容'
        }
        
        # 模拟更新成功
        mock_patch.return_value = {'id': 123}
        
        result = self.gitee_client.update_discussion_to_mr(
            project_id="test_owner/test_repo",
            mr_id="123",
            discussion_id="456",
            discussion_data={"resolved": True}
        )
        
        assert result is True
        mock_get.assert_called_once()
        mock_patch.assert_called_once()
        
        # 检查patch调用参数
        patch_call_args = mock_patch.call_args
        # 检查第一个参数是URL
        assert len(patch_call_args[0]) >= 1  # URL参数
        # 检查data参数在kwargs中
        assert 'data' in patch_call_args[1]  # data参数
        assert patch_call_args[1]['data']['body'] == '这是一个评论内容\n- [x] 解决\n还有一些其他内容'
    
    @patch.object(Gitee, '_get')
    def test_update_discussion_to_mr_get_failed(self, mock_get):
        """测试获取评论失败的情况"""
        mock_get.return_value = None
        
        result = self.gitee_client.update_discussion_to_mr(
            project_id="test_owner/test_repo",
            mr_id="123",
            discussion_id="456",
            discussion_data={"resolved": True}
        )
        
        assert result is False
    
    @patch.object(Gitee, '_get')
    @patch.object(Gitee, '_patch')
    def test_update_discussion_to_mr_patch_failed(self, mock_patch, mock_get):
        """测试更新评论失败的情况"""
        mock_get.return_value = {
            'body': '这是一个评论内容\n- [ ] 解决'
        }
        mock_patch.return_value = None
        
        result = self.gitee_client.update_discussion_to_mr(
            project_id="test_owner/test_repo",
            mr_id="123",
            discussion_id="456",
            discussion_data={"resolved": True}
        )
        
        assert result is False
    
    def test_update_checkbox_status_edge_cases(self):
        """测试边界情况"""
        # 空内容
        result = self.gitee_client._update_checkbox_status("", True)
        assert result == "- [x] 解决"
        
        # 只有换行符
        result = self.gitee_client._update_checkbox_status("\n\n", False)
        assert result == "\n\n- [ ] 解决"
        
        # 包含特殊字符的checkbox
        original_body = "内容\n- [ ] 解决\n- [x] 其他"
        result = self.gitee_client._update_checkbox_status(original_body, True)
        assert "- [x] 解决" in result
        assert "- [x] 其他" in result  # 其他checkbox保持不变


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 