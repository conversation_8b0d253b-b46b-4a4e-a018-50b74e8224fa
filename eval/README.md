# 统一评估系统

## 概述

这是一个基于统一架构的代码质量评估系统，支持多种评估模式，充分利用了gate_keeper的核心能力。

## 架构设计

### 核心思想
- **统一架构**：评估模式和实际运行模式使用同一套核心代码
- **代码复用**：MR模式和Branch模式完全复用核心用例
- **领域统一**：使用统一的领域对象（TestCase扩展自CodeCheckRule）

### 重构优势
- **消除重复**：移除了重复的规则管理和代码分析逻辑
- **统一维护**：核心功能集中，评估逻辑专注
- **易于扩展**：新增评估模式只需扩展EvaluationService

## 支持的分析模式

### 1. MR模式评估
使用评测集的yaml内容检查指定MR代码
```bash
python eval/run_unified_evaluation.py \
    --dataset eval/datasets/c_evalset_enhanced.yaml \
    --repo-path /path/to/repo \
    --mode mr \
    --mr-id 123 \
    --base-branch master \
    --dev-branch feature
```

### 2. Branch模式评估
使用评测集的yaml内容检查指定Branch全部代码
```bash
python eval/run_unified_evaluation.py \
    --dataset eval/datasets/c_evalset_enhanced.yaml \
    --repo-path /path/to/repo \
    --mode branch \
    --branch develop
```

### 3. 评测集自包含评测
使用评测集中指定的git信息或代码进行检查
```bash
python eval/run_unified_evaluation.py \
    --dataset eval/datasets/c_evalset_enhanced.yaml \
    --repo-path /path/to/repo \
    --mode dataset
```

## 核心特性

### 1. 统一领域对象
- **TestCase**：扩展自CodeCheckRule，支持评估模式特有字段
- **双向转换**：TestCase和CodeCheckRule之间可相互转换
- **Git信息支持**：支持有Git信息和无Git信息的测试用例

### 2. 复用核心用例
- **MR模式**：复用AnalyzeMRUseCase
- **Branch模式**：复用AnalyzeBranchUseCase
- **数据集模式**：使用专门的EvalUseCase

### 3. 统一评估服务
- **EvaluationService**：整合所有评估模式
- **规则管理**：临时替换规则管理器使用评测集规则
- **结果收集**：统一收集分析结果和计算指标

### 4. 智能指标计算
- **混淆矩阵**：TP/FP/FN/TN统计
- **评估指标**：准确率、精确率、召回率、F1分数
- **结果报告**：详细的评估报告和JSON导出

## 文件结构

```
eval/
├── unified_evaluator.py              # 统一评估器（新）
├── run_unified_evaluation.py         # 统一运行脚本（新）
├── datasets/                         # 评测集数据
│   ├── c_evalset.yaml               # 基础评测集
│   ├── c_evalset_enhanced.yaml      # 增强评测集
│   └── c_evalset_simple.yaml        # 简化评测集
├── models.py                         # 评估指标模型
├── progress_manager.py               # 进度管理
└── evaluation_report_service.py      # 报告生成服务
```

## 核心组件

### 1. UnifiedEvaluator
- 专注于结果收集和指标计算
- 使用核心代码进行分析
- 提供统一的接口

### 2. EvaluationService
- 整合所有评估模式
- 复用核心用例
- 管理规则替换

### 3. EvalUseCase
- 处理评测集自包含评测
- 支持有Git信息和无Git信息的测试用例

## 使用示例

### 快速开始
```bash
# 设置环境
export PYTHONPATH=.

# 运行数据集模式评估
python eval/run_unified_evaluation.py \
    --dataset eval/datasets/c_evalset_simple.yaml \
    --repo-path /path/to/repo \
    --mode dataset
```

### 编程接口
```python
from eval.unified_evaluator import UnifiedEvaluator
from gate_keeper.application.service.service_orchestrator_v2 import ServiceOrchestratorV2

# 初始化评估器
orchestrator = ServiceOrchestratorV2(config)
evaluator = UnifiedEvaluator(orchestrator)

# 执行评估
metrics, results, report_service = evaluator.evaluate_dataset(
    dataset=dataset,
    repo_path="/path/to/repo",
    analysis_mode="dataset"
)
```

## 迁移指南

### 从旧版本迁移
1. **更新导入**：使用新的统一评估器
2. **调整参数**：按照新的参数格式调用
3. **处理结果**：结果格式保持一致，但来源更可靠

### 兼容性
- 保持与现有YAML数据集格式的兼容性
- 保持与现有评估指标的兼容性
- 保持与现有报告格式的兼容性

## 技术实现

### 1. 统一架构
- 使用ServiceOrchestratorV2管理服务生命周期
- 复用核心用例避免重复实现
- 统一的领域对象和服务层

### 2. 规则管理
- 临时替换规则管理器使用评测集规则
- 支持规则分组和策略选择
- 自动恢复原始规则管理器

### 3. 结果处理
- 统一的结果收集和指标计算
- 支持多种评估模式的结果合并
- 详细的评估报告生成 