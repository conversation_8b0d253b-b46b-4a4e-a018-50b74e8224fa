"""
YAML数据集加载器

提供YAML格式评测数据集的加载和解析功能
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

from .models import (EvaluationDataset, Example, GitInfo, Principle, Rule,
                     RuleCategory)


class YamlDatasetLoader:
    """YAML数据集加载器"""
    
    @staticmethod
    def load_from_file(file_path: str) -> EvaluationDataset:
        """从YAML文件加载数据集"""
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"数据集文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        return YamlDatasetLoader._parse_dataset(data)
    
    @staticmethod
    def load_from_string(yaml_content: str) -> EvaluationDataset:
        """从YAML字符串加载数据集"""
        data = yaml.safe_load(yaml_content)
        return YamlDatasetLoader._parse_dataset(data)
    
    @staticmethod
    def _parse_dataset(data: Dict[str, Any]) -> EvaluationDataset:
        """解析数据集"""
        # 解析基本信息
        created_at_str = data.get('created_at', '')
        if created_at_str:
            created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
        else:
            created_at = datetime.now()
        
        updated_at_str = data.get('updated_at', '')
        if updated_at_str:
            updated_at = datetime.fromisoformat(updated_at_str.replace('Z', '+00:00'))
        else:
            updated_at = datetime.now()
        
        # 解析规则分类
        rule_categories = []
        for cat_data in data.get('rule_categories', []):
            category = YamlDatasetLoader._parse_rule_category(cat_data)
            rule_categories.append(category)
        
        return EvaluationDataset(
            name=data['name'],
            version=data['version'],
            description=data.get('description', ''),
            created_at=created_at,
            updated_at=updated_at,
            total_rules=data.get('total_rules', 0),
            coverage=data.get('coverage', ''),
            rule_categories=rule_categories
        )
    
    @staticmethod
    def _parse_rule_category(data: Dict[str, Any]) -> RuleCategory:
        """解析规则分类"""
        # 解析直接的原则（兼容旧格式）
        principles = []
        for principle_data in data.get('principles', []):
            principle = YamlDatasetLoader._parse_principle(principle_data)
            principles.append(principle)
        
        # 解析嵌套的规则（新格式）
        rules = []
        for rule_data in data.get('rules', []):
            rule = YamlDatasetLoader._parse_rule(rule_data)
            rules.append(rule)
        
        return RuleCategory(
            id=data['id'],
            index_num=data.get('index_num', 0),
            name=data['name'],
            principles=principles,
            rules=rules
        )
    
    @staticmethod
    def _parse_rule(data: Dict[str, Any]) -> Rule:
        """解析具体规则"""
        principles = []
        for principle_data in data.get('principles', []):
            principle = YamlDatasetLoader._parse_principle(principle_data)
            principles.append(principle)
        
        return Rule(
            id=data['id'],
            index_num=data.get('index_num', 0),
            name=data['name'],
            principles=principles
        )
    
    @staticmethod
    def _parse_principle(data: Dict[str, Any]) -> Principle:
        """解析编码原则"""
        pos_examples = []
        for example_data in data.get('pos_examples', []):
            example = YamlDatasetLoader._parse_example(example_data)
            pos_examples.append(example)
        
        neg_examples = []
        for example_data in data.get('neg_examples', []):
            example = YamlDatasetLoader._parse_example(example_data)
            neg_examples.append(example)
        
        return Principle(
            content=data['content'],
            match_regex=data.get('match_regex', ''),
            suggestion=data.get('suggestion', ''),
            pos_examples=pos_examples,
            neg_examples=neg_examples
        )
    
    @staticmethod
    def _parse_example(data: Dict[str, Any]) -> Example:
        """解析测试用例示例"""
        git_data = data.get('git', {})
        git_info = GitInfo(
            repo_url=git_data.get('repo_url', ''),
            branch=git_data.get('branch', ''),
            commit=git_data.get('commit', ''),
            mr_id=git_data.get('mr_id', ''),
            file_path=git_data.get('file_path', ''),
            function_name=git_data.get('function_name', '')
        )
        
        return Example(
            case_name=data['case_name'],
            description=data['description'],
            expected_answer=data.get('expected_answer', ''),
            think=data.get('think', ''),
            code=data.get('code', ''),
            git=git_info
        )


class YamlDataset:
    """YAML数据集包装器"""
    
    def __init__(self, dataset: EvaluationDataset, file_path: str = None):
        self.dataset = dataset
        self.file_path = file_path
    
    @classmethod
    def load(cls, file_path: str) -> 'YamlDataset':
        """加载YAML数据集"""
        dataset = YamlDatasetLoader.load_from_file(file_path)
        return cls(dataset, file_path)
    
    def get_all_test_cases(self) -> List[Dict[str, Any]]:
        """获取所有测试用例（兼容旧格式）"""
        test_cases = []
        case_id = 1
        
        for category in self.dataset.rule_categories:
            for principle in category.get_all_principles():
                # 处理正面示例
                for pos_example in principle.pos_examples:
                    test_case = {
                        "id": f"TC{case_id:03d}",
                        "type": "positive",
                        "category_id": category.id,
                        "category_name": category.name,
                        "principle_content": principle.content,
                        "case_name": pos_example.case_name,
                        "description": pos_example.description,
                        "expected_answer": pos_example.expected_answer,
                        "think": pos_example.think,
                        "code": pos_example.code,
                        "git": {
                            "repo_url": pos_example.git.repo_url,
                            "branch": pos_example.git.branch,
                            "commit": pos_example.git.commit,
                            "mr_id": pos_example.git.mr_id,
                            "file_path": pos_example.git.file_path,
                            "function_name": pos_example.git.function_name
                        }
                    }
                    test_cases.append(test_case)
                    case_id += 1
                
                # 处理负面示例
                for neg_example in principle.neg_examples:
                    test_case = {
                        "id": f"TC{case_id:03d}",
                        "type": "negative",
                        "category_id": category.id,
                        "category_name": category.name,
                        "principle_content": principle.content,
                        "case_name": neg_example.case_name,
                        "description": neg_example.description,
                        "expected_answer": neg_example.expected_answer,
                        "think": neg_example.think,
                        "code": neg_example.code,
                        "git": {
                            "repo_url": neg_example.git.repo_url,
                            "branch": neg_example.git.branch,
                            "commit": neg_example.git.commit,
                            "mr_id": neg_example.git.mr_id,
                            "file_path": neg_example.git.file_path,
                            "function_name": neg_example.git.function_name
                        }
                    }
                    test_cases.append(test_case)
                    case_id += 1
        
        return test_cases
    
    def export_to_json_format(self) -> Dict[str, Any]:
        """导出为JSON格式（兼容旧格式）"""
        test_cases = self.get_all_test_cases()
        
        return {
            "metadata": {
                "name": self.dataset.name,
                "version": self.dataset.version,
                "description": self.dataset.description,
                "created_at": self.dataset.created_at.isoformat(),
                "total_cases": len(test_cases),
                "total_rules": self.dataset.total_rules,
                "coverage": self.dataset.coverage
            },
            "test_cases": test_cases
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        return self.dataset.get_statistics()
    
    def get_examples_by_category(self, category_id: str) -> List[Example]:
        """根据分类ID获取示例"""
        return self.dataset.get_examples_by_category(category_id)
    
    def get_category_by_id(self, category_id: str) -> Optional[RuleCategory]:
        """根据ID获取分类"""
        return self.dataset.get_category_by_id(category_id) 