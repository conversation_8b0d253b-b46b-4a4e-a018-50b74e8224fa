## 运算异常
### 【规则】： 两个数据做除法或者取模运算时一定要考虑除数是否为0
### 【检查要求】
难点： 这条规则的难点是需要找到全部的上下文，例如：
1.很多时候分母是一个全局变量或宏定义，处于不同的文件，如果宏定义明确写的值不是0，那就不需要进行判断
2.或者是一个函数返回，需要看函数返回的具体逻辑才可以判断是否违规
### 【匹配正则】
```
[a-zA-Z0-9_\->\[\]\.]+\s*(\/|\/=|%(?![a-zA-Z])|%=(?![a-zA-Z]))\s*[a-zA-Z][a-zA-Z0-9_\->\[\]\.]*
```
### 【正例】
#### 【预期结果】无违规 
#### 【代码】
```
UINT32 SafeDivide(UINT32 a, UINT32 b)
{
    if (b == 0) {
        return 0;
    }
    return a / b;
}
```
### 【反例】
#### 【预期结果】违反规范
```
UINT32 error_div(UINT32 x, UINT32 y)
{
    return x / y;
}
```


### 【评测用例】
#### 【预期结果】 不违规
#### 【文件路径】 sm/source/ext_pkt_ident/src/algo/ext_pkt_ident_time_diff.c:ExtPktIdentU_ThreeSigma
#### 【代码】
```
LOCAL INLINE UINT16 ExtPktIdentU_ThreeSigma(UINT64 *dataOut, UINT16 *timeDiff, UINT16 length)
{
    UINT16 i, j;
    UINT8 sigma;
    double std = 0;
    double mean = 0;
    for (i = 0; i < length; i++) {
        mean += timeDiff[i];
    }
    if (mean == 0) {
        return 0;
    }
    mean /= length;
    for (i = 0; i < length; i++) {
        std += (timeDiff[i] - mean) * (timeDiff[i] - mean);
    }
    std /= length;
    std = sqrt(std);
    sigma = (UINT8)(TIME_THREE_SIGMA * std / mean);
    if (sigma < TIME_THREE_SIGMA) {
        sigma = TIME_THREE_SIGMA;
    }
    for (i = 0, j = 0; i < length; i++) {
        if ((timeDiff[i] - mean - sigma * std) < 0) {
            dataOut[j] = timeDiff[i];
            j++;
        }
    }
    return j;
}
```


#### 【评测用例】
#### 【预期结果】违规 ( EPI_GetRtpStudyPktNum的返回值未做0值排除 )
#### 【文件路径】 sm/ext_pkt_ident/src/main/ext_pkt_ident_rtp.c , sm/source/ext_pkt_ident/src/cfg/ext_pkt_ident_cfg.h
#### 【代码】

```
extern UINT16 g_epiRtpStudyPktNum;

LOCAL INLINE UINT16 EPI_GetRtpStudyPktNum()
{
    return g_epiRtpStudyPktNum;
}


VOID ExtPktIdentU_RtpLearningProc(UINT8 uschNo, FI_PROC_CTX *procCtx, EPI_FLOW_NODE *pFlowNode)
{
    BOOL needLearn = VOS_FALSE;
    FI_RTP_TWO_TUPLE_NODE *twoTupleNode = VOS_NULL_PTR;
    if (ExtPktIdentU_RtpGetTwoTupleNode(uschNo, procCtx, pFlowNode, &twoTupleNode) != VOS_TRUE) {
        pFlowNode->frameIdentStatus = E_FRAMEIDENT_NO_NEED;
        return;
    }
    ExtPktIdentU_RtpCollectPktInfo(uschNo, procCtx, pFlowNode, twoTupleNode);
    ExtPktIdentU_RtpKeepOrderProc(uschNo, procCtx, twoTupleNode);
    ExtPktIdentU_RtpCollectTimeDiff(uschNo, procCtx, twoTupleNode);

    if (pFlowNode->learnFlag == VOS_TRUE && (pFlowNode->totalPktNum % EPI_GetRtpStudyPktNum() == 0)) {
        needLearn = VOS_TRUE;
        EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_PERIOD_LEARNING);
    } else if (EXTPKTIDENTU_FIRST_LEARNING_VALID(procCtx, pFlowNode) == VOS_TRUE) {
        needLearn = VOS_TRUE;
        EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_TIME_LEARNING);
    }
    if (needLearn == VOS_TRUE) {
        ExtPktIdentU_RtpSelectIdentAlgo(uschNo, procCtx, pFlowNode);
        ExtPktIdentU_RtpTimeDiffCalc(uschNo, procCtx, pFlowNode);
        if (ExtPktIdentU_LearningResultProc(uschNo, procCtx, pFlowNode) != VOS_TRUE) {
            return;
        }
    }
    if (pFlowNode->learnFlag == VOS_TRUE) {
        if (twoTupleNode->algoType == FI_ALOG_RTP_MARKER) {
            ExtPktIdentU_RtpGetVideoFrameMarker(uschNo, procCtx, pFlowNode, twoTupleNode);
            EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_MARKER);
        } else if (twoTupleNode->algoType == FI_ALOG_TIME_DIFF) {
            ExtPktIdentU_RtpGetVideoFrameTimeDiff(uschNo, procCtx, pFlowNode, twoTupleNode);
            EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_TIME_DIFF);
        }
    }
    ExtPktIdentU_RtpCacheListProc(uschNo, procCtx, twoTupleNode);
    ExtPktIdentU_RtpNoVideoProc(uschNo, procCtx, pFlowNode);
    procCtx->frameIdentAlgo = twoTupleNode->algoType;
}
```

#### 【评测用例】
实际代码中的明显的未做0值判断的问题较少，此用例为构造用例，手动删除了0值判断
#### 【预期结果】违规 ( g_stBwmGlobalShr[bwmSrcType].ssgNum 未做0值判断 )
#### 【文件路径】构造案例
#### 【代码】
```
UINT32 BWM_GetGlobalPercent(UINT16 globalControllerId, BWM_GLOBAL_SOURCE_TYPE_E bwmSrcType)
{
    // 关闭状态不做调整
    if (g_stBwmGlobalShr[bwmSrcType].switchState == BWM_DYANMIC_SWITCH_CPU) {
        return BWM_GLOBAL_PERCENT_PRECISION;
    }

    // 功能开启时/ssg复位启动时，带宽默认按平均值控制
    if (g_stBwmGlobalShr[bwmSrcType].switchState == BWM_DYANMIC_SWITCH_SYSTEM) {
        return BWM_GLOBAL_PERCENT_PRECISION / g_stBwmGlobalShr[bwmSrcType].ssgNum;
    }

    if (globalControllerId >= g_bwmDynamicSize[bwmSrcType]) {
        BWM_GLOBAL_CNTR_ADD(0, BWM_GLOBAL_ERR_ID_GET_PERCENT);
        return BWM_GLOBAL_PERCENT_PRECISION / g_stBwmGlobalShr[bwmSrcType].ssgNum;
    }
    // A桶时，c面定时器正在处理当前桶数据，因此百分比应该获取上一周期数据
    BWM_STAT_BUCKET_S *statBucket = SM_NULL_PTR;
    BWM_GET_LAST_BUCKET(g_stBwmGlobalShr[bwmSrcType].switchState, statBucket, bwmSrcType);

    UINT32 percent = statBucket[globalControllerId].percent;
    return (percent > BWM_GLOBAL_PERCENT_PRECISION) ? BWM_GLOBAL_PERCENT_PRECISION : percent;
}
```0