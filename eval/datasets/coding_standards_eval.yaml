name: 通用编码守则-基础篇
version: 202507072020
description: 基于编码规范_c.md构建的C语言编码规范评测集
created_at: "2025-01-23T11:00:00Z"
total_rules: 35
coverage: "35/35 rules covered (100%)"

rule_categories:
  - id: MEM-01-COPY
    index_num: 1
    name: 内存拷贝
    principles:
      - content: 内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。
        pos_examples:
          - case_name: 拷贝前检查边界
            description: 先确认源长度不大于目标剩余空间
            expected_answer: "没有违规"
            think: "显式校验避免越界"
            code: |
              if (srcLen <= dstMax) {
                  memcpy(dst, src, srcLen);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 直接拷贝
            description: 未检查长度越界
            expected_answer: "违规：可能发生缓冲区越界"
            think: "未进行边界检查，可能导致缓冲区溢出"
            code: |
              memcpy(dst, src, srcLen); /* dst可能不足 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: MEM-02-MOVE
    index_num: 2
    name: 内存搬移
    principles:
      - content: 不要使用memcpy_s函数操作有地址重叠的内存，行为结果未知，推荐使用memmove_s。
        pos_examples:
          - case_name: 使用memmove处理重叠内存
            description: 正确处理内存重叠情况
            expected_answer: "没有违规"
            think: "使用memmove正确处理重叠内存"
            code: |
              memmove(buffer + 5, buffer, 11);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 使用memcpy处理重叠内存
            description: 错误使用memcpy处理重叠内存
            expected_answer: "违规：使用memcpy操作有地址重叠的内存"
            think: "memcpy不能正确处理重叠内存，应使用memmove"
            code: |
              memcpy(buffer + 5, buffer, 11);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: STR-01-BINARY
    index_num: 3
    name: 二进制数据处理
    principles:
      - content: 不要将二进制码流当做字符串操作，可能不存在'\0'结束符，造成访问越界
        pos_examples:
          - case_name: 正确处理二进制数据
            description: 使用memcpy处理二进制数据
            expected_answer: "没有违规"
            think: "使用memcpy处理二进制数据，避免字符串函数"
            code: |
              memcpy(dst, binary_data, data_len);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 错误使用字符串函数处理二进制数据
            description: 将二进制数据当作字符串处理
            expected_answer: "违规：将二进制数据当作字符串处理"
            think: "二进制数据可能没有'\0'结束符，使用字符串函数会越界"
            code: |
              printf("%s\n", (char*)binary_data);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: STR-02-FORMAT
    index_num: 4
    name: 字符串格式化
    principles:
      - content: 使用memcpy_s接口操作字符串时要考虑'\0'携带结束符
        pos_examples:
          - case_name: 正确处理字符串结束符
            description: 考虑'\0'结束符的字符串操作
            expected_answer: "没有违规"
            think: "正确计算字符串长度，包含结束符"
            code: |
              strcpy_s(dst, dst_size, src);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 忽略字符串结束符
            description: 未考虑'\0'结束符的字符串操作
            expected_answer: "违规：使用memcpy操作字符串时未考虑'\0'结束符"
            think: "字符串操作必须考虑结束符，否则可能越界"
            code: |
              memcpy(dst, src, strlen(src));
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: ARR-01-BOUNDS
    index_num: 5
    name: 数组边界检查
    principles:
      - content: 数组下标访问前做有效性校验，确保访问不越界。
        pos_examples:
          - case_name: 数组边界检查
            description: 访问数组前进行边界检查
            expected_answer: "没有违规"
            think: "显式边界检查确保安全访问"
            code: |
              if (index < array_size) {
                  value = array[index];
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 直接访问数组
            description: 未进行边界检查的数组访问
            expected_answer: "违规：数组下标访问前未做有效性校验"
            think: "未检查边界可能导致数组越界访问"
            code: |
              value = array[index];
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: ARR-02-SIZE
    index_num: 6
    name: 大数组定义
    principles:
      - content: 禁止在函数中定义占用超过500字节的数组，该数组会占用栈空间，在多级调用叠加后可能导致栈溢出。
        pos_examples:
          - case_name: 合理大小的栈数组
            description: 使用合理大小的栈数组
            expected_answer: "没有违规"
            think: "栈数组大小合理，不会导致栈溢出"
            code: |
              char small_array[100];
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 超大栈数组
            description: 在函数中定义超过500字节的数组
            expected_answer: "违规：在函数中定义了超过500字节的栈数组"
            think: "大数组占用栈空间，可能导致栈溢出"
            code: |
              char large_array[1000];
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: ARR-03-TYPE
    index_num: 7
    name: 数组类型转换
    principles:
      - content: C语言不同于高级语言，不同类型的数组指针不能通过强转使用，会造成数据错误或者越界读写
        pos_examples:
          - case_name: 正确使用数组类型
            description: 使用正确的数组类型
            expected_answer: "没有违规"
            think: "使用正确的数组类型，避免类型转换错误"
            code: |
              int array[10];
              int value = array[index];
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 错误的数组类型转换
            description: 强制转换不同类型的数组指针
            expected_answer: "违规：不同类型的数组指针不能通过强转使用"
            think: "类型转换可能导致数据错误或越界读写"
            code: |
              int array[10];
              char* ptr = (char*)array;
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: ARR-04-PARAM
    index_num: 8
    name: 数组参数传递
    principles:
      - content: 数组作为函数参数时，禁止在函数内使用sizeof参数计算数组占用空间，必须同时将数组长度作为函数参数传入。
        pos_examples:
          - case_name: 正确传递数组长度
            description: 将数组长度作为参数传递
            expected_answer: "没有违规"
            think: "显式传递数组长度，避免使用sizeof"
            code: |
              void process_array(int* array, int length) {
                  for (int i = 0; i < length; i++) {
                      // 处理数组元素
                  }
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 使用sizeof计算数组长度
            description: 在函数内使用sizeof计算数组长度
            expected_answer: "违规：在函数内使用sizeof参数计算数组占用空间"
            think: "sizeof在函数内无法正确计算数组大小"
            code: |
              void process_array(int* array) {
                  int length = sizeof(array) / sizeof(array[0]);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: RES-01-MALLOC
    index_num: 9
    name: 内存申请接口
    principles:
      - content: 不要使用malloc和free等申请系统内存，系统内存预留有限，并且出现内存泄漏时难以定位。
        pos_examples:
          - case_name: 使用推荐的内存接口
            description: 使用mem_api.h中的接口
            expected_answer: "没有违规"
            think: "使用带有内存统计的接口，便于定位问题"
            code: |
              void* ptr = UTIL_MallocDyn(100);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 使用禁止的系统内存接口
            description: 使用malloc申请系统内存
            expected_answer: "违规：使用了禁止的系统内存接口malloc"
            think: "系统内存接口难以定位内存泄漏问题"
            code: |
              void* ptr = malloc(100);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: RES-02-LEAK
    index_num: 10
    name: 内存泄漏防护
    principles:
      - content: 申请临时内存资源注意在函数执行完成后释放，提前退出的分支也要考虑释放。
        pos_examples:
          - case_name: 正确释放内存
            description: 在所有退出路径都释放内存
            expected_answer: "没有违规"
            think: "确保所有分支都正确释放内存"
            code: |
              void* ptr = UTIL_MallocDyn(100);
              if (error) {
                  UTIL_FreeDyn(ptr);
                  return;
              }
              // 使用ptr
              UTIL_FreeDyn(ptr);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 提前退出未释放内存
            description: 提前退出分支未释放内存
            expected_answer: "违规：申请的内存未在提前退出分支中释放"
            think: "提前退出时未释放内存，造成内存泄漏"
            code: |
              void* ptr = UTIL_MallocDyn(100);
              if (error) {
                  return; /* 未释放内存 */
              }
              UTIL_FreeDyn(ptr);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: FUN-01-RECURSION
    index_num: 11
    name: 递归函数限制
    principles:
      - content: 禁止使用递归函数，递归函数调用栈深度不可控，容易造成栈溢出。
        pos_examples:
          - case_name: 使用迭代替代递归
            description: 使用循环实现相同功能
            expected_answer: "没有违规"
            think: "使用迭代避免递归调用栈问题"
            code: |
              int factorial(int n) {
                  int result = 1;
                  for (int i = 1; i <= n; i++) {
                      result *= i;
                  }
                  return result;
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 使用递归函数
            description: 使用递归实现功能
            expected_answer: "违规：使用了递归函数"
            think: "递归函数调用栈深度不可控，容易造成栈溢出"
            code: |
              int factorial(int n) {
                  if (n <= 1) return 1;
                  return n * factorial(n - 1);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: FUN-02-POINTER
    index_num: 12
    name: 指针参数检查
    principles:
      - content: 函数参数为指针类型时，必须检查指针的有效性，避免空指针访问。
        pos_examples:
          - case_name: 检查指针有效性
            description: 函数开始检查指针参数
            expected_answer: "没有违规"
            think: "检查指针有效性避免空指针访问"
            code: |
              void process_data(int* data) {
                  if (data == NULL) {
                      return;
                  }
                  // 处理数据
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 未检查指针有效性
            description: 直接使用指针参数
            expected_answer: "违规：函数参数为指针类型时未检查指针的有效性"
            think: "未检查指针可能导致空指针访问"
            code: |
              void process_data(int* data) {
                  *data = 100; /* 可能访问空指针 */
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: ARITH-01-OVERFLOW
    index_num: 13
    name: 算术溢出检查
    principles:
      - content: 进行算术运算前，必须检查操作数是否会发生溢出。
        pos_examples:
          - case_name: 检查算术溢出
            description: 运算前检查溢出
            expected_answer: "没有违规"
            think: "检查溢出避免计算结果错误"
            code: |
              if (a > 0 && b > 0 && a > INT_MAX - b) {
                  // 处理溢出
              } else {
                  result = a + b;
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 未检查算术溢出
            description: 直接进行算术运算
            expected_answer: "违规：进行算术运算前未检查操作数是否会发生溢出"
            think: "未检查溢出可能导致计算结果错误"
            code: |
              int result = a + b; /* 可能溢出 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: ARITH-02-DIVISION
    index_num: 14
    name: 除零检查
    principles:
      - content: 进行除法运算前，必须检查除数是否为零。
        pos_examples:
          - case_name: 检查除零
            description: 除法前检查除数
            expected_answer: "没有违规"
            think: "检查除零避免程序崩溃"
            code: |
              if (divisor != 0) {
                  result = dividend / divisor;
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 未检查除零
            description: 直接进行除法运算
            expected_answer: "违规：进行除法运算前未检查除数是否为零"
            think: "未检查除零可能导致程序崩溃"
            code: |
              int result = dividend / divisor; /* 可能除零 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: SEC-01-BUFFER
    index_num: 15
    name: 缓冲区安全
    principles:
      - content: 使用安全的字符串函数，避免缓冲区溢出。
        pos_examples:
          - case_name: 使用安全字符串函数
            description: 使用strcpy_s等安全函数
            expected_answer: "没有违规"
            think: "使用安全函数避免缓冲区溢出"
            code: |
              strcpy_s(dst, dst_size, src);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 使用不安全字符串函数
            description: 使用strcpy等不安全函数
            expected_answer: "违规：使用了不安全的字符串函数"
            think: "不安全函数可能导致缓冲区溢出"
            code: |
              strcpy(dst, src); /* 可能溢出 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: SEC-02-INPUT
    index_num: 16
    name: 输入验证
    principles:
      - content: 对用户输入进行严格验证，防止恶意输入。
        pos_examples:
          - case_name: 验证用户输入
            description: 对输入进行验证
            expected_answer: "没有违规"
            think: "验证输入防止恶意数据"
            code: |
              if (input_length > 0 && input_length < MAX_LENGTH) {
                  process_input(input);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 未验证用户输入
            description: 直接使用用户输入
            expected_answer: "违规：对用户输入未进行严格验证"
            think: "未验证输入可能导致安全问题"
            code: |
              process_input(user_input); /* 未验证 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: PRIV-01-LOG
    index_num: 17
    name: 隐私日志保护
    principles:
      - content: 日志中不得包含用户隐私信息，如密码、身份证号等。
        pos_examples:
          - case_name: 保护隐私信息
            description: 日志中不包含敏感信息
            expected_answer: "没有违规"
            think: "保护用户隐私信息"
            code: |
              LOG_INFO("用户登录成功，用户ID: %d", user_id);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 泄露隐私信息
            description: 日志中包含敏感信息
            expected_answer: "违规：日志中包含用户隐私信息"
            think: "日志中不应包含密码等敏感信息"
            code: |
              LOG_INFO("用户登录，密码: %s", password);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: COMP-01-MACRO
    index_num: 18
    name: 编译宏使用
    principles:
      - content: 使用编译宏时，必须考虑不同平台的兼容性。
        pos_examples:
          - case_name: 平台兼容的宏使用
            description: 考虑平台兼容性
            expected_answer: "没有违规"
            think: "使用平台兼容的宏定义"
            code: |
              #ifdef _WIN32
                  #define PATH_SEPARATOR "\\"
              #else
                  #define PATH_SEPARATOR "/"
              #endif
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 平台相关的宏使用
            description: 未考虑平台兼容性
            expected_answer: "违规：使用编译宏时未考虑不同平台的兼容性"
            think: "硬编码平台相关宏可能导致兼容性问题"
            code: |
              #define PATH_SEPARATOR "\\" /* Windows专用 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: LOG-01-LEVEL
    index_num: 19
    name: 日志级别使用
    principles:
      - content: 根据日志的重要程度选择合适的日志级别。
        pos_examples:
          - case_name: 正确使用日志级别
            description: 根据重要性选择级别
            expected_answer: "没有违规"
            think: "使用合适的日志级别"
            code: |
              LOG_ERROR("系统错误: %s", error_msg);
              LOG_INFO("用户操作: %s", action);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 错误使用日志级别
            description: 使用不合适的日志级别
            expected_answer: "违规：未根据日志的重要程度选择合适的日志级别"
            think: "错误使用日志级别影响日志分析"
            code: |
              LOG_DEBUG("系统错误: %s", error_msg); /* 错误应该用ERROR级别 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: STRUCT-01-ALIGN
    index_num: 20
    name: 结构体对齐
    principles:
      - content: 结构体成员排列时，考虑内存对齐，提高访问效率。
        pos_examples:
          - case_name: 合理的内存对齐
            description: 考虑内存对齐的结构体
            expected_answer: "没有违规"
            think: "合理排列成员提高访问效率"
            code: |
              struct aligned_struct {
                  int a;      /* 4字节 */
                  char b;     /* 1字节 */
                  int c;      /* 4字节 */
              };
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 不合理的内存对齐
            description: 未考虑内存对齐的结构体
            expected_answer: "违规：结构体成员排列时未考虑内存对齐"
            think: "不合理的内存对齐影响访问效率"
            code: |
              struct unaligned_struct {
                  char a;     /* 1字节 */
                  int b;      /* 4字节，可能不对齐 */
                  char c;     /* 1字节 */
              };
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

  - id: VAR-01-TYPE
    index_num: 21
    name: 变量类型选择
    principles:
      - content: 根据变量的取值范围选择合适的类型，避免溢出。
        pos_examples:
          - case_name: 合适的变量类型
            description: 根据取值范围选择类型
            expected_answer: "没有违规"
            think: "选择合适的类型避免溢出"
            code: |
              uint32_t counter = 0; /* 无符号32位 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 不合适的变量类型
            description: 使用可能溢出的类型
            expected_answer: "违规：未根据变量的取值范围选择合适的类型"
            think: "类型选择不当可能导致溢出"
            code: |
              char counter = 0; /* 可能溢出 */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: "" 