name: 通用编码守则-基础篇
version: 202507072020
rule_categories:
  - id: MEM-01
    name: 内存操作
    index_num: 1
    rules:
    - id: MEM-01-COPY
      index_num: 1
      name: 内存拷贝
      principles:
      - content: 内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。
        match_regex: "memcpy\\s*\\(|memcpy_s\\s*\\(|memmove\\s*\\(|memmove_s\\s*\\("
        suggestion: 检查所有内存拷贝操作前是否有边界校验，重点关注：1)源数据长度是否超过目标缓冲区大小；2)目标缓冲区是否有足够空间；3)是否处理了边界值情况；4)优先使用安全函数如memcpy_s
        pos_examples:
          - case_name: 拷贝前检查边界
            description: 先确认源长度不大于目标剩余空间
            expected_answer: "没有违规"
            think: "通过条件判断srcLen <= dstMax确保源数据长度不超过目标缓冲区剩余空间，有效防止缓冲区越界写入，这是内存拷贝的标准安全做法"
            code: |
              UINT32 ProcessData(UINT8 *dst, UINT32 dstMax, const UINT8 *src, UINT32 srcLen)
              {
                  if (dst == NULL || src == NULL) {
                      return ERR_NULL_PTR;
                  }
                  if (srcLen <= dstMax) {
                      memcpy(dst, src, srcLen);
                      return srcLen;
                  } else {
                      LOG_ERROR("Source length %u exceeds destination capacity %u", srcLen, dstMax);
                      return ERR_BUFFER_OVERFLOW;
                  }
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 直接拷贝
            description: 未检查长度越界
            expected_answer: "违规：可能发生缓冲区越界"
            think: "直接调用memcpy而未进行边界检查，当srcLen大于dst缓冲区大小时会发生越界写入，可能覆盖相邻内存区域，导致程序崩溃或安全漏洞"
            code: |
              VOID UnsafeDataCopy(UINT8 *dst, const UINT8 *src, UINT32 srcLen)
              {
                  memcpy(dst, src, srcLen); 
                  ProcessResult(dst);
              }
    - id: MEM-01-MOVE
      index_num: 1
      name: 内存搬移
      principles:
      - content: 不要使用memcpy_s函数操作有地址重叠的内存,行为结果未知,推荐使用memove_s。
        match_regex: "memcpy_s\\s*\\(.*\\)|memmove_s\\s*\\(.*\\)"
        suggestion: 检查内存拷贝操作的源和目标地址是否可能重叠，重点关注：1)源和目标指针是否指向同一块内存区域；2)是否存在地址范围重叠的可能；3)重叠情况下必须使用memmove_s而非memcpy_s；4)注意数组内元素移动等常见重叠场景
        pos_examples:
          - case_name: 重叠区域用memmove_s
            description: 源目地址重叠时使用memmove_s
            expected_answer: "没有违规"
            think: "memmove_s函数专门设计用于处理源和目标内存区域重叠的情况，它通过内部临时缓冲区或反向拷贝等机制确保数据正确传输，避免了memcpy_s在重叠区域的未定义行为"
            code: |
              UINT32 ShiftBuffer(UINT8 *buffer, UINT32 bufLen, UINT32 offset, UINT32 dataLen)
              {
                  if (buffer == NULL || offset + dataLen > bufLen) {
                      return ERR_INVALID_PARAM;
                  }
                  errno_t ret = memmove_s(buffer + offset, bufLen - offset, buffer, dataLen);
                  if (ret != EOK) {
                      LOG_ERROR("memmove_s failed with error %d", ret);
                      return ERR_MEMORY_OP;
                  }
                  return OK;
              }
        neg_examples:
          - case_name: 重叠仍用memcpy_s
            description: 地址重叠却调用memcpy_s
            expected_answer: "违规：应使用memmove_s"
            think: "当源和目标内存区域存在重叠时，memcpy_s的行为是未定义的，可能导致数据损坏。在这种情况下必须使用memmove_s来保证数据的正确性"
            code: |
              UINT32 CompactArray(UINT32 *array, UINT32 arrayLen, UINT32 removeIndex)
              {
                  if (array == NULL || removeIndex >= arrayLen) {
                      return ERR_INVALID_PARAM;
                  }

                  UINT32 moveLen = (arrayLen - removeIndex - 1) * sizeof(UINT32);
                  if (moveLen > 0) {
                      memcpy_s(array + removeIndex, moveLen, array + removeIndex + 1, moveLen);
                  }
                  return arrayLen - 1;
              }
  - id: STR-01
    name: 字符串操作
    rules:
    - id: STR-01-FOMAT
      index_num: 1
      name: 注意数据格式
      principles:
      - content: 不要将二进制码流当做字符串操作,可能不存在'\0'结束符,造成访问越界
        match_regex: "strlen\\s*\\(|strcpy\\s*\\(|strcat\\s*\\(|strcmp\\s*\\(|strstr\\s*\\("
        suggestion: 检查是否对二进制数据使用了字符串函数，重点关注：1)数据来源是否为二进制流或网络数据包；2)是否使用strlen等依赖'\0'的函数；3)应使用明确长度的内存操作函数；4)确保数据长度通过其他方式获得而非依赖终止符
        pos_examples:
          - case_name: 按字节处理二进制
            description: 使用memcpy_s按字节拷贝二进制数据
            expected_answer: "没有违规"
            think: "二进制数据不保证以'\0'结尾，使用memcpy_s并显式指定长度是正确做法，避免了字符串函数可能导致的越界访问"
            code: |
              UINT32 ProcessBinaryData(UINT8 *dst, UINT32 dstLen, const UINT8 *binData, UINT32 dataLen)
              {
                  if (dst == NULL || binData == NULL) {
                      return ERR_NULL_PTR;
                  }
                  if (dataLen > dstLen) {
                      return ERR_BUFFER_TOO_SMALL;
                  }
                  errno_t ret = memcpy_s(dst, dstLen, binData, dataLen);
                  if (ret != EOK) {
                      return ERR_COPY_FAILED;
                  }
                  return dataLen;
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""   
        neg_examples:
          - case_name: 把二进制当字符串
            description: 用strlen计算二进制长度
            expected_answer: "违规：可能因无'\0'而越界"
            think: "strlen函数会一直搜索直到遇到'\0'字符，但二进制数据中可能不包含'\0'或'\0'出现在数据中间，导致strlen返回错误长度或访问越界"
            code: |
              UINT32 ParseBinaryPacket(const UINT8 *binData, UINT32 maxLen)
              {
                  // 错误：将二进制数据当作字符串处理
                  UINT32 len = strlen((const char*)binData); /* binData可能不含'\0'，导致越界 */

                  if (len > maxLen) {
                      return ERR_PACKET_TOO_LARGE;
                  }

                  // 后续处理可能基于错误的长度值
                  return ProcessPacketData(binData, len);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""     
    - id: STR-01-FUNC
      index_num: 2
      name: 字符串操作函数
      principles:
      - content: 使用memcpy_s接口操作字符串时要考虑'\0'携带结束符
        match_regex: "memcpy_s\\s*\\(.*\\)|strcpy_s\\s*\\(.*\\)|strcat_s\\s*\\(.*\\)"
        suggestion: 使用strcpy_s、strcat_s等字符串操作接口处理字符串,接口内部已经考虑了结束符'\0'处理
        pos_examples:
          - case_name: 使用strcpy_s复制字符串
            description: 自动处理'\0'终止符
            expected_answer: "没有违规"
            think: "strcpy_s函数专门为字符串设计，会自动复制源字符串的'\0'终止符，确保目标字符串正确终止，无需手动处理终止符"
            code: |
              UINT32 CopyUserName(CHAR *dst, UINT32 dstLen, const CHAR *src)
              {
                  if (dst == NULL || src == NULL) {
                      return ERR_NULL_PTR;
                  }
                  errno_t ret = strcpy_s(dst, dstLen, src);
                  if (ret != EOK) {
                      LOG_ERROR("String copy failed, error: %d", ret);
                      return ERR_STRING_COPY;
                  }

                  return strlen(dst);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""               
        neg_examples:
          - case_name: memcpy_s复制字符串
            description: 未预留'\0'位置
            expected_answer: "违规：未考虑终止符"
            think: "使用memcpy_s复制字符串时只复制了strlen(src)个字符，没有包含'\0'终止符，导致目标字符串不完整，可能引起后续字符串操作错误"
            code: |
              UINT32 BuildMessage(CHAR *dst, UINT32 dstLen, const CHAR *prefix, const CHAR *content)
              {
                  if (dst == NULL || prefix == NULL || content == NULL) {
                      return ERR_NULL_PTR;
                  }

                  UINT32 prefixLen = strlen(prefix);
                  memcpy_s(dst, dstLen, prefix, prefixLen);

                  return strcat_s(dst + prefixLen, dstLen - prefixLen, content);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""               
  - id: ARR-01
    index_num: 3
    name: 数组操作
    rules:
    - id: ARR-01-INDEX
      index_num: 1
      name: 下标访问
      principles:
        - content: 数组下标访问前做有效性校验,确保访问不越界。
          match_regex: "\\[\\s*\\w+\\s*\\]|\\[\\s*\\w+\\s*[+\\-*/]\\s*\\w+\\s*\\]"
          suggestion: 检查所有数组下标访问是否有边界校验，重点关注：1)下标变量是否在有效范围内；2)是否检查了下标小于数组长度；3)动态计算的下标是否有溢出风险；4)循环中的数组访问边界条件
          pos_examples:
            - case_name: 先检查再访问
              description: 访问前校验下标范围
              expected_answer: "没有违规"
              think: "通过条件判断idx < ARRAY_LEN(buf)确保数组下标在有效范围内，这是防止数组越界访问的标准做法，避免了访问未分配内存导致的程序崩溃"
              code: |
                UINT32 GetArrayElement(const UINT32 *buf, UINT32 bufSize, UINT32 idx, UINT32 *outVal)
                {
                    if (buf == NULL || outVal == NULL) {
                        return ERR_NULL_PTR;
                    }
                    if (idx < bufSize) {
                        *outVal = buf[idx];
                        return OK;
                    } else {
                        LOG_ERROR("Array index %u out of bounds (size: %u)", idx, bufSize);
                        return ERR_INDEX_OUT_OF_BOUNDS;
                    }
                }
              git:
                repo_url: ""
                branch: ""
                commit: ""
                mr_id: ""
          neg_examples:
            - case_name: 直接访问
              description: 未校验下标直接取值
              expected_answer: "违规：可能越界访问"
              think: "直接使用idx作为数组下标而未进行边界检查，当idx大于等于数组大小时会访问未分配的内存，可能导致程序崩溃或读取到垃圾数据"
              code: |
                UINT32 ProcessArrayData(const UINT32 *buf, UINT32 idx)
                {
                    UINT32 val = buf[idx]; 
                    return val * 2 + 1;
                }
              git:
                repo_url: ""
                branch: ""
                commit: ""
                mr_id: ""
    - id: ARR-01-DEFINE
      index_num: 2  
      name: 数组定义
      principles:
      - content: 避免使用二级以及以上指针结构操作多维数组,容易造成代码晦涩难懂,操作出错,建议通过结构体分级定义或者每一级单独使用变量表达。
        match_regex: "\\*\\s*\\*|\\[\\s*\\]\\s*\\[\\s*\\]"
        suggestion: 检查多维数组操作的复杂度，重点关注：1)是否使用了二级或更高级指针；2)指针运算是否复杂难懂；3)建议用结构体封装或单独变量替代；4)确保指针操作的可读性和维护性
        pos_examples:
        - case_name: 推荐用法
          description: 推荐用法,单独定义偏移后的指针操作
          expected_answer: "没有违规"        
          think: "代码遵循了规则建议，通过单独定义偏移后的指针变量来操作多维数组，避免了复杂的二级指针语法，提高了代码可读性和维护性，降低了出错概率"
          code: |
            ```
            g_apSMTempVal[0] = (VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UINT8 *),"g_saDecryptBlockPtr");
            UINT8 *curInst = (UINT8 *)((UINT8 *)g_apSMTempVal[0] + sizeof(UINT8 *) * inst);*curInst = (UINT8 *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UINT8), "g_saDecryptBlockmem");
            ```
          git:
            repo_url: ""
            branch: ""
            commit: ""
            mr_id: ""           
          
        neg_examples:
        - case_name: 使用了二级数组指针
          description: 如下代码申请全部线程内存头指针数组,使用全局变量保存内存起始地址。然后再按照线程实例申请线程级实际内存,写入线程级头地址时操作错误。编码上使用了二级数组指针
          expected_answer: "违规：使用了二级指针操作数组"
          think: "代码中使用了UCHAR **g_saDecryptBlockPtr二级指针来操作多维数组，这种写法晦涩难懂，容易在指针运算和内存管理中出错，应该通过结构体或单独变量来简化"
          code: |
            ```
            UCHAR **g_saDecryptBlockPtr=(UCHAR **)(&g_apSMTempVal[0]);

            UINT32 SA_EgnInitQuicDecryptBlockMem_P005(UCHAR instId)
            {
              UINT32 arryIdx = instId & (MAX_THREAD_NUM_IN_NODE - 1);
              if (*g saDecryptBlockPtr != NULL) {
                if (g_saDecryptBlockPtr[arryIdx] != NULL) {
                    UCHAR *instMemAddr = (UCHAR *)g_saDecryptBlockPtr[arryIdx];
                    (VOID)memset_s((VOID *))instMemAddr, SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE * sizeof(UCHAR), 0,SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE * sizeof(UCHAR));
                    return VOS_OK;
                }
                return VOS_ERR;
              }
              *g_saDecryptBlockPtr = (SM_VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UCHAR *),"g_saDecryptBlockptn");

              if (*g_saDecryptBlockPtr == NULL) {
                SA_CNTR_ADD(instId, SA_U_ERR_PATCH_02);
                return VOS_ERR;
              }

              g_saDecryptBlockPtr[arryIdx] = (SM_VOID *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UCHAR *),g_seuecrypcBlockmem);

              if (g_saDecryptBlockPtr[arryIdx] == NULL) {
                SA_CNTR_ADD(instId, SA_U_ERR_PATCH_02);
                return vos_ERR;
              }
              UCHAR *instMemAddr = g_saDecryptBlockPtr[arryIdx];
              (VOID)memset_s((VOID *)instMemAddr, SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE * sizeof(UCHAR), ,
                  SA QUIC_ DECRYPT_BLOCK_BUFF_SIZE * siZeof(UCHAR);
              SA_CNTR_ADD(instId, SA_U_NORM_PATCH_05);
              return vos_oK;
            }
            ```
          git:
            repo_url: ""
            branch: ""
            commit: ""
            mr_id: ""                  
        - case_name: 使用指向数组指针操作-hard negative
          description: 使用指向数组指针操作,理解也不太直观,不推荐
          expected_answer: "虽然没有使用二级及以上指针,但是没有遵循规则建议通过结构体分级定义或者每一级单独使用变量表达"
          think: "虽然没有使用二级及以上指针,但是规则中建议通过结构体分级定义或者每一级单独使用变量表达,为了谨慎起见应该视作不通过"
          code: |
            ```
            g_apSMTempVal[0] = (VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UINT8 *), "g_saDecryptBlockPtr");
            UINT8* (*instArray)[MAX_THREAD_NUM_IN_NODE] = (UINT8* (*)[MAX_THREAD_NUM_IN_NODE])(g_apSMTempVal[0]);
            (*instArray)[inst] = (UINT8 *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UINT8), "g_saDecryptBlockmem");
            ```
          git:
            repo_url: ""
            branch: ""
            commit: ""
            mr_id: ""

      - content: 禁止在函数中定义占用超过500字节的数组(例如:char array[1000]),该数组会占用栈空间,在多级调用叠加后可能导致栈溢出,超大数组使用动态申请或者全局变量。
        match_regex: "\\w+\\s+\\w+\\[\\s*\\d+\\s*\\]|CHAR\\s+\\w+\\[|UINT8\\s+\\w+\\[|UINT32\\s+\\w+\\["
        suggestion: 检查函数内栈数组的大小，重点关注：1)计算数组占用字节数是否超过500字节；2)考虑数据类型大小(如UINT32是4字节)；3)大数组应使用动态分配或全局变量；4)注意多级函数调用的栈累积效应
        pos_examples:
          - case_name: 动态申请大缓冲区
            description: 使用 UTIL_MallocDyn 申请堆内存避免大栈数组
            expected_answer: "没有违规"
            think: 函数内未出现大于 500 B 的栈数组,符合规范
            code: |
              VOID Process()
              {
                  UINT8 *buf = (UINT8 *)UTIL_MallocDyn(0, 1024);
                  if (buf == NULL) return;
                  /* 使用 buf */
                  UTIL_FreeDyn(0, buf);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 函数内定义大栈数组
            description: 在栈上定义 1 KiB 缓冲区
            expected_answer: "违规：函数内定义超过 500 字节栈数组"
            think: 1024 B > 500 B,可能导致栈溢出
            code: |
              VOID Process()
              {
                  CHAR buf[1024]; 
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

    - id: ARR-01-TYPE
      index_num: 3
      name: 数组类型
      principles:
      - content: C语言不同于高级语言,不同类型的数组指针不能通过强转使用,会造成数据错误或者越界读写
        match_regex: "\\(\\s*\\w+\\s*\\*\\s*\\)|\\(\\s*\\w+\\s*\\)\\s*\\w+"
        suggestion: 检查数组指针的类型转换，重点关注：1)是否存在不同类型指针间的强制转换；2)转换后的访问是否会超出原数组边界；3)数据类型大小不匹配的风险；4)保持指针类型与数组元素类型一致
        pos_examples:
        - case_name: 正确保持类型一致
          description: 保持数组元素类型与指针类型完全一致
          expected_answer: "没有违规"
          think: "UINT8数组使用UINT8指针访问,类型匹配,无强转"
          code: |
            UINT8 buffer[16];
            UINT8 *p = buffer;
            p[0] = 0xAA;
        neg_examples:
        - case_name: 强制类型转换
          description: 将UINT8数组强转为UINT32指针后访问
          expected_answer: "违规：强转类型后访问数组,可能导致越界"
          think: "UINT8[16]被当作UINT32*使用,一次访问4字节,越界风险"
          code: |
            UINT8 buffer[16];
            UINT32 *p = (UINT32 *)buffer;
            p[3] = 0x11223344;     
    - id: ARR-01-LENGTH
      index_num: 4
      name: 数组类型
      principles:
      - content: 数组作为函数参数时禁止在函数内使用sizeof计算数组占用空间,必须同时将数组长度作为函数参数传入。
        match_regex: "sizeof\\s*\\(\\s*\\w+\\s*\\)|ARRAY_LEN\\s*\\("
        suggestion: 计算元素个数使用宏#define ARRAY_LEN(arr) (sizeof(arr)/sizeof((arr)[0]))
        pos_examples:
        - case_name: 显式传入长度
          description: 通过额外参数传入数组长度
          expected_answer: "没有违规"
          think: "函数内不依赖sizeof(arr),符合规范"
          code: |
            void PrintArray(const UINT32 arr[], UINT32 len)
            {
                for (UINT32 i = 0; i < len; ++i) {
                    printf("%u\n", arr[i]);
                }
            }
        neg_examples:
        - case_name: 使用sizeof计算参数数组长度
          description: 在函数体内使用sizeof(arr)计算长度
          expected_answer: "违规：数组作为参数时sizeof(arr)返回指针大小"
          think: "形参arr退化为指针,sizeof(arr)不等于数组总字节数"
          code: |
            void PrintArray(UINT32 arr[])
            {
                UINT32 len = sizeof(arr) / sizeof(arr[0]);
                for (UINT32 i = 0; i < len; ++i) {
                    printf("%u\n", arr[i]);
                }
            }
  - id: RES-04
    index_num: 4
    name: 资源使用
    rules:
    - id: RES-04-ALLOC
      index_num: 1
      name: 内存申请
      principles:
      - content: |
          不要使用malloc和free等申请系统内存,系统内存预留有限,并且出现内存泄漏时难以定位。使用带有内存统计接口,定义在mem_api.h,有临时内存、用户级内存、非用户级内存、动态内存等申请接口,请仔细阅读接口功能描述,根据实际场景选择。
          以下接口不要使用:
          ```
          PS_MemAlloc
          PSP_Malloc
          VOS_MemAlloc_F
          VOS_MemAllocAlignN
          PS_MemAllocAlign16
          MEM_ShmAllocByName
          MEM_PrivMAllocByName
          MEM_ShrMemAlloc
          PS_MemFree
          PSP_Free
          PS_ShrMemFree
          ```
        match_regex: "malloc\\s*\\(|free\\s*\\(|PS_MemAlloc|PSP_Malloc|VOS_MemAlloc|UTIL_Malloc"
        suggestion: 使用带有内存统计接口,如UTIL_MallocDyn、UTIL_MallocFix等。
        neg_examples:
        - case_name: 使用系统malloc
          description: 直接调用malloc申请内存
          expected_answer: "违规:使用了禁止的malloc接口"
          think: "未使用UTIL_*系列接口,且malloc不在白名单"
          code: |
            void *p = malloc(256);
            if (p) free(p);
    - id: RES-04-FREE
      index_num: 2
      name: 内存释放
      principles:
      - content: 申请临时内存资源注意在函数执行完成后释放,提前退出的分支也要考虑释放。
        match_regex: "UTIL_MallocTmp|UTIL_FreeTmp|UTIL_MallocDyn|UTIL_FreeDyn|return\\s+\\w+;"
        suggestion: 检查内存申请和释放的配对，重点关注：1)每个内存申请是否都有对应的释放；2)所有函数退出路径(包括异常分支)是否都释放了内存；3)提前return语句前是否释放资源；4)使用goto统一释放或RAII模式
        pos_examples:
        - case_name: 所有路径均释放
          description: 提前返回前释放临时内存
          expected_answer: "没有违规"
          think: "所有return前都调用了UTIL_FreeTmp"
          code: |
            VOID Foo()
            {
                VOID *tmp = UTIL_MallocTmp(0, 100);
                if (tmp == NULL) return;
                if (SomeCheck() != OK) {
                    UTIL_FreeTmp(0, tmp);
                    return;
                }
                UTIL_FreeTmp(0, tmp);
            }
        neg_examples:
        - case_name: 正常分支未释放
          description: 仅异常分支释放,正常分支遗漏
          expected_answer: "违规：正常分支未释放临时内存"
          think: "tmpmem在成功路径未释放"
          code: |
            UINT32 Query(UINT32 id, CHAR *out, UINT32 outLen)
            {
                VOID *tmp = UTIL_MallocTmp(0, 64);
                if (tmp == NULL) return ERR;
                if (GetById(id, tmp, 64) != OK) {
                    UTIL_FreeTmp(0, tmp);
                    return ERR;
                }
                strcpy_s(out, outLen, (CHAR*)tmp);
                return OK; 
            }
      - content: 申请消息、表项等资源在使用前退出的分支要考虑释放。
        suggestion: 检查消息和表项资源的生命周期管理，重点关注：1)资源申请后是否在所有退出分支都有释放；2)异常处理路径中的资源清理；3)嵌套资源申请的释放顺序；4)避免资源泄露导致系统资源耗尽
        extra_resource: https://codehub-y.huawei.com/5gcore/up/domain/common/merge_requests/4432  
    - id: RES-04-CURSOR
      index_num: 3
      name: 游标释放
      principles:
      - content: |
          游标是指在遍历配置DB、DDF、CDDB等记录时平台接口内会自动申请一个资源记录遍历过程的信息,如果使用next接口依次遍历到最后返回失败,平台会自动释放游标。如果遍历中间退出时需要业务释放游标,否则就会发生游标泄露。游标总数有限,如果全部泄露光后调用查询接口将失败,影响业务正常运行。目前需要考虑释放游标的接口:
          (1) 配置DB:

          DBApiQueryFirst_Adapt
          DBApiQueryNext_Adapt(遍历接口)
          DBApiQueryEnd_Adapt(释放接口)

          OMPROXY_CdbQueryFirst
          OMPROXY_CdbQueryNext(遍历接口)
          OMPROXY_CdbQueryEnd(释放接口)

          (2) DDF:

          UPDBADPT_QueryFirst
          UPDBADPT_QueryNext(遍历接口)
          UPDBADPT_QueryEnd(释放接口)

          (3) lua:

          spt_Query(查询接口)
          spt_EndQuery(释放接口)
        match_regex: "QueryFirst|QueryNext|QueryEnd|spt_Query|spt_EndQuery|break\\s*;|return\\s*;"
        suggestion: 检查游标的申请和释放配对，重点关注：1)QueryFirst成功后是否在所有退出路径都调用了QueryEnd；2)循环中break或return前是否释放游标；3)异常处理分支中的游标清理；4)lua中spt_Query成功后必须调用spt_EndQuery

        neg_examples:
        - case_name: 中途返回未释放游标
          description: 遍历中途异常退出未调QueryEnd
          expected_answer: "违规：游标泄露"
          think: "异常分支未调用OMPROXY_CdbQueryEnd释放游标"
          code: |
            VOID Iterate()
            {
                CURSOR_S cursor;
                if (OMPROXY_CdbQueryFirst(&cursor) != OK) return;
                while (Next()) {
                    if (Check()) break; 
                }
            }      
      - content: lua中只要spt_Query查询成功无论spt_GetNextRecord接口是否失败都要使用spt_EndQuery进行释放。
        suggestion: 检查lua查询接口的使用，重点关注：1)spt_Query成功后是否无条件调用spt_EndQuery；2)不依赖spt_GetNextRecord的返回值决定是否释放；3)确保在所有代码路径都有释放调用；4)异常情况下的资源清理
        extra_resource: http://3ms.huawei.com/hi/group/2823529/wiki_5322487.html  
    - id: RES-04-IMPLICIT
      index_num: 4
      name: 隐式内存释放
      principles:
      - content: strdup、vasprint接口内有申请内存操作,使用后要进行释放
        suggestion: 检查隐式内存申请函数的使用，重点关注：1)strdup、vasprintf等函数返回的指针是否有对应的free调用；2)函数调用失败时的处理；3)所有使用这些函数的代码路径都要有内存释放；4)避免忘记释放导致内存泄露
      - content: 使用默认参数调用pthread_create,线程退出时没有调用pthread_join造成栈内存泄露
        suggestion: 检查线程创建和回收的配对，重点关注：1)每个pthread_create是否有对应的pthread_join或pthread_detach；2)线程属性是否正确设置为detached状态；3)避免僵尸线程导致的资源泄露；4)确保线程生命周期管理正确
      - content: |
          cJSON_Parse等接口中会创建cjson对象,调用这个接口以及其衍生的接口（CFG_ParseJsonFile等）解析json格式后需要调用cJSON_Delete释放cjson对象。
          ```
          cJSON_CreateObject
          cJSON_CreateArray
          cJSON_CreateString
          cJSON_CreateNumber
          cJSON_CreateBool
          cJSON_CreateNull
          cJSON_Parse

          # 以上接口创建的对象使用后需要调用cJSON_Delete进行释放
          ```
        suggestion: 检查cJSON对象的创建和释放，重点关注：1)所有cJSON_Create*和cJSON_Parse创建的对象是否都调用了cJSON_Delete；2)嵌套JSON对象的释放顺序；3)异常分支中的JSON对象清理；4)避免重复释放或忘记释放
        extra_resource: https://codehub-y.huawei.com/5gcore/mse/vas/ssu/merge_requests/1581  
  - id: FUN-05
    index_num: 5
    name: 函数实现
    rules:
    - id: FUN-05-DEF
      index_num: 1
      name: 函数定义
      principles:
      - content: 定义递归函数,容易造成死循环或栈溢出。
        match_regex: "\\w+\\s*\\(.*\\)\\s*\\{[^}]*\\w+\\s*\\([^)]*\\w+[^)]*\\)"
        suggestion: 检查函数是否使用递归实现，重点关注：1)函数是否调用自身；2)递归是否有明确的终止条件；3)递归深度是否可控；4)建议用循环或迭代方式替代递归；5)考虑栈空间限制
        pos_examples:
        - case_name: 迭代实现阶乘
          description: 使用循环代替递归,控制栈深度
          expected_answer: "没有违规"
          think: "未使用递归,避免栈溢出风险"
          code: |
            UINT32 factorial(UINT32 n)
            {
                UINT32 result = 1;
                for (UINT32 i = 1; i <= n; ++i) {
                    result *= i;
                }
                return result;
            }
        neg_examples:
        - case_name: 递归阶乘实现
          description: 使用递归方式实现阶乘
          expected_answer: "违规：定义递归函数"
          think: "函数factorial递归调用自身,可能导致栈溢出"
          code: |
            UINT32 factorial(UINT32 n)
            {
                if (n == 0) return 1;
                return n * factorial(n - 1);
            }
      - content: 函数中循环定义要考虑其循环次数,避免运行次数过大造成死循环。
        match_regex: "for\\s*\\(|while\\s*\\(|do\\s*\\{.*\\}\\s*while"
        suggestion: 检查循环的终止条件和次数限制，重点关注：1)循环是否有明确的退出条件；2)循环变量是否会正确递增/递减；3)是否设置了最大循环次数限制；4)while(1)等无限循环是否有break语句；5)避免条件永远为真的情况
        pos_examples:
        - case_name: 带最大上限的循环
          description: 循环次数受MAX_COUNT限制
          expected_answer: "没有违规"
          think: "循环上限固定,防止死循环"
          code: |
            #define MAX_COUNT 1000
            VOID process()
            {
                for (UINT32 i = 0; i < MAX_COUNT; ++i) {
                    /* do work */
                }
            }
        neg_examples:
        - case_name: 无退出条件的while循环
          description: 循环条件恒真,可能死循环
          expected_answer: "违规：循环次数无限制,可能造成死循环"
          think: "while(1)无退出条件"
          code: |
            VOID loop()
            {
                while (1) {
                    /* do something */
                }
            }
      - content: 循环变量类型范围不要低于阈值类型范围,禁止使用低于32bit的类型循环变量。
        match_regex: "for\\s*\\(\\s*(UCHAR|UINT8|UINT16|USHORT|CHAR|SHORT|UINT32|ULONG)\\s+\\w+"
        suggestion: 检查循环变量的数据类型，重点关注：1)循环变量是否为32位或以上类型；2)避免使用UCHAR、UINT8、UINT16等小于32位的类型；3)考虑循环次数是否可能超出变量类型范围；4)推荐使用UINT32或ULONG作为循环变量
        pos_examples:
        - case_name: 使用UINT32作为循环变量
          description: 循环变量为32位无符号整型
          expected_answer: "没有违规"
          think: "i为UINT32,满足≥32bit要求"
          code: |
            VOID iterate(UINT32 times)
            {
                for (UINT32 i = 0; i < times; ++i) {
                    /* do work */
                }
            }
        neg_examples:
        - case_name: 使用UCHAR作为循环变量
          description: 循环变量为8位无符号整型
          expected_answer: "违规：循环变量类型低于32bit"
          think: "i为UCHAR类型,仅8bit"
          code: |
            VOID iterate(UINT32 times)
            {
                for (UCHAR i = 0; i < times; ++i) {
                    /* do work */
                }
            }
    - id: FUN-05-CALLBACK
      index_num: 2
      name: 回调函数
      principles:
      - content: 注册给平台的回调函数是在平台的线程执行的,与业务不在同一线程,在该回调函数中直接做业务处理,与业务线程存在访问冲突的风险。例如绝对定时器VOS_AbstmrCbkInit。
        suggestion: 检查回调函数的线程安全性，重点关注：1)回调函数是否直接操作全局变量或共享数据；2)是否通过消息队列等机制与业务线程通信；3)避免在回调中进行复杂的业务逻辑处理；4)确保跨线程访问的数据有适当的同步机制
        pos_examples:
          - case_name: 回调仅发消息
            description: 在回调中仅向业务线程发消息,不直接操作共享数据
            expected_answer: "没有违规"
            think: "通过消息队列异步处理,避免跨线程直接访问"
            code: |
              VOID TimerCallback(UINT32 timerId)
              {
                  MSG_S msg = {0};
                  msg.id   = MSG_TIMER_EXPIRY;
                  msg.data = timerId;
                  SendMsgToBusinessThread(&msg);
              }
        neg_examples:
          - case_name: 回调直接改全局变量
            description: 在回调函数内直接修改业务全局变量
            expected_answer: "违规：回调线程直接操作业务共享数据"
            think: "g_counter可能被业务线程并发访问,存在竞态条件"
            code: |
              UINT32 g_counter = 0;
              VOID TimerCallback(UINT32 timerId)
              {
                  g_counter++; 
              }
        extra_resource: https://3ms.huawei.com/hi/group/1008251/wiki_6353724.html    
    - id: FUN-05-PARAM
      index_num: 3
      name: 函数参数
      principles:
      # ---------- 原则 1 ----------
      - content: 禁止函数中修改指针参数的值。C语言中指针类型参数本质属于局部变量,注意不要与C++引用混淆,直接修改参数的值不会改变原指针值。
        match_regex: "\\w+\\s*\\+\\+|\\w+\\s*--|\\+\\+\\s*\\w+|--\\s*\\w+|\\w+\\s*\\+=|\\w+\\s*-="
        suggestion: 检查函数内是否修改了指针参数，重点关注：1)指针参数是否有++、--、+=、-=等修改操作；2)如需移动指针应使用临时变量；3)理解C语言指针参数是值传递的特性；4)避免代码意图不明确的指针操作
        pos_examples:
          - case_name: 通过临时指针遍历
            description: 函数内部使用临时指针变量移动,不修改实参指针
            expected_answer: "没有违规"
            think: "仅修改局部变量tmp,未修改实参buf"
            code: |
              void fill_zero(UINT8 *buf, UINT32 len)
              {
                  UINT8 *tmp = buf;
                  while (len--) {
                      *tmp++ = 0;
                  }
              }
        neg_examples:
          - case_name: 直接修改指针参数
            description: 函数内对指针形参进行自增
            expected_answer: "违规：修改了指针参数的值"
            think: "buf++只改变了形参局部副本,调用方实参未变,且代码意图易混淆"
            code: |
              void fill_zero(UINT8 *buf, UINT32 len)
              {
                  while (len--) {
                      *buf++ = 0;   
                  }
              }
        extra_resource: https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/DTS2022073000911    
      # ---------- 原则 2 ----------
      - content: 调用函数时严格区分使用指针还是指针地址,避免错误的操作内存,导致无法预期的行为。
        match_regex: "&\\w+|\\*\\*\\w+|\\(\\w+\\s*\\*\\*\\)|\\w+\\(.*&\\w+.*\\)"
        suggestion: 检查函数调用时指针参数的传递方式，重点关注：1)需要修改指针本身时是否传递了指针的地址(&ptr)；2)函数参数类型与实参类型是否匹配；3)避免不必要的强制类型转换；4)理解一级指针和二级指针的使用场景
        pos_examples:
          - case_name: 正确传递指针
            description: 需要修改指针指向时传递指针的地址
            expected_answer: "没有违规"
            think: "传入&ptr,函数通过二级指针修改原指针"
            code: |
              void alloc_buffer(UINT8 **pptr, UINT32 len)
              {
                  *pptr = UTIL_MallocDyn(0, len);
              }
              UINT8 *p = NULL;
              alloc_buffer(&p, 256);
        neg_examples:
          - case_name: 混淆指针与指针地址
            description: 应传二级指针却传一级指针
            expected_answer: "违规：未区分指针与指针地址"
            think: "调用方传p而非&p,函数内会把p的内容当地址使用,导致异常"
            code: |
              void alloc_buffer(UINT8 **pptr, UINT32 len)
              {
                  *pptr = UTIL_MallocDyn(0, len);
              }

              UINT8 *p = NULL;
              alloc_buffer((UINT8 **)&p, 256);  
        extra_resource: https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/DTS2022051216813    
    - id: FUN-05-FUNC
      index_num: 4
      name: 函数功能
      principles:
      - content: 实现IPv4处理时要同步考虑IPv6实现。
        match_regex: "inet_addr|inet_pton|AF_INET|AF_INET6|IPv4|IPv6"
        suggestion: 检查IP地址处理函数的完整性，重点关注：1)新增IPv4功能时是否同步实现了IPv6支持；2)是否使用了inet_pton等同时支持IPv4/IPv6的函数；3)数据结构是否能容纳IPv6地址；4)避免后续IPv6改造的返工成本
        pos_examples:
          - case_name: IPv4/IPv6 统一处理接口
            description: 新增地址解析接口同时支持 IPv4 与 IPv6
            expected_answer: "没有违规"
            think: "函数内部根据地址族区分处理,IPv6 逻辑已同步实现"
            code: |
              INT32 ParseIP(const CHAR *ipStr, UINT8 *buf, UINT32 bufLen, UINT16 *addrFamily)
              {
                  if (inet_pton(AF_INET, ipStr, buf) == 1) {
                      *addrFamily = AF_INET;
                      return 0;
                  }
                  if (inet_pton(AF_INET6, ipStr, buf) == 1) {
                      *addrFamily = AF_INET6;
                      return 0;
                  }
                  return -1;
              }
        neg_examples:
          - case_name: 仅处理 IPv4 地址
            description: 仅使用 inet_addr 解析 IPv4,无 IPv6 分支
            expected_answer: "违规：未同步考虑 IPv6 实现"
            think: "后续若需支持 IPv6 必须返工,增加成本"
            code: |
              UINT32 ParseIPv4Only(const CHAR *ipStr)
              {
                  return (UINT32)inet_addr(ipStr); 
              }
  # 6. 通信
  - id: COMM-01
    index_num: 6
    name: 通信
    rules:
    # 6.1 数据格式
    - id: COMM-01-ENDIAN
      index_num: 1
      name: 数据格式
      principles:
      - content: 跨系统通信需要考虑字节序转换
        match_regex: "htonl\\s*\\(|htons\\s*\\(|ntohl\\s*\\(|ntohs\\s*\\(|Send\\s*\\(|Recv\\s*\\("
        suggestion: 检查网络通信中的字节序处理，重点关注：1)发送数据前是否使用htonl/htons转换为网络序；2)接收数据后是否使用ntohl/ntohs转换为主机序；3)多字节整数在网络传输中的字节序一致性；4)跨平台兼容性考虑
        pos_examples:
          - case_name: 发送前转网络序
            description: 发送前使用htonl转换整型
            expected_answer: "没有违规"
            think: "显式字节序转换,跨平台正确"
            code: |
              UINT32 value = 0x12345678;
              UINT32 netValue = htonl(value);
              Send(&netValue, sizeof(netValue));
        neg_examples:
          - case_name: 直接发送主机序
            description: 未做任何字节序转换
            expected_answer: "违规：未考虑跨系统字节序差异"
            think: "主机序与网络序可能不同,接收端解析错误"
            code: |
              UINT32 value = 0x12345678;
              Send(&value, sizeof(value));

    # 6.2 合法性校验
    - id: COMM-01-VALIDATE
      index_num: 2
      name: 合法性校验
      principles:
      - content: 进程间通信时,接收方需要进行相关数据的校验,避免数据非法,导致处理异常。
        match_regex: "len\\s*<|version\\s*!=|sizeof\\s*\\(.*HDR|->\\w+\\[\\d+\\]"
        suggestion: 检查进程间通信的数据校验，重点关注：1)消息长度是否小于最小头部大小；2)版本号、魔数等关键字段是否校验；3)变长数据的长度字段是否合理；4)防止恶意或损坏数据导致的越界访问
        pos_examples:
          - case_name: 收到消息后校验长度与版本
            description: 先检查消息长度和版本号
            expected_answer: "没有违规"
            think: "防止非法数据导致越界或逻辑错误"
            code: |
              VOID HandleMsg(const MSG_HDR *hdr, UINT32 len)
              {
                  if (len < sizeof(MSG_HDR) || hdr->version != MSG_VERSION) {
                      return;
                  }
                  /* 继续处理 */
              }
        neg_examples:
          - case_name: 直接使用消息字段
            description: 未校验长度直接解析
            expected_answer: "违规：未对进程间数据进行校验"
            think: "len可能小于sizeof(MSG_HDR)"
            code: |
              VOID HandleMsg(const MSG_HDR *hdr, UINT32 len)
              {
                  UINT32 value = hdr->payload[0];
              }

      - content: 对于可选信元,其对应数据可能无效,使用前需要做有效性检查。
        match_regex: "has\\w+|is\\w+Valid|\\w+Present|if\\s*\\(.*->has"
        suggestion: 检查可选字段的有效性验证，重点关注：1)使用可选字段前是否检查了对应的has标志位；2)可选字段的默认值处理；3)避免使用未初始化或无效的可选数据；4)确保可选字段的逻辑一致性
        pos_examples:
          - case_name: 可选字段先判有效性
            description: 检查hasFlag标志再使用flag值
            expected_answer: "没有违规"
            think: "避免使用未提供的可选字段"
            code: |
              VOID ProcOpt(const MSG_S *msg)
              {
                  if (msg->hasFlag) {
                      UseFlag(msg->flag);
                  }
              }
        neg_examples:
          - case_name: 直接使用可选信元
            description: 未判断hasX直接使用x
            expected_answer: "违规：未检查可选信元有效性"
            think: "x字段可能未被填充"
            code: |
              VOID ProcOpt(const MSG_S *msg)
              {
                  UseFlag(msg->flag); 
              }
  # 7. 锁
  - id: LOCK-01
    index_num: 7
    name: 锁
    rules:
    # 7.1 锁类型
    - id: LOCK-01-MUTEX
      index_num: 1
      name: 锁类型
      principles:
      - content: 转发面禁止使用mutex类型锁
        match_regex: "pthread_mutex_|pthread_spin_|spinlock"
        suggestion: 检查转发面代码的锁类型选择，重点关注：1)转发面(FIFO线程)是否使用了mutex锁；2)应使用spinlock等非阻塞锁机制；3)避免转发面线程被阻塞影响性能；4)确保锁的选择符合实时性要求
        pos_examples:
          - case_name: 转发面使用spinlock
            description: 使用pthread_spinlock_t
            expected_answer: "没有违规"
            think: "spinlock适用于转发面,无阻塞"
            code: |
              pthread_spinlock_t g_lock;
              VOID FifoTask()
              {
                  pthread_spin_lock(&g_lock);
                  /* critical section */
                  pthread_spin_unlock(&g_lock);
              }
        neg_examples:
          - case_name: 转发面使用mutex
            description: 直接调用pthread_mutex_lock
            expected_answer: "违规：转发面使用mutex"
            think: "mutex可能阻塞,违背转发面要求"
            code: |
              pthread_mutex_t g_mutex;
              VOID FifoTask()
              {
                  pthread_mutex_lock(&g_mutex);
                  /* critical section */
                  pthread_mutex_unlock(&g_mutex);
              }
    # 7.2 共享范围
    - id: LOCK-01-SCOPE
      index_num: 2
      name: 共享范围
      principles:
      - content: 转发面（FIFO线程）和控制面共享锁时,需使用trylock,否则容易造成死锁。
        match_regex: "pthread_mutex_trylock|pthread_mutex_lock|trylock"
        suggestion: 检查跨线程共享锁的使用方式，重点关注：1)FIFO线程访问共享锁是否使用trylock；2)避免FIFO线程阻塞等待控制面释放锁；3)trylock失败时的处理策略；4)防止转发面和控制面的死锁风险
        pos_examples:
          - case_name: 共享锁使用trylock
            description: 使用pthread_mutex_trylock避免阻塞
            expected_answer: "没有违规"
            think: "FIFO线程不会阻塞在控制面锁"
            code: |
              VOID FifoTask()
              {
                  if (pthread_mutex_trylock(&g_shared) == 0) {
                      /* critical section */
                      pthread_mutex_unlock(&g_shared);
                  } else {
                      /* retry later */
                  }
              }
        neg_examples:
          - case_name: FIFO线程直接lock共享mutex
            description: 未使用trylock
            expected_answer: "违规：可能造成FIFO线程死锁"
            think: "FIFO线程阻塞等待控制面释放锁"
            code: |
              VOID FifoTask()
              {
                  pthread_mutex_lock(&g_shared); 
              }

      - content: 锁的范围设计要合理,不要过大造成串行瓶颈,在控制一定业务复杂度下越小越好。
        suggestion: 检查锁的临界区大小，重点关注：1)临界区是否包含了不必要的代码；2)锁保护的范围是否最小化；3)复杂业务逻辑是否都在锁内执行；4)是否可以拆分为更细粒度的锁；5)避免锁成为性能瓶颈
        pos_examples:
          - case_name: 最小临界区
            description: 仅保护共享计数器更新
            expected_answer: "没有违规"
            think: "锁粒度最小,减少串行化"
            code: |
              VOID IncCounter()
              {
                  pthread_spin_lock(&g_ctrLock);
                  g_counter++;
                  pthread_spin_unlock(&g_ctrLock);
              }
        neg_examples:
          - case_name: 大临界区
            description: 整个业务处理加锁
            expected_answer: "违规：锁粒度过大"
            think: "业务逻辑全部串行,成为瓶颈"
            code: |
              VOID HandlePacket(PACKET *pkt)
              {
                  pthread_mutex_lock(&g_bigLock);
                  Parse(pkt);
                  Route(pkt);
                  Forward(pkt);
                  pthread_mutex_unlock(&g_bigLock);
              }
  # 8.安全函数
  - id: SEC-01
    name: 安全函数
    description: |
      安全函数使用说明参考如下链接CMC DOC中chm文档：
      https://3ms.huawei.com/next/groups/index.html#/wiki/detail?groupId=3862259&wikiId=6662388    

      Huawei Secure C V100R001C01SPC017B001版本CMC链接如下：
      https://cmc-szv.clouddragon.huawei.com/cmcversion/index/releaseView?deltaId=10191407089386628&isSelect=Doc    
    rules:
    # 8.1 目的缓冲区
    - id: SEC-01-DEST
      index_num: 1
      name: 目的缓冲区
      principles:

      - content: 目的缓冲区为字节数,当目的缓冲为数组类型变量destBuff[BUFF_SIZE]形式, 长度大小使用 sizeof(destBuff) ,禁止直接使用BUFF_SIZE作为长度。
        match_regex: "sizeof\\s*\\(|\\w+_s\\s*\\(.*,\\s*\\d+\\s*,"
        suggestion: 检查缓冲区长度的获取方式，重点关注：1)数组类型变量是否使用sizeof(destBuff)而非硬编码常量；2)避免数组大小改变时忘记更新长度参数；3)确保长度参数与实际缓冲区大小一致；4)提高代码的维护性和安全性
        pos_examples:
          - case_name: 使用sizeof获取数组长度
            description: 数组长度用sizeof(destBuff)计算
            expected_answer: "没有违规"
            think: "使用sizeof(destBuff)能够自动计算数组的实际字节大小，当数组大小改变时无需手动修改长度参数，避免了硬编码带来的维护问题和潜在错误"
            code: |
              UINT32 FormatMessage(const CHAR *template, UINT32 value)
              {
                  CHAR destBuff[128];
                  CHAR srcBuff[64];

                  sprintf_s(srcBuff, sizeof(srcBuff), "%u", value);

                  errno_t ret = memcpy_s(destBuff, sizeof(destBuff), srcBuff, strlen(srcBuff));
                  if (ret != EOK) {
                      return ERR_COPY_FAILED;
                  }
                  return OK;
              }
        neg_examples:
          - case_name: 直接使用宏常量作长度
            description: 用BUFF_SIZE常量代替sizeof
            expected_answer: "违规：硬编码长度"
            think: "直接使用BUFF_SIZE宏而不是sizeof(destBuff)存在风险，当数组定义改变但忘记更新宏时，会导致长度不匹配，可能引起缓冲区溢出或其他内存安全问题"
            code: |
              #define BUFF_SIZE 128
              UINT32 ProcessUserInput(const CHAR *input)
              {
                  CHAR destBuff[BUFF_SIZE];  

                  errno_t ret = memcpy_s(destBuff, BUFF_SIZE, input, strlen(input));
                  if (ret != EOK) {
                      return ERR_COPY_FAILED;
                  }
                  return ProcessBuffer(destBuff);
              }

      - content: 字符串类型操作需要考虑带结束符'\0'后的缓冲区长度。
        suggestion: 检查字符串操作的缓冲区大小计算，重点关注：1)缓冲区大小是否包含了'\0'终止符的空间；2)字符串长度+1是否超过缓冲区大小；3)strcpy_s等函数的目标缓冲区是否足够；4)避免字符串截断或缓冲区溢出
        pos_examples:
          - case_name: 为'\0'预留空间
            description: 字符串缓冲区大小包含终止符
            expected_answer: "没有违规"
            code: |
              CHAR buf[16];
              strcpy_s(buf, sizeof(buf), "hello");
        neg_examples:
          - case_name: 未预留终止符空间
            description: 缓冲区大小刚好等于源字符串长度
            expected_answer: "违规：未考虑终止符"
            code: |
              CHAR buf[5];
              strcpy_s(buf, sizeof(buf), "hello"); 
      - content: "memcpy_s用法: 禁止出现源目的缓冲区重叠的用法。如过无法避免,使用memmove_s函数处理重叠的拷贝,并接受微弱性能劣化的影响。"
        suggestion: 检查memcpy_s的使用场景，重点关注：1)源和目标缓冲区是否可能存在地址重叠；2)重叠情况下是否改用memmove_s；3)数组内部元素移动等常见重叠场景；4)确保内存操作的正确性
        pos_examples:
          - case_name: 重叠区域使用memmove_s
            description: 源目的缓冲区重叠改用memmove_s
            expected_answer: "没有违规"
            code: |
              UINT8 buf[10] = {0,1,2,3,4,5,6,7,8,9};
              memmove_s(buf + 2, sizeof(buf) - 2, buf, 6);
        neg_examples:
          - case_name: 重叠仍用memcpy_s
            description: 源目的缓冲区重叠却调用memcpy_s
            expected_answer: "违规：重叠缓冲区用memcpy_s"
            code: |
              UINT8 buf[10];
              memcpy_s(buf + 2, sizeof(buf) - 2, buf, 6);

      - content: 当安全函数操作的目的缓冲区地址为函数入参时,同时也须将目的缓冲区长度通过函数入参传递进来,不能靠外部保证安全而硬编码目的缓冲区长度。
        suggestion: 检查函数参数设计的安全性，重点关注：1)接受缓冲区指针的函数是否同时接受长度参数；2)是否在函数内硬编码了缓冲区大小；3)长度参数是否被正确使用；4)避免依赖外部约定的缓冲区大小
        pos_examples:
          - case_name: 长度由调用者传入
            description: 目的缓冲区长度作为函数参数
            expected_answer: "没有违规"
            code: |
              VOID SafeCopy(CHAR *dest, UINT32 destLen, const CHAR *src)
              {
                  strcpy_s(dest, destLen, src);
              }
        neg_examples:
          - case_name: 函数内硬编码长度
            description: 目的缓冲区长度写死在函数内
            expected_answer: "违规：硬编码长度"
            code: |
              VOID SafeCopy(CHAR *dest, const CHAR *src)
              {
                  strcpy_s(dest, 128, src); 
              }

    # 8.2 返回值
    - id: SEC-01-RET
      index_num: 2
      name: 返回值
      principles:

      - content: 安全函数一定要判断返回值进行相应的处理。
        match_regex: "\\w+_s\\s*\\(.*\\)\\s*;|errno_t\\s+\\w+\\s*=|if\\s*\\(.*\\w+_s\\s*\\("
        suggestion: 检查安全函数返回值的处理，重点关注：1)所有安全函数调用是否检查了返回值；2)返回值不为EOK时是否有相应的错误处理；3)避免忽略函数失败继续执行；4)确保错误处理逻辑的完整性
        pos_examples:
          - case_name: 检查返回值并处理错误
            description: 判断strcpy_s返回值
            expected_answer: "没有违规"
            think: "安全函数返回errno_t类型错误码，通过检查返回值可以及时发现操作失败并进行相应处理，这是安全编程的基本要求，能够避免基于错误假设继续执行"
            code: |
              UINT32 BuildFilePath(CHAR *fullPath, UINT32 pathLen, const CHAR *dir, const CHAR *filename)
              {
                  if (fullPath == NULL || dir == NULL || filename == NULL) {
                      return ERR_NULL_PTR;
                  }
                  errno_t rc = strcpy_s(fullPath, pathLen, dir);
                  if (rc != EOK) {
                      LOG_ERROR("Directory copy failed, error: %d", rc);
                      return ERR_PATH_COPY;
                  }
                  rc = strcat_s(fullPath, pathLen, "/");
                  if (rc != EOK) {
                      LOG_ERROR("Separator append failed, error: %d", rc);
                      return ERR_PATH_APPEND;
                  }

                  rc = strcat_s(fullPath, pathLen, filename);
                  if (rc != EOK) {
                      LOG_ERROR("Filename append failed, error: %d", rc);
                      return ERR_PATH_APPEND;
                  }

                  return OK;
              }
        neg_examples:
          - case_name: 忽略返回值
            description: 不检查安全函数返回值
            expected_answer: "违规：未处理返回值"
            think: "忽略安全函数返回值意味着无法知道操作是否成功，当函数因缓冲区不足等原因失败时，程序会基于错误假设继续执行，可能导致逻辑错误或安全问题"
            code: |
              UINT32 ConcatenateStrings(CHAR *result, UINT32 resultLen, const CHAR *str1, const CHAR *str2)
              {
                  strcpy_s(result, resultLen, str1); 
                  strcat_s(result, resultLen, str2); 
                  return strlen(result);
              }

      - content: 安全函数保护不能作为常规流程的长度检查手段,需要提前做显式检查。
        suggestion: 检查长度校验的实现方式，重点关注：1)是否在调用安全函数前进行了显式长度检查；2)不应依赖安全函数的截断行为作为长度控制；3)提前检查可以提供更好的错误处理；4)避免数据被意外截断
        pos_examples:
          - case_name: 提前显式检查长度
            description: 先判断长度再调用安全函数
            expected_answer: "没有违规"
            code: |
              if (strlen(src) >= sizeof(buf)) {
                  return ERR_TOO_LONG;
              }
              strcpy_s(buf, sizeof(buf), src);
        neg_examples:
          - case_name: 仅靠安全函数截断
            description: 用安全函数当长度检查
            expected_answer: "违规：未预先显式检查"
            code: |
              strcpy_s(buf, sizeof(buf), src); /* 依赖截断 */

      - content: sprintf_s操作成功时返回写入的字符个数,失败返回-1,返回值大于0代表成功。
        suggestion: 检查sprintf_s返回值的判断逻辑，重点关注：1)是否正确判断返回值>0为成功；2)不要将-1当作成功条件；3)理解sprintf_s的返回值含义；4)正确处理格式化失败的情况
        pos_examples:
          - case_name: 正确判断sprintf_s返回值
            description: 返回值>0视为成功
            expected_answer: "没有违规"
            code: |
              INT32 n = sprintf_s(buf, sizeof(buf), "%d-%d", a, b);
              if (n > 0) {
                  /* success */
              }
        neg_examples:
          - case_name: 误判返回值
            description: 把-1当成功,-1会进if
            expected_answer: "违规：错误判断返回值"
            code: |
              if (sprintf_s(buf, sizeof(buf), "%d", val)) {
                  ...
              }

      - content: memset_s清零局部定义的固定结构
        suggestion: 检查memset_s的特殊使用场景，重点关注：1)仅对局部定义的固定结构体使用时可以不检查返回值；2)其他情况下仍需检查memset_s返回值；3)确保是栈上分配的固定大小结构；4)理解这是安全函数返回值检查的例外情况
        pos_examples:
          - case_name: 清零局部结构体
            description: 用memset_s清空局部变量
            expected_answer: "没有违规（例外）"
            code: |
              MY_STRUCT st = {0};
              memset_s(&st, sizeof(st), 0, sizeof(st)); 
        neg_examples: []   # 该条为“例外”,无负例

    # 8.3 函数封装
    - id: SEC-01-WRAP
      index_num: 3
      name: 函数封装
      principles:
      - content: 禁止对安全函数进行自定义封装
        suggestion: 检查是否存在安全函数的自定义封装，重点关注：1)是否定义了包装安全函数的宏或函数；2)直接使用原生安全函数而非自定义封装；3)避免隐藏安全函数的错误处理机制；4)保持代码的透明性和可维护性
        pos_examples:
          - case_name: 直接调用安全函数
            description: 代码中直接调用strcpy_s
            expected_answer: "没有违规"
            code: |
              strcpy_s(dest, destLen, src);
        neg_examples:
          - case_name: 自定义封装strcpy_s
            description: 在项目中包装一层MyStrCpy
            expected_answer: "违规：自定义封装安全函数"
            code: |
              #define MyStrCpy(d,s) strcpy_s(d,sizeof(d),s)
  # 9. 安全隐私
  - id: PRIV-01
    index_num: 9
    name: 安全隐私
    rules:
    # 9.1 隐私保护
    - id: PRIV-01-LOG
      index_num: 1
      name: 隐私保护
      principles:
      - content: 禁止在日志中记录用户隐私信息,包括IMSI,IMEI,MSISDN,用户IP（IPV4、IPV6）,MSID,CUI,MAC,usermane,password,密钥等,如果外部需求记录需联系安全SE确认实现方案；
        match_regex: "PS_WriteLog.*IMSI|PS_WriteLog.*IMEI|PS_WriteLog.*IP|PS_WriteLog.*password|PS_WriteLog.*key|PS_WriteLog.*MAC"
        suggestion: 检查日志中的隐私信息泄露，重点关注：1)日志是否包含IMSI、IMEI、IP地址等用户隐私信息；2)密码、密钥等敏感信息是否明文输出；3)必要时联系安全SE确认合规方案；4)使用脱敏或哈希方式处理敏感数据
        pos_examples:
          - case_name: 打印非隐私信息
            description: 日志中仅输出计数器
            expected_answer: "没有违规"
            think: "不涉及任何列出的隐私字段"
            code: |
              PS_WriteLog(LOG_INFO, "Process done, cnt=%u", g_counter);
        neg_examples:
          - case_name: 打印用户IPv4地址
            description: 日志直接打印client_ip
            expected_answer: "违规：日志记录用户IP"
            think: "用户IPv4属于被禁止的隐私信息"
            code: |
              PS_WriteLog(LOG_INFO, "Client IP=%s", client_ip);

      - content: 禁止明文打印OM类、业务类的密钥的密钥,如果涉及到密文打印的需求联系安全SE确认实现方案。
        match_regex: "PS_WriteLog.*key|PS_WriteLog.*Key|PS_WriteLog.*password|PS_WriteLog.*secret"
        suggestion: 检查日志中的敏感信息输出，重点关注：1)是否打印了密钥、密码等敏感信息的明文；2)仅打印密钥ID或句柄而非内容；3)敏感信息需要时应联系安全SE确认方案；4)避免通过日志泄露关键安全信息
        pos_examples:
          - case_name: 不打印密钥
            description: 日志中仅打印密钥ID
            expected_answer: "没有违规"
            think: "未暴露密钥明文"
            code: |
              PS_WriteLog(LOG_INFO, "Use key id=%u", key_id);
        neg_examples:
          - case_name: 明文打印密钥
            description: 日志直接输出密钥内容
            expected_answer: "违规：明文打印密钥"
            think: "密钥以明文形式泄露"
            code: |
              PS_WriteLog(LOG_INFO, "Key=%s", key_hex_str);

    # 9.2 命令资料
    - id: PRIV-01-CMD
      index_num: 2
      name: 命令资料
      principles:
      - content: 新增或修改命令,包括工程命令,未补充相应资料。
        suggestion: 检查命令变更的文档同步，重点关注：1)新增命令是否在CLI手册中有说明；2)工程命令是否有对应的使用文档；3)命令参数和功能描述是否完整；4)确保运维和测试人员能够了解新命令
        pos_examples:
          - case_name: 新增命令同步补充文档
            description: 新增show_stats命令并更新CLI手册
            expected_answer: "没有违规"
            think: "资料已同步"
            code: |
              /* 新增命令已在CLI手册和工程文档中登记 */
        neg_examples:
          - case_name: 新增命令无文档
            description: 新增diag_reset命令未更新任何资料
            expected_answer: "违规：未补充命令资料"
            think: "缺少文档导致运维/测试无法识别"  
  # 10. 编译宏
  - id: MACRO-01
    index_num: 10
    name: 编译宏
    rules:
    - id: MACRO-01-DEBUG
      index_num: 1
      name: __DEBUG__宏
      principles:
      - content: 禁止业务代码使用__DEBUG__宏隔离实现,会造成自验证二进制与发布二进制产生差异,可能影响验证准确性,建议通过alpha测试打桩。
        match_regex: "#ifdef\\s+__DEBUG__|#ifndef\\s+__DEBUG__|#if.*__DEBUG__"
        suggestion: 检查调试代码的实现方式，重点关注：1)是否使用__DEBUG__宏隔离调试代码；2)建议使用运行时配置控制调试功能；3)确保发布版和调试版行为一致；4)避免编译时差异影响测试有效性
        pos_examples:
          - case_name: 使用运行时开关
            description: 通过配置项而非__DEBUG__宏控制调试行为
            expected_answer: "没有违规"
            think: "发布与调试二进制行为一致"
            code: |
              if (g_dbgCfg.enable_trace) {
                  TracePacket(pkt);
              }
        neg_examples:
          - case_name: 使用__DEBUG__隔离代码
            description: 用__DEBUG__宏包裹调试逻辑
            expected_answer: "违规：使用__DEBUG__宏隔离实现"
            think: "发布版与调试版行为不同"
            code: |
              #ifdef __DEBUG__
              DumpPacket(pkt);
              #endif
  # 11. 运算
  - id: CALC-01
    index_num: 11
    name: 运算
    rules:
    # 11.1 运算溢出
    - id: CALC-01-OVERFLOW
      index_num: 1
      name: 运算溢出
      principles:
      - content: 两个数据相加或相乘时一定要考虑是否会溢出,造成数值翻转等错误。
        match_regex: "\\w+\\s*[+*]\\s*\\w+|\\w+\\s*\\+=\\s*\\w+|\\w+\\s*\\*=\\s*\\w+"
        suggestion: 检查算术运算的溢出保护，重点关注：1)加法前是否检查a > MAX_VALUE - b；2)乘法前是否检查a > MAX_VALUE / b；3)避免溢出导致的数值翻转；4)使用安全的运算函数或提前校验
        pos_examples:
          - case_name: 先检查再相加
            description: 使用UINT32_MAX提前判断
            expected_answer: "没有违规"
            think: "通过检查a > UINT32_MAX - b来预防加法溢出，这种方法避免了实际执行可能溢出的运算，确保结果的正确性和程序的稳定性"
            code: |
              UINT32 SafeAdd(UINT32 a, UINT32 b, UINT32 *result)
              {
                  if (result == NULL) {
                      return ERR_NULL_PTR;
                  }

                  // 正确：检查加法溢出
                  if (a > UINT32_MAX - b) {
                      LOG_ERROR("Addition overflow: %u + %u", a, b);
                      return ERR_OVERFLOW;
                  }

                  *result = a + b;
                  return OK;
              }
        neg_examples:
          - case_name: 直接相加
            description: 未检查溢出
            expected_answer: "违规：可能数值翻转"
            think: "直接执行a + b而不检查溢出，当两数之和超过UINT32_MAX时会发生数值翻转，结果变成一个很小的数，可能导致逻辑错误或安全问题"
            code: |
              UINT32 CalculateBufferSize(UINT32 headerSize, UINT32 dataSize)
              {
                  // 错误：未检查加法溢出
                  UINT32 totalSize = headerSize + dataSize; /* 可能溢出翻转 */

                  // 基于可能错误的大小分配内存
                  return AllocateBuffer(totalSize);
              }

      - content: 两个数据相减时一定要先进行大小判断,避免减翻。
        suggestion: 检查减法运算的安全性，重点关注：1)无符号数相减前是否判断了被减数≥减数；2)避免无符号数减法结果翻转为很大的正数；3)有符号数减法的溢出检查；4)特别注意跨线程时间戳相减的场景
        pos_examples:
          - case_name: 先判断再相减
            description: 保证被减数≥减数
            expected_answer: "没有违规"
            code: |
              if (a < b) return ERR_UNDERFLOW;
              UINT32 diff = a - b;
        neg_examples:
          - case_name: 直接相减
            description: 未判断大小
            expected_answer: "违规：可能减翻"
            code: |
              UINT32 diff = a - b;

      - content: 时间戳相减需要注意是否会减翻,尤其是两个时间戳来自两个不同的线程的场景,由于线程切换,可能随时会翻转。
        suggestion: 检查时间戳运算的特殊处理，重点关注：1)跨线程时间戳相减的翻转风险；2)使用无符号差值计算处理时间戳翻转；3)考虑系统时间回调的影响；4)时间差计算的溢出保护
        pos_examples:
          - case_name: 时间戳防翻处理
            description: 使用无符号差值判断
            expected_answer: "没有违规"
            code: |
              UINT32 diff = (UINT32)(t2 - t1); /* t2≥t1 或 翻转后仍正确 */
        neg_examples:
          - case_name: 直接相减
            description: 未考虑翻转
            expected_answer: "违规：可能减翻"
            code: |
              INT32 diff = t2 - t1;
    # 11.2 运算异常
    - id: CALC-01-DIV
      index_num: 2
      name: 运算异常
      principles:
      - content: 两个数据做除法或者取模运算时一定要考虑除数是否为0。
        match_regex: "\\w+\\s*/\\s*\\w+|\\w+\\s*%\\s*\\w+|\\w+\\s*/=\\s*\\w+|\\w+\\s*%=\\s*\\w+"
        suggestion: 检查除法和取模运算的安全性，重点关注：1)除法和取模运算前是否检查除数非零；2)避免除零异常导致程序崩溃；3)零除数的错误处理逻辑；4)特别注意动态计算的除数值
        pos_examples:
          - case_name: 先判断除数
            description: 除法前检查非零
            expected_answer: "没有违规"
            think: "在执行除法运算前检查除数是否为0，这是防止除零异常的标准做法，避免了程序崩溃或产生未定义行为"
            code: |
              UINT32 CalculateAverage(UINT32 total, UINT32 count, UINT32 *average)
              {
                  if (average == NULL) {
                      return ERR_NULL_PTR;
                  }
                  if (count == 0) {
                      LOG_ERROR("Division by zero: count is 0");
                      return ERR_DIV_ZERO;
                  }

                  *average = total / count;
                  return OK;
              }
          - case_name: 先判断除数
            description: 除法前检查非零
            expected_answer: "没有违规"
            think: "核心位置是 `mean /= length;` 按照代码逻辑 如果length为0,则mean不会累加, if(mean==0) 的条件会提前退出，所以代码没有问题"
            code: |
              LOCAL INLINE UINT16 ExtPktIdentU_ThreeSigma(UINT64 *dataOut, UINT16 *timeDiff, UINT16 length)
              {
                  UINT16 i, j;
                  UINT8 sigma;
                  double std = 0;
                  double mean = 0;
                  for (i = 0; i < length; i++) {
                      mean += timeDiff[i];
                  }
                  if (mean == 0) {
                      return 0;
                  }
                  mean /= length;
                  for (i = 0; i < length; i++) {
                      std += (timeDiff[i] - mean) * (timeDiff[i] - mean);
                  }
                  std /= length;
                  std = sqrt(std);
                  sigma = (UINT8)(TIME_THREE_SIGMA * std / mean);
                  if (sigma < TIME_THREE_SIGMA) {
                      sigma = TIME_THREE_SIGMA;
                  }
                  for (i = 0, j = 0; i < length; i++) {
                      if ((timeDiff[i] - mean - sigma * std) < 0) {
                          dataOut[j] = timeDiff[i];
                          j++;
                      }
                  }
                  return j;
              }              
        neg_examples:
          - case_name: 直接除法
            description: 未检查除数
            expected_answer: "违规：除数可能为0"
            think: "直接执行除法运算而不检查除数，当除数为0时会触发除零异常，导致程序崩溃或产生未定义的行为"
            code: |
              UINT32 CalculateRatio(UINT32 numerator, UINT32 denominator)
              {
                  UINT32 ratio = numerator / denominator; 

                  return ratio * 100; 
              }
          - case_name: 直接除法
            description: 未检查除数
            expected_answer: "违规：除数可能为0"
            think: "违规 ( EPI_GetRtpStudyPktNum的返回值未做0值排除 )的行为"
            code: |
              extern UINT16 g_epiRtpStudyPktNum;

              LOCAL INLINE UINT16 EPI_GetRtpStudyPktNum()
              {
                  return g_epiRtpStudyPktNum;
              }


              VOID ExtPktIdentU_RtpLearningProc(UINT8 uschNo, FI_PROC_CTX *procCtx, EPI_FLOW_NODE *pFlowNode)
              {
                  BOOL needLearn = VOS_FALSE;
                  FI_RTP_TWO_TUPLE_NODE *twoTupleNode = VOS_NULL_PTR;
                  if (ExtPktIdentU_RtpGetTwoTupleNode(uschNo, procCtx, pFlowNode, &twoTupleNode) != VOS_TRUE) {
                      pFlowNode->frameIdentStatus = E_FRAMEIDENT_NO_NEED;
                      return;
                  }
                  ExtPktIdentU_RtpCollectPktInfo(uschNo, procCtx, pFlowNode, twoTupleNode);
                  ExtPktIdentU_RtpKeepOrderProc(uschNo, procCtx, twoTupleNode);
                  ExtPktIdentU_RtpCollectTimeDiff(uschNo, procCtx, twoTupleNode);

                  if (pFlowNode->learnFlag == VOS_TRUE && (pFlowNode->totalPktNum % EPI_GetRtpStudyPktNum() == 0)) {
                      needLearn = VOS_TRUE;
                      EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_PERIOD_LEARNING);
                  } else if (EXTPKTIDENTU_FIRST_LEARNING_VALID(procCtx, pFlowNode) == VOS_TRUE) {
                      needLearn = VOS_TRUE;
                      EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_TIME_LEARNING);
                  }
                  if (needLearn == VOS_TRUE) {
                      ExtPktIdentU_RtpSelectIdentAlgo(uschNo, procCtx, pFlowNode);
                      ExtPktIdentU_RtpTimeDiffCalc(uschNo, procCtx, pFlowNode);
                      if (ExtPktIdentU_LearningResultProc(uschNo, procCtx, pFlowNode) != VOS_TRUE) {
                          return;
                      }
                  }
                  if (pFlowNode->learnFlag == VOS_TRUE) {
                      if (twoTupleNode->algoType == FI_ALOG_RTP_MARKER) {
                          ExtPktIdentU_RtpGetVideoFrameMarker(uschNo, procCtx, pFlowNode, twoTupleNode);
                          EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_MARKER);
                      } else if (twoTupleNode->algoType == FI_ALOG_TIME_DIFF) {
                          ExtPktIdentU_RtpGetVideoFrameTimeDiff(uschNo, procCtx, pFlowNode, twoTupleNode);
                          EPI_CNTR_ADD(uschNo, EPI_U_FRAME_IDENT_RTP_TIME_DIFF);
                      }
                  }
                  ExtPktIdentU_RtpCacheListProc(uschNo, procCtx, twoTupleNode);
                  ExtPktIdentU_RtpNoVideoProc(uschNo, procCtx, pFlowNode);
                  procCtx->frameIdentAlgo = twoTupleNode->algoType;
              }
          - case_name: 直接除法
            description: 未检查除数
            expected_answer: "违规：除数可能为0"
            think: ""
            code: |
              UINT32 BWM_GetGlobalPercent(UINT16 globalControllerId, BWM_GLOBAL_SOURCE_TYPE_E bwmSrcType)
              {
                  // 关闭状态不做调整
                  if (g_stBwmGlobalShr[bwmSrcType].switchState == BWM_DYANMIC_SWITCH_CPU) {
                      return BWM_GLOBAL_PERCENT_PRECISION;
                  }

                  // 功能开启时/ssg复位启动时，带宽默认按平均值控制
                  if (g_stBwmGlobalShr[bwmSrcType].switchState == BWM_DYANMIC_SWITCH_SYSTEM) {
                      return BWM_GLOBAL_PERCENT_PRECISION / g_stBwmGlobalShr[bwmSrcType].ssgNum;
                  }

                  if (globalControllerId >= g_bwmDynamicSize[bwmSrcType]) {
                      BWM_GLOBAL_CNTR_ADD(0, BWM_GLOBAL_ERR_ID_GET_PERCENT);
                      return BWM_GLOBAL_PERCENT_PRECISION / g_stBwmGlobalShr[bwmSrcType].ssgNum;
                  }
                  // A桶时，c面定时器正在处理当前桶数据，因此百分比应该获取上一周期数据
                  BWM_STAT_BUCKET_S *statBucket = SM_NULL_PTR;
                  BWM_GET_LAST_BUCKET(g_stBwmGlobalShr[bwmSrcType].switchState, statBucket, bwmSrcType);

                  UINT32 percent = statBucket[globalControllerId].percent;
                  return (percent > BWM_GLOBAL_PERCENT_PRECISION) ? BWM_GLOBAL_PERCENT_PRECISION : percent;
              }
    # 11.3 移位操作
    - id: CALC-01-SHIFT
      index_num: 3
      name: 移位操作
      principles:
      - content: 对数据进行左移或右移操作时,保证移的位数不超过数据本身bit数；例如：UCHAR数据类型时移位不要超过8bit,USHORT数据类型时移位不要超过16bit,ULONG数据类型时移位不要超过32bit。
        match_regex: "<<\\s*\\w+|>>\\s*\\w+|\\w+\\s*<<|\\w+\\s*>>"
        suggestion: 检查移位操作的位数限制，重点关注：1)移位位数是否超过数据类型的位宽；2)UCHAR≤8位、USHORT≤16位、ULONG≤32位；3)动态移位位数的范围检查；4)避免未定义行为导致的结果错误
        pos_examples:
          - case_name: 检查移位位数
            description: 移位前判断位数
            expected_answer: "没有违规"
            code: |
              if (shift >= 32) return ERR_SHIFT;
              ULONG res = val << shift;
        neg_examples:
          - case_name: 直接移位
            description: 未检查移位范围
            expected_answer: "违规：移位位数可能超限"
            code: |
              UCHAR res = (UCHAR)(val << shift); 
  # 12. 调测机制
  - id: DBG-01
    index_num: 12
    name: 调测机制
    rules:
    # 12.1 日志输出
    - id: DBG-01-LOGIMPL
      index_num: 1
      name: 日志输出
      principles:
      - content: 禁止私自实现日志功能,私有实现容易遗漏日志管理功能,导致日志过多而磁盘满上报告警并影响业务。
        match_regex: "printf\\s*\\(|fprintf\\s*\\(|PS_WriteLog\\s*\\(|LOG_"
        suggestion: 检查日志实现的规范性，重点关注：1)是否使用了printf、fprintf等私有日志实现；2)必须使用平台统一的日志接口如PS_WriteLog；3)避免绕过日志管理机制；4)确保日志有流控和轮转功能
        pos_examples:
          - case_name: 使用平台统一日志接口
            description: 调用PS_WriteLog
            expected_answer: "没有违规"
            code: |
              PS_WriteLog(LOG_INFO, "module init ok");
        neg_examples:
          - case_name: 私有printf日志
            description: 自行用printf写日志
            expected_answer: "违规：私自实现日志"
            code: |
              printf("[MYLOG] module init ok\n");

    # 12.2 日志流控
    - id: DBG-01-LOGFLOW
      index_num: 2
      name: 日志流控
      principles:
      - content: 正常业务流程不允许默认打印日志
        match_regex: "PS_WriteLog\\s*\\(\\s*LOG_INFO|PS_WriteLog\\s*\\(\\s*LOG_DEBUG"
        suggestion: 检查正常业务流程的日志输出，重点关注：1)高频业务路径是否默认打印日志；2)正常报文处理不应有INFO/DEBUG级别日志；3)仅在异常或关键事件时打印日志；4)避免日志过多影响性能和存储
        pos_examples:
          - case_name: 正常流程无默认日志
            description: 关键路径默认静默
            expected_answer: "没有违规"
            code: |
              /* 无日志 */
        neg_examples:
          - case_name: 正常流程默认打印
            description: 每个报文默认打印
            expected_answer: "违规：正常流程打印日志"
            code: |
              PS_WriteLog(LOG_INFO, "recv pkt");

      - content: 外部输入可大量构造的异常流程,日志必须加流控。
        match_regex: "PS_WriteLog\\s*\\(\\s*LOG_ERR|PS_WriteLog\\s*\\(\\s*LOG_WARN|static.*cnt|%\\s*\\d+\\s*=="
        suggestion: 检查异常日志的流控机制，重点关注：1)外部可触发的异常是否有日志流控；2)使用计数器控制日志频率(如每100次打印一次)；3)避免攻击者通过异常输入刷爆日志；4)确保关键异常信息不被淹没
        pos_examples:
          - case_name: 异常日志加计数限流
            description: 每100次打印一次
            expected_answer: "没有违规"
            code: |
              static UINT32 err_cnt = 0;
              if (++err_cnt % 100 == 0) {
                  PS_WriteLog(LOG_ERR, "invalid pkt (%u total)", err_cnt);
              }
        neg_examples:
          - case_name: 异常无流控
            description: 每个异常都打印
            expected_answer: "违规：无流控"
            code: |
              PS_WriteLog(LOG_ERR, "invalid pkt");

      - content: 定时器回调函数里不允许打印日志,防止大量回调触发海量日志。
        suggestion: 检查定时器回调中的日志输出，重点关注：1)定时器回调函数是否包含日志打印；2)高频定时器可能产生大量日志；3)回调中应静默处理或使用计数器；4)避免定时器日志影响系统性能
        pos_examples:
          - case_name: 定时器回调无日志
            description: 回调中静默处理
            expected_answer: "没有违规"
            code: |
              VOID TimerCbk(UINT32 id) { /* do work */ }
        neg_examples:
          - case_name: 定时器回调打印日志
            description: 高频回调打印
            expected_answer: "违规：定时器回调打印日志"
            code: |
              VOID TimerCbk(UINT32 id) {
                  PS_WriteLog(LOG_INFO, "timer %u", id);
              }

      - content: info等低级别日志虽然默认不打印,但仍然会有加锁等消耗,主要流程中不要使用。
        suggestion: 检查主要流程中的低级别日志，重点关注：1)高频路径是否使用了INFO/DEBUG级别日志；2)即使不打印也有函数调用和加锁开销；3)主流程应使用计数器或其他轻量级统计；4)避免不必要的性能损耗
        pos_examples:
          - case_name: 主流程用计数器
            description: 用原子计数代替info日志
            expected_answer: "没有违规"
            code: |
              __sync_fetch_and_add(&g_pkts, 1);
        neg_examples:
          - case_name: 主流程用INFO日志
            description: 大量调用info级别
            expected_answer: "违规：主要流程使用info日志"
            code: |
              for (...) {
                  PS_WriteLog(LOG_INFO, "step %u", i);
              }

    # 12.3 计数
    - id: DBG-01-COUNTER
      index_num: 3
      name: 计数
      principles:
      - content: 计数需要唯一表达含义,避免多个计数复用相同计数,导致错误不明确,无法定界问题。
        suggestion: 检查计数器的语义唯一性，重点关注：1)每种错误或事件是否有独立的计数器；2)避免多种情况共用一个计数器；3)计数器命名应明确表达统计内容；4)确保问题定位时能准确识别原因
        pos_examples:
          - case_name: 独立计数器
            description: 每种错误单独计数
            expected_answer: "没有违规"
            code: |
              g_drop_no_buf++;
              g_drop_chksum++;
        neg_examples:
          - case_name: 复用同一计数器
            description: 多种错误共用一个计数
            expected_answer: "违规：计数含义不唯一"
            code: |
              g_drop_total++; 

    # 12.4 工程命令
    - id: DBG-01-CMD
      index_num: 4
      name: 工程命令
      principles:
      - content: 关键信息的查询命令要增加到信息采集工具中
        suggestion: 检查关键命令的工具集成，重点关注：1)新增的重要查询命令是否已加入信息采集工具；2)确保运维人员能通过统一工具获取关键信息；3)命令的可用性和文档完整性；4)避免关键信息查询遗漏
        pos_examples:
          - case_name: 关键信息已加入采集工具
            description: show_session_stats 已注册到info_collection
            expected_answer: "没有违规"
            code: |
              /* CLI 命令已登记到信息采集脚本 */
        neg_examples:
          - case_name: 关键命令未加入采集
            description: 新命令show_key_stats未登记
            expected_answer: "违规：未加入信息采集工具"
            code: |
              /* show_key_stats 未在采集脚本中 */

      - content: 关键资源要有对应的调试命令可查
        suggestion: 检查资源的调试命令覆盖，重点关注：1)新增的关键资源是否提供了查询命令；2)内存池、队列、表项等资源的可观测性；3)调试命令的参数完整性和安全性；4)确保问题定位时能查看资源状态
        pos_examples:
          - case_name: 资源对应调试命令
            description: 内存池提供debug命令
            expected_answer: "没有违规"
            code: |
              /* debug mem_pool show 已存在 */
        neg_examples:
          - case_name: 无调试命令
            description: 新增bitmap资源无查询命令
            expected_answer: "违规：缺少调试命令"
            code: |
              /* bitmap 资源无法通过 CLI 查看 */

      - content: 工程命令需考虑异常参数输入、内部数据量级、多线程访问等条件下处理,避免出现访问异常或者海量打印导致程序异常。
        suggestion: 检查工程命令的健壮性设计，重点关注：1)命令参数的有效性校验；2)大数据量输出的分页或限制机制；3)多线程环境下的数据访问安全；4)异常输入的容错处理；5)避免命令执行影响业务性能
        pos_examples:
          - case_name: 命令带参数校验
            description: 参数长度和范围检查
            expected_answer: "没有违规"
            code: |
              if (len > MAX_PRINT_LEN) len = MAX_PRINT_LEN;
        neg_examples:
          - case_name: 无参数校验
            description: 用户输入长度直接用于打印
            expected_answer: "违规：未做异常参数处理"
            code: |
              printf("%.*s", len, buf); 
  # 13. 结构体
  - id: STRUCT-01
    index_num: 13
    name: 结构体
    rules:
    - id: STRUCT-01-DEF
      index_num: 1
      name: 结构体定义
      principles:
      - content: 禁止使用__attribute__((aligned((bytes))))隐式对齐结构体,会触发某些编译器使用sse指令时误判对齐造成程序异常,另外也会造成自动对齐空间无法使用浪费内存。
        match_regex: "__attribute__\\s*\\(\\s*\\(\\s*aligned|struct\\s+\\w+\\s*\\{.*\\}\\s*__attribute__"
        suggestion: 检查结构体的对齐方式，重点关注：1)是否使用了__attribute__((aligned))强制对齐；2)依赖编译器自动对齐而非手动指定；3)避免SSE指令误判和内存浪费；4)确保结构体设计的可移植性
        pos_examples:
          - case_name: 默认对齐方式
            description: 不添加任何对齐属性,依赖编译器自动对齐
            expected_answer: "没有违规"
            think: "未使用__attribute__((aligned)),避免对齐误判与内存浪费"
            code: |
              struct PacketHeader {
                  UINT32 seq;
                  UINT16 len;
                  UINT8  flags;
              };
        neg_examples:
          - case_name: 显式指定对齐
            description: 使用__attribute__((aligned))强制64字节对齐
            expected_answer: "违规：使用__attribute__((aligned))"
            think: "显式对齐可能导致SSE误判及内存浪费"
            code: |
              struct DataBlock {
                  UINT32 a;
              } __attribute__((aligned(64)));
  # 14. 变量
  - id: VAR-01
    index_num: 14
    name: 变量
    rules:
    - id: VAR-01-TYPE
      index_num: 1
      name: 变量类型
      principles:
      - content: BOOL类型变量不同编译器实现长度存在差异,会导致不同系统下内存对齐差异,并且不利于对后续字段含义扩展,因此建议使用UINT32类型。
        match_regex: "BOOL\\s+\\w+|bool\\s+\\w+|UINT32\\s+\\w+.*flag|UINT32\\s+\\w+.*enable"
        suggestion: 检查布尔类型变量的定义，重点关注：1)是否使用了BOOL或bool类型；2)建议用UINT32替代以确保跨平台一致性；3)标志位字段便于后续扩展为多状态；4)避免不同编译器的长度差异问题
        pos_examples:
          - case_name: 使用UINT32代替BOOL
            description: 标志位定义为UINT32
            expected_answer: "没有违规（遵循建议）, 0/1 表示真假"
            think: "跨平台长度一致,便于扩展"
            code: |
              UINT32 isEnabled; 
        neg_examples:
          - case_name: 使用BOOL类型
            description: 直接使用bool或BOOL定义标志
            expected_answer: "违反建议：使用BOOL类型,可能1字节或4字节"
            think: "不同编译器BOOL长度可能不同,影响对齐"
            code: |
              BOOL isEnabled;