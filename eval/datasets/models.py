"""
评测数据集数据模型

定义YAML格式评测数据集的数据结构
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional


@dataclass
class GitInfo:
    """Git信息"""
    repo_url: str = ""
    branch: str = ""
    commit: str = ""
    mr_id: str = ""
    file_path: str = ""
    function_name: str = ""


@dataclass
class Example:
    """测试用例示例"""
    case_name: str
    description: str
    expected_answer: str
    think: str = ""
    code: str = ""
    git: GitInfo = None
    
    def __post_init__(self):
        if self.git is None:
            self.git = GitInfo()


@dataclass
class Principle:
    """编码原则"""
    content: str
    match_regex: str = ""  # 新增：用于识别应该应用该规则的代码模式
    suggestion: str = ""   # 新增：检查该规则时的建议和注意方向
    pos_examples: List[Example] = None
    neg_examples: List[Example] = None

    def __post_init__(self):
        if self.pos_examples is None:
            self.pos_examples = []
        if self.neg_examples is None:
            self.neg_examples = []


@dataclass
class Rule:
    """具体规则"""
    id: str
    index_num: int
    name: str
    principles: List[Principle]


@dataclass
class RuleCategory:
    """规则分类"""
    id: str
    index_num: int
    name: str
    rules: List[Rule] = None
    principles: List[Principle] = None  # 兼容旧格式
    
    def __post_init__(self):
        if self.rules is None and self.principles is None:
            self.principles = []
        elif self.rules is None:
            self.rules = []
        elif self.principles is None:
            self.principles = []
    
    def get_all_principles(self) -> List[Principle]:
        """获取所有原则（包括嵌套规则中的原则）"""
        all_principles = []
        
        # 直接的原则
        if self.principles:
            all_principles.extend(self.principles)
        
        # 嵌套规则中的原则
        if self.rules:
            for rule in self.rules:
                if rule.principles:
                    all_principles.extend(rule.principles)
        
        return all_principles


@dataclass
class EvaluationDataset:
    """评测数据集"""
    name: str
    version: str
    description: str = ""
    created_at: datetime = None
    updated_at: datetime = None
    total_rules: int = 0
    coverage: str = ""
    rule_categories: List[RuleCategory] = None
    
    def __post_init__(self):
        if self.rule_categories is None:
            self.rule_categories = []
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def get_all_examples(self) -> List[Example]:
        """获取所有测试用例"""
        examples = []
        for category in self.rule_categories:
            for principle in category.get_all_principles():
                examples.extend(principle.pos_examples)
                examples.extend(principle.neg_examples)
        return examples
    
    def get_examples_by_category(self, category_id: str) -> List[Example]:
        """根据分类ID获取测试用例"""
        for category in self.rule_categories:
            if category.id == category_id:
                examples = []
                for principle in category.get_all_principles():
                    examples.extend(principle.pos_examples)
                    examples.extend(principle.neg_examples)
                return examples
        return []
    
    def get_principle_by_content(self, content: str) -> Optional[Principle]:
        """根据内容查找原则"""
        for category in self.rule_categories:
            for principle in category.get_all_principles():
                if principle.content == content:
                    return principle
        return None
    
    def get_category_by_id(self, category_id: str) -> Optional[RuleCategory]:
        """根据ID查找分类"""
        for category in self.rule_categories:
            if category.id == category_id:
                return category
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        total_categories = len(self.rule_categories)
        total_principles = sum(len(cat.get_all_principles()) for cat in self.rule_categories)
        total_pos_examples = sum(
            len(principle.pos_examples) 
            for cat in self.rule_categories 
            for principle in cat.get_all_principles()
        )
        total_neg_examples = sum(
            len(principle.neg_examples) 
            for cat in self.rule_categories 
            for principle in cat.get_all_principles()
        )
        
        return {
            "total_categories": total_categories,
            "total_principles": total_principles,
            "total_pos_examples": total_pos_examples,
            "total_neg_examples": total_neg_examples,
            "total_examples": total_pos_examples + total_neg_examples,
            "coverage": self.coverage,
            "version": self.version
        } 