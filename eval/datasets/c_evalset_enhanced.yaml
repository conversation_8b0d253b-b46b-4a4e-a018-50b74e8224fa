name: 通用编码守则-增强版
version: 202507072100
description: "包含复杂实际代码场景的C语言编码规范评测集"
created_at: "2025-07-26T01:41:00+08:00"
updated_at: "2025-07-26T01:41:00+08:00"
rule_categories:
  - id: MEM-01
    name: 内存操作
    index_num: 1
    rules:
    - id: MEM-01-COPY
      index_num: 1
      name: 内存拷贝
      principles:
      - content: 内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。
        pos_examples:
          - case_name: 网络数据包拷贝前检查边界
            description: 在网络协议栈中，拷贝数据包前先验证缓冲区大小
            expected_answer: "没有违规"
            think: "显式校验避免越界，符合网络协议栈安全要求"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-001"
              file_path: "src/network_packet_handler.c"
              function_name: "safe_packet_copy"
        neg_examples:
          - case_name: 直接拷贝网络数据包
            description: 未检查缓冲区大小直接拷贝，可能导致栈溢出
            expected_answer: "违规：可能发生缓冲区越界"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-001"
              file_path: "src/network_packet_handler.c"
              function_name: "unsafe_packet_copy"
    - id: MEM-01-MOVE
      index_num: 2
      name: 内存搬移
      principles:
      - content: 不要使用memcpy_s函数操作有地址重叠的内存,行为结果未知,推荐使用memove_s。
        pos_examples:
          - case_name: 缓冲区重排使用memmove_s
            description: 在数据压缩算法中，使用memmove_s处理重叠区域
            expected_answer: "没有违规"
            think: "memmove_s保证重叠区正确复制，适用于数据重排场景"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-002"
              file_path: "src/data_compression.c"
              function_name: "compress_buffer_rearrange"
        neg_examples:
          - case_name: 重叠区域仍用memcpy_s
            description: 在数据重排时错误使用memcpy_s处理重叠区域
            expected_answer: "违规：应使用memmove_s"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-002"
              file_path: "src/data_compression.c"
              function_name: "unsafe_buffer_rearrange"
  - id: STR-01
    index_num: 2
    name: 字符串操作
    rules:
    - id: STR-01-FORMAT
      index_num: 1
      name: 注意数据格式
      principles:
      - content: 不要将二进制码流当做字符串操作,可能不存在'\0'结束符,造成访问越界
        pos_examples:
          - case_name: 二进制协议解析按字节处理
            description: 在协议解析器中，正确处理二进制数据流
            expected_answer: "没有违规"
            think: "显式指定长度,不依赖'\0'，适用于协议解析"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-003"
              file_path: "src/protocol_parser.c"
              function_name: "parse_binary_protocol"
        neg_examples:
          - case_name: 二进制数据当字符串处理
            description: 在协议解析中错误使用字符串函数处理二进制数据
            expected_answer: "违规：可能因无'\0'而越界"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-003"
              file_path: "src/protocol_parser.c"
              function_name: "unsafe_binary_processing"
    - id: STR-01-FUNC
      index_num: 2
      name: 字符串操作函数
      principles:
      - content: 使用memcpy_s接口操作字符串时要考虑'\0'携带结束符
        suggestion: 使用strcpy_s、strcat_s等字符串操作接口处理字符串,接口内部已经考虑了结束符'\0'处理
        pos_examples:
          - case_name: 配置文件解析使用strcpy_s
            description: 在配置管理模块中，安全地复制配置字符串
            expected_answer: "没有违规"
            think: "strcpy_s内部复制'\0'，适用于配置字符串处理"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-004"
              file_path: "src/config_manager.c"
              function_name: "safe_config_copy"
        neg_examples:
          - case_name: 字符串复制未考虑终止符
            description: 在配置处理中错误使用memcpy_s复制字符串
            expected_answer: "违规：未考虑终止符"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-004"
              file_path: "src/config_manager.c"
              function_name: "unsafe_string_copy"
  - id: ARR-01
    index_num: 3
    name: 数组操作
    rules:
    - id: ARR-01-INDEX
      index_num: 1
      name: 下标访问
      principles:
        - content: 数组下标访问前做有效性校验,确保访问不越界。
          pos_examples:
            - case_name: 哈希表访问前检查索引
              description: 在哈希表实现中，访问前验证索引有效性
              expected_answer: "没有违规"
              think: "显式防止越界，适用于哈希表数据结构"
              git:
                repo_url: "test_external_projects/c_test_project_no_comment"
                branch: "feature/enhanced-evaluation-modules"
                commit: "7ecb213"
                mr_id: "MR-005"
                file_path: "src/hash_table.c"
                function_name: "safe_hash_table_access"
          neg_examples:
            - case_name: 直接访问哈希表槽位
              description: 未校验哈希索引直接访问数组
              expected_answer: "违规：可能越界访问"
              git:
                repo_url: "test_external_projects/c_test_project_no_comment"
                branch: "feature/enhanced-evaluation-modules"
                commit: "7ecb213"
                mr_id: "MR-005"
                file_path: "src/hash_table.c"
                function_name: "unsafe_hash_table_access"
    - id: ARR-01-DEFINE
      index_num: 2  
      name: 数组定义
      principles:
      - content: 避免使用二级以及以上指针结构操作多维数组,容易造成代码晦涩难懂,操作出错,建议通过结构体分级定义或者每一级单独使用变量表达。
        pos_examples:
        - case_name: 图像处理使用结构体分级定义
          description: 在图像处理模块中，使用结构体而非二级指针
          expected_answer: "没有违规"        
          think: "遵循了规则的建议,使用结构体分级定义,代码清晰易懂"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-006"
            file_path: "src/image_processor.c"
            function_name: "safe_image_processing"
        neg_examples:
        - case_name: 使用二级指针操作图像数据
          description: 在图像处理中错误使用二级指针
          expected_answer: "违规：使用了二级数组指针"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-006"
            file_path: "src/image_processor.c"
            function_name: "unsafe_image_processing"
      - content: 禁止在函数中定义占用超过500字节的数组(例如:char array[1000]),该数组会占用栈空间,在多级调用叠加后可能导致栈溢出,超大数组使用动态申请或者全局变量。
        pos_examples:
          - case_name: 大缓冲区使用动态分配
            description: 在数据处理模块中，使用堆内存而非大栈数组
            expected_answer: "没有违规"
            think: "函数内未出现大于 500 B 的栈数组,符合规范"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-007"
              file_path: "src/data_processor.c"
              function_name: "safe_large_buffer_processing"
        neg_examples:
          - case_name: 函数内定义大栈数组
            description: 在数据处理函数中定义大栈缓冲区
            expected_answer: "违规：函数内定义超过 500 字节栈数组"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-007"
              file_path: "src/data_processor.c"
              function_name: "unsafe_large_buffer_processing"
  - id: RES-04
    index_num: 4
    name: 资源使用
    rules:
    - id: RES-04-ALLOC
      index_num: 1
      name: 内存申请
      principles:
      - content: 不要使用malloc和free等申请系统内存,系统内存预留有限,并且出现内存泄漏时难以定位。使用带有内存统计接口,定义在mem_api.h,有临时内存、用户级内存、非用户级内存、动态内存等申请接口,请仔细阅读接口功能描述,根据实际场景选择。
        neg_examples:
        - case_name: 数据库连接池使用系统malloc
          description: 在数据库连接池中直接调用malloc申请内存
          expected_answer: "违规:使用了禁止的malloc接口"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-008"
            file_path: "src/db_connection_pool.c"
            function_name: "unsafe_connection_alloc"
    - id: RES-04-FREE
      index_num: 2
      name: 内存释放
      principles:
      - content: 申请临时内存资源注意在函数执行完成后释放,提前退出的分支也要考虑释放。
        pos_examples:
        - case_name: 数据库查询所有路径均释放
          description: 在数据库查询模块中，所有返回路径都释放临时内存
          expected_answer: "没有违规"
          think: "所有return前都调用了UTIL_FreeTmp"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-009"
            file_path: "src/db_query.c"
            function_name: "safe_database_query"
        neg_examples:
        - case_name: 数据库查询正常分支未释放
          description: 数据库查询成功时未释放临时内存
          expected_answer: "违规：正常分支未释放临时内存"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-009"
            file_path: "src/db_query.c"
            function_name: "unsafe_database_query"
  - id: FUN-05
    index_num: 5
    name: 函数实现
    rules:
    - id: FUN-05-DEF
      index_num: 1
      name: 函数定义
      principles:
      - content: 定义递归函数,容易造成死循环或栈溢出。
        pos_examples:
        - case_name: 文件系统遍历使用迭代
          description: 在文件系统遍历中使用循环而非递归
          expected_answer: "没有违规"
          think: "未使用递归,避免栈溢出风险"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-010"
            file_path: "src/file_system.c"
            function_name: "safe_directory_traverse"
        neg_examples:
        - case_name: 文件系统递归遍历
          description: 使用递归方式遍历文件系统
          expected_answer: "违规：定义递归函数"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-010"
            file_path: "src/file_system.c"
            function_name: "unsafe_recursive_traverse"
      - content: 函数中循环定义要考虑其循环次数,避免运行次数过大造成死循环。
        pos_examples:
        - case_name: 网络重传机制带最大次数
          description: 网络重传循环有最大次数限制
          expected_answer: "没有违规"
          think: "循环上限固定,防止死循环"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-011"
            file_path: "src/network_retry.c"
            function_name: "safe_retry_mechanism"
        neg_examples:
        - case_name: 网络重传无退出条件
          description: 网络重传循环无退出条件
          expected_answer: "违规：循环次数无限制,可能造成死循环"
          git:
            repo_url: "test_external_projects/c_test_project_no_comment"
            branch: "feature/enhanced-evaluation-modules"
            commit: "7ecb213"
            mr_id: "MR-011"
            file_path: "src/network_retry.c"
            function_name: "unsafe_retry_mechanism"
  - id: COMM-01
    index_num: 6
    name: 通信
    rules:
    - id: COMM-01-ENDIAN
      index_num: 1
      name: 数据格式
      principles:
      - content: 跨系统通信需要考虑字节序转换
        pos_examples:
          - case_name: 网络协议发送前转网络序
            description: 在网络协议栈中，发送前使用htonl转换整型
            expected_answer: "没有违规"
            think: "显式字节序转换,跨平台正确"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-012"
              file_path: "src/network_protocol.c"
              function_name: "safe_network_send"
        neg_examples:
          - case_name: 网络协议直接发送主机序
            description: 网络协议未做字节序转换
            expected_answer: "违规：未考虑跨系统字节序差异"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-012"
              file_path: "src/network_protocol.c"
              function_name: "unsafe_network_send"
    - id: COMM-01-VALIDATE
      index_num: 2
      name: 合法性校验
      principles:
      - content: 进程间通信时,接收方需要进行相关数据的校验,避免数据非法,导致处理异常。
        pos_examples:
          - case_name: IPC消息接收后校验
            description: 在进程间通信中，收到消息后校验长度与版本
            expected_answer: "没有违规"
            think: "防止非法数据导致越界或逻辑错误"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-013"
              file_path: "src/ipc_communication.c"
              function_name: "safe_ipc_message_handle"
        neg_examples:
          - case_name: IPC消息直接使用字段
            description: 进程间通信未校验长度直接解析
            expected_answer: "违规：未对进程间数据进行校验"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-013"
              file_path: "src/ipc_communication.c"
              function_name: "unsafe_ipc_message_handle"
  - id: LOCK-01
    index_num: 7
    name: 锁
    rules:
    - id: LOCK-01-MUTEX
      index_num: 1
      name: 锁类型
      principles:
      - content: 转发面禁止使用mutex类型锁
        pos_examples:
          - case_name: 转发面使用spinlock
            description: 在数据包转发中使用pthread_spinlock_t
            expected_answer: "没有违规"
            think: "spinlock适用于转发面,无阻塞"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-014"
              file_path: "src/packet_forwarding.c"
              function_name: "safe_packet_forward"
        neg_examples:
          - case_name: 转发面使用mutex
            description: 在数据包转发中错误使用pthread_mutex_lock
            expected_answer: "违规：转发面使用mutex"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-014"
              file_path: "src/packet_forwarding.c"
              function_name: "unsafe_packet_forward"
  - id: SEC-01
    index_num: 8
    name: 安全函数
    rules:
    - id: SEC-01-DEST
      index_num: 1
      name: 目的缓冲区
      principles:
      - content: 目的缓冲区为字节数,当目的缓冲为数组类型变量destBuff[BUFF_SIZE]形式, 长度大小使用 sizeof(destBuff) ,禁止直接使用BUFF_SIZE作为长度。
        pos_examples:
          - case_name: 安全函数使用sizeof获取数组长度
            description: 在加密模块中，使用sizeof获取数组长度
            expected_answer: "没有违规"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-015"
              file_path: "src/encryption_module.c"
              function_name: "safe_encryption_buffer_copy"
        neg_examples:
          - case_name: 安全函数直接使用宏常量作长度
            description: 在加密模块中错误使用宏常量代替sizeof
            expected_answer: "违规：硬编码长度"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-015"
              file_path: "src/encryption_module.c"
              function_name: "unsafe_encryption_buffer_copy"
  - id: PRIV-01
    index_num: 9
    name: 安全隐私
    rules:
    - id: PRIV-01-LOG
      index_num: 1
      name: 隐私保护
      principles:
      - content: 禁止在日志中记录用户隐私信息,包括IMSI,IMEI,MSISDN,用户IP（IPV4、IPV6）,MSID,CUI,MAC,usermane,password,密钥等,如果外部需求记录需联系安全SE确认实现方案；
        pos_examples:
          - case_name: 日志打印非隐私信息
            description: 在用户管理模块中，日志仅输出计数器
            expected_answer: "没有违规"
            think: "不涉及任何列出的隐私字段"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-016"
              file_path: "src/user_management.c"
              function_name: "safe_user_logging"
        neg_examples:
          - case_name: 日志打印用户IPv4地址
            description: 在用户管理模块中，日志直接打印client_ip
            expected_answer: "违规：日志记录用户IP"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-016"
              file_path: "src/user_management.c"
              function_name: "unsafe_user_logging"
  - id: CALC-01
    index_num: 11
    name: 运算
    rules:
    - id: CALC-01-OVERFLOW
      index_num: 1
      name: 运算溢出
      principles:
      - content: 两个数据相加或相乘时一定要考虑是否会溢出,造成数值翻转等错误。
        pos_examples:
          - case_name: 计费系统先检查再相加
            description: 在计费系统中，使用UINT32_MAX提前判断
            expected_answer: "没有违规"
            think: "提前防止加法溢出"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-017"
              file_path: "src/billing_system.c"
              function_name: "safe_billing_calculation"
        neg_examples:
          - case_name: 计费系统直接相加
            description: 在计费系统中未检查溢出
            expected_answer: "违规：可能数值翻转"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-017"
              file_path: "src/billing_system.c"
              function_name: "unsafe_billing_calculation"
  - id: DBG-01
    index_num: 12
    name: 调测机制
    rules:
    - id: DBG-01-LOGIMPL
      index_num: 1
      name: 日志输出
      principles:
      - content: 禁止私自实现日志功能,私有实现容易遗漏日志管理功能,导致日志过多而磁盘满上报告警并影响业务。
        pos_examples:
          - case_name: 监控系统使用平台统一日志接口
            description: 在监控系统中调用PS_WriteLog
            expected_answer: "没有违规"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-018"
              file_path: "src/monitoring_system.c"
              function_name: "safe_monitoring_log"
        neg_examples:
          - case_name: 监控系统私有printf日志
            description: 在监控系统中自行用printf写日志
            expected_answer: "违规：私自实现日志"
            git:
              repo_url: "test_external_projects/c_test_project_no_comment"
              branch: "feature/enhanced-evaluation-modules"
              commit: "7ecb213"
              mr_id: "MR-018"
              file_path: "src/monitoring_system.c"
              function_name: "unsafe_monitoring_log" 