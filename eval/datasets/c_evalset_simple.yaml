name: 简化编码守则测试集
version: 202507280000
description: "用于测试默认Git配置功能的简化评测集"
created_at: "2025-07-28T00:00:00+08:00"
updated_at: "2025-07-28T00:00:00+08:00"
rule_categories:
  - id: MEM-01
    name: 内存操作
    index_num: 1
    rules:
    - id: MEM-01-COPY
      index_num: 1
      name: 内存拷贝
      principles:
      - content: 内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。
        pos_examples:
          - case_name: 安全内存拷贝示例
            description: 正确的内存拷贝实现
            expected_answer: "没有违规"
            think: "显式校验避免越界，符合安全要求"
            # 注意：这里没有git信息，无code
        neg_examples:
          - case_name: 不安全内存拷贝示例
            description: 未检查缓冲区大小直接拷贝
            expected_answer: "违规：可能发生缓冲区越界"
            think: "缺少边界检查，存在安全风险"
            # 注意：这里没有git信息，无code
    - id: STR-01-FORMAT
      index_num: 2
      name: 字符串格式
      principles:
      - content: 不要将二进制码流当做字符串操作,可能不存在'\0'结束符,造成访问越界
        pos_examples:
          - case_name: 正确字符串处理示例
            description: 正确处理字符串数据
            expected_answer: "没有违规"
            think: "正确处理字符串，避免越界"
            # 注意：这里没有git信息，无code
        neg_examples:
          - case_name: 错误字符串处理示例
            description: 错误使用字符串函数处理二进制数据
            expected_answer: "违规：可能因无'\0'而越界"
            think: "错误使用字符串函数，存在越界风险"
            # 注意：这里没有git信息，无code