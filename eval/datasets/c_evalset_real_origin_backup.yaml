name: 通用编码守则-基础篇
version: 202507072020
rule_categories:
  - id: MEM-01
    name: 内存操作
    index_num: 1
    rules:
    - id: MEM-01-COPY
      index_num: 1
      name: 内存拷贝
      principles:
      - content: 内存拷贝时访问目的缓冲区、源内存是否会发生越界,注意边界值。
        pos_examples:
          - case_name: 拷贝前检查边界
            description: 先确认源长度不大于目标剩余空间
            expected_answer: "没有违规"
            think: "显式校验避免越界"
            code: |
              if (srcLen <= dstMax) {
                  memcpy(dst, src, srcLen);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 直接拷贝
            description: 未检查长度越界
            expected_answer: "违规：可能发生缓冲区越界"
            code: |
              memcpy(dst, src, srcLen); /* dst可能不足 */
    - id: MEM-01-MOVE
      index_num: 1
      name: 内存搬移
      principles:
      - content: 不要使用memcpy_s函数操作有地址重叠的内存,行为结果未知,推荐使用memove_s。
        pos_examples:
          - case_name: 重叠区域用memmove_s
            description: 源目地址重叠时使用memmove_s
            expected_answer: "没有违规"
            think: "memmove_s保证重叠区正确复制"
            code: |
              memmove_s(dst, dstLen, src, len);
        neg_examples:
          - case_name: 重叠仍用memcpy_s
            description: 地址重叠却调用memcpy_s
            expected_answer: "违规：应使用memmove_s"
            code: |
              memcpy_s(dst, dstLen, src, len); /* 重叠未处理 */
  - id: STR-01
    name: 字符串操作
    rules:
    - id: STR-01-FOMAT
      index_num: 1
      name: 注意数据格式
      principles:
      - content: 不要将二进制码流当做字符串操作,可能不存在'\0'结束符,造成访问越界
        pos_examples:
          - case_name: 按字节处理二进制
            description: 使用memcpy_s按字节拷贝二进制数据
            expected_answer: "没有违规"
            think: "显式指定长度,不依赖'\0'"
            code: |
              memcpy_s(dst, dstLen, src, exactLen);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""   
        neg_examples:
          - case_name: 把二进制当字符串
            description: 用strlen计算二进制长度
            expected_answer: "违规：可能因无'\0'而越界"
            code: |
              UINT32 len = strlen(binData); /* binData可能不含'\0' */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""     
    - id: STR-01-FUNC
      index_num: 2
      name: 字符串操作函数
      principles:
      - content: 使用memcpy_s接口操作字符串时要考虑'\0'携带结束符
        suggestion: 使用strcpy_s、strcat_s等字符串操作接口处理字符串,接口内部已经考虑了结束符'\0'处理
        pos_examples:
          - case_name: 使用strcpy_s复制字符串
            description: 自动处理'\0'终止符
            expected_answer: "没有违规"
            think: "strcpy_s内部复制'\0'"
            code: |
              strcpy_s(dst, dstLen, src);
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""               
        neg_examples:
          - case_name: memcpy_s复制字符串
            description: 未预留'\0'位置
            expected_answer: "违规：未考虑终止符"
            code: |
              memcpy_s(dst, dstLen, src, strlen(src)); /* 漏'\0' */
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""               
  - id: ARR-01
    index_num: 3
    name: 数组操作
    rules:
    - id: ARR-01-INDEX
      index_num: 1
      name: 下标访问
      principles:
        - content: 数组下标访问前做有效性校验,确保访问不越界。
          pos_examples:
            - case_name: 先检查再访问
              description: 访问前校验下标范围
              expected_answer: "没有违规"
              think: 显式防止越界
              code: |
                if (idx < ARRAY_LEN(buf)) {
                    val = buf[idx];
                }
              git:
                repo_url: ""
                branch: ""
                commit: ""
                mr_id: ""
          neg_examples:
            - case_name: 直接访问
              description: 未校验下标直接取值
              expected_answer: "违规：可能越界访问"
              think: idx 可能超出数组范围
              code: |
                val = buf[idx];
              git:
                repo_url: ""
                branch: ""
                commit: ""
                mr_id: ""
    - id: ARR-01-DEFINE
      index_num: 2  
      name: 数组定义
      principles:
      - content: 避免使用二级以及以上指针结构操作多维数组,容易造成代码晦涩难懂,操作出错,建议通过结构体分级定义或者每一级单独使用变量表达。
        pos_examples:
        - case_name: 推荐用法
          description: 推荐用法,单独定义偏移后的指针操作
          expected_answer: "没有违规"        
          think: "遵循了规则的建议,单独定义偏移后的指针操作,并且没有使用二级及以上的指针定义数组,符合规范"
          code: |
            ```
            g_apSMTempVal[0] = (VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UINT8 *),"g_saDecryptBlockPtr");
            UINT8 *curInst = (UINT8 *)((UINT8 *)g_apSMTempVal[0] + sizeof(UINT8 *) * inst);*curInst = (UINT8 *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UINT8), "g_saDecryptBlockmem");
            ```
          git:
            repo_url: ""
            branch: ""
            commit: ""
            mr_id: ""           
          
        neg_examples:
        - case_name: 使用了二级数组指针
          description: 如下代码申请全部线程内存头指针数组,使用全局变量保存内存起始地址。然后再按照线程实例申请线程级实际内存,写入线程级头地址时操作错误。编码上使用了二级数组指针
          think: ""
          code: |
            ```
            UCHAR **g_saDecryptBlockPtr=(UCHAR **)(&g_apSMTempVal[0]);

            UINT32 SA_EgnInitQuicDecryptBlockMem_P005(UCHAR instId)
            {
              UINT32 arryIdx = instId & (MAX_THREAD_NUM_IN_NODE - 1);
              if (*g saDecryptBlockPtr != NULL) {
                if (g_saDecryptBlockPtr[arryIdx] != NULL) {
                    UCHAR *instMemAddr = (UCHAR *)g_saDecryptBlockPtr[arryIdx];
                    (VOID)memset_s((VOID *))instMemAddr, SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE * sizeof(UCHAR), 0,SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE * sizeof(UCHAR));
                    return VOS_OK;
                }
                return VOS_ERR;
              }
              *g_saDecryptBlockPtr = (SM_VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UCHAR *),"g_saDecryptBlockptn");

              if (*g_saDecryptBlockPtr == NULL) {
                SA_CNTR_ADD(instId, SA_U_ERR_PATCH_02);
                return VOS_ERR;
              }

              g_saDecryptBlockPtr[arryIdx] = (SM_VOID *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UCHAR *),g_seuecrypcBlockmem);

              if (g_saDecryptBlockPtr[arryIdx] == NULL) {
                SA_CNTR_ADD(instId, SA_U_ERR_PATCH_02);
                return vos_ERR;
              }
              UCHAR *instMemAddr = g_saDecryptBlockPtr[arryIdx];
              (VOID)memset_s((VOID *)instMemAddr, SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE * sizeof(UCHAR), ,
                  SA QUIC_ DECRYPT_BLOCK_BUFF_SIZE * siZeof(UCHAR);
              SA_CNTR_ADD(instId, SA_U_NORM_PATCH_05);
              return vos_oK;
            }
            ```
          git:
            repo_url: ""
            branch: ""
            commit: ""
            mr_id: ""                  
        - case_name: 使用指向数组指针操作-hard negative
          description: 使用指向数组指针操作,理解也不太直观,不推荐
          expected_answer: "虽然没有使用二级及以上指针,但是没有遵循规则建议通过结构体分级定义或者每一级单独使用变量表达"
          think: "虽然没有使用二级及以上指针,但是规则中建议通过结构体分级定义或者每一级单独使用变量表达,为了谨慎起见应该视作不通过"
          code: |
            ```
            g_apSMTempVal[0] = (VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UINT8 *), "g_saDecryptBlockPtr");
            UINT8* (*instArray)[MAX_THREAD_NUM_IN_NODE] = (UINT8* (*)[MAX_THREAD_NUM_IN_NODE])(g_apSMTempVal[0]);
            (*instArray)[inst] = (UINT8 *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UINT8), "g_saDecryptBlockmem");
            ```
          git:
            repo_url: ""
            branch: ""
            commit: ""
            mr_id: ""

      - content: 禁止在函数中定义占用超过500字节的数组(例如:char array[1000]),该数组会占用栈空间,在多级调用叠加后可能导致栈溢出,超大数组使用动态申请或者全局变量。
        pos_examples:
          - case_name: 动态申请大缓冲区
            description: 使用 UTIL_MallocDyn 申请堆内存避免大栈数组
            expected_answer: "没有违规"
            think: 函数内未出现大于 500 B 的栈数组,符合规范
            code: |
              VOID Process()
              {
                  UINT8 *buf = (UINT8 *)UTIL_MallocDyn(0, 1024);
                  if (buf == NULL) return;
                  /* 使用 buf */
                  UTIL_FreeDyn(0, buf);
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""
        neg_examples:
          - case_name: 函数内定义大栈数组
            description: 在栈上定义 1 KiB 缓冲区
            expected_answer: "违规：函数内定义超过 500 字节栈数组"
            think: 1024 B > 500 B,可能导致栈溢出
            code: |
              VOID Process()
              {
                  CHAR buf[1024]; /* 违规大栈数组 */
                  /* 使用 buf */
              }
            git:
              repo_url: ""
              branch: ""
              commit: ""
              mr_id: ""

    - id: ARR-01-TYPE
      index_num: 3
      name: 数组类型
      principles:
      - content: C语言不同于高级语言,不同类型的数组指针不能通过强转使用,会造成数据错误或者越界读写
        pos_examples:
        - case_name: 正确保持类型一致
          description: 保持数组元素类型与指针类型完全一致
          expected_answer: "没有违规"
          think: "UINT8数组使用UINT8指针访问,类型匹配,无强转"
          code: |
            UINT8 buffer[16];
            UINT8 *p = buffer;
            p[0] = 0xAA;
        neg_examples:
        - case_name: 强制类型转换
          description: 将UINT8数组强转为UINT32指针后访问
          expected_answer: "违规：强转类型后访问数组,可能导致越界"
          think: "UINT8[16]被当作UINT32*使用,一次访问4字节,越界风险"
          code: |
            UINT8 buffer[16];
            UINT32 *p = (UINT32 *)buffer;
            p[3] = 0x11223344;   /* 越界写入 */    
    - id: ARR-01-LENGTH
      index_num: 4
      name: 数组类型
      principles:
      - content: 数组作为函数参数时禁止在函数内使用sizeof计算数组占用空间,必须同时将数组长度作为函数参数传入。
        suggestion: 计算元素个数使用宏#define ARRAY_LEN(arr) (sizeof(arr)/sizeof((arr)[0]))
        pos_examples:
        - case_name: 显式传入长度
          description: 通过额外参数传入数组长度
          expected_answer: "没有违规"
          think: "函数内不依赖sizeof(arr),符合规范"
          code: |
            void PrintArray(const UINT32 arr[], UINT32 len)
            {
                for (UINT32 i = 0; i < len; ++i) {
                    printf("%u\n", arr[i]);
                }
            }
        neg_examples:
        - case_name: 使用sizeof计算参数数组长度
          description: 在函数体内使用sizeof(arr)计算长度
          expected_answer: "违规：数组作为参数时sizeof(arr)返回指针大小"
          think: "形参arr退化为指针,sizeof(arr)不等于数组总字节数"
          code: |
            void PrintArray(UINT32 arr[])
            {
                UINT32 len = sizeof(arr) / sizeof(arr[0]); /* 错误 */
                for (UINT32 i = 0; i < len; ++i) {
                    printf("%u\n", arr[i]);
                }
            }
  - id: RES-04
    index_num: 4
    name: 资源使用
    rules:
    - id: RES-04-ALLOC
      index_num: 1
      name: 内存申请
      principles:
      - content: |
          不要使用malloc和free等申请系统内存,系统内存预留有限,并且出现内存泄漏时难以定位。使用带有内存统计接口,定义在mem_api.h,有临时内存、用户级内存、非用户级内存、动态内存等申请接口,请仔细阅读接口功能描述,根据实际场景选择。
          以下接口不要使用:
          ```
          PS_MemAlloc
          PSP_Malloc
          VOS_MemAlloc_F
          VOS_MemAllocAlignN
          PS_MemAllocAlign16
          MEM_ShmAllocByName
          MEM_PrivMAllocByName
          MEM_ShrMemAlloc
          PS_MemFree
          PSP_Free
          PS_ShrMemFree
          ```
        suggestion: 使用带有内存统计接口,如UTIL_MallocDyn、UTIL_MallocFix等。
        neg_examples:
        - case_name: 使用系统malloc
          description: 直接调用malloc申请内存
          expected_answer: "违规:使用了禁止的malloc接口"
          think: "未使用UTIL_*系列接口,且malloc不在白名单"
          code: |
            void *p = malloc(256);
            if (p) free(p);
    - id: RES-04-FREE
      index_num: 2
      name: 内存释放
      principles:
      - content: 申请临时内存资源注意在函数执行完成后释放,提前退出的分支也要考虑释放。
        pos_examples:
        - case_name: 所有路径均释放
          description: 提前返回前释放临时内存
          expected_answer: "没有违规"
          think: "所有return前都调用了UTIL_FreeTmp"
          code: |
            VOID Foo()
            {
                VOID *tmp = UTIL_MallocTmp(0, 100);
                if (tmp == NULL) return;
                if (SomeCheck() != OK) {
                    UTIL_FreeTmp(0, tmp);
                    return;
                }
                UTIL_FreeTmp(0, tmp);
            }
        neg_examples:
        - case_name: 正常分支未释放
          description: 仅异常分支释放,正常分支遗漏
          expected_answer: "违规：正常分支未释放临时内存"
          think: "tmpmem在成功路径未释放"
          code: |
            UINT32 Query(UINT32 id, CHAR *out, UINT32 outLen)
            {
                VOID *tmp = UTIL_MallocTmp(0, 64);
                if (tmp == NULL) return ERR;
                if (GetById(id, tmp, 64) != OK) {
                    UTIL_FreeTmp(0, tmp);
                    return ERR;
                }
                strcpy_s(out, outLen, (CHAR*)tmp);
                return OK; /* 此处未释放tmp */
            }
      - content: 申请消息、表项等资源在使用前退出的分支要考虑释放。
        extra_resource: https://codehub-y.huawei.com/5gcore/up/domain/common/merge_requests/4432  
    - id: RES-04-CURSOR
      index_num: 3
      name: 游标释放
      principles:
      - content: |
          游标是指在遍历配置DB、DDF、CDDB等记录时平台接口内会自动申请一个资源记录遍历过程的信息,如果使用next接口依次遍历到最后返回失败,平台会自动释放游标。如果遍历中间退出时需要业务释放游标,否则就会发生游标泄露。游标总数有限,如果全部泄露光后调用查询接口将失败,影响业务正常运行。目前需要考虑释放游标的接口:
          (1) 配置DB:

          DBApiQueryFirst_Adapt
          DBApiQueryNext_Adapt(遍历接口)
          DBApiQueryEnd_Adapt(释放接口)

          OMPROXY_CdbQueryFirst
          OMPROXY_CdbQueryNext(遍历接口)
          OMPROXY_CdbQueryEnd(释放接口)
        
          (2) DDF:

          UPDBADPT_QueryFirst
          UPDBADPT_QueryNext(遍历接口)
          UPDBADPT_QueryEnd(释放接口)

          (3) lua:

          spt_Query(查询接口)
          spt_EndQuery(释放接口)

        neg_examples:
        - case_name: 中途返回未释放游标
          description: 遍历中途异常退出未调QueryEnd
          expected_answer: "违规：游标泄露"
          think: "异常分支未调用OMPROXY_CdbQueryEnd释放游标"
          code: |
            VOID Iterate()
            {
                CURSOR_S cursor;
                if (OMPROXY_CdbQueryFirst(&cursor) != OK) return;
                while (Next()) {
                    if (Check()) break; /* 提前退出,未QueryEnd */
                }
            }      
      - content: lua中只要spt_Query查询成功无论spt_GetNextRecord接口是否失败都要使用spt_EndQuery进行释放。
        extra_resource: http://3ms.huawei.com/hi/group/2823529/wiki_5322487.html          
    - id: RES-04-IMPLICIT
      index_num: 4
      name: 隐式内存释放
      principles:
      - content: strdup、vasprint接口内有申请内存操作,使用后要进行释放
      - content: 使用默认参数调用pthread_create,线程退出时没有调用pthread_join造成栈内存泄露
      - content: |
          cJSON_Parse等接口中会创建cjson对象,调用这个接口以及其衍生的接口（CFG_ParseJsonFile等）解析json格式后需要调用cJSON_Delete释放cjson对象。
          ```
          cJSON_CreateObject
          cJSON_CreateArray
          cJSON_CreateString
          cJSON_CreateNumber
          cJSON_CreateBool
          cJSON_CreateNull
          cJSON_Parse

          # 以上接口创建的对象使用后需要调用cJSON_Delete进行释放
          ```
        extra_resource: https://codehub-y.huawei.com/5gcore/mse/vas/ssu/merge_requests/1581  
  - id: FUN-05
    index_num: 5
    name: 函数实现
    rules:
    - id: FUN-05-DEF
      index_num: 1
      name: 函数定义
      principles:
      - content: 定义递归函数,容易造成死循环或栈溢出。
        pos_examples:
        - case_name: 迭代实现阶乘
          description: 使用循环代替递归,控制栈深度
          expected_answer: "没有违规"
          think: "未使用递归,避免栈溢出风险"
          code: |
            UINT32 factorial(UINT32 n)
            {
                UINT32 result = 1;
                for (UINT32 i = 1; i <= n; ++i) {
                    result *= i;
                }
                return result;
            }
        neg_examples:
        - case_name: 递归阶乘实现
          description: 使用递归方式实现阶乘
          expected_answer: "违规：定义递归函数"
          think: "函数factorial递归调用自身,可能导致栈溢出"
          code: |
            UINT32 factorial(UINT32 n)
            {
                if (n == 0) return 1;
                return n * factorial(n - 1);
            }
      - content: 函数中循环定义要考虑其循环次数,避免运行次数过大造成死循环。
        pos_examples:
        - case_name: 带最大上限的循环
          description: 循环次数受MAX_COUNT限制
          expected_answer: "没有违规"
          think: "循环上限固定,防止死循环"
          code: |
            #define MAX_COUNT 1000
            VOID process()
            {
                for (UINT32 i = 0; i < MAX_COUNT; ++i) {
                    /* do work */
                }
            }
        neg_examples:
        - case_name: 无退出条件的while循环
          description: 循环条件恒真,可能死循环
          expected_answer: "违规：循环次数无限制,可能造成死循环"
          think: "while(1)无退出条件"
          code: |
            VOID loop()
            {
                while (1) {
                    /* do something */
                }
            }
      - content: 循环变量类型范围不要低于阈值类型范围,禁止使用低于32bit的类型循环变量。
        pos_examples:
        - case_name: 使用UINT32作为循环变量
          description: 循环变量为32位无符号整型
          expected_answer: "没有违规"
          think: "i为UINT32,满足≥32bit要求"
          code: |
            VOID iterate(UINT32 times)
            {
                for (UINT32 i = 0; i < times; ++i) {
                    /* do work */
                }
            }
        neg_examples:
        - case_name: 使用UCHAR作为循环变量
          description: 循环变量为8位无符号整型
          expected_answer: "违规：循环变量类型低于32bit"
          think: "i为UCHAR类型,仅8bit"
          code: |
            VOID iterate(UINT32 times)
            {
                for (UCHAR i = 0; i < times; ++i) {
                    /* do work */
                }
            }
    - id: FUN-05-CALLBACK
      index_num: 2
      name: 回调函数
      principles:
      - content: 注册给平台的回调函数是在平台的线程执行的,与业务不在同一线程,在该回调函数中直接做业务处理,与业务线程存在访问冲突的风险。例如绝对定时器VOS_AbstmrCbkInit。
        pos_examples:
          - case_name: 回调仅发消息
            description: 在回调中仅向业务线程发消息,不直接操作共享数据
            expected_answer: "没有违规"
            think: "通过消息队列异步处理,避免跨线程直接访问"
            code: |
              VOID TimerCallback(UINT32 timerId)
              {
                  MSG_S msg = {0};
                  msg.id   = MSG_TIMER_EXPIRY;
                  msg.data = timerId;
                  SendMsgToBusinessThread(&msg);
              }
        neg_examples:
          - case_name: 回调直接改全局变量
            description: 在回调函数内直接修改业务全局变量
            expected_answer: "违规：回调线程直接操作业务共享数据"
            think: "g_counter可能被业务线程并发访问,存在竞态条件"
            code: |
              UINT32 g_counter = 0;
              VOID TimerCallback(UINT32 timerId)
              {
                  g_counter++; /* 与业务线程冲突 */
              }
        extra_resource: https://3ms.huawei.com/hi/group/1008251/wiki_6353724.html  
    - id: FUN-05-PARAM
      index_num: 3
      name: 函数参数
      principles:
      # ---------- 原则 1 ----------
      - content: 禁止函数中修改指针参数的值。C语言中指针类型参数本质属于局部变量,注意不要与C++引用混淆,直接修改参数的值不会改变原指针值。
        pos_examples:
          - case_name: 通过临时指针遍历
            description: 函数内部使用临时指针变量移动,不修改实参指针
            expected_answer: "没有违规"
            think: "仅修改局部变量tmp,未修改实参buf"
            code: |
              void fill_zero(UINT8 *buf, UINT32 len)
              {
                  UINT8 *tmp = buf;
                  while (len--) {
                      *tmp++ = 0;
                  }
              }
        neg_examples:
          - case_name: 直接修改指针参数
            description: 函数内对指针形参进行自增
            expected_answer: "违规：修改了指针参数的值"
            think: "buf++只改变了形参局部副本,调用方实参未变,且代码意图易混淆"
            code: |
              void fill_zero(UINT8 *buf, UINT32 len)
              {
                  while (len--) {
                      *buf++ = 0;   /* 修改形参buf */
                  }
              }
        extra_resource: https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/DTS2022073000911  
      # ---------- 原则 2 ----------
      - content: 调用函数时严格区分使用指针还是指针地址,避免错误的操作内存,导致无法预期的行为。
        pos_examples:
          - case_name: 正确传递指针
            description: 需要修改指针指向时传递指针的地址
            expected_answer: "没有违规"
            think: "传入&ptr,函数通过二级指针修改原指针"
            code: |
              void alloc_buffer(UINT8 **pptr, UINT32 len)
              {
                  *pptr = UTIL_MallocDyn(0, len);
              }

              /* 调用方 */
              UINT8 *p = NULL;
              alloc_buffer(&p, 256);
        neg_examples:
          - case_name: 混淆指针与指针地址
            description: 应传二级指针却传一级指针
            expected_answer: "违规：未区分指针与指针地址"
            think: "调用方传p而非&p,函数内会把p的内容当地址使用,导致异常"
            code: |
              void alloc_buffer(UINT8 **pptr, UINT32 len)
              {
                  *pptr = UTIL_MallocDyn(0, len);
              }

              /* 调用方错误示范 */
              UINT8 *p = NULL;
              alloc_buffer((UINT8 **)&p, 256);   /* 错误：强转掩盖了类型不匹配 */
        extra_resource: https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/DTS2022051216813  
    - id: FUN-05-FUNC
      index_num: 4
      name: 函数功能
      principles:
      - content: 实现IPv4处理时要同步考虑IPv6实现。
        pos_examples:
          - case_name: IPv4/IPv6 统一处理接口
            description: 新增地址解析接口同时支持 IPv4 与 IPv6
            expected_answer: "没有违规"
            think: "函数内部根据地址族区分处理,IPv6 逻辑已同步实现"
            code: |
              INT32 ParseIP(const CHAR *ipStr, UINT8 *buf, UINT32 bufLen, UINT16 *addrFamily)
              {
                  if (inet_pton(AF_INET, ipStr, buf) == 1) {
                      *addrFamily = AF_INET;
                      return 0;
                  }
                  if (inet_pton(AF_INET6, ipStr, buf) == 1) {
                      *addrFamily = AF_INET6;
                      return 0;
                  }
                  return -1;
              }
        neg_examples:
          - case_name: 仅处理 IPv4 地址
            description: 仅使用 inet_addr 解析 IPv4,无 IPv6 分支
            expected_answer: "违规：未同步考虑 IPv6 实现"
            think: "后续若需支持 IPv6 必须返工,增加成本"
            code: |
              UINT32 ParseIPv4Only(const CHAR *ipStr)
              {
                  return (UINT32)inet_addr(ipStr); /* 仅支持 IPv4 */
              }
  # 6. 通信
  - id: COMM-01
    index_num: 6
    name: 通信
    rules:
    # 6.1 数据格式
    - id: COMM-01-ENDIAN
      index_num: 1
      name: 数据格式
      principles:
      - content: 跨系统通信需要考虑字节序转换
        pos_examples:
          - case_name: 发送前转网络序
            description: 发送前使用htonl转换整型
            expected_answer: "没有违规"
            think: "显式字节序转换,跨平台正确"
            code: |
              UINT32 value = 0x12345678;
              UINT32 netValue = htonl(value);
              Send(&netValue, sizeof(netValue));
        neg_examples:
          - case_name: 直接发送主机序
            description: 未做任何字节序转换
            expected_answer: "违规：未考虑跨系统字节序差异"
            think: "主机序与网络序可能不同,接收端解析错误"
            code: |
              UINT32 value = 0x12345678;
              Send(&value, sizeof(value));

    # 6.2 合法性校验
    - id: COMM-01-VALIDATE
      index_num: 2
      name: 合法性校验
      principles:
      - content: 进程间通信时,接收方需要进行相关数据的校验,避免数据非法,导致处理异常。
        pos_examples:
          - case_name: 收到消息后校验长度与版本
            description: 先检查消息长度和版本号
            expected_answer: "没有违规"
            think: "防止非法数据导致越界或逻辑错误"
            code: |
              VOID HandleMsg(const MSG_HDR *hdr, UINT32 len)
              {
                  if (len < sizeof(MSG_HDR) || hdr->version != MSG_VERSION) {
                      return;
                  }
                  /* 继续处理 */
              }
        neg_examples:
          - case_name: 直接使用消息字段
            description: 未校验长度直接解析
            expected_answer: "违规：未对进程间数据进行校验"
            think: "len可能小于sizeof(MSG_HDR)"
            code: |
              VOID HandleMsg(const MSG_HDR *hdr, UINT32 len)
              {
                  UINT32 value = hdr->payload[0]; /* 可能越界 */
              }

      - content: 对于可选信元,其对应数据可能无效,使用前需要做有效性检查。
        pos_examples:
          - case_name: 可选字段先判有效性
            description: 检查hasFlag标志再使用flag值
            expected_answer: "没有违规"
            think: "避免使用未提供的可选字段"
            code: |
              VOID ProcOpt(const MSG_S *msg)
              {
                  if (msg->hasFlag) {
                      UseFlag(msg->flag);
                  }
              }
        neg_examples:
          - case_name: 直接使用可选信元
            description: 未判断hasX直接使用x
            expected_answer: "违规：未检查可选信元有效性"
            think: "x字段可能未被填充"
            code: |
              VOID ProcOpt(const MSG_S *msg)
              {
                  UseFlag(msg->flag); /* flag可能无效 */
              }
  # 7. 锁
  - id: LOCK-01
    index_num: 7
    name: 锁
    rules:
    # 7.1 锁类型
    - id: LOCK-01-MUTEX
      index_num: 1
      name: 锁类型
      principles:
      - content: 转发面禁止使用mutex类型锁
        pos_examples:
          - case_name: 转发面使用spinlock
            description: 使用pthread_spinlock_t
            expected_answer: "没有违规"
            think: "spinlock适用于转发面,无阻塞"
            code: |
              pthread_spinlock_t g_lock;
              VOID FifoTask()
              {
                  pthread_spin_lock(&g_lock);
                  /* critical section */
                  pthread_spin_unlock(&g_lock);
              }
        neg_examples:
          - case_name: 转发面使用mutex
            description: 直接调用pthread_mutex_lock
            expected_answer: "违规：转发面使用mutex"
            think: "mutex可能阻塞,违背转发面要求"
            code: |
              pthread_mutex_t g_mutex;
              VOID FifoTask()
              {
                  pthread_mutex_lock(&g_mutex);
                  /* critical section */
                  pthread_mutex_unlock(&g_mutex);
              }
    # 7.2 共享范围
    - id: LOCK-01-SCOPE
      index_num: 2
      name: 共享范围
      principles:
      - content: 转发面（FIFO线程）和控制面共享锁时,需使用trylock,否则容易造成死锁。
        pos_examples:
          - case_name: 共享锁使用trylock
            description: 使用pthread_mutex_trylock避免阻塞
            expected_answer: "没有违规"
            think: "FIFO线程不会阻塞在控制面锁"
            code: |
              VOID FifoTask()
              {
                  if (pthread_mutex_trylock(&g_shared) == 0) {
                      /* critical section */
                      pthread_mutex_unlock(&g_shared);
                  } else {
                      /* retry later */
                  }
              }
        neg_examples:
          - case_name: FIFO线程直接lock共享mutex
            description: 未使用trylock
            expected_answer: "违规：可能造成FIFO线程死锁"
            think: "FIFO线程阻塞等待控制面释放锁"
            code: |
              VOID FifoTask()
              {
                  pthread_mutex_lock(&g_shared); /* 阻塞风险 */
              }

      - content: 锁的范围设计要合理,不要过大造成串行瓶颈,在控制一定业务复杂度下越小越好。
        pos_examples:
          - case_name: 最小临界区
            description: 仅保护共享计数器更新
            expected_answer: "没有违规"
            think: "锁粒度最小,减少串行化"
            code: |
              VOID IncCounter()
              {
                  pthread_spin_lock(&g_ctrLock);
                  g_counter++;
                  pthread_spin_unlock(&g_ctrLock);
                  /* 其他处理在锁外 */
              }
        neg_examples:
          - case_name: 大临界区
            description: 整个业务处理加锁
            expected_answer: "违规：锁粒度过大"
            think: "业务逻辑全部串行,成为瓶颈"
            code: |
              VOID HandlePacket(PACKET *pkt)
              {
                  pthread_mutex_lock(&g_bigLock);
                  Parse(pkt);
                  Route(pkt);
                  Forward(pkt);
                  pthread_mutex_unlock(&g_bigLock);
              }
  # 8.安全函数
  - id: SEC-01
    name: 安全函数
    description: |
      安全函数使用说明参考如下链接CMC DOC中chm文档：
      https://3ms.huawei.com/next/groups/index.html#/wiki/detail?groupId=3862259&wikiId=6662388  

      Huawei Secure C V100R001C01SPC017B001版本CMC链接如下：
      https://cmc-szv.clouddragon.huawei.com/cmcversion/index/releaseView?deltaId=10191407089386628&isSelect=Doc  
    rules:
    # 8.1 目的缓冲区
    - id: SEC-01-DEST
      index_num: 1
      name: 目的缓冲区
      principles:

      - content: 目的缓冲区为字节数,当目的缓冲为数组类型变量destBuff[BUFF_SIZE]形式, 长度大小使用 sizeof(destBuff) ,禁止直接使用BUFF_SIZE作为长度。
        pos_examples:
          - case_name: 使用sizeof获取数组长度
            description: 数组长度用sizeof(destBuff)计算
            expected_answer: "没有违规"
            code: |
              CHAR destBuff[128];
              memcpy_s(destBuff, sizeof(destBuff), src, srcLen);
        neg_examples:
          - case_name: 直接使用宏常量作长度
            description: 用BUFF_SIZE常量代替sizeof
            expected_answer: "违规：硬编码长度"
            code: |
              #define BUFF_SIZE 128
              CHAR destBuff[BUFF_SIZE];
              memcpy_s(destBuff, BUFF_SIZE, src, srcLen);

      - content: 字符串类型操作需要考虑带结束符'\0'后的缓冲区长度。
        pos_examples:
          - case_name: 为'\0'预留空间
            description: 字符串缓冲区大小包含终止符
            expected_answer: "没有违规"
            code: |
              CHAR buf[16];
              strcpy_s(buf, sizeof(buf), "hello");
        neg_examples:
          - case_name: 未预留终止符空间
            description: 缓冲区大小刚好等于源字符串长度
            expected_answer: "违规：未考虑终止符"
            code: |
              CHAR buf[5];
              strcpy_s(buf, sizeof(buf), "hello"); /* 需要6字节 */
      - content: "memcpy_s用法: 禁止出现源目的缓冲区重叠的用法。如过无法避免,使用memmove_s函数处理重叠的拷贝,并接受微弱性能劣化的影响。"
        pos_examples:
          - case_name: 重叠区域使用memmove_s
            description: 源目的缓冲区重叠改用memmove_s
            expected_answer: "没有违规"
            code: |
              UINT8 buf[10] = {0,1,2,3,4,5,6,7,8,9};
              memmove_s(buf + 2, sizeof(buf) - 2, buf, 6);
        neg_examples:
          - case_name: 重叠仍用memcpy_s
            description: 源目的缓冲区重叠却调用memcpy_s
            expected_answer: "违规：重叠缓冲区用memcpy_s"
            code: |
              UINT8 buf[10];
              memcpy_s(buf + 2, sizeof(buf) - 2, buf, 6);

      - content: 当安全函数操作的目的缓冲区地址为函数入参时,同时也须将目的缓冲区长度通过函数入参传递进来,不能靠外部保证安全而硬编码目的缓冲区长度。
        pos_examples:
          - case_name: 长度由调用者传入
            description: 目的缓冲区长度作为函数参数
            expected_answer: "没有违规"
            code: |
              VOID SafeCopy(CHAR *dest, UINT32 destLen, const CHAR *src)
              {
                  strcpy_s(dest, destLen, src);
              }
        neg_examples:
          - case_name: 函数内硬编码长度
            description: 目的缓冲区长度写死在函数内
            expected_answer: "违规：硬编码长度"
            code: |
              VOID SafeCopy(CHAR *dest, const CHAR *src)
              {
                  strcpy_s(dest, 128, src); /* 假设128字节,不安全 */
              }

    # 8.2 返回值
    - id: SEC-01-RET
      index_num: 2
      name: 返回值
      principles:

      - content: 安全函数一定要判断返回值进行相应的处理。
        pos_examples:
          - case_name: 检查返回值并处理错误
            description: 判断strcpy_s返回值
            expected_answer: "没有违规"
            code: |
              errno_t rc = strcpy_s(buf, sizeof(buf), src);
              if (rc != EOK) {
                  LOG_ERROR("copy failed");
                  return ERR;
              }
        neg_examples:
          - case_name: 忽略返回值
            description: 不检查安全函数返回值
            expected_answer: "违规：未处理返回值"
            code: |
              strcpy_s(buf, sizeof(buf), src); /* 未检查 */

      - content: 安全函数保护不能作为常规流程的长度检查手段,需要提前做显式检查。
        pos_examples:
          - case_name: 提前显式检查长度
            description: 先判断长度再调用安全函数
            expected_answer: "没有违规"
            code: |
              if (strlen(src) >= sizeof(buf)) {
                  return ERR_TOO_LONG;
              }
              strcpy_s(buf, sizeof(buf), src);
        neg_examples:
          - case_name: 仅靠安全函数截断
            description: 用安全函数当长度检查
            expected_answer: "违规：未预先显式检查"
            code: |
              strcpy_s(buf, sizeof(buf), src); /* 依赖截断 */

      - content: sprintf_s操作成功时返回写入的字符个数,失败返回-1,返回值大于0代表成功。
        pos_examples:
          - case_name: 正确判断sprintf_s返回值
            description: 返回值>0视为成功
            expected_answer: "没有违规"
            code: |
              INT32 n = sprintf_s(buf, sizeof(buf), "%d-%d", a, b);
              if (n > 0) {
                  /* success */
              }
        neg_examples:
          - case_name: 误判返回值
            description: 把-1当成功
            expected_answer: "违规：错误判断返回值"
            code: |
              if (sprintf_s(buf, sizeof(buf), "%d", val)) { /* -1会进if */
                  /* 错误地认为成功 */
              }

      - content: memset_s清零局部定义的固定结构
        pos_examples:
          - case_name: 清零局部结构体
            description: 用memset_s清空局部变量
            expected_answer: "没有违规（例外）"
            code: |
              MY_STRUCT st = {0};
              memset_s(&st, sizeof(st), 0, sizeof(st)); /* 例外：无需检查返回值 */
        neg_examples: []   # 该条为“例外”,无负例

    # 8.3 函数封装
    - id: SEC-01-WRAP
      index_num: 3
      name: 函数封装
      principles:
      - content: 禁止对安全函数进行自定义封装
        pos_examples:
          - case_name: 直接调用安全函数
            description: 代码中直接调用strcpy_s
            expected_answer: "没有违规"
            code: |
              strcpy_s(dest, destLen, src);
        neg_examples:
          - case_name: 自定义封装strcpy_s
            description: 在项目中包装一层MyStrCpy
            expected_answer: "违规：自定义封装安全函数"
            code: |
              #define MyStrCpy(d,s) strcpy_s(d,sizeof(d),s)
  # 9. 安全隐私
  - id: PRIV-01
    index_num: 9
    name: 安全隐私
    rules:
    # 9.1 隐私保护
    - id: PRIV-01-LOG
      index_num: 1
      name: 隐私保护
      principles:
      - content: 禁止在日志中记录用户隐私信息,包括IMSI,IMEI,MSISDN,用户IP（IPV4、IPV6）,MSID,CUI,MAC,usermane,password,密钥等,如果外部需求记录需联系安全SE确认实现方案；
        pos_examples:
          - case_name: 打印非隐私信息
            description: 日志中仅输出计数器
            expected_answer: "没有违规"
            think: "不涉及任何列出的隐私字段"
            code: |
              PS_WriteLog(LOG_INFO, "Process done, cnt=%u", g_counter);
        neg_examples:
          - case_name: 打印用户IPv4地址
            description: 日志直接打印client_ip
            expected_answer: "违规：日志记录用户IP"
            think: "用户IPv4属于被禁止的隐私信息"
            code: |
              PS_WriteLog(LOG_INFO, "Client IP=%s", client_ip);

      - content: 禁止明文打印OM类、业务类的密钥的密钥,如果涉及到密文打印的需求联系安全SE确认实现方案。
        pos_examples:
          - case_name: 不打印密钥
            description: 日志中仅打印密钥ID
            expected_answer: "没有违规"
            think: "未暴露密钥明文"
            code: |
              PS_WriteLog(LOG_INFO, "Use key id=%u", key_id);
        neg_examples:
          - case_name: 明文打印密钥
            description: 日志直接输出密钥内容
            expected_answer: "违规：明文打印密钥"
            think: "密钥以明文形式泄露"
            code: |
              PS_WriteLog(LOG_INFO, "Key=%s", key_hex_str);

    # 9.2 命令资料
    - id: PRIV-01-CMD
      index_num: 2
      name: 命令资料
      principles:
      - content: 新增或修改命令,包括工程命令,未补充相应资料。
        pos_examples:
          - case_name: 新增命令同步补充文档
            description: 新增show_stats命令并更新CLI手册
            expected_answer: "没有违规"
            think: "资料已同步"
            code: |
              /* 新增命令已在CLI手册和工程文档中登记 */
        neg_examples:
          - case_name: 新增命令无文档
            description: 新增diag_reset命令未更新任何资料
            expected_answer: "违规：未补充命令资料"
            think: "缺少文档导致运维/测试无法识别"  
  # 10. 编译宏
  - id: MACRO-01
    index_num: 10
    name: 编译宏
    rules:
    - id: MACRO-01-DEBUG
      index_num: 1
      name: __DEBUG__宏
      principles:
      - content: 禁止业务代码使用__DEBUG__宏隔离实现,会造成自验证二进制与发布二进制产生差异,可能影响验证准确性,建议通过alpha测试打桩。
        pos_examples:
          - case_name: 使用运行时开关
            description: 通过配置项而非__DEBUG__宏控制调试行为
            expected_answer: "没有违规"
            think: "发布与调试二进制行为一致"
            code: |
              if (g_dbgCfg.enable_trace) {
                  TracePacket(pkt);
              }
        neg_examples:
          - case_name: 使用__DEBUG__隔离代码
            description: 用__DEBUG__宏包裹调试逻辑
            expected_answer: "违规：使用__DEBUG__宏隔离实现"
            think: "发布版与调试版行为不同"
            code: |
              #ifdef __DEBUG__
              DumpPacket(pkt);
              #endif
  # 11. 运算
  - id: CALC-01
    index_num: 11
    name: 运算
    rules:

    # 11.1 运算溢出
    - id: CALC-01-OVERFLOW
      index_num: 1
      name: 运算溢出
      principles:
      - content: 两个数据相加或相乘时一定要考虑是否会溢出,造成数值翻转等错误。
        pos_examples:
          - case_name: 先检查再相加
            description: 使用UINT32_MAX提前判断
            expected_answer: "没有违规"
            think: "提前防止加法溢出"
            code: |
              if (a > UINT32_MAX - b) {
                  return ERR_OVERFLOW;
              }
              UINT32 c = a + b;
        neg_examples:
          - case_name: 直接相加
            description: 未检查溢出
            expected_answer: "违规：可能数值翻转"
            code: |
              UINT32 c = a + b;

      - content: 两个数据相减时一定要先进行大小判断,避免减翻。
        pos_examples:
          - case_name: 先判断再相减
            description: 保证被减数≥减数
            expected_answer: "没有违规"
            code: |
              if (a < b) return ERR_UNDERFLOW;
              UINT32 diff = a - b;
        neg_examples:
          - case_name: 直接相减
            description: 未判断大小
            expected_answer: "违规：可能减翻"
            code: |
              UINT32 diff = a - b;

      - content: 时间戳相减需要注意是否会减翻,尤其是两个时间戳来自两个不同的线程的场景,由于线程切换,可能随时会翻转。
        pos_examples:
          - case_name: 时间戳防翻处理
            description: 使用无符号差值判断
            expected_answer: "没有违规"
            code: |
              UINT32 diff = (UINT32)(t2 - t1); /* t2≥t1 或 翻转后仍正确 */
        neg_examples:
          - case_name: 直接相减
            description: 未考虑翻转
            expected_answer: "违规：可能减翻"
            code: |
              INT32 diff = t2 - t1;

    # 11.2 运算异常
    - id: CALC-01-DIV
      index_num: 2
      name: 运算异常
      principles:
      - content: 两个数据做除法或者取模运算时一定要考虑除数是否为0。
        pos_examples:
          - case_name: 先判断除数
            description: 除法前检查非零
            expected_answer: "没有违规"
            code: |
              if (div == 0) return ERR_DIV_ZERO;
              result = val / div;
        neg_examples:
          - case_name: 直接除法
            description: 未检查除数
            expected_answer: "违规：除数可能为0"
            code: |
              result = val / div;

    # 11.3 移位操作
    - id: CALC-01-SHIFT
      index_num: 3
      name: 移位操作
      principles:
      - content: 对数据进行左移或右移操作时,保证移的位数不超过数据本身bit数；例如：UCHAR数据类型时移位不要超过8bit,USHORT数据类型时移位不要超过16bit,ULONG数据类型时移位不要超过32bit。
        pos_examples:
          - case_name: 检查移位位数
            description: 移位前判断位数
            expected_answer: "没有违规"
            code: |
              if (shift >= 32) return ERR_SHIFT;
              ULONG res = val << shift;
        neg_examples:
          - case_name: 直接移位
            description: 未检查移位范围
            expected_answer: "违规：移位位数可能超限"
            code: |
              UCHAR res = (UCHAR)(val << shift); /* shift可能≥8 */
  # 12. 调测机制
  - id: DBG-01
    index_num: 12
    name: 调测机制
    rules:
    # 12.1 日志输出
    - id: DBG-01-LOGIMPL
      index_num: 1
      name: 日志输出
      principles:
      - content: 禁止私自实现日志功能,私有实现容易遗漏日志管理功能,导致日志过多而磁盘满上报告警并影响业务。
        pos_examples:
          - case_name: 使用平台统一日志接口
            description: 调用PS_WriteLog
            expected_answer: "没有违规"
            code: |
              PS_WriteLog(LOG_INFO, "module init ok");
        neg_examples:
          - case_name: 私有printf日志
            description: 自行用printf写日志
            expected_answer: "违规：私自实现日志"
            code: |
              printf("[MYLOG] module init ok\n");

    # 12.2 日志流控
    - id: DBG-01-LOGFLOW
      index_num: 2
      name: 日志流控
      principles:
      - content: 正常业务流程不允许默认打印日志
        pos_examples:
          - case_name: 正常流程无默认日志
            description: 关键路径默认静默
            expected_answer: "没有违规"
            code: |
              /* 无日志 */
        neg_examples:
          - case_name: 正常流程默认打印
            description: 每个报文默认打印
            expected_answer: "违规：正常流程打印日志"
            code: |
              PS_WriteLog(LOG_INFO, "recv pkt");

      - content: 外部输入可大量构造的异常流程,日志必须加流控。
        pos_examples:
          - case_name: 异常日志加计数限流
            description: 每100次打印一次
            expected_answer: "没有违规"
            code: |
              static UINT32 err_cnt = 0;
              if (++err_cnt % 100 == 0) {
                  PS_WriteLog(LOG_ERR, "invalid pkt (%u total)", err_cnt);
              }
        neg_examples:
          - case_name: 异常无流控
            description: 每个异常都打印
            expected_answer: "违规：无流控"
            code: |
              PS_WriteLog(LOG_ERR, "invalid pkt");

      - content: 定时器回调函数里不允许打印日志,防止大量回调触发海量日志。
        pos_examples:
          - case_name: 定时器回调无日志
            description: 回调中静默处理
            expected_answer: "没有违规"
            code: |
              VOID TimerCbk(UINT32 id) { /* do work */ }
        neg_examples:
          - case_name: 定时器回调打印日志
            description: 高频回调打印
            expected_answer: "违规：定时器回调打印日志"
            code: |
              VOID TimerCbk(UINT32 id) {
                  PS_WriteLog(LOG_INFO, "timer %u", id);
              }

      - content: info等低级别日志虽然默认不打印,但仍然会有加锁等消耗,主要流程中不要使用。
        pos_examples:
          - case_name: 主流程用计数器
            description: 用原子计数代替info日志
            expected_answer: "没有违规"
            code: |
              __sync_fetch_and_add(&g_pkts, 1);
        neg_examples:
          - case_name: 主流程用INFO日志
            description: 大量调用info级别
            expected_answer: "违规：主要流程使用info日志"
            code: |
              for (...) {
                  PS_WriteLog(LOG_INFO, "step %u", i);
              }

    # 12.3 计数
    - id: DBG-01-COUNTER
      index_num: 3
      name: 计数
      principles:
      - content: 计数需要唯一表达含义,避免多个计数复用相同计数,导致错误不明确,无法定界问题。
        pos_examples:
          - case_name: 独立计数器
            description: 每种错误单独计数
            expected_answer: "没有违规"
            code: |
              g_drop_no_buf++;
              g_drop_chksum++;
        neg_examples:
          - case_name: 复用同一计数器
            description: 多种错误共用一个计数
            expected_answer: "违规：计数含义不唯一"
            code: |
              g_drop_total++; /* 无法区分原因 */

    # 12.4 工程命令
    - id: DBG-01-CMD
      index_num: 4
      name: 工程命令
      principles:
      - content: 关键信息的查询命令要增加到信息采集工具中
        pos_examples:
          - case_name: 关键信息已加入采集工具
            description: show_session_stats 已注册到info_collection
            expected_answer: "没有违规"
            code: |
              /* CLI 命令已登记到信息采集脚本 */
        neg_examples:
          - case_name: 关键命令未加入采集
            description: 新命令show_key_stats未登记
            expected_answer: "违规：未加入信息采集工具"
            code: |
              /* show_key_stats 未在采集脚本中 */

      - content: 关键资源要有对应的调试命令可查
        pos_examples:
          - case_name: 资源对应调试命令
            description: 内存池提供debug命令
            expected_answer: "没有违规"
            code: |
              /* debug mem_pool show 已存在 */
        neg_examples:
          - case_name: 无调试命令
            description: 新增bitmap资源无查询命令
            expected_answer: "违规：缺少调试命令"
            code: |
              /* bitmap 资源无法通过 CLI 查看 */

      - content: 工程命令需考虑异常参数输入、内部数据量级、多线程访问等条件下处理,避免出现访问异常或者海量打印导致程序异常。
        pos_examples:
          - case_name: 命令带参数校验
            description: 参数长度和范围检查
            expected_answer: "没有违规"
            code: |
              if (len > MAX_PRINT_LEN) len = MAX_PRINT_LEN;
        neg_examples:
          - case_name: 无参数校验
            description: 用户输入长度直接用于打印
            expected_answer: "违规：未做异常参数处理"
            code: |
              printf("%.*s", len, buf); /* len可能极大 */
  # 13. 结构体
  - id: STRUCT-01
    index_num: 13
    name: 结构体
    rules:
    - id: STRUCT-01-DEF
      index_num: 1
      name: 结构体定义
      principles:
      - content: 禁止使用__attribute__((aligned((bytes))))隐式对齐结构体,会触发某些编译器使用sse指令时误判对齐造成程序异常,另外也会造成自动对齐空间无法使用浪费内存。
        pos_examples:
          - case_name: 默认对齐方式
            description: 不添加任何对齐属性,依赖编译器自动对齐
            expected_answer: "没有违规"
            think: "未使用__attribute__((aligned)),避免对齐误判与内存浪费"
            code: |
              struct PacketHeader {
                  UINT32 seq;
                  UINT16 len;
                  UINT8  flags;
              };
        neg_examples:
          - case_name: 显式指定对齐
            description: 使用__attribute__((aligned))强制64字节对齐
            expected_answer: "违规：使用__attribute__((aligned))"
            think: "显式对齐可能导致SSE误判及内存浪费"
            code: |
              struct DataBlock {
                  UINT32 a;
              } __attribute__((aligned(64)));
  # 14. 变量
  - id: VAR-01
    index_num: 14
    name: 变量
    rules:
    - id: VAR-01-TYPE
      index_num: 1
      name: 变量类型
      principles:
      - content: BOOL类型变量不同编译器实现长度存在差异,会导致不同系统下内存对齐差异,并且不利于对后续字段含义扩展,因此建议使用UINT32类型。
        pos_examples:
          - case_name: 使用UINT32代替BOOL
            description: 标志位定义为UINT32
            expected_answer: "没有违规（遵循建议）"
            think: "跨平台长度一致,便于扩展"
            code: |
              UINT32 isEnabled; /* 0/1 表示真假 */
        neg_examples:
          - case_name: 使用BOOL类型
            description: 直接使用bool或BOOL定义标志
            expected_answer: "违反建议：使用BOOL类型"
            think: "不同编译器BOOL长度可能不同,影响对齐"
            code: |
              BOOL isEnabled; /* 可能1字节或4字节 */
              