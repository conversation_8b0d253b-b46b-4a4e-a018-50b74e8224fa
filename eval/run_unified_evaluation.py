#!/usr/bin/env python3
"""
统一评估系统运行脚本

展示如何使用重构后的统一评估系统，复用gate_keeper现有能力
"""

import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 使用优化的配置和报告服务
from eval.config_manager import evaluation_config
from eval.evaluation_coordinator import EvaluationCoordinator
from eval.optimized_report_service import OptimizedEvaluationReportService
# 复用gate_keeper的核心服务，避免重复实现
from gate_keeper.application.service.service_orchestrator import (
    ServiceConfig, ServiceOrchestrator)
from gate_keeper.config import config as app_config
from gate_keeper.shared.log import app_logger as logger


def _convert_dataset_to_test_cases(dataset, yaml_path):
    """将YAML数据集转换为TestCase列表，复用unified_evaluator的逻辑"""
    from gate_keeper.domain.rule.check_rule import TestCase

    test_cases = []

    for category in dataset.dataset.rule_categories:
        for principle in category.get_all_principles():
            # 处理正面示例
            for pos_example in principle.pos_examples:
                test_case = TestCase(
                    id=f"TC_{category.id}_{hash(principle.content) % 10000}_pos_{len(test_cases)}",
                    name=principle.content,
                    description=pos_example.description,
                    category=[category.name],
                    enabled=True,
                    severity="medium",
                    case_name=pos_example.case_name,
                    expected_answer=pos_example.expected_answer,
                    think=pos_example.think,
                    code=pos_example.code,
                    git={
                        "repo_url": pos_example.git.repo_url,
                        "branch": pos_example.git.branch,
                        "commit": pos_example.git.commit,
                        "mr_id": pos_example.git.mr_id,
                        "file_path": pos_example.git.file_path,
                        "function_name": pos_example.git.function_name
                    } if pos_example.git else None,
                    type="positive"
                )
                # 添加YAML源文件路径
                if yaml_path:
                    test_case.yaml_source = yaml_path
                test_cases.append(test_case)

            # 处理负面示例
            for neg_example in principle.neg_examples:
                test_case = TestCase(
                    id=f"TC_{category.id}_{hash(principle.content) % 10000}_neg_{len(test_cases)}",
                    name=principle.content,
                    description=neg_example.description,
                    category=[category.name],
                    enabled=True,
                    severity="medium",
                    case_name=neg_example.case_name,
                    expected_answer=neg_example.expected_answer,
                    think=neg_example.think,
                    code=neg_example.code,
                    git={
                        "repo_url": neg_example.git.repo_url,
                        "branch": neg_example.git.branch,
                        "commit": neg_example.git.commit,
                        "mr_id": neg_example.git.mr_id,
                        "file_path": neg_example.git.file_path,
                        "function_name": neg_example.git.function_name
                    } if neg_example.git else None,
                    type="negative"
                )
                # 添加YAML源文件路径
                if yaml_path:
                    test_case.yaml_source = yaml_path
                test_cases.append(test_case)

    return test_cases


# 注意：_calculate_metrics 和 _extract_llm_results_from_check_mr_result 函数
# 已经移动到 EvaluationCoordinator 中，避免重复实现


def _validate_config_for_debug_mode():
    """验证调试模式所需的配置"""
    required_configs = {
        'repo_dir': app_config.repo_dir,
        'mr_id': app_config.mr_id,
        'baseBranch': app_config.baseBranch,
        'devBranch': app_config.devBranch
    }

    missing_configs = [key for key, value in required_configs.items() if value == "not_set"]

    if missing_configs:
        logger.error(f"❌ 配置文件中缺少必需参数: {', '.join(missing_configs)}")
        logger.error("请检查配置文件或使用命令行参数")
        return False

    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="统一评估系统")
    parser.add_argument("--dataset", required=True, help="YAML数据集文件路径")
    parser.add_argument("--repo-path", required=True, help="仓库路径")
    parser.add_argument("--mode", choices=["mr", "branch", "dataset"], default="dataset", 
                       help="评估模式: mr/branch/dataset")
    parser.add_argument("--output-dir", default="./evaluation_output", help="输出目录")
    
    # MR模式参数
    parser.add_argument("--mr-id", type=int, help="MR ID (MR模式需要)")
    parser.add_argument("--base-branch", default="master", help="基础分支")
    parser.add_argument("--dev-branch", default="develop", help="开发分支")
    
    # Branch模式参数
    parser.add_argument("--branch", default="master", help="分支名称 (Branch模式)")
    
    # 通用参数
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 验证参数
    if args.mode == "mr" and not args.mr_id:
        parser.error("MR模式需要提供 --mr-id 参数")
    
    if not os.path.exists(args.dataset):
        parser.error(f"数据集文件不存在: {args.dataset}")
    
    if not os.path.exists(args.repo_path):
        parser.error(f"仓库路径不存在: {args.repo_path}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # 使用优化的配置管理器检查配置
        logger.info("🔧 检查配置状态...")
        config_valid = evaluation_config.log_config_status(args.mode)
        if not config_valid:
            logger.error("❌ 配置验证失败，请检查必需的配置项")
            sys.exit(1)

        # 初始化服务编排器
        logger.info("🔧 初始化服务编排器...")
        service_config = ServiceConfig(
            git_token=getattr(app_config, 'git_token', 'not_set'),
            git_platform=getattr(app_config, 'git_platform', 'gitee'),
            llm_endpoint=getattr(app_config, 'llm_endpoint', 'http://localhost:11434'),
            llm_model=getattr(app_config, 'llm_model_id', 'qwen2.5:7b'),
            use_static_analysis=getattr(app_config, 'use_static_analysis', True),
            use_optimized_context=getattr(app_config, 'use_optimized_context', True),
            max_workers=getattr(app_config, 'llm_concurrent', 5),
            max_context_chain_depth=getattr(app_config, 'max_context_chain_depth', 3),
            max_context_chains=getattr(app_config, 'max_context_chains', 3),
            max_context_size=getattr(app_config, 'max_context_size', 8000)
        )
        orchestrator = ServiceOrchestrator(service_config)
        logger.info("✅ 服务编排器初始化完成")
        
        # 使用评估协调器，完全复用gate_keeper的现有用例
        logger.info("🔧 初始化评估协调器...")
        coordinator = EvaluationCoordinator(orchestrator)

        # 加载数据集并转换为TestCase
        logger.info(f"📂 加载数据集: {args.dataset}")
        from eval.datasets.yaml_dataset import YamlDataset
        dataset = YamlDataset.load(args.dataset)
        test_cases = _convert_dataset_to_test_cases(dataset, args.dataset)
        logger.info(f"✅ 数据集加载完成，包含 {len(test_cases)} 个测试用例")

        # 根据模式执行评估，完全复用gate_keeper的现有用例
        logger.info(f"🚀 开始执行评估，模式: {args.mode}")

        if args.mode == "mr":
            metrics, results = coordinator.evaluate_mr_mode(
                test_cases=test_cases,
                repo_path=args.repo_path,
                mr_id=args.mr_id,
                base_branch=args.base_branch,
                dev_branch=args.dev_branch
            )
        elif args.mode == "branch":
            metrics, results = coordinator.evaluate_branch_mode(
                test_cases=test_cases,
                repo_path=args.repo_path,
                branch=args.branch
            )
        elif args.mode == "dataset":
            metrics, results = coordinator.evaluate_dataset_mode(
                test_cases=test_cases,
                repo_path=args.repo_path,
                branch=args.branch
            )
        else:
            raise ValueError(f"不支持的分析模式: {args.mode}")
        
        # 使用优化的报告服务保存结果
        logger.info("💾 保存评估结果...")
        output_config = evaluation_config.get_output_config(args.output_dir)
        report_service = OptimizedEvaluationReportService(output_config)

        # 保存所有结果
        dataset_name = Path(args.dataset).name
        report_service.save_all_results(
            metrics=metrics,
            results=results,
            mode=args.mode,
            dataset_name=dataset_name,
            repo_path=args.repo_path,
            branch=getattr(args, 'branch', None),
            mr_id=getattr(args, 'mr_id', None),
            analysis_target=getattr(args, 'mr_id', None) if args.mode == 'mr' else getattr(args, 'branch', None),
            debug=True
        )

        logger.info(f"✅ 评估完成，结果保存在: {args.output_dir}")
        
    except Exception as e:
        logger.error(f"评估失败: {e}", exc_info=True)
        sys.exit(1)


# 注意：_save_evaluation_results 和 _generate_detailed_report 函数已被
# OptimizedEvaluationReportService 替代，不再需要这些重复的实现


if __name__ == "__main__":
    # 调试模式：如果直接运行且没有参数，使用默认值
    if len(sys.argv) == 1:
        # 选择调试模式 (取消注释对应的模式)

        # 验证配置是否已设置
        if not _validate_config_for_debug_mode():
            sys.exit(1)

        # ===== MR模式调试参数 =====
        # sys.argv.extend([
        #     "--dataset", "eval/datasets/c_evalset_simple.yaml",
        #     "--repo-path", app_config.repo_dir,
        #     "--mode", "mr",
        #     "--mr-id", str(app_config.mr_id),
        #     "--base-branch", app_config.baseBranch,
        #     "--dev-branch", app_config.devBranch,
        #     "--output-dir", "./evaluation_output/mr_mode"
        # ])
        
        # # ===== Branch模式调试参数 =====
        # sys.argv.extend([
        #     "--dataset", "eval/datasets/c_evalset_real.yaml",
        #     "--repo-path", app_config.repo_dir,
        #     "--mode", "branch",
        #     "--branch", app_config.devBranch,
        #     "--output-dir", "./evaluation_output/branch_mode"
        # ])
        
        # ===== Dataset模式调试参数 =====
        sys.argv.extend([
            "--dataset", "eval/datasets/c_evalset_real.yaml",
            "--repo-path", app_config.repo_dir,
            "--mode", "dataset",
            "--branch", app_config.devBranch,
            "--output-dir", "./evaluation_output/dataset_mode"
        ])
        
        print("🔧 调试模式已启用")
        print("💡 提示：要切换调试模式，请取消注释对应的参数组")
    
    main()