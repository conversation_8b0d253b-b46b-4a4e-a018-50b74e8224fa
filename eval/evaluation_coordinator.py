"""
评估协调器

专注于结果收集和指标计算，完全复用gate_keeper的核心检查能力
"""

from pathlib import Path
from typing import List, Tuple

from eval.models import EvaluationMetrics
from gate_keeper.application.usecases.analyze import (AnalyzeBranchUseCase,
                                                      AnalyzeMRUseCase)
from gate_keeper.application.usecases.eval import EvalUseCase
from gate_keeper.domain.rule.check_rule import TestCase
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.shared.log import app_logger as logger


class EvaluationCoordinator:
    """
    评估协调器 - 专注于结果收集和指标计算
    
    核心思想：
    1. 完全复用gate_keeper的现有用例（AnalyzeMRUseCase、AnalyzeBranchUseCase、EvalUseCase）
    2. 不重新实现检查逻辑，只负责结果收集和指标计算
    3. TestCase通过to_check_rule()转换为CheckRule，复用现有的规则处理逻辑
    """
    
    def __init__(self, orchestrator):
        """
        初始化评估协调器
        
        Args:
            orchestrator: 服务编排器
        """
        self.orchestrator = orchestrator
        
        # 获取核心服务
        self.git_service = orchestrator.get_service("git_service")
        self.llm_service = orchestrator.get_service("llm_service")
        self.code_analyzer = orchestrator.get_service("code_analyzer")
        
        logger.info("✅ 评估协调器初始化完成")
    
    def evaluate_mr_mode(self, test_cases: List[TestCase], repo_path: str, 
                        mr_id: int, base_branch: str, dev_branch: str) -> Tuple[EvaluationMetrics, List[AnalyzeLLMResult]]:
        """
        MR模式评估 - 直接复用AnalyzeMRUseCase
        
        Args:
            test_cases: 测试用例列表
            repo_path: 仓库路径
            mr_id: MR ID
            base_branch: 基础分支
            dev_branch: 开发分支
            
        Returns:
            评估指标和分析结果
        """
        logger.info(f"🔍 开始MR模式评估，MR ID: {mr_id}, 测试用例数量: {len(test_cases)}")
        
        # 检查是否有YAML源文件，如果有则直接使用YAML规则管理器
        yaml_path = None
        if test_cases and hasattr(test_cases[0], 'yaml_source') and test_cases[0].yaml_source:
            yaml_path = test_cases[0].yaml_source

        if yaml_path and yaml_path.endswith(('.yaml', '.yml')):
            # 直接使用YAML文件路径创建规则管理器
            from gate_keeper.application.service.rule.rule_service.manager import \
                RuleManager
            yaml_rule_manager = RuleManager(yaml_path)

            # 调试：立即加载规则来验证
            rules = yaml_rule_manager.load_rules()
            logger.info(f"🔧 YAML规则管理器调试:")
            logger.info(f"  文件路径: {yaml_path}")
            logger.info(f"  加载的规则数量: {len(rules)}")

            self.orchestrator.set_custom_rule_manager(yaml_rule_manager)
            logger.info(f"使用YAML文件作为规则源: {yaml_path}")
        else:
            # 回退到转换TestCase的方式
            rules = [tc.to_check_rule() for tc in test_cases]

            # 调试信息：检查转换后的规则
            logger.info(f"📋 转换后的规则信息:")
            for i, rule in enumerate(rules):
                logger.info(f"  规则 {i+1}: ID={rule.id}, Name={rule.name}, Description={rule.description[:50]}...")
                logger.info(f"    Category={rule.category}, Enabled={rule.enabled}")

            self.orchestrator.set_rules(rules)
            logger.info("使用转换后的TestCase作为规则源")
        
        # 直接使用AnalyzeMRUseCase
        analyze_mr_usecase = AnalyzeMRUseCase(
            self.git_service, self.llm_service, self.code_analyzer
        )
        
        check_mr_result = analyze_mr_usecase.execute(
            repo_dir=repo_path,
            mr_id=mr_id,
            base_branch=base_branch,
            dev_branch=dev_branch
        )
        
        # 从CheckMRResult中提取LLM分析结果
        results = self._extract_llm_results_from_check_mr_result(check_mr_result)
        
        # 计算评估指标
        metrics = self._calculate_metrics(test_cases, results)
        
        logger.info(f"✅ MR模式评估完成，获得 {len(results)} 个结果")
        return metrics, results
    
    def evaluate_branch_mode(self, test_cases: List[TestCase], repo_path: str, 
                           branch: str) -> Tuple[EvaluationMetrics, List[AnalyzeLLMResult]]:
        """
        Branch模式评估 - 直接复用AnalyzeBranchUseCase
        
        Args:
            test_cases: 测试用例列表
            repo_path: 仓库路径
            branch: 分支名称
            
        Returns:
            评估指标和分析结果
        """
        logger.info(f"🔍 开始Branch模式评估，分支: {branch}, 测试用例数量: {len(test_cases)}")

        # 检查是否有YAML源文件，如果有则直接使用YAML规则管理器
        yaml_path = None
        if test_cases and hasattr(test_cases[0], 'yaml_source') and test_cases[0].yaml_source:
            yaml_path = test_cases[0].yaml_source

        if yaml_path and yaml_path.endswith(('.yaml', '.yml')):
            # 直接使用YAML文件路径创建规则管理器
            from gate_keeper.application.service.rule.rule_service.manager import \
                RuleManager
            yaml_rule_manager = RuleManager(yaml_path)

            # 调试：立即加载规则来验证
            rules = yaml_rule_manager.load_rules()
            logger.info(f"🔧 YAML规则管理器调试:")
            logger.info(f"  文件路径: {yaml_path}")
            logger.info(f"  加载的规则数量: {len(rules)}")

            self.orchestrator.set_custom_rule_manager(yaml_rule_manager)
            logger.info(f"使用YAML文件作为规则源: {yaml_path}")
        else:
            # 回退到转换TestCase的方式
            rules = [tc.to_check_rule() for tc in test_cases]
            self.orchestrator.set_rules(rules)
            logger.info("使用转换后的TestCase作为规则源")
        
        # 直接使用AnalyzeBranchUseCase
        analyze_branch_usecase = AnalyzeBranchUseCase(
            self.git_service, self.llm_service, self.code_analyzer
        )
        
        check_mr_result = analyze_branch_usecase.execute(
            repo_dir=repo_path,
            branch=branch
        )
        
        # 从CheckMRResult中提取LLM分析结果
        results = self._extract_llm_results_from_check_mr_result(check_mr_result)
        
        # 计算评估指标
        metrics = self._calculate_metrics(test_cases, results)
        
        logger.info(f"✅ Branch模式评估完成，获得 {len(results)} 个结果")
        return metrics, results
    
    def evaluate_dataset_mode(self, test_cases: List[TestCase], repo_path: str, 
                            branch: str = "master") -> Tuple[EvaluationMetrics, List[AnalyzeLLMResult]]:
        """
        评测集自包含评测 - 直接使用EvalUseCase
        
        Args:
            test_cases: 测试用例列表
            repo_path: 仓库路径
            branch: 默认分支名称
            
        Returns:
            评估指标和分析结果
        """
        logger.info(f"🔍 开始评测集自包含评测，测试用例数量: {len(test_cases)}")
        
        # 直接使用EvalUseCase
        eval_usecase = EvalUseCase(
            self.git_service, self.llm_service, self.code_analyzer
        )
        
        results = eval_usecase.execute(
            test_cases=test_cases,
            repo_path=repo_path,
            branch=branch
        )
        
        # 计算评估指标
        metrics = self._calculate_metrics(test_cases, results)
        
        logger.info(f"✅ 评测集自包含评测完成，获得 {len(results)} 个结果")
        return metrics, results
    
    def _extract_llm_results_from_check_mr_result(self, check_mr_result) -> List[AnalyzeLLMResult]:
        """
        从CheckMRResult中提取AnalyzeLLMResult列表
        
        Args:
            check_mr_result: CheckMRResult对象
            
        Returns:
            List[AnalyzeLLMResult]: 提取的LLM分析结果列表
        """
        results = []
        
        for diff in check_mr_result.diffs:
            for affected_function in diff.affected_functions:
                results.extend(affected_function.llm_results)
        
        logger.info(f"从CheckMRResult中提取到 {len(results)} 个LLM分析结果")
        return results
    
    def _calculate_metrics(self, test_cases: List[TestCase], 
                          detection_results: List[AnalyzeLLMResult]) -> EvaluationMetrics:
        """
        计算评估指标 - 专注于指标计算，不涉及检查逻辑
        
        Args:
            test_cases: 测试用例列表
            detection_results: 检测结果列表
            
        Returns:
            评估指标
        """
        logger.info("开始计算评估指标")
        
        total_cases = len(test_cases)
        true_positives = 0
        false_positives = 0
        false_negatives = 0
        true_negatives = 0
        
        # 分析每个检测结果
        for i, result in enumerate(detection_results):
            # 根据索引匹配测试用例
            if i < len(test_cases):
                test_case = test_cases[i]
            else:
                logger.warning(f"检测结果索引 {i} 超出测试用例范围，跳过")
                continue
                
            # 判断是否为正面示例
            is_positive_example = test_case.type == "positive"
            
            # 判断检测结果
            is_detected = not result.is_pass  # 检测到问题
            
            # 计算指标
            if is_positive_example and is_detected:
                false_positives += 1  # 正确代码却被误报为有问题
            elif is_positive_example and not is_detected:
                true_negatives += 1  # 正确代码没报错，正确
            elif not is_positive_example and is_detected:
                true_positives += 1  # 错误代码被正确检测出来
            else:  # not is_positive_example and not is_detected
                false_negatives += 1  # 错误代码却没检测出问题

        
        # 计算衍生指标
        accuracy = (true_positives + true_negatives) / total_cases if total_cases > 0 else 0
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        metrics = EvaluationMetrics(
            total_cases=total_cases,
            true_positives=true_positives,
            false_positives=false_positives,
            false_negatives=false_negatives,
            true_negatives=true_negatives,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1_score
        )
        
        logger.info(f"评估指标计算完成: 准确率={accuracy:.3f}, 精确率={precision:.3f}, 召回率={recall:.3f}, F1={f1_score:.3f}")
        return metrics
        return metrics
        return metrics
        return metrics
        return metrics
        return metrics
        return metrics
