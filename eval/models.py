"""
评估相关的数据模型
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional


@dataclass
class EvaluationMetrics:
    """评估指标"""
    total_cases: int
    true_positives: int
    false_positives: int
    false_negatives: int
    true_negatives: int
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    evaluation_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.evaluation_time is None:
            self.evaluation_time = datetime.now()


@dataclass
class EvaluationResult:
    """评估结果"""
    metrics: EvaluationMetrics
    report_path: str
    dataset_info: dict
    repository_info: dict 