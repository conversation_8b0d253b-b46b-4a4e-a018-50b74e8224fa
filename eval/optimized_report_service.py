"""
优化的评估报告服务

复用gate_keeper的现有能力，避免重复实现
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

from eval.models import EvaluationMetrics
from gate_keeper.domain.value_objects.analysis_result import Analyze<PERSON><PERSON><PERSON><PERSON>
from gate_keeper.shared.log import app_logger as logger


class OptimizedEvaluationReportService:
    """优化的评估报告服务 - 复用gate_keeper的现有能力"""
    
    def __init__(self, output_config: Dict[str, str]):
        """
        初始化报告服务
        
        Args:
            output_config: 输出配置字典
        """
        self.output_config = output_config
        self.output_dir = Path(output_config['output_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def save_evaluation_metrics(self, metrics: EvaluationMetrics, mode: str):
        """
        保存评估指标
        
        Args:
            metrics: 评估指标
            mode: 评估模式
        """
        metrics_data = {
            "total_cases": metrics.total_cases,
            "true_positives": metrics.true_positives,
            "false_positives": metrics.false_positives,
            "false_negatives": metrics.false_negatives,
            "true_negatives": metrics.true_negatives,
            "accuracy": metrics.accuracy,
            "precision": metrics.precision,
            "recall": metrics.recall,
            "f1_score": metrics.f1_score,
            "evaluation_time": metrics.evaluation_time.isoformat() if metrics.evaluation_time else None,
            "mode": mode
        }
        
        with open(self.output_config['metrics_file'], 'w', encoding='utf-8') as f:
            json.dump(metrics_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"评估指标已保存: {self.output_config['metrics_file']}")
    
    def save_evaluation_results(self, results: List[AnalyzeLLMResult]):
        """
        保存评估结果

        Args:
            results: 评估结果列表
        """
        results_data = []
        for result in results:
            # 安全地处理violations属性
            violations_data = []
            if hasattr(result, 'violations') and result.violations:
                for v in result.violations:
                    if hasattr(v, 'model_dump'):
                        violations_data.append(v.model_dump())
                    elif hasattr(v, 'dict'):
                        violations_data.append(v.dict())
                    else:
                        # 如果是普通字典或其他类型，直接转换
                        violations_data.append({
                            "rule_id": getattr(v, 'rule_id', ''),
                            "rule_content": getattr(v, 'rule_content', ''),
                            "location": getattr(v, 'location', {}),
                            "severity": getattr(v, 'severity', ''),
                            "message": getattr(v, 'message', '')
                        })

            result_dict = {
                "is_pass": result.is_pass,
                "reason": result.reason,
                "violations": violations_data,
                "code": getattr(result, 'code', ''),
                "prompt": getattr(result, 'prompt', ''),
                "response": getattr(result, 'response', ''),
                "call_id": getattr(result, 'call_id', ''),
                "test_case_info": getattr(result, 'test_case_info', {})
            }
            results_data.append(result_dict)

        with open(self.output_config['results_file'], 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)

        logger.info(f"评估结果已保存: {self.output_config['results_file']}")

        # 同时保存LLM详细信息
        self._save_llm_details(results)

    def _save_llm_details(self, results: List[AnalyzeLLMResult]):
        """
        保存LLM调用详细信息

        Args:
            results: 评估结果列表
        """
        # 生成时间戳文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"llm_details_{timestamp}.json"
        llm_details_file = self.output_dir / filename

        # 转换为可序列化的格式
        llm_details = []
        for result in results:
            # 只保存有LLM调用信息的结果
            if (hasattr(result, 'call_id') and result.call_id) or \
               (hasattr(result, 'prompt') and result.prompt) or \
               (hasattr(result, 'response') and result.response):

                detail = {
                    "call_id": getattr(result, 'call_id', ''),
                    "is_pass": result.is_pass,
                    "reason": result.reason,
                    "code": getattr(result, 'code', ''),
                    "prompt": getattr(result, 'prompt', ''),
                    "response": getattr(result, 'response', ''),
                    "violations": []
                }

                # 安全地处理violations
                if hasattr(result, 'violations') and result.violations:
                    for v in result.violations:
                        violation_dict = {
                            "rule_id": getattr(v, 'rule_id', ''),
                            "rule_content": getattr(v, 'rule_content', ''),
                            "location": getattr(v, 'location', {}),
                            "severity": getattr(v, 'severity', ''),
                            "message": getattr(v, 'message', '')
                        }
                        detail["violations"].append(violation_dict)

                llm_details.append(detail)

        # 保存到文件
        with open(llm_details_file, 'w', encoding='utf-8') as f:
            json.dump(llm_details, f, ensure_ascii=False, indent=2)

        logger.info(f"LLM详细信息已保存: {llm_details_file} (包含 {len(llm_details)} 条记录)")
        return str(llm_details_file)

    def generate_summary_report(self, metrics: EvaluationMetrics, mode: str,
                              dataset_name: str, repo_path: str) -> str:
        """
        生成摘要报告
        
        Args:
            metrics: 评估指标
            mode: 评估模式
            dataset_name: 数据集名称
            repo_path: 仓库路径
            
        Returns:
            报告内容
        """
        report_lines = [
            "# 评估摘要报告\n",
            f"**评估模式**: {mode}\n",
            f"**数据集**: {dataset_name}\n",
            f"**仓库路径**: {repo_path}\n",
            f"**评估时间**: {metrics.evaluation_time}\n",
            "## 评估指标\n",
            f"- 总测试用例数: {metrics.total_cases}",
            f"- 准确率: {metrics.accuracy:.3f}",
            f"- 精确率: {metrics.precision:.3f}",
            f"- 召回率: {metrics.recall:.3f}",
            f"- F1分数: {metrics.f1_score:.3f}\n",
            "## 详细统计\n",
            f"- 真阳性 (TP): {metrics.true_positives}",
            f"- 假阳性 (FP): {metrics.false_positives}",
            f"- 假阴性 (FN): {metrics.false_negatives}",
            f"- 真阴性 (TN): {metrics.true_negatives}\n",
            "## 结果文件\n",
            "- 评估指标: `evaluation_metrics.json`",
            "- 详细结果: `evaluation_results.json`",
            "- 详细报告: `detailed_evaluation_report.md`"
        ]
        
        content = "\n".join(report_lines)
        
        with open(self.output_config['summary_file'], 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"摘要报告已保存: {self.output_config['summary_file']}")
        return content
    
    def generate_detailed_report(self, metrics: EvaluationMetrics, results: List[AnalyzeLLMResult],
                               mode: str, dataset_name: str, repo_path: str, **kwargs) -> str:
        """
        生成详细报告

        Args:
            metrics: 评估指标
            results: 评估结果
            mode: 评估模式
            dataset_name: 数据集名称
            repo_path: 仓库路径
            **kwargs: 其他参数

        Returns:
            报告内容
        """
        # 生成详细报告内容
        report_lines = [
            f"# 详细评估报告\n",
            f"**数据集**: {dataset_name}",
            f"**仓库路径**: {repo_path}",
            f"**评估模式**: {mode}",
            f"**评估时间**: {metrics.evaluation_time}\n",

            "## 评估指标\n",
            f"- 总测试用例数: {metrics.total_cases}",
            f"- 准确率: {metrics.accuracy:.3f}",
            f"- 精确率: {metrics.precision:.3f}",
            f"- 召回率: {metrics.recall:.3f}",
            f"- F1分数: {metrics.f1_score:.3f}\n",

            "## 详细统计\n",
            f"- 真阳性 (TP): {metrics.true_positives}",
            f"- 假阳性 (FP): {metrics.false_positives}",
            f"- 假阴性 (FN): {metrics.false_negatives}",
            f"- 真阴性 (TN): {metrics.true_negatives}\n",

            "## 分析结果详情\n"
        ]

        # 添加每个结果的详细信息
        for i, result in enumerate(results):
            report_lines.extend([
                f"### 结果 {i+1}",
                f"- **通过状态**: {'✅ 通过' if result.is_pass else '❌ 未通过'}",
                f"- **原因**: {result.reason}",
                f"- **违规数量**: {len(result.violations) if hasattr(result, 'violations') and result.violations else 0}",
                ""
            ])

            # 添加违规详情
            if hasattr(result, 'violations') and result.violations:
                report_lines.append("**违规详情**:")
                for j, violation in enumerate(result.violations):
                    report_lines.append(f"  {j+1}. {getattr(violation, 'message', '未知违规')}")
                report_lines.append("")

            # 添加LLM原始请求和响应
            self._add_llm_details_to_report(report_lines, result, i+1)

        report_content = "\n".join(report_lines)

        # 保存详细报告
        with open(self.output_config['detailed_report_file'], 'w', encoding='utf-8') as f:
            f.write(report_content)

        # 保存LLM详细信息
        llm_details_file = self._save_llm_details(results)

        logger.info(f"详细报告已保存: {self.output_config['detailed_report_file']}")
        logger.info(f"LLM详细信息已保存: {llm_details_file}")

        return report_content
    
    def log_evaluation_summary(self, metrics: EvaluationMetrics):
        """
        记录评估摘要到日志
        
        Args:
            metrics: 评估指标
        """
        logger.info("📊 评估摘要:")
        logger.info(f"  总测试用例数: {metrics.total_cases}")
        logger.info(f"  准确率: {metrics.accuracy:.3f}")
        logger.info(f"  精确率: {metrics.precision:.3f}")
        logger.info(f"  召回率: {metrics.recall:.3f}")
        logger.info(f"  F1分数: {metrics.f1_score:.3f}")
        logger.info(f"  真阳性: {metrics.true_positives}")
        logger.info(f"  假阳性: {metrics.false_positives}")
        logger.info(f"  假阴性: {metrics.false_negatives}")
        logger.info(f"  真阴性: {metrics.true_negatives}")
    
    def save_all_results(self, metrics: EvaluationMetrics, results: List[AnalyzeLLMResult],
                        mode: str, dataset_name: str, repo_path: str, **kwargs):
        """
        保存所有评估结果
        
        Args:
            metrics: 评估指标
            results: 评估结果
            mode: 评估模式
            dataset_name: 数据集名称
            repo_path: 仓库路径
            **kwargs: 其他参数
        """
        # 保存指标
        self.save_evaluation_metrics(metrics, mode)
        
        # 保存结果
        self.save_evaluation_results(results)
        
        # 生成摘要报告
        self.generate_summary_report(metrics, mode, dataset_name, repo_path)
        
        # 生成详细报告
        self.generate_detailed_report(metrics, results, mode, dataset_name, repo_path, **kwargs)
        
        # 记录摘要到日志
        self.log_evaluation_summary(metrics)

    def _add_llm_details_to_report(self, report_lines: List[str], result: AnalyzeLLMResult, result_index: int):
        """
        将LLM原始请求和响应添加到报告中

        Args:
            report_lines: 报告行列表
            result: 分析结果
            result_index: 结果索引
        """
        # 检查是否有LLM调用信息
        has_llm_info = (
            (hasattr(result, 'call_id') and result.call_id) or
            (hasattr(result, 'prompt') and result.prompt) or
            (hasattr(result, 'response') and result.response)
        )

        if not has_llm_info:
            report_lines.extend([
                "**LLM调用信息**: 无LLM调用记录",
                ""
            ])
            return

        report_lines.append("#### LLM调用详情")

        # 添加调用ID
        if hasattr(result, 'call_id') and result.call_id:
            report_lines.append(f"- **调用ID**: `{result.call_id}`")

        # 添加代码信息
        if hasattr(result, 'code') and result.code:
            report_lines.extend([
                f"- **分析代码**: ",
                "```",
                result.code,
                "```"
            ])

        # 添加原始请求
        if hasattr(result, 'prompt') and result.prompt:
            report_lines.extend([
                "",
                "**原始请求 (Prompt)**:",
                "`````",
                result.prompt,
                "`````"
            ])

        # 添加原始响应
        if hasattr(result, 'response') and result.response:
            # 清理响应中的嵌套代码块标记
            cleaned_response = self._clean_nested_code_blocks(result.response)
            report_lines.extend([
                "",
                "**原始响应 (Response)**:",
                "```",
                cleaned_response,
                "```"
            ])

        report_lines.append("---")

    def _clean_nested_code_blocks(self, text: str) -> str:
        """清理文本中的嵌套代码块标记，避免在Markdown中显示异常"""
        if not text:
            return text

        import re

        # 移除开头和结尾的代码块标记
        text = re.sub(r"^```(?:json|xml|javascript|python)?\s*", "", text, flags=re.IGNORECASE | re.MULTILINE)
        text = re.sub(r"\s*```$", "", text, flags=re.IGNORECASE | re.MULTILINE)

        # 移除中间的代码块标记（但保留内容）
        text = re.sub(r"```(?:json|xml|javascript|python)?\s*", "", text, flags=re.IGNORECASE)

        return text.strip()

        logger.info(f"✅ 所有评估结果已保存到: {self.output_dir}")
