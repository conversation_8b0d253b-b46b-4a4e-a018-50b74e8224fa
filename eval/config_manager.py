"""
评估模块配置管理器

复用gate_keeper的配置管理能力，避免重复实现
"""

from pathlib import Path
from typing import Dict, Any, Optional

from gate_keeper.config import config
from gate_keeper.shared.log import app_logger as logger


class EvaluationConfigManager:
    """评估配置管理器 - 复用gate_keeper的配置能力"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.base_config = config
        self._evaluation_defaults = {
            'default_output_dir': './evaluation_output',
            'default_mode': 'dataset',
            'default_branch': 'master',
            'max_test_cases': 1000,
            'enable_detailed_logging': True,
            'report_format': 'markdown'
        }
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，优先从gate_keeper配置获取
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        # 首先尝试从gate_keeper配置获取
        if hasattr(self.base_config, key):
            value = getattr(self.base_config, key)
            if value != "not_set":
                return value
        
        # 然后从评估默认配置获取
        if key in self._evaluation_defaults:
            return self._evaluation_defaults[key]
        
        # 最后返回默认值
        return default
    
    def validate_required_configs(self, mode: str) -> Dict[str, bool]:
        """
        验证必需的配置项
        
        Args:
            mode: 评估模式
            
        Returns:
            验证结果字典
        """
        validation_results = {}
        
        # 通用必需配置
        common_required = ['repo_dir', 'log_dir', 'llm_model_id']
        
        # 模式特定必需配置
        mode_specific = {
            'mr': ['mr_id', 'baseBranch', 'devBranch'],
            'branch': ['devBranch'],
            'dataset': []
        }
        
        required_configs = common_required + mode_specific.get(mode, [])
        
        for config_key in required_configs:
            value = self.get_config_value(config_key)
            is_valid = value is not None and value != "not_set"
            validation_results[config_key] = is_valid
            
            if not is_valid:
                logger.warning(f"配置项 {config_key} 未设置或无效")
        
        return validation_results
    
    def get_debug_config(self, mode: str) -> Dict[str, Any]:
        """
        获取调试模式配置
        
        Args:
            mode: 评估模式
            
        Returns:
            调试配置字典
        """
        debug_config = {
            'dataset': 'eval/datasets/c_evalset_simple.yaml',
            'repo_path': self.get_config_value('repo_dir'),
            'mode': mode,
            'output_dir': f'./evaluation_output/{mode}_mode'
        }
        
        if mode == 'mr':
            debug_config.update({
                'mr_id': self.get_config_value('mr_id'),
                'base_branch': self.get_config_value('baseBranch'),
                'dev_branch': self.get_config_value('devBranch')
            })
        elif mode in ['branch', 'dataset']:
            debug_config.update({
                'branch': self.get_config_value('devBranch')
            })
        
        return debug_config
    
    def create_service_config(self) -> Dict[str, Any]:
        """
        创建服务配置，复用gate_keeper的配置
        
        Returns:
            服务配置字典
        """
        return {
            'git_token': self.get_config_value('token'),
            'git_platform': self.get_config_value('git_platform', 'gitee'),
            'llm_model': self.get_config_value('llm_model_id'),
            'llm_concurrent': self.get_config_value('llm_concurrent', 1),
            'max_context_size': self.get_config_value('context_max_context_size', 8000),
            'max_context_chain_depth': self.get_config_value('max_context_chain_depth', 3),
            'max_context_chains': self.get_config_value('max_context_chains', 3),
            'use_static_analysis': True,
            'use_optimized_context': True,
            'max_workers': 5
        }
    
    def get_output_config(self, output_dir: str) -> Dict[str, Any]:
        """
        获取输出配置
        
        Args:
            output_dir: 输出目录
            
        Returns:
            输出配置字典
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        return {
            'output_dir': str(output_path),
            'metrics_file': str(output_path / 'evaluation_metrics.json'),
            'results_file': str(output_path / 'evaluation_results.json'),
            'summary_file': str(output_path / 'evaluation_summary.md'),
            'detailed_report_file': str(output_path / 'detailed_evaluation_report.md'),
            'llm_details_file': str(output_path / 'llm_details.json')
        }
    
    def log_config_status(self, mode: str):
        """
        记录配置状态
        
        Args:
            mode: 评估模式
        """
        logger.info("📋 配置状态检查:")
        validation_results = self.validate_required_configs(mode)
        
        for config_key, is_valid in validation_results.items():
            status = "✅" if is_valid else "❌"
            value = self.get_config_value(config_key)
            logger.info(f"  {status} {config_key}: {value}")
        
        # 检查是否所有必需配置都有效
        all_valid = all(validation_results.values())
        if all_valid:
            logger.info("✅ 所有必需配置项都已正确设置")
        else:
            invalid_configs = [k for k, v in validation_results.items() if not v]
            logger.warning(f"❌ 以下配置项需要设置: {', '.join(invalid_configs)}")
        
        return all_valid


# 全局配置管理器实例
evaluation_config = EvaluationConfigManager()
