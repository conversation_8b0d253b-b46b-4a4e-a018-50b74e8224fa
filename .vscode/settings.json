{"python.pythonPath": "~/.miniconda3/envs/git_keeper/bin/python", "python.envFile": "${workspaceFolder}/.env", "plantuml.java": "/opt/homebrew/opt/openjdk/bin/java", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests"], "python-envs.defaultEnvManager": "ms-python.python:conda", "python-envs.defaultPackageManager": "ms-python.python:conda", "python-envs.pythonProjects": []}