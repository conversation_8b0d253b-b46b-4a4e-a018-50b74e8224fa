# Python 编码规范与质量检查项

---

## 命名规范

### 变量命名

【规则】变量命名应使用小写字母与下划线（snake\_case），避免使用缩写与无意义命名。
【案例 - 正确】

```python
user_name = "Alice"
max_count = 10
```

【案例 - 错误】

```python
UserName = "Alice"   # 首字母大写不规范
cnt = 10             # 缩写不可读
```

【规则】常量名应使用全大写字母，单词间用下划线分隔。
【案例】

```python
MAX_RETRIES = 3
DEFAULT_TIMEOUT = 10
```

---

### 函数与类命名

【规则】函数名应使用小写加下划线（snake\_case），表达函数行为或功能。
【规则】类名应使用首字母大写的驼峰式（PascalCase）。
【规则】私有函数或变量应以单下划线 `_` 开头，避免被外部访问。
【规则】特殊方法应遵循 `__method__` 的命名格式，不应自定义伪魔术方法。
【案例】

```python
def calculate_area(): ...
class DataProcessor: ...
```

---

## 代码结构

### 缩进与空格

【规则】每层缩进应使用 4 个空格，不允许使用制表符（Tab）。
【规则】运算符前后必须保留空格，函数定义与调用的参数之间不要多余空格。
【案例 - 正确】

```python
if x == 1:
    print("ok")
```

【案例 - 错误】

```python
if(x==1):print("ok")
```

---

### 空行与文件结构

【规则】函数与类之间应使用两个空行分隔，函数内部逻辑段落之间使用一个空行。
【规则】模块文件应以模块导入、常量定义、类/函数定义、执行逻辑顺序组织。

---

## 编码风格

### 可读性

【规则】每行代码不应超过 79 个字符，长表达式应使用换行符和括号分行。
【规则】不允许在单行中编写多个语句。
【案例 - 错误】

```python
x = 1; y = 2
```

---

### 注释规范

【规则】注释应简洁准确，描述意图而非代码本身；代码修改后必须更新注释。
【规则】块注释前应有一行空行，行注释至少前置两个空格并以 `#` 开头。
【案例 - 正确】

```python
# 计算圆面积
def area(radius):
    return 3.14 * radius ** 2
```

---

## 编程实践

### 函数设计

【规则】函数应聚焦单一职责，函数长度建议控制在 20 行以内。
【规则】函数参数不宜过多，超过 4 个应封装为结构或使用关键字参数。
【规则】避免使用全局变量，必要时应加 `_global` 后缀并说明用途。

---

### 异常处理

【规则】捕获异常应使用具体异常类型，避免使用裸 `except:`。
【规则】在 `try...except` 块中应记录日志或明确处理逻辑，禁止无意义的 `pass`。
【案例 - 错误】

```python
try:
    do_something()
except:
    pass  # 禁止
```

---

### 可迭代与推导式

【规则】推荐使用列表推导式或生成器表达式替代冗长的 for 循环，但需确保可读性。
【规则】不要将多层嵌套的推导式写在一行，避免影响阅读。

---

## 第三方依赖

【规则】导入语句应分为三组：标准库、第三方库、自定义模块，各组之间空一行。
【规则】禁止使用通配符导入（`from module import *`），应显式列出所需名称。

---

## 类型与文档

### 类型注解

【规则】所有公开函数和方法必须提供类型注解，包含参数类型与返回类型。
【案例】

```python
def add(x: int, y: int) -> int:
    return x + y
```

---

### 文档字符串

【规则】所有模块、类、函数应添加 docstring，格式应统一（建议使用 Google 或 reST 风格）。
【规则】类和函数的 docstring 应说明其作用、参数、返回值和异常信息。

---

# 代码质量检查项（按特性分类）

---

## 可维护

### E.1

【规则】数据与业务分离，灵活运用设计模式，满足 SOLID 原则，高内聚低耦合，易于扩展。

### G.4

【规则】通过抽象建模合理设计类/模块，隔离变化，职责单一，满足开闭原则。

### G.5

【规则】基于接口设计编码，向稳定方向依赖，尽量减少依赖，无循环依赖。

### P.4

【规则】Commit 信息准确细致，小步提交，提交内容原子化。

### G.2

【规则】合理使用编程语言特性/API，采用最佳编码实践。

---

## 可测试

### E.2

【规则】输出高质量的开发者测试用例，满足 FIRST、CORRECT 等测试用例设计开发原则。

### G.7

【规则】系统可控制，无明显共享耦合，简化输入，减少系统状态和全局变量，并提供隔离和配置手段。

### G.8

【规则】基于 TDD 方式开发，保证需求完整落地，开发者测试防护完备。

### P.5

【规则】新增代码对应的开发者测试用例代码完备、规范。

### P.6

【规则】合理记录日志，内容简洁清晰，便于问题快速定位。

---

## 可读可维护

### E.3

【规则】主动进行重构，准确识别坏味道并采取恰当手法，显著提升代码的可读性与可维护性。

### G.1

【规则】代码简洁，结构清晰，功能单一且内聚。

### G.3

【规则】合理进行代码微重构，提高可读性和可维护性，遵循标准步骤。

### P.2

【规则】代码较为简洁，层次结构较为清晰，功能基本单一。

### P.3

【规则】命名规范、见名知意，注释合理，尽量做到代码自注释。

---

## 高效

### E.4

【规则】针对性能热点代码，采用高效算法，合理使用存储结构和 IQ，显著提升系统性能。

### G.9

【规则】高效编写循环语句、条件语句、计算操作、函数等。

### G.10

【规则】合理使用并发处理，最小化临界区，提高系统性能。

### P.7

【规则】正确使用资源，资源申请与使用匹配，无不必要的内存间拷贝。

---

## 总结分析

### E.5

【规则】积极学习总结新型/难点技术知识，在团队内持续分享。

---

## 综合能力（ALL）

### E.6

【规则】在 Clean Code 七个特征方面有明显的优秀实践。

### G.2

【规则】合理使用编程语言特性/API，采用最佳编码实践。

---

## 功能

### P.1

【规则】代码业务逻辑正确，新开发/修改方案合理，前向兼容，无基本功能问题。

### F.1

【规则】代码业务逻辑实现不合理，存在严重功能问题。

---

## 可靠性

### P.8

【规则】操作防错，提供合理的风险告知、操作校验、报错提示及对应解决方法。

### P.9

【规则】合理分层处理异常，通过重试、回滚、告警等方式保证系统可靠，并记录日志。

### F.6

【规则】资源泄露，产生大量碎片资源，系统资源异常消耗或性能显著下降。

---

## 安全

### F.3

【规则】暴露不必要的攻击面，与外部交互冗余，泄露内部数据，引入安全漏洞。

### F.4

【规则】缺少输入校验与转义，处理不当导致非法输入引发安全问题。

### F.5

【规则】未进行防御式编程，未遵守安全编码规范，带来安全漏洞。

---

## 可移植

### G.6

【规则】合理抽象硬件功能，适应不同操作系统、编译器平台。

### F.8

【规则】与硬件、操作系统、编译器强依赖，未处理字节序、直接使用设备宏等，降低可移植性。

---

## 性能问题

### F.7

【规则】热点代码实现效率低，并发处理不当导致性能下降。

---

## 可读性问题

### F.2

【规则】代码结构混乱，冗余明显，功能不单一，影响可读性和维护性。

