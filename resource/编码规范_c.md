## 1. 内存操作
### 1.1 内存拷贝
【规则】内存拷贝时访问目的缓冲区、源内存是否会发生越界，注意边界值。

### 1.2 内存搬移
【规则】不要使用memcpy_s函数操作有地址重叠的内存，行为结果未知，推荐使用memove_s。

## 2. 字符串操作
### 2.1 注意数据格式
【规则】不要将二进制码流当做字符串操作，可能不存在'\0'结束符，造成访问越界

### 2.2 正确使用字符串操作函数
【规则】使用memcpy_s接口操作字符串时要考虑'\0'携带结束符  
【推荐】使用strcpy_s、strcat_s等字符串操作接口处理字符串，接口内部已经考虑了结束符'\0'处理

## 3. 数组操作
### 3.1 下标访问
【规则】数组下标访问前做有效性校验，确保访问不越界。

### 3.2 数组定义
【规则】避免使用二级以及以上指针结构操作多维数组，容易造成代码晦涩难懂，操作出错，建议通过结构体分级定义或者每一级单独使用变量表达。  
【案例】如下代码申请全部线程内存头指针数组，使用全局变量保存内存起始地址。然后再按照线程实例申请线程级实际内存，写入线程级头地址时操作错误。

修改代码参考：

```c
// 方法1，单独定义偏移后的指针操作
g_apSMTempVal[0] = (VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UINT8 *), "g_saDecryptBlockPtr");
UINT8 *curInst = (UINT8 *)((UINT8 *)g_apSMTempVal[0] + sizeof(UINT8 *) * inst);
*curInst = (UINT8 *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UINT8), "g_saDecryptBlockmem");

// 方法2，使用指向数组指针操作，理解也不太直观，不推荐
g_apSMTempVal[0] = (VOID *)SA_MallocFix(MAX_THREAD_NUM_IN_NODE, sizeof(UINT8 *), "g_saDecryptBlockPtr");
UINT8* (*instArray)[MAX_THREAD_NUM_IN_NODE] = (UINT8* (*)[MAX_THREAD_NUM_IN_NODE])(g_apSMTempVal[0]);
(*instArray)[inst] = (UINT8 *)SA_MallocFix(SA_QUIC_DECRYPT_BLOCK_BUFF_SIZE, sizeof(UINT8), "g_saDecryptBlockmem");
```

【规则】禁止在函数中定义占用超过500字节的数组（例如：char array[1000]），该数组会占用栈空间，在多级调用叠加后可能导致栈溢出，超大数组使用动态申请或者全局变量。

### 3.3 数组类型

【规则】C语言不同于高级语言，不同类型的数组指针不能通过强转使用，会造成数据错误或者越界读写

### 3.4 数组长度

【规则】数组作为函数参数时，禁止在函数内使用sizeof参数计算数组占用空间，必须同时将数组长度作为函数参数传入。
【推荐】计算数组元素个数采用如下方式，能有效屏蔽系统差异：
`#define ARRAY_LEN(arr) (sizeof(arr) / sizeof((arr)[0]))`

## 4. 资源使用

### 4.1 内存申请

【规则】不要使用malloc和free等申请系统内存，系统内存预留有限，并且出现内存泄漏时难以定位。
使用带有内存统计接口，定义在mem\_api.h，有临时内存、用户级内存、非用户级内存、动态内存等申请接口，请仔细阅读接口功能描述，根据实际场景选择。
如果你不确定接口是不是带有内存统计的，可以先忽略，可以明确的是以下接口不要使用：

```
PS_MemAlloc
PSP_Malloc
VOS_MemAlloc_F
VOS_MemAllocAlignN
PS_MemAllocAlign16
MEM_ShmAllocByName
MEM_PrivMAllocByName
MEM_ShrMemAlloc
PS_MemFree
PSP_Free
PS_ShrMemFree
```

mem_api.h 接口说明

```
这段代码是关于内存管理的接口定义，主要包括了临时内存、动态内存、静态内存和共享内存的申请和释放接口。下面是对这些接口的总结：

 1. 临时内存接口：
    · UTIL_MallocTmp：申请临时内存，适用于业务流程中的临时使用，流程结束后释放。
    · UTIL_FreeTmp：释放临时内存。
    · UTIL_TempHandle：获取公共机制里难以获得的临时内存handle。

 2. 动态内存接口：
    · UTIL_MallocDyn：申请动态内存，适用于业务中动态申请和释放的内存。
    · UTIL_FreeDyn：释放动态内存，接口内做减统计。
    · UTIL_MallocDynNum：申请带有统计信息的动态内存，必须使用UTIL_FreeDynNum释放。
    · UTIL_FreeDynNum：释放带有统计信息的动态内存。

 3. 静态内存接口：
    · UTIL_MallocFix：申请静态固定大小内存。
    · UTIL_MallocSpec：申请静态用户规格相关内存。
    · UTIL_MallocAlignNFix：申请静态对齐内存。
    · UTIL_MallocAlignNSpec：申请静态用户对齐内存。
    · UTIL_MallocHeadAlignNFix：申请静态带头对齐内存。
    · UTIL_MallocHeadAlignNSpec：申请静态带头用户对齐内存。
    · UTIL_Free：释放静态内存，一般用于异常分支释放处理。

 4. 共享内存接口：
    · UTIL_MallocShrByName：按名称申请共享内存，兼容旧代码，新增代码不要使用。

 5. 其他接口：
    · UTIL_MallocInfoCallbackReg：注册内存申请回调函数。
    · UTIL_FreeStaticMem：释放静态内存。
    · UTIL_MallocAlignNWithInfo：申请对齐内存。
    · UTIL_MallocWithInfo：申请内存，带有信息。
    · UTIL_FreeDynMem：释放动态内存。
    · UTIL_ShrMallocByNameWithInfo：按名称申请共享内存。
    · UTIL_AddFixedMemInfo：添加固定内存信息。
    · UTIL_AddSessionMemInfo：添加用户规格内存信息。
    · UTIL_AddMemInfoByName：按名称添加内存信息。
    · UTIL_ShowMemInfo：显示内存信息。
    · UTIL_GetHandleBySoId：根据SOID获取handle。
    · UTIL_MallocDynNumWithInfo：申请带有统计信息的动态内存。
    · UTIL_FreeDynNumMem：释放带有统计信息的动态内存。
    · UTIL_GetDynNumMemInfo：获取动态内存信息。
    · UTIL_GetCommTmpHandle：获取公共临时内存handle。
    · UTIL_MaxUnitNumMemInfoReg：注册最大单位数内存信息。
    · UTIL_GetMemInfoNum：获取内存信息数量。
    · UTIL_GetMemInfo：获取内存信息。
    · UTIL_GetFixedLitterMemInfo：获取固定小内存信息。

这些接口提供了灵活的内存管理机制，适用于不同的内存使用场景，确保内存的高效管理和资源的可控性。
```

### 4.2 内存释放
【规则】申请临时内存资源注意在函数执行完成后释放，提前退出的分支也要考虑释放。1.申请消息、表项等资源在使用前退出的分支要考虑释放。2.注意：一定要考虑**异常场景**需要进行资源的释放，避免资源泄露。
【案例】
```
// 根据hostid查询name
UINT32 DOMAIN_BackupGetHostInfoById(UINT32 hostId, INT8 *hostName, UINT32 hostLen, UINT32 *hostKey)
{
    OM_PROXY_CDB_CONDITION cond = { 0 };
    cond.udwFieldId = (FID_UGWCOMM_VHOST_HOSTID - 1);
    cond.ucOper = M_CFG_CDB_EQUAL;
    *(UINT32 *)cond.aValue = hostId;

    VOID *tmpmem = UTIL_MallocTmp(0, sizeof(OM_PROXY_CDB_CONDITION));
    if (tmpmem == NULL) {
        PS_WriteLog(E_PS_LOG_ERR, "[DOMAIN]BackupGetHostNameById query hostInfo by Id %u failed %u", hostId, ret);
        return VOS_ERR;
    }
    CLASS_UGWCOMM_VHOST_S hostInfo = { 0 };
    (VOID)memset_s(tmpmem, sizeof(OM_PROXY_CDB_CONDITION), 0, sizeof(OM_PROXY_CDB_CONDITION));
    UINT32 ret = OMPROXY_CdbQuerySingle(SM_TBL_ID_VHOST, 1, &cond, (INT8 *)&hostInfo);
    if (ret != VOS_OK) {
        DOMAIN_CNTR_ADD(0, DOMAIN_BACKUP_QUERY_HOST_BYID_FAIL);
        UTIL_FreeTmp(0, tmpmem);
        PS_WriteLog(E_PS_LOG_ERR, "[DOMAIN]BackupGetHostNameById query hostInfo by Id %u failed %u", hostId, ret);
        return VOS_ERR;
    }

    errno_t strRet = strcpy_s((VOID *)hostName, hostLen, hostInfo.acHostName);
    if (strRet != EOK) {
        DOMAIN_CNTR_ADD(0, DOMAIN_BACKUP_STRCPYS_HOSTNAME_ERR);
        PS_WriteLog(E_PS_LOG_ERR, "[DOMAIN]BackupGetHostNameById strcpy_s error %d", strRet);
        return VOS_ERR;
    }
    *hostKey = hostInfo.uiHostKey;
    DOMAIN_CNTR_ADD(0, DOMAIN_BACKUP_QUERY_HOST_BYID_SUCC);
    return VOS_OK;
}
```
代码中语句`tmpmem = UTIL_MallocTmp(0, sizeof(OM_PROXY_CDB_CONDITION))` 动态分配了内存，在路径 `if (ret != VOS_OK)`中使用了`UTIL_FreeTmp`进行了释放，但是在`ret == VOS_OK`路径没有调用释放接口，造成内存泄露


### 4.3 游标释放
【规则】游标是指在遍历配置DB、DDF、CDDB等记录时平台接口内会自动申请一个资源记录遍历过程的信息，如果使用next接口依次遍历到最后返回失败，平台会自动释放游标。如果遍历中间退出时需要业务释放游标，否则就会发生游标泄露。游标总数有限，如果全部泄露光后调用查询接口将失败，影响业务正常运行。目前需要考虑释放游标的接口：
(1) 配置DB：

DBApiQueryFirst_Adapt
DBApiQueryNext_Adapt（遍历接口）
DBApiQueryEnd_Adapt（释放接口）

OMPROXY_CdbQueryFirst
OMPROXY_CdbQueryNext（遍历接口）
OMPROXY_CdbQueryEnd（释放接口）
​
(2) DDF：

UPDBADPT_QueryFirst
UPDBADPT_QueryNext（遍历接口）
UPDBADPT_QueryEnd（释放接口）
​
(3) lua：

spt_Query（查询接口）
spt_EndQuery（释放接口）
​
【规则】lua中只要spt_Query查询成功无论spt_GetNextRecord接口是否失败都要使用spt_EndQuery进行释放。
【案例】<placeholder>

### 4.4 隐式内存释放
【规则】strdup、vasprint接口内有申请内存操作，使用后要进行释放
【规则】使用默认参数调用pthread_create，线程退出时没有调用pthread_join造成栈内存泄露
【规则】cJSON_Parse等接口中会创建cjson对象，调用这个接口以及其衍生的接口（CFG_ParseJsonFile等）解析json格式后需要调用cJSON_Delete释放cjson对象。
```
cJSON_CreateObject
cJSON_CreateArray
cJSON_CreateString
cJSON_CreateNumber
cJSON_CreateBool
cJSON_CreateNull
cJSON_Parse
# 以上接口创建的对象使用后需要调用cJSON_Delete进行释放
```


## 5. 函数实现

### 5.1 函数定义

【规则】定义递归函数，容易造成死循环或栈溢出
...

### 5.2 回调函数

【规则】注册给平台的回调函数...
【案例】<placeholder>

### 5.3 函数参数

【规则】禁止函数中修改指针参数的值...
【案例】<placeholder>
【规则】调用函数时严格区分使用指针还是指针地址...
【案例】<placeholder>

### 5.4 函数功能

【规则】实现IPv4处理时要同步考虑IPv6实现。

## 6. 通信

### 6.1 数据格式

【规则】跨系统通信需要考虑字节序转换

### 6.2 合法性校验

【规则】进程间通信时...
【规则】对于可选信元，其对应数据可能无效...

## 7. 锁

### 7.1 锁类型

【规则】转发面禁止使用mutex类型锁

### 7.2 共享范围

【规则】...trylock...
【规则】...

## 8. 安全函数

【说明】参考链接：<placeholder>
【说明】Huawei Secure C V100R001C01SPC017文档：<placeholder>
Huawei Secure C V100R001C01SPC017 说明文档.CHM <placeholder>

### 8.1 目的缓冲区

【规则】目的缓冲区为字节数...
【规则】...'\0'后的缓冲区长度
...

### 8.2 返回值

【规则】安全函数一定要判断返回值
【规则】sprintf\_s 操作成功返回字符数...

### 8.3 函数封装

【规则】禁止对安全函数进行自定义封装

## 9. 安全隐私

### 9.1 隐私保护

【规则】禁止在日志中记录用户隐私信息
【规则】禁止明文打印密钥
...

### 9.2 命令资料

【规则】新增或修改命令，需补充资料

## 10. 编译宏

### 10.1 \_\_DEBUG\_\_宏

【规则】禁止业务代码使用\_\_DEBUG\_\_宏隔离实现...

## 11. 运算

### 11.1 运算溢出

【规则】两个数据相加或相乘时...
【规则】两个数据相减时...
【规则】时间戳相减时...
【案例】<placeholder>

### 11.2 运算异常

【规则】除法或取模时需判断除数是否为0

### 11.3 移位操作

【规则】左移/右移操作不超过bit数...

## 12. 调测机制

### 12.1 日志输出

【规则】禁止私自实现日志功能
【案例】<placeholder>

### 12.2 日志流控

【规则】业务流程禁止默认打印日志
【规则】异常流程日志需流控
【规则】定时器中禁止打印
【规则】主流程禁止info日志

### 12.3 计数

【规则】计数需表达唯一含义

### 12.4 工程命令

【规则】关键查询命令应加入采集工具
【案例】<placeholder>
【规则】关键资源应有调试命令
【案例】<placeholder>
【规则】工程命令应处理异常参数、多线程等
...

## 13. 结构体

### 13.1 结构体定义

【规则】禁止使用\_\_attribute\_\_((aligned(...)))隐式对齐

## 14. 变量

### 14.1 变量类型

【建议】BOOL变量跨平台对齐差异大，建议使用UINT32

```