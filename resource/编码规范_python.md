## 命名规范

### 1.1 变量命名

【规则】变量命名应使用小写字母与下划线（snake\_case），避免使用缩写与无意义命名。
【规则】常量名应使用全大写字母，单词间用下划线分隔。

### 1.2 函数与类命名

【规则】函数名使用 snake\_case，类名使用 PascalCase。
【规则】私有成员以单下划线 `_` 开头，特殊方法使用 `__method__` 格式，不得伪造。

## 代码结构

### 2.1 缩进与空格

【规则】缩进使用 4 个空格；运算符两侧留空格；函数参数间无多余空格。

### 2.2 空行与文件结构

【规则】函数和类之间使用两个空行；模块结构为：导入 → 常量 → 类/函数 → 执行逻辑。

## 编码风格

### 3.1 可读性

【规则】每行不超过 79 字符；不允许一行多个语句。

### 3.2 注释规范

【规则】注释准确简洁，描述意图；块注释前空一行；行注释前两个空格，使用 `#`。

## 编程实践

### 4.1 函数设计

【规则】函数应职责单一，控制在 20 行以内，参数不宜超过 4 个。
【规则】避免全局变量，必要时需加 `_global` 后缀说明。

### 4.2 异常处理

【规则】捕获具体异常类型，禁止裸 `except:`；异常中应记录日志或清晰处理，禁止空 `pass`。

### 4.3 推导式

【规则】推荐使用推导式替代简单 for 循环，保持可读性，避免多层嵌套。

## 第三方依赖

### 5.1 导入规范

【规则】导入顺序为标准库 → 第三方库 → 本地模块；各组之间空一行。
【规则】禁止使用通配符导入。

## 类型与文档

### 6.1 类型注解

【规则】所有公开函数必须有参数与返回值类型注解。

### 6.2 文档字符串

【规则】所有模块、类、函数应编写 docstring，统一风格（建议 Google 或 reST 风格）。

## 代码质量检查项

### 7.1 单一职责（E.1）

【规则】逻辑清晰、职责分明，满足高内聚低耦合。

### 7.2 模块抽象（G.4）

【规则】模块封装合理，职责明确，接口清晰。

### 7.3 接口导向（G.5）

【规则】依赖抽象接口，避免循环依赖。

### 7.4 提交信息规范（P.4）

【规则】Commit 信息准确、小步提交、原子化。

### 7.5 编码实践（G.2）

【规则】遵循 Python 最佳实践，合理使用语言特性。

## 可测试

### 8.1 测试用例质量（E.2）

【规则】测试用例完整，满足 FIRST、CORRECT 原则。

### 8.2 可控性与可隔离性（G.7）

【规则】可测试性强，避免共享状态，支持配置与隔离。

### 8.3 TDD 驱动开发（G.8）

【规则】遵循 TDD 流程，保障测试防护。

### 8.4 测试用例完备性（P.5）

【规则】新增/修改代码需对应测试用例，规范完整。

### 8.5 日志记录规范（P.6）

【规则】日志记录合理、简洁，有助于问题定位。

## 可读可维护

### 9.1 重构意识（E.3）

【规则】主动识别坏味道，持续进行结构重构。

### 9.2 简洁结构（G.1）

【规则】代码结构清晰简洁，功能单一。

### 9.3 微重构能力（G.3）

【规则】定期进行小范围重构，提升可读性。

### 9.4 层次结构清晰（P.2）

【规则】合理模块划分，层级明确，依赖清晰。

### 9.5 命名与注释（P.3）

【规则】命名见名知意，注释到位，自解释代码优先。

## 高效

### 10.1 高效算法与数据结构（E.4）

【规则】使用高效算法与数据结构，关注性能瓶颈。

### 10.2 高效语句书写（G.9）

【规则】编写高效的循环、判断、表达式。

### 10.3 并发优化（G.10）

【规则】合理使用并发（如多线程、asyncio），避免竞争和瓶颈。

## 总结分析

### 11.1 总结与技术沉淀（E.5）

【规则】总结经验，提升技术深度并在团队中分享。

## 综合能力（ALL）

### 12.1 Clean Code 实践（E.6）

【规则】体现 Clean Code 特征：简单、清晰、表达力强、可维护。

### 12.2 编程规范应用（G.2）

【规则】全面贯彻编码规范，形成良好代码风格。

## 功能

### 13.1 功能正确性（P.1）

【规则】功能实现正确，兼容性好，无逻辑错误。

### 13.2 严重功能错误（F.1）

【规则】实现错误，逻辑漏洞或重大缺陷。

## 可靠性

### 14.1 操作防错（P.8）

【规则】输入校验完整，错误提示清晰，防止误操作。

### 14.2 异常分层处理（P.9）

【规则】异常处理层次清晰，含日志、回滚、告警等机制。