# GateKeeper 文件排除模式配置
# 支持 glob 模式、正则表达式和路径前缀
# 以 # 开头的行会被忽略

# 测试文件
tests/**
*_test.py
test_*.py

# 文档文件
docs/**
*.md
*.rst
*.txt

# 构建和缓存文件
__pycache__/**
*.pyc
*.pyo
*.pyd
.pytest_cache/**
.coverage
htmlcov/**

# 依赖目录
node_modules/**
venv/**
env/**
.venv/**

# 版本控制
.git/**
.svn/**
.hg/**

# IDE 和编辑器
.vscode/**
.idea/**
*.swp
*.swo
*~

# 日志文件
*.log
logs/**

# 临时文件
*.tmp
*.temp
temp/**

# 特定项目文件
archive/**
backup/**
dist/**
build/**

# 配置文件（可选）
# *.ini
# *.cfg
# *.conf 