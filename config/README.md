# Config 目录

本目录包含项目的配置文件，用于各种功能的配置和规则定义。

## 配置文件说明

### `exclude_patterns.txt`
文件过滤器配置文件，定义需要排除的文件和目录模式。

**格式：**
- 每行一个模式
- 支持glob模式：`*.py`, `tests/*`, `docs/**`
- 支持正则表达式：`test.*\.py$`, `.*\.(log|tmp)$`
- 支持路径前缀：`node_modules/`, `.git/`
- 以`#`开头的行会被忽略（注释）

**用法：**
```bash
# 通过命令行参数指定
python gate_keeper/interfaces/cli/main.py --exclude-file config/exclude_patterns.txt

# 通过环境变量指定
export EXCLUDE_PATTERNS="tests/*,*.md,node_modules/**"
```

### `merge_folders_config.txt`
项目合并配置文件，定义合并规则和忽略规则。

**格式：**
- `ignore <pattern>`: 忽略匹配的文件/目录
- `keep_a <path>`: 保留A项目的文件
- `keep_b <path>`: 保留B项目的文件
- `keep_both <path>`: 保留两个项目的文件

**用法：**
```bash
./scripts/merge_folders.sh <A项目路径> <B项目路径> config/merge_folders_config.txt
```

## 配置优先级

1. 命令行参数（最高优先级）
2. 配置文件
3. 环境变量
4. 默认配置（最低优先级）

## 注意事项

1. 配置文件使用UTF-8编码
2. 路径模式支持相对路径和绝对路径
3. 正则表达式模式需要正确转义特殊字符
4. 建议在修改配置前备份原文件 