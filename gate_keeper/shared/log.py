import logging
import os
import sys
from logging.handlers import TimedRotatingFileHandler

from gate_keeper.config import config


class SafeUTF8FileHandler(TimedRotatingFileHandler):
    """支持UTF-8编码且处理特殊字符的日志文件处理器"""
    def emit(self, record):
        try:
            msg = self.format(record)
            msg = msg.encode('utf-8', errors='replace').decode('utf-8')  # 替换非法字符
            self.stream.write(msg + self.terminator)
            self.flush()
        except Exception as e:
            self.handleError(record)

class SafeUTF8ConsoleHandler(logging.StreamHandler):
    """解决控制台输出编码问题的处理器"""
    def __init__(self, stream=sys.stdout):
        super().__init__(stream)
    
    def emit(self, record):
        try:
            msg = self.format(record)
            self.stream.write(msg.encode('utf-8', errors='replace').decode('utf-8') + '\n')
            self.flush()
        except Exception:
            self.handleError(record)

# 创建日志目录（如果不存在）
LOG_DIR = config.log_dir
if LOG_DIR:
    os.makedirs(LOG_DIR, exist_ok=True)

# 日志级别映射
LOG_LEVEL = config.log_level
LEVEL_MAP = {
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
    "DEBUG": logging.DEBUG,
}
level = LEVEL_MAP.get(config.log_level.upper(), logging.INFO)

# 配置日志格式
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def setup_logger(name, log_file, level=level):
    """设置支持UTF-8的安全日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 文件处理器（仅当配置了日志目录时启用）
    if LOG_DIR:
        file_handler = SafeUTF8FileHandler(
            os.path.join(LOG_DIR, log_file),
            when='midnight',
            backupCount=7,
            encoding='utf-8'  # 显式指定编码
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = SafeUTF8ConsoleHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 禁用传播到根logger
    logger.propagate = False
    return logger

# 为不同模块创建专用logger
app_logger = setup_logger('app', 'app.log')
db_logger = setup_logger('database', 'database.log')
api_logger = setup_logger('api', 'api.log')

if __name__ == "__main__":
    # 测试日志输出（包含特殊字符）
    test_msg = "包含特殊字符 → \u200b和中文"
    app_logger.info(test_msg)
    db_logger.debug(f"测试消息: {test_msg}")
    api_logger.error("错误示例：不兼容字符 \u2028")