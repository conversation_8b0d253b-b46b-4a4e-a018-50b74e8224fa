import re


def extract_last_code_block(text: str) -> str:
    # 1. 提取所有闭合代码块
    pattern_closed = re.compile(r'```[a-zA-Z]*\n([\s\S]*?)```')
    matches = pattern_closed.findall(text)

    if matches:
        return matches[-1].strip()

    # 2. 没有闭合，查找最后一个开头
    pattern_open = re.compile(r'```[a-zA-Z]*\n')
    last_open_match = None
    for match in pattern_open.finditer(text):
        last_open_match = match

    if last_open_match:
        code_start = last_open_match.end()
        return text[code_start:].strip()

    return ""



def extract_last_code_block(text: str) -> str:
    """
    提取文本中最后出现的代码块（闭合或未闭合都可以）。

    - 闭合代码块格式：```lang\n...```
    - 未闭合代码块格式：```lang\n...
    """
    # 匹配闭合或未闭合的代码块
    pattern = re.compile(
        r"```[a-zA-Z]*\n[\s\S]*?```"       # 闭合代码块
        r"|```[a-zA-Z]*\n[\s\S]*?$",       # 或未闭合代码块直到文本结尾
        re.MULTILINE
    )

    matches = list(pattern.finditer(text))
    if not matches:
        return ""

    last_match = matches[-1].group()

    # 去除开头和结尾的 ``` 及可选语言标识
    content = re.sub(r"^```[a-zA-Z]*\n", "", last_match)
    content = re.sub(r"```$", "", content)

    return content.strip()


import re


def extract_last_code_block(text: str) -> str:
    """
    提取文本中最后出现的代码块（闭合或未闭合都可以）。
    """
    pattern = re.compile(
        r"```[a-zA-Z]*\n[\s\S]*?```"       # 闭合代码块
        r"|```[a-zA-Z]*\n[\s\S]*?$",       # 或未闭合代码块直到文本结尾
        re.MULTILINE
    )

    matches = list(pattern.finditer(text))
    if not matches:
        return ""

    last_match = matches[-1].group()

    # 去除开头和结尾的 ``` 及可选语言标识
    content = re.sub(r"^```[a-zA-Z]*\n", "", last_match)
    content = re.sub(r"```$", "", content)

    return content.strip()


if __name__ == "__main__":
    # Case 1: 闭合代码块
    s1 = "文字\n```asm\nmov eax, 1\n```\n其他"
    
    # Case 2: 未闭合代码块
    s2 = "回复如下：\n```c\nint main() {\n  return 0;\n// 没有闭合"
    
    # Case 3: 多个代码块，最后未闭合
    s3 = "前一个\n```py\nprint('hello')\n```\n后一个未闭合\n```js\nconsole.log(1);"

    # Case 4: 多个闭合代码块，最后一个是目标
    s4 = "```go\nfmt.Println(\"one\")\n```\n...\n```go\nfmt.Println(\"two\")\n```"

    # Case 5: 没有代码块
    s5 = "这里没有代码块，仅仅是一些文字内容"

    for i, s in enumerate([s1, s2, s3, s4, s5], 1):
        print(f"\n== Case {i} ==\n{extract_last_code_block(s)}")
