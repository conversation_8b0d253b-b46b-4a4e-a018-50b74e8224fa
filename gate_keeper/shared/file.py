import argparse
import os
import urllib.parse
from pathlib import Path
from typing import List, Optional

from gate_keeper.domain.code.value_objects.language import LANGUAGE_MAP


def list_files(directory: str, recursive: bool = False) -> List[str]:
    """
    List all file paths in the specified directory.
    
    Args:
        directory: Path to the directory to list files from
        recursive: Whether to recursively list files in subdirectories
        
    Returns:
        List of file paths relative to the specified directory
    """
    directory_path = Path(directory)
    
    if not directory_path.exists():
        raise FileNotFoundError(f"Directory not found: {directory}")
    
    if not directory_path.is_dir():
        raise NotADirectoryError(f"Not a directory: {directory}")
    
    file_paths = []
    
    if recursive:
        # Walk through all subdirectories recursively
        for root, _, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_paths.append(file_path)
    else:
        # List only files in the specified directory
        file_paths = [
            str(file_path) for file_path in directory_path.iterdir() 
            if file_path.is_file()
        ]
    
    return file_paths


def determine_language_by_filename(filename):
    ext = os.path.splitext(filename)[1]
    language = None
    for key in LANGUAGE_MAP:
        if ext in LANGUAGE_MAP[key]:
            language = key
            break
    return language

def sanitize_name(name: str) -> str:
    """
    使用 URL 编码（百分号编码）将字符串转为安全路径名，
    保留常见安全字符，编码所有特殊字符（包括 /、= 等）。
    """
    # safe='' 表示所有特殊字符都会被编码，确保路径安全
    return urllib.parse.quote(name, safe='')