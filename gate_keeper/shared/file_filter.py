import fnmatch
import os
import re
from pathlib import Path
from typing import List, Union


class FileFilter:
    """
    文件过滤器类
    提供文件过滤功能，支持多种过滤模式：
    - glob模式：如 "*.py", "tests/*"
    - 正则表达式：如 ".*\\.py$", "tests/.*"
    - 路径前缀：如 "docs/", "archive/"
    """
    
    def __init__(self, exclude_patterns: List[str] = None):
        if exclude_patterns is None:
            exclude_patterns = []
        """
        初始化文件过滤器
        
        Args:
            exclude_patterns: 排除模式列表，支持glob、正则、前缀等
        """
        self.exclude_patterns = exclude_patterns or []
        self._compiled_patterns = self._compile_patterns()
    
    def _compile_patterns(self) -> List[Union[str, re.Pattern]]:
        """
        编译过滤模式
        
        Returns:
            编译后的模式列表
        """
        compiled = []
        for pattern in self.exclude_patterns:
            pattern = pattern.strip()
            if not pattern or pattern.startswith('#'):
                continue  # 跳过空行和注释
            
            # 判断模式类型
            if self._is_regex_pattern(pattern):
                # 正则表达式模式
                try:
                    compiled.append(re.compile(pattern))
                except re.error:
                    # 如果正则编译失败，当作普通glob处理
                    compiled.append(pattern)
            else:
                # glob模式或路径前缀
                compiled.append(pattern)
        
        return compiled
    
    def _is_regex_pattern(self, pattern: str) -> bool:
        """
        判断是否为正则表达式模式：
        1. 以/包裹的
        2. 或包含正则特殊字符（^$+{}[]|()）且不是单纯*或?的glob
        """
        if pattern.startswith('/') and pattern.endswith('/') and len(pattern) > 2:
            return True
        # 只包含*或?的视为glob，其余含正则特殊字符的视为正则
        regex_chars = set('^$+{}[]|()')
        if any(c in pattern for c in regex_chars):
            # 但如果只包含*或?，不是正则
            if not all(c in '*?' for c in pattern if not c.isalnum() and c not in '/._-'):
                return True
        return False
    
    def should_exclude(self, file_path: Union[str, Path]) -> bool:
        """
        判断文件是否应该被排除
        """
        if not self.exclude_patterns:
            return False
        if isinstance(file_path, Path):
            file_path = str(file_path)
        try:
            rel_path = os.path.relpath(file_path)
        except ValueError:
            rel_path = file_path
        filename = os.path.basename(rel_path)
        for pattern in self._compiled_patterns:
            if isinstance(pattern, re.Pattern):
                # 以/包裹的正则表达式，去除包裹再匹配
                regex = pattern.pattern
                if regex.startswith('/') and regex.endswith('/'):
                    regex = regex[1:-1]
                if re.search(regex, rel_path) or re.search(regex, filename):
                    return True
            else:
                # 其余都按glob处理
                if fnmatch.fnmatch(rel_path, pattern):
                    return True
        return False
    
    def filter_files(self, file_paths: List[Union[str, Path]]) -> List[str]:
        """
        过滤文件列表
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            过滤后的文件路径列表
        """
        if not self.exclude_patterns:
            return [str(p) for p in file_paths]
        
        filtered = []
        for file_path in file_paths:
            if not self.should_exclude(file_path):
                filtered.append(str(file_path))
        
        return filtered
    
    def filter_directory(self, root_dir: Union[str, Path]) -> List[str]:
        """
        过滤目录中的所有文件
        
        Args:
            root_dir: 根目录路径
            
        Returns:
            过滤后的文件路径列表
        """
        if not self.exclude_patterns:
            # 如果没有过滤模式，返回所有文件
            return self._collect_all_files(root_dir)
        
        filtered_files = []
        root_path = Path(root_dir)
        
        for file_path in root_path.rglob('*'):
            if file_path.is_file():
                # 转换为相对于根目录的路径
                rel_path = str(file_path.relative_to(root_path))
                if not self.should_exclude(rel_path):
                    filtered_files.append(str(file_path))
        
        return filtered_files
    
    def _collect_all_files(self, root_dir: Union[str, Path]) -> List[str]:
        """
        收集目录中的所有文件（无过滤）
        
        Args:
            root_dir: 根目录路径
            
        Returns:
            所有文件路径列表
        """
        root_path = Path(root_dir)
        return [str(p) for p in root_path.rglob('*') if p.is_file()]
    
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> 'FileFilter':
        """
        从文件创建过滤器
        
        Args:
            file_path: 包含过滤模式的文件路径
            
        Returns:
            FileFilter实例
        """
        patterns = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        patterns.append(line)
        except FileNotFoundError:
            raise FileNotFoundError(f"过滤模式文件不存在: {file_path}")
        except Exception as e:
            raise ValueError(f"读取过滤模式文件失败: {e}")
        
        return cls(patterns)
    
    @classmethod
    def from_environment(cls, env_var: str = 'EXCLUDE_PATTERNS') -> 'FileFilter':
        """
        从环境变量创建过滤器
        
        Args:
            env_var: 环境变量名
            
        Returns:
            FileFilter实例
        """
        import os
        patterns_str = os.getenv(env_var, '')
        if not patterns_str:
            return cls()
        
        patterns = [p.strip() for p in patterns_str.split(',') if p.strip()]
        return cls(patterns)
    
    def add_pattern(self, pattern: str):
        """
        添加过滤模式
        
        Args:
            pattern: 过滤模式
        """
        self.exclude_patterns.append(pattern)
        self._compiled_patterns = self._compile_patterns()
    
    def remove_pattern(self, pattern: str):
        """
        移除过滤模式
        
        Args:
            pattern: 要移除的过滤模式
        """
        if pattern in self.exclude_patterns:
            self.exclude_patterns.remove(pattern)
            self._compiled_patterns = self._compile_patterns()
    
    def clear_patterns(self):
        """清空所有过滤模式"""
        self.exclude_patterns.clear()
        self._compiled_patterns.clear()
    
    def get_patterns(self) -> List[str]:
        """
        获取所有过滤模式
        
        Returns:
            过滤模式列表
        """
        return self.exclude_patterns.copy()
    
    def __str__(self) -> str:
        return f"FileFilter(patterns={self.exclude_patterns})"
    
    def __repr__(self) -> str:
        return self.__str__() 