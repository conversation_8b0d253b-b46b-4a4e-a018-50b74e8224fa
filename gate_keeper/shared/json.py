import json
from typing import IO, Any

from pydantic import BaseModel


def _serialize(obj: Any):
    """
    将对象转换为可 JSON 序列化的数据结构。
    - 支持 BaseModel、BaseModel 列表
    - 递归处理嵌套结构
    """
    if isinstance(obj, BaseModel):
        return obj.model_dump()
    elif isinstance(obj, list):
        return [_serialize(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: _serialize(v) for k, v in obj.items()}
    else:
        return obj  # 原生类型直接返回

def dump_to_json(obj: Any, file: IO, **kwargs):
    """
    将 obj 转换为 JSON 并写入到 file 中，兼容 Pydantic/BaseModel 类型。
    参数 kwargs 会传给 json.dump，例如 indent=4, ensure_ascii=False。
    """
    json.dump(_serialize(obj), file, **kwargs)
