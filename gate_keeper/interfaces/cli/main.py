import argparse
import json
import os
import sys
import traceback
from pathlib import Path
from pprint import pprint

from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.config import config
from gate_keeper.infrastructure.git.codehub.client import CodeHub
from gate_keeper.infrastructure.llm.client.client_factory import LLMFactory
from gate_keeper.shared.file_filter import FileFilter
from gate_keeper.shared.json import dump_to_json
from gate_keeper.shared.log import app_logger as logger

ARGUMENTS = [
    {"name": "--debug", "action": "store_true", "config_key": "DEBUG"},
    {"name": "--repo-dir", "type": str, "config_key": "repo_dir"},
    {"name": "--token", "type": str, "config_key": "token"},
    {"name": "--dev-branch", "type": str, "config_key": "devBranch"},
    {"name": "--base-branch", "type": str, "config_key": "baseBranch"},
    {"name": "--dest-dir", "type": str, "config_key": "dest_dir"},
    {"name": "--output-file-name", "type": str, "config_key": "output_file_name"},
    {"name": "--ollama-endpoint", "type": str, "config_key": "ollama_endpoint"},
    {"name": "--ollama-token", "type": str, "config_key": "ollama_token"},
    {"name": "--llm-model-id", "type": str, "config_key": "llm_model_id"},
    {"name": "--llm-concurrent", "type": int, "config_key": "llm_concurrent"},
    {"name": "--default-llm-max-tokens", "type": int, "config_key": "default_llm_max_tokens"},
    {"name": "--max-diff-results", "type": int, "config_key": "max_diff_results", "help": "限制最大diff result数量，用于测试和调试，默认不限制"},
    {"name": "--log-dir", "type": str, "config_key": "log_dir"},
    {"name": "--log-level", "type": str, "config_key": "log_level"},
    {"name": "--repo-cache-dir", "type": str, "config_key": "repo_cache_dir"},
    {"name": "--max-cache-repos", "type": int, "config_key": "max_cache_repos"},
    {"name": "--max-cache-branches", "type": int, "config_key": "max_cache_branches"},
    {"name": "--max-cache-branch-commits", "type": int, "config_key": "max_cache_branch_commits"},
    {"name": "--graph-call-chain-radius", "type": int, "default": 3, "config_key": "graph_call_chain_radius"},
    {"name": "--rule-file-path", "type": str, "config_key": "rule_file_path"},
    {"name": "--rule-include-sheets", "nargs": '+', "type": str, "config_key": "rule_include_sheets"},
    {"name": "--rule-merge-on", "nargs": '+', "type": str, "config_key": "rule_merge_on"},
    {"name": "--code-check-main-instruction", "type": str, "config_key": "code_check_main_instruction"},
    {"name": "--mr-id", "type": str, "required": True, "config_key": "mr_id"},
    {"name": "--repo-project-id", "type": str, "required": False, "config_key": "repo_project_id"},# codehub project id
    {"name": "--base-commit-sha", "type": str, "required": False},
    {"name": "--dev-commit-sha", "type": str, "required": False},
    # 文件过滤器相关参数
    {"name": "--exclude", "action": "append", "help": "排除的文件模式，支持glob语法"},
    {"name": "--exclude-file", "type": str, "help": "包含排除模式的文件路径"},
    # 规则分组相关参数
    {"name": "--rule-grouping-strategy", "type": str, "choices": ["category", "similarity", "adaptive", "minimize_calls"], "config_key": "rule_grouping_strategy", "help": "规则分组策略: category(按类别), similarity(按相似度), adaptive(自适应), minimize_calls(最小调用次数)"},
    {"name": "--min-rule-group-size", "type": int, "config_key": "min_rule_group_size", "help": "最小规则分组大小 (仅在adaptive策略时使用)"},
    {"name": "--max-rule-group-size", "type": int, "config_key": "max_rule_group_size", "help": "最大规则分组大小 (所有策略都会应用此限制)"},
    {"name": "--target-rule-group-size", "type": int, "config_key": "target_rule_group_size", "help": "目标规则分组大小 (仅在adaptive策略时使用)"},
    {"name": "--rule-similarity-threshold", "type": float, "config_key": "rule_similarity_threshold", "help": "规则相似度分组阈值 (仅在similarity策略时使用)"},
    # LLM调用次数限制参数
    {"name": "--max-llm-calls-per-task", "type": int, "config_key": "max_llm_calls_per_task", "help": "每次任务的总LLM调用次数限制"},
    {"name": "--max-llm-calls-per-affected-function", "type": int, "config_key": "max_llm_calls_per_affected_function", "help": "每个被修改函数的最大LLM调用次数限制"},
    {"name": "--max-llm-calls-per-rule-group", "type": int, "config_key": "max_llm_calls_per_rule_group", "help": "每个规则组的最大LLM调用次数限制"},
]


def parse_exclude_patterns(args) -> list:
    """
    解析排除模式，支持多种来源：
    1. 命令行参数 --exclude
    2. 配置文件 exclude_patterns
    3. 环境变量 EXCLUDE_PATTERNS
    4. 排除模式文件 --exclude-file
    
    Args:
        args: 命令行参数
        
    Returns:
        合并后的排除模式列表
    """
    patterns = []
    
    # 1. 从命令行参数获取
    if hasattr(args, 'exclude') and args.exclude:
        patterns.extend(args.exclude)
    
    # 2. 从配置文件获取
    if hasattr(config, 'exclude_patterns'):
        patterns.extend(config.exclude_patterns)
    
    # 3. 从环境变量获取
    env_patterns = os.getenv('EXCLUDE_PATTERNS')
    if env_patterns:
        env_list = [p.strip() for p in env_patterns.split(',') if p.strip()]
        patterns.extend(env_list)
    
    # 4. 从文件获取
    if hasattr(args, 'exclude_file') and args.exclude_file:
        try:
            file_filter = FileFilter.from_file(args.exclude_file)
            patterns.extend(file_filter.get_patterns())
        except Exception as e:
            logger.warning(f"读取排除模式文件失败: {e}")
    
    # 去重并返回
    return list(set(patterns))


def bind_args_to_config(args):
    for arg in ARGUMENTS:
        key = arg.get("config_key")
        if key and hasattr(config, key):
            # 处理参数名转换，将-替换为_
            arg_name = key.replace("-", "_")
            # 使用getattr安全获取属性，如果不存在则返回None
            value = getattr(args, arg_name, None)
            if value is not None:
                setattr(config, key, value)


def add_arguments(parser):
    for arg in ARGUMENTS:
        kwargs = arg.copy()
        name = kwargs.pop("name")
        # 过滤掉config_key参数，因为argparse不支持
        kwargs.pop("config_key", None)
        parser.add_argument(name, **kwargs)
def main():
    parser = argparse.ArgumentParser(description="GateKeeper · Analyze MR")
    add_arguments(parser)
    args = parser.parse_args()

    # 覆盖配置
    bind_args_to_config(args)
    
    # 解析排除模式
    exclude_patterns = parse_exclude_patterns(args)
    if exclude_patterns:
        config.exclude_patterns = exclude_patterns
        logger.info(f"文件过滤模式: {exclude_patterns}")

    logger.info("当前配置信息如下：")
    pprint({k: getattr(config, k) for k in dir(config) if not k.startswith("__") and not callable(getattr(config, k))})

    # 构建依赖 - 使用服务编排器V2管理复杂依赖关系
    from gate_keeper.application.service.service_orchestrator import \
        ServiceOrchestratorFactoryV2

    # 创建服务编排器V2
    orchestrator = ServiceOrchestratorFactoryV2.create_from_config(
        git_token=config.token,
        use_static_analysis=True,
        use_optimized_context=True
    )
    
    # 创建 usecase（传入服务编排器）
    usecase = AnalyzeMRAndReportUsecase(orchestrator)

    analyze_results, report_content = usecase.execute(
        repo_dir=config.repo_dir,
        project_id=config.repo_project_id,
        mr_id=config.mr_id,
        base_branch=config.baseBranch,
        dev_branch=config.devBranch,
        max_context_chain_depth=config.max_context_chain_depth,
        base_commit_sha=getattr(config, 'base_commit_sha', None),
        dev_commit_sha=getattr(config, 'dev_commit_sha', None),
        exclude_patterns=config.exclude_patterns,
        max_diff_results=getattr(config, 'max_diff_results', None),
    )

    dest_path = Path(config.dest_dir)
    dest_path.mkdir(parents=True, exist_ok=True)

    report_path = dest_path / "report.md"
    raw_result_path = dest_path / config.output_file_name

    report_path.write_text(report_content, encoding="utf-8")
    logger.info(f"报告已生成: {report_path}")

    with open(raw_result_path, "w", encoding="utf-8") as f:
        dump_to_json(analyze_results, f, ensure_ascii=False, indent=4)
    logger.info(f"原始结果已写入: {raw_result_path}")


if __name__ == "__main__":
    print("GateKeeper CLI Version: 1")
    try:        
        # 调试模式：如果直接运行且没有参数，使用默认值
        if len(sys.argv) == 1:
            # 从环境变量读取参数，如果没有则使用默认值
            sys.argv.extend([
                "--mr-id", str(config.mr_id),
                "--repo-project-id", str(config.repo_project_id), 
                "--dev-branch", config.devBranch,
                "--base-branch", config.baseBranch,
                "--token", config.token,
                "--repo-dir", config.repo_dir,
            ])
        main()
    except Exception as e:
        logger.error(f"执行失败: {e}")
        traceback.print_exc()
