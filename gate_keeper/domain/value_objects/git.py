from datetime import datetime
from typing import List, Optional

from pydantic.dataclasses import dataclass


@dataclass
class CodeCheckDiscussion:
    id: str
    body: str
    mr_id: str
    project_id: Optional[str] = None
    commit_id: Optional[str] = None
    checked_code: Optional[str] = None
    resolved: Optional[bool] = None

@dataclass
class MergeRequest:
    id: str
    title: str
    state: str
    source_branch: str
    target_branch: str
    author: str  # 仅保留用户名或昵称
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    web_url: Optional[str] = None