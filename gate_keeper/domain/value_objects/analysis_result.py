import uuid
from typing import List, Optional

from pydantic import BaseModel


class ViolationItem(BaseModel):
    rule_id: str
    rule_content: Optional[str] = None
    location: dict
    severity: Optional[str] = None
    message: str

class AnalyzeLLMResult(BaseModel):
    is_pass: bool
    reason: str
    violations: List[ViolationItem]
    code: str = ""
    prompt: Optional[str] = None  # 记录本次 LLM 调用的 prompt
    response: Optional[str] = None  # 记录本次 LLM 调用的 response
    call_id: Optional[str] = None  # 记录本次 LLM 调用的唯一 ID
    test_case_info: Optional[dict] = None  # 测试用例信息（用于无git信息的测试用例）

class AggregatedAnalyzeLLMResult(BaseModel):
    """聚合的LLM分析结果，用于合并多个AnalyzeLLMResult"""
    is_pass: bool
    reason: str
    violations: List[ViolationItem]
    code: str = ""
    call_ids: List[str] = []  # 聚合的所有LLM调用的call_id列表
    function_name: Optional[str] = None  # 函数名称
    file_path: Optional[str] = None  # 文件路径