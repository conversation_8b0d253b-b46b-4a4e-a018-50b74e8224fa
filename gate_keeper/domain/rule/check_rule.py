from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class PromptTemplate(BaseModel):
    """规则专用的prompt模板"""
    instruction: str = ""  # 自定义指令，覆盖默认指令
    requirement_template: str = ""  # 规则要求模板，支持变量替换
    example_template: str = ""  # 示例模板
    output_format: str = ""  # 自定义输出格式
    context_requirements: str = ""  # 上下文要求说明
    focus_points: List[str] = Field(default_factory=list)  # 重点关注点列表


class GitInfo(BaseModel):
    """Git信息"""
    repo_url: str = ""
    branch: str = ""
    commit: str = ""
    mr_id: str = ""
    file_path: str = ""
    function_name: str = ""


class TestCase(BaseModel):
    """评测用例 - 扩展自CodeCheckRule，用于评估模式"""
    # 继承CodeCheckRule的所有字段
    id: str
    name: str
    description: str = ""
    category: List[str]
    enabled: bool = True
    severity: str = ""
    file_path: str = ""
    rule_value: str = ""
    contact_info: Any = None
    example: str = ""
    languages: List[str] = Field(default_factory=list)
    raw: Any = ""

    # 新增：评测集优化字段
    match_regex: str = ""  # 用于识别应该应用该规则的代码模式
    suggestion: str = ""   # 检查该规则时的建议和注意方向
    
    # prompt模板相关字段
    prompt_template: Optional[PromptTemplate] = None  # 自定义prompt模板
    custom_instruction: str = ""  # 自定义指令（简化版本）
    custom_requirement_format: str = ""  # 自定义要求格式
    custom_example_format: str = ""  # 自定义示例格式
    custom_output_format: str = ""  # 自定义输出格式
    focus_keywords: List[str] = Field(default_factory=list)  # 重点关注关键词
    
    # 评估模式特有字段
    case_name: str = ""  # 测试用例名称
    expected_answer: str = ""  # 期望答案
    think: str = ""  # 思考过程
    code: str = ""  # 测试代码
    git: Optional[GitInfo] = None  # Git信息
    type: str = "positive"  # 测试类型：positive/negative
    yaml_source: Optional[str] = None  # YAML源文件路径
    
    def to_check_rule(self) -> 'CodeCheckRule':
        """转换为CodeCheckRule对象"""
        return CodeCheckRule(
            id=self.id,
            name=self.name,
            description=self.description,
            category=self.category,
            enabled=self.enabled,
            severity=self.severity,
            file_path=self.file_path,
            rule_value=self.rule_value,
            contact_info=self.contact_info,
            example=self.example,
            languages=self.languages,
            raw=self.raw,
            match_regex=self.match_regex,
            suggestion=self.suggestion,
            prompt_template=self.prompt_template,
            custom_instruction=self.custom_instruction,
            custom_requirement_format=self.custom_requirement_format,
            custom_example_format=self.custom_example_format,
            custom_output_format=self.custom_output_format,
            focus_keywords=self.focus_keywords
        )
    
    @classmethod
    def from_check_rule(cls, rule: 'CodeCheckRule', **kwargs) -> 'TestCase':
        """从CodeCheckRule创建TestCase"""
        return cls(
            id=rule.id,
            name=rule.name,
            description=rule.description,
            category=rule.category,
            enabled=rule.enabled,
            severity=rule.severity,
            file_path=rule.file_path,
            rule_value=rule.rule_value,
            contact_info=rule.contact_info,
            example=rule.example,
            languages=rule.languages,
            raw=rule.raw,
            match_regex=rule.match_regex,
            suggestion=rule.suggestion,
            prompt_template=rule.prompt_template,
            custom_instruction=rule.custom_instruction,
            custom_requirement_format=rule.custom_requirement_format,
            custom_example_format=rule.custom_example_format,
            custom_output_format=rule.custom_output_format,
            focus_keywords=rule.focus_keywords,
            **kwargs
        )


class CodeCheckRule(BaseModel):
    id: str
    name: str
    description: str = ""
    category: List[str]
    enabled: bool = True
    severity: str = ""
    file_path: str = ""
    rule_value: str = ""
    contact_info: Any = None
    example: str = ""
    languages: List[str] = Field(default_factory=list)
    raw: Any = ""


    match_regex: str = ""  # 用于识别应该应用该规则的代码模式
    suggestion: str = ""   # 检查该规则时的建议和注意方向

    # 新增：prompt模板相关字段
    prompt_template: Optional[PromptTemplate] = None  # 自定义prompt模板
    custom_instruction: str = ""  # 自定义指令（简化版本）
    custom_requirement_format: str = ""  # 自定义要求格式
    custom_example_format: str = ""  # 自定义示例格式
    custom_output_format: str = ""  # 自定义输出格式
    focus_keywords: List[str] = Field(default_factory=list)  # 重点关注关键词
    
    def get_effective_instruction(self) -> str:
        """获取有效的指令内容"""
        if self.prompt_template and self.prompt_template.instruction:
            return self.prompt_template.instruction
        return self.custom_instruction
    
    def get_effective_requirement_format(self) -> str:
        """获取有效的要求格式"""
        if self.prompt_template and self.prompt_template.requirement_template:
            return self.prompt_template.requirement_template
        return self.custom_requirement_format
    
    def get_effective_example_format(self) -> str:
        """获取有效的示例格式"""
        if self.prompt_template and self.prompt_template.example_template:
            return self.prompt_template.example_template
        return self.custom_example_format
    
    def get_effective_output_format(self) -> str:
        """获取有效的输出格式"""
        if self.prompt_template and self.prompt_template.output_format:
            return self.prompt_template.output_format
        return self.custom_output_format
    
    def get_focus_points(self) -> List[str]:
        """获取重点关注点"""
        if self.prompt_template and self.prompt_template.focus_points:
            return self.prompt_template.focus_points
        return self.focus_keywords
    
    def has_custom_prompt(self) -> bool:
        """检查是否有自定义prompt"""
        return (self.prompt_template is not None or 
                self.custom_instruction or 
                self.custom_requirement_format or 
                self.custom_example_format or 
                self.custom_output_format or 
                self.focus_keywords)
    
    def format_requirement_with_variables(self, **kwargs) -> str:
        """使用变量格式化要求模板"""
        template = self.get_effective_requirement_format()
        if not template:
            # 如果没有自定义模板，使用默认格式
            return self._get_default_requirement_format()
        
        try:
            return template.format(**kwargs)
        except (KeyError, ValueError) as e:
            # 如果格式化失败，返回原始模板
            return template
    
    def _get_default_requirement_format(self) -> str:
        """获取默认的要求格式"""
        parts = []
        parts.append(f"规则: {self.id}")
        if self.name != self.id:
            parts.append(f"名称: {self.name}")
        if self.category:
            parts.append(f"类型: {'-'.join(self.category)}")
        if self.severity:
            parts.append(f"优先级: {self.severity}")
        if self.rule_value:
            parts.append(f"规则值: {self.rule_value}")
        if self.description:
            parts.append(f"描述: {self.description}")
        if self.example:
            parts.append(f"示例: {self.example}")
        
        return "\n".join(parts)
    

