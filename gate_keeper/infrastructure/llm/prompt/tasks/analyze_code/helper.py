from typing import List

from gate_keeper.infrastructure.llm.dto.prompt import Example, Requirement


def default_requirements_for_language(language: str) -> List[Requirement]:
    if language.lower() == "c":
        return [
            Requirement(requirement="资源管理\n检查项:\n动态申请的资源是否及时释放\n\n问题描述:\n可能存在资源泄露风险，需确认所有申请资源均有对应释放"),
            Requirement(requirement="循环变量范围\n检查项:\n循环变量类型与边界条件匹配性\n\n问题描述:\n循环变量类型与边界条件类型不匹配，可能导致死循环或异常"),
            Requirement(requirement="安全函数使用\n检查项:\n检查安全函数调用是否正确，返回值是否校验\n\n问题描述:\n安全函数未正确处理返回值，存在风险"),
        ]
    elif language.lower() == "python":
        return [
            Requirement(requirement="资源管理\n检查项:\n文件、网络连接等资源是否正确关闭\n\n问题描述:\n打开的资源未正确关闭，可能导致资源泄漏"),
            Requirement(requirement="循环变量范围\n检查项:\n循环终止条件和迭代变量匹配\n\n问题描述:\n循环可能永远不会结束，导致死循环"),
            Requirement(requirement="异常处理\n检查项:\n异常是否被正确捕获和处理\n\n问题描述:\n未处理异常可能导致程序崩溃"),
        ]
    else:
        return [
            Requirement(requirement="资源管理\n检查项:\n动态资源申请与释放是否匹配\n\n问题描述:\n存在资源泄露风险"),
        ]

def default_examples_for_language(language: str) -> List[Example]:
    if language.lower() == "c":
        example1 = """
用户输入：
```

\#include \<stdlib.h>

void example() {
char\* p = malloc(100);
// 忘记释放内存
}

```
检查结果:
<FinalAnswer>
{
    "is_pass": false,
    "reason": "检测到资源泄露风险",
    "violations": [
        {
            "rule_id": "资源管理",
            "location": { "file_path": "example.c" },
            "message": "动态申请的内存未释放"
        }
    ]
}
</FinalAnswer>
"""
        example2 = """
用户输入：
```

void loop\_issue() {
unsigned char i = 0;
while(i < 300) {
// 可能死循环，因为i为8位
i++;
}
}

```
检查结果:
<FinalAnswer>
{
    "is_pass": false,
    "reason": "循环变量类型和边界可能导致死循环",
    "violations": [
        {
            "rule_id": "循环变量范围",
            "location": { "file_path": "loop.c" },
            "message": "循环变量类型为unsigned char，循环边界超过其最大值，可能死循环"
        }
    ]
}
</FinalAnswer>
"""
        return [Example(content=example1), Example(content=example2)]

    elif language.lower() == "python":
        example1 = """
用户输入：
```

def read\_file():
f = open("file.txt", "r")
data = f.read()
\# 忘记关闭文件
return data

```
检查结果:
<FinalAnswer>
{
    "is_pass": false,
    "reason": "资源泄露风险，文件未关闭",
    "violations": [
        {
            "rule_id": "资源管理",
            "location": { "file_path": "example.py" },
            "message": "打开的文件未关闭"
        }
    ]
}
</FinalAnswer>
"""
        example2 = """
用户输入：
```

i = 0
while i < 5:
print(i)
\# 忘记增加 i，导致死循环

```
检查结果:
<FinalAnswer>
{
    "is_pass": false,
    "reason": "循环变量未更新导致死循环风险",
    "violations": [
        {
            "rule_id": "循环变量范围",
            "location": { "file_path": "loop.py" },
            "message": "循环变量未更新，可能死循环"
        }
    ]
}
</FinalAnswer>
"""
        return [Example(content=example1), Example(content=example2)]

    else:
        return []

