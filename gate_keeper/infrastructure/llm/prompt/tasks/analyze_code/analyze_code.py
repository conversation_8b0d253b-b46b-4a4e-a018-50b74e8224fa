# gate_keeper/infrastructure/llm/prompt/tasks/analyze_code/analyze_code.py
import json
from typing import List

from gate_keeper.config import config
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.infrastructure.llm.dto.prompt import (Example, Instruction,
                                                       Requirement)
from gate_keeper.infrastructure.llm.prompt.tasks.analyze_code.helper import (
    default_examples_for_language, default_requirements_for_language)


def get_instruction_main():
  instruction=config.code_check_main_instruction or """请结合提供的上下文仔细分析待检查的代码，检查是否违反用户提供的代码规范.
  <important-rules>
  1. 仅检查用户提供的规则内容，忽略所有未明确给出的规范。
  2. 若未发现违反规则的情况，应将 is_pass 设置为 true,避免误判。
  3. 默认仅检查“待检查函数”，除非某条规则明确需要结合上下游函数进行判断。
  4. 若违规发生在“上下文代码”中（即待检查函数的上游/下游调用函数），必须在 message 中清楚注明“待检查函数的上游函数 xxx”或“下游函数 xxx 存在问题”，以避免混淆。
  5. 在说明中包含关键的违规代码片段，方便用户快速定位问题。
  </important-rules>
  """
  return instruction

def get_output_schema_str(exclude_fields=None):
    """
    动态生成输出schema字符串，只包含required字段，并可额外排除指定字段
    """
    schema = AnalyzeLLMResult.model_json_schema()
    
    # 先只保留required字段
    required_fields = schema.get('required', [])
    filtered_properties = {}
    
    for field in required_fields:
        if field in schema['properties']:
            filtered_properties[field] = schema['properties'][field]
    
    # 然后排除指定的字段
    if exclude_fields:
        for field in exclude_fields:
            filtered_properties.pop(field, None)
            required_fields = [r for r in required_fields if r != field]
    
    # 构建过滤后的schema
    filtered_schema = {
        "type": "object",
        "properties": filtered_properties,
        "required": required_fields,
        "$defs": schema.get("$defs", {})
    }
    
    return json.dumps(filtered_schema, ensure_ascii=False, indent=2)


OUTPUT_FORMAT = """
## 输出格式要求：
- 将检测结果格式化为 JSON，使用一对 XML 标签 <FinalAnswer></FinalAnswer> 包裹。
- 输出应包含字段：is_pass、reason、violations（列表）。
- 输出 JSON 的 schema 如下：
""" \
+ get_output_schema_str(exclude_fields=['code', 'prompt', 'response', 'call_id', 'test_case_info']) \
+ """

## 输出示例：

**通过的情况：**
```
<FinalAnswer>
{
  "is_pass": true,
  "reason": "代码符合所有检查规则",
  "violations": []
}
</FinalAnswer>
```

**不通过的情况：**
```
<FinalAnswer>
{
  "is_pass": false,
  "reason": "发现内存管理违规",
  "violations": [
    {
      "rule_id": "MEM-01",
      "rule_content": "禁止使用malloc",
      "location": {"file_path": "src/test.c"},
      "severity": "高",
      "message": "函数中使用了malloc申请内存: char* buf = malloc(100);"
    }
  ]
}
</FinalAnswer>
```

"""

def create_instruction(check_rules: List[CodeCheckRule] = None, language: str = "C"):
    base_instruction = Instruction(
        instruction=get_instruction_main(),
        requirements=[],
        examples=[],
        output_format=OUTPUT_FORMAT
    )

    if not check_rules:
        # 返回默认规则集
        base_instruction.requirements.extend(default_requirements_for_language(language))
        base_instruction.examples.extend(default_examples_for_language(language))
        return base_instruction

    requirements = []
    examples = []
    custom_instructions = []
    
    for rule_index, rule in enumerate(check_rules):
        if language in rule.languages or not rule.languages:
            # 检查是否有自定义指令
            if rule.has_custom_prompt():
                # 使用自定义prompt模板
                custom_instruction = rule.get_effective_instruction()
                if custom_instruction:
                    custom_instructions.append(custom_instruction)
                
                # 使用自定义要求格式
                requirement_content = rule.format_requirement_with_variables(
                    rule_id=rule.id,
                    rule_name=rule.name,
                    rule_type='-'.join(rule.category) if rule.category else "未分类",
                    severity=rule.severity or "未指定",
                    rule_value=rule.rule_value,
                    description=rule.description,
                    language=language
                )
            else:
                # 使用默认格式
                rule_name = rule.id if rule.id == rule.name else rule.id+"-"+rule.name
                requirement_content = "\n".join([
                    f"规则: {rule_name}",
                    f"规则类型: {'-'.join(rule.category)}",
                    f"优先级: {rule.severity or '未指定'}",
                    f"规则内容: {rule.rule_value or rule.description or '未指定'}"])
            
            req = Requirement(requirement=requirement_content)
            
            # 处理示例
            if rule.has_custom_prompt() and rule.get_effective_example_format():
                # 使用自定义示例格式
                example_content = rule.get_effective_example_format()
                if rule.example:
                    # 如果有原始示例，可以合并或替换
                    example_content = f"{example_content}\n\n原始示例:\n{rule.example}"
            elif rule.example:
                # 使用原始示例
                example_content = rule.example
            else:
                example_content = ""
            
            if example_content:
                req.examples.append(Example(content=example_content))
            
            # 添加重点关注点
            focus_points = rule.get_focus_points()
            if focus_points:
                focus_content = "重点关注:\n" + "\n".join(f"- {point}" for point in focus_points)
                req.requirement += f"\n\n{focus_content}"
            
            requirements.append(req)

    # 合并自定义指令
    final_instruction = get_instruction_main()
    if custom_instructions:
        custom_instruction_text = "\n\n".join(custom_instructions)
        final_instruction += f"\n\n自定义规则指令:\n{custom_instruction_text}"

    inst = Instruction(
        instruction=final_instruction,
        requirements=requirements,
        examples=examples,
        output_format=OUTPUT_FORMAT
    )
    return inst
