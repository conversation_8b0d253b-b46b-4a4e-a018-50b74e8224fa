from gate_keeper.infrastructure.llm.dto.prompt import (Example, Instruction,
                                                       Requirement)

SYSTEM="You are a helpful professional coding expert focus on perfomance,security field"


INSTRUCTION_MAIN="Please analyze the provided code and its context to find if there are user concerned defects"

INSTRUCTION_REQ_1_TITLE="1.规范类"

INSTRUCTION_REQ_1_REQ_1="""1.1 命名
命名应该避免的名称以下命名应该尽量避免单字符名称, 除了计数器和迭代器.
"""
INSTRUCTION_REQ_1_EXP_1="""
if __name__ == '__main__':
    # 不推荐的写法
    # 尽量避免单字符变量名
    s = "hello world!
"""


INSTRUCTION_REQ_1_REQ_2="""1.2 包/模块名中的连字符(-)"""

INSTRUCTION_REQ_1_EXP_2="""
# 错误的包名
# 引用文件 html-parser.py
import html-parser

# 正确的写法
# 文件名应为 html_parser.py
import html_parser
"""

INSTRUCTION_REQ_1_REQ_3="""1.3 应避免使用小写字母l(L)，大写字母O(o)或I(i)单独作为一个变量的名称，以区分数字1和0"""

INSTRUCTION_REQ_1_EXP_3="""
if __name__ == '__main__':
    # 不推荐的写法
    # 尽量避免l、O 等容易混淆的字母
    l = 1
    O = 0
    l = (O + 1)*l
"""

INSTRUCTION_REQ_1_REQ_4="""1.4 
当参数名称和Python保留字冲突，可在最后添加一个下划线，而不是使用缩写或自造的词
# 如果变量名和python保留字冲突，则在末尾添加下划线
# 切记不要自己造词，或者使用缩写
"""

INSTRUCTION_REQ_1_EXP_4="""
def print_():
    ... ...

if __name__ == '__main__':
    str_ = "hello world!"
    print_(str_)
"""


INSTRUCTION_REQ_2_TITLE="""2.命名约定"""

INSTRUCTION_REQ_2_REQ_1="""
2.1 模块
模块尽量使用小写命名，首字母保持小写，尽量不要用下划线(除非多个单词，且数量不多的情况)
# 正确的模块名
import decoder
import html_parser

# 不推荐的模块名
import Decoder
2.2 类名
类名使用驼峰(CamelCase)命名风格，首字母大写，私有类可用一个下划线开头
class Farm():
    pass

class AnimalFarm(Farm):
    pass

class _PrivateFarm(Farm):
    pass
将相关的类和顶级函数放在同一个模块里. 不像Java, 没必要限制一个类一个模块.

2.3 函数
函数名一律小写，如有多个单词，用下划线隔开
def run():
    pass

def run_with_env():
    pass
私有函数在函数前加一个下划线_
class Person():

    def _private_func():
        pass
2.4 变量名
变量名尽量小写, 如有多个单词，用下划线隔开
if __name__ == '__main__':
    count = 0
    school_name = ''
常量采用全大写，如有多个单词，使用下划线隔开
MAX_CLIENT = 100
MAX_CONNECTION = 1000
CONNECTION_TIMEOUT = 600

"""



example1="""普通变量
PUBLIC_VARIABLE="This is public variable"

<FinalAnswer>
{
  "is_pass": false,
  "reason": "检测到1个代码规范问题",
  "code": "CODE_STYLE_VIOLATION",
  "violations": [
    {
      "rule_id": "INVALID_VARIABLE_NAMING",
      "location": {
        "file_path": "unknown"
      },
      "message": "变量名 'PUBLIC_VARIABLE' 不符合普通变量命名规范，应使用小写字母和下划线连接"
    }
  ]
}
</FinalAnswer>
"""

example2="""常量
API_KEY="your_api_key_here"

<FinalAnswer> { "is_pass": true, "reason": "所有变量命名均符合规范", "code": "CODE_STYLE_PASS", "violations": [] } </FinalAnswer>
"""

INSTRUCTION_REQ_OUTPUT="""将检测输出格式化为JSON格式，格式如下：
{
    "type": "object",
    "properties": {
        "is_pass": { "type": "boolean" },
        "reason": { "type": "string" },
        "code": { "type": "string" },
        "violations": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "rule_id": { "type": "string" },
                    "location": {
                        "type": "object",
                        "properties": {
                            "file_path": { "type": "string" }
                        },
                        "required": ["file_path"]
                    },
                    "message": { "type": "string" }
                },
                "required": ["rule_id", "message","location"]
            }
        }
    },
    "required": ["is_pass", "reason", "code"]
}
"""

INSTRUCTION_REQ_OUTPUT_EXP="""
<FinalAnswer>
{
    "is_pass": false,
    "reason": "检测到2个代码规范问题",
    "code": "CODE_STYLE_VIOLATION",
    "violations": [
        {
            "rule_id": "LINE_LENGTH_EXCEEDED",
            "severity": "warning",
            "location": {
                "file_path": "src/main.py"
            },
            "message": "第15行代码长度超过80个字符"
        },
        {
            "rule_id": "MISSING_DOCSTRING",
            "severity": "error",
            "location": {
                "file_path": "src/utils/api_client.py"
            },
            "message": "公共函数`fetch_data()`缺少文档字符串"
        }
    ]
}
</FinalAnswer>
"""




GENERATION_EXAPMLES = [
    Example(content=example1),
    Example(content=example2)
]

INSTRUCTION = Instruction(
    instruction=INSTRUCTION_MAIN,
    requirements=[
        Requirement(
            requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_1]),
            examples=[
                Example(content=INSTRUCTION_REQ_1_EXP_1),
            ]
        ),
        Requirement(
            requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_2]),
            examples=[
                Example(content=INSTRUCTION_REQ_1_EXP_2),
            ]
        ),
        Requirement(
            requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_3]),
            examples=[
                Example(content=INSTRUCTION_REQ_1_EXP_3),
            ]
        ),
        Requirement(
            requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_4]),
            examples=[
                Example(content=INSTRUCTION_REQ_1_EXP_4),
            ]
        ),
        Requirement(
            requirement="\n".join([INSTRUCTION_REQ_2_TITLE,INSTRUCTION_REQ_2_REQ_1])
        ),
        Requirement(
            requirement="\n".join([INSTRUCTION_REQ_OUTPUT,INSTRUCTION_REQ_OUTPUT_EXP])
        )
    ],
    examples=GENERATION_EXAPMLES
)
