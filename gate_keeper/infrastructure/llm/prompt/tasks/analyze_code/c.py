from typing import List

from gate_keeper.config import config
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.dto.prompt import (Example, Instruction,
                                                       Requirement)

SYSTEM="你是一个乐于助人的基于代码规范的缺陷检查助手,关注代码性能和设计规范"




INSTRUCTION_REQ_1_TITLE="通用编码守则"

INSTRUCTION_REQ_1_REQ_1="""通用编码守则-基础篇-4.2
检查项:
1.函数内的内存泄露

问题描述:
临时内存申请使用后未释放
"""

INSTRUCTION_REQ_1_REQ_2="""通用编码守则-基础篇-5.1
检查项:
循环变量范围小于阈值造成死循环

问题描述:
使用UINT8或UINT16类型循环变量与UINT32类型阈值比较,可能发生死循环
"""

INSTRUCTION_REQ_1_REQ_3="""通用编码守则-基础篇-5.4
检查项: 
IPV6处理遗漏

问题描述:
业务流程漏考虑IPV6场景
"""

INSTRUCTION_REQ_1_REQ_4="""通用编码守则-基础篇-8.1
检查项:
安全函数检查

问题描述:
1.源目的缓冲区长度参数错误或未经逻辑检查写成相同的值
2.sprintf_s强转无符号类型后判断大于0

"""

INSTRUCTION_REQ_1_REQ_5="""通用编码守则-基础篇-8.2
检查项: 
安全函数返回值检查

问题描述:
安全函数没有检查返回值，例外情况:临时定义的结构变量清零

"""


INSTRUCTION_REQ_2_TITLE="""转发框架"""

INSTRUCTION_REQ_2_REQ_1="""转发框架-1.4
检查项: 
buffer重复释放

问题描述:
同一buffer资源被先后释放2次,或者主动释放后还在使用
"""



example1="""
用户输入：
```
#include <stdlib.h>
#include <stdint.h>

void example_leak_and_loop(uint32_t limit) {
    uint8_t i;
    char *buf = (char *)malloc(256);
    
    for (i = 0; i < limit; i++) {
        // do something
    }

    // buf 未释放，存在内存泄露
}
```
检查结果:
<FinalAnswer> 
{ "is_pass": false, "reason": "检测到2个代码规范问题", "violations": [ { "rule_id": "通用编码守则-基础篇-4.2", "location": { "file_path": "example.c" }, "message": "函数内申请的临时内存 buf 未释放，存在内存泄露风险" }, { "rule_id": "通用编码守则-基础篇-5.1", "location": { "file_path": "example.c" }, "message": "使用 uint8_t 类型变量 i 与 uint32_t 类型的 limit 比较，可能造成死循环" } ] } 
</FinalAnswer>
"""

example2="""
用户输入:
```
#include <stdio.h>

void safe_formatting(char *dst, size_t dst_size, int id) {
    snprintf(dst, dst_size, "Device ID: %d", id);
}
```
检查结果:
<FinalAnswer> 
{ "is_pass": true, "reason": "所有编码规范项检查通过", "violations": [] } 
</FinalAnswer>
"""

INSTRUCTION_REQ_OUTPUT="""将检测输出格式化为JSON格式，格式如下：
{
    "type": "object",
    "properties": {
        "is_pass": { "type": "boolean" },
        "reason": { "type": "string" },
        "violations": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "rule_id": { "type": "string" },
                    "location": {
                        "type": "object",
                        "properties": {
                            "file_path": { "type": "string" }
                        },
                        "required": ["file_path"]
                    },
                    "message": { "type": "string" }
                },
                "required": ["rule_id", "message","location"]
            }
        }
    },
    "required": ["is_pass", "reason", "code"]
}
并使用<FinalAnswer></FinalAnswer> 标签包裹
"""

INSTRUCTION_REQ_OUTPUT_EXP="""
<FinalAnswer>
{
    "is_pass": false,
    "reason": "检测到2个代码规范问题",
    "violations": [
        {
            "rule_id": "LINE_LENGTH_EXCEEDED",
            "severity": "warning",
            "location": {
                "file_path": "src/main.py"
            },
            "message": "第15行代码长度超过80个字符"
        },
        {
            "rule_id": "MISSING_DOCSTRING",
            "severity": "error",
            "location": {
                "file_path": "src/utils/api_client.py"
            },
            "message": "公共函数`fetch_data()`缺少文档字符串"
        }
    ]
}
</FinalAnswer>
"""




GENERATION_EXAPMLES = [
    Example(content=example1),
    Example(content=example2)
]

# INSTRUCTION = Instruction(
#     instruction=INSTRUCTION_MAIN,
#     requirements=[
#         Requirement(
#             requirement="\n".join([INSTRUCTION_REQ_OUTPUT,INSTRUCTION_REQ_OUTPUT_EXP])
#         ),
#         Requirement(
#             requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_1]),
#             # examples=[
#             #     Example(content=INSTRUCTION_REQ_1_EXP_1),
#             # ]
#         ),
#         Requirement(
#             requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_2]),
#             # examples=[
#             #     Example(content=INSTRUCTION_REQ_1_EXP_2),
#             # ]
#         ),
#         Requirement(
#             requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_3]),
#             # examples=[
#             #     Example(content=INSTRUCTION_REQ_1_EXP_3),
#             # ]
#         ),
#         Requirement(
#             requirement="\n".join([INSTRUCTION_REQ_1_TITLE,INSTRUCTION_REQ_1_REQ_4]),
#             # examples=[
#             #     Example(content=INSTRUCTION_REQ_1_EXP_4),
#             # ]
#         ),
#         Requirement(
#             requirement="\n".join([INSTRUCTION_REQ_2_TITLE,INSTRUCTION_REQ_2_REQ_1])
#         )
#     ],
#     examples=GENERATION_EXAPMLES
# )
