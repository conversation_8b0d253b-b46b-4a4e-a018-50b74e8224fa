from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from gate_keeper.config import config


class LLMParameters(BaseModel):
    model_id: str = Field(default_factory=lambda: config.llm_model_id, description="LLM model identifier")
    temperature: float = Field(default=0.7, ge=0, le=2)
    stream: bool = Field(default=False)
    max_tokens: int = Field(default_factory=lambda: config.default_llm_max_tokens or 36000,gt=0)
    top_p: Optional[float] = Field(default=None, ge=0, le=1)
    stop: Optional[List[str]] = Field(default=None, description="Stop sequences")
    system_prompt: Optional[str] = None
    extra: Dict[str, Any] = Field(default_factory=dict, description="Backend-specific extra parameters")
