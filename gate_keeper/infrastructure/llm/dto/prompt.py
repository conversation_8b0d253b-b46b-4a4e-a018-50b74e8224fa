#gate_keeper/infrastructure/llm/dto/prompt.py
from typing import List, Optional

from pydantic import BaseModel, Field


class Example(BaseModel):
    content: str


class Requirement(BaseModel):
    requirement: str
    examples: Optional[List[Example]] = Field(default_factory=list)


class Instruction(BaseModel):
    instruction: str
    requirements: Optional[List[Requirement]] = Field(default_factory=list)
    examples: Optional[List[Example]] = Field(default_factory=list)
    output_format: str = None


class UserInput(BaseModel):
    content: str


class Prompt(BaseModel):
    system: str = ""
    instruction: Optional[Instruction] = None
    user_input: Optional[UserInput] = None

    def set_instruction(self, instruction: str, examples: Optional[List[str]] = None, requirements: Optional[List[dict]] = None):
        req_objs = []
        if requirements:
            for r in requirements:
                req_examples = [Example(content=e) for e in r.get("examples", [])]
                req_objs.append(Requirement(requirement=r["requirement"], examples=req_examples))

        example_objs = [Example(content=e) for e in examples] if examples else []

        self.instruction = Instruction(
            instruction=instruction,
            requirements=req_objs if req_objs else None,
            examples=example_objs if example_objs else None
        )

    def set_user_input(self, user_input: str):
        self.user_input = UserInput(content=user_input)

    def basic_render(self) -> str:
        parts = [self.system.strip(), ""]

        if self.instruction:
            # Instruction content
            if self.instruction.instruction:
                parts.append(self.instruction.instruction.strip())            
            # Requirements section
            if self.instruction.requirements:
                parts.append("<check_rules>")
                for req in self.instruction.requirements:
                    parts.append(req.requirement.strip())
                    if req.examples:
                        parts.append("例如:\n")
                        for ex in req.examples:
                            parts.append(f"- {ex.content.strip()}")
                parts.append("</check_rules>")

            # Examples (non-requirement examples)
            if self.instruction.examples:
                parts.append("例如:\n")
                for ex in self.instruction.examples:
                    parts.append(f"- {ex.content.strip()}")
            
            if self.instruction.output_format:
                parts.append(self.instruction.output_format.strip())

        # User input
        if self.user_input:
            parts.append("###\n用户输入:\n")
            parts.append(self.user_input.content.strip())

        return "\n\n".join([p for p in parts if p])