import json
import logging
import ssl
from abc import ABC, abstractmethod
from typing import Generator, Union
from urllib.parse import urljoin, urlparse

import requests
from requests.adapters import HTTPAdapter
from urllib3.poolmanager import PoolManager

from gate_keeper.shared.log import app_logger as logger


class LLMClient(ABC):
    def __init__(self, base_url, timeout=360000, headers=None, session=None):
        self.base_url = base_url
        self.timeout = timeout
        self.headers = headers or {"Content-Type": "application/json"}
        self.session = session or requests.Session()

    def send_request(self, endpoint, method="GET", params=None, data=None,
                     headers=None, stream=False, files=None, json=True, max_retries=3):
        url = urljoin(self.base_url, endpoint)
        all_headers = {**self.headers, **(headers or {})}
        payload = {"json": data} if json else {"data": data}

        for attempt in range(max_retries):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=all_headers,
                    params=params,
                    timeout=self.timeout,
                    stream=stream,
                    verify=True,  # Enable SSL verification
                    files=files,
                    **payload
                )
                response.raise_for_status()
                return response
            except requests.Timeout:
                logger.warning(f"Request timed out after {self.timeout} seconds (attempt {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    logger.error(f"Request timed out after {max_retries} attempts")
                    raise
                # 指数退避
                import time
                time.sleep(2 ** attempt)
            except requests.RequestException as e:
                logger.warning(f"Request failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    logger.error(f"Request failed after {max_retries} attempts: {e}")
                    raise
                # 指数退避
                import time
                time.sleep(2 ** attempt)

    @abstractmethod
    def generate(self, prompt: str, parameters) -> Union[str, Generator[str, None, None]]:
        pass

    @abstractmethod
    def get_config(self):
        pass





def stream_openai_response_lines(response):
    for line in response.iter_lines():
        if line and line.startswith(b"data: "):
            json_part = line[len(b"data: "):]
            if json_part.strip() == b"[DONE]":
                break
            try:
                chunk = json.loads(json_part)
                delta = chunk.get("choices", [{}])[0].get("delta", {})
                content = delta.get("content", "")
                if content:
                    yield content
            except json.JSONDecodeError:
                continue