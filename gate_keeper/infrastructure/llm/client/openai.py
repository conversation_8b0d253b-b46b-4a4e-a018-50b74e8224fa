from gate_keeper.infrastructure.llm.client.base import (
    LLMClient, stream_openai_response_lines)
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters


class OpenAIClient(LLMClient):
    def __init__(self, api_key, base_url="<placeholder>"):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        super().__init__(base_url, headers=headers)
    
    def generate(self, prompt: str, parameters: LLMParameters):
        messages = []
        if parameters.system_prompt:
            messages.append({"role": "system", "content": parameters.system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": parameters.model_id,
            "messages": messages,
            "temperature": parameters.temperature,
            "max_tokens": parameters.max_tokens,
            "stream": parameters.stream,
            **parameters.extra  # 支持 stop, top_p 等
        }
        if parameters.stop:
            data["stop"] = parameters.stop
        if parameters.top_p is not None:
            data["top_p"] = parameters.top_p

        response = self.send_request("/chat/completions", method="POST", data=data, stream=parameters.stream)
        if parameters.stream:
            return stream_openai_response_lines(response)
        else:
            return response.json()["choices"][0]["message"]["content"]

    def get_config(self):
        return {"provider": "OpenAI"}
