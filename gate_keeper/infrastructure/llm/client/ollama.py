import json

from gate_keeper.config import config
from gate_keeper.infrastructure.llm.client.base import LL<PERSON>lient
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters
from gate_keeper.shared.log import app_logger as logger


class OllamaClient(LLMClient):
    def __init__(self, base_url):
        super().__init__(base_url)

    def generate(self, prompt, parameters: LLMParameters,token=config.ollama_token):
        print("[Ollama 调用] 正在请求大模型推理...", flush=True)
        if config.DEBUG:
            logger.debug(f"Prompt:===============\n {prompt}")
        data = {
            "model": parameters.model_id,
            "prompt": prompt,
            "temperature": parameters.temperature,
            "stream": parameters.stream,
            **parameters.extra
        }
        headers={
            "Authorization": f"Bearer {token}"
        }
        response = self.send_request("/api/generate", method="POST", data=data, stream=parameters.stream,headers=headers)
        if parameters.stream:
            return self.__stream_resp(response)
        else:
            res = self.__normal_resp(response)
            if config.DEBUG:
                logger.debug(f"Result:===============\n {res}")
            return res
    def __stream_resp(self,response):
            for line in response.iter_lines():
                if line:
                    try:
                        yield json.loads(line.decode()).get("response", "")
                    except json.JSONDecodeError:
                        continue        

    
    def __normal_resp(self,response):
        resp = response.json()
        return resp.get("response","")

    def get_config(self):
        return {"provider": "Ollama"}
