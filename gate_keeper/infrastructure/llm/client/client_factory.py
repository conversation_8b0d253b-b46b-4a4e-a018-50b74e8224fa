from gate_keeper.config import config
from gate_keeper.infrastructure.llm.client.base import LLMClient
# from gate_keeper.infrastructure.llm.client.fuyao import FUYAOClient
from gate_keeper.infrastructure.llm.client.ollama import OllamaClient
from gate_keeper.infrastructure.llm.client.openai import OpenAIClient


class LLMFactory:
    @staticmethod
    def create(provider: str, **kwargs) -> LLMClient:
        provider = provider.lower()
        if provider == "openai":
            return OpenAIClient(api_key=kwargs["api_key"])
        elif provider == "ollama":
            return OllamaClient(base_url=kwargs.get("base_url", config.ollama_endpoint))
        # elif provider == "fuyao":
        #     return FUYAOClient()
        else:
            raise ValueError(f"Unsupported provider: {provider}")
