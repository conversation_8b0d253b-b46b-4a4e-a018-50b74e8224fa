"""
Excel Reader

简单的 Excel 文件读取器，用于读取规则文件
"""

from typing import Any, Dict, List

import pandas as pd


class ExcelReader:
    """Excel 文件读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化 Excel 读取器
        
        Args:
            file_path: Excel 文件路径
        """
        self.file_path = file_path
    
    def get_sheet_names(self) -> List[str]:
        """
        获取所有工作表名称
        
        Returns:
            工作表名称列表
        """
        try:
            excel_file = pd.ExcelFile(self.file_path)
            return excel_file.sheet_names
        except Exception:
            return []
    
    def parse_group_by(self, sheet_name: str, merge_on: List[str] = None) -> List[Dict[str, Any]]:
        """
        解析工作表并按指定字段分组
        
        Args:
            sheet_name: 工作表名称
            merge_on: 合并字段列表
            
        Returns:
            分组后的数据列表
        """
        try:
            # 读取 Excel 工作表
            df = pd.read_excel(self.file_path, sheet_name=sheet_name)
            
            # 如果没有指定合并字段，返回所有行
            if not merge_on:
                return df.to_dict('records')
            
            # 按指定字段分组
            grouped_data = []
            for _, group in df.groupby(merge_on):
                # 将分组数据转换为字典
                group_dict = {}
                for col in df.columns:
                    if col in merge_on:
                        group_dict[col] = group[col].iloc[0]
                    else:
                        # 对于非分组字段，合并所有值
                        values = group[col].dropna().tolist()
                        group_dict[col] = values if len(values) > 1 else values[0] if values else None
                
                grouped_data.append(group_dict)
            
            return grouped_data
            
        except Exception as e:
            print(f"读取 Excel 文件失败: {e}")
            return [] 