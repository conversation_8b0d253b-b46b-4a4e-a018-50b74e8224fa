import json
import os
import re
import subprocess
from urllib.parse import urlparse

import requests


class BaseGitClient:
    def __init__(self, access_token: str, api_base: str):
        self.access_token = access_token
        self.api_base = api_base

    def _parse_repo_url(self, repo_url: str):
        """
        解析 repo_url，支持 SSH / HTTPS 格式，正确提取 owner（多级路径）和 repo 名
        返回 (owner, repo_name)
        
        支持的格式：
        - SSH: ****************:owner/repo.git
             ****************:group/subgroup/repo.git
        - HTTPS: https://platform.com/owner/repo
               https://platform.com/group/subgroup/repo
        """
        if repo_url.startswith("git@"):  # SSH格式
            pattern = r"git@(.+):(.+)\.git"
            match = re.match(pattern, repo_url)
            if not match:
                raise ValueError(f"无法解析 ssh 格式的 repo_url: {repo_url}")
            path = match.group(2)  # 例：group/subgroup/myrepo
            parts = path.strip("/").split("/")
            if len(parts) < 2:
                raise ValueError(f"路径层级不足: {repo_url}")
            owner = "/".join(parts[:-1])
            repo = parts[-1]
            return owner, repo
        else:  # HTTPS格式
            try:
                parsed = urlparse(repo_url)
                path = parsed.path.strip("/")
                parts = path.split("/")
                if len(parts) < 2:
                    raise ValueError(f"无法解析 https 格式的 repo_url: {repo_url}")
                owner = "/".join(parts[:-1])
                repo = parts[-1].replace(".git", "")
                return owner, repo
            except ValueError as e:
                raise ValueError(f"无法解析 https 格式的 repo_url: {repo_url}") from e

    def _get(self, url: str, params: dict = None, extra_headers: dict = None):
        params = params or {}
        headers = extra_headers or {}
        resp = requests.get(url, params=params, headers=headers,verify=False)
        if resp.status_code == 200:
            return resp.json()
        elif resp.status_code == 404:
            return None
        else:
            raise RuntimeError(f"Git API请求失败: {resp.status_code} {resp.text}")

    def _post(self, url: str, data: dict, params: dict = None, extra_headers: dict = None):
        headers = extra_headers or {}
        params = params or {}
        resp = requests.post(url, params=params, json=data, headers=headers,verify=False)
        if resp.status_code not in (200, 201):
            raise RuntimeError(f"Git API请求失败: {resp.status_code} {resp.text}")
        return resp.json()

    def _delete(self, url: str, params: dict = None, extra_headers: dict = None):
        params = params or {}
        headers = extra_headers or {}
        resp = requests.delete(url, params=params, headers=headers,verify=False)
        if resp.status_code in (200, 204):
            return True
        else:
            raise RuntimeError(f"Git API删除请求失败: {resp.status_code} {resp.text}")

    def _put(self, url: str, data: dict = None, params: dict = None, extra_headers: dict = None):
        headers = extra_headers or {}
        params = params or {}
        data = data or {}
        resp = requests.put(url, params=params, json=data, headers=headers,verify=False)
        if resp.status_code in (200, 201):
            return resp.json()
        else:
            raise RuntimeError(f"Git API PUT请求失败: {resp.status_code} {resp.text}")

    def _patch(self, url: str, data: dict = None, params: dict = None, extra_headers: dict = None):
        headers = extra_headers or {}
        params = params or {}
        data = data or {}
        resp = requests.patch(url, params=params, json=data, headers=headers,verify=False)
        if resp.status_code in (200, 201):
            return resp.json()
        else:
            raise RuntimeError(f"Git API PATCH请求失败: {resp.status_code} {resp.text}")