from typing import Optional, Union
from urllib.parse import urlparse

from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.config import config
from gate_keeper.infrastructure.git.codehub.client import CodeHub
from gate_keeper.infrastructure.git.gitee.client import Gitee
from gate_keeper.shared.log import app_logger as logger


class GitClientFactory:
    """Git客户端工厂类"""
    
    @staticmethod
    def create_comment_service(access_token: str = None) -> IGitPlatformService:
        """获取Git客户端"""
        token = access_token or config.token
        if config.git_platform == "codehub":
            return CodeHub(access_token=token)
        elif config.git_platform == "gitee":
            return Gitee(access_token=token)
        else:
            raise ValueError(f"不支持的Git类型: {config.git_platform}")