from typing import Any, Optional

from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from gate_keeper.domain.value_objects.git import CodeCheckDiscussion

from .model import GiteePRComment, GiteeUser


@dataclass
class GetPRCommentResp(GiteePRComment):
    pass

@dataclass
class EditPRCommentRequest:
    body: str

@dataclass
class EditPRCommentResponse(GiteePRComment):
    pass

@dataclass
class GiteeSubmitPRCommentRequest:
    body: str
    commit_id: Optional[str] = None
    path: Optional[str] = None
    position: Optional[int] = None


class GiteePRCommentResponse(BaseModel):
    url: Optional[str] = None
    id: Optional[int] = None
    path: Optional[str] = None
    position: Optional[str] = None
    original_position: Optional[str] = None
    new_line: Optional[str] = None
    commit_id: Optional[str] = None
    original_commit_id: Optional[str] = None
    user: Optional[Any] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    body: Optional[str] = None
    html_url: Optional[str] = None
    merge_request_url: Optional[str] = None
    _links: Optional[Any] = None
    comment_type: Optional[str] = None
    in_reply_to_id: Optional[int] = None

    def to_code_check_discussion(self)->CodeCheckDiscussion:
        return CodeCheckDiscussion(
            id=str(self.id),
            body=self.body,
            mr_id=str(self.id),
            project_id=None,
            commit_id=self.commit_id,
            checked_code=None,
            resolved=None,
        )


class MRDiscussionQuery(BaseModel):
    page: int = 1  # 当前页码
    per_page: int = 20  # 每页数量，最大100
    direction: Optional[str] = None  # 升序/降序
    comment_type: Optional[str] = None  # 评论类型（如'code'/'MR'等）