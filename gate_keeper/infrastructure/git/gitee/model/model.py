from typing import Optional

from pydantic import BaseModel
from pydantic.dataclasses import dataclass


@dataclass
class GiteeUser:
    id: int
    login: str
    name: str
    avatar_url: str
    url: str
    html_url: str
    remark: Optional[str]
    followers_url: str
    following_url: str
    gists_url: str
    starred_url: str
    subscriptions_url: str
    organizations_url: str
    repos_url: str
    events_url: str
    received_events_url: str
    type: str
    member_role: str

@dataclass
class GiteePRComment:
    _links: str  # 该字段的结构未提供，若为 dict，可进一步定义
    body: str
    comment_type: str
    commit_id: str
    created_at: str
    html_url: str
    id: int
    in_reply_to_id: Optional[int]
    new_line: Optional[str]
    original_commit_id: str
    original_position: Optional[str]
    path: Optional[str]
    position: Optional[str]
    merge_request_url: str
    updated_at: str
    url: str
    user: GiteeUser



