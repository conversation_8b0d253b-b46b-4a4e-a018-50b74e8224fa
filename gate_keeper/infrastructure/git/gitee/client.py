import re
from typing import List
from urllib.parse import urlparse

import requests

from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.domain.value_objects.git import (CodeCheckDiscussion,
                                                  MergeRequest)
from gate_keeper.infrastructure.git.base_client import BaseGitClient
from gate_keeper.infrastructure.git.gitee.model.dto import (
    GiteePRCommentResponse, MRDiscussionQuery)
from gate_keeper.shared.log import app_logger as logger


class Gitee(BaseGitClient, IGitPlatformService):
    def __init__(self, access_token: str):
        super().__init__(access_token=access_token, api_base="https://gitee.com/api/v5")
    def _get_auth_header(self):
        return {"Authorization": f"token {self.access_token}"}
    
    def _normalize_project_id(self, project_id: str):
        """
        支持 project_id 既可为 owner/repo，也可为 repo_url，自动转换为 owner/repo，兼容多级repo路径
        """
        if "gitee.com" in project_id or project_id.startswith("git@"):
            # 解析repo_url，支持多级repo
            m = re.match(r"(?:https?://gitee.com/|*************:)([^/]+)/(.+?)(?:\.git)?$", project_id)
            if m:
                return f"{m.group(1)}/{m.group(2)}"
            else:
                raise ValueError(f"无法从repo_url解析owner/repo: {project_id}")
        return project_id

    def get_mr_info(self, project_id: str, mr_id: str):
        try:
            owner_repo = self._normalize_project_id(project_id)
            owner, repo = owner_repo.split("/")
            url = f"{self.api_base}/repos/{owner}/{repo}/pulls/{mr_id}"
            resp = self._get(url, extra_headers=self._get_auth_header())
            if not resp:
                return None
            print(resp)
            # 字段映射到通用MergeRequest
            return MergeRequest(
                id=str(resp.get('number', '')),
                title=resp.get('title', ''),
                state=resp.get('state', ''),
                source_branch=resp.get('head', {}).get('ref', ''),
                target_branch=resp.get('base', {}).get('ref', ''),
                author=resp.get('user', {}).get('login', '') or resp.get('user', {}).get('name', ''),
                created_at=resp.get('created_at'),
                updated_at=resp.get('updated_at'),
                web_url=resp.get('html_url', '')
            )
        except Exception as e:
            # 网络异常、API异常等直接返回None
            return None


    def get_discussions_of_mr(self, project_id: str, mr_id: str)->List[CodeCheckDiscussion]:
        discussion_qeury = MRDiscussionQuery(page=1, per_page=100, direction=None, comment_type=None)
        discussions = self.__get_discussions_of_mr(project_id, mr_id, discussion_qeury)
        return [item.to_code_check_discussion() for item in discussions]

    def __get_discussions_of_mr(self, project_id: str, mr_id: str, discussion_qeury: MRDiscussionQuery)->List[GiteePRCommentResponse]:
        owner_repo = self._normalize_project_id(project_id)
        owner, repo = owner_repo.split("/")
        url = f"{self.api_base}/repos/{owner}/{repo}/pulls/{mr_id}/comments"
        query_params = discussion_qeury.model_dump() if discussion_qeury else {}
        response = self._get(url, params=query_params, extra_headers=self._get_auth_header()) or []
        discussions = [GiteePRCommentResponse.model_validate(item) for item in response]
        return discussions

    def post_discussion_to_mr(self, project_id: str, mr_id: str, discussion_content: str):
        try:
            owner_repo = self._normalize_project_id(project_id)
            owner, repo = owner_repo.split("/")
            url = f"{self.api_base}/repos/{owner}/{repo}/pulls/{mr_id}/comments"
            
            if discussion_content.strip():
                discussion_content = f"""- [ ] 解决\n{discussion_content.strip()}"""
            return self._post(url, {"body": discussion_content}, extra_headers=self._get_auth_header())
        except Exception as e:
            logger.error(e)
            # 捕获网络异常、API异常等，返回False
            return False

    def update_discussion_to_mr(self, project_id: str, mr_id: str, discussion_id: str, discussion_data):
        """
        更新MR评论，支持修改markdown checkbox状态
        
        Args:
            project_id: 项目ID
            mr_id: MR ID  
            discussion_id: 讨论ID
            discussion_data: 要更新的数据，目前支持 {"resolved": true/false}
            
        Returns:
            bool: 更新是否成功
        """
        try:
            owner_repo = self._normalize_project_id(project_id)
            owner, repo = owner_repo.split("/")
            
            # 先获取当前评论内容
            get_url = f"{self.api_base}/repos/{owner}/{repo}/pulls/comments/{discussion_id}"
            current_comment = self._get(get_url, extra_headers=self._get_auth_header())
            
            if not current_comment:
                logger.error(f"无法获取评论内容: discussion_id={discussion_id}")
                return False
            
            current_body = current_comment.get('body', '')
            resolved = discussion_data.get('resolved', False)
            
            # 解析并修改markdown checkbox状态
            updated_body = self._update_checkbox_status(current_body, resolved)
            
            # 更新评论
            update_url = f"{self.api_base}/repos/{owner}/{repo}/pulls/comments/{discussion_id}"
            data = {"body": updated_body}
            
            result = self._patch(update_url, data=data, extra_headers=self._get_auth_header())
            if result:
                logger.info(f"成功更新评论: discussion_id={discussion_id}, resolved={resolved}")
                return True
            else:
                logger.error(f"更新评论失败: discussion_id={discussion_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新评论异常: project_id={project_id}, mr_id={mr_id}, discussion_id={discussion_id}, error={e}")
            return False
    
    def _update_checkbox_status(self, body: str, resolved: bool) -> str:
        """
        更新markdown checkbox状态
        
        Args:
            body: 原始评论内容
            resolved: 是否解决
            
        Returns:
            str: 更新后的评论内容
        """
        import re

        # 匹配markdown checkbox模式，支持缩进
        checkbox_pattern = r'^\s*-\s*\[([ x])\]\s*解决\s*$'
        
        lines = body.split('\n')
        updated_lines = []
        checkbox_found = False
        
        for line in lines:
            match = re.match(checkbox_pattern, line)
            if match:
                # 找到checkbox，更新状态
                checkbox_found = True
                new_status = 'x' if resolved else ' '
                # 保持原有的缩进
                indent = line[:line.find('-')]
                updated_lines.append(f"{indent}- [{new_status}] 解决")
            else:
                updated_lines.append(line)
        
        # 如果没有找到checkbox，在末尾添加
        if not checkbox_found:
            new_status = 'x' if resolved else ' '
            updated_lines.append(f"- [{new_status}] 解决")
        
        result = '\n'.join(updated_lines)
        # 如果原始内容为空，直接返回checkbox，不要换行符
        if not body.strip():
            # 如果body全是空白但有换行符，保留换行符
            if body.count('\n') > 0:
                return body + f"- [{new_status}] 解决"
            return f"- [{new_status}] 解决"
        return result

    def delete_discussion_of_mr(self, project_id: str, mr_id: str, comment_id: int):
        """
        删除MR评论，兼容新版接口
        """
        owner_repo = self._normalize_project_id(project_id)
        owner, repo = owner_repo.split("/", 1)
        url = f"{self.api_base}/repos/{owner}/{repo}/pulls/comments/{comment_id}"
        deleted = self._delete(url, extra_headers=self._get_auth_header())
        return deleted