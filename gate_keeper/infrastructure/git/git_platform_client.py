import json
import os
import re
import subprocess
from urllib.parse import urlparse

import requests

from gate_keeper.application.interfaces.git_intf import IGitPlatformService


class GitPlatformClient(IGitPlatformService):
    """Git平台客户端 - 与Git平台API交互的基础实现"""
    
    def __init__(self, access_token: str, api_base: str):
        self.access_token = access_token
        self.api_base = api_base

    def _parse_repo_url(self, repo_url: str):
        """
        解析 repo_url，支持 SSH / HTTPS 格式，正确提取 owner（多级路径）和 repo 名
        返回 (owner, repo_name)
        
        支持的格式：
        - SSH: ****************:owner/repo.git
             ****************:group/subgroup/repo.git
        - HTTPS: https://platform.com/owner/repo
               https://platform.com/group/subgroup/repo
        """
        if repo_url.startswith("git@"):  # SSH格式
            pattern = r"git@(.+):(.+)\.git"
            match = re.match(pattern, repo_url)
            if not match:
                raise ValueError(f"无法解析 ssh 格式的 repo_url: {repo_url}")
            path = match.group(2)  # 例：group/subgroup/myrepo
            parts = path.strip("/").split("/")
            if len(parts) < 2:
                raise ValueError(f"路径层级不足: {repo_url}")
            owner = "/".join(parts[:-1])
            repo = parts[-1]
            return owner, repo
        else:  # HTTPS格式
            try:
                parsed = urlparse(repo_url)
                path = parsed.path.strip("/")
                parts = path.split("/")
                if len(parts) < 2:
                    raise ValueError(f"无法解析 https 格式的 repo_url: {repo_url}")
                owner = "/".join(parts[:-1])
                repo = parts[-1].replace(".git", "")
                return owner, repo
            except ValueError as e:
                raise ValueError(f"无法解析 https 格式的 repo_url: {repo_url}") from e

    def _get(self, url: str, params: dict = None, extra_headers: dict = None):
        """GET请求 - 平台API调用"""
        params = params or {}
        headers = extra_headers or {}
        resp = requests.get(url, params=params, headers=headers,verify=False)
        if resp.status_code == 200:
            return resp.json()
        elif resp.status_code == 404:
            return None
        else:
            raise RuntimeError(f"Git API请求失败: {resp.status_code} {resp.text}")

    def _post(self, url: str, data: dict, params: dict = None, extra_headers: dict = None):
        """POST请求 - 平台API调用"""
        headers = extra_headers or {}
        params = params or {}
        resp = requests.post(url, params=params, json=data, headers=headers,verify=False)
        if resp.status_code not in (200, 201):
            raise RuntimeError(f"Git API请求失败: {resp.status_code} {resp.text}")
        return resp.json()

    def _delete(self, url: str, params: dict = None, extra_headers: dict = None):
        """DELETE请求 - 平台API调用"""
        params = params or {}
        headers = extra_headers or {}
        resp = requests.delete(url, params=params, headers=headers,verify=False)
        if resp.status_code in (200, 204):
            return True
        else:
            raise RuntimeError(f"Git API删除请求失败: {resp.status_code} {resp.text}")

    def _put(self, url: str, data: dict = None, params: dict = None, extra_headers: dict = None):
        """PUT请求 - 平台API调用"""
        headers = extra_headers or {}
        params = params or {}
        data = data or {}
        resp = requests.put(url, params=params, json=data, headers=headers,verify=False)
        if resp.status_code in (200, 201):
            return resp.json()
        else:
            raise RuntimeError(f"Git API PUT请求失败: {resp.status_code} {resp.text}")

    # IGitPlatformService 接口实现
    def get_mr_info(self, project_id: str, mr_id: str) -> dict:
        """获取MR信息 - 平台API调用"""
        raise NotImplementedError("子类必须实现此方法")

    def get_discussions_of_mr(self, project_id: str, mr_id: str) -> list:
        """获取MR上的讨论 - 平台API调用"""
        raise NotImplementedError("子类必须实现此方法")

    def post_discussion_to_mr(self, project_id: str, mr_id: str, discussion_content: str):
        """在MR上添加讨论 - 平台API调用"""
        raise NotImplementedError("子类必须实现此方法")

    def update_discussion_to_mr(self, project_id: str, mr_id: str, discussion_id: str, discussion_data):
        """更新MR上的讨论 - 平台API调用"""
        raise NotImplementedError("子类必须实现此方法")
    
    def delete_discussion_of_mr(self, project_id: str, mr_id: str, discussion_id: str):
        """删除MR上的讨论 - 平台API调用"""
        raise NotImplementedError("子类必须实现此方法") 