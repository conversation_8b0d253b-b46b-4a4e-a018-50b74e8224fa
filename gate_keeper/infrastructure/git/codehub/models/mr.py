from dataclasses import field
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from gate_keeper.infrastructure.git.codehub.models.user import User


@dataclass
class Project:
    """CodeHub项目信息"""
    id: int
    name: str
    path_with_namespace: str
    http_url_to_repo: str
    web_url: str



@dataclass
class Reviewer:
    """MR评审人信息"""
    id: int
    username: str
    name: str
    name_cn: str
    email: str
    state: str
    avatar_url: str

@dataclass
class TimeStats:
    """MR时间统计"""
    time_estimate: Optional[int]
    total_time_spent: Optional[int]
    human_time_estimate: Optional[str]
    human_total_time_spent: Optional[str]

@dataclass
class Pipeline:
    """CI流水线信息"""
    id: int
    sha: str
    ref: str 
    status: str
    web_url: str

@dataclass
class DiffRefs:
    """MR差异引用"""
    base_sha: str
    head_sha: str
    start_sha: str

class MergeRequest(BaseModel):
    """CodeHub合并请求模型"""
    id: int
    iid: int
    project_id: int
    title: str
    description: str
    state: str
    created_at: datetime 
    updated_at: datetime
    merged_by: Optional[User] 
    merged_at: Optional[datetime]
    closed_by: Optional[User]
    closed_at: Optional[datetime]
    target_branch: str
    source_branch: str
    user_notes_count: int
    upvotes: int
    downvotes: int
    author: User
    assignee: Optional[User]
    source_project_id: int
    target_project_id: int
    labels: List[str]
    work_in_progress: bool
    merge_status: str
    sha: str
    merge_commit_sha: Optional[str]
    web_url: str
    time_stats: TimeStats
    squash: bool
    approval_merge_request_reviewers: List[Reviewer] = field(default_factory=list)
    approval_merge_request_approvers: List[Reviewer] = field(default_factory=list)
    source_project: Optional[Project] = None
    target_project: Optional[Project] = None
    pipeline: Optional[Pipeline] = None
    diff_refs: Optional[DiffRefs] = None
    added_lines: Optional[int] = None
    removed_lines: Optional[int] = None
    changes_count: str = "0"
    pipeline_status: str = ""
    codequality_status: str = ""
    from_forked_project: bool = False

    @classmethod
    def from_api_response(cls, data: dict) -> 'MergeRequest':
        """从API响应构造MergeRequest对象"""
        return cls(
            id=data['id'],
            iid=data['iid'],
            project_id=data['project_id'],
            title=data['title'],
            description=data['description'],
            state=data['state'],
            created_at=datetime.fromisoformat(data['created_at']),
            updated_at=datetime.fromisoformat(data['updated_at']),
            merged_by=User(**data['merged_by']) if data.get('merged_by') else None,
            merged_at=datetime.fromisoformat(data['merged_at']) if data.get('merged_at') else None,
            closed_by=User(**data['closed_by']) if data.get('closed_by') else None,
            closed_at=datetime.fromisoformat(data['closed_at']) if data.get('closed_at') else None,
            target_branch=data['target_branch'],
            source_branch=data['source_branch'],
            user_notes_count=data.get('user_notes_count', 0),
            upvotes=data.get('upvotes', 0),
            downvotes=data.get('downvotes', 0),
            author=User(**data['author']),
            assignee=User(**data['assignee']) if data.get('assignee') else None,
            source_project_id=data['source_project_id'],
            target_project_id=data['target_project_id'],
            labels=data.get('labels', []),
            work_in_progress=data.get('work_in_progress', False),
            merge_status=data['merge_status'],
            sha=data['sha'],
            merge_commit_sha=data.get('merge_commit_sha'),
            web_url=data['web_url'],
            time_stats=TimeStats(**data['time_stats']) if 'time_stats' in data else TimeStats(None, None, None, None),
            squash=data.get('squash', False),
            approval_merge_request_reviewers=[
                Reviewer(**r) for r in data.get('approval_merge_request_reviewers', [])
            ],
            approval_merge_request_approvers=[
                Reviewer(**r) for r in data.get('approval_merge_request_approvers', [])
            ],
            source_project=Project(**data['source_project']) if 'source_project' in data else None,
            target_project=Project(**data['target_project']) if 'target_project' in data else None,
            pipeline=Pipeline(**data['pipeline']) if data.get('pipeline') else None,
            diff_refs=DiffRefs(**data['diff_refs']) if 'diff_refs' in data else None,
            added_lines=data.get('added_lines'),
            removed_lines=data.get('removed_lines'),
            changes_count=str(data.get('changes_count', '0')),
            pipeline_status=data.get('pipeline_status', ''),
            codequality_status=data.get('codequality_status', ''),
            from_forked_project=data.get('from_forked_project', False)
        )
