from typing import Any, Dict, List, Optional

from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from gate_keeper.infrastructure.git.codehub.models.discussion import \
    DiscussionNote
from gate_keeper.infrastructure.git.codehub.models.user import User


class UpdateDiscussionNoteRequest(BaseModel):
    id: Optional[str] = None
    merge_request_iid: Optional[str] = None
    discussion_id: Optional[str] = None
    body: Optional[str] = None
    severity: Optional[str] = None
    assignee_id: Optional[str] = None
    review_categories: Optional[str] = None
    review_modules: Optional[str] = None
    issue_id: Optional[int] = None
    proposer_id: Optional[str] = None
    resolved: Optional[bool] = None 

class CreateMRDiscussionResp(BaseModel):
    """创建MR讨论的响应模型"""
    id: str
    individual_note: bool
    notes: List[DiscussionNote]  
    project_id: int
    noteable_type: str
    commit_id: Optional[str]
    project_full_path: str
    a_mode: Optional[str]
    b_mode: Optional[str]
    deleted_file: Optional[bool]
    new_file: Optional[bool]
    renamed_file: Optional[bool]
    resolved: bool
    archived: bool
    review_categories: str
    review_categories_cn: str
    review_categories_en: str
    review_modules: Optional[str]
    severity: str
    severity_cn: str
    severity_en: str
    assignee: User  
    proposer: User
    issue: Optional[Any]
    merge_request_version_params: Optional[Any]
    diff_file: Optional[Any]
    added_lines: Optional[List[int]]
    removed_lines: Optional[List[int]]


