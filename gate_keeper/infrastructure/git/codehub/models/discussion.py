from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel
from pydantic.dataclasses import dataclass

from gate_keeper.infrastructure.git.codehub.models.user import User


class DiscussionNote(BaseModel):
    """讨论中的单个评论note"""
    id: int
    body: str
    author: User
    created_at: str
    updated_at: str
    noteable_id: int
    noteable_type: str
    review_categories: str
    severity: str
    assignee: Optional[User] = None
    file_path: Optional[str] = None
    line: Optional[int] = None

class Discussion(BaseModel):
    """CodeHub合并请求的完整讨论"""
    id: str
    notes: List[DiscussionNote]
    project_id: int
    noteable_type: str
    project_full_path: str
    review_categories: str
    severity: str
    assignee: Optional[User] = None
    resolved: bool = False

    @classmethod
    def from_api_response(cls, data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> List['Discussion']:
        """从API响应构建Discussion对象，支持单个对象和列表"""
        if isinstance(data, list):
            return [cls._parse_single_discussion(item) for item in data]
        return [cls._parse_single_discussion(data)]

    @classmethod
    def _parse_single_discussion(cls, data: Dict[str, Any]) -> 'Discussion':
        """解析单个Discussion对象"""
        notes = [
            DiscussionNote(
                id=note['id'],
                body=note['body'],
                author=User(**note['author']),
                created_at=note['created_at'],
                updated_at=note['updated_at'],
                noteable_id=note['noteable_id'],
                noteable_type=note['noteable_type'],
                review_categories=note['review_categories'],
                severity=note['severity'],
                assignee=User(**note['assignee']) if note.get('assignee') else None,
                file_path=note.get('file_path'),
                line=note.get('line')
            )
            for note in data['notes']
        ]
        
        return cls(
            id=data['id'],
            notes=notes,
            project_id=data['project_id'],
            noteable_type=data['noteable_type'],
            project_full_path=data['project_full_path'],
            review_categories=data['review_categories'],
            severity=data['severity'],
            assignee=User(**data['assignee']) if data.get('assignee') else None,
            resolved=data.get('resolved', False)
        )
