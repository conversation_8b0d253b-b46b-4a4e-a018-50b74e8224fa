from typing import List, Optional

from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.domain.value_objects.git import (CodeCheckDiscussion,
                                                  MergeRequest)
from gate_keeper.infrastructure.git.base_client import BaseGitClient
from gate_keeper.infrastructure.git.codehub.models.discussion import Discussion
from gate_keeper.infrastructure.git.codehub.models.dto.discussion import \
    CreateMRDiscussionResp
from gate_keeper.infrastructure.git.codehub.models.mr import \
    MergeRequest as MergeRequestModel
from gate_keeper.shared.log import app_logger as logger


class CodeHub(BaseGitClient, IGitPlatformService):
    def __init__(self, access_token: str):
        super().__init__(access_token, api_base="<internal_codehub_url>")

    def _auth_headers(self) -> dict:
        return {"PRIVATE-TOKEN": self.access_token}

    def get_mr_info(self, project_id: str, mr_id: str) -> MergeRequest:
        """获取 MR 信息，返回通用领域对象"""
        url = f"{self.api_base}/projects/{project_id}/merge_requests/{mr_id}"
        resp = self._get(url, extra_headers=self._auth_headers())
        mr = MergeRequestModel.model_validate(resp)
        return MergeRequest(
            id=str(mr.id),
            title=mr.title,
            state=mr.state,
            source_branch=mr.source_branch,
            target_branch=mr.target_branch,
            author=getattr(mr.author, 'username', None) or getattr(mr.author, 'name', None) or '',
            created_at=mr.created_at,
            updated_at=mr.updated_at,
            web_url=mr.web_url
        )

    def post_discussion_to_mr(
        self, project_id: int, mr_id: int, comment: str
    ) -> Optional[CreateMRDiscussionResp]:
        """向 MR 发布新的讨论"""
        if not comment:
            return None

        data = {
            "body": comment,
            "severity": "suggestion",
            "review_categories": "其他问题(Other)"
        }

        url = f"{self.api_base}/projects/{project_id}/merge_requests/{mr_id}/discussions"
        resp = self._post(url, data=data, extra_headers=self._auth_headers())
        return CreateMRDiscussionResp.model_validate(resp)

    def get_discussions_of_mr(self, project_id: str, mr_id: int) -> List[CodeCheckDiscussion]:
        """获取 MR 上的讨论并转换为 CodeCheckDiscussion 对象"""
        raw_discussions = self._get_raw_discussions(project_id, mr_id)
        discussions = []

        for d in raw_discussions:
            latest_note = d.notes[-1] if d.notes else None
            checked_code = (
                f"{latest_note.file_path}:{latest_note.line}"
                if latest_note and latest_note.file_path and latest_note.line
                else ""
            )
            discussions.append(CodeCheckDiscussion(
                id=d.id,
                body=latest_note.body if latest_note else "",
                mr_id=str(mr_id),
                project_id=str(d.project_id),
                commit_id="",  # 暂不提供
                checked_code=checked_code,
                resolved=d.resolved
            ))

        return discussions

    def _get_raw_discussions(self, project_id: str, mr_id: int) -> List[Discussion]:
        url = f"{self.api_base}/projects/{project_id}/merge_requests/{mr_id}/discussions"
        resp = self._get(url, extra_headers=self._auth_headers())
        return Discussion.from_api_response(resp)

    def update_discussion_to_mr(
        self, project_id: str, mr_id: str, discussion_id: str, discussion_data
    ) -> Optional[Discussion]:
        """
        更新某个讨论的状态（如resolved状态）
        
        Args:
            project_id: 项目ID
            mr_id: MR ID
            discussion_id: 讨论ID
            discussion_data: 要更新的数据，支持dict或UpdateDiscussionNoteRequest对象
            
        Returns:
            Optional[Discussion]: 更新后的讨论对象，失败时返回None
        """
        try:
            # 构建更新数据
            update_data = {}
            
            # 处理不同类型的输入
            if hasattr(discussion_data, 'model_dump'):
                # Pydantic模型对象
                data_dict = discussion_data.model_dump()
            elif isinstance(discussion_data, dict):
                # 字典对象
                data_dict = discussion_data
            else:
                # 其他类型，尝试转换为字典
                data_dict = vars(discussion_data) if hasattr(discussion_data, '__dict__') else {}
            
            # 映射字段名
            if "resolved" in data_dict:
                update_data["resolved"] = data_dict["resolved"]
            elif "resolved" in data_dict:
                update_data["resolved"] = data_dict["resolved"]
            
            # 如果没有需要更新的数据，直接返回
            if not update_data:
                return None
                
            url = f"{self.api_base}/projects/{project_id}/merge_requests/{mr_id}/discussions/{discussion_id}"
            resp = self._put(url, data=update_data, extra_headers=self._auth_headers())
            
            if resp:
                return Discussion.from_api_response(resp)
            return None
            
        except Exception as e:
            logger.error(f"更新CodeHub讨论失败: project_id={project_id}, mr_id={mr_id}, discussion_id={discussion_id}, error={e}")
            return None

    def delete_discussion_of_mr(self, project_id: str, mr_id: int, discussion_id: str) -> bool:
        """删除 MR 中指定讨论的所有 note"""
        discussions = self._get_raw_discussions(project_id, mr_id)
        discussion = next((d for d in discussions if d.id == discussion_id), None)

        if not discussion:
            return True

        for note in discussion.notes:
            url = f"{self.api_base}/projects/{project_id}/merge_requests/{mr_id}/discussions/{discussion_id}/notes/{note.id}"
            if not self._delete(url, extra_headers=self._auth_headers()):
                raise RuntimeError(f"删除 MR 讨论失败: discussion_id={discussion_id}, note_id={note.id}")
        
        return True
