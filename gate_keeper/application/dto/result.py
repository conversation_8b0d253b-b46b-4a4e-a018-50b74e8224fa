# gate_keeper.application.dto.result
from dataclasses import field
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from gate_keeper.external.code_analyzer.models.call_relation import FunctionCall
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.external.code_analyzer.models.function import Function
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult


class AffectedFunction(CodeElement):
    name: str
    type: str = "function"
    start_line: int
    end_line: int
    changed_lines: List[int]
    code: str
    filepath:str
    related_calls: List[FunctionCall] = field(default_factory=list)
    related_definitions:List[Function] = field(default_factory=list)
    call_chains:List[List[str]]=field(default_factory=list)
    llm_results: List[AnalyzeLLMResult] = field(default_factory=list)


class DiffResult(BaseModel):
    filepath: str
    origin_file_content: str
    new_file_content: str
    affected_functions: List[AffectedFunction]
    ungrouped_lines: Optional[List[int]] = field(default_factory=list)
    file_status:str # 'added'|'deleted'|'modified'|'unchanged'




class CheckMRResult(BaseModel):
    mr_id: int
    base_branch: str
    dev_branch: str
    diffs: List[DiffResult]