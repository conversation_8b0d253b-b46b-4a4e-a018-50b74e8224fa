import os
from abc import ABC, abstractmethod

from gate_keeper.external.code_analyzer.models.function import Function


class IASTparser(ABC):
    model_config = {"arbitrary_types_allowed": True}

    @abstractmethod
    def extract_functions(self,filepath: str,file_content:str=None) -> list[Function]:
        pass 

    @abstractmethod
    def find_parent_element(self, filepath: str, start_line: int, end_line: int, file_content=None):
        pass 

    @abstractmethod
    def extract_calls(self, filepath: str,file_content:str=None)->list:
        pass
