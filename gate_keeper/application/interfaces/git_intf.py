from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional

from gate_keeper.domain.value_objects.git import CodeCheckDiscussion


class ICodeRepositoryService(ABC):
    """代码仓库操作服务接口 - 业务逻辑导向"""
    @abstractmethod
    def clone(self, repo_url: str, local_dir: Optional[str] = None, branch: Optional[str] = None) -> str:
        pass
    @abstractmethod
    def get_file_content_by_ref(self, repo_dir: str, ref_or_sha: str, file_path_in_repo: str) -> str:
        pass
    @abstractmethod
    def get_diff_by_mr_id(self, repo_path: str, mr_id: int, base_branch: str, dev_branch: str, base_commit_sha: str = None, dev_commit_sha: str = None) -> Any:
        pass
    @abstractmethod
    def keep_branch_update_if_needed(self, repo_dir: str, branch: str, remote: str = "origin") -> bool:
        pass
    @abstractmethod
    def get_remote_latest_branch_commit_sha(self, repo_dir: str, branch: str, remote: str) -> str:
        pass
    @abstractmethod
    def list_repo_files(self, repo_path: str, ref_or_sha: str) -> List[str]:
        pass

class IGitPlatformService(ABC):
    """Git平台API服务接口 - 平台API调用"""
    @abstractmethod
    def get_mr_info(self, project_id: str, mr_id: str) -> Optional[dict]:
        pass
    @abstractmethod
    def get_discussions_of_mr(self, project_id: str, mr_id: str) -> List[CodeCheckDiscussion]:
        pass
    @abstractmethod
    def post_discussion_to_mr(self, project_id: str, mr_id: str, discussion_content: str):
        pass
    @abstractmethod
    def update_discussion_to_mr(self, project_id: str, mr_id: str, discussion_id: str, discussion_data):
        pass
    @abstractmethod
    def delete_discussion_of_mr(self, project_id: str, mr_id: str, discussion_id: str):
        pass


