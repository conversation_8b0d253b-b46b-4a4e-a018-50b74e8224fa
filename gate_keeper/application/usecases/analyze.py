# gate_keeper/application/usecases/analyze.py
from typing import Dict

from gate_keeper.application.dto.result import Check<PERSON><PERSON><PERSON><PERSON>, DiffResult
from gate_keeper.application.interfaces.git_intf import ICodeRepositoryService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.git import CodeRepositoryService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.config import config
from gate_keeper.shared.file import determine_language_by_filename
from gate_keeper.shared.log import app_logger as logger


class AnalyzeBaseUseCase:
    def __init__(self, git_service, llm_service):
        self.git_service:ICodeRepositoryService = git_service
        self.llm_service:LLMService = llm_service

    def analyze_mr(self, check_mr_result):
        check_mr_result = self.llm_service.analyze_mr(check_mr_result)
        return check_mr_result



    
class AnalyzeMRUseCase(AnalyzeBaseUseCase):
    def __init__(self, git_service: ICodeRepositoryService, llm_service: LLMService, repo_analyzer: RepositoryAnalyzer):
        super().__init__(git_service, llm_service)
        self.repo_analyzer = repo_analyzer

    def execute(self, repo_dir: str, mr_id: int, base_branch: str, dev_branch: str,max_context_chain_depth: int = None,base_commit_sha:str=None,dev_commit_sha:str=None,remote="origin",exclude_patterns: list = None, max_diff_results: int = config.max_diff_results):
        # 使用配置中的默认值
        if max_context_chain_depth is None:
            max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
        
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        self.git_service.keep_branch_update_if_needed(repo_dir, base_branch)
        self.git_service.keep_branch_update_if_needed(repo_dir, dev_branch)

        if not base_commit_sha:
            base_commit_sha=self.git_service.get_remote_latest_branch_commit_sha(repo_dir,base_branch,remote)
        
        if not dev_commit_sha:
            dev_commit_sha=self.git_service.get_remote_latest_branch_commit_sha(repo_dir,dev_branch,remote)
        
        diffs = self.git_service.get_diff_by_mr_id(repo_dir, mr_id, base_branch, dev_branch,base_commit_sha,dev_commit_sha)

        diff_results = []

        # 添加文件处理进度条
        try:
            from tqdm import tqdm
            progress_bar = tqdm(total=len(diffs), desc="📁 处理变更文件", unit="文件")
        except ImportError:
            progress_bar = None
            logger.warning("tqdm未安装，无法显示进度条")

        try:
            for patched_file in diffs:
                if determine_language_by_filename(patched_file.path):
                    diff_result = self._process_file_change(repo_dir, base_branch, dev_branch, base_commit_sha,dev_commit_sha,patched_file,exclude_patterns, max_context_chain_depth)
                    diff_results.append(diff_result)

                    # 更新进度条
                    if progress_bar:
                        progress_bar.set_postfix({
                            '当前文件': patched_file.path.split('/')[-1][:20],
                            '已处理': len(diff_results)
                        })
                        progress_bar.update(1)
                else:
                    # 跳过不支持的文件类型
                    if progress_bar:
                        progress_bar.set_postfix({
                            '当前文件': f"跳过 {patched_file.path.split('/')[-1][:20]}",
                            '已处理': len(diff_results)
                        })
                        progress_bar.update(1)
        finally:
            # 确保进度条被关闭
            if progress_bar:
                progress_bar.close()
        
        # 应用智能的最大diff结果数限制
        yaml_evaluation_mode = getattr(config, 'yaml_evaluation_mode', False)
        if not yaml_evaluation_mode and max_diff_results is not None and max_diff_results > 0:
            diff_results = self._apply_smart_diff_limit(diff_results, max_diff_results)
        elif yaml_evaluation_mode:
            print(f"🔧 评估模式：跳过max_diff_results限制，保持所有 {len(diff_results)} 个文件")

        check_mr_result = CheckMRResult(
            mr_id=mr_id,
            base_branch=base_branch,
            dev_branch=dev_branch,
            diffs=diff_results
        )
        
        return self.analyze_mr(check_mr_result)

    def _apply_smart_diff_limit(self, diff_results: list, max_diff_results: int) -> list:
        """
        智能应用diff结果数量限制

        策略：
        1. 优先保留有函数的文件（源文件）
        2. 按函数数量排序，优先保留函数多的文件
        3. 保留重要的文件类型（.c > .h > 其他）
        4. 在同等条件下，保留变更行数多的文件

        Args:
            diff_results: 原始diff结果列表
            max_diff_results: 最大保留数量

        Returns:
            经过智能筛选的diff结果列表
        """
        if len(diff_results) <= max_diff_results:
            return diff_results

        logger.info(f"智能diff限制: 从 {len(diff_results)} 个文件中选择 {max_diff_results} 个")

        # 1. 按重要性分类和评分
        scored_diffs = []
        for diff in diff_results:
            score = self._calculate_diff_importance_score(diff)
            scored_diffs.append((score, diff))

        # 2. 按分数排序（降序）
        scored_diffs.sort(key=lambda x: x[0], reverse=True)

        # 3. 选择前N个
        selected_diffs = [diff for _, diff in scored_diffs[:max_diff_results]]

        # 4. 输出选择结果
        logger.info(f"智能选择结果:")
        for i, (score, diff) in enumerate(scored_diffs[:max_diff_results]):
            logger.info(f"  {i+1}. {diff.filepath} (分数: {score:.1f}, 函数: {len(diff.affected_functions)})")

        if len(scored_diffs) > max_diff_results:
            excluded_count = len(scored_diffs) - max_diff_results
            logger.info(f"排除了 {excluded_count} 个文件（函数数量较少或重要性较低）")

        return selected_diffs

    def _calculate_diff_importance_score(self, diff) -> float:
        """
        计算diff的重要性分数

        评分标准：
        - 函数数量：每个函数 +10分
        - 文件类型：.c文件 +50分，.h文件 +10分，其他 +0分
        - 变更行数：每行 +0.1分
        - 文件状态：modified +5分，added +3分，deleted +1分

        Args:
            diff: DiffResult对象

        Returns:
            重要性分数
        """
        score = 0.0

        # 1. 函数数量权重最高
        score += len(diff.affected_functions) * 10

        # 2. 文件类型权重
        if diff.filepath.endswith('.c'):
            score += 50  # C源文件最重要
        elif diff.filepath.endswith('.h'):
            score += 10  # 头文件次要
        elif diff.filepath.endswith(('.cpp', '.cc', '.cxx')):
            score += 45  # C++源文件
        elif diff.filepath.endswith('.py'):
            score += 40  # Python文件
        # 其他文件类型不加分

        # 3. 变更行数（ungrouped_lines代表未归属到函数的变更行）
        if hasattr(diff, 'ungrouped_lines') and diff.ungrouped_lines:
            score += len(diff.ungrouped_lines) * 0.1

        # 4. 文件状态
        if diff.file_status == "modified":
            score += 5
        elif diff.file_status == "added":
            score += 3
        elif diff.file_status == "deleted":
            score += 1

        # 5. 路径权重（src目录下的文件更重要）
        if '/src/' in diff.filepath or diff.filepath.startswith('src/'):
            score += 20
        elif '/include/' in diff.filepath or diff.filepath.startswith('include/'):
            score += 5

        return score

    def _process_file_change(self, repo_dir, base_branch, dev_branch, base_commit_sha,dev_commit_sha,patched_file,exclude_patterns: list = None, max_context_chain_depth: int = None):
        # 使用配置中的默认值
        if max_context_chain_depth is None:
            max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
            
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        if patched_file.is_removed_file:
            return self._process_deleted_file(repo_dir, base_branch, patched_file, max_context_chain_depth)
        elif patched_file.is_added_file:
            return self._process_new_file(repo_dir, dev_branch,dev_commit_sha, patched_file,exclude_patterns,max_context_chain_depth)
        else:
            return self._process_modified_file(repo_dir, base_branch, dev_branch,base_commit_sha,dev_commit_sha, patched_file, max_context_chain_depth, exclude_patterns)

    def _process_modified_file(self, repo_dir, base_branch, dev_branch,base_commit_sha,dev_commit_sha, patched_file, max_context_chain_depth: int = None, exclude_patterns: list = None):
        # 使用配置中的默认值
        if max_context_chain_depth is None:
            max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
            
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        # 使用commit_sha优先，fallback到branch
        base_ref = base_commit_sha if base_commit_sha else base_branch
        dev_ref = dev_commit_sha if dev_commit_sha else dev_branch
        origin_content = self.git_service.get_file_content_by_ref(repo_dir, base_ref, patched_file.path)
        new_content = self.git_service.get_file_content_by_ref(repo_dir, dev_ref, patched_file.path)
        changed_lines = get_changed_lines_from_patched_file(patched_file)
        
        # 使用 RepositoryIndex 分析函数变更
        affected_funcs = self.repo_analyzer.analyze_functions(
            repo_dir=repo_dir,
            file_path=patched_file.path,
            file_content=new_content,
            branch=dev_branch,
            commit_sha=dev_commit_sha,
            changed_lines=changed_lines,
            exclude_patterns=exclude_patterns,
            depth=max_context_chain_depth
        )


        
        affected_lines = {line for func in affected_funcs for line in func.changed_lines}
        ungrouped_lines = [line for line in changed_lines if line not in affected_lines]



        return DiffResult(
            filepath=patched_file.path,
            origin_file_content=origin_content,
            new_file_content=new_content,
            affected_functions=affected_funcs,
            ungrouped_lines=ungrouped_lines,
            file_status="modified"
        )

    def _process_new_file(self, repo_dir, dev_branch,dev_commit_sha, patched_file,exclude_patterns: list = None,max_context_chain_depth: int = None):
        # 使用配置中的默认值
        if max_context_chain_depth is None:
            max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
            
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        # 获取新增文件内容
        dev_ref = dev_commit_sha if dev_commit_sha else dev_branch
        new_content = self.git_service.get_file_content_by_ref(repo_dir, dev_ref, patched_file.path)

        # 使用 RepositoryIndex 分析函数变更
        affected_functions = self.repo_analyzer.analyze_functions(
            repo_dir=repo_dir,
            file_path=patched_file.path,
            file_content=new_content,
            branch=dev_branch,
            commit_sha=dev_commit_sha,
            changed_lines=None,
            exclude_patterns=exclude_patterns,
            depth=max_context_chain_depth
        )

        return DiffResult(
            filepath=patched_file.path,
            origin_file_content="",
            new_file_content=new_content,
            affected_functions=affected_functions,
            ungrouped_lines=[],
            file_status="added"
        )

    def _process_deleted_file(self, repo_dir, base_branch, patched_file, max_context_chain_depth: int = None):
        # 使用配置中的默认值
        if max_context_chain_depth is None:
            max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
        # 获取删除前文件内容
        origin_content = self.git_service.get_file_content_by_ref(repo_dir, base_branch, patched_file.path)

        return DiffResult(
            filepath=patched_file.path,
            origin_file_content=origin_content,
            new_file_content="",
            affected_functions=[],  # 删除文件不需要变更函数
            ungrouped_lines=[],
            file_status="deleted"
        )

class AnalyzeBranchUseCase(AnalyzeBaseUseCase):
    def __init__(self, git_service: ICodeRepositoryService, llm_service: LLMService, repo_analyzer: RepositoryAnalyzer):
        super().__init__(git_service, llm_service)
        self.repo_analyzer = repo_analyzer
        
    def execute(self, repo_dir: str, branch: str, commit_sha: str = None, max_context_chain_depth: int = None, max_diff_results = config.max_diff_results):
        # 使用配置中的默认值
        if max_context_chain_depth is None:
            max_context_chain_depth = getattr(config, 'max_context_chain_depth', 3)
        self.git_service.keep_branch_update_if_needed(repo_dir, branch)
        
        # 直接获取所有函数，而不是依赖graph_builder.get_modules()
        all_functions = self.repo_analyzer.get_all_functions(
            repo_dir=repo_dir,
            branch=branch,
            commit_sha=commit_sha,
            file_pattern="*.c"
        )
        
        logger.info(f"AnalyzeBranchUseCase: 从分支 {branch} 中获取到 {len(all_functions)} 个函数")
        
        # 按文件分组函数
        functions_by_file = {}
        for af in all_functions:
            if af.filepath not in functions_by_file:
                functions_by_file[af.filepath] = []
            functions_by_file[af.filepath].append(af)
        
        diff_results = []

        # 添加文件处理进度条
        try:
            from tqdm import tqdm
            progress_bar = tqdm(total=len(functions_by_file), desc="📁 处理分支文件", unit="文件")
        except ImportError:
            progress_bar = None
            logger.warning("tqdm未安装，无法显示进度条")

        try:
            # 为每个文件创建一个DiffResult
            for file_path, functions in functions_by_file.items():
                # 获取文件内容
                ref = commit_sha if commit_sha else branch
                try:
                    file_content = self.git_service.get_file_content_by_ref(repo_dir, ref, file_path)
                except Exception as e:
                    logger.warning(f"无法获取文件内容 {file_path}: {e}")
                    file_content = ""

                diff_results.append(DiffResult(
                    filepath=file_path,
                    origin_file_content="",
                    new_file_content=file_content,
                    affected_functions=functions,
                    ungrouped_lines=[],
                    file_status="unchanged"
                ))

                # 更新进度条
                if progress_bar:
                    progress_bar.set_postfix({
                        '当前文件': file_path.split('/')[-1][:20],
                        '函数数': len(functions)
                    })
                    progress_bar.update(1)
        finally:
            # 确保进度条被关闭
            if progress_bar:
                progress_bar.close()

        if max_diff_results is not None and max_diff_results > 0:
            diff_results = diff_results[:max_diff_results]            

        check_mr_result = CheckMRResult(
            mr_id=0,
            base_branch=branch,
            dev_branch=branch,
            diffs=diff_results
        )
        return self.analyze_mr(check_mr_result)

def get_changed_lines_from_patched_file(patched_file) -> list[int]:
    changed_lines = []
    for hunk in patched_file:
        for line in hunk:
            # line.is_added 或 line.is_removed 表示该行是变更行
            if line.is_added or line.is_removed:
                # line.target_line_no 是新版本的行号，line.source_line_no 是旧版本的行号
                # 根据场景选用：
                # 对于新文件和修改文件，通常用 target_line_no（新文件行号）
                # 对于删除文件，通常用 source_line_no（旧文件行号）
                # 这里先收集新文件的行号（即 target_line_no）
                if line.target_line_no:
                    changed_lines.append(line.target_line_no)
    return sorted(set(changed_lines))
