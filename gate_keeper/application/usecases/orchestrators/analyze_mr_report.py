from datetime import datetime
from typing import Optional

from gate_keeper.application.dto.result import CheckMRR<PERSON>ult
from gate_keeper.application.service.deduplication import \
    ViolationDeduplicationService
from gate_keeper.application.service.report import ReportService
from gate_keeper.application.usecases.analyze import AnalyzeMRUseCase
from gate_keeper.config import config
from gate_keeper.domain.value_objects.analysis_result import (
    AggregatedAnalyzeLLMR<PERSON>ult, AnalyzeLLMResult, ViolationItem)
from gate_keeper.shared.log import app_logger as logger


class AnalyzeMRAndReportUsecase(AnalyzeMRUseCase):
    def __init__(self, orchestrator):
        """
        初始化AnalyzeMRAndReportUsecase
        
        Args:
            orchestrator: 服务编排器
        """
        # 从编排器获取服务
        git_service = orchestrator.get_service("git_service")
        llm_service = orchestrator.get_service("llm_service")
        repo_analyzer = orchestrator.get_service("code_analyzer")
        
        # 设置repo_analyzer的编排器引用
        repo_analyzer.orchestrator = orchestrator
        
        super().__init__(git_service, llm_service, repo_analyzer)
        
        # 保存编排器引用
        self.orchestrator = orchestrator
        
        # 初始化ViolationItem排重服务
        self.violation_deduplication_service = ViolationDeduplicationService()
    
    def execute(self, repo_dir: str,project_id:str, mr_id: int, base_branch: str, dev_branch: str,max_context_chain_depth: int = 3,base_commit_sha:str=None,dev_commit_sha:str=None,exclude_patterns: list = None, max_diff_results: int = None):
        if exclude_patterns is None:
            exclude_patterns = []
        analyze_results = super().execute(repo_dir, mr_id, base_branch, dev_branch, max_context_chain_depth, base_commit_sha, dev_commit_sha, exclude_patterns=exclude_patterns, max_diff_results=max_diff_results)

        report_svc = ReportService.create_for_project(
            project_id=project_id,
            access_token=config.token,
        )

        # 从CheckMRResult中提取违规结果，按函数聚合
        aggregated_results = []
        for diff in analyze_results.diffs:
            for affected_function in diff.affected_functions:
                if affected_function.llm_results:
                    # 筛选出违规结果
                    not_pass_llm_results = [result for result in affected_function.llm_results if result and not result.is_pass]
                    
                    if not_pass_llm_results:
                        # 收集所有call_id
                        call_ids = []
                        for llm_result in not_pass_llm_results:
                            if llm_result.call_id:
                                call_ids.append(llm_result.call_id)
                        
                        # 合并同一函数的违规信息
                        combined_result = AggregatedAnalyzeLLMResult(
                            is_pass=False,
                            reason=f"函数 '{affected_function.name}' 存在代码规范问题",
                            code=affected_function.code,
                            violations=[],
                            call_ids=call_ids,
                            function_name=affected_function.name,
                            file_path=affected_function.filepath
                        )
                        # 收集所有违规详情
                        all_violations = []
                        for llm_result in not_pass_llm_results:
                            if llm_result.violations:
                                all_violations.extend(llm_result.violations)
                            else:
                                # 检查reason是否包含无效内容
                                if llm_result.reason and "未检测到明显的代码规范问题" in llm_result.reason:
                                    logger.warning(f"跳过无效的LLM结果，函数: {affected_function.name}, reason: {llm_result.reason}")
                                    continue
                                
                                # 如果没有详细的violations字段，使用基本信息
                                all_violations.append(ViolationItem(
                                    rule_id="unknown",
                                    rule_content="unknown",
                                    location={"file_path": affected_function.filepath, "line": affected_function.start_line},
                                    severity="unknown",
                                    message=llm_result.reason
                                ))
                        
                        # 对违规项进行相似度排重
                        if all_violations:
                            original_count = len(all_violations)
                            deduplicated_violations = self.violation_deduplication_service.deduplicate_violations(all_violations)
                            deduplicated_count = len(deduplicated_violations)
                            
                            if original_count != deduplicated_count:
                                logger.info(f"函数 '{affected_function.name}' 违规项排重: {original_count} -> {deduplicated_count}")
                            
                            combined_result.violations = deduplicated_violations
                        
                        aggregated_results.append(combined_result)
                        

        report_content = report_svc.generate_markdown_report(aggregated_results)
        # if report_content:
        #     report_svc.post_mr_comment_with_deduplication(project_id, mr_id, report_content)

        report_id = f"{mr_id}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        for result in aggregated_results:
            single_report_content = report_svc.generate_violation_markdown_report(result,report_id=report_id,is_independent=True)
            if single_report_content:
                report_svc.post_mr_comment_with_deduplication(project_id, mr_id, single_report_content)
                logger.info(f"报告{report_id}已提交")
        
        return analyze_results, report_content