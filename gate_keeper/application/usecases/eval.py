"""
评估模式专用用例

处理评估模式的第三种子模式：评测集自包含评测
"""

from pathlib import Path
from typing import Dict, List, Optional, Tuple

from gate_keeper.application.dto.result import CheckMRResult, DiffResult
from gate_keeper.application.interfaces.git_intf import ICodeRepositoryService
from gate_keeper.application.service.analysis import RepositoryAnalyzer
from gate_keeper.application.service.llm import LLMService
from gate_keeper.domain.rule.check_rule import GitInfo, TestCase
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.shared.file import determine_language_by_filename
from gate_keeper.shared.log import app_logger as logger


class EvalUseCase:
    """评估模式专用用例 - 处理评测集自包含评测"""
    
    def __init__(self, git_service: ICodeRepositoryService, llm_service: LLMService, repo_analyzer: RepositoryAnalyzer):
        self.git_service = git_service
        self.llm_service = llm_service
        self.repo_analyzer = repo_analyzer
    
    def execute(self, test_cases: List[TestCase], repo_path: str, branch: str = "master") -> List[AnalyzeLLMResult]:
        """
        执行评测集自包含评测

        Args:
            test_cases: 测试用例列表
            repo_path: 仓库路径
            branch: 分支名称

        Returns:
            分析结果列表
        """
        logger.info(f"🔍 开始执行评测集自包含评测，测试用例数量: {len(test_cases)}")

        results = []

        # 添加整体进度条
        try:
            from tqdm import tqdm
            progress_bar = tqdm(total=len(test_cases), desc="📋 评估测试用例", unit="用例")
        except ImportError:
            progress_bar = None
            logger.warning("tqdm未安装，无法显示进度条")

        try:
            for i, test_case in enumerate(test_cases):
                try:
                    result = self._analyze_single_test_case(test_case, repo_path, branch)
                    results.append(result)

                    # 更新进度条
                    if progress_bar:
                        # 显示当前测试用例信息
                        progress_bar.set_postfix({
                            '当前': f"{test_case.id}",
                            '状态': '✅通过' if result.is_pass else '❌失败'
                        })
                        progress_bar.update(1)

                except Exception as e:
                    logger.error(f"分析测试用例 {test_case.id} 失败: {e}")
                    # 创建错误结果
                    result = self._create_error_result(test_case, str(e))
                    results.append(result)

                    # 更新进度条
                    if progress_bar:
                        progress_bar.set_postfix({
                            '当前': f"{test_case.id}",
                            '状态': '⚠️错误'
                        })
                        progress_bar.update(1)
        finally:
            # 确保进度条被关闭
            if progress_bar:
                progress_bar.close()

        logger.info(f"✅ 评测集自包含评测完成，成功分析 {len(results)} 个测试用例")
        return results
    
    def _analyze_single_test_case(self, test_case: TestCase, repo_path: str, branch: str) -> AnalyzeLLMResult:
        """分析单个测试用例"""
        logger.debug(f"分析测试用例: {test_case.id} - {test_case.case_name}")
        
        # 检查是否有Git信息
        if test_case.git and test_case.git.repo_url:
            return self._analyze_with_git_info(test_case, repo_path, branch)
        else:
            return self._analyze_without_git_info(test_case, repo_path, branch)
    
    def _analyze_with_git_info(self, test_case: TestCase, repo_path: str, branch: str) -> AnalyzeLLMResult:
        """使用Git信息进行分析"""
        git_info = test_case.git
        
        # 检查是否需要切换到指定分支
        if git_info.branch and git_info.branch != branch:
            logger.info(f"切换到指定分支: {git_info.branch}")
            self.git_service.keep_branch_update_if_needed(repo_path, git_info.branch)
            current_branch = git_info.branch
        else:
            current_branch = branch
        
        # 获取文件内容
        if git_info.file_path:
            try:
                ref = git_info.commit if git_info.commit else current_branch
                file_content = self.git_service.get_file_content_by_ref(repo_path, ref, git_info.file_path)
            except Exception as e:
                logger.warning(f"无法获取文件内容 {git_info.file_path}: {e}")
                file_content = test_case.code  # 回退到测试用例中的代码
        else:
            file_content = test_case.code
        
        # 创建DiffResult
        diff_result = DiffResult(
            filepath=git_info.file_path or "test_case_code",
            origin_file_content="",
            new_file_content=file_content,
            affected_functions=[],
            ungrouped_lines=[],
            file_status="unchanged"
        )
        
        # 创建CheckMRResult
        check_mr_result = CheckMRResult(
            mr_id=0,
            base_branch=current_branch,
            dev_branch=current_branch,
            diffs=[diff_result]
        )
        
        # 转换为CodeCheckRule并分析
        check_rule = test_case.to_check_rule()
        return self.llm_service.analyze_mr_with_rules(check_mr_result, [check_rule])
    
    def _analyze_without_git_info(self, test_case: TestCase, repo_path: str, branch: str) -> AnalyzeLLMResult:
        """不使用Git信息进行分析"""
        logger.debug(f"使用测试用例代码进行分析: {test_case.id}")
        
        # 直接使用测试用例中的代码
        file_content = test_case.code
        
        # 创建DiffResult
        diff_result = DiffResult(
            filepath="test_case_code",
            origin_file_content="",
            new_file_content=file_content,
            affected_functions=[],
            ungrouped_lines=[],
            file_status="unchanged"
        )
        
        # 创建CheckMRResult
        check_mr_result = CheckMRResult(
            mr_id=0,
            base_branch=branch,
            dev_branch=branch,
            diffs=[diff_result]
        )
        
        # 转换为CodeCheckRule并分析
        check_rule = test_case.to_check_rule()
        return self.llm_service.analyze_mr_with_rules(check_mr_result, [check_rule])
    
    def _create_error_result(self, test_case: TestCase, error_message: str) -> AnalyzeLLMResult:
        """创建错误结果"""
        return AnalyzeLLMResult(
            rule_id=test_case.id,
            rule_name=test_case.name,
            is_pass=False,
            reason=f"分析失败: {error_message}",
            violations=[],
            affected_functions=[],
            filepath=test_case.git.file_path if test_case.git else "test_case_code",
            function_name=test_case.git.function_name if test_case.git else "",
            line_number=0
        ) 