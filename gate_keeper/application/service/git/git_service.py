import os
import tempfile
from contextlib import contextmanager
from functools import lru_cache
from io import String<PERSON>
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from git import BadName, GitCommandError, Repo
from pydantic import BaseModel
from unidiff import PatchedFile, PatchSet

from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.shared.log import app_logger as logger


class GitService(BaseModel):
    comment_service: Optional[IGitPlatformService] = None
    model_config = {"arbitrary_types_allowed": True}

    def __init__(self, **data):
        super().__init__(**data)
        self._repo_cache: Dict[str, Repo] = {}              # path -> Repo 实例
        self._commit_cache: Dict[Tuple[str, str], str] = {} # (path, ref) -> sha
        self._file_cache: Dict[Tuple[str, str, str], str] = {} # (path, sha, file_path) -> content
        self._clone_cache: Dict[str, str] = {}              # repo_url -> local path

    def _get_repo(self, repo_path: str) -> Repo:
        if repo_path not in self._repo_cache:
            self._repo_cache[repo_path] = Repo(repo_path)
        return self._repo_cache[repo_path]

    def __build_remote_ref_path(self,remote: str, branch: str) -> str:
        """
        构造远程分支的完整 Git ref 路径。

        Args:
            remote (str): 远程名，例如 'origin'
            branch (str): 分支名，例如 'dev'

        Returns:
            str: 完整的 ref path，例如 'refs/remotes/origin/dev'
        """
        return f"refs/remotes/{remote}/{branch}"
        
    def _resolve_sha(self, repo_path: str, sha: str, fallback_branch: str) -> str:
        return sha or self.get_branch_commit_sha(repo_path, fallback_branch)

    def clone(self, repo_url: str, local_dir: Optional[str] = None, branch: Optional[str] = None) -> str:
        if repo_url in self._clone_cache:
            logger.debug(f"[cache] Using cached clone path for {repo_url}")
            return self._clone_cache[repo_url]

        temp_dir = local_dir or tempfile.mkdtemp()
        repo_name = repo_url.split("/")[-1].replace(".git", "")
        repo_path = os.path.join(temp_dir, repo_name)
        logger.debug(f"Cloning repo {repo_url} into {repo_path} (branch={branch})")
        clone_args = {"multi_options": ["--no-single-branch"]}
        if branch:
            clone_args["branch"] = branch
        Repo.clone_from(repo_url, repo_path, **clone_args)
        logger.debug(f"Cloned repo at {repo_path}")
        self._clone_cache[repo_url] = repo_path
        return repo_path

    def resolve_commit_ref(self, repo_path: str, ref_or_sha: str, force_resolve: bool = False,remote="origin") -> str:
        import subprocess

        from git import Repo
        key = (repo_path, ref_or_sha)
        if not force_resolve and key in self._commit_cache:
            return self._commit_cache[key]

        repo = Repo(repo_path)

        # Step 1: 本地能解析就直接返回
        try:
            commit_sha = repo.commit(ref_or_sha).hexsha
            self._commit_cache[key] = commit_sha
            return commit_sha
        except Exception:
            logger.debug(f"本地无法解析: {ref_or_sha}")

        # Step 2: 是远程分支则尝试 fetch
        if ref_or_sha.startswith("origin/"):
            fetch_branch = ref_or_sha[len("origin/"):]
            try:
                fetch_refspec = f"refs/heads/{fetch_branch}:refs/remotes/origin/{fetch_branch}"
                logger.debug(f"Fetching with refspec: {fetch_refspec}")
                repo.remotes.origin.fetch(refspec=fetch_refspec)
                commit_sha = repo.commit(ref_or_sha).hexsha
                self._commit_cache[key] = commit_sha
                return commit_sha
            except Exception as e:
                logger.warning(f"远程 fetch 失败: {e}")

        # Step 3: 是 commit SHA 的情况，尝试远程 fetch
        if len(ref_or_sha) == 40:
            try:
                result = subprocess.run(
                    ["git", "fetch", "origin", ref_or_sha],
                    cwd=repo_path,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                )
                if result.returncode == 0:
                    commit_sha = repo.commit(ref_or_sha).hexsha
                    self._commit_cache[key] = commit_sha
                    return commit_sha
                else:
                    logger.warning(f"Fetch by SHA failed: {result.stderr.strip()}")
            except Exception as e:
                logger.warning(f"Fetch by SHA error: {e}")

        raise RuntimeError(f"无法解析 ref 或 commit: {ref_or_sha}")




    def list_repo_files(self, repo_path: str, ref_or_sha: str) -> List[str]:
        sha = self.resolve_commit_ref(repo_path, ref_or_sha)
        repo = self._get_repo(repo_path)
        logger.debug(f"Listing files in repo at {repo_path} for commit {sha}")
        lines = repo.git.ls_tree("-r", sha).split("\n")
        files = [line.split("\t", 1)[-1] for line in lines if line.strip()]
        logger.debug(f"Found {len(files)} files")
        return files


    def get_file_content_by_ref(self, repo_dir: str, ref_or_sha: str, file_path_in_repo: str,remote="origin") -> str:
        sha = self.resolve_commit_ref(repo_dir, ref_or_sha,remote=remote)
        
        # 标准化路径
        repo_path = Path(repo_dir).resolve()
        file_path = (repo_path / file_path_in_repo).resolve()

        try:
            # 计算相对路径（用于 git show）
            rel_path = file_path.relative_to(repo_path)
        except ValueError:
            logger.warning(f"File path {file_path} is not under repo {repo_path}")
            return ""

        cache_key = (str(repo_path), sha, str(rel_path))
        if cache_key in self._file_cache:
            return self._file_cache[cache_key]

        repo = self._get_repo(str(repo_path))
        logger.debug(f"Getting file content for '{rel_path}' at sha {sha}")
        try:
            # 使用 as_process=True 获取字节流内容
            process = repo.git.show(f"{sha}:{rel_path.as_posix()}", as_process=True)
            raw_bytes = process.stdout.read()

            # 多编码尝试解码为文本（优先 utf-8，其次 gbk）
            for encoding in ["utf-8", "gbk"]:
                try:
                    decoded = raw_bytes.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Unable to decode file with utf-8 or gbk")

            # 重新编码为 utf-8 并返回
            utf8_content = decoded.encode("utf-8").decode("utf-8")
            self._file_cache[cache_key] = utf8_content
            return utf8_content

        except GitCommandError as e:
            logger.error(f"Failed to get file content for {rel_path} at {ref_or_sha}: {e.stderr}")
            return ""
        except Exception as e:
            logger.exception(f"Unexpected error while getting file content: {e}")
            return ""


    def get_modified_code_from_patch(self, patched_file: PatchedFile) -> dict:
        added, removed = [], []
        for hunk in patched_file:
            for line in hunk:
                if line.is_added:
                    added.append(line.value.rstrip('\n'))
                elif line.is_removed:
                    removed.append(line.value.rstrip('\n'))
        logger.debug(f"Patch for file {patched_file.path}: {len(added)} lines added, {len(removed)} lines removed")
        return {"added": added, "removed": removed}

    def get_branch_commit_sha(self, repo_path: str, branch: str) -> str:
        sha = self.resolve_commit_ref(repo_path, branch)
        logger.debug(f"Branch '{branch}' commit sha is {sha}")
        return sha

    def get_remote_latest_branch_commit_sha(self,repo_dir: str, branch: str, remote: str) -> str:
        repo = Repo(repo_dir)
        ref_name=self.__build_remote_ref_path(remote,branch)
        try:
            sha = repo.git.rev_parse(ref_name)
            return sha
        except GitCommandError as e:
            logger.error(f"无法解析远程分支 {ref_name}: {e}")
            raise RuntimeError(f"无法解析远程分支 {ref_name}")


    def get_remote_branch_commit_sha(self,repo_dir: str,branch: str,remote:str="origin") -> str:
        repo = Repo(repo_dir)
        ref_name = f"{remote}/{branch}"
        try:
            sha = repo.git.rev_parse(ref_name)
            return sha
        except GitCommandError as e:
            logger.error(f"无法解析远程分支 {ref_name}: {e}")
            raise RuntimeError(f"无法解析远程分支 {ref_name}")

    def keep_branch_update_if_needed(self, repo_dir: str, branch: str, remote: str = "origin") -> bool:
        repo = Repo(repo_dir)

        # 1. Fetch remote
        try:
            repo.remote(remote).fetch()
            logger.debug(f"Fetched remote '{remote}'")
        except GitCommandError as e:
            logger.error(f"fetch 远程失败 {remote}: {e}")
            raise

        # 2. 获取远程分支最新 commit sha
        try:
            remote_sha = self.get_remote_branch_commit_sha(repo_dir, branch,remote)
            logger.debug(f"Remote {remote}/{branch} sha: {remote_sha}")
        except RuntimeError:
            raise

        # 3. 获取本地分支 commit sha
        try:
            local_commit = repo.refs[branch].commit
            local_sha = local_commit.hexsha
            logger.debug(f"Local branch {branch} sha: {local_sha}")
        except (IndexError, AttributeError, BadName):
            # 本地分支不存在，尝试创建并追踪远程分支（使用 refs 路径避免歧义）
            try:
                remote_ref_path = f"refs/remotes/{remote}/{branch}"
                repo.git.checkout("-b", branch, remote_ref_path)
                logger.info(f"Created local branch {branch} tracking {remote}/{branch}")
                return True
            except GitCommandError as e:
                logger.error(f"创建本地分支失败: {e}")
                raise

        # 4. 比较 sha，相同则跳过
        if local_sha == remote_sha:
            logger.debug(f"[skip] {branch} 已是最新 (sha {local_sha[:7]})")
            return False

        # 5. 不同则切换到本地分支并 pull
        logger.debug(f"[update] {branch} local: {local_sha[:7]} -> remote: {remote_sha[:7]}")
        repo.git.checkout(branch)
        
        # 处理分歧分支的情况，使用 rebase 策略
        try:
            # 首先尝试设置 pull.rebase 为 true，然后执行 pull
            repo.git.config("pull.rebase", "true", local=True)
            repo.remotes[remote].pull(branch)
        except GitCommandError as e:
            logger.warning(f"Rebase pull failed for {branch}: {e}")
            # 如果 rebase 失败，尝试使用 merge 策略
            try:
                repo.git.config("pull.rebase", "false", local=True)
                repo.git.config("pull.ff", "false", local=True)
                repo.remotes[remote].pull(branch)
            except GitCommandError as merge_error:
                logger.error(f"Merge pull also failed for {branch}: {merge_error}")
                # 如果都失败，尝试强制重置到远程分支
                logger.info(f"Attempting to reset {branch} to remote {remote}/{branch}")
                repo.git.reset("--hard", f"{remote}/{branch}")
        
        new_local_sha = repo.refs[branch].commit.hexsha
        logger.debug(f"[check] {branch} 更新后本地 sha: {new_local_sha[:7]} vs 远程 {remote_sha[:7]}")
        return True

    @contextmanager
    def checkout_to_ref(self, repo_path: str, ref_or_sha: str):
        repo = Repo(repo_path)
        current = repo.head.commit.hexsha
        target = self.resolve_commit_ref(repo_path, ref_or_sha)
        logger.debug(f"Checking out from {current} to {target}")
        if current == target:
            logger.debug("Already at target commit, no checkout needed")
            yield
            return
        repo.git.checkout(target)
        try:
            yield
        finally:
            logger.debug(f"Restoring original commit {current}")
            repo.git.checkout(current)

    def get_diff_by_mr_id(self, repo_path: str, mr_id: int, base_branch: str, dev_branch: str,base_commit_sha:str=None,dev_commit_sha:str=None,remote="origin") -> PatchSet:
        """
        获取某个 MR（通过分支差异模拟）对应的文件改动 diff。
        注意：这里假定 mr_id 仅用于标识，真实 diff 通过 dev_branch 与 base_branch 的 diff 获取。
        使用mr_id 获取 base_branch 和 dev_branch 的能力 需要对应的git 实现
        """
        # to-do get information from mr_id
        return self.__get_diff_by_repo(repo_path,base_branch,dev_branch,base_commit_sha,dev_commit_sha,remote)

    def __get_diff_by_repo(self, repo_path: str, base_branch: str, dev_branch: str, 
                        base_commit_sha: str = None, dev_commit_sha: str = None,remote="origin"):
        repo = self._get_repo(repo_path)
        
        # Update local branches first
        self.keep_branch_update_if_needed(repo_path, base_branch,remote)
        self.keep_branch_update_if_needed(repo_path, dev_branch,remote)

        # Now compare the local branches (which should match remotes)
        base_commit_sha = self._resolve_sha(repo_path, base_commit_sha, base_branch)
        dev_commit_sha = self._resolve_sha(repo_path, dev_commit_sha, dev_branch)

        diff_output = repo.git.diff(f"{base_commit_sha}...{dev_commit_sha}", "--unified=0", "--no-color")
        patch_set = PatchSet(StringIO(diff_output))
        logger.debug(f"Parsed diff: {len(patch_set)} files changed")
        return patch_set
    
    def comment_issue(self, repo_url: str, issue_id: int, comment: str):
        if not self.comment_service:
            raise RuntimeError("没有配置comment_service，无法评论Issue")
        return self.comment_service.comment_issue(repo_url, issue_id, comment)

    def post_discussion_to_mr(self, project_id: str, mr_id: str, discussion_content: str):
        """在MR上添加讨论，委托给comment_service实现。"""
        if not self.comment_service:
            raise RuntimeError("没有配置comment_service，无法评论MR")
        return self.comment_service.post_discussion_to_mr(project_id, mr_id, discussion_content)