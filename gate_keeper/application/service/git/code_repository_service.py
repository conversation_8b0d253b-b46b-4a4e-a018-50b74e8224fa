import os
import tempfile
from contextlib import contextmanager
from functools import lru_cache
from io import StringIO
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from git import BadName, GitCommandError, Repo
from pydantic import BaseModel
from unidiff import PatchedFile, PatchSet

from gate_keeper.application.interfaces.git_intf import \
    ICodeRepositoryService as ICodeRepositoryService
from gate_keeper.shared.log import app_logger as logger


class CodeRepositoryService(BaseModel, ICodeRepositoryService):
    """代码仓库业务服务 - 支持代码分析业务逻辑"""
    
    model_config = {"arbitrary_types_allowed": True}

    def __init__(self, **data):
        super().__init__(**data)
        self._repo_cache: Dict[str, Repo] = {}              # path -> Repo 实例
        self._commit_cache: Dict[Tuple[str, str], str] = {} # (path, ref) -> sha
        self._file_cache: Dict[Tuple[str, str, str], str] = {} # (path, sha, file_path) -> content
        self._clone_cache: Dict[str, str] = {}              # repo_url -> local path

    def _get_repo(self, repo_path: str) -> Repo:
        if repo_path not in self._repo_cache:
            self._repo_cache[repo_path] = Repo(repo_path)
        return self._repo_cache[repo_path]

    def __build_remote_ref_path(self,remote: str, branch: str) -> str:
        """
        构造远程分支的完整 Git ref 路径。

        Args:
            remote (str): 远程名，例如 'origin'
            branch (str): 分支名，例如 'dev'

        Returns:
            str: 完整的 ref path，例如 'refs/remotes/origin/dev'
        """
        return f"refs/remotes/{remote}/{branch}"
        
    def _resolve_sha(self, repo_path: str, sha: str, fallback_branch: str) -> str:
        return sha or self.get_commit_sha(repo_path, fallback_branch)

    def clone(self, repo_url: str, local_dir: Optional[str] = None, branch: Optional[str] = None) -> str:
        """克隆仓库 - 业务逻辑：支持缓存和分支选择"""
        if repo_url in self._clone_cache:
            logger.debug(f"[cache] Using cached clone path for {repo_url}")
            return self._clone_cache[repo_url]

        temp_dir = local_dir or tempfile.mkdtemp()
        repo_name = repo_url.split("/")[-1].replace(".git", "")
        repo_path = os.path.join(temp_dir, repo_name)
        logger.debug(f"Cloning repo {repo_url} into {repo_path} (branch={branch})")
        clone_args = {"multi_options": ["--no-single-branch"]}
        if branch:
            clone_args["branch"] = branch
        Repo.clone_from(repo_url, repo_path, **clone_args)
        logger.debug(f"Cloned repo at {repo_path}")
        self._clone_cache[repo_url] = repo_path
        return repo_path

    def clone_repository(self, repo_url: str, local_dir: Optional[str] = None, branch: Optional[str] = None) -> str:
        """克隆仓库 - 业务逻辑：支持缓存和分支选择（兼容性方法）"""
        return self.clone(repo_url, local_dir, branch)

    def resolve_commit_ref(self, repo_path: str, ref_or_sha: str, force_resolve: bool = False,remote="origin") -> str:
        """解析提交引用 - 业务逻辑：智能解析和缓存"""
        import subprocess

        from git import Repo
        key = (repo_path, ref_or_sha)
        if not force_resolve and key in self._commit_cache:
            return self._commit_cache[key]

        repo = Repo(repo_path)

        # Step 1: 本地能解析就直接返回
        try:
            commit_sha = repo.commit(ref_or_sha).hexsha
            self._commit_cache[key] = commit_sha
            return commit_sha
        except Exception:
            logger.debug(f"本地无法解析: {ref_or_sha}")

        # Step 2: 是远程分支则尝试 fetch
        if ref_or_sha.startswith("origin/"):
            fetch_branch = ref_or_sha[len("origin/"):]
            try:
                fetch_refspec = f"refs/heads/{fetch_branch}:refs/remotes/origin/{fetch_branch}"
                logger.debug(f"Fetching with refspec: {fetch_refspec}")
                repo.remotes.origin.fetch(refspec=fetch_refspec)
                commit_sha = repo.commit(ref_or_sha).hexsha
                self._commit_cache[key] = commit_sha
                return commit_sha
            except Exception as e:
                logger.warning(f"远程 fetch 失败: {e}")

        # Step 3: 是 commit SHA 的情况，尝试远程 fetch
        if len(ref_or_sha) == 40:
            try:
                result = subprocess.run(
                    ["git", "fetch", "origin", ref_or_sha],
                    cwd=repo_path,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                )
                if result.returncode == 0:
                    commit_sha = repo.commit(ref_or_sha).hexsha
                    self._commit_cache[key] = commit_sha
                    return commit_sha
                else:
                    logger.warning(f"Fetch by SHA failed: {result.stderr.strip()}")
            except Exception as e:
                logger.warning(f"Fetch by SHA error: {e}")

        raise RuntimeError(f"无法解析 ref 或 commit: {ref_or_sha}")

    def list_repo_files(self, repo_path: str, ref_or_sha: str) -> List[str]:
        """列出仓库文件 - 业务逻辑：支持引用解析"""
        sha = self.resolve_commit_ref(repo_path, ref_or_sha)
        repo = self._get_repo(repo_path)
        logger.debug(f"Listing files in repo at {repo_path} for commit {sha}")
        lines = repo.git.ls_tree("-r", sha).split("\n")
        files = [line.split("\t", 1)[-1] for line in lines if line.strip()]
        logger.debug(f"Found {len(files)} files")
        return files

    def list_repository_files(self, repo_path: str, ref_or_sha: str) -> List[str]:
        """列出仓库文件 - 业务逻辑：支持引用解析（兼容性方法）"""
        return self.list_repo_files(repo_path, ref_or_sha)

    def get_file_content(self, repo_dir: str, ref_or_sha: str, file_path: str) -> str:
        """获取文件内容 - 业务逻辑：智能编码处理和缓存"""
        sha = self.resolve_commit_ref(repo_dir, ref_or_sha)
        
        # 标准化路径
        repo_path = Path(repo_dir).resolve()
        file_path_obj = (repo_path / file_path).resolve()

        try:
            # 计算相对路径（用于 git show）
            rel_path = file_path_obj.relative_to(repo_path)
        except ValueError:
            logger.warning(f"File path {file_path_obj} is not under repo {repo_path}")
            return ""

        cache_key = (str(repo_path), sha, str(rel_path))
        if cache_key in self._file_cache:
            return self._file_cache[cache_key]

        repo = self._get_repo(str(repo_path))
        logger.debug(f"Getting file content for '{rel_path}' at sha {sha}")
        try:
            # 使用 as_process=True 获取字节流内容
            process = repo.git.show(f"{sha}:{rel_path.as_posix()}", as_process=True)
            raw_bytes = process.stdout.read()

            # 多编码尝试解码为文本（优先 utf-8，其次 gbk）
            for encoding in ["utf-8", "gbk"]:
                try:
                    decoded = raw_bytes.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Unable to decode file with utf-8 or gbk")

            # 重新编码为 utf-8 并返回
            utf8_content = decoded.encode("utf-8").decode("utf-8")
            self._file_cache[cache_key] = utf8_content
            return utf8_content

        except GitCommandError as e:
            logger.error(f"Failed to get file content for {rel_path} at {ref_or_sha}: {e.stderr}")
            return ""
        except Exception as e:
            logger.exception(f"Unexpected error while getting file content: {e}")
            return ""

    def get_modified_code_from_patch(self, patched_file: PatchedFile) -> dict:
        """从补丁获取修改的代码 - 业务逻辑：代码变更分析"""
        added, removed = [], []
        for hunk in patched_file:
            for line in hunk:
                if line.is_added:
                    added.append(line.value.rstrip('\n'))
                elif line.is_removed:
                    removed.append(line.value.rstrip('\n'))
        logger.debug(f"Patch for file {patched_file.path}: {len(added)} lines added, {len(removed)} lines removed")
        return {"added": added, "removed": removed}

    def get_commit_sha(self, repo_path: str, ref_or_branch: str) -> str:
        """获取提交SHA - 业务逻辑：智能解析"""
        sha = self.resolve_commit_ref(repo_path, ref_or_branch)
        logger.debug(f"Branch '{ref_or_branch}' commit sha is {sha}")
        return sha

    def get_remote_latest_branch_commit_sha(self,repo_dir: str, branch: str, remote: str) -> str:
        """获取远程分支最新提交SHA - 业务逻辑：远程同步"""
        repo = Repo(repo_dir)
        ref_name=self.__build_remote_ref_path(remote,branch)
        try:
            sha = repo.git.rev_parse(ref_name)
            logger.debug(f"Remote branch '{branch}' latest commit sha is {sha}")
            return sha
        except GitCommandError as e:
            logger.warning(f"Failed to get remote branch commit sha: {e}")
            raise RuntimeError(f"无法获取远程分支 {branch} 的提交SHA")

    def get_remote_branch_commit_sha(self,repo_dir: str,branch: str,remote:str="origin") -> str:
        """获取远程分支提交SHA - 业务逻辑：远程引用解析"""
        repo = Repo(repo_dir)
        ref_name=self.__build_remote_ref_path(remote,branch)
        try:
            sha = repo.git.rev_parse(ref_name)
            logger.debug(f"Remote branch '{branch}' commit sha is {sha}")
            return sha
        except GitCommandError as e:
            logger.warning(f"Failed to get remote branch commit sha: {e}")
            raise RuntimeError(f"无法获取远程分支 {branch} 的提交SHA")

    def keep_branch_updated(self, repo_dir: str, branch: str, remote: str = "origin") -> bool:
        """保持分支更新 - 业务逻辑：智能同步策略"""
        repo = Repo(repo_dir)
        ref_name = self.__build_remote_ref_path(remote, branch)
        
        try:
            # 检查远程分支是否存在
            repo.git.rev_parse(ref_name)
            logger.debug(f"Remote branch {branch} exists, keeping updated")
            return True
        except GitCommandError:
            logger.warning(f"Remote branch {branch} does not exist")
            return False

    def get_diff_between_branches(self, repo_dir: str, base_branch: str, dev_branch: str, 
                                 base_commit_sha: str = None, dev_commit_sha: str = None) -> List:
        """获取分支间差异 - 业务逻辑：代码变更分析"""
        # 使用commit_sha优先，fallback到branch
        base_ref = base_commit_sha if base_commit_sha else base_branch
        dev_ref = dev_commit_sha if dev_commit_sha else dev_branch
        
        repo = Repo(repo_dir)
        logger.debug(f"Getting diff between {base_ref} and {dev_ref}")
        
        try:
            # 获取差异
            diff_output = repo.git.diff(base_ref, dev_ref, '--unified=3')
            if not diff_output.strip():
                logger.debug("No differences found between branches")
                return []
            
            # 解析差异
            from unidiff import PatchSet
            patch_set = PatchSet(diff_output)
            return list(patch_set)
            
        except GitCommandError as e:
            logger.error(f"Failed to get diff: {e}")
            return []

    def get_diff_by_mr_id(self, repo_path: str, mr_id: int, base_branch: str, dev_branch: str,base_commit_sha:str=None,dev_commit_sha:str=None,remote="origin") -> PatchSet:
        """根据MR ID获取差异 - 业务逻辑：MR分析"""
        return self.__get_diff_by_repo(repo_path, base_branch, dev_branch, base_commit_sha, dev_commit_sha, remote)

    def __get_diff_by_repo(self, repo_path: str, base_branch: str, dev_branch: str, 
                          base_commit_sha: str = None, dev_commit_sha: str = None,remote="origin"):
        """根据仓库获取差异 - 业务逻辑：分支对比"""
        # 使用commit_sha优先，fallback到branch
        base_ref = base_commit_sha if base_commit_sha else base_branch
        dev_ref = dev_commit_sha if dev_commit_sha else dev_branch
        
        repo = Repo(repo_path)
        logger.debug(f"Getting diff between {base_ref} and {dev_ref}")
        
        try:
            # 获取差异
            diff_output = repo.git.diff(base_ref, dev_ref, '--unified=3')
            if not diff_output.strip():
                logger.debug("No differences found between branches")
                return PatchSet("")
            
            # 解析差异
            patch_set = PatchSet(diff_output)
            return patch_set
            
        except GitCommandError as e:
            logger.error(f"Failed to get diff: {e}")
            return PatchSet("")

    @contextmanager
    def checkout_to_ref(self, repo_path: str, ref_or_sha: str):
        """切换到指定引用 - 业务逻辑：临时工作区管理"""
        repo = Repo(repo_path)
        original_head = repo.head.commit.hexsha
        
        try:
            repo.git.checkout(ref_or_sha)
            logger.debug(f"Checked out to {ref_or_sha}")
            yield
        finally:
            repo.git.checkout(original_head)
            logger.debug(f"Restored to original head {original_head}")

    # 兼容性方法 - 保持向后兼容
    def get_file_content_by_ref(self, repo_dir: str, ref_or_sha: str, file_path_in_repo: str, remote="origin") -> str:
        """兼容性方法：获取文件内容"""
        return self.get_file_content(repo_dir, ref_or_sha, file_path_in_repo)

    def get_branch_commit_sha(self, repo_path: str, branch: str) -> str:
        """兼容性方法：获取分支提交SHA"""
        return self.get_commit_sha(repo_path, branch)

    def get_diff(self, repo_path: str, base_branch: str, target_branch: str) -> Optional['CheckMRResult']:
        """
        获取两个分支之间的差异
        
        Args:
            repo_path: 仓库路径
            base_branch: 基础分支
            target_branch: 目标分支
            
        Returns:
            CheckMRResult: 差异结果，包含被修改的函数信息
        """
        try:
            # 获取分支差异
            diff_files = self.get_diff_between_branches(
                repo_dir=repo_path,
                base_branch=base_branch,
                dev_branch=target_branch
            )
            
            if not diff_files:
                logger.info(f"分支 {target_branch} 相对于 {base_branch} 没有差异")
                return None
            
            # 转换为CheckMRResult格式
            from gate_keeper.application.dto.result import (CheckMRResult,
                                                            DiffResult)
            
            diff_results = []
            for diff_file in diff_files:
                # 这里需要进一步处理，提取函数级别的差异
                # 暂时创建一个简单的DiffResult
                diff_result = DiffResult(
                    file_path=diff_file.path,
                    old_file_path=diff_file.source_file,
                    change_type='modified' if diff_file.is_modified else 'added' if diff_file.is_added else 'deleted',
                    added_lines=[],
                    deleted_lines=[],
                    affected_functions=[]  # 需要进一步分析函数级别的差异
                )
                diff_results.append(diff_result)
            
            return CheckMRResult(
                mr_id=0,  # 评估模式下没有MR ID
                repo_url=repo_path,
                base_branch=base_branch,
                dev_branch=target_branch,
                diffs=diff_results
            )
            
        except Exception as e:
            logger.error(f"获取分支差异失败: {e}")
            return None

    def keep_branch_update_if_needed(self, repo_dir: str, branch: str, remote: str = "origin") -> bool:
        """兼容性方法：保持分支更新"""
        return self.keep_branch_updated(repo_dir, branch, remote) 