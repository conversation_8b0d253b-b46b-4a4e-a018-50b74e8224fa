import hashlib
import re
from typing import Any, Dict, List, Optional, Tuple

from Levenshtein import ratio

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.domain.value_objects.git import CodeCheckDiscussion
from gate_keeper.shared.log import app_logger as logger


class CommentDeduplicationService:
    """
    评论排重服务，防止同一MR多次提交导致重复评论
    支持完整的代码上下文展示和智能去重
    """
    
    def __init__(self, git_client: IGitPlatformService = None):
        """
        初始化评论排重服务
        
        Args:
            git_client: Git客户端，用于获取现有评论
        """
        self.git_client = git_client
        self.similarity_threshold = 0.65  # 降低相似度阈值
        self.code_hash_enabled = True    # 代码哈希排重开关
        self.text_similarity_enabled = True  # 文本相似度排重开关
    
    def format_comment_with_context(self, comment: str, affected_function: Optional[AffectedFunction] = None) -> str:
        """
        格式化评论，包含完整上下文
        
        Args:
            comment: 原始评论内容
            affected_function: 受影响的函数
            
        Returns:
            str: 格式化后的评论
        """
        formatted_comment = []
        
        # 1. 添加问题描述
        formatted_comment.append(comment)
        
        # 2. 添加被测函数信息
        if affected_function:
            formatted_comment.append("\n<details><summary>检查上下文</summary>\n")
            
            formatted_comment.append("### 检查的函数")
            formatted_comment.append(f"- 函数: `{affected_function.name}`")
            formatted_comment.append(f"- 位置: `{affected_function.filepath}:{affected_function.start_line}-{affected_function.end_line}`")
            
            # 添加函数代码
            formatted_comment.append("\n```python")
            formatted_comment.append(affected_function.code)
            formatted_comment.append("```")
            
            # 3. 添加调用上下文
            if affected_function.related_calls:
                formatted_comment.append("\n### 调用上下文")
                for call in affected_function.related_calls:
                    formatted_comment.append(f"- 调用位置: `{call.file_path}:{','.join(map(str, call.line))}`")
                    if call.code:
                        formatted_comment.append("```python")
                        formatted_comment.append(call.code)
                        formatted_comment.append("```")
            
            # 4. 添加相关定义
            if affected_function.related_definitions:
                formatted_comment.append("\n### 相关定义")
                for definition in affected_function.related_definitions:
                    formatted_comment.append(f"- 定义: `{definition.name}`")
                    formatted_comment.append("```python")
                    formatted_comment.append(definition.code)
                    formatted_comment.append("```")
            
            # 5. 添加变更信息
            if affected_function.changed_lines:
                formatted_comment.append("\n### 变更行")
                formatted_comment.append(f"变更的行号: {', '.join(map(str, affected_function.changed_lines))}")
                
            formatted_comment.append("\n</details>")
        
        return "\n".join(formatted_comment)

    def _calculate_context_fingerprint(self, af: AffectedFunction) -> str:
        """
        计算代码上下文的指纹
        
        Args:
            af: 受影响的函数
            
        Returns:
            str: 上下文指纹
        """
        if not af:
            return ""
            
        context_elements = []
        
        # 1. 函数本身的代码
        if af.code:
            context_elements.append(af.code)
            
        # 2. 调用上下文
        if af.related_calls:
            for call in af.related_calls:
                if call.code:
                    context_elements.append(call.code)
                    
        # 3. 相关定义
        if af.related_definitions:
            for definition in af.related_definitions:
                if hasattr(definition, 'code') and definition.code:
                    context_elements.append(definition.code)
                    
        # 4. 变更信息
        if af.changed_lines:
            context_elements.append(f"changed_lines:{','.join(map(str, af.changed_lines))}")
            
        # 生成指纹
        combined = "\n".join(context_elements)
        return hashlib.md5(combined.encode('utf-8')).hexdigest()
    
    def _is_duplicate_by_code(self, new_comment: str, existing_comments: List[Dict[str, Any]],
                             affected_function: Optional[AffectedFunction] = None) -> bool:
        """
        基于代码上下文判断是否重复
        
        Args:
            new_comment: 新评论内容
            existing_comments: 现有评论列表
            affected_function: 受影响的函数
            
        Returns:
            bool: 是否重复
        """
        if not self.code_hash_enabled:
            return False
            
        # 计算新评论的代码哈希
        new_hash = self._calculate_code_hash(new_comment, affected_function)
        
        # 遍历现有评论
        for existing in existing_comments:
            # 首先尝试直接匹配评论内容
            if existing.get("body", "") == new_comment:
                return True
        
        # 然后尝试代码上下文匹配
            existing_af = None
            if "affected_function" in existing:
                af_dict = existing["affected_function"]
                if isinstance(af_dict, dict):
                    try:
                        existing_af = AffectedFunction(
                            name=af_dict.get("name", ""),
                            filepath=af_dict.get("filepath", ""),
                            start_line=af_dict.get("start_line", 0),
                            end_line=af_dict.get("end_line", 0),
                            code=af_dict.get("code", ""),
                            changed_lines=af_dict.get("changed_lines", []),
                            related_calls=af_dict.get("related_calls", []),
                            related_definitions=af_dict.get("related_definitions", [])
                        )
                    except Exception as e:
                        logger.error(f"解析受影响函数失败: {e}")
                        continue
                        
            # 计算现有评论的代码哈希
            existing_hash = self._calculate_code_hash(existing.get("body", ""), existing_af)
            
            # 比较代码哈希
            if new_hash == existing_hash:
                    return True
        
        return False
    
    def _is_same_issue(self, comment1: str, comment2: str) -> bool:
        """
        判断是否是相同的问题
        
        Args:
            comment1: 评论1
            comment2: 评论2
            
        Returns:
            bool: 是否相同
        """
        # 1. 移除代码块，只比较问题描述
        clean1 = self._remove_code_blocks(comment1)
        clean2 = self._remove_code_blocks(comment2)
        
        # 2. 计算相似度
        similarity = self._calculate_similarity(clean1, clean2)
        logger.debug(f"问题相似度: {similarity}, 阈值: {self.similarity_threshold}")
        logger.debug(f"问题1: {clean1}")
        logger.debug(f"问题2: {clean2}")
        return similarity >= self.similarity_threshold
        
    def _remove_code_blocks(self, text: str) -> str:
        """
        移除文本中的代码块
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
            
        # 移除 ``` 包围的代码块
        text = re.sub(r'```(?:\w+)?\s*.*?\s*```', '', text, flags=re.DOTALL)
        
        # 移除 ` 包围的行内代码
        text = re.sub(r'`[^`\n]+`', '', text)
        
        # 移除详情块
        text = re.sub(r'<details>.*?</details>', '', text, flags=re.DOTALL)
        
        # 移除多余的空行和空格
        text = re.sub(r'\n\s*\n', '\n', text)
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        return text.strip()
    
    def _is_duplicate_by_similarity(self, new_comment: str, existing_comments: List[Dict[str, Any]]) -> bool:
        """
        基于文本相似度判断是否重复
        
        Args:
            new_comment: 新评论内容
            existing_comments: 现有评论列表
            
        Returns:
            bool: 是否重复
        """
        if not self.text_similarity_enabled:
            return False
            
        if not new_comment:
            return False
            
        # 遍历现有评论
        for existing in existing_comments:
            existing_comment = getattr(existing, "body", "")
            if not existing_comment:
                continue
                
            # 计算相似度
            similarity = self._calculate_similarity(new_comment, existing_comment)
            if similarity >= self.similarity_threshold:
                return True
        
        return False
    
    def _calculate_code_hash(self, comment: str, affected_function: Optional[AffectedFunction] = None) -> str:
        """
        计算评论的代码哈希值
        
        Args:
            comment: 评论内容
            affected_function: 受影响的函数
            
        Returns:
            str: 哈希值
        """
        # 提取代码块
        code_blocks = self._extract_code_blocks(comment)
        
        # 如果有受影响的函数，也包含其代码和位置信息
        if affected_function:
            if affected_function.code:
                code_blocks.append(affected_function.code)
            if affected_function.filepath and affected_function.start_line and affected_function.end_line:
                code_blocks.append(f"{affected_function.filepath}:{affected_function.start_line}-{affected_function.end_line}")
            if affected_function.changed_lines:
                code_blocks.append(f"changed_lines:{','.join(map(str, affected_function.changed_lines))}")
        
        # 合并所有代码并计算哈希
        combined_code = "\n".join(code_blocks)
        if not combined_code.strip():
            # 如果没有代码，使用评论内容本身
            combined_code = comment
        
        return hashlib.md5(combined_code.encode('utf-8')).hexdigest()
    
    def _extract_code_blocks(self, comment: str) -> List[str]:
        """
        从评论中提取代码块
        
        Args:
            comment: 评论内容
            
        Returns:
            List[str]: 代码块列表
        """
        if not comment:
            return []
            
        code_blocks = []
        
        # 匹配 ``` 包围的代码块
        code_pattern = r'```(?:\w+)?\n(.*?)\n```'
        matches = re.findall(code_pattern, comment, re.DOTALL)
        code_blocks.extend(matches)
        
        # 匹配 ` 包围的行内代码
        inline_pattern = r'`([^`]+)`'
        inline_matches = re.findall(inline_pattern, comment)
        code_blocks.extend(inline_matches)
        
        return code_blocks
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度（使用编辑距离）
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 预处理文本
        text1 = text1.lower().strip()
        text2 = text2.lower().strip()
        
        # 使用 Levenshtein 距离计算相似度
        return ratio(text1, text2) 

    def _to_discussion_obj(self, raw: dict) -> CodeCheckDiscussion:
        return CodeCheckDiscussion(
            id=raw.get("id"),
            body=raw.get("body"),
            mr_id=raw.get("mr_id"),
            project_id=raw.get("project_id"),
            commit_id=raw.get("commit_id"),
            checked_code=raw.get("checked_code"),
            resolved=raw.get("resolved", False)
        )
            
    def get_existing_comments(self, project_id: str, mr_id: int) -> List[CodeCheckDiscussion]:
        """
        获取MR的现有评论列表，返回领域对象
        """
        comments = self.git_client.get_discussions_of_mr(project_id, mr_id)
        return comments
        
    def should_skip_comment(self, project_id:str, mr_id: int, new_comment: str,
                           affected_function: Optional[AffectedFunction] = None) -> Tuple[bool, str, list]:
        """
        判断是否应该跳过评论，支持反打开逻辑
        Returns: (should_skip, reason, need_reopen_ids)
        """
        try:
            if not new_comment:
                return True, "评论内容为空", []
            existing_comments = self.get_existing_comments(project_id, mr_id)
            if not existing_comments:
                return False, "无现有评论", []
            need_reopen_ids = []
            for comment in existing_comments:
                # 判断同一份代码
                if affected_function and comment.checked_code:
                    new_code_hash = self._calculate_code_hash(new_comment, affected_function)
                    if new_code_hash != comment.checked_code:
                        continue
                # 判断结果一致
                if self._is_same_issue(new_comment, comment.body):
                    if not comment.resolved:
                        return True, "重复未解决", []
                    else:
                        need_reopen_ids.append(comment.id)
            if need_reopen_ids:
                return False, "需反打开", need_reopen_ids
            return False, "无重复", []
        except Exception as e:
            logger.error(f"排重检测失败: {e}")
            return False, f"排重检测失败: {str(e)}", [] 

    def get_duplicate_analysis(self, project_id: str, mr_id: int, new_comment: str,
                             affected_function: Optional[AffectedFunction] = None) -> Dict[str, Any]:
        """
        获取详细的重复分析结果，主要用于测试和调试
        
        Args:
            project_id: 项目ID
            mr_id: MR ID
            new_comment: 新评论内容
            affected_function: 受影响的函数
            
        Returns:
            包含详细分析结果的字典
        """
        try:
            if not new_comment:
                return {
                    "error": "评论内容为空",
                    "total_existing": 0,
                    "code_hash_matches": [],
                    "similarity_matches": []
                }
            
            existing_comments = self.get_existing_comments(project_id, mr_id)
            
            result = {
                "total_existing": len(existing_comments),
                "code_hash_matches": [],
                "similarity_matches": []
            }
            
            # 分析每个现有评论
            for comment in existing_comments:
                # 检查代码哈希匹配
                if self.code_hash_enabled and affected_function:
                    new_hash = self._calculate_code_hash(new_comment, affected_function)
                    
                    # 检查现有评论是否有相同的代码哈希
                    if hasattr(comment, 'checked_code') and comment.checked_code == new_hash:
                        result["code_hash_matches"].append({
                            "id": comment.id,
                            "body": comment.body,
                            "hash_match": True
                        })
                
                # 检查文本相似度匹配
                if self.text_similarity_enabled and comment.body:
                    similarity = self._calculate_similarity(new_comment, comment.body)
                    result["similarity_matches"].append({
                        "id": comment.id,
                        "body": comment.body,
                        "similarity": similarity
                    })
            
            # 按相似度排序
            result["similarity_matches"].sort(key=lambda x: x["similarity"], reverse=True)
            
            return result
            
        except Exception as e:
            logger.error(f"重复分析失败: {e}")
            return {
                "error": f"分析失败: {str(e)}",
                "total_existing": 0,
                "code_hash_matches": [],
                "similarity_matches": []
            } 