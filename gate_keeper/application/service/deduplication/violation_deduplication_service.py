import re
from typing import List, Tuple

from Levenshtein import ratio

from gate_keeper.domain.value_objects.analysis_result import ViolationItem
from gate_keeper.shared.log import app_logger as logger


class ViolationDeduplicationService:
    """
    ViolationItem相似度排重服务，用于在聚合ViolationItem时去除重复的违规信息
    """
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        初始化ViolationItem排重服务
        
        Args:
            similarity_threshold: 相似度阈值，超过此阈值认为重复 (0-1)
        """
        self.similarity_threshold = similarity_threshold
    
    def deduplicate_violations(self, violations: List[ViolationItem]) -> List[ViolationItem]:
        """
        对ViolationItem列表进行相似度排重
        
        Args:
            violations: 原始ViolationItem列表
            
        Returns:
            List[ViolationItem]: 排重后的ViolationItem列表
        """
        if not violations:
            return []
        
        if len(violations) == 1:
            return violations
        
        # 首先过滤掉无效的违规项
        valid_violations = []
        for violation in violations:
            # 过滤掉"unknown"规则ID的违规项
            if violation.rule_id == "unknown":
                logger.warning(f"过滤掉无效的违规项: rule_id=unknown, message={violation.message[:50]}...")
                continue
            
            # 过滤掉包含"未检测到明显的代码规范问题"的违规项
            if violation.message and "未检测到明显的代码规范问题" in violation.message:
                logger.warning(f"过滤掉无效的违规项: message包含无效内容, rule_id={violation.rule_id}")
                continue
            
            valid_violations.append(violation)
        
        if not valid_violations:
            logger.info("所有违规项都被过滤掉，返回空列表")
            return []
        
        if len(valid_violations) == 1:
            return valid_violations
        
        # 按规则ID分组，相同规则ID的违规项更容易重复
        violations_by_rule = {}
        for violation in valid_violations:
            rule_id = violation.rule_id or "unknown"
            if rule_id not in violations_by_rule:
                violations_by_rule[rule_id] = []
            violations_by_rule[rule_id].append(violation)
        
        deduplicated_violations = []
        
        # 对每个规则ID组内的违规项进行排重
        for rule_id, rule_violations in violations_by_rule.items():
            if len(rule_violations) == 1:
                deduplicated_violations.extend(rule_violations)
                continue
            
            # 对同一规则ID的违规项进行相似度排重
            unique_violations = self._deduplicate_by_similarity(rule_violations)
            deduplicated_violations.extend(unique_violations)
        
        # 最后对所有违规项进行跨规则ID的排重
        final_deduplicated = self._deduplicate_by_similarity(deduplicated_violations)
        
        logger.info(f"ViolationItem排重: 原始数量={len(violations)}, 排重后数量={len(final_deduplicated)}")
        
        return final_deduplicated
    
    def _deduplicate_by_similarity(self, violations: List[ViolationItem]) -> List[ViolationItem]:
        """
        基于相似度对ViolationItem列表进行排重
        
        Args:
            violations: ViolationItem列表
            
        Returns:
            List[ViolationItem]: 排重后的列表
        """
        if len(violations) <= 1:
            return violations
        
        unique_violations = []
        processed_indices = set()
        
        for i, violation in enumerate(violations):
            if i in processed_indices:
                continue
            
            # 将当前违规项添加到唯一列表
            unique_violations.append(violation)
            processed_indices.add(i)
            
            # 与后续的违规项比较相似度
            for j in range(i + 1, len(violations)):
                if j in processed_indices:
                    continue
                
                other_violation = violations[j]
                
                # 计算相似度
                similarity = self._calculate_violation_similarity(violation, other_violation)
                
                if similarity >= self.similarity_threshold:
                    logger.debug(f"发现重复ViolationItem: 相似度={similarity:.3f}")
                    logger.debug(f"  违规项1: {violation.rule_id} - {violation.message[:50]}...")
                    logger.debug(f"  违规项2: {other_violation.rule_id} - {other_violation.message[:50]}...")
                    processed_indices.add(j)
        
        return unique_violations
    
    def _calculate_violation_similarity(self, violation1: ViolationItem, violation2: ViolationItem) -> float:
        """
        计算两个ViolationItem的相似度
        
        Args:
            violation1: 违规项1
            violation2: 违规项2
            
        Returns:
            float: 相似度 (0-1)
        """
        # 提取用于比较的文本特征
        text1 = self._extract_comparison_text(violation1)
        text2 = self._extract_comparison_text(violation2)
        
        # 计算文本相似度
        similarity = self._calculate_text_similarity(text1, text2)
        
        # 考虑规则ID和严重程度的权重
        rule_id_weight = 0.3
        severity_weight = 0.1
        message_weight = 0.6
        
        # 规则ID匹配度
        rule_id_similarity = 1.0 if violation1.rule_id == violation2.rule_id else 0.0
        
        # 严重程度匹配度
        severity_similarity = 1.0 if violation1.severity == violation2.severity else 0.0
        
        # 综合相似度
        weighted_similarity = (
            rule_id_similarity * rule_id_weight +
            severity_similarity * severity_weight +
            similarity * message_weight
        )
        
        return weighted_similarity
    
    def _extract_comparison_text(self, violation: ViolationItem) -> str:
        """
        提取ViolationItem中用于比较的文本
        
        Args:
            violation: ViolationItem
            
        Returns:
            str: 用于比较的文本
        """
        text_parts = []
        
        # 添加规则内容
        if violation.rule_content:
            text_parts.append(violation.rule_content)
        
        # 添加违规信息
        if violation.message:
            # 清理违规信息，移除代码块和特殊字符
            cleaned_message = self._clean_message_text(violation.message)
            text_parts.append(cleaned_message)
        
        # 添加位置信息
        if violation.location:
            file_path = violation.location.get("file_path", "")
            if file_path:
                text_parts.append(file_path)
        
        return " ".join(text_parts)
    
    def _clean_message_text(self, message: str) -> str:
        """
        清理违规信息文本，移除代码块和特殊字符
        
        Args:
            message: 原始违规信息
            
        Returns:
            str: 清理后的文本
        """
        if not message:
            return ""
        
        # 移除代码块
        cleaned = re.sub(r'```[\s\S]*?```', '', message)
        
        # 移除行内代码
        cleaned = re.sub(r'`[^`]*`', '', cleaned)
        
        # 移除HTML标签
        cleaned = re.sub(r'<[^>]*>', '', cleaned)
        
        # 移除多余空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned.strip()
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 预处理文本
        text1 = text1.lower().strip()
        text2 = text2.lower().strip()
        
        # 如果文本完全相同
        if text1 == text2:
            return 1.0
        
        # 使用 Levenshtein 距离计算相似度
        return ratio(text1, text2)
    
    def get_deduplication_stats(self, original_violations: List[ViolationItem], 
                               deduplicated_violations: List[ViolationItem]) -> dict:
        """
        获取排重统计信息
        
        Args:
            original_violations: 原始违规项列表
            deduplicated_violations: 排重后违规项列表
            
        Returns:
            dict: 统计信息
        """
        original_count = len(original_violations)
        deduplicated_count = len(deduplicated_violations)
        removed_count = original_count - deduplicated_count
        
        stats = {
            "original_count": original_count,
            "deduplicated_count": deduplicated_count,
            "removed_count": removed_count,
            "reduction_rate": removed_count / original_count if original_count > 0 else 0.0
        }
        
        # 按规则ID统计
        rule_stats = {}
        for violation in original_violations:
            rule_id = violation.rule_id or "unknown"
            if rule_id not in rule_stats:
                rule_stats[rule_id] = {"original": 0, "deduplicated": 0}
            rule_stats[rule_id]["original"] += 1
        
        for violation in deduplicated_violations:
            rule_id = violation.rule_id or "unknown"
            if rule_id not in rule_stats:
                rule_stats[rule_id] = {"original": 0, "deduplicated": 0}
            rule_stats[rule_id]["deduplicated"] += 1
        
        stats["rule_stats"] = rule_stats
        
        return stats 