"""
基于Embedding的规则匹配器

提供基于语义相似度的智能规则预筛选功能
"""

import json
import time
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import numpy as np

from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.shared.log import app_logger as logger


class EmbeddingCache:
    """Embedding缓存管理器"""
    
    def __init__(self, cache_file: Optional[str] = None, max_cache_size: int = 10000):
        self.cache_file = Path(cache_file) if cache_file else None
        self.max_cache_size = max_cache_size
        self._cache: Dict[str, np.ndarray] = {}
        self._access_count: Dict[str, int] = {}
        self._load_cache()
    
    def _load_cache(self):
        """从文件加载缓存"""
        if self.cache_file and self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for key, embedding_list in data.items():
                        self._cache[key] = np.array(embedding_list)
                        self._access_count[key] = 1
                logger.info(f"Loaded {len(self._cache)} embeddings from cache")
            except Exception as e:
                logger.warning(f"Failed to load embedding cache: {e}")
    
    def _save_cache(self):
        """保存缓存到文件"""
        if not self.cache_file:
            return
        
        try:
            # 只保存最常用的embeddings
            sorted_items = sorted(
                self._cache.items(),
                key=lambda x: self._access_count.get(x[0], 0),
                reverse=True
            )[:self.max_cache_size]
            
            cache_data = {key: embedding.tolist() for key, embedding in sorted_items}
            
            self.cache_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f)
            
            logger.debug(f"Saved {len(cache_data)} embeddings to cache")
        except Exception as e:
            logger.warning(f"Failed to save embedding cache: {e}")
    
    def get(self, key: str) -> Optional[np.ndarray]:
        """获取缓存的embedding"""
        if key in self._cache:
            self._access_count[key] = self._access_count.get(key, 0) + 1
            return self._cache[key]
        return None
    
    def put(self, key: str, embedding: np.ndarray):
        """存储embedding到缓存"""
        if len(self._cache) >= self.max_cache_size:
            self._evict_least_used()
        
        self._cache[key] = embedding
        self._access_count[key] = 1
    
    def _evict_least_used(self):
        """淘汰最少使用的embedding"""
        if not self._access_count:
            return
        
        least_used_key = min(self._access_count, key=self._access_count.get)
        self._cache.pop(least_used_key, None)
        self._access_count.pop(least_used_key, None)
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()
        self._access_count.clear()
    
    def save(self):
        """手动保存缓存"""
        self._save_cache()


class EmbeddingMatcher:
    """基于Embedding的规则匹配器"""
    
    def __init__(self, 
                 embedding_model: str = "text-embedding-ada-002",
                 similarity_threshold: float = 0.7,
                 cache_file: Optional[str] = None):
        """
        初始化Embedding匹配器
        
        Args:
            embedding_model: 使用的embedding模型
            similarity_threshold: 相似度阈值
            cache_file: 缓存文件路径
        """
        self.embedding_model = embedding_model
        self.similarity_threshold = similarity_threshold
        self.cache = EmbeddingCache(cache_file)
        self._rule_embeddings: Dict[str, np.ndarray] = {}
        self._rules_indexed = False
    
    def _get_embedding_client(self):
        """获取embedding客户端"""
        # 这里可以根据配置选择不同的embedding服务
        # 例如：OpenAI, HuggingFace, 本地模型等
        try:
            import openai
            return openai
        except ImportError:
            logger.error("OpenAI client not available, please install openai package")
            return None
    
    def _compute_embedding(self, text: str) -> Optional[np.ndarray]:
        """计算文本的embedding"""
        # 先检查缓存
        cached_embedding = self.cache.get(text)
        if cached_embedding is not None:
            return cached_embedding
        
        try:
            client = self._get_embedding_client()
            if not client:
                return None
            
            response = client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            
            embedding = np.array(response.data[0].embedding)
            
            # 存储到缓存
            self.cache.put(text, embedding)
            
            return embedding
            
        except Exception as e:
            logger.warning(f"Failed to compute embedding for text: {e}")
            return None
    
    def _compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个embedding的余弦相似度"""
        try:
            # 余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.warning(f"Failed to compute similarity: {e}")
            return 0.0
    
    def index_rules(self, rules: List[CodeCheckRule]) -> bool:
        """为规则建立embedding索引"""
        logger.info(f"Building embedding index for {len(rules)} rules...")
        
        success_count = 0
        for rule in rules:
            # 构建规则的文本表示
            rule_text = self._build_rule_text(rule)
            
            # 计算embedding
            embedding = self._compute_embedding(rule_text)
            if embedding is not None:
                self._rule_embeddings[rule.id] = embedding
                success_count += 1
        
        self._rules_indexed = True
        logger.info(f"Successfully indexed {success_count}/{len(rules)} rules")
        
        # 保存缓存
        self.cache.save()
        
        return success_count > 0
    
    def _build_rule_text(self, rule: CodeCheckRule) -> str:
        """构建规则的文本表示用于embedding"""
        parts = []
        
        # 规则名称和描述
        if rule.name:
            parts.append(f"规则名称: {rule.name}")
        if rule.description:
            parts.append(f"规则描述: {rule.description}")
        if rule.rule_value:
            parts.append(f"规则内容: {rule.rule_value}")
        
        # 建议内容
        if rule.suggestion:
            parts.append(f"检查建议: {rule.suggestion}")
        
        # 分类信息
        if rule.category:
            parts.append(f"分类: {', '.join(rule.category)}")
        
        # 示例代码（如果有）
        if rule.example:
            parts.append(f"示例: {rule.example}")
        
        return " ".join(parts)
    
    def _build_code_text(self, code_element: CodeElement) -> str:
        """构建代码元素的文本表示用于embedding"""
        parts = []
        
        # 代码内容
        if hasattr(code_element, 'code') and code_element.code:
            parts.append(f"代码内容: {code_element.code}")
        
        # 元素信息
        if hasattr(code_element, 'name') and code_element.name:
            parts.append(f"元素名称: {code_element.name}")
        if hasattr(code_element, 'type') and code_element.type:
            parts.append(f"元素类型: {code_element.type}")
        
        # 文件路径信息
        if hasattr(code_element, 'filepath') and code_element.filepath:
            file_path = Path(code_element.filepath)
            parts.append(f"文件类型: {file_path.suffix}")
        
        return " ".join(parts)
    
    def get_matching_rules(self, code_element: CodeElement, rules: List[CodeCheckRule]) -> List[Tuple[CodeCheckRule, float]]:
        """获取与代码元素匹配的规则及其相似度分数"""
        if not self._rules_indexed:
            if not self.index_rules(rules):
                logger.warning("Failed to index rules, falling back to all rules")
                return [(rule, 1.0) for rule in rules]
        
        # 构建代码文本并计算embedding
        code_text = self._build_code_text(code_element)
        code_embedding = self._compute_embedding(code_text)
        
        if code_embedding is None:
            logger.warning("Failed to compute code embedding, returning all rules")
            return [(rule, 1.0) for rule in rules]
        
        # 计算与所有规则的相似度
        matching_rules = []
        for rule in rules:
            if rule.id in self._rule_embeddings:
                rule_embedding = self._rule_embeddings[rule.id]
                similarity = self._compute_similarity(code_embedding, rule_embedding)
                
                if similarity >= self.similarity_threshold:
                    matching_rules.append((rule, similarity))
        
        # 按相似度降序排列
        matching_rules.sort(key=lambda x: x[1], reverse=True)
        
        logger.debug(f"Found {len(matching_rules)} matching rules for code element {code_element.name}")
        
        return matching_rules
    
    def get_applicable_rules(self, code_element: CodeElement, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """获取适用的规则（只返回规则，不返回分数）"""
        matching_rules = self.get_matching_rules(code_element, rules)
        return [rule for rule, _ in matching_rules]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取匹配器统计信息"""
        return {
            "embedding_model": self.embedding_model,
            "similarity_threshold": self.similarity_threshold,
            "indexed_rules": len(self._rule_embeddings),
            "cache_size": len(self.cache._cache),
            "rules_indexed": self._rules_indexed
        }
    
    def update_threshold(self, new_threshold: float):
        """更新相似度阈值"""
        self.similarity_threshold = new_threshold
        logger.info(f"Updated similarity threshold to {new_threshold}")
    
    def clear_index(self):
        """清空规则索引"""
        self._rule_embeddings.clear()
        self._rules_indexed = False
        logger.info("Cleared rule embedding index")
    
    def __del__(self):
        """析构函数，保存缓存"""
        try:
            self.cache.save()
        except:
            pass
