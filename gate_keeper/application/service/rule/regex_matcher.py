"""
正则表达式匹配器

用于基于match_regex字段对代码进行预筛选，提高规则检查的精度和性能
"""

import re
import signal
import threading
import time
from typing import Dict, List, Optional, Pattern

from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.shared.log import app_logger as logger


class RegexCache:
    """正则表达式编译缓存"""
    
    _cache: Dict[str, Pattern] = {}
    _max_cache_size: int = 1000
    _access_count: Dict[str, int] = {}
    
    @classmethod
    def get_compiled_regex(cls, pattern: str) -> Optional[Pattern]:
        """
        获取编译后的正则表达式，支持缓存
        
        Args:
            pattern: 正则表达式模式
            
        Returns:
            编译后的正则表达式对象，如果编译失败返回None
        """
        if not pattern:
            return None
            
        if pattern in cls._cache:
            cls._access_count[pattern] = cls._access_count.get(pattern, 0) + 1
            return cls._cache[pattern]
        
        try:
            compiled_regex = re.compile(pattern, re.MULTILINE | re.DOTALL)
            
            # 缓存管理：如果缓存已满，移除最少使用的项
            if len(cls._cache) >= cls._max_cache_size:
                cls._evict_least_used()
            
            cls._cache[pattern] = compiled_regex
            cls._access_count[pattern] = 1
            return compiled_regex
            
        except re.error as e:
            logger.warning(f"Invalid regex pattern: {pattern}, error: {e}")
            return None
    
    @classmethod
    def _evict_least_used(cls):
        """移除最少使用的缓存项"""
        if not cls._access_count:
            return
            
        # 找到访问次数最少的模式
        least_used_pattern = min(cls._access_count, key=cls._access_count.get)
        
        # 移除缓存
        cls._cache.pop(least_used_pattern, None)
        cls._access_count.pop(least_used_pattern, None)
        
        logger.debug(f"Evicted regex pattern from cache: {least_used_pattern}")
    
    @classmethod
    def clear_cache(cls):
        """清空缓存"""
        cls._cache.clear()
        cls._access_count.clear()
        logger.info("Regex cache cleared")
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, int]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(cls._cache),
            "max_cache_size": cls._max_cache_size,
            "total_patterns": len(cls._access_count)
        }


class RegexMatcher:
    """正则表达式匹配器"""
    
    def __init__(self, timeout: float = 1.0):
        """
        初始化匹配器
        
        Args:
            timeout: 单个正则匹配的超时时间（秒）
        """
        self.timeout = timeout
    
    def match_code(self, code: str, regex_pattern: str) -> bool:
        """
        检查代码是否匹配正则表达式

        Args:
            code: 要检查的代码内容
            regex_pattern: 正则表达式模式

        Returns:
            是否匹配
        """
        if not code or not regex_pattern:
            return False

        compiled_regex = RegexCache.get_compiled_regex(regex_pattern)
        if not compiled_regex:
            return False

        return self._match_with_timeout(compiled_regex, code, regex_pattern)

    def _match_with_timeout(self, compiled_regex: Pattern, code: str, regex_pattern: str) -> bool:
        """
        带超时的正则匹配

        Args:
            compiled_regex: 编译后的正则表达式
            code: 要检查的代码内容
            regex_pattern: 原始正则表达式模式（用于日志）

        Returns:
            是否匹配
        """
        # 对于已知的危险模式，直接返回False
        dangerous_patterns = [
            r"(a+)+b",  # 灾难性回溯
            r"(a*)*b",  # 灾难性回溯
            r"(a+)+$",  # 灾难性回溯
            r"(a*)*$",  # 灾难性回溯
        ]

        for dangerous in dangerous_patterns:
            if regex_pattern.strip() == dangerous:
                logger.warning(f"Skipping dangerous regex pattern: {regex_pattern}")
                return False

        # 对于超长的代码，限制长度以避免性能问题
        if len(code) > 10000:  # 10KB限制
            logger.warning(f"Code too long ({len(code)} chars), truncating for regex matching")
            code = code[:10000]

        result = [None]
        exception = [None]

        def match_worker():
            try:
                start_time = time.time()
                match = compiled_regex.search(code)
                elapsed = time.time() - start_time

                if elapsed > self.timeout:
                    logger.warning(f"Regex matching slow ({elapsed:.3f}s): {regex_pattern[:50]}...")

                result[0] = match is not None
            except Exception as e:
                exception[0] = e

        # 使用线程执行匹配，设置超时
        thread = threading.Thread(target=match_worker)
        thread.daemon = True
        thread.start()
        thread.join(timeout=self.timeout)

        if thread.is_alive():
            # 超时了
            logger.warning(f"Regex matching timeout ({self.timeout}s): {regex_pattern[:50]}...")
            return False

        if exception[0]:
            logger.warning(f"Regex matching error: {exception[0]}, pattern: {regex_pattern[:50]}...")
            return False

        return result[0] if result[0] is not None else False
    
    def get_matching_rules(self, code: str, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """
        获取匹配代码的规则列表

        Args:
            code: 要检查的代码内容
            rules: 规则列表

        Returns:
            匹配的规则列表
        """
        if not rules:
            return []

        if not code:
            # 如果没有代码内容，返回所有没有regex要求的规则
            return [rule for rule in rules if not rule.match_regex]

        matching_rules = []

        for rule in rules:
            try:
                # 如果规则没有match_regex，则认为适用于所有代码
                if not rule.match_regex:
                    matching_rules.append(rule)
                    continue

                # 检查是否匹配
                if self.match_code(code, rule.match_regex):
                    matching_rules.append(rule)
            except Exception as e:
                logger.warning(f"Error processing rule {rule.id}: {e}")
                # 出错时，如果规则没有regex要求，仍然包含它
                if not rule.match_regex:
                    matching_rules.append(rule)

        return matching_rules
    
    def batch_match_rules(self, code: str, rules: List[CodeCheckRule]) -> Dict[str, List[CodeCheckRule]]:
        """
        批量匹配规则，返回分类结果
        
        Args:
            code: 要检查的代码内容
            rules: 规则列表
            
        Returns:
            分类结果：{"matched": [匹配的规则], "unmatched": [未匹配的规则], "no_regex": [无regex的规则]}
        """
        result = {
            "matched": [],
            "unmatched": [],
            "no_regex": []
        }
        
        if not code:
            result["no_regex"] = rules
            return result
        
        for rule in rules:
            if not rule.match_regex:
                result["no_regex"].append(rule)
            elif self.match_code(code, rule.match_regex):
                result["matched"].append(rule)
            else:
                result["unmatched"].append(rule)
        
        return result
    
    def extract_code_content(self, check_item: CodeElement) -> str:
        """
        从代码元素中提取用于匹配的代码内容

        Args:
            check_item: 代码元素

        Returns:
            提取的代码内容
        """
        if check_item is None:
            logger.warning("Code element is None")
            return ""

        # 优先使用代码元素的内容
        if hasattr(check_item, 'code') and check_item.code:
            return str(check_item.code)

        # 如果没有内容，尝试从文件中读取
        if hasattr(check_item, 'filepath') and check_item.filepath:
            try:
                with open(check_item.filepath, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                # 如果有行号信息，只提取相关行
                if (hasattr(check_item, 'start_line') and hasattr(check_item, 'end_line') and
                    check_item.start_line is not None and check_item.end_line is not None):
                    start_line = max(0, check_item.start_line - 1)  # 转换为0基索引
                    end_line = min(len(lines), check_item.end_line)
                    if start_line < end_line:
                        return ''.join(lines[start_line:end_line])
                    else:
                        return ""
                else:
                    return ''.join(lines)

            except Exception as e:
                logger.warning(f"Failed to read file {check_item.filepath}: {e}")
                return ""

        # 如果都没有，尝试使用name作为代码内容（最后的备选方案）
        if hasattr(check_item, 'name') and check_item.name:
            logger.debug(f"Using element name as code content: {check_item.name}")
            return str(check_item.name)

        logger.debug("No code content found in element")
        return ""
    
    def get_applicable_rules_with_regex(self, check_item: CodeElement, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """
        基于match_regex筛选适用规则

        Args:
            check_item: 代码元素
            rules: 规则列表

        Returns:
            适用的规则列表
        """
        if not rules:
            return []

        if check_item is None:
            logger.warning("Code element is None, returning rules without regex requirements")
            return [rule for rule in rules if not rule.match_regex]

        try:
            code_content = self.extract_code_content(check_item)
            return self.get_matching_rules(code_content, rules)
        except Exception as e:
            logger.error(f"Error in regex-based rule filtering: {e}")
            # 出错时返回所有没有regex要求的规则
            return [rule for rule in rules if not rule.match_regex]
