"""
规则预筛选管理器

支持多种预筛选策略：正则表达式、embedding匹配、无预筛选
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from gate_keeper.application.service.rule.embedding_matcher import \
    EmbeddingMatcher
from gate_keeper.application.service.rule.regex_matcher import RegexMatcher
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.shared.log import app_logger as logger


class PreScreeningStrategy(Enum):
    """预筛选策略枚举"""
    NONE = "none"                    # 不使用预筛选
    REGEX = "regex"                  # 基于正则表达式
    EMBEDDING = "embedding"          # 基于embedding相似度
    HYBRID = "hybrid"                # 混合策略（regex + embedding）


@dataclass
class PreScreeningConfig:
    """预筛选配置"""
    strategy: PreScreeningStrategy = PreScreeningStrategy.REGEX
    
    # 正则表达式配置
    regex_timeout: float = 1.0
    regex_cache_size: int = 1000
    
    # Embedding配置
    embedding_model: str = "text-embedding-ada-002"
    embedding_similarity_threshold: float = 0.7
    embedding_cache_file: Optional[str] = "data/embedding_cache.json"
    
    # 混合策略配置
    hybrid_regex_weight: float = 0.3
    hybrid_embedding_weight: float = 0.7
    hybrid_min_score: float = 0.5
    
    # 性能配置
    enable_fallback: bool = True     # 启用降级策略
    max_processing_time: float = 5.0 # 最大处理时间（秒）
    
    # 调试配置
    enable_stats: bool = True        # 启用统计信息
    log_performance: bool = False    # 记录性能日志


class RulePreScreeningManager:
    """规则预筛选管理器"""
    
    def __init__(self, config: PreScreeningConfig):
        self.config = config
        self.regex_matcher: Optional[RegexMatcher] = None
        self.embedding_matcher: Optional[EmbeddingMatcher] = None
        self.stats = {
            "total_requests": 0,
            "strategy_usage": {},
            "performance": {},
            "fallback_count": 0
        }
        
        self._initialize_matchers()
    
    def _initialize_matchers(self):
        """初始化匹配器"""
        if self.config.strategy in [PreScreeningStrategy.REGEX, PreScreeningStrategy.HYBRID]:
            self.regex_matcher = RegexMatcher(timeout=self.config.regex_timeout)
            logger.info("Initialized regex matcher")
        
        if self.config.strategy in [PreScreeningStrategy.EMBEDDING, PreScreeningStrategy.HYBRID]:
            self.embedding_matcher = EmbeddingMatcher(
                embedding_model=self.config.embedding_model,
                similarity_threshold=self.config.embedding_similarity_threshold,
                cache_file=self.config.embedding_cache_file
            )
            logger.info("Initialized embedding matcher")
    
    def get_applicable_rules(self, code_element: CodeElement, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """获取适用的规则"""
        import time
        start_time = time.time()

        # 边界条件检查
        if not rules:
            logger.debug("No rules provided, returning empty list")
            return []

        if code_element is None:
            logger.warning("Code element is None, returning all rules")
            return rules

        self.stats["total_requests"] += 1
        strategy_name = self.config.strategy.value
        self.stats["strategy_usage"][strategy_name] = self.stats["strategy_usage"].get(strategy_name, 0) + 1

        try:
            # 根据策略选择处理方法
            if self.config.strategy == PreScreeningStrategy.NONE:
                result = self._no_prescreening(rules)
            elif self.config.strategy == PreScreeningStrategy.REGEX:
                result = self._regex_prescreening(code_element, rules)
            elif self.config.strategy == PreScreeningStrategy.EMBEDDING:
                result = self._embedding_prescreening(code_element, rules)
            elif self.config.strategy == PreScreeningStrategy.HYBRID:
                result = self._hybrid_prescreening(code_element, rules)
            else:
                logger.warning(f"Unknown strategy: {self.config.strategy}, falling back to no prescreening")
                result = self._no_prescreening(rules)

            processing_time = time.time() - start_time

            # 检查处理时间
            if processing_time > self.config.max_processing_time:
                logger.warning(f"Prescreening took too long: {processing_time:.2f}s")
                if self.config.enable_fallback:
                    self.stats["fallback_count"] += 1
                    result = self._no_prescreening(rules)

            # 确保结果不为None
            if result is None:
                logger.warning("Prescreening returned None, falling back to all rules")
                result = self._no_prescreening(rules)

            # 记录性能统计
            if self.config.enable_stats:
                self.stats["performance"][strategy_name] = self.stats["performance"].get(strategy_name, [])
                self.stats["performance"][strategy_name].append(processing_time)

            if self.config.log_performance:
                logger.debug(f"Prescreening completed in {processing_time:.3f}s, "
                           f"filtered {len(rules)} -> {len(result)} rules")

            return result

        except Exception as e:
            logger.error(f"Prescreening failed: {e}")
            if self.config.enable_fallback:
                self.stats["fallback_count"] += 1
                return self._no_prescreening(rules)
            else:
                raise
    
    def _no_prescreening(self, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """不使用预筛选，返回所有规则"""
        return rules
    
    def _regex_prescreening(self, code_element: CodeElement, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """基于正则表达式的预筛选"""
        if not self.regex_matcher:
            logger.warning("Regex matcher not initialized, falling back to no prescreening")
            return self._no_prescreening(rules)

        try:
            result = self.regex_matcher.get_applicable_rules_with_regex(code_element, rules)
            if result is None:
                logger.warning("Regex matcher returned None, falling back to no prescreening")
                return self._no_prescreening(rules)
            return result
        except Exception as e:
            logger.warning(f"Regex prescreening failed: {e}, falling back to no prescreening")
            return self._no_prescreening(rules)
    
    def _embedding_prescreening(self, code_element: CodeElement, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """基于embedding的预筛选"""
        if not self.embedding_matcher:
            logger.warning("Embedding matcher not initialized, falling back to no prescreening")
            return self._no_prescreening(rules)

        try:
            result = self.embedding_matcher.get_applicable_rules(code_element, rules)
            if result is None:
                logger.warning("Embedding matcher returned None, falling back to no prescreening")
                return self._no_prescreening(rules)
            return result
        except Exception as e:
            logger.warning(f"Embedding prescreening failed: {e}, falling back to no prescreening")
            return self._no_prescreening(rules)
    
    def _hybrid_prescreening(self, code_element: CodeElement, rules: List[CodeCheckRule]) -> List[CodeCheckRule]:
        """混合策略预筛选"""
        if not self.regex_matcher or not self.embedding_matcher:
            logger.warning("Hybrid matchers not fully initialized, falling back to available matcher")
            if self.regex_matcher:
                return self._regex_prescreening(code_element, rules)
            elif self.embedding_matcher:
                return self._embedding_prescreening(code_element, rules)
            else:
                return self._no_prescreening(rules)

        try:
            # 获取正则匹配结果
            regex_rules = self.regex_matcher.get_applicable_rules_with_regex(code_element, rules)
            if regex_rules is None:
                regex_rules = []

            # 获取embedding匹配结果（带分数）
            embedding_results = self.embedding_matcher.get_matching_rules(code_element, rules)
            if embedding_results is None:
                embedding_results = []

            embedding_rules_dict = {rule.id: score for rule, score in embedding_results}

            # 混合评分
            hybrid_scores = {}
            for rule in rules:
                regex_score = 1.0 if rule in regex_rules else 0.0
                embedding_score = embedding_rules_dict.get(rule.id, 0.0)

                # 加权平均
                hybrid_score = (
                    self.config.hybrid_regex_weight * regex_score +
                    self.config.hybrid_embedding_weight * embedding_score
                )

                if hybrid_score >= self.config.hybrid_min_score:
                    hybrid_scores[rule.id] = hybrid_score

            # 返回符合条件的规则，按分数排序
            result_rules = [rule for rule in rules if rule.id in hybrid_scores]
            result_rules.sort(key=lambda r: hybrid_scores[r.id], reverse=True)

            return result_rules

        except Exception as e:
            logger.warning(f"Hybrid prescreening failed: {e}, falling back to no prescreening")
            return self._no_prescreening(rules)
    
    def update_config(self, new_config: PreScreeningConfig):
        """更新配置"""
        old_strategy = self.config.strategy
        self.config = new_config
        
        # 如果策略改变，重新初始化匹配器
        if old_strategy != new_config.strategy:
            self._initialize_matchers()
            logger.info(f"Strategy changed from {old_strategy.value} to {new_config.strategy.value}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 添加匹配器统计
        if self.regex_matcher:
            from gate_keeper.application.service.rule.regex_matcher import \
                RegexCache
            stats["regex_cache"] = RegexCache.get_cache_stats()
        
        if self.embedding_matcher:
            stats["embedding"] = self.embedding_matcher.get_stats()
        
        # 计算平均性能
        for strategy, times in stats["performance"].items():
            if times:
                stats["performance"][f"{strategy}_avg"] = sum(times) / len(times)
                stats["performance"][f"{strategy}_max"] = max(times)
                stats["performance"][f"{strategy}_min"] = min(times)
        
        return stats
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            "total_requests": 0,
            "strategy_usage": {},
            "performance": {},
            "fallback_count": 0
        }
        logger.info("Cleared prescreening statistics")
    
    def warm_up(self, rules: List[CodeCheckRule]):
        """预热匹配器（建立索引等）"""
        logger.info("Warming up prescreening matchers...")
        
        if self.embedding_matcher and rules:
            self.embedding_matcher.index_rules(rules)
        
        logger.info("Prescreening warm-up completed")


def create_prescreening_manager(config_dict: Optional[Dict[str, Any]] = None) -> RulePreScreeningManager:
    """创建预筛选管理器的工厂函数"""
    if config_dict is None:
        config_dict = {}
    
    # 解析策略
    strategy_str = config_dict.get("strategy", "regex")
    try:
        strategy = PreScreeningStrategy(strategy_str)
    except ValueError:
        logger.warning(f"Invalid strategy '{strategy_str}', using default 'regex'")
        strategy = PreScreeningStrategy.REGEX
    
    # 创建配置
    config = PreScreeningConfig(
        strategy=strategy,
        regex_timeout=config_dict.get("regex_timeout", 1.0),
        regex_cache_size=config_dict.get("regex_cache_size", 1000),
        embedding_model=config_dict.get("embedding_model", "text-embedding-ada-002"),
        embedding_similarity_threshold=config_dict.get("embedding_similarity_threshold", 0.7),
        embedding_cache_file=config_dict.get("embedding_cache_file", "data/embedding_cache.json"),
        hybrid_regex_weight=config_dict.get("hybrid_regex_weight", 0.3),
        hybrid_embedding_weight=config_dict.get("hybrid_embedding_weight", 0.7),
        hybrid_min_score=config_dict.get("hybrid_min_score", 0.5),
        enable_fallback=config_dict.get("enable_fallback", True),
        max_processing_time=config_dict.get("max_processing_time", 5.0),
        enable_stats=config_dict.get("enable_stats", True),
        log_performance=config_dict.get("log_performance", False)
    )
    
    return RulePreScreeningManager(config)
    
    return RulePreScreeningManager(config)
    
    return RulePreScreeningManager(config)
