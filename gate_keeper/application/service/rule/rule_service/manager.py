# manager.py
# 规则管理器，负责规则的加载、筛选、分组，依赖领域对象CodeCheckRule。
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml

from gate_keeper.application.service.rule.regex_matcher import RegexMatcher
from gate_keeper.application.service.rule.rule_prescreening import (
    PreScreeningConfig, PreScreeningStrategy, RulePreScreeningManager,
    create_prescreening_manager)
from gate_keeper.domain.rule.check_rule import CodeCheckRule
# 只在需要时再import，避免循环依赖
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.infrastructure.doc.excel import reader
from gate_keeper.shared.log import app_logger as logger
from gate_keeper.shared.str import to_string

# 添加全局缓存，避免相同规则的分组日志重复打印
_grouping_log_cache = {}

def _get_config():
    """获取配置，使用延迟导入避免循环依赖"""
    try:
        from gate_keeper.config import config
        return config
    except ImportError:
        # 如果导入失败，返回一个默认配置对象
        class DefaultConfig:
            rule_grouping_strategy = 'adaptive'
            rule_similarity_threshold = 0.7
            min_rule_group_size = 2
            max_rule_group_size = 5
            target_rule_group_size = 3
        return DefaultConfig()


def _log_grouping_statistics(grouped_rules: dict, strategy_name: str, total_rules: int, strategy: str = None, config = None):
    """
    记录规则分组统计信息
    
    Args:
        grouped_rules: 分组后的规则字典
        strategy_name: 分组策略名称
        total_rules: 总规则数
        strategy: 分组策略字符串
        config: 配置对象
    """
    # 生成缓存键：基于规则内容和策略
    cache_key = f"{strategy_name}_{total_rules}_{hash(frozenset((k, len(v)) for k, v in grouped_rules.items()))}"
    
    # 检查是否已经打印过相同的分组统计
    if cache_key in _grouping_log_cache:
        logger.debug(f"跳过重复的规则分组统计日志 - {strategy_name} 策略")
        return
    
    # 记录到缓存
    _grouping_log_cache[cache_key] = True
    
    # 限制缓存大小，避免内存泄漏
    if len(_grouping_log_cache) > 100:
        # 清空缓存
        _grouping_log_cache.clear()
    
    # 打印前置说明（只在首次分组时）
    logger.info(f"开始规则分组，适用规则数: {total_rules}")
    logger.info(f"使用分组策略: {strategy}")
    
    # 根据策略打印特定说明
    if strategy == 'minimize_calls':
        logger.info("使用最小调用次数分组策略")
    elif strategy == 'adaptive' and config:
        min_size = getattr(config, 'min_rule_group_size', 3)
        max_size = getattr(config, 'max_rule_group_size', 8)
        target_size = getattr(config, 'target_rule_group_size', 5)
        logger.info(f"自适应分组参数: min={min_size}, max={max_size}, target={target_size}")
    elif strategy == 'similarity' and config:
        threshold = getattr(config, 'rule_similarity_threshold', 0.7)
        logger.info(f"相似度阈值: {threshold}")
    
    # 打印分组统计信息
    logger.info("=" * 60)
    logger.info(f"规则分组统计 - {strategy_name} 策略")
    logger.info("=" * 60)
    logger.info(f"总规则数: {total_rules}")
    logger.info(f"分组数量: {len(grouped_rules)}")
    
    # 统计每个分组的规则数量
    group_sizes = []
    for group_name, group_rules in grouped_rules.items():
        group_size = len(group_rules)
        group_sizes.append(group_size)
        logger.info(f"  {group_name}: {group_size} 条规则")
    
    # 统计信息
    if group_sizes:
        avg_size = sum(group_sizes) / len(group_sizes)
        min_size = min(group_sizes)
        max_size = max(group_sizes)
        logger.info(f"分组大小统计:")
        logger.info(f"  平均每组: {avg_size:.1f} 条规则")
        logger.info(f"  最小分组: {min_size} 条规则")
        logger.info(f"  最大分组: {max_size} 条规则")
    
    logger.info("=" * 60)


class RuleManager:
    def __init__(self, rule_file_path: Union[str, Path] = None, merge_on: Optional[List[str]] = None,
                 include_sheets: Optional[List[str]] = None, rules: Optional[List[CodeCheckRule]] = None,
                 enable_regex_matching: bool = True, prescreening_config: Optional[Dict[str, Any]] = None):
        """
        初始化规则管理器

        Args:
            rule_file_path: 规则文件路径（字符串或Path对象），可选
            merge_on: Excel 合并字段（可选）
            include_sheets: 包含的 Excel 工作表（可选）
            rules: 直接提供的规则列表（可选，优先级高于文件路径）
            enable_regex_matching: 是否启用正则表达式匹配（默认启用，已废弃，请使用prescreening_config）
            prescreening_config: 预筛选配置字典
        """
        # 统一转换为Path对象
        self.rule_file_path = Path(rule_file_path) if rule_file_path else Path("direct_rules.yaml")
        self.merge_on = merge_on
        self.include_sheets = include_sheets
        self._rules: List[CodeCheckRule] = []
        self._loaded = False
        self._cache_key = None
        # 添加规则分组缓存
        self._grouping_cache = {}

        # 预筛选管理器（新的统一接口）
        if prescreening_config is not None:
            self.prescreening_manager = create_prescreening_manager(prescreening_config)
        else:
            # 向后兼容：如果没有提供prescreening_config，根据enable_regex_matching创建默认配置
            if enable_regex_matching:
                default_config = {"strategy": "regex"}
            else:
                default_config = {"strategy": "none"}
            self.prescreening_manager = create_prescreening_manager(default_config)

        # 保持向后兼容的属性
        self.enable_regex_matching = enable_regex_matching
        self.regex_matcher = RegexMatcher() if enable_regex_matching else None

        # 如果直接提供了规则列表，直接使用
        if rules is not None:
            self._rules = rules
            self._loaded = True
            self._cache_key = "direct_rules"
            # 预热预筛选管理器
            self.prescreening_manager.warm_up(rules)

    def _get_cache_key(self) -> str:
        """生成缓存键，用于判断是否需要重新加载"""
        return f"{self.rule_file_path}:{self.merge_on}:{self.include_sheets}"

    def load_rules(self, force_reload: bool = False) -> List[CodeCheckRule]:
        """
        加载规则，支持缓存和强制重新加载

        Args:
            force_reload: 是否强制重新加载

        Returns:
            规则列表
        """
        # 如果已经直接提供了规则，直接返回
        if self._cache_key == "direct_rules" and self._loaded and not force_reload:
            return self._rules

        current_cache_key = self._get_cache_key()

        if not self._loaded or force_reload or self._cache_key != current_cache_key:
            # 如果是直接提供的规则，不需要从文件加载
            if self._cache_key == "direct_rules":
                pass  # 规则已经在初始化时设置
            else:
                self._rules = self._load_from_file()
            self._loaded = True
            self._cache_key = current_cache_key

        return self._rules

    def _load_from_file(self) -> List[CodeCheckRule]:
        """从文件加载规则"""
        suffix = self.rule_file_path.suffix.lower()
        if suffix == ".xlsx":
            return self._load_excel()
        elif suffix == ".md":
            return self._load_markdown()
        elif suffix in [".yaml", ".yml"]:
            return self._load_yaml()
        else:
            raise ValueError(f"不支持的文件格式: {self.rule_file_path}")

    def _load_excel(self) -> List[CodeCheckRule]:
        """从 Excel 文件加载规则"""
        excel_reader = reader.ExcelReader(str(self.rule_file_path))
        parsed_rules = []
        
        if self.include_sheets is None:
            include_sheets = excel_reader.get_sheet_names()
        else:
            include_sheets = self.include_sheets

        for include_sheet in include_sheets:
            rules = excel_reader.parse_group_by(include_sheet, self.merge_on)
            for rule_dict in rules:
                values = list(rule_dict.values())
                severity = values[2] or "未指定，请自行判断"
                
                # 创建基础规则
                cr = CodeCheckRule(
                    id=values[0],
                    name=values[0],
                    category=[include_sheet, to_string(list(set(values[3]))), to_string(values[1])],
                    rule_value="\n".join(values[4]),
                    description="",
                    enabled=True,
                    severity=severity,
                    file_path=self.rule_file_path,
                    contact_info=None,
                    raw=rule_dict
                )
                
                # 尝试从Excel中加载自定义prompt模板
                # 假设Excel中有额外的列用于自定义prompt模板
                try:
                    # 检查是否有自定义prompt相关的列
                    custom_instruction = rule_dict.get('custom_instruction', '')
                    custom_requirement_format = rule_dict.get('custom_requirement_format', '')
                    custom_example_format = rule_dict.get('custom_example_format', '')
                    custom_output_format = rule_dict.get('custom_output_format', '')
                    context_requirements = rule_dict.get('context_requirements', '')
                    focus_keywords_str = rule_dict.get('focus_keywords', '')
                    
                    # 处理重点关注关键词（假设用逗号分隔）
                    focus_keywords = []
                    if focus_keywords_str:
                        focus_keywords = [kw.strip() for kw in focus_keywords_str.split(',') if kw.strip()]
                    
                    # 如果有任何自定义prompt内容，创建PromptTemplate
                    if any([custom_instruction, custom_requirement_format, custom_example_format,
                           custom_output_format, context_requirements, focus_keywords]):
                        from gate_keeper.domain.rule.check_rule import \
                            PromptTemplate
                        cr.prompt_template = PromptTemplate(
                            instruction=custom_instruction,
                            requirement_template=custom_requirement_format,
                            example_template=custom_example_format,
                            output_format=custom_output_format,
                            context_requirements=context_requirements,
                            focus_points=focus_keywords
                        )
                        
                        logger.debug(f"为规则 {cr.id} 加载了自定义prompt模板")
                        
                except Exception as e:
                    # 如果加载自定义prompt模板失败，记录日志但不影响规则加载
                    logger.debug(f"加载规则 {values[0]} 的自定义prompt模板失败: {e}")
                
                parsed_rules.append(cr)
        return parsed_rules

    def _load_markdown(self) -> List[CodeCheckRule]:
        """从 Markdown 文件加载规则"""
        with open(self.rule_file_path, "r", encoding="utf-8") as f:
            md_text = f.read()
        if not md_text:
            return []

        lines = md_text.splitlines()
        rules = []

        current_category = ""
        current_rule = None
        buffer = []

        category_pattern = re.compile(r"^## (?:\d+\.\s*)?(.+)$")
        rule_pattern = re.compile(r"^### (\d+\.\d+)\s+(?:[A-Z]\.\d+\s+)?(.+)$")
        # 扩展标签模式，支持自定义prompt模板
        tag_pattern = re.compile(r"【(规则|推荐|建议|案例|指令|要求模板|示例模板|输出格式|上下文要求|重点关注)】([\s\S]+?)($|(?=【))")

        def flush_rule():
            if current_rule:
                current_rule.raw = "\n".join(buffer).strip()
                tags = tag_pattern.findall(current_rule.raw)
                
                # 初始化自定义prompt模板
                custom_instruction = ""
                custom_requirement_format = ""
                custom_example_format = ""
                custom_output_format = ""
                context_requirements = ""
                focus_keywords = []
                
                for tag, content, _ in tags:
                    content = content.strip()
                    if tag == "规则":
                        current_rule.rule_value += f"{content}\n"
                    elif tag == "推荐":
                        current_rule.rule_value += f"推荐：{content}\n"
                    elif tag == "建议":
                        current_rule.rule_value += f"建议：{content}\n"
                    elif tag == "案例":
                        # 目前的案例是链接，没有agent无法进行解析,链接会变成干扰项
                        if "http://" not in content and "https://" not in content:
                            current_rule.example += f"{content}\n"
                    elif tag == "指令":
                        custom_instruction += f"{content}\n"
                    elif tag == "要求模板":
                        custom_requirement_format += f"{content}\n"
                    elif tag == "示例模板":
                        custom_example_format += f"{content}\n"
                    elif tag == "输出格式":
                        custom_output_format += f"{content}\n"
                    elif tag == "上下文要求":
                        context_requirements += f"{content}\n"
                    elif tag == "重点关注":
                        # 重点关注点按行分割
                        focus_points = [point.strip() for point in content.split('\n') if point.strip()]
                        focus_keywords.extend(focus_points)

                # 设置规则内容
                current_rule.rule_value = current_rule.rule_value.strip()
                current_rule.example = current_rule.example.strip()
                
                # 设置自定义prompt模板
                if any([custom_instruction, custom_requirement_format, custom_example_format, 
                       custom_output_format, context_requirements, focus_keywords]):
                    # 创建PromptTemplate对象
                    from gate_keeper.domain.rule.check_rule import \
                        PromptTemplate
                    current_rule.prompt_template = PromptTemplate(
                        instruction=custom_instruction.strip(),
                        requirement_template=custom_requirement_format.strip(),
                        example_template=custom_example_format.strip(),
                        output_format=custom_output_format.strip(),
                        context_requirements=context_requirements.strip(),
                        focus_points=focus_keywords
                    )
                
                rules.append(current_rule)

        for line in lines:
            line = line.rstrip()

            m_cat = category_pattern.match(line)
            if m_cat:
                flush_rule()
                current_category = m_cat.group(1)
                current_rule = None
                buffer = []
                continue

            m_rule = rule_pattern.match(line)
            if m_rule:
                flush_rule()
                rule_id = m_rule.group(1)
                rule_name = m_rule.group(2)
                current_rule = CodeCheckRule(
                    id=rule_id,
                    name=rule_name,
                    category=[current_category],
                )
                buffer = []
            elif current_rule:
                buffer.append(line)

        flush_rule()
        return rules

    def _load_yaml(self) -> List[CodeCheckRule]:
        """从YAML文件加载规则（支持评测集格式）"""
        with open(self.rule_file_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f)
        
        if not data:
            return []
        
        rules = []
        
        # 处理评测集格式的YAML
        if "rule_categories" in data:
            # 评测集格式：包含rule_categories
            for category in data.get("rule_categories", []):
                category_id = category.get("id", "unknown")
                category_name = category.get("name", "unknown")
                
                for rule_group in category.get("rules", []):
                    rule_group_id = rule_group.get("id", "unknown")
                    rule_group_name = rule_group.get("name", "unknown")
                    
                    for principle in rule_group.get("principles", []):
                        principle_content = principle.get("content", "")

                        # 为每个principle创建一个规则
                        rule_id = f"{rule_group_id}-{len(rules) + 1}"
                        rule = CodeCheckRule(
                            id=rule_id,
                            name=principle_content[:50] + "..." if len(principle_content) > 50 else principle_content,
                            category=[category_name, rule_group_name],
                            rule_value=principle_content,
                            description=f"来自评测集: {category_name} - {rule_group_name}",
                            enabled=True,
                            severity="中等",  # 评测集规则默认中等严重程度
                            file_path=str(self.rule_file_path),
                            contact_info=None,
                            raw=principle,
                            match_regex=principle.get("match_regex", ""),
                            suggestion=principle.get("suggestion", "")
                        )
                        rules.append(rule)
        
        # 处理标准规则格式的YAML（如果将来需要）
        elif "rules" in data:
            # 标准规则格式：直接包含rules列表
            for rule_data in data.get("rules", []):
                rule = CodeCheckRule(
                    id=rule_data.get("id", f"rule_{len(rules) + 1}"),
                    name=rule_data.get("name", "未命名规则"),
                    category=rule_data.get("category", ["默认分类"]),
                    rule_value=rule_data.get("rule_value", ""),
                    description=rule_data.get("description", ""),
                    enabled=rule_data.get("enabled", True),
                    severity=rule_data.get("severity", "中等"),
                    file_path=self.rule_file_path,
                    contact_info=rule_data.get("contact_info"),
                    raw=rule_data
                )
                rules.append(rule)
        
        logger.info(f"从YAML文件加载了 {len(rules)} 条规则")

        # 调试信息：显示加载的规则详情
        for i, rule in enumerate(rules):
            logger.info(f"  YAML规则 {i+1}: ID={rule.id}, Name={rule.name[:50]}...")
            logger.info(f"    Category={rule.category}, Enabled={rule.enabled}")

        return rules

    def _get_grouping_cache_key(self, check_item: CodeElement, grouping_strategy: str) -> str:
        """生成规则分组缓存键"""
        # 基于检查项的关键信息和分组策略生成缓存键
        # AffectedFunction 有 filepath 属性，没有 language 属性
        item_key = f"{check_item.name}_{check_item.type}_{getattr(check_item, 'filepath', 'unknown')}"
        return f"{item_key}_{grouping_strategy}"

    def get_applicable_rules(self, check_item: CodeElement, grouping_strategy: str = None) -> dict:
        """
        根据检查项类型、文件名、语言等筛选适用规则，并按策略分组
        Args:
            check_item: 代码元素
            grouping_strategy: 分组策略，可选，覆盖全局配置
        Returns:
            分组后的规则字典 {group_name: [rules]}
        """
        # 生成缓存键
        config = _get_config()
        strategy = grouping_strategy or config.rule_grouping_strategy
        cache_key = self._get_grouping_cache_key(check_item, strategy)
        
        # 检查缓存
        if cache_key in self._grouping_cache:
            logger.debug(f"使用缓存的规则分组结果 - 函数: {check_item.name}")
            return self._grouping_cache[cache_key]
        
        rules = self.load_rules()
        logger.info(f"🔍 规则匹配调试 - 检查项: {check_item.name}, 类型: {check_item.type}")
        logger.info(f"  总规则数: {len(rules)}")

        # 实现规则的enable和语言过滤
        applicable = []
        for rule in rules:
            # To-do:过滤未启用的规则
            # To-do:过滤语言不匹配的规则
            applicable.append(rule)

        logger.info(f"  适用规则数: {len(applicable)}")
        
        # 分组策略 - 移除重复的前置日志，统一在 _log_grouping_statistics 中处理
        if strategy == 'category':
            grouped_rules = self.group_rules_by_category_for_list(applicable)
            _log_grouping_statistics(grouped_rules, "Category", len(applicable), strategy, config)
        elif strategy == 'similarity':
            grouped_rules = self.group_rules_by_similarity_for_list(applicable, config.rule_similarity_threshold)
            _log_grouping_statistics(grouped_rules, "Similarity", len(applicable), strategy, config)
        elif strategy == 'adaptive':
            min_size = config.min_rule_group_size
            max_size = config.max_rule_group_size
            target_size = config.target_rule_group_size
            grouped_rules = self.group_rules_adaptive_for_list(applicable, min_size, max_size, target_size)
            _log_grouping_statistics(grouped_rules, "Adaptive", len(applicable), strategy, config)
        elif strategy == 'minimize_calls':
            # 专门用于减少LLM调用次数的策略
            min_size = config.min_rule_group_size
            max_size = config.max_rule_group_size
            target_size = config.target_rule_group_size
            grouped_rules = self.group_rules_minimize_calls(applicable, min_size, max_size, target_size)
            _log_grouping_statistics(grouped_rules, "MinimizeCalls", len(applicable), strategy, config)
        else:
            grouped_rules = {'all': applicable}
            _log_grouping_statistics(grouped_rules, "Default", len(applicable), strategy, config)
        
        # 缓存结果
        self._grouping_cache[cache_key] = grouped_rules
        
        # 限制缓存大小，避免内存泄漏
        if len(self._grouping_cache) > 1000:
            # 清空缓存
            self._grouping_cache.clear()
            logger.debug("规则分组缓存已清空")
        
        return grouped_rules

    def group_rules_by_category_for_list(self, rules: list) -> dict:
        logger.info(f"开始按类别分组，规则数: {len(rules)}")
        grouped = {}
        for rule in rules:
            if rule.category and len(rule.category) > 1:
                group = tuple(rule.category[:-1])
            elif rule.category:
                group = tuple(rule.category)
            else:
                group = ("default",)
            grouped.setdefault(group, []).append(rule)
        
        logger.info(f"类别分组完成，共 {len(grouped)} 个分组")
        return grouped

    def group_rules_by_similarity_for_list(self, rules: list, similarity_threshold: float = 0.7) -> dict:
        # 调用真实分组算法
        return self._group_rules_by_similarity(rules, similarity_threshold)

    def _group_rules_by_similarity(self, rules: list, similarity_threshold: float = 0.7) -> dict:
        if not rules:
            return {}
        
        logger.info(f"开始按相似度分组，规则数: {len(rules)}, 阈值: {similarity_threshold}")
        
        def calculate_similarity(rule1: CodeCheckRule, rule2: CodeCheckRule) -> float:
            text1 = f"{rule1.name} {rule1.rule_value} {rule1.description}".lower()
            text2 = f"{rule2.name} {rule2.rule_value} {rule2.description}".lower()
            words1 = set(text1.split())
            words2 = set(text2.split())
            if not words1 or not words2:
                return 0.0
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            return len(intersection) / len(union) if union else 0.0
        
        grouped = {}
        unassigned = rules.copy()
        group_id = 0
        
        while unassigned:
            seed_rule = unassigned.pop(0)
            current_group = [seed_rule]
            to_remove = []
            
            for rule in unassigned:
                if calculate_similarity(seed_rule, rule) >= similarity_threshold:
                    current_group.append(rule)
                    to_remove.append(rule)
            
            for rule in to_remove:
                unassigned.remove(rule)
            
            grouped[f"similarity_group_{group_id}"] = current_group
            logger.debug(f"创建相似度分组 {group_id}: {len(current_group)} 条规则")
            group_id += 1
        
        logger.info(f"相似度分组完成，共 {len(grouped)} 个分组")
        return grouped

    def group_rules_adaptive_for_list(self, rules: list, min_size: int = 2, max_size: int = 5, target_size: int = 3) -> dict:
        # 调用真实分组算法
        return self._group_rules_adaptive(rules, min_size, max_size, target_size)

    def _group_rules_adaptive(self, rules: list, min_group_size: int = 3, max_group_size: int = 8, target_group_size: int = 5) -> dict:
        logger.info(f"开始自适应分组，规则数: {len(rules)}, 参数: min={min_group_size}, max={max_group_size}, target={target_group_size}")
        
        # 复制 group_rules_adaptive 的实现，但用传入的 rules
        # 首先按类别分组
        def group_by_category(rules):
            grouped = {}
            for rule in rules:
                if rule.category and len(rule.category) > 1:
                    group = tuple(rule.category[:-1])
                elif rule.category:
                    group = tuple(rule.category)
                else:
                    group = ("default",)
                grouped.setdefault(group, []).append(rule)
            return grouped
        
        category_groups = group_by_category(rules)
        logger.info(f"初始类别分组: {len(category_groups)} 个分组")
        
        small_groups = []
        large_groups = []
        good_groups = {}
        group_counter = 0
        
        for group_key, group_rules in category_groups.items():
            group_size = len(group_rules)
            if group_size < min_group_size:
                small_groups.extend(group_rules)
                logger.debug(f"小分组 {group_key}: {group_size} 条规则 (小于 {min_group_size})")
            elif group_size > max_group_size:
                large_groups.append((group_key, group_rules))
                logger.debug(f"大分组 {group_key}: {group_size} 条规则 (大于 {max_group_size})")
            else:
                good_groups[f"adaptive_group_{group_counter}"] = group_rules
                logger.debug(f"合适分组 {group_counter}: {group_size} 条规则")
                group_counter += 1
        def merge_small_groups_by_similarity(rules, target_size):
            if len(rules) <= target_size:
                # 如果总规则数小于等于目标大小，直接返回一个分组
                return [rules]
            
            groups = []
            remaining = rules.copy()
            
            while remaining:
                # 如果剩余规则数小于等于目标大小，直接作为一个分组
                if len(remaining) <= target_size:
                    groups.append(remaining)
                    break
                
                # 开始构建当前分组
                current_group = [remaining.pop(0)]
                
                # 尝试添加更多规则到当前分组
                while len(current_group) < target_size and remaining:
                    best_rule = None
                    best_similarity = 0
                    
                    # 寻找最相似的规则
                    for rule in remaining:
                        similarities = []
                        for group_rule in current_group:
                            sim = self._calculate_rule_similarity(rule, group_rule)
                            similarities.append(sim)
                        avg_similarity = sum(similarities) / len(similarities)
                        if avg_similarity > best_similarity:
                            best_similarity = avg_similarity
                            best_rule = rule
                    
                    # 如果找到相似度足够高的规则，添加到当前分组
                    if best_rule and best_similarity > 0.05:
                        current_group.append(best_rule)
                        remaining.remove(best_rule)
                    else:
                        # 如果找不到相似规则，但当前分组还没达到最小大小，强制添加一个规则
                        if len(current_group) < min_group_size and remaining:
                            current_group.append(remaining.pop(0))
                        else:
                            break
                
                # 将当前分组添加到结果中
                groups.append(current_group)
            
            # 后处理：确保没有小于最小分组大小的分组
            final_groups = []
            for group in groups:
                if len(group) < min_group_size and final_groups:
                    # 如果当前分组太小，合并到前一个分组
                    final_groups[-1].extend(group)
                else:
                    final_groups.append(group)
            
            # 最终检查：如果还有小于最小分组大小的分组，且不是最后一个分组，继续合并
            if final_groups and len(final_groups[-1]) < min_group_size and len(final_groups) > 1:
                # 将最后一个小组合并到倒数第二个分组
                final_groups[-2].extend(final_groups[-1])
                final_groups.pop()
            
            return final_groups
        if small_groups:
            logger.info(f"处理小分组: {len(small_groups)} 条规则需要合并")
            merged_groups = merge_small_groups_by_similarity(small_groups, target_group_size)
            logger.info(f"小分组合并完成: {len(merged_groups)} 个合并组")
            
            # 检查合并后的分组是否满足最小大小要求
            for i, group in enumerate(merged_groups):
                if len(group) >= min_group_size:
                    good_groups[f"merged_group_{i}"] = group
                    logger.debug(f"合并组 {i}: {len(group)} 条规则")
                else:
                    # 如果合并后的分组仍然太小，尝试合并到现有的合适分组中
                    if good_groups:
                        # 找到最相似的分组进行合并
                        best_group_key = None
                        best_similarity = 0
                        for group_key, existing_group in good_groups.items():
                            total_similarity = 0
                            for rule in group:
                                for existing_rule in existing_group:
                                    total_similarity += self._calculate_rule_similarity(rule, existing_rule)
                            avg_similarity = total_similarity / (len(group) * len(existing_group))
                            if avg_similarity > best_similarity:
                                best_similarity = avg_similarity
                                best_group_key = group_key
                        
                        if best_group_key:
                            good_groups[best_group_key].extend(group)
                            logger.debug(f"小分组 {i} ({len(group)} 条规则) 合并到 {best_group_key}")
                        else:
                            # 如果找不到合适的分组，强制合并到第一个分组
                            first_key = list(good_groups.keys())[0]
                            good_groups[first_key].extend(group)
                            logger.debug(f"小分组 {i} ({len(group)} 条规则) 强制合并到 {first_key}")
                    else:
                        # 如果没有现有分组，创建一个新分组（虽然可能小于最小大小，但这是边界情况）
                        good_groups[f"small_group_{i}"] = group
                        logger.debug(f"小分组 {i}: {len(group)} 条规则 (边界情况)")
        
        def split_large_group_by_subcategory(rules, max_size):
            subcategory_groups = {}
            for rule in rules:
                if rule.category and len(rule.category) > 1:
                    subcat = tuple(rule.category)
                else:
                    subcat = (rule.id[:3],)
                subcategory_groups.setdefault(subcat, []).append(rule)
            result_groups = []
            for subgroup in subcategory_groups.values():
                if len(subgroup) <= max_size:
                    result_groups.append(subgroup)
                else:
                    chunk_size = max_size
                    for i in range(0, len(subgroup), chunk_size):
                        result_groups.append(subgroup[i:i + chunk_size])
            return result_groups
        
        if large_groups:
            logger.info(f"处理大分组: {len(large_groups)} 个大分组需要拆分")
            for group_key, group_rules in large_groups:
                logger.debug(f"拆分大分组 {group_key}: {len(group_rules)} 条规则")
                split_groups = split_large_group_by_subcategory(group_rules, max_group_size)
                logger.debug(f"大分组 {group_key} 拆分为 {len(split_groups)} 个子组")
                for i, group in enumerate(split_groups):
                    good_groups[f"split_{group_key}_{i}"] = group
                    logger.debug(f"拆分组 {group_key}_{i}: {len(group)} 条规则")
        
        logger.info(f"自适应分组完成，最终分组数: {len(good_groups)}")
        return good_groups

    def group_rules_minimize_calls(self, rules: list, min_size: int = None, max_size: int = None, target_size: int = None) -> dict:
        """
        专门用于减少LLM调用次数的分组策略
        强制将规则合并成较大的分组，即使相似度较低
        
        Args:
            rules: 规则列表
            min_size: 最小分组大小（从配置获取）
            max_size: 最大分组大小（从配置获取）
            target_size: 目标分组大小（从配置获取）
        """
        # 生成缓存键，避免重复打印分组过程日志
        cache_key = f"minimize_calls_{len(rules)}_{min_size}_{max_size}_{target_size}"
        if hasattr(self, '_minimize_calls_log_cache'):
            minimize_calls_log_cache = self._minimize_calls_log_cache
        else:
            minimize_calls_log_cache = set()
            self._minimize_calls_log_cache = minimize_calls_log_cache
        
        # 只在第一次调用时打印详细日志
        if cache_key not in minimize_calls_log_cache:
            logger.info(f"开始最小调用次数分组，规则数: {len(rules)}")
            minimize_calls_log_cache.add(cache_key)
        else:
            logger.debug(f"使用缓存的最小调用次数分组结果，规则数: {len(rules)}")
        
        # 如果没有传入参数，使用默认值
        if min_size is None:
            min_size = 8
        if max_size is None:
            max_size = 15
        if target_size is None:
            target_size = 10
        
        if len(rules) <= min_size:  # 如果规则总数很少，直接作为一个分组
            if cache_key not in minimize_calls_log_cache:
                logger.info(f"规则总数较少({len(rules)})，直接作为一个分组")
            return {'minimize_calls_group_0': rules}
        
        # 根据配置参数计算目标分组数
        target_groups = max(2, min(4, len(rules) // target_size))
        actual_target_size = len(rules) // target_groups
        
        if cache_key not in minimize_calls_log_cache:
            logger.info(f"目标分组数: {target_groups}, 目标每组大小: {actual_target_size}")
            logger.info(f"配置参数: min_size={min_size}, max_size={max_size}, target_size={target_size}")
        
        # 首先按类别分组
        def group_by_category(rules):
            grouped = {}
            for rule in rules:
                if rule.category and len(rule.category) > 1:
                    group = tuple(rule.category[:-1])
                elif rule.category:
                    group = tuple(rule.category)
                else:
                    group = ("default",)
                grouped.setdefault(group, []).append(rule)
            return grouped
        
        category_groups = group_by_category(rules)
        if cache_key not in minimize_calls_log_cache:
            logger.info(f"初始类别分组: {len(category_groups)} 个分组")
        
        # 如果类别分组已经很少，直接使用
        if len(category_groups) <= target_groups:
            if cache_key not in minimize_calls_log_cache:
                logger.info("类别分组数量已经满足要求")
            result = {}
            for i, (category, group_rules) in enumerate(category_groups.items()):
                result[f"minimize_calls_group_{i}"] = group_rules
            return result
        
        # 否则，强制合并小分组
        all_rules = []
        for group_rules in category_groups.values():
            all_rules.extend(group_rules)
        
        # 强制分成目标数量的分组
        result = {}
        for i in range(target_groups):
            start_idx = i * actual_target_size
            end_idx = start_idx + actual_target_size if i < target_groups - 1 else len(all_rules)
            group_rules = all_rules[start_idx:end_idx]
            result[f"minimize_calls_group_{i}"] = group_rules
            if cache_key not in minimize_calls_log_cache:
                logger.debug(f"强制分组 {i}: {len(group_rules)} 条规则")
        
        if cache_key not in minimize_calls_log_cache:
            logger.info(f"最小调用次数分组完成，共 {len(result)} 个分组")
        return result

    def group_rules_by_category(self) -> Dict[tuple, List[CodeCheckRule]]:
        """
        按规则的 category[:-1]（即从第0个到倒数第二个）进行分组
        
        Returns:
            分组后的规则字典 {group_key: [rules]}
        """
        rules = self.load_rules()
        grouped = {}
        for rule in rules:
            # 取 category 的前n-1项作为分组key
            if rule.category and len(rule.category) > 1:
                group = tuple(rule.category[:-1])
            elif rule.category:
                group = tuple(rule.category)
            else:
                group = ("default",)
            grouped.setdefault(group, []).append(rule)
        return grouped

    def group_rules_by_similarity(self, similarity_threshold: float = 0.7) -> Dict[str, List[CodeCheckRule]]:
        """
        基于规则内容相似度进行智能分组
        
        Args:
            similarity_threshold: 相似度阈值
            
        Returns:
            分组后的规则字典 {group_id: [rules]}
        """
        rules = self.load_rules()
        if not rules:
            return {}
        
        # 简化的相似度计算（基于关键词重叠）
        def calculate_similarity(rule1: CodeCheckRule, rule2: CodeCheckRule) -> float:
            text1 = f"{rule1.name} {rule1.rule_value} {rule1.description}".lower()
            text2 = f"{rule2.name} {rule2.rule_value} {rule2.description}".lower()
            
            words1 = set(text1.split())
            words2 = set(text2.split())
            
            if not words1 or not words2:
                return 0.0
            
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            
            return len(intersection) / len(union) if union else 0.0
        
        # 基于相似度聚类
        grouped = {}
        unassigned = rules.copy()
        group_id = 0
        
        while unassigned:
            # 创建新组，以第一个未分配规则为种子
            seed_rule = unassigned.pop(0)
            current_group = [seed_rule]
            
            # 查找与种子规则相似的规则
            to_remove = []
            for rule in unassigned:
                if calculate_similarity(seed_rule, rule) >= similarity_threshold:
                    current_group.append(rule)
                    to_remove.append(rule)
            
            # 移除已分组的规则
            for rule in to_remove:
                unassigned.remove(rule)
            
            grouped[f"similarity_group_{group_id}"] = current_group
            group_id += 1
        
        return grouped

    def group_rules_adaptive(self, 
                           min_group_size: int = 3, 
                           max_group_size: int = 8,
                           target_group_size: int = 5) -> Dict[str, List[CodeCheckRule]]:
        """
        自适应规则分组，综合考虑类别、相似度和组大小
        
        Args:
            min_group_size: 最小分组大小
            max_group_size: 最大分组大小
            target_group_size: 目标分组大小
            
        Returns:
            分组后的规则字典 {group_id: [rules]}
        """
        # 首先按类别分组
        category_groups = self.group_rules_by_category()
        
        # 分析组大小分布
        small_groups = []
        large_groups = []
        good_groups = {}
        
        group_counter = 0
        
        for group_key, rules in category_groups.items():
            group_size = len(rules)
            
            if group_size < min_group_size:
                # 小组需要合并
                small_groups.extend(rules)
            elif group_size > max_group_size:
                # 大组需要拆分
                large_groups.append((group_key, rules))
            else:
                # 大小合适的组
                good_groups[f"adaptive_group_{group_counter}"] = rules
                group_counter += 1
        
        # 处理小组：按相似度合并
        if small_groups:
            merged_groups = self._merge_small_groups_by_similarity(
                small_groups, target_group_size
            )
            for i, group in enumerate(merged_groups):
                good_groups[f"merged_group_{i}"] = group
        
        # 处理大组：按类别层次拆分
        for group_key, rules in large_groups:
            split_groups = self._split_large_group_by_subcategory(
                rules, max_group_size
            )
            for i, group in enumerate(split_groups):
                good_groups[f"split_{group_key}_{i}"] = group
        
        return good_groups

    def _merge_small_groups_by_similarity(self, 
                                        rules: List[CodeCheckRule], 
                                        target_size: int) -> List[List[CodeCheckRule]]:
        """基于相似度合并小组"""
        if len(rules) <= target_size:
            return [rules]
        
        # 使用简单的贪心算法
        groups = []
        remaining = rules.copy()
        
        while remaining:
            if len(remaining) <= target_size:
                groups.append(remaining)
                break
            
            # 创建新组
            current_group = [remaining.pop(0)]
            
            # 基于相似度添加规则到当前组
            while len(current_group) < target_size and remaining:
                best_rule = None
                best_similarity = 0
                
                for rule in remaining:
                    # 计算与组内规则的平均相似度
                    similarities = []
                    for group_rule in current_group:
                        sim = self._calculate_rule_similarity(rule, group_rule)
                        similarities.append(sim)
                    
                    avg_similarity = sum(similarities) / len(similarities)
                    if avg_similarity > best_similarity:
                        best_similarity = avg_similarity
                        best_rule = rule
                
                if best_rule and best_similarity > 0.3:  # 最低相似度阈值
                    current_group.append(best_rule)
                    remaining.remove(best_rule)
                else:
                    break
            
            groups.append(current_group)
        
        return groups

    def _split_large_group_by_subcategory(self, 
                                        rules: List[CodeCheckRule], 
                                        max_size: int) -> List[List[CodeCheckRule]]:
        """基于子类别拆分大组"""
        # 尝试按更细粒度的类别拆分
        subcategory_groups = {}
        
        for rule in rules:
            # 使用完整的category作为细分依据
            if rule.category and len(rule.category) > 1:
                subcat = tuple(rule.category)
            else:
                subcat = (rule.id[:3],)  # 使用ID前缀作为fallback
            
            subcategory_groups.setdefault(subcat, []).append(rule)
        
        # 如果子类别分组还是太大，继续拆分
        result_groups = []
        for subgroup in subcategory_groups.values():
            if len(subgroup) <= max_size:
                result_groups.append(subgroup)
            else:
                # 简单均分
                chunk_size = max_size
                for i in range(0, len(subgroup), chunk_size):
                    result_groups.append(subgroup[i:i + chunk_size])
        
        return result_groups

    def _calculate_rule_similarity(self, rule1: CodeCheckRule, rule2: CodeCheckRule) -> float:
        """计算两个规则的相似度"""
        # 简化的相似度计算
        text1 = f"{rule1.name} {rule1.description or ''}".lower()
        text2 = f"{rule2.name} {rule2.description or ''}".lower()
        
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0

    def get_rules_by_severity(self, severity: str) -> List[CodeCheckRule]:
        """
        根据严重程度筛选规则
        
        Args:
            severity: 严重程度
            
        Returns:
            匹配的规则列表
        """
        rules = self.load_rules()
        return [rule for rule in rules if rule.severity == severity and rule.enabled]

    def get_rules_by_category(self, category: str) -> List[CodeCheckRule]:
        """
        根据分类筛选规则
        
        Args:
            category: 分类名称
            
        Returns:
            匹配的规则列表
        """
        rules = self.load_rules()
        return [rule for rule in rules if category in rule.category and rule.enabled]

    def get_rule_by_id(self, rule_id: str) -> Optional[CodeCheckRule]:
        """
        根据规则ID获取规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            规则对象，如果不存在返回 None
        """
        rules = self.load_rules()
        for rule in rules:
            if rule.id == rule_id:
                return rule
        return None

    def enable_rule(self, rule_id: str) -> bool:
        """
        启用规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            是否成功启用
        """
        rule = self.get_rule_by_id(rule_id)
        if rule:
            rule.enabled = True
            return True
        return False

    def disable_rule(self, rule_id: str) -> bool:
        """
        禁用规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            是否成功禁用
        """
        rule = self.get_rule_by_id(rule_id)
        if rule:
            rule.enabled = False
            return True
        return False

    def reload_rules(self) -> List[CodeCheckRule]:
        """
        强制重新加载规则
        
        Returns:
            重新加载的规则列表
        """
        return self.load_rules(force_reload=True)
    
    def set_rules(self, rules: List[CodeCheckRule]) -> None:
        """
        直接设置规则列表（用于评估模式等场景）
        
        Args:
            rules: 规则列表
        """
        self._rules = rules
        self._loaded = True
        self._cache_key = "direct_rules"
        # 清除分组缓存，因为规则已经改变
        self._grouping_cache.clear()
        logger.info(f"规则管理器已设置 {len(rules)} 条规则")
    
    def get_rules(self) -> List[CodeCheckRule]:
        """
        获取当前规则列表
        
        Returns:
            当前规则列表
        """
        return self._rules.copy()

    def get_applicable_rules_with_prescreening(self, check_item: CodeElement, grouping_strategy: str = None) -> dict:
        """
        使用预筛选策略获取适用规则，并按策略分组

        Args:
            check_item: 代码元素
            grouping_strategy: 分组策略，可选，覆盖全局配置

        Returns:
            分组后的规则字典 {group_name: [rules]}
        """
        try:
            # 加载所有规则
            all_rules = self.load_rules()

            # 使用预筛选管理器筛选规则
            applicable_rules = self.prescreening_manager.get_applicable_rules(check_item, all_rules)

            # 进一步按传统方式筛选（基于类型、文件名等）
            filtered_rules = self._filter_rules_by_element(applicable_rules, check_item)

            # 按策略分组
            config = _get_config()
            strategy = grouping_strategy or config.rule_grouping_strategy
            grouped_rules = self._group_rules_by_strategy(filtered_rules, strategy)

            logger.debug(f"Prescreening: {len(all_rules)} -> {len(applicable_rules)} -> {len(filtered_rules)} rules for {check_item.name}")

            return grouped_rules

        except Exception as e:
            logger.warning(f"Prescreening failed, falling back to traditional method: {e}")
            return self.get_applicable_rules(check_item, grouping_strategy)

    def get_applicable_rules_with_regex(self, check_item: CodeElement, grouping_strategy: str = None) -> dict:
        """
        基于match_regex筛选适用规则，并按策略分组

        Args:
            check_item: 代码元素
            grouping_strategy: 分组策略，可选，覆盖全局配置

        Returns:
            分组后的规则字典 {group_name: [rules]}
        """
        if not self.enable_regex_matching or not self.regex_matcher:
            # 如果未启用regex匹配，回退到传统方法
            return self.get_applicable_rules(check_item, grouping_strategy)

        try:
            # 加载所有规则
            all_rules = self.load_rules()

            # 使用regex预筛选
            applicable_rules = self.regex_matcher.get_applicable_rules_with_regex(check_item, all_rules)

            # 进一步按传统方式筛选（基于类型、文件名等）
            filtered_rules = self._filter_rules_by_element(applicable_rules, check_item)

            # 按策略分组
            config = _get_config()
            strategy = grouping_strategy or config.rule_grouping_strategy
            # 使用现有的分组逻辑
            if strategy == 'category':
                grouped_rules = self.group_rules_by_category_for_list(filtered_rules)
            elif strategy == 'similarity':
                grouped_rules = self.group_rules_by_similarity_for_list(filtered_rules, config.rule_similarity_threshold)
            elif strategy == 'adaptive':
                min_size = config.min_rule_group_size
                max_size = config.max_rule_group_size
                target_size = config.target_rule_group_size
                grouped_rules = self.group_rules_adaptive_for_list(filtered_rules, min_size, max_size, target_size)
            elif strategy == 'minimize_calls':
                min_size = config.min_rule_group_size
                max_size = config.max_rule_group_size
                target_size = config.target_rule_group_size
                grouped_rules = self.group_rules_minimize_calls(filtered_rules, min_size, max_size, target_size)
            else:
                grouped_rules = {'all': filtered_rules}

            logger.debug(f"Regex matching: {len(all_rules)} -> {len(applicable_rules)} -> {len(filtered_rules)} rules for {check_item.name}")

            return grouped_rules

        except Exception as e:
            logger.warning(f"Regex matching failed, falling back to traditional method: {e}")
            return self.get_applicable_rules(check_item, grouping_strategy)

    def _filter_rules_by_element(self, rules: List[CodeCheckRule], check_item: CodeElement) -> List[CodeCheckRule]:
        """
        根据代码元素特征进一步筛选规则

        Args:
            rules: 预筛选的规则列表
            check_item: 代码元素

        Returns:
            筛选后的规则列表
        """
        filtered_rules = []

        for rule in rules:
            # 检查语言匹配
            if rule.languages and check_item.filepath:
                file_ext = Path(check_item.filepath).suffix.lower()
                language_match = any(
                    lang.lower() in file_ext or file_ext.lstrip('.') in lang.lower()
                    for lang in rule.languages
                )
                if not language_match:
                    continue

            # 检查文件路径匹配（如果规则有特定的文件路径要求）
            # 这里可以根据需要添加更多筛选逻辑

            filtered_rules.append(rule)

        return filtered_rules

    def _group_rules_by_strategy(self, rules: List[CodeCheckRule], strategy: str) -> Dict[str, List[CodeCheckRule]]:
        """
        根据策略对规则进行分组

        Args:
            rules: 规则列表
            strategy: 分组策略

        Returns:
            分组后的规则字典
        """
        config = _get_config()

        if strategy == 'category':
            return self.group_rules_by_category_for_list(rules)
        elif strategy == 'similarity':
            return self.group_rules_by_similarity_for_list(rules, config.rule_similarity_threshold)
        elif strategy == 'adaptive':
            min_size = config.min_rule_group_size
            max_size = config.max_rule_group_size
            target_size = config.target_rule_group_size
            return self.group_rules_adaptive_for_list(rules, min_size, max_size, target_size)
        elif strategy == 'minimize_calls':
            min_size = config.min_rule_group_size
            max_size = config.max_rule_group_size
            target_size = config.target_rule_group_size
            return self.group_rules_minimize_calls(rules, min_size, max_size, target_size)
        else:
            return {'all': rules}

    def get_regex_matching_stats(self) -> Dict[str, Any]:
        """
        获取正则匹配统计信息

        Returns:
            统计信息字典
        """
        if not self.regex_matcher:
            return {"enabled": False}

        from gate_keeper.application.service.rule.regex_matcher import \
            RegexCache

        rules = self.load_rules()
        rules_with_regex = [r for r in rules if r.match_regex]

        return {
            "enabled": self.enable_regex_matching,
            "total_rules": len(rules),
            "rules_with_regex": len(rules_with_regex),
            "regex_coverage": len(rules_with_regex) / len(rules) if rules else 0,
            "cache_stats": RegexCache.get_cache_stats()
        }

    def get_prescreening_stats(self) -> Dict[str, Any]:
        """
        获取预筛选统计信息

        Returns:
            统计信息字典
        """
        stats = self.prescreening_manager.get_stats()

        rules = self.load_rules()
        stats.update({
            "total_rules": len(rules),
            "manager_type": "RulePreScreeningManager"
        })

        return stats

    def update_prescreening_config(self, config_dict: Dict[str, Any]):
        """
        更新预筛选配置

        Args:
            config_dict: 新的配置字典
        """
        new_manager = create_prescreening_manager(config_dict)

        # 如果有规则已加载，预热新的管理器
        if self._loaded and self._rules:
            new_manager.warm_up(self._rules)

        self.prescreening_manager = new_manager
        logger.info("Updated prescreening configuration")

    def clear_prescreening_stats(self):
        """清空预筛选统计信息"""
        self.prescreening_manager.clear_stats()

    def get_rules_summary(self) -> Dict[str, Any]:
        """
        获取规则统计摘要
        
        Returns:
            规则统计信息
        """
        rules = self.load_rules()
        total_rules = len(rules)
        enabled_rules = len([r for r in rules if r.enabled])
        disabled_rules = total_rules - enabled_rules
        
        # 按严重程度统计
        severity_stats = {}
        for rule in rules:
            severity = rule.severity or "未指定"
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        # 按分类统计
        category_stats = {}
        for rule in rules:
            if rule.category:
                category = rule.category[0] if rule.category else "未分类"
                category_stats[category] = category_stats.get(category, 0) + 1
        
        return {
            "total_rules": total_rules,
            "enabled_rules": enabled_rules,
            "disabled_rules": disabled_rules,
            "severity_stats": severity_stats,
            "category_stats": category_stats,
            "file_path": self.rule_file_path,
            "merge_on": self.merge_on,
            "include_sheets": self.include_sheets
        } 

    def simulate_grouping_plan(self, check_item: CodeElement, grouping_strategy: str = None, llm_max_concurrency: int = None):
        """
        模拟分组和LLM调用计划，结构化打印分组参数、分组明细、LLM调用计划（只打印一次）
        Args:
            check_item: 代码元素（如AffectedFunction）
            grouping_strategy: 分组策略
            llm_max_concurrency: LLM最大并发数（可选）
        """
        config = _get_config()
        strategy = grouping_strategy or getattr(config, 'rule_grouping_strategy', 'adaptive')
        min_size = getattr(config, 'min_rule_group_size', 3)
        max_size = getattr(config, 'max_rule_group_size', 8)
        target_size = getattr(config, 'target_rule_group_size', 5)
        llm_limit = llm_max_concurrency or getattr(config, 'llm_max_concurrency', 5)

        # 获取分组结果（会自动缓存，且只打印一次）
        grouped_rules = self.get_applicable_rules(check_item, grouping_strategy=strategy)
        total_rules = sum(len(v) for v in grouped_rules.values())
        group_count = len(grouped_rules)
        group_sizes = [len(v) for v in grouped_rules.values()]

        # 只打印一次（用分组参数和分组hash做key）
        plan_key = f"plan_{strategy}_{min_size}_{max_size}_{target_size}_{llm_limit}_{total_rules}_{group_count}_{hash(frozenset((k, len(v)) for k, v in grouped_rules.items()))}"
        if hasattr(self, '_printed_plan_keys'):
            printed_plan_keys = self._printed_plan_keys
        else:
            printed_plan_keys = set()
            self._printed_plan_keys = printed_plan_keys
        if plan_key in printed_plan_keys:
            logger.info("[分组模拟] 已打印过本次分组计划，跳过重复输出")
            return
        printed_plan_keys.add(plan_key)

        logger.info("\n=================【分组模拟/执行计划】=================")
        logger.info(f"分组策略: {strategy}")
        logger.info(f"分组参数: min_group_size={min_size}, max_group_size={max_size}, target_group_size={target_size}")
        logger.info(f"LLM最大并发: {llm_limit}")
        logger.info(f"总规则数: {total_rules}")
        logger.info(f"分组数量: {group_count}")
        logger.info("\n【规则分组明细】")
        for i, (group_name, rules) in enumerate(grouped_rules.items()):
            rule_ids = [getattr(r, 'id', str(r)) for r in rules]
            logger.info(f"  组{i+1} [{group_name}]: {len(rules)} 条规则 -> {rule_ids}")
        logger.info("\n【LLM调用计划】")
        logger.info(f"  预计LLM调用次数: {group_count}")
        logger.info(f"  每组规则数: {group_sizes}")
        logger.info("====================================================\n")