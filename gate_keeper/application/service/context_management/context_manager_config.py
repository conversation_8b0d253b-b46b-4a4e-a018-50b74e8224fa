"""
Context Manager Configuration

上下文管理器配置类，集中管理所有配置项
从配置模块中动态读取，支持灵活配置
"""

from dataclasses import dataclass, field
from typing import Dict, List


@dataclass
class ContextManagerConfig:
    """上下文管理器配置"""
    
    # 基础配置 - 从配置模块动态读取
    max_context_size: int = None
    max_chain_depth: int = None
    max_chains: int = None
    
    # 相关性计算权重 - 从配置模块读取，有默认值
    relevance_weights: Dict[str, float] = None
    
    # 函数选择策略 - 从配置模块读取，有默认值
    function_selection_strategy: str = None
    
    # 上下文选择策略 - 从配置模块读取，有默认值
    context_selection_strategy: str = None
    
    # 日志配置 - 从配置模块读取，有默认值
    enable_detailed_logging: bool = None
    log_context_building: bool = None
    

    
    # 过滤配置 - 从配置模块读取，有默认值
    min_relevance_threshold: float = None
    max_context_size_ratio: float = None
    
    # 格式化配置 - 从配置模块读取，有默认值
    include_chain_info: bool = None
    include_relevance_score: bool = None
    include_file_paths: bool = None
    
    def __post_init__(self):
        """初始化后处理，从配置模块动态读取配置"""
        self._load_config_from_module()
        self._set_defaults()
    
    def _load_config_from_module(self):
        """从配置模块动态加载配置"""
        try:
            from gate_keeper.config import config

            # 基础配置
            if self.max_context_size is None:
                self.max_context_size = getattr(config, 'max_context_size', 8000)
            
            if self.max_chain_depth is None:
                self.max_chain_depth = getattr(config, 'max_context_chain_depth', 3)
                
            if self.max_chains is None:
                self.max_chains = getattr(config, 'max_context_chains', 3)
            
            # 相关性计算权重
            if self.relevance_weights is None:
                default_weights = {
                    "chain_length": 0.3,
                    "function_position": 0.3,
                    "call_density": 0.2,
                    "file_relevance": 0.2
                }
                # 从配置变量中读取权重
                weights = {
                    "chain_length": getattr(config, 'context_relevance_weight_chain_length', 0.3),
                    "function_position": getattr(config, 'context_relevance_weight_function_position', 0.3),
                    "call_density": getattr(config, 'context_relevance_weight_call_density', 0.2),
                    "file_relevance": getattr(config, 'context_relevance_weight_file_relevance', 0.2)
                }
                self.relevance_weights = weights
            
            # 函数选择策略
            if self.function_selection_strategy is None:
                self.function_selection_strategy = getattr(config, 'context_function_selection_strategy', 'balanced')
            
            # 上下文选择策略
            if self.context_selection_strategy is None:
                self.context_selection_strategy = getattr(config, 'context_selection_strategy', 'relevance_first')
            
            # 日志配置
            if self.enable_detailed_logging is None:
                self.enable_detailed_logging = getattr(config, 'context_enable_detailed_logging', False)
            
            if self.log_context_building is None:
                self.log_context_building = getattr(config, 'context_log_context_building', True)
            
            # 性能配置

            
            # 过滤配置
            if self.min_relevance_threshold is None:
                self.min_relevance_threshold = getattr(config, 'context_min_relevance_threshold', 0.1)
            
            if self.max_context_size_ratio is None:
                self.max_context_size_ratio = getattr(config, 'context_max_context_size_ratio', 0.8)
            
            # 格式化配置
            if self.include_chain_info is None:
                self.include_chain_info = getattr(config, 'context_include_chain_info', True)
            
            if self.include_relevance_score is None:
                self.include_relevance_score = getattr(config, 'context_include_relevance_score', True)
            
            if self.include_file_paths is None:
                self.include_file_paths = getattr(config, 'context_include_file_paths', True)
                
        except ImportError as e:
            # 如果无法导入配置模块，使用默认值
            self._set_defaults()
    
    def _set_defaults(self):
        """设置默认值（当配置模块不可用时）"""
        # 基础配置默认值
        if self.max_context_size is None:
            self.max_context_size = 8000
        
        if self.max_chain_depth is None:
            self.max_chain_depth = 3
            
        if self.max_chains is None:
            self.max_chains = 3
        
        # 相关性计算权重默认值
        if self.relevance_weights is None:
            self.relevance_weights = {
                "chain_length": 0.3,
                "function_position": 0.3,
                "call_density": 0.2,
                "file_relevance": 0.2
            }
        
        # 函数选择策略默认值
        if self.function_selection_strategy is None:
            self.function_selection_strategy = "balanced"
        
        # 上下文选择策略默认值
        if self.context_selection_strategy is None:
            self.context_selection_strategy = "relevance_first"
        
        # 日志配置默认值
        if self.enable_detailed_logging is None:
            self.enable_detailed_logging = False
        
        if self.log_context_building is None:
            self.log_context_building = True
        

        
        # 过滤配置默认值
        if self.min_relevance_threshold is None:
            self.min_relevance_threshold = 0.1
        
        if self.max_context_size_ratio is None:
            self.max_context_size_ratio = 0.8
        
        # 格式化配置默认值
        if self.include_chain_info is None:
            self.include_chain_info = True
        
        if self.include_relevance_score is None:
            self.include_relevance_score = True
        
        if self.include_file_paths is None:
            self.include_file_paths = True
    
    def validate(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 类型检查和转换
        try:
            if isinstance(self.max_context_size, str):
                self.max_context_size = int(self.max_context_size)
            elif hasattr(self.max_context_size, '__class__') and 'MagicMock' in str(self.max_context_size.__class__):
                # 处理MagicMock对象，使用默认值
                self.max_context_size = 8000
        except (ValueError, TypeError):
            self.max_context_size = 8000
            errors.append("max_context_size 类型错误，使用默认值 8000")
        
        try:
            if isinstance(self.max_chain_depth, str):
                self.max_chain_depth = int(self.max_chain_depth)
            elif hasattr(self.max_chain_depth, '__class__') and 'MagicMock' in str(self.max_chain_depth.__class__):
                # 处理MagicMock对象，使用默认值
                self.max_chain_depth = 3
        except (ValueError, TypeError):
            self.max_chain_depth = 3
            errors.append("max_chain_depth 类型错误，使用默认值 3")
        
        try:
            if isinstance(self.max_chains, str):
                self.max_chains = int(self.max_chains)
            elif hasattr(self.max_chains, '__class__') and 'MagicMock' in str(self.max_chains.__class__):
                # 处理MagicMock对象，使用默认值
                self.max_chains = 3
        except (ValueError, TypeError):
            self.max_chains = 3
            errors.append("max_chains 类型错误，使用默认值 3")
        
        # 处理其他可能为MagicMock的配置项
        if hasattr(self.min_relevance_threshold, '__class__') and 'MagicMock' in str(self.min_relevance_threshold.__class__):
            self.min_relevance_threshold = 0.1
        
        if hasattr(self.max_context_size_ratio, '__class__') and 'MagicMock' in str(self.max_context_size_ratio.__class__):
            self.max_context_size_ratio = 0.8
        
        if hasattr(self.relevance_weights, '__class__') and 'MagicMock' in str(self.relevance_weights.__class__):
            self.relevance_weights = {
                "chain_length": 0.3,
                "function_position": 0.3,
                "call_density": 0.2,
                "file_relevance": 0.2
            }
        
        # 数值验证
        if self.max_context_size <= 0:
            errors.append("max_context_size 必须大于 0")
        
        if self.max_chain_depth <= 0:
            errors.append("max_chain_depth 必须大于 0")
        
        if self.max_chains <= 0:
            errors.append("max_chains 必须大于 0")
        
        if self.min_relevance_threshold < 0 or self.min_relevance_threshold > 1:
            errors.append("min_relevance_threshold 必须在 0-1 之间")
        
        if self.max_context_size_ratio <= 0 or self.max_context_size_ratio > 1:
            errors.append("max_context_size_ratio 必须在 0-1 之间")
        
        # 验证权重总和
        try:
            weight_sum = sum(self.relevance_weights.values())
            if abs(weight_sum - 1.0) > 0.01:
                errors.append(f"relevance_weights 权重总和必须为 1.0，当前为 {weight_sum}")
        except (TypeError, AttributeError):
            # 如果权重计算失败，使用默认权重
            self.relevance_weights = {
                "chain_length": 0.3,
                "function_position": 0.3,
                "call_density": 0.2,
                "file_relevance": 0.2
            }
            errors.append("relevance_weights 计算失败，使用默认权重")
        
        return errors
    
    def get_effective_max_context_size(self) -> int:
        """获取有效的最大上下文大小"""
        return int(self.max_context_size * self.max_context_size_ratio)
    
    @classmethod
    def create_from_config(cls, **kwargs) -> 'ContextManagerConfig':
        """
        从配置创建实例，支持覆盖默认值
        
        Args:
            **kwargs: 要覆盖的配置项
            
        Returns:
            ContextManagerConfig: 配置实例
        """
        return cls(**kwargs)
    
    def reload_config(self):
        """重新加载配置"""
        self._load_config_from_module()
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要"""
        return {
            "max_context_size": self.max_context_size,
            "max_chain_depth": self.max_chain_depth,
            "max_chains": self.max_chains,
            "context_selection_strategy": self.context_selection_strategy,
            "function_selection_strategy": self.function_selection_strategy,
            "log_context_building": self.log_context_building,
            "effective_max_context_size": self.get_effective_max_context_size()
        } 