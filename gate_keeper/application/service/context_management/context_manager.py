"""
上下文管理器 - 基于调用链的智能上下文选择
解决上下文膨胀问题，为每个调用链单独生成优化的上下文

重构版本：使用配置管理和组件化设计，集成规则管理功能
"""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.analysis.chain_selector.scoring_based import \
    ScoringBasedSelector
from gate_keeper.application.service.rule import RuleManager
from gate_keeper.external.code_analyzer import StaticAnalyzer
from gate_keeper.external.code_analyzer.models.function import Function

from .context_manager_config import ContextManagerConfig
from .context_relevance_calculator import ContextRelevanceCalculator
from .context_selector import ContextSelector

logger = logging.getLogger(__name__)


@dataclass
class CallChainContext:
    """调用链上下文"""
    chain: List[str]  # 调用链：['main', 'process_data', 'validate_input']
    functions: List[Function]  # 链中函数的完整定义
    relevance_score: float  # 相关性评分
    context_size: int  # 上下文大小（字符数）


class ContextManager:
    """智能上下文管理器"""
    
    def __init__(self, 
                 static_analyzer: StaticAnalyzer, 
                 config: Optional[ContextManagerConfig] = None,
                 rule_manager: Optional[RuleManager] = None,
                 max_context_size: Optional[int] = None, 
                 max_chain_depth: Optional[int] = None):
        """
        初始化上下文管理器
        
        Args:
            static_analyzer: 静态分析器
            config: 配置对象
            rule_manager: 规则管理器（可选）
            max_context_size: 最大上下文大小（向后兼容）
            max_chain_depth: 最大链深度（向后兼容）
        """
        # 使用配置或创建默认配置
        if config is None:
            config = ContextManagerConfig()
        
        # 向后兼容：允许通过参数覆盖配置
        if max_context_size is not None:
            config.max_context_size = max_context_size
        if max_chain_depth is not None:
            config.max_chain_depth = max_chain_depth
        
        # 验证配置
        config_errors = config.validate()
        if config_errors:
            logger.warning(f"配置验证警告: {config_errors}")
        
        self.config = config
        self.static_analyzer = static_analyzer
        self.rule_manager = rule_manager
        self.chain_selector = ScoringBasedSelector()
        
        # 初始化组件
        self.relevance_calculator = ContextRelevanceCalculator(config)
        self.context_selector = ContextSelector(config)
        
        logger.info(f"上下文管理器初始化完成，配置: {config.max_context_size}字符, {config.max_chains}链, {config.max_chain_depth}深度")
        if rule_manager:
            logger.info("规则管理器已集成")
        
    def generate_optimized_contexts(self, af: AffectedFunction, max_chains: int = None) -> List[CallChainContext]:
        """
        为变更函数生成优化的调用链上下文
        
        Args:
            af: 变更函数
            max_chains: 最大调用链数量
            
        Returns:
            List[CallChainContext]: 优化的调用链上下文列表
        """
        # 使用配置中的默认值
        max_chains = max_chains or self.config.max_chains
        
        if self.config.log_context_building:
            logger.info(f"开始为函数 {af.name} 生成优化上下文")
        
        # 1. 获取所有可能的调用链
        # 获取双向调用链（谁调用它 -> 它 -> 它调用谁）
        bi_chains = self.static_analyzer.get_bidirectional_call_chains(
            af.name, af.filepath, max_depth=self.config.max_chain_depth
        )
        
        # 如果没有调用链，创建单函数链
        if not bi_chains:
            bi_chains = [[af.name]]
        
        call_chains = bi_chains
        
        # 2. 为每个调用链生成上下文
        chain_contexts = []
        for chain in call_chains:
            context = self._build_chain_context(af, chain)
            if context:
                chain_contexts.append(context)
        
        # 3. 过滤低相关性上下文（保留空上下文用于向后兼容）
        filtered_contexts = []
        for context in chain_contexts:
            if context.relevance_score >= self.config.min_relevance_threshold or (not context.functions and not any(node.strip() for node in context.chain)):
                filtered_contexts.append(context)
        
        # 4. 选择最优的调用链
        selected_contexts = self.context_selector.select_optimal_contexts(
            filtered_contexts, 
            max_chains=max_chains,
            max_context_size=self.config.max_context_size
        )
        
        # 5. 记录统计信息
        if self.config.log_context_building:
            stats = self.context_selector.get_selection_statistics(chain_contexts, selected_contexts)
            logger.info(f"函数 {af.name} 上下文生成完成: "
                       f"原始 {stats.get('original_count', 0)} -> "
                       f"选中 {stats.get('selected_count', 0)} "
                       f"(相关性提升: {stats.get('relevance_improvement', 0):.3f})")
        
        return selected_contexts
    

    
    def _build_chain_context(self, af: AffectedFunction, chain: List[str]) -> Optional[CallChainContext]:
        """为单个调用链构建上下文"""
        try:
            # 1. 获取链中所有函数的定义
            functions = []
            total_size = 0

            for node in chain:
                # 支持节点ID格式（如 main.c::main 或 main.c:function:main），提取函数名
                if '::' in node:
                    func_name = node.split('::')[-1]
                elif ':function:' in node:
                    func_name = node.split(':function:', 1)[1]
                else:
                    func_name = node
                func_defs = self.static_analyzer.repo_index.function_definitions.get(func_name, [])
                if func_defs:
                    # 选择最相关的函数定义
                    best_func = self._select_best_function_definition(func_defs, af.filepath)
                    if best_func and best_func.code:
                        functions.append(best_func)
                        total_size += len(best_func.code)

                        # 检查是否超过大小限制
                        if total_size > self.config.max_context_size:
                            if self.config.log_context_building:
                                logger.warning(f"调用链上下文超过大小限制: {total_size} > {self.config.max_context_size}")
                            return None

            # 对于空函数名的情况，创建空的上下文
            if not functions and not any(node.strip() for node in chain):
                return CallChainContext(
                    chain=chain,
                    functions=[],
                    relevance_score=0.0,
                    context_size=0
                )
            
            if not functions:
                return None

            # 2. 使用相关性计算器计算评分
            relevance_score = self.relevance_calculator.calculate_chain_relevance(af, chain, functions)

            return CallChainContext(
                chain=chain,
                functions=functions,
                relevance_score=relevance_score,
                context_size=total_size
            )

        except Exception as e:
            logger.error(f"构建调用链上下文失败: {e}")
            return None
    
    def _select_best_function_definition(self, func_defs: List[Function], target_filepath: str) -> Optional[Function]:
        """选择最相关的函数定义"""
        if not func_defs:
            return None
            
        # 优先选择同文件的定义
        same_file_defs = [f for f in func_defs if f.filepath == target_filepath]
        if same_file_defs:
            return same_file_defs[0]
            
        # 其次选择有完整代码的定义
        code_defs = [f for f in func_defs if f.code and not f.is_declaration]
        if code_defs:
            return code_defs[0]
            
        # 最后选择第一个定义
        return func_defs[0]
    
    # 向后兼容：保留原有方法接口
    def _calculate_chain_relevance(self, af: AffectedFunction, chain: List[str], functions: List[Function]) -> float:
        """计算调用链的相关性评分（向后兼容方法）"""
        return self.relevance_calculator.calculate_chain_relevance(af, chain, functions)
    
    def _select_optimal_contexts(self, contexts: List[CallChainContext], max_chains: int) -> List[CallChainContext]:
        """选择最优的上下文组合（向后兼容方法）"""
        return self.context_selector.select_optimal_contexts(
            contexts, 
            max_chains=max_chains,
            max_context_size=self.config.max_context_size
        )
    
    # 向后兼容：保留原有属性
    @property
    def max_context_size(self) -> int:
        """最大上下文大小（向后兼容属性）"""
        return self.config.max_context_size
    
    @property
    def max_chain_depth(self) -> int:
        """最大链深度（向后兼容属性）"""
        return self.config.max_chain_depth
    
    def format_context_for_prompt(self, context: CallChainContext) -> str:
        """格式化调用链上下文为prompt格式"""
        if not context.functions:
            return f"调用链: {' -> '.join(context.chain)} (无相关函数定义)"
        
        result = f"调用链: {' -> '.join(context.chain)}\n"
        result += f"相关性评分: {context.relevance_score:.3f}\n"
        result += f"上下文大小: {context.context_size} 字符\n\n"
        
        for func in context.functions:
            result += f"// {func.filepath} (行 {func.range.start_line}-{func.range.end_line})\n"
            result += f"{func.code}\n\n"
        
        return result
    
    def get_context_summary(self, contexts: List[CallChainContext]) -> str:
        """获取上下文摘要信息"""
        if not contexts:
            return "无上下文"
        
        total_size = sum(ctx.context_size for ctx in contexts)
        avg_relevance = sum(ctx.relevance_score for ctx in contexts) / len(contexts)
        
        summary = f"上下文数量: {len(contexts)}\n"
        summary += f"总大小: {total_size} 字符\n"
        summary += f"平均相关性: {avg_relevance:.3f}\n"
        
        for i, ctx in enumerate(contexts, 1):
            summary += f"  链{i}: {len(ctx.chain)} 节点, {ctx.context_size} 字符, 相关性 {ctx.relevance_score:.3f}\n"
        
        return summary
    
    def generate_prompt_content(self, af: AffectedFunction, file_path: str) -> str:
        """生成完整的 prompt 内容"""
        # 生成优化的调用链上下文
        chain_contexts = self.generate_optimized_contexts(af)
        
        # 记录上下文摘要
        context_summary = self.get_context_summary(chain_contexts)
        logger.info(f"函数 {af.name} 上下文摘要:\n{context_summary}")
        
        # 构建 prompt 内容
        input_content = ""
        
        if chain_contexts:
            input_content += "调用链上下文:\n"
            for i, context in enumerate(chain_contexts, 1):
                input_content += f"\n=== 调用链 {i} ===\n"
                input_content += self.format_context_for_prompt(context)
                input_content += "\n"
        
        input_content += f"""待检查内容:
// {file_path}
{af.code}
"""
        return input_content
    
    def get_applicable_rules(self, af: AffectedFunction) -> Dict:
        """
        获取适用于指定函数的规则分组
        
        Args:
            af: 受影响的函数
            
        Returns:
            Dict: 分组后的规则字典
        """
        if not self.rule_manager:
            logger.warning("规则管理器未配置，无法获取适用规则")
            return {}
        
        try:
            # 使用规则管理器获取适用规则
            grouped_rules = self.rule_manager.get_applicable_rules(af)
            logger.info(f"为函数 {af.name} 获取到 {len(grouped_rules)} 个规则分组")
            return grouped_rules
        except Exception as e:
            logger.error(f"获取适用规则失败: {e}")
            return {}
    
    def generate_prompt_with_rules(self, af: AffectedFunction, file_path: str) -> Dict[str, str]:
        """
        生成包含规则分组的完整prompt内容
        
        Args:
            af: 受影响的函数
            file_path: 文件路径
            
        Returns:
            Dict[str, str]: 规则组名到prompt内容的映射
        """
        if not self.rule_manager:
            logger.warning("规则管理器未配置，使用默认prompt")
            return {"default": self.generate_prompt_content(af, file_path)}
        
        try:
            # 获取适用规则分组
            grouped_rules = self.get_applicable_rules(af)
            
            # 为每个规则组生成prompt
            prompt_contents = {}
            for group_name, rules in grouped_rules.items():
                # 生成上下文内容
                context_content = self.generate_prompt_content(af, file_path)
                
                # 添加规则内容
                rules_content = self._format_rules_for_prompt(rules)
                
                # 组合完整的prompt
                full_prompt = f"{context_content}\n\n适用规则:\n{rules_content}"
                prompt_contents[group_name] = full_prompt
            
            logger.info(f"为函数 {af.name} 生成了 {len(prompt_contents)} 个规则组的prompt")
            return prompt_contents
            
        except Exception as e:
            logger.error(f"生成规则prompt失败: {e}")
            return {"default": self.generate_prompt_content(af, file_path)}
    
    def _format_rules_for_prompt(self, rules: List) -> str:
        """格式化规则列表为prompt格式"""
        if not rules:
            return "无适用规则"
        
        rules_content = ""
        for i, rule in enumerate(rules, 1):
            rules_content += f"规则 {i}: {rule.id}\n"
            rules_content += f"描述: {rule.name}\n"
            rules_content += f"严重程度: {rule.severity}\n"
            rules_content += f"规则内容:\n{rule.rule_value}\n\n"
        
        return rules_content
    
    def get_rule_summary(self, af: AffectedFunction) -> str:
        """
        获取规则摘要信息
        
        Args:
            af: 受影响的函数
            
        Returns:
            str: 规则摘要
        """
        if not self.rule_manager:
            return "无规则管理器"
        
        try:
            grouped_rules = self.get_applicable_rules(af)
            total_rules = sum(len(rules) for rules in grouped_rules.values())
            
            summary = f"适用规则总数: {total_rules}\n"
            summary += f"规则分组数: {len(grouped_rules)}\n"
            
            for group_name, rules in grouped_rules.items():
                summary += f"  {group_name}: {len(rules)} 条规则\n"
            
            return summary
            
        except Exception as e:
            logger.error(f"获取规则摘要失败: {e}")
            return "规则摘要获取失败" 