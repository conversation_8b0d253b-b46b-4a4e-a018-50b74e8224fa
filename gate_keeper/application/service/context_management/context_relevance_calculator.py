"""
Context Relevance Calculator

上下文相关性计算器，负责计算调用链的相关性评分
"""

import logging
from typing import Dict, List

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.external.code_analyzer.models.function import Function

from .context_manager_config import ContextManagerConfig

logger = logging.getLogger(__name__)


class ContextRelevanceCalculator:
    """上下文相关性计算器"""
    
    def __init__(self, config: ContextManagerConfig):
        self.config = config
    
    def calculate_chain_relevance(self, 
                                 af: AffectedFunction, 
                                 chain: List[str], 
                                 functions: List[Function]) -> float:
        """
        计算调用链的相关性评分
        
        Args:
            af: 受影响的函数
            chain: 调用链
            functions: 链中的函数列表
            
        Returns:
            float: 相关性评分 (0-1)
        """
        if not chain or not functions:
            return 0.0
        
        try:
            # 计算各项评分
            chain_length_score = self._calculate_chain_length_score(chain)
            function_position_score = self._calculate_function_position_score(af, chain)
            call_density_score = self._calculate_call_density_score(chain)
            file_relevance_score = self._calculate_file_relevance_score(af.filepath, functions)
            
            # 加权计算总分
            total_score = (
                chain_length_score * self.config.relevance_weights["chain_length"] +
                function_position_score * self.config.relevance_weights["function_position"] +
                call_density_score * self.config.relevance_weights["call_density"] +
                file_relevance_score * self.config.relevance_weights["file_relevance"]
            )
            
            if self.config.enable_detailed_logging:
                logger.debug(f"相关性评分详情 - 链: {chain}")
                logger.debug(f"  链长度评分: {chain_length_score:.3f}")
                logger.debug(f"  函数位置评分: {function_position_score:.3f}")
                logger.debug(f"  调用密度评分: {call_density_score:.3f}")
                logger.debug(f"  文件相关性评分: {file_relevance_score:.3f}")
                logger.debug(f"  总评分: {total_score:.3f}")
            
            return min(1.0, max(0.0, total_score))
            
        except Exception as e:
            logger.error(f"计算相关性评分失败: {e}")
            return 0.0
    
    def _calculate_chain_length_score(self, chain: List[str]) -> float:
        """计算链长度评分（越短越好）"""
        if not chain:
            return 0.0
        
        # 使用指数衰减函数，链越短评分越高
        length = len(chain)
        if length == 1:
            return 1.0
        elif length <= 3:
            return 0.8
        elif length <= 5:
            return 0.6
        elif length <= 8:
            return 0.4
        else:
            return 0.2
    
    def _calculate_function_position_score(self, af: AffectedFunction, chain: List[str]) -> float:
        """计算函数位置评分"""
        if af.name not in chain:
            return 0.0
        
        # 变更函数在链中的位置越重要越好
        center_index = len(chain) // 2
        af_index = chain.index(af.name)
        distance_from_center = abs(af_index - center_index)
        
        # 距离中心越近评分越高
        if distance_from_center == 0:
            return 1.0
        elif distance_from_center == 1:
            return 0.8
        elif distance_from_center == 2:
            return 0.6
        else:
            return 0.4
    
    def _calculate_call_density_score(self, chain: List[str]) -> float:
        """计算调用密度评分"""
        if len(chain) < 2:
            return 0.0
        
        # 这里需要访问静态分析器的调用关系数据
        # 暂时返回一个基于链长度的启发式评分
        # 链越短，密度越高
        length = len(chain)
        if length <= 2:
            return 0.8
        elif length <= 4:
            return 0.6
        elif length <= 6:
            return 0.4
        else:
            return 0.2
    
    def _calculate_file_relevance_score(self, target_filepath: str, functions: List[Function]) -> float:
        """计算文件相关性评分"""
        if not functions:
            return 0.0
        
        same_file_count = sum(1 for f in functions if f.filepath == target_filepath)
        return same_file_count / len(functions)
    
    def filter_by_relevance_threshold(self, contexts: List, threshold: float = None) -> List:
        """根据相关性阈值过滤上下文"""
        if threshold is None:
            threshold = self.config.min_relevance_threshold
        
        filtered = [ctx for ctx in contexts if ctx.relevance_score >= threshold]
        
        if self.config.log_context_building:
            logger.info(f"相关性过滤: {len(contexts)} -> {len(filtered)} (阈值: {threshold})")
        
        return filtered
    
    def sort_by_relevance(self, contexts: List) -> List:
        """按相关性排序"""
        return sorted(contexts, key=lambda x: x.relevance_score, reverse=True) 