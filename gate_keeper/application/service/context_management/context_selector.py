"""
Context Selector

上下文选择器，负责从候选上下文中选择最优的组合
"""

import logging
from typing import List, Optional

from .context_manager_config import ContextManagerConfig

logger = logging.getLogger(__name__)


class ContextSelector:
    """上下文选择器"""
    
    def __init__(self, config: ContextManagerConfig):
        self.config = config
    
    def select_optimal_contexts(self, 
                               contexts: List, 
                               max_chains: Optional[int] = None,
                               max_context_size: Optional[int] = None) -> List:
        """
        选择最优的上下文组合
        
        Args:
            contexts: 候选上下文列表
            max_chains: 最大链数量
            max_context_size: 最大上下文大小
            
        Returns:
            List: 选中的上下文列表
        """
        if not contexts:
            return []
        
        # 使用配置中的默认值
        max_chains = max_chains or self.config.max_chains
        max_context_size = max_context_size or self.config.get_effective_max_context_size()
        
        # 根据策略选择上下文
        if self.config.context_selection_strategy == "relevance_first":
            return self._select_relevance_first(contexts, max_chains, max_context_size)
        elif self.config.context_selection_strategy == "size_first":
            return self._select_size_first(contexts, max_chains, max_context_size)
        elif self.config.context_selection_strategy == "balanced":
            return self._select_balanced(contexts, max_chains, max_context_size)
        else:
            # 默认使用相关性优先策略
            return self._select_relevance_first(contexts, max_chains, max_context_size)
    
    def _select_relevance_first(self, contexts: List, max_chains: int, max_context_size: int) -> List:
        """相关性优先选择策略"""
        # 按相关性排序
        sorted_contexts = sorted(contexts, key=lambda x: x.relevance_score, reverse=True)
        
        selected = []
        total_size = 0
        
        for context in sorted_contexts:
            # 检查数量限制
            if len(selected) >= max_chains:
                break
            
            # 检查大小限制
            if total_size + context.context_size > max_context_size:
                if self.config.log_context_building:
                    logger.info(f"大小限制，跳过上下文: {context.chain} (大小: {context.context_size})")
                continue
            
            selected.append(context)
            total_size += context.context_size
        
        if self.config.log_context_building:
            logger.info(f"相关性优先选择: {len(contexts)} -> {len(selected)} (总大小: {total_size})")
        
        return selected
    
    def _select_size_first(self, contexts: List, max_chains: int, max_context_size: int) -> List:
        """大小优先选择策略"""
        # 按大小排序（从小到大）
        sorted_contexts = sorted(contexts, key=lambda x: x.context_size)
        
        selected = []
        total_size = 0
        
        for context in sorted_contexts:
            # 检查数量限制
            if len(selected) >= max_chains:
                break
            
            # 检查大小限制
            if total_size + context.context_size > max_context_size:
                if self.config.log_context_building:
                    logger.info(f"大小限制，跳过上下文: {context.chain} (大小: {context.context_size})")
                continue
            
            selected.append(context)
            total_size += context.context_size
        
        if self.config.log_context_building:
            logger.info(f"大小优先选择: {len(contexts)} -> {len(selected)} (总大小: {total_size})")
        
        return selected
    
    def _select_balanced(self, contexts: List, max_chains: int, max_context_size: int) -> List:
        """平衡选择策略"""
        # 计算综合评分（相关性 * 大小效率）
        scored_contexts = []
        for context in contexts:
            # 大小效率 = 1 / (1 + 大小/平均大小)
            avg_size = sum(c.context_size for c in contexts) / len(contexts)
            size_efficiency = 1.0 / (1.0 + context.context_size / avg_size)
            combined_score = context.relevance_score * size_efficiency
            scored_contexts.append((context, combined_score))
        
        # 按综合评分排序
        scored_contexts.sort(key=lambda x: x[1], reverse=True)
        
        selected = []
        total_size = 0
        
        for context, score in scored_contexts:
            # 检查数量限制
            if len(selected) >= max_chains:
                break
            
            # 检查大小限制
            if total_size + context.context_size > max_context_size:
                if self.config.log_context_building:
                    logger.info(f"大小限制，跳过上下文: {context.chain} (综合评分: {score:.3f})")
                continue
            
            selected.append(context)
            total_size += context.context_size
        
        if self.config.log_context_building:
            logger.info(f"平衡选择: {len(contexts)} -> {len(selected)} (总大小: {total_size})")
        
        return selected
    
    def get_selection_statistics(self, original_contexts: List, selected_contexts: List) -> dict:
        """获取选择统计信息"""
        if not original_contexts:
            return {}
        
        total_original_size = sum(c.context_size for c in original_contexts)
        total_selected_size = sum(c.context_size for c in selected_contexts)
        
        avg_original_relevance = sum(c.relevance_score for c in original_contexts) / len(original_contexts)
        avg_selected_relevance = sum(c.relevance_score for c in selected_contexts) / len(selected_contexts) if selected_contexts else 0
        
        return {
            "original_count": len(original_contexts),
            "selected_count": len(selected_contexts),
            "selection_ratio": len(selected_contexts) / len(original_contexts),
            "original_total_size": total_original_size,
            "selected_total_size": total_selected_size,
            "size_reduction_ratio": 1 - (total_selected_size / total_original_size) if total_original_size > 0 else 0,
            "avg_original_relevance": avg_original_relevance,
            "avg_selected_relevance": avg_selected_relevance,
            "relevance_improvement": avg_selected_relevance - avg_original_relevance
        } 