import html
import json
import re
import uuid
from typing import Any, Dict, List, Optional, Tuple

import requests

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.interfaces.git_intf import IGitPlatformService
from gate_keeper.application.service.deduplication.comment_deduplication_service import \
    CommentDeduplicationService
from gate_keeper.application.service.git.git_service import GitService
from gate_keeper.config import config
from gate_keeper.domain.value_objects.analysis_result import (AnalyzeLLMResult,
                                                              ViolationItem)
from gate_keeper.infrastructure.git.client_factory import GitClientFactory
from gate_keeper.shared.log import app_logger as logger


class ReportService:
    def __init__(self, 
                 access_token: str = None, 
                 git_client: Optional[IGitPlatformService] = None):
        self.token = access_token
        
        # 初始化Git客户端
        if git_client:
            # 使用提供的Git客户端
            self._git_client = git_client
        else:
            # 使用工厂创建Git客户端
            self._git_client = GitClientFactory.create_comment_service(
                access_token=self.token or config.token,
            )
        
        # 初始化评论排重服务
        self.deduplication_service = CommentDeduplicationService(self._git_client)
        
        # 初始化Git服务
        self.git_service = GitService(comment_service=self._git_client)
    
    @classmethod
    def create_for_project(cls, 
                       project_id: str,
                       access_token: str = None) -> 'ReportService':
        """
        根据项目ID创建ReportService实例
        Args:
            project_id: 项目ID（owner/repo或codehub project_id）
            access_token: 访问令牌
        Returns:
            ReportService: 配置了对应Git平台的ReportService实例
        """
        git_client = GitClientFactory.create_comment_service(
            access_token=access_token or config.token
        )
        return cls(
            access_token=access_token,
            git_client=git_client
        )

    def __generate_report_header(self) -> str:
        """
        生成报告头
        """
        return "## 代码规范检查报告\n"

    def __generate_rule_meta(self) -> str:
        """
        生成编码守则meta信息
        """
        rule_url = getattr(config, 'rule_file_url', 'N/A')
        return f"[编码守则]({rule_url})\n"

    def __generate_violation_table_header(self) -> str:
        """
        违规详情表格表头
        """
        return "| 规则ID | 优先级 | 规则内容 | 文件路径 | 违规信息 |\n|---|---|---|---|---|"

    def __generate_violation_table_rows(self, violation: List[ViolationItem]) -> List[str]:
        """
        单条违规转为表格行
        """
        result = []
        for vio in violation:
            # 过滤掉无效的违规项
            if vio.rule_id == "unknown":
                logger.warning(f"跳过无效的违规项: rule_id=unknown, message={vio.message[:50]}...")
                continue
            
            if vio.message and "未检测到明显的代码规范问题" in vio.message:
                logger.warning(f"跳过无效的违规项: message包含无效内容, rule_id={vio.rule_id}")
                continue
            
            rule_id = vio.rule_id or "N/A"
            severity = vio.severity or "N/A"
            rule_content = vio.rule_content or "N/A"
            file_path = vio.location.get("file_path", "N/A") or "N/A"
            message = vio.message or "N/A"
            
            # 清理表格内容，确保markdown表格格式正确
            def clean_table_cell(text: str, is_violation_message: bool = False) -> str:
                if not text:
                    return "N/A"
                # 移除换行符，用空格替换
                cleaned = text.replace('\n', ' ').replace('\r', ' ')
                # 移除markdown代码块标记，避免表格渲染冲突
                cleaned = cleaned.replace('```', '').replace('`', '')
                # 移除多余的空格
                cleaned = ' '.join(cleaned.split())
                
                # 违规信息不截断，其他字段适当截断
                if is_violation_message:
                    # 违规信息完整显示，但确保不会导致表格行被分割
                    # 如果太长，用HTML的<br>标签分割，这样在markdown表格中会正确显示
                    if len(cleaned) > 200:  # 设置一个合理的最大长度
                        # 在合适的位置分割（如句号、逗号等）
                        split_chars = ['。', '；', '，', '.', ';', ',']
                        split_pos = -1
                        for char in split_chars:
                            pos = cleaned.find(char, 150)  # 从150字符开始找分割点
                            if pos > 0 and pos < 200:
                                split_pos = pos + 1
                                break
                        
                        if split_pos > 0:
                            cleaned = cleaned[:split_pos] + '<br>' + cleaned[split_pos:]
                        else:
                            # 如果找不到合适的分割点，强制在200字符处分割
                            cleaned = cleaned[:200] + '<br>' + cleaned[200:]
                    
                    # 如果原始文本包含换行符，在清理后的文本中也添加HTML换行标签
                    # 这样可以保持原有的段落结构
                    if '\n' in text:
                        # 在句号、分号等标点符号后添加换行
                        for punct in ['。', '；', '！', '？', '.', '!', '?']:
                            cleaned = cleaned.replace(punct, punct + '<br>')
                        # 移除末尾的换行标签
                        if cleaned.endswith('<br>'):
                            cleaned = cleaned[:-5]
                else:
                    # 其他字段适当截断
                    if len(cleaned) > 50:  # 其他字段限制为50字符
                        cleaned = cleaned[:47] + "..."
                
                return cleaned
            
            clean_rule_id = clean_table_cell(rule_id)
            clean_severity = clean_table_cell(severity)
            clean_rule_content = clean_table_cell(rule_content)
            clean_file_path = clean_table_cell(file_path)
            clean_message = clean_table_cell(message, is_violation_message=True)
            
            result.append(f"| {clean_rule_id} | {clean_severity} | {clean_rule_content} | {clean_file_path} | {clean_message} |")
        return result

    def __generate_code_block(self, code: str) -> str:
        if not code:
            return ""
        return f"\n检查代码:\n```\n{code}\n```"

    def generate_violation_markdown_report(self, analyze_result: AnalyzeLLMResult, report_id: str=None, is_independent: bool = False) -> str:
        """
        生成单条违规的完整Markdown报告（可独立使用）
        """
        report_lines = []
        is_pass = analyze_result.is_pass
        if is_pass:
            return ""
        reason = html.escape(analyze_result.reason)
        violations = analyze_result.violations
        
        # 过滤掉无效的违规项
        valid_violations = []
        for violation in violations:
            if violation.rule_id == "unknown":
                logger.warning(f"跳过无效的违规项: rule_id=unknown, message={violation.message[:50]}...")
                continue
            
            if violation.message and "未检测到明显的代码规范问题" in violation.message:
                logger.warning(f"跳过无效的违规项: message包含无效内容, rule_id={violation.rule_id}")
                continue
            
            valid_violations.append(violation)
        
        # 如果没有有效的违规项，不生成报告
        if not valid_violations:
            logger.info("没有有效的违规项，跳过报告生成")
            return ""
        
        if is_independent:
            report_lines.append(self.__generate_report_header())
            report_lines.append(self.__generate_rule_meta())
        status = "✅ 通过" if is_pass else "❌ 未通过"
        report_lines.append(f"### 检测批次{report_id or uuid.uuid4()}: {status}")
        report_lines.append(f"**原因:** {reason}")
        
        # 添加 call_id 信息，方便调试
        if hasattr(analyze_result, 'call_ids') and analyze_result.call_ids:
            # 聚合结果，显示所有call_id
            call_ids_str = ", ".join(analyze_result.call_ids)
            report_lines.append(f"**调试ID:** `{call_ids_str}`")
        elif analyze_result.call_id:
            # 单个结果，显示单个call_id
            report_lines.append(f"**调试ID:** `{analyze_result.call_id}`")
        
        # 如果是聚合结果，显示函数信息
        if hasattr(analyze_result, 'function_name') and analyze_result.function_name:
            report_lines.append(f"**函数:** `{analyze_result.function_name}`")
        if hasattr(analyze_result, 'file_path') and analyze_result.file_path:
            report_lines.append(f"**文件:** `{analyze_result.file_path}`")
        
        if valid_violations:
            report_lines.append("\n**违规详情:**")
            report_lines.append(self.__generate_violation_table_header())
            report_lines.extend(self.__generate_violation_table_rows(valid_violations))
        if analyze_result.code:
            report_lines.append(self.__generate_code_block(analyze_result.code))
        report_lines.append("\n---\n")
        return "\n".join(report_lines)

    def generate_markdown_report(self, llm_results: list[AnalyzeLLMResult]) -> str:
        """
        生成 Markdown 报告，支持多条检测结果汇总
        """
        if not llm_results or all(r.is_pass for r in llm_results):
            return ""
        report_lines = []
        report_lines.append(self.__generate_report_header())
        report_lines.append(self.__generate_rule_meta())
        for idx, result in enumerate(llm_results):
            report_lines.append(self.generate_violation_markdown_report(result,report_id=f"{idx + 1}",is_independent=False))
        return "\n".join(report_lines) if len(report_lines) > 1 else ""


    def post_mr_comment(self, repo_url: str, mr_id: int, comment: str) -> bool:
        """
        向 MR 发布评论
        """
        if not comment:
            print("评论内容为空，跳过发布。")
            return False

        try:
            url = f"{repo_url}/pulls/{mr_id}/comments"
            headers = {"Authorization": f"Bearer {self.token or config.token}"}
            data = {"body": comment}
            response = requests.post(url, json=data, headers=headers)
            if response.status_code == 200:
                return True
            else:
                print(f"发布评论失败: {response.status_code} {response.text}")
                return False
        except Exception as e:
            print(f"发布评论失败: {e}")
            return False

    def post_mr_comment_with_deduplication(self, project_id: str, mr_id: int, comment: str,
                                         affected_function: Optional[AffectedFunction] = None,
                                         ) -> Dict[str, Any]:
        """
        向 MR 发布评论，支持排重功能和自动反打开
        """
        if not comment:
            return {
                "success": False,
                "reason": "评论内容为空",
                "skipped": True
            }

        try:
            # 检查是否需要跳过（排重）并自动反打开
            should_skip, skip_reason, need_reopen_ids = self.deduplication_service.should_skip_comment(
                project_id=project_id,
                mr_id=mr_id,
                new_comment=comment,
                affected_function=affected_function
            )

            # 自动反打开逻辑
            reopen_results = []
            if need_reopen_ids:
                for discussion_id in need_reopen_ids:
                    try:
                        self._git_client.update_discussion_to_mr(
                            project_id=project_id,
                            mr_id=mr_id,
                            discussion_id=discussion_id,
                            discussion_data={"resolved": False}
                        )
                        reopen_results.append({"id": discussion_id, "success": True})
                    except Exception as e:
                        reopen_results.append({"id": discussion_id, "success": False, "error": str(e)})

            if should_skip:
                logger.info(f"跳过重复评论: {skip_reason}")
                return {
                    "success": True,
                    "reason": f"评论已跳过（排重）: {skip_reason}",
                    "skipped": True,
                    "reopen_results": reopen_results
                }

            
            self.git_service.post_discussion_to_mr(project_id, mr_id, comment)
            logger.info("评论已成功发布")
            return {
                "success": True,
                "reason": "评论发布成功",
                "skipped": False,
                "reopen_results": reopen_results
            }

        except Exception as e:
            logger.error(f"发布评论失败: {e}")
            return {
                "success": False,
                "reason": f"发布失败: {str(e)}",
                "skipped": False
            }