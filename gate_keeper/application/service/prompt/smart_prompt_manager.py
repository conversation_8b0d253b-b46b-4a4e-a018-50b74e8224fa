"""
智能提示词管理器

根据不同的策略和配置，智能生成最优的提示词组合，
平衡规则完整性、上下文相关性和长度限制。
"""

import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import (
    CallChainContext, ContextManager)
from gate_keeper.application.service.prompt.prompt_length_manager import (
    PromptAllocation, PromptLengthManager)
from gate_keeper.application.service.rule.rule_service.manager import \
    RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule

logger = logging.getLogger(__name__)


class PromptStrategy(Enum):
    """提示词策略枚举"""
    BALANCED = "balanced"           # 平衡策略：规则和上下文并重
    RULE_FOCUSED = "rule_focused"   # 规则重点：优先保证规则完整性
    CONTEXT_FOCUSED = "context_focused"  # 上下文重点：优先保证上下文完整性
    MINIMAL = "minimal"             # 最小策略：最小化提示词长度
    COMPREHENSIVE = "comprehensive" # 全面策略：最大化信息覆盖


@dataclass
class SmartPromptConfig:
    """智能提示词配置"""
    # 基础配置
    max_prompt_length: int = 32000
    prompt_strategy: PromptStrategy = PromptStrategy.BALANCED
    
    # 规则分组配置
    rule_grouping_strategy: str = "adaptive"
    min_rule_group_size: int = 3
    max_rule_group_size: int = 8
    target_rule_group_size: int = 5
    
    # 调用链配置
    max_context_chains: int = 3
    max_context_chain_depth: int = 3
    max_context_size: int = 8000
    
    # 长度分配配置
    allocation_strategy: str = "balanced"
    
    # 优先级配置
    rule_priority_weights: Dict[str, float] = None
    context_priority_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.rule_priority_weights is None:
            self.rule_priority_weights = {
                "security": 1.0,
                "performance": 0.8,
                "naming": 0.6,
                "structure": 0.7,
                "default": 0.5
            }
        
        if self.context_priority_weights is None:
            self.context_priority_weights = {
                "chain_length": 0.3,
                "relevance_score": 0.4,
                "file_relevance": 0.2,
                "call_density": 0.1
            }


@dataclass
class SmartPromptResult:
    """智能提示词结果"""
    prompt: str                           # 生成的提示词
    allocation: PromptAllocation          # 长度分配方案
    selected_rules: List[CodeCheckRule]   # 选中的规则
    selected_contexts: List[CallChainContext]  # 选中的上下文
    rule_groups: Dict[str, List[CodeCheckRule]]  # 规则分组
    metadata: Dict[str, Any]              # 元数据


class SmartPromptManager:
    """全局智能提示词管理器"""
    
    def __init__(self, 
                 rule_manager: RuleManager,
                 context_manager: Optional[ContextManager] = None,
                 config: Optional[SmartPromptConfig] = None):
        """
        初始化智能提示词管理器
        
        Args:
            rule_manager: 规则管理器
            context_manager: 上下文管理器
            config: 配置对象
        """
        self.rule_manager = rule_manager
        self.context_manager = context_manager
        self.config = config or SmartPromptConfig()
        
        # 初始化提示词长度管理器
        self.prompt_length_manager = PromptLengthManager(
            max_prompt_length=self.config.max_prompt_length,
            context_manager=self.context_manager,
            allocation_strategy=self.config.allocation_strategy
        )
        
        logger.info(f"智能提示词管理器初始化完成，策略: {self.config.prompt_strategy.value}")
    
    def generate_smart_prompt(self,
                             affected_function: AffectedFunction,
                             system_prompt: str,
                             output_format: str = "",
                             custom_rules: Optional[List[CodeCheckRule]] = None) -> SmartPromptResult:
        """
        生成智能提示词
        
        Args:
            affected_function: 受影响的函数
            system_prompt: 系统提示词
            output_format: 输出格式
            custom_rules: 自定义规则（可选）
            
        Returns:
            SmartPromptResult: 智能提示词结果
        """
        logger.info(f"开始为函数 {affected_function.name} 生成智能提示词")
        
        # 1. 获取适用的规则分组
        rule_groups = self._get_optimized_rule_groups(affected_function, custom_rules)
        
        # 2. 获取优化的调用链上下文
        contexts = self._get_optimized_contexts(affected_function)
        
        # 3. 根据策略选择最优的规则和上下文组合
        selected_rules, selected_contexts = self._select_optimal_combination(
            rule_groups, contexts, affected_function
        )
        
        # 4. 生成优化的提示词
        optimized_prompt, allocation = self.prompt_length_manager.generate_optimized_prompt(
            system_prompt=system_prompt,
            rules=selected_rules,
            affected_function=affected_function,
            contexts=selected_contexts,
            output_format=output_format
        )
        
        # 5. 构建结果
        # 计算总规则数
        if isinstance(rule_groups, dict):
            total_rules = sum(len(rules) for rules in rule_groups.values())
        else:
            total_rules = len(rule_groups) if isinstance(rule_groups, list) else 0
        
        result = SmartPromptResult(
            prompt=optimized_prompt,
            allocation=allocation,
            selected_rules=selected_rules,
            selected_contexts=selected_contexts,
            rule_groups=rule_groups,
            metadata={
                "strategy": self.config.prompt_strategy.value,
                "total_rules": total_rules,
                "selected_rules_count": len(selected_rules),
                "total_contexts": len(contexts),
                "selected_contexts_count": len(selected_contexts),
                "prompt_length": len(optimized_prompt),
                "utilization_rate": len(optimized_prompt) / self.config.max_prompt_length
            }
        )
        
        # 计算总规则数用于日志
        if isinstance(rule_groups, dict):
            total_rules = sum(len(rules) for rules in rule_groups.values())
        else:
            total_rules = len(rule_groups) if isinstance(rule_groups, list) else 0
        
        logger.info(f"智能提示词生成完成: "
                   f"规则 {len(selected_rules)}/{total_rules}, "
                   f"上下文 {len(selected_contexts)}/{len(contexts)}, "
                   f"长度 {len(optimized_prompt)}/{self.config.max_prompt_length}")
        
        return result
    
    def _get_optimized_rule_groups(self, 
                                  affected_function: AffectedFunction,
                                  custom_rules: Optional[List[CodeCheckRule]] = None) -> Dict[str, List[CodeCheckRule]]:
        """获取优化的规则分组"""
        if custom_rules:
            # 使用自定义规则
            rules = custom_rules
        else:
            # 从规则管理器获取适用规则
            rule_groups = self.rule_manager.get_applicable_rules(
                affected_function, 
                self.config.rule_grouping_strategy
            )
            return rule_groups
        
        # 对自定义规则进行分组
        if self.config.rule_grouping_strategy == "category":
            return self._group_rules_by_category(rules)
        elif self.config.rule_grouping_strategy == "similarity":
            return self._group_rules_by_similarity(rules)
        elif self.config.rule_grouping_strategy == "adaptive":
            return self._group_rules_adaptive(rules)
        else:
            return {"custom_group": rules}
    
    def _get_optimized_contexts(self, affected_function: AffectedFunction) -> List[CallChainContext]:
        """获取优化的调用链上下文"""
        if not self.context_manager:
            # 如果没有上下文管理器，返回空上下文
            return []
        
        try:
            contexts = self.context_manager.generate_optimized_contexts(
                affected_function, 
                max_chains=self.config.max_context_chains
            )
            return contexts
        except Exception as e:
            logger.warning(f"获取调用链上下文失败: {e}")
            return []
    
    def _select_optimal_combination(self,
                                  rule_groups: Dict[str, List[CodeCheckRule]],
                                  contexts: List[CallChainContext],
                                  affected_function: AffectedFunction) -> Tuple[List[CodeCheckRule], List[CallChainContext]]:
        """根据策略选择最优的规则和上下文组合"""
        
        if self.config.prompt_strategy == PromptStrategy.RULE_FOCUSED:
            return self._select_rule_focused_combination(rule_groups, contexts)
        elif self.config.prompt_strategy == PromptStrategy.CONTEXT_FOCUSED:
            return self._select_context_focused_combination(rule_groups, contexts)
        elif self.config.prompt_strategy == PromptStrategy.MINIMAL:
            return self._select_minimal_combination(rule_groups, contexts)
        elif self.config.prompt_strategy == PromptStrategy.COMPREHENSIVE:
            return self._select_comprehensive_combination(rule_groups, contexts)
        else:  # BALANCED
            return self._select_balanced_combination(rule_groups, contexts)
    
    def _select_balanced_combination(self,
                                   rule_groups: Dict[str, List[CodeCheckRule]],
                                   contexts: List[CallChainContext]) -> Tuple[List[CodeCheckRule], List[CallChainContext]]:
        """平衡策略：规则和上下文并重"""
        # 计算可用长度
        target_code_length = len(f"// test.py\ndef test_function():\n    pass")
        available_length = self.config.max_prompt_length - target_code_length
        
        # 平衡分配：规则50%，上下文50%
        rules_length = int(available_length * 0.5)
        contexts_length = available_length - rules_length
        
        # 选择规则
        selected_rules = self._select_rules_by_priority(rule_groups, rules_length)
        
        # 选择上下文
        selected_contexts = self._select_contexts_by_priority(contexts, contexts_length)
        
        return selected_rules, selected_contexts
    
    def _select_rule_focused_combination(self,
                                       rule_groups: Dict[str, List[CodeCheckRule]],
                                       contexts: List[CallChainContext]) -> Tuple[List[CodeCheckRule], List[CallChainContext]]:
        """规则重点策略：优先保证规则完整性"""
        # 计算可用长度
        target_code_length = len(f"// test.py\ndef test_function():\n    pass")
        available_length = self.config.max_prompt_length - target_code_length
        
        # 规则重点分配：规则70%，上下文30%
        rules_length = int(available_length * 0.7)
        contexts_length = available_length - rules_length
        
        # 选择规则
        selected_rules = self._select_rules_by_priority(rule_groups, rules_length)
        
        # 选择上下文
        selected_contexts = self._select_contexts_by_priority(contexts, contexts_length)
        
        return selected_rules, selected_contexts
    
    def _select_context_focused_combination(self,
                                          rule_groups: Dict[str, List[CodeCheckRule]],
                                          contexts: List[CallChainContext]) -> Tuple[List[CodeCheckRule], List[CallChainContext]]:
        """上下文重点策略：优先保证上下文完整性"""
        # 计算可用长度
        target_code_length = len(f"// test.py\ndef test_function():\n    pass")
        available_length = self.config.max_prompt_length - target_code_length
        
        # 上下文重点分配：规则30%，上下文70%
        rules_length = int(available_length * 0.3)
        contexts_length = available_length - rules_length
        
        # 选择规则
        selected_rules = self._select_rules_by_priority(rule_groups, rules_length)
        
        # 选择上下文
        selected_contexts = self._select_contexts_by_priority(contexts, contexts_length)
        
        return selected_rules, selected_contexts
    
    def _select_minimal_combination(self,
                                  rule_groups: Dict[str, List[CodeCheckRule]],
                                  contexts: List[CallChainContext]) -> Tuple[List[CodeCheckRule], List[CallChainContext]]:
        """最小策略：最小化提示词长度"""
        # 只选择最重要的规则和上下文
        selected_rules = self._select_top_priority_rules(rule_groups, max_count=3)
        selected_contexts = self._select_top_priority_contexts(contexts, max_count=1)
        
        return selected_rules, selected_contexts
    
    def _select_comprehensive_combination(self,
                                        rule_groups: Dict[str, List[CodeCheckRule]],
                                        contexts: List[CallChainContext]) -> Tuple[List[CodeCheckRule], List[CallChainContext]]:
        """全面策略：最大化信息覆盖"""
        # 计算可用长度
        target_code_length = len(f"// test.py\ndef test_function():\n    pass")
        available_length = self.config.max_prompt_length - target_code_length
        
        # 全面分配：规则60%，上下文40%
        rules_length = int(available_length * 0.6)
        contexts_length = available_length - rules_length
        
        # 选择规则
        selected_rules = self._select_rules_by_priority(rule_groups, rules_length)
        
        # 选择上下文
        selected_contexts = self._select_contexts_by_priority(contexts, contexts_length)
        
        return selected_rules, selected_contexts
    
    def _select_rules_by_priority(self, 
                                rule_groups: Dict[str, List[CodeCheckRule]], 
                                max_length: int) -> List[CodeCheckRule]:
        """根据优先级选择规则"""
        # 将所有规则展平
        all_rules = []
        if isinstance(rule_groups, dict):
            for group_name, rules in rule_groups.items():
                if isinstance(rules, list):
                    all_rules.extend(rules)
        elif isinstance(rule_groups, list):
            all_rules = rule_groups
        
        # 使用提示词长度管理器选择最优规则
        return self.prompt_length_manager.select_optimal_rules(all_rules, max_length)
    
    def _select_contexts_by_priority(self, 
                                   contexts: List[CallChainContext], 
                                   max_length: int) -> List[CallChainContext]:
        """根据优先级选择上下文"""
        # 使用提示词长度管理器选择最优上下文
        return self.prompt_length_manager.select_optimal_contexts(contexts, max_length)
    
    def _select_top_priority_rules(self, 
                                 rule_groups: Dict[str, List[CodeCheckRule]], 
                                 max_count: int) -> List[CodeCheckRule]:
        """选择最高优先级的规则"""
        # 将所有规则展平并按优先级排序
        all_rules = []
        for group_name, rules in rule_groups.items():
            all_rules.extend(rules)
        
        # 按优先级排序
        all_rules.sort(key=lambda rule: self._calculate_rule_priority(rule), reverse=True)
        
        return all_rules[:max_count]
    
    def _select_top_priority_contexts(self, 
                                    contexts: List[CallChainContext], 
                                    max_count: int) -> List[CallChainContext]:
        """选择最高优先级的上下文"""
        # 按相关性评分排序
        sorted_contexts = sorted(contexts, key=lambda ctx: ctx.relevance_score, reverse=True)
        return sorted_contexts[:max_count]
    
    def _calculate_rule_priority(self, rule: CodeCheckRule) -> float:
        """计算规则优先级"""
        base_priority = 0.5
        
        # 根据类别调整优先级
        if rule.category:
            for category in rule.category:
                category_lower = category.lower()
                if "安全" in category or "security" in category_lower:
                    base_priority += self.config.rule_priority_weights.get("security", 1.0)
                elif "性能" in category or "performance" in category_lower:
                    base_priority += self.config.rule_priority_weights.get("performance", 0.8)
                elif "命名" in category or "naming" in category_lower:
                    base_priority += self.config.rule_priority_weights.get("naming", 0.6)
                elif "结构" in category or "structure" in category_lower:
                    base_priority += self.config.rule_priority_weights.get("structure", 0.7)
        
        # 不限制最大值，允许优先级超过1.0
        return max(base_priority, 0.0)
    
    def _group_rules_by_category(self, rules: List[CodeCheckRule]) -> Dict[str, List[CodeCheckRule]]:
        """按类别分组规则"""
        grouped = {}
        for rule in rules:
            if rule.category:
                category_key = tuple(rule.category)
            else:
                category_key = ("default",)
            
            if category_key not in grouped:
                grouped[category_key] = []
            grouped[category_key].append(rule)
        
        return grouped
    
    def _group_rules_by_similarity(self, rules: List[CodeCheckRule]) -> Dict[str, List[CodeCheckRule]]:
        """按相似度分组规则"""
        # 简化实现，实际可以使用更复杂的相似度算法
        return self.rule_manager.group_rules_by_similarity_for_list(rules, 0.7)
    
    def _group_rules_adaptive(self, rules: List[CodeCheckRule]) -> Dict[str, List[CodeCheckRule]]:
        """自适应分组规则"""
        return self.rule_manager.group_rules_adaptive_for_list(
            rules,
            self.config.min_rule_group_size,
            self.config.max_rule_group_size,
            self.config.target_rule_group_size
        )
    
    def get_prompt_statistics(self, result: SmartPromptResult) -> Dict[str, Any]:
        """获取提示词统计信息"""
        total_rules = sum(len(rules) for rules in result.rule_groups.values()) if result.rule_groups else 0
        total_contexts = result.metadata.get("total_contexts", 0)
        
        return {
            "prompt_length": len(result.prompt),
            "max_length": self.config.max_prompt_length,
            "utilization_rate": len(result.prompt) / self.config.max_prompt_length,
            "rule_coverage": len(result.selected_rules) / total_rules if total_rules > 0 else 0.0,
            "context_coverage": len(result.selected_contexts) / total_contexts if total_contexts > 0 else 0.0,
            "strategy": self.config.prompt_strategy.value,
            "allocation": {
                "rules_length": result.allocation.rules_length,
                "context_length": result.allocation.context_length,
                "target_code_length": result.allocation.target_code_length,
                "remaining_length": result.allocation.remaining_length
            }
        } 