"""
智能提示词长度管理器

根据LLM的token限制，智能分配提示词各部分的长度，
确保最重要的内容得到优先保留。
"""

import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Tuple

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import (
    CallChainContext, ContextManager)
from gate_keeper.domain.rule.check_rule import CodeCheckRule

logger = logging.getLogger(__name__)


class ContentType(Enum):
    """内容类型枚举"""
    SYSTEM_PROMPT = "system_prompt"
    RULES = "rules"
    CONTEXT = "context"
    TARGET_CODE = "target_code"
    OUTPUT_FORMAT = "output_format"


@dataclass
class ContentSegment:
    """内容片段"""
    content_type: ContentType
    content: str
    length: int
    priority: float  # 优先级 (0.0-1.0)
    is_required: bool = True  # 是否必需
    original_rule: Optional[CodeCheckRule] = None  # 保存原始规则
    original_context: Optional[CallChainContext] = None  # 保存原始上下文


@dataclass
class PromptAllocation:
    """提示词分配方案"""
    total_limit: int
    system_prompt_length: int
    rules_length: int
    context_length: int
    target_code_length: int
    output_format_length: int
    remaining_length: int


class PromptLengthManager:
    """提示词长度管理器"""
    
    def __init__(self, 
                 max_prompt_length: int = 32000,
                 context_manager: Optional[ContextManager] = None,
                 allocation_strategy: str = "balanced"):
        """
        初始化提示词长度管理器
        
        Args:
            max_prompt_length: 最大提示词长度
            context_manager: 上下文管理器
            allocation_strategy: 分配策略 ("balanced", "context_heavy", "rules_heavy")
        """
        # 验证参数
        if max_prompt_length < 0:
            raise ValueError(f"max_prompt_length 不能为负数: {max_prompt_length}")
        
        self.max_prompt_length = max_prompt_length
        self.context_manager = context_manager
        self.allocation_strategy = allocation_strategy
        
        # 默认分配比例
        self.default_allocation = self._get_default_allocation()
    
    def _get_default_allocation(self) -> Dict[str, float]:
        """获取默认分配比例"""
        if self.allocation_strategy == "context_heavy":
            return {
                "system_prompt": 0.05,  # 5%
                "rules": 0.25,          # 25%
                "context": 0.50,        # 50%
                "target_code": 0.15,    # 15%
                "output_format": 0.05   # 5%
            }
        elif self.allocation_strategy == "rules_heavy":
            return {
                "system_prompt": 0.05,  # 5%
                "rules": 0.50,          # 50%
                "context": 0.25,        # 25%
                "target_code": 0.15,    # 15%
                "output_format": 0.05   # 5%
            }
        else:  # balanced
            return {
                "system_prompt": 0.05,  # 5%
                "rules": 0.35,          # 35%
                "context": 0.35,        # 35%
                "target_code": 0.20,    # 20%
                "output_format": 0.05   # 5%
            }
    
    def calculate_prompt_allocation(self, 
                                  system_prompt: str,
                                  target_code: str,
                                  output_format: str = "") -> PromptAllocation:
        """
        计算提示词分配方案
        
        Args:
            system_prompt: 系统提示词
            target_code: 目标代码
            output_format: 输出格式
            
        Returns:
            PromptAllocation: 分配方案
        """
        # 计算必需内容的长度
        system_prompt_length = len(system_prompt)
        target_code_length = len(target_code)
        output_format_length = len(output_format)
        
        required_length = system_prompt_length + target_code_length + output_format_length
        
        # 计算可用长度
        available_length = self.max_prompt_length - required_length
        
        if available_length <= 0:
            logger.warning(f"必需内容长度 ({required_length}) 超过总限制 ({self.max_prompt_length})")
            available_length = 0
        
        # 根据策略分配剩余长度
        rules_length = int(available_length * self.default_allocation["rules"])
        context_length = int(available_length * self.default_allocation["context"])
        
        return PromptAllocation(
            total_limit=self.max_prompt_length,
            system_prompt_length=system_prompt_length,
            rules_length=rules_length,
            context_length=context_length,
            target_code_length=target_code_length,
            output_format_length=output_format_length,
            remaining_length=available_length - rules_length - context_length
        )
    
    def select_optimal_rules(self, 
                           rules: List[CodeCheckRule], 
                           max_length: int) -> List[CodeCheckRule]:
        """
        选择最优的规则组合
        
        Args:
            rules: 规则列表
            max_length: 最大长度限制
            
        Returns:
            List[CodeCheckRule]: 选中的规则列表
        """
        if not rules or max_length <= 0:
            return []
        
        # 计算每个规则的优先级和长度
        rule_segments = []
        for rule in rules:
            if rule is None:
                continue
                
            try:
                rule_content = self._format_rule_content(rule)
                rule_length = len(rule_content)
                priority = self._calculate_rule_priority(rule)
                
                rule_segments.append(ContentSegment(
                    content_type=ContentType.RULES,
                    content=rule_content,
                    length=rule_length,
                    priority=priority,
                    is_required=False,
                    original_rule=rule  # 保存原始规则
                ))
            except (AttributeError, TypeError) as e:
                logger.warning(f"跳过无效规则: {e}")
                continue
        
        # 按优先级排序
        rule_segments.sort(key=lambda x: x.priority, reverse=True)
        
        # 贪心选择
        selected_rules = []
        current_length = 0
        
        for segment in rule_segments:
            if current_length + segment.length <= max_length:
                selected_rules.append(segment)
                current_length += segment.length
            else:
                # 尝试截断规则内容
                truncated_content = self._truncate_rule_content(segment.content, max_length - current_length)
                if truncated_content:
                    segment.content = truncated_content
                    segment.length = len(truncated_content)
                    selected_rules.append(segment)
                break
        
        # 返回原始规则对象
        return [segment.original_rule for segment in selected_rules]
    
    def select_optimal_contexts(self, 
                              contexts: List[CallChainContext], 
                              max_length: int) -> List[CallChainContext]:
        """
        选择最优的上下文组合
        
        Args:
            contexts: 上下文列表
            max_length: 最大长度限制
            
        Returns:
            List[CallChainContext]: 选中的上下文列表
        """
        if not contexts or max_length <= 0:
            return []
        
        # 计算每个上下文的优先级和长度
        context_segments = []
        for context in contexts:
            if context is None:
                continue
                
            try:
                context_content = self._format_context_content(context)
                context_length = len(context_content)
                priority = getattr(context, 'relevance_score', 0.5)  # 使用相关性评分作为优先级
                
                context_segments.append(ContentSegment(
                    content_type=ContentType.CONTEXT,
                    content=context_content,
                    length=context_length,
                    priority=priority,
                    is_required=False,
                    original_context=context  # 保存原始上下文
                ))
            except (AttributeError, TypeError) as e:
                logger.warning(f"跳过无效上下文: {e}")
                continue
        
        # 按优先级排序
        context_segments.sort(key=lambda x: x.priority, reverse=True)
        
        # 贪心选择
        selected_contexts = []
        current_length = 0
        
        for segment in context_segments:
            if current_length + segment.length <= max_length:
                selected_contexts.append(segment)
                current_length += segment.length
            else:
                # 尝试截断上下文内容
                truncated_content = self._truncate_context_content(segment.content, max_length - current_length)
                if truncated_content:
                    segment.content = truncated_content
                    segment.length = len(truncated_content)
                    selected_contexts.append(segment)
                break
        
        # 返回原始上下文对象
        return [segment.original_context for segment in selected_contexts]
    
    def generate_optimized_prompt(self,
                                system_prompt: str,
                                rules: List[CodeCheckRule],
                                affected_function: AffectedFunction,
                                contexts: List[CallChainContext],
                                output_format: str = "") -> Tuple[str, PromptAllocation]:
        """
        生成优化的提示词
        
        Args:
            system_prompt: 系统提示词
            rules: 规则列表
            affected_function: 受影响的函数
            contexts: 上下文列表
            output_format: 输出格式
            
        Returns:
            Tuple[str, PromptAllocation]: 优化后的提示词和分配方案
        """
        # 计算分配方案
        target_code = f"// {affected_function.filepath}\n{affected_function.code}"
        allocation = self.calculate_prompt_allocation(system_prompt, target_code, output_format)
        
        # 选择最优规则
        selected_rules = self.select_optimal_rules(rules, allocation.rules_length)
        
        # 选择最优上下文
        selected_contexts = self.select_optimal_contexts(contexts, allocation.context_length)
        
        # 构建最终提示词
        optimized_prompt = self._build_final_prompt(
            system_prompt=system_prompt,
            selected_rules=selected_rules,
            selected_contexts=selected_contexts,
            target_code=target_code,
            output_format=output_format
        )
        
        logger.info(f"提示词优化完成: 总长度={len(optimized_prompt)}, "
                   f"规则数={len(selected_rules)}/{len(rules)}, "
                   f"上下文数={len(selected_contexts)}/{len(contexts)}")
        
        return optimized_prompt, allocation
    
    def _format_rule_content(self, rule: CodeCheckRule) -> str:
        """格式化规则内容"""
        content = f"规则ID: {rule.id}\n"
        content += f"规则名称: {rule.name}\n"
        content += f"描述: {rule.description}\n"
        if rule.category:
            content += f"类别: {', '.join(rule.category)}\n"
        if rule.languages:
            content += f"适用语言: {', '.join(rule.languages)}\n"
        content += "\n"
        return content
    
    def _format_context_content(self, context: CallChainContext) -> str:
        """格式化上下文内容"""
        content = f"调用链: {' -> '.join(context.chain)}\n"
        content += f"相关性评分: {context.relevance_score:.2f}\n\n"
        
        for func in context.functions:
            content += f"// {func.filepath}\n"
            content += f"{func.code}\n\n"
        
        return content
    
    def _calculate_rule_priority(self, rule: CodeCheckRule) -> float:
        """计算规则优先级"""
        priority = 0.5  # 基础优先级
        
        # 根据类别调整优先级
        if rule.category:
            # 安全相关规则优先级更高
            if any("安全" in cat or "security" in cat.lower() for cat in rule.category):
                priority += 0.3
            # 性能相关规则优先级较高
            elif any("性能" in cat or "performance" in cat.lower() for cat in rule.category):
                priority += 0.2
            # 命名规范优先级较低
            elif any("命名" in cat or "naming" in cat.lower() for cat in rule.category):
                priority -= 0.1
        
        # 根据语言匹配度调整优先级
        if rule.languages and len(rule.languages) == 1:
            priority += 0.1  # 单一语言规则优先级稍高
        
        return min(max(priority, 0.0), 1.0)
    
    def _truncate_rule_content(self, content: str, max_length: int) -> str:
        """截断规则内容"""
        if len(content) <= max_length:
            return content
        
        # 保留规则ID和名称，截断描述
        lines = content.split('\n')
        essential_lines = []
        current_length = 0
        
        for line in lines:
            if line.startswith(('规则ID:', '规则名称:')):
                essential_lines.append(line)
                current_length += len(line) + 1
            elif line.startswith('描述:') and current_length + len(line) + 1 <= max_length:
                # 截断描述
                remaining_length = max_length - current_length - 10  # 保留"描述: "和省略号
                if remaining_length > 0:
                    truncated_desc = line[:remaining_length] + "..."
                    essential_lines.append(truncated_desc)
                    break
        
        return '\n'.join(essential_lines) if essential_lines else ""
    
    def _truncate_context_content(self, content: str, max_length: int) -> str:
        """截断上下文内容"""
        if len(content) <= max_length:
            return content
        
        # 保留调用链信息，截断函数代码
        lines = content.split('\n')
        essential_lines = []
        current_length = 0
        
        for line in lines:
            if line.startswith(('调用链:', '相关性评分:')):
                essential_lines.append(line)
                current_length += len(line) + 1
            elif line.startswith('//') and current_length + len(line) + 1 <= max_length:
                essential_lines.append(line)
                current_length += len(line) + 1
            elif current_length + len(line) + 1 <= max_length:
                essential_lines.append(line)
                current_length += len(line) + 1
            else:
                break
        
        return '\n'.join(essential_lines) if essential_lines else ""
    
    def _content_to_rule(self, content: str) -> CodeCheckRule:
        """将内容转换回规则对象（简化实现）"""
        # 这里需要根据实际需求实现内容到规则的转换
        # 为了简化，返回一个基础规则对象
        return CodeCheckRule(
            id="truncated_rule",
            name="截断的规则",
            description=content,
            category=["其他"],
            enabled=True,
            languages=["*"]
        )
    
    def _content_to_context(self, content: str) -> CallChainContext:
        """将内容转换回上下文对象（简化实现）"""
        # 这里需要根据实际需求实现内容到上下文的转换
        # 为了简化，返回一个基础上下文对象
        return CallChainContext(
            chain=["truncated_chain"],
            functions=[],
            relevance_score=0.5,
            context_size=len(content)
        )
    
    def _build_final_prompt(self,
                           system_prompt: str,
                           selected_rules: List[CodeCheckRule],
                           selected_contexts: List[CallChainContext],
                           target_code: str,
                           output_format: str) -> str:
        """构建最终提示词"""
        parts = []
        
        # 系统提示词
        parts.append(system_prompt)
        parts.append("")
        
        # 规则部分
        if selected_rules:
            parts.append("编码规范:")
            for rule in selected_rules:
                parts.append(self._format_rule_content(rule))
        
        # 上下文部分
        if selected_contexts:
            parts.append("调用链上下文:")
            for i, context in enumerate(selected_contexts, 1):
                parts.append(f"\n=== 调用链 {i} ===")
                parts.append(self._format_context_content(context))
        
        # 目标代码
        parts.append("待检查内容:")
        parts.append(target_code)
        
        # 输出格式
        if output_format:
            parts.append("")
            parts.append(output_format)
        
        return "\n".join(parts) 