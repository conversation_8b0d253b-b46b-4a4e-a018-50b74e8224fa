"""
提示词管理器

负责组织和管理提示词模板，根据规则和上下文生成最终的prompt。
让LLM服务成为纯粹的执行者。
"""

from typing import List, Optional

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.application.service.rule.rule_service.manager import \
    RuleManager
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.infrastructure.llm.dto.prompt import Prompt
from gate_keeper.infrastructure.llm.prompt.tasks.analyze_code.analyze_code import \
    create_instruction
from gate_keeper.infrastructure.llm.prompt.tasks.analyze_code.c import SYSTEM
from gate_keeper.shared.log import app_logger as logger


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, rule_manager: RuleManager, context_manager: Optional[ContextManager] = None):
        self.rule_manager = rule_manager
        self.context_manager = context_manager
        logger.info("提示词管理器初始化完成")
    
    def generate_prompt(self, function_info: AffectedFunction, rules: List[CodeCheckRule]) -> str:
        """生成完整的prompt"""
        try:
            # 1. 获取规则内容
            rule_content = self._get_rule_content(rules)
            
            # 2. 获取上下文（如果有）
            context_content = ""
            if self.context_manager:
                context_content = self._get_context_content(function_info)
            
            # 3. 组装prompt
            prompt = self._assemble_prompt(
                rule_content=rule_content,
                context_content=context_content,
                function_code=function_info.code,
                file_path=function_info.filepath
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"生成prompt失败: {e}")
            # 回退到简单模式
            return self._generate_simple_prompt(function_info, rules)
    
    def _get_rule_content(self, rules: List[CodeCheckRule]) -> str:
        """获取规则内容"""
        if not rules:
            return ""
        
        rule_contents = []
        for rule in rules:
            if hasattr(rule, 'content') and rule.content:
                rule_contents.append(rule.content)
        
        return "\n\n".join(rule_contents)
    
    def _get_context_content(self, function_info: AffectedFunction) -> str:
        """获取上下文内容"""
        if not self.context_manager:
            return ""
        
        try:
            # 生成优化的调用链上下文
            chain_contexts = self.context_manager.generate_optimized_contexts(
                function_info, max_chains=3
            )
            
            # 记录上下文摘要
            context_summary = self.context_manager.get_context_summary(chain_contexts)
            logger.info(f"函数 {function_info.name} 上下文摘要:\n{context_summary}")
            
            # 构建上下文内容
            context_content = ""
            if chain_contexts:
                context_content += "调用链上下文:\n"
                for i, context in enumerate(chain_contexts, 1):
                    context_content += f"\n=== 调用链 {i} ===\n"
                    context_content += self.context_manager.format_context_for_prompt(context)
                    context_content += "\n"
            
            return context_content
            
        except Exception as e:
            logger.error(f"获取上下文内容失败: {e}")
            return ""
    
    def _assemble_prompt(self, rule_content: str, context_content: str, 
                        function_code: str, file_path: str) -> str:
        """组装prompt"""
        # 创建Prompt对象
        p = Prompt(
            system=SYSTEM.encode("utf-8").decode("utf-8"),
            instruction=create_instruction([])  # 规则内容会在用户输入中提供
        )
        
        # 构建用户输入内容
        user_input = ""
        
        # 添加规则内容
        if rule_content:
            user_input += f"检查规则:\n{rule_content}\n\n"
        
        # 添加上下文内容
        if context_content:
            user_input += f"{context_content}\n"
        
        # 添加待检查的函数代码
        user_input += f"""待检查内容:
// {file_path}
{function_code}
"""
        
        # 设置用户输入
        p.set_user_input(user_input)
        
        # 渲染prompt
        return p.basic_render()
    
    def _generate_simple_prompt(self, function_info: AffectedFunction, 
                               rules: List[CodeCheckRule]) -> str:
        """生成简单的prompt（回退模式）"""
        # 创建Prompt对象
        p = Prompt(
            system=SYSTEM.encode("utf-8").decode("utf-8"),
            instruction=create_instruction(rules)
        )
        
        # 构建简单的用户输入
        user_input = ""
        
        # 添加相关上下文（如果有的话）
        if function_info.related_definitions:
            related_context = "\n\n".join(
                f"// {rf.filepath}\n{rf.code}" for rf in function_info.related_definitions
            )
            user_input += f"参考上下文:\n{related_context}\n"
        
        # 添加待检查的函数代码
        user_input += f"""待检查内容:
// {function_info.filepath}
{function_info.code}
"""
        
        # 设置用户输入
        p.set_user_input(user_input)
        
        # 渲染prompt
        return p.basic_render()
    
    def get_prompt_info(self, function_info: AffectedFunction, rules: List[CodeCheckRule]) -> dict:
        """获取prompt信息（用于调试）"""
        return {
            "function_name": function_info.name,
            "file_path": function_info.filepath,
            "rules_count": len(rules),
            "has_context_manager": self.context_manager is not None,
            "context_manager_type": type(self.context_manager).__name__ if self.context_manager else None
        } 