import json
import re

from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.shared.log import app_logger as logger


class ResultParser:
    def __init__(self):
        pass

    def process_result(self, text: str, original_code: str, call_id: str = None) -> AnalyzeLLMResult:
        """
        解析模型返回文本，提取 JSON 并转为简化模型
        """
        try:
            answer = self.__extract_answer(text)
            if not answer:
                logger.warning(f"无法提取到JSON内容，call_id: {call_id}")
                return AnalyzeLLMResult(is_pass=False, reason="结果无法解析：无法提取到JSON内容", violations=[], call_id=call_id)
            
            extracted_obj = self.safe_json_loads(answer)
            
            # 检查解析结果的有效性
            if not extracted_obj or not isinstance(extracted_obj, dict):
                logger.warning(f"解析结果无效，call_id: {call_id}, extracted_obj: {extracted_obj}")
                return AnalyzeLLMResult(is_pass=False, reason="结果无法解析：解析结果无效", violations=[], call_id=call_id)
            
            # 兼容violations字段为None或缺失
            violations = extracted_obj.get('violations', [])
            if violations is None:
                violations = []
            valid_violations = []
            for violation in violations:
                if isinstance(violation, dict):
                    # 保证location为dict且有line字段
                    if 'location' not in violation or not isinstance(violation['location'], dict):
                        violation['location'] = {}
                    if 'line' not in violation['location']:
                        violation['location']['line'] = None
                    if 'message' not in violation:
                        violation['message'] = violation.get('reason', '违规详情')
                    valid_violations.append(violation)
            extracted_obj['violations'] = valid_violations
            
            # 处理reason字段为null的情况
            if 'reason' not in extracted_obj or extracted_obj['reason'] is None:
                if extracted_obj.get('is_pass', False):
                    extracted_obj['reason'] = "代码检查通过"
                else:
                    extracted_obj['reason'] = "代码检查未通过"
            
            # 保证reason中的\n为字符串
            if isinstance(extracted_obj.get('reason'), str):
                extracted_obj['reason'] = extracted_obj['reason'].replace('\n', r'\\n').replace('\r', r'\\r')
            
            # 检查reason是否包含无效内容
            if isinstance(extracted_obj.get('reason'), str) and "未检测到明显的代码规范问题" in extracted_obj['reason']:
                logger.warning(f"LLM返回了无效的分析结果，call_id: {call_id}, reason: {extracted_obj['reason']}")
                # 对于这种无效结果，返回通过而不是失败
                extracted_obj['is_pass'] = True
                extracted_obj['reason'] = "代码检查通过"
                extracted_obj['violations'] = []
            
            res = AnalyzeLLMResult.model_validate(extracted_obj)
            if call_id:
                res.call_id = call_id
            logger.info(f"解析结果: Pass: {res.is_pass}, Reason: {res.reason}, call_id: {call_id}")
            return res
        except Exception as e:
            logger.warning(f"解析失败: {e} \n{text[:100]}, call_id: {call_id}")
            return AnalyzeLLMResult(is_pass=False, reason=f"结果无法解析：{str(e)}", violations=[], call_id=call_id)

    def __extract_answer(self, text: str) -> str:
        """
        优先提取 FinalAnswer 标签的内容，否则提取最后一个 JSON 对象
        支持多种格式：
        1. <FinalAnswer>...</FinalAnswer> (大小写不敏感)
        2. <Finalanswer>...</Finalanswer>
        3. 代码块包裹的JSON (```json, ```xml, ```, 或无标记)
        4. 纯JSON对象
        """
        if not text or not text.strip():
            return ""
            
        # 1. 优先匹配 FinalAnswer 标签（大小写不敏感）
        # 支持 <FinalAnswer>, <Finalanswer>, <finalanswer> 等变体
        final_answer_patterns = [
            r"<FinalAnswer>(.*?)</FinalAnswer>",
            r"<Finalanswer>(.*?)</Finalanswer>",
            r"<finalanswer>(.*?)</finalanswer>",
            r"<FinalAnswer>(.*?)</Finalanswer>",
            r"<Finalanswer>(.*?)</FinalAnswer>"
        ]
        
        for pattern in final_answer_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                result = matches[-1].strip()
                # 清理代码块标记
                result = self._clean_code_block(result)
                logger.debug(f"从FinalAnswer标签提取到内容: {result[:100]}...")
                return result
                
        # 1.5. 处理没有结束标签的FinalAnswer（截断情况）
        final_answer_start_patterns = [
            r"<FinalAnswer>(.*)",
            r"<Finalanswer>(.*)",
            r"<finalanswer>(.*)"
        ]
        
        for pattern in final_answer_start_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                result = matches[-1].strip()
                # 清理代码块标记
                result = self._clean_code_block(result)
                logger.debug(f"从截断的FinalAnswer标签提取到内容: {result[:100]}...")
                return result

        # 2. 提取代码块中的JSON（支持多种语言标记）
        code_block_patterns = [
            r"```json\s*(.*?)\s*```",
            r"```xml\s*(.*?)\s*```", 
            r"```\s*(.*?)\s*```",
            r"`(.*?)`"
        ]
        
        for pattern in code_block_patterns:
            matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
            if matches:
                for match in reversed(matches):  # 从最后一个开始检查
                    match = match.strip()
                    if match.startswith('{') and self._looks_like_json(match):
                        logger.debug(f"从代码块提取到JSON: {match[:100]}...")
                        return match

        # 3. 提取最后一个可能的 JSON 对象（更宽松的匹配）
        # 首先尝试匹配完整的JSON对象
        json_pattern = r"(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})"
        json_matches = re.findall(json_pattern, text, re.DOTALL)
        if json_matches:
            result = json_matches[-1].strip()
            logger.debug(f"从JSON模式提取到内容: {result[:100]}...")
            return result
            
        # 4. 如果上面的匹配失败，尝试更简单的匹配
        simple_json_pattern = r"(\{[\s\S]*?\})"
        simple_matches = re.findall(simple_json_pattern, text)
        if simple_matches:
            result = simple_matches[-1].strip()
            logger.debug(f"从简单JSON模式提取到内容: {result[:100]}...")
            return result
            
        # 5. 如果还是没有找到，尝试从文本末尾开始查找JSON
        lines = text.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line.startswith('{') and self._looks_like_json(line):
                logger.debug(f"从行尾提取到JSON: {line[:100]}...")
                return line
                
        logger.warning(f"无法从文本中提取JSON内容: {text[:200]}...")
        return ""
        
    def _clean_code_block(self, text: str) -> str:
        text = text.strip()

        # 移除所有代码块标记（包括嵌套的）
        # 移除开始的代码块标记
        text = re.sub(r"^```(?:json|xml|javascript|python)?\s*", "", text, flags=re.IGNORECASE | re.MULTILINE)
        # 移除结束的代码块标记
        text = re.sub(r"\s*```$", "", text, flags=re.IGNORECASE | re.MULTILINE)
        # 移除中间的代码块标记
        text = re.sub(r"```(?:json|xml|javascript|python)?\s*", "", text, flags=re.IGNORECASE)
        text = text.strip()

        # 移除内联代码标记
        text = text.strip('`')

        # 移除XML标记（如果存在）
        text = re.sub(r"</?(?:xml|json)\s*[^>]*>", "", text, flags=re.IGNORECASE)
        text = text.strip()

        # 如果是JSON对象，提取JSON部分
        start = text.find('{')
        end = text.rfind('}')
        if start != -1 and end != -1:
            text = text[start:end+1]

        return text
        
    def _looks_like_json(self, text: str) -> bool:
        """简单判断文本是否像JSON"""
        text = text.strip()
        if not text.startswith('{') or not text.endswith('}'):
            return False
        # 检查是否包含JSON的基本结构
        return '"' in text and (':' in text or ',' in text)

    def safe_json_loads(self, text: str):
        """
        尝试解析 JSON，自动处理常见格式错误
        """
        if not text or not text.strip():
            raise ValueError("Empty text provided")

        
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
            
        # 去除 Markdown 包裹（如 ```json ... ```)
        text = re.sub(r"^```(?:json)?|```$", "", text.strip(), flags=re.IGNORECASE).strip()
        
        # 处理可能的截断JSON（如果以逗号结尾）
        text = re.sub(r",\s*$", "", text)
        
        # 处理可能的截断JSON（如果以数组或对象结尾）
        if text.endswith('['):
            text += ']'
        elif text.endswith('{'):
            text += '}'
            
        # 替换单引号为双引号（如果明显是 JSON-like 内容）
        if text.count("'") > text.count('"'):
            text = text.replace("'", '"')
            
        # 处理可能的转义字符问题
        # 修复非法控制字符：把裸 \n \t 等变成 JSON 合法的 \\\n
        text = text.replace('\n', '\\n').replace('\t', '\\t').replace('\r', '\\r')
        text = text.replace('\\"', '"')  
        
        # 清除多余逗号
        text = re.sub(r",\s*}", "}", text)
        text = re.sub(r",\s*]", "]", text)
        
        # 处理可能的未闭合引号
        quote_count = text.count('"')
        if quote_count % 2 != 0:
            # 如果引号数量为奇数，尝试在末尾添加引号
            if not text.endswith('"'):
                text += '"'
        
        try:
            return json.loads(text)
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败，尝试修复: {e}")
            logger.debug(f"原始文本: {text[:200]}...")
            
            # 尝试修复括号不平衡
            left_brace = text.count('{')
            right_brace = text.count('}')
            if left_brace > right_brace:
                text += '}' * (left_brace - right_brace)
            elif right_brace > left_brace:
                text = '{' * (right_brace - left_brace) + text
            left_bracket = text.count('[')
            right_bracket = text.count(']')
            if left_bracket > right_bracket:
                text += ']' * (left_bracket - right_bracket)
            elif right_bracket > left_bracket:
                text = '[' * (right_bracket - left_bracket) + text
            try:
                return json.loads(text)
            except Exception:
                pass
            return {}