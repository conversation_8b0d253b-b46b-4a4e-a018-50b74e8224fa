import threading
import uuid
from pathlib import Path
from typing import Dict, List

from tqdm import tqdm

from gate_keeper.application.dto.result import AffectedFunction, CheckMRResult
from gate_keeper.application.service.context_management import ContextManager
from gate_keeper.application.service.llm.llm_result_parser import ResultParser
from gate_keeper.application.service.rule.rule_service.manager import \
    RuleManager
from gate_keeper.config import config
from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.domain.value_objects.analysis_result import AnalyzeLLMResult
from gate_keeper.external.code_analyzer import StaticAnalyzer
from gate_keeper.infrastructure.llm.client.base import LLMClient
from gate_keeper.infrastructure.llm.dto.parameter import LLMParameters
from gate_keeper.infrastructure.llm.dto.prompt import Prompt
from gate_keeper.infrastructure.llm.prompt.tasks.analyze_code.analyze_code import \
    create_instruction
from gate_keeper.infrastructure.llm.prompt.tasks.analyze_code.c import SYSTEM
from gate_keeper.shared.log import app_logger as logger

result_parser = ResultParser()

from concurrent.futures import ThreadPoolExecutor, as_completed


class LLMService():
    def __init__(self, client: LLMClient, max_workers: int = None, max_calls_per_affected_function: int = None, 
                 static_analyzer: StaticAnalyzer = None, use_optimized_context: bool = None,
                 max_calls_per_task: int = None, max_calls_per_rule_group: int = None, orchestrator = None):
        """
        初始化LLM服务
        
        Args:
            client: LLM客户端
            max_workers: 最大并发工作线程数
            max_calls_per_affected_function: 每个被修改函数的最大调用次数
            static_analyzer: 静态分析器（可选）
            use_optimized_context: 是否使用优化的上下文（可选）
            max_calls_per_task: 任务级最大调用次数
            max_calls_per_rule_group: 规则组级最大调用次数
            orchestrator: 服务编排器（可选，用于评估模式）
        """
        self.client = client
        self.orchestrator = orchestrator  # 保存orchestrator引用，用于评估模式
        self.max_workers = max_workers or getattr(config, 'llm_concurrent', 5)
        self.max_calls_per_affected_function = max_calls_per_affected_function if max_calls_per_affected_function is not None else getattr(config, 'max_llm_calls_per_affected_function', None)
        self.max_calls_per_task = max_calls_per_task if max_calls_per_task is not None else getattr(config, 'max_llm_calls_per_task', None)
        self.max_calls_per_rule_group = max_calls_per_rule_group if max_calls_per_rule_group is not None else getattr(config, 'max_llm_calls_per_rule_group', None)
        
        # 上下文管理相关
        self.use_optimized_context = use_optimized_context if use_optimized_context is not None else getattr(config, 'use_optimized_context', True)
        self.context_manager = None
        
        # 初始化上下文管理器（如果启用）
        if self.use_optimized_context and static_analyzer:
            self._init_context_manager(static_analyzer)
        
        # 存储静态分析器引用（用于后续设置）
        self._static_analyzer = static_analyzer
        
        logger.info(f"LLM服务初始化完成: 并发={self.max_workers}, 优化上下文={self.use_optimized_context}")
        if self.max_calls_per_task:
            logger.info(f"任务级调用限制: {self.max_calls_per_task}")
        if self.max_calls_per_rule_group:
            logger.info(f"规则组级调用限制: {self.max_calls_per_rule_group}")
        if self.max_calls_per_affected_function:
            logger.info(f"被修改函数级调用限制: {self.max_calls_per_affected_function}")

    def _init_context_manager(self, static_analyzer):
        """初始化上下文管理器"""
        if static_analyzer and self.use_optimized_context and self.context_manager is None:
            self.context_manager = ContextManager(static_analyzer)
            logger.info("上下文管理器初始化完成")
    
    def set_static_analyzer(self, static_analyzer):
        """设置静态分析器并初始化上下文管理器"""
        self._static_analyzer = static_analyzer
        self._init_context_manager(static_analyzer)

    def _generate_prompt(self, af: AffectedFunction, file_path: str, rules: List[CodeCheckRule]) -> Prompt:
        """
        生成用于LLM分析的提示词
        
        Args:
            af: 待分析的函数
            file_path: 文件路径
            rules: 适用的规则列表
            
        Returns:
            Prompt: 包含系统提示词和用户提示词的Prompt对象
        """
        p = Prompt(system=SYSTEM.encode("utf-8").decode("utf-8"), instruction=create_instruction(rules))
        
        # 使用优化的上下文管理器
        if self.context_manager and self.use_optimized_context:
            # 使用ContextManager生成包含规则的prompt内容
            prompt_contents = self.context_manager.generate_prompt_with_rules(af, file_path)
            # 使用第一个规则组的内容（如果有多个组，LLMService会分别处理）
            input_content = list(prompt_contents.values())[0] if prompt_contents else self.context_manager.generate_prompt_content(af, file_path)
        else:
            # 使用原有的简单上下文拼接
            input_content = self._generate_simple_prompt_content(af, file_path)
            
        p.set_user_input(input_content)
        prompt_str = p.basic_render()
        return prompt_str
    

    
    def _generate_simple_prompt_content(self, af: AffectedFunction, file_path: str) -> str:
        """生成简单的 prompt 内容（原有逻辑）"""
        input_content = ""
        
        # 添加相关上下文（如果有的话）
        if af.related_definitions:
            related_context = "\n\n".join(f"// {rf.filepath}\n{rf.code}" for rf in af.related_definitions)
            input_content += f"参考上下文:\n{related_context}\n"
        
        # 始终添加待检查的函数代码
        input_content += f"""待检查内容:
// {file_path}
{af.code}
"""
        return input_content

    def _call_llm(self, prompt_str: str) -> Dict:
        """
        调用LLM并解析结果
        
        Args:
            prompt: 提示词字符串
            
        Returns:
            Dict: 解析后的LLM响应结果
            
        Raises:
            Exception: LLM调用或结果解析失败时抛出
        """
        try:
            # 生成唯一的 call_id
            call_id = str(uuid.uuid4())
            
            # 构建LLM参数
            parameters = LLMParameters(
                stop=["</Finalanswer>"]  # 使用标签作为停止词
            )
            
            # 调用LLM
            logger.debug(f"开始调用LLM，call_id: {call_id}")
            response = self.client.generate(prompt_str, parameters)
            if not response:
                logger.error(f"LLM返回空响应，call_id: {call_id}")
                return None
                
            # 记录原始响应用于调试
            logger.debug(f"LLM原始响应，call_id: {call_id}, 长度: {len(response)}")
            if len(response) < 100:  # 响应太短可能是错误
                logger.warning(f"LLM响应异常短，call_id: {call_id}, 响应: {response}")
            
            # 解析结果
            result = result_parser.process_result(response, prompt_str, call_id)
            if not result:
                logger.error(f"LLM响应解析失败，call_id: {call_id}")
                return None
                
            # 检查解析结果的有效性
            if result.reason and "未检测到明显的代码规范问题" in result.reason:
                logger.warning(f"LLM返回了无效的分析结果，call_id: {call_id}, reason: {result.reason}")
                # 对于这种无效结果，返回None而不是创建默认违规项
                return None
                
            # 注入 prompt/response
            result.prompt = prompt_str
            result.response = response
            result.call_id = call_id # 注入 call_id
            
            logger.debug(f"LLM调用成功，call_id: {call_id}, is_pass: {result.is_pass}")
            return result
        except Exception as e:
            logger.error(f"LLM调用失败，call_id: {call_id if 'call_id' in locals() else 'unknown'}, 错误: {str(e)}", exc_info=True)
            return None

    def _analyze_single_function(self, af: AffectedFunction, file_path: str, rule_manager: RuleManager = None, 
                                grouped_rules_dict: dict = None, pbar=None, pbar_lock=None, 
                                task_call_count=None, rule_group_call_count=None):
        """
        分析单个被修改函数，应用多层级的LLM调用限制
        
        Args:
            af: 被修改函数
            file_path: 文件路径
            rule_manager: 规则管理器（可选，优先使用ContextManager中的规则管理器）
            grouped_rules_dict: 预计算的分组规则（可选）
            pbar: 进度条
            pbar_lock: 进度条锁
            task_call_count: 任务级调用计数器（共享）
            rule_group_call_count: 规则组级调用计数器（共享）
        """
        if not af.code:
            logger.warning(f"{file_path} is empty, skip this function")
            return None
        
        try:
            # 优先使用ContextManager中的规则管理器
            if self.context_manager and self.context_manager.rule_manager:
                grouped_rules_dict = self.context_manager.get_applicable_rules(af)
                logger.info(f"使用ContextManager的规则管理器为函数 {af.name} 获取规则")
            elif grouped_rules_dict is None and rule_manager:
                # 如果ContextManager没有规则管理器，使用传入的rule_manager
                grouped_rules_dict = rule_manager.get_applicable_rules(af)
                logger.info(f"使用传入的规则管理器为函数 {af.name} 获取规则")
            elif grouped_rules_dict is None:
                logger.warning(f"函数 {af.name} 没有可用的规则管理器")
                return []
            
            # 获取调用链数量（简化处理，直接使用默认值）
            chain_count = 1  # 默认每个函数一个调用链
            
            llm_results = []
            affected_function_call_count = 0  # 被修改函数级调用计数
            
            for group_name, group_rules in grouped_rules_dict.items():
                # 1. 检查任务级限制
                if (self.max_calls_per_task is not None and 
                    task_call_count is not None and 
                    task_call_count.value >= self.max_calls_per_task):
                    logger.info(f"任务达到最大LLM调用次数限制 ({self.max_calls_per_task})，停止所有分析")
                    return llm_results
                
                # 2. 检查规则组级限制
                if (self.max_calls_per_rule_group is not None and 
                    rule_group_call_count is not None and 
                    rule_group_call_count.value >= self.max_calls_per_rule_group):
                    logger.info(f"规则组达到最大LLM调用次数限制 ({self.max_calls_per_rule_group})，停止分析函数 {af.name}")
                    return llm_results
                
                # 3. 检查被修改函数级限制
                if (self.max_calls_per_affected_function is not None and 
                    affected_function_call_count >= self.max_calls_per_affected_function):
                    logger.info(f"被修改函数 {af.name} 达到最大调用次数限制 ({self.max_calls_per_affected_function})，停止分析")
                    break
                
                # 4. 计算该规则组的实际调用次数（考虑调用链数量）
                actual_chain_count = min(chain_count, self.max_calls_per_rule_group or chain_count)
                
                # 5. 检查是否超过被修改函数级限制
                if (self.max_calls_per_affected_function is not None and 
                    affected_function_call_count + actual_chain_count > self.max_calls_per_affected_function):
                    logger.info(f"被修改函数 {af.name} 调用次数将超过限制，跳过规则组 {group_name}")
                    continue
                
                try:
                    prompt = self._generate_prompt(af, file_path, group_rules)
                    response = self._call_llm(prompt)
                    
                    # 更新各级调用计数
                    affected_function_call_count += 1
                    if task_call_count is not None:
                        task_call_count.value += 1
                    if rule_group_call_count is not None:
                        rule_group_call_count.value += 1
                    
                    if pbar is not None and pbar_lock is not None:
                        with pbar_lock:
                            pbar.update(1)
                    
                    if response:
                        llm_results.append(response)
                        logger.info(f"规则组 {group_name} 分析完成，结果: {response.is_pass or None}")
                        
                except Exception as e:
                    logger.error(f"分析规则组 {group_name} 失败: {str(e)}", exc_info=True)
                    continue
            
            return llm_results
            
        except Exception as e:
            logger.error(f"LLM分析失败: {str(e)}", exc_info=True)
            return []
    


    def analyze_mr(self, check_mr_result: CheckMRResult, max_workers: int = None) -> CheckMRResult:
        # 使用配置中的默认值
        if max_workers is None:
            max_workers = getattr(config, 'llm_concurrent', 5)
        check_items = []
        for diff in check_mr_result.diffs:
            for af in diff.affected_functions:
                check_items.append((diff, af))

        # 优先使用ContextManager中的规则管理器
        rule_manager = None
        
        # 导入RuleManager
        from gate_keeper.application.service.rule.rule_service.manager import \
            RuleManager

        # 优先使用ContextManager中的规则管理器
        if self.context_manager and self.context_manager.rule_manager:
            rule_manager = self.context_manager.rule_manager
            logger.info("使用ContextManager中的规则管理器")
        else:
            # 检查是否处于评估模式（通过检查是否有自定义规则管理器）
            # 这里需要从ServiceOrchestrator中获取自定义规则管理器
            custom_rule_manager = None
            if hasattr(self, 'orchestrator') and hasattr(self.orchestrator, '_custom_rule_manager'):
                custom_rule_manager = self.orchestrator._custom_rule_manager
            
            if custom_rule_manager:
                rule_manager = custom_rule_manager
                logger.info("使用评估模式的自定义规则管理器")
            else:
                # 检查是否处于YAML评估模式
                yaml_evaluation_mode = getattr(config, 'yaml_evaluation_mode', False)
                if yaml_evaluation_mode:
                    logger.info("检测到YAML评估模式，跳过配置文件规则加载")
                    # 在YAML评估模式下，创建一个空的规则管理器
                    
                    class EmptyRuleManager(RuleManager):
                        def __init__(self):
                            # 不调用父类的__init__，避免文件路径检查
                            self.rule_file_path = Path("yaml_evaluation_dummy.md")
                            self.merge_on = None
                            self.include_sheets = None
                            self._rules = []
                            self._loaded = True
                            self._cache_key = "yaml_evaluation_empty"
                            self._grouping_cache = {}
                        
                        def _load_from_file(self) -> List[CodeCheckRule]:
                            """重写方法，返回空列表"""
                            return []
                        
                        def load_rules(self, force_reload: bool = False) -> List[CodeCheckRule]:
                            """重写方法，始终返回空列表"""
                            return []
                    
                    rule_manager = EmptyRuleManager()
                else:
                    # 如果没有ContextManager或ContextManager没有规则管理器，创建新的
                    rule_file_path = getattr(config, 'rule_file_path', None)
                    if rule_file_path and rule_file_path != "not_set":
                        rule_manager = RuleManager(
                            rule_file_path=Path(rule_file_path),
                            merge_on=config.rule_merge_on,
                            include_sheets=config.include_sheets if hasattr(config, 'include_sheets') else None,
                        )
                        logger.info("创建新的规则管理器")
                    else:
                        logger.warning("配置中未设置有效的rule_file_path，创建空的规则管理器")
                        # 使用相同的EmptyRuleManager
                        class EmptyRuleManager2(RuleManager):
                            def __init__(self):
                                # 不调用父类的__init__，避免文件路径检查
                                self.rule_file_path = Path("empty_rules.md")
                                self.merge_on = None
                                self.include_sheets = None
                                self._rules = []
                                self._loaded = True
                                self._cache_key = "empty_rules"
                                self._grouping_cache = {}
                            
                            def _load_from_file(self) -> List[CodeCheckRule]:
                                """重写方法，返回空列表"""
                                return []
                            
                            def load_rules(self, force_reload: bool = False) -> List[CodeCheckRule]:
                                """重写方法，始终返回空列表"""
                                return []
                        
                        rule_manager = EmptyRuleManager2()

        # 0. 先模拟分组计划，只打印一次
        if check_items:
            # 使用第一个函数作为示例来打印分组计划
            sample_af = check_items[0][1]
            rule_manager.simulate_grouping_plan(sample_af, llm_max_concurrency=max_workers)

        # 1. 预统计所有 LLM 调用次数（考虑多层限制）
        total_llm_calls = 0
        max_possible_calls = 0  # 最大可能的调用次数（不考虑限制）
        total_rules = 0  # 规则总数
        total_call_chains = 0  # 调用链总数
        grouped_rules_dicts = []
        
        # 只在预统计阶段调用一次 get_applicable_rules，避免重复日志
        for diff, af in check_items:
            # 优先使用ContextManager获取规则
            if self.context_manager and self.context_manager.rule_manager:
                grouped_rules = self.context_manager.get_applicable_rules(af)
            else:
                grouped_rules = rule_manager.get_applicable_rules(af)
            
            grouped_rules_dicts.append(grouped_rules)
            max_possible_calls += len(grouped_rules)
            # 统计规则总数
            for group_rules in grouped_rules.values():
                total_rules += len(group_rules)
            # 统计调用链总数
            total_call_chains += len(af.related_calls) if hasattr(af, 'related_calls') else 0
            # 考虑被修改函数级限制
            max_calls_for_this_function = min(len(grouped_rules), self.max_calls_per_affected_function or len(grouped_rules))
            total_llm_calls += max_calls_for_this_function

        # 2. 应用任务级限制
        if self.max_calls_per_task is not None:
            total_llm_calls = min(total_llm_calls, self.max_calls_per_task)

        # 3. 打印详细的统计信息
        logger.info("=" * 60)
        logger.info("LLM分析统计信息")
        logger.info("=" * 60)
        logger.info(f"检查项目总数: {len(check_items)} 个函数")
        logger.info(f"规则分组总数: {max_possible_calls} 个分组")
        logger.info(f"适用规则总数: {total_rules} 条规则")
        logger.info(f"调用链总数: {total_call_chains} 个调用关系")
        logger.info(f"最大可能调用次数: {max_possible_calls} 次（不考虑限制）")
        
        # 显示各层限制信息
        limit_info = []
        if self.max_calls_per_task is not None:
            limit_info.append(f"任务级限制: {self.max_calls_per_task}")
        if self.max_calls_per_rule_group is not None:
            limit_info.append(f"规则组级限制: {self.max_calls_per_rule_group}")
        if self.max_calls_per_affected_function is not None:
            limit_info.append(f"被修改函数级限制: {self.max_calls_per_affected_function}")
        
        if limit_info:
            logger.info(f"应用限制: {' + '.join(limit_info)}")
        
        logger.info(f"实际工作调用次数: {total_llm_calls} 次")
        if max_possible_calls > total_llm_calls:
            logger.info(f"因调用次数限制，减少了 {max_possible_calls - total_llm_calls} 次调用")
        logger.info("=" * 60)
        
        # 4. 创建共享计数器
        import multiprocessing
        task_call_count = multiprocessing.Value('i', 0)  # 任务级计数器
        rule_group_call_count = multiprocessing.Value('i', 0)  # 规则组级计数器
        
        pbar_lock = threading.Lock()
        from tqdm import tqdm
        with tqdm(total=total_llm_calls, desc="LLM调用进度") as pbar:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_af = {}
                for i, ((diff, af), grouped_rules) in enumerate(zip(check_items, grouped_rules_dicts)):
                    # 传递预计算的分组规则和计数器，避免重复调用
                    future = executor.submit(
                        self._analyze_single_function, 
                        af, af.filepath, rule_manager, grouped_rules, 
                        pbar, pbar_lock, task_call_count, rule_group_call_count
                    )
                    future_to_af[future] = (diff, af)
                
                for future in as_completed(future_to_af):
                    diff, af = future_to_af[future]
                    try:
                        res = future.result()
                        if res:
                            if isinstance(res, list):
                                af.llm_results = res
                            else:
                                af.llm_results = [res]
                        else:
                            af.llm_results = []
                    except Exception as e:
                        logger.error(f"LLM分析函数 {af.name} 失败: {e}")
                        af.llm_results = []
        
        logger.info(f"LLM分析完成，共处理 {len(check_items)} 个函数，实际调用 {task_call_count.value} 次")
        return check_mr_result

    def analyze_specific_function(self, affected_function: AffectedFunction, file_path: str,
                                 rule_manager: RuleManager = None, grouped_rules_dict: dict = None) -> List[AnalyzeLLMResult]:
        """
        分析特定函数 - 为评估用例提供支持
        
        Args:
            affected_function: 目标函数
            file_path: 文件路径
            rule_manager: 规则管理器（可选）
            grouped_rules_dict: 预计算的分组规则（可选）
            
        Returns:
            List[AnalyzeLLMResult]: 分析结果列表
        """
        if not affected_function.code:
            logger.warning(f"函数 {affected_function.name} 代码为空，跳过分析")
            return []
        
        try:
            # 获取适用的规则
            if grouped_rules_dict is None:
                if self.context_manager and self.context_manager.rule_manager:
                    grouped_rules_dict = self.context_manager.get_applicable_rules(affected_function)
                    logger.info(f"使用ContextManager的规则管理器为函数 {affected_function.name} 获取规则")
                elif rule_manager:
                    grouped_rules_dict = rule_manager.get_applicable_rules(affected_function)
                    logger.info(f"使用传入的规则管理器为函数 {affected_function.name} 获取规则")
                else:
                    # 检查是否处于YAML评估模式
                    yaml_evaluation_mode = getattr(config, 'yaml_evaluation_mode', False)
                    if yaml_evaluation_mode:
                        logger.info(f"YAML评估模式下，函数 {affected_function.name} 没有可用的规则管理器，跳过分析")
                        return []
                    else:
                        logger.warning(f"函数 {affected_function.name} 没有可用的规则管理器")
                        return []
            
            llm_results = []
            
            # 为规则组分析添加进度条
            with tqdm(total=len(grouped_rules_dict), desc=f"📋 分析函数 {affected_function.name}", unit="规则组", leave=False) as pbar:
                for group_name, group_rules in grouped_rules_dict.items():
                    try:
                        pbar.set_postfix({"规则组": group_name})
                        prompt = self._generate_prompt(affected_function, file_path, group_rules)
                        response = self._call_llm(prompt)
                        
                        if response:
                            llm_results.append(response)
                            pbar.set_postfix({
                                "规则组": group_name,
                                "结果": "通过" if response.is_pass else "失败"
                            })
                            logger.info(f"规则组 {group_name} 分析完成，结果: {response.is_pass}")
                        else:
                            pbar.set_postfix({
                                "规则组": group_name,
                                "结果": "无响应"
                            })
                        
                        pbar.update(1)
                        
                    except Exception as e:
                        pbar.set_postfix({
                            "规则组": group_name,
                            "结果": "异常"
                        })
                        pbar.update(1)
                        logger.error(f"分析规则组 {group_name} 失败: {str(e)}", exc_info=True)
                        continue
            
            return llm_results
            
        except Exception as e:
            logger.error(f"LLM分析失败: {str(e)}", exc_info=True)
            return []

    def analyze_functions_batch(self, affected_functions: List[AffectedFunction], file_path: str,
                               rule_manager: RuleManager = None, max_workers: int = None, 
                               tracker=None) -> List[AnalyzeLLMResult]:
        """
        批量分析函数列表 - 为评估用例提供支持
        
        Args:
            affected_functions: 目标函数列表
            file_path: 文件路径
            rule_manager: 规则管理器（可选）
            max_workers: 最大并发数（可选）
            
        Returns:
            List[AnalyzeLLMResult]: 分析结果列表
        """
        if not affected_functions:
            return []
        
        # 使用配置的并发数
        if max_workers is None:
            max_workers = self.max_workers
        
        logger.info(f"开始批量分析 {len(affected_functions)} 个函数，最大并发数: {max_workers}")
        
        all_results = []
        
        # 使用进度跟踪器或默认进度条
        if tracker:
            # 使用进度跟踪器
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_function = {
                    executor.submit(self.analyze_specific_function, af, file_path, rule_manager): af
                    for af in affected_functions
                }
                
                # 收集结果
                completed_count = 0
                for future in as_completed(future_to_function):
                    af = future_to_function[future]
                    try:
                        results = future.result()
                        all_results.extend(results)
                        completed_count += 1
                        
                        # 更新LLM分析进度
                        tracker.track_llm_analysis(completed_count, len(affected_functions), af.name)
                        
                        logger.info(f"函数 {af.name} 分析完成，获得 {len(results)} 个结果")
                    except Exception as e:
                        completed_count += 1
                        tracker.track_llm_analysis(completed_count, len(affected_functions), af.name)
                        logger.error(f"函数 {af.name} 分析失败: {e}")
        else:
            # 使用默认进度条
            with tqdm(total=len(affected_functions), desc="🤖 LLM批量分析", unit="函数") as pbar:
                # 使用线程池进行并发分析
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有任务
                    future_to_function = {
                        executor.submit(self.analyze_specific_function, af, file_path, rule_manager): af
                        for af in affected_functions
                    }
                    
                    # 收集结果
                    for future in as_completed(future_to_function):
                        af = future_to_function[future]
                        try:
                            results = future.result()
                            all_results.extend(results)
                            pbar.set_postfix({
                                "函数": af.name,
                                "结果数": len(results)
                            })
                            pbar.update(1)
                            logger.info(f"函数 {af.name} 分析完成，获得 {len(results)} 个结果")
                        except Exception as e:
                            pbar.set_postfix({
                                "函数": af.name,
                                "状态": "失败"
                            })
                            pbar.update(1)
                            logger.error(f"函数 {af.name} 分析失败: {e}")
        
        logger.info(f"批量分析完成，总共获得 {len(all_results)} 个结果")
        return all_results

    def analyze_mr_with_rules(self, check_mr_result: CheckMRResult, rules: List[CodeCheckRule]) -> AnalyzeLLMResult:
        """
        使用指定规则分析MR
        
        Args:
            check_mr_result: MR检查结果
            rules: 要使用的规则列表
            
        Returns:
            分析结果
        """
        logger.info(f"使用指定规则分析MR，规则数量: {len(rules)}")
        
        # 创建临时的规则管理器
        class TempRuleManager(RuleManager):
            def __init__(self, rules: List[CodeCheckRule]):
                # 不调用父类的__init__，避免文件路径检查
                self._rules = rules
                self._loaded = True
            
            def load_rules(self, force_reload: bool = False) -> List[CodeCheckRule]:
                return self._rules
            
            def get_applicable_rules(self, check_item, grouping_strategy: str = None) -> dict:
                # 简单返回所有规则作为一个组
                return {"default": self._rules}
        
        temp_rule_manager = TempRuleManager(rules)
        
        # 分析所有diff
        all_results = []
        for diff in check_mr_result.diffs:
            if diff.affected_functions:
                # 有函数信息，分析函数
                for af in diff.affected_functions:
                    function_results = self.analyze_specific_function(
                        af, diff.filepath, temp_rule_manager
                    )
                    all_results.extend(function_results)
            else:
                # 没有函数信息，创建虚拟函数进行分析
                virtual_af = AffectedFunction(
                    name="virtual_function",
                    type="function",
                    start_line=1,
                    end_line=len(diff.new_file_content.split('\n')),
                    changed_lines=[],
                    code=diff.new_file_content,
                    related_calls=[],
                    related_definitions=[],
                    filepath=diff.filepath
                )
                function_results = self.analyze_specific_function(
                    virtual_af, diff.filepath, temp_rule_manager
                )
                all_results.extend(function_results)
        
        # 合并结果（取第一个结果作为代表）
        if all_results:
            return all_results[0]
        else:
            # 没有结果，返回默认结果
            return AnalyzeLLMResult(
                rule_id=rules[0].id if rules else "UNKNOWN",
                rule_name=rules[0].name if rules else "未知规则",
                is_pass=True,
                reason="未发现问题",
                violations=[],
                affected_functions=[],
                filepath=check_mr_result.diffs[0].filepath if check_mr_result.diffs else "",
                function_name="",
                line_number=0
            )
