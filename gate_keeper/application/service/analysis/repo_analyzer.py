"""
仓库分析器

提供代码仓库级别的分析功能，包括函数分析、模块分析等。
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

from tqdm import tqdm

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.application.service.git.code_repository_service import \
    CodeRepositoryService
from gate_keeper.config import config

# 添加 code_analyzer 项目路径到 Python 路径
code_analyzer_path = Path(__file__).parent.parent.parent.parent / "external" / "code_analyzer"
if str(code_analyzer_path) not in sys.path:
    sys.path.insert(0, str(code_analyzer_path))

from gate_keeper.application.service.analysis.header_file_analyzer import \
    header_analyzer
from gate_keeper.external.code_analyzer import RepositoryIndex, StaticAnalyzer
from gate_keeper.external.code_analyzer.models.function import Function
from gate_keeper.shared.log import app_logger as logger


class RepositoryAnalyzer:
    """仓库分析器 - 提供代码仓库级别的分析功能"""
    
    def __init__(self, git_service: CodeRepositoryService, orchestrator=None):
        self.git_service = git_service
        self.orchestrator = orchestrator
        self._index_cache: Dict[str, RepositoryIndex] = {}

    def _get_index_key(self, repo_dir: str, ref: str) -> str:
        return f"{repo_dir}:{ref}"
    
    def _paths_equal(self, path1: str, path2: str) -> bool:
        """比较两个路径是否相等（处理相对路径和绝对路径）"""
        from pathlib import Path

        # 标准化路径
        p1 = Path(path1).resolve()
        p2 = Path(path2).resolve()
        
        return p1 == p2

    def get_index(self, repo_dir: str, branch: str, commit_sha: str = None, exclude_patterns: list = None) -> RepositoryIndex:
        """获取仓库索引"""
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        ref = commit_sha or branch
        cache_key = self._get_index_key(repo_dir, ref)
        
        if cache_key not in self._index_cache:
            logger.info(f"Building repository index for {repo_dir} at {ref}")
            
            # 创建RepositoryIndex
            repo_index = RepositoryIndex(repo_dir=repo_dir, branch=branch, commit_sha=commit_sha, exclude_patterns=exclude_patterns)
            
            # 构建索引
            repo_index.build()
            
            # 缓存索引
            self._index_cache[cache_key] = repo_index
            
        return self._index_cache[cache_key]

    def _setup_static_analyzer_for_orchestrator(self, repo_dir: str, branch: str, commit_sha: str = None, exclude_patterns: list = None):
        """为服务编排器设置StaticAnalyzer"""
        if self.orchestrator is not None:
            # 获取或创建仓库索引
            repo_index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
            
            # 创建StaticAnalyzer
            static_analyzer = StaticAnalyzer(repo_index)
            
            # 设置到服务编排器
            self.orchestrator.set_static_analyzer(static_analyzer)
            logger.info(f"已为服务编排器设置StaticAnalyzer: {repo_dir}@{branch}")

    def analyze_functions(self, repo_dir: str, file_path: str, file_content: str, 
                         branch: str, commit_sha: str = None, changed_lines=None, 
                         exclude_patterns=None, depth: int = None) -> List[AffectedFunction]:
        # 使用配置中的默认值
        if depth is None:
            depth = getattr(config, 'max_context_chain_depth', 3)
            
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        """
        分析文件中的函数变更 - 使用增强分析器
        
        Args:
            repo_dir: 仓库目录
            file_path: 文件路径
            file_content: 文件内容
            branch: 分支名
            commit_sha: 提交SHA（可选）
            changed_lines: 变更行号列表（可选）
            exclude_patterns: 排除模式列表（可选）
            depth: 调用链深度
            
        Returns:
            List[AffectedFunction]: 受影响的函数列表
        """
        # 为服务编排器设置StaticAnalyzer（如果启用了优化上下文）
        self._setup_static_analyzer_for_orchestrator(repo_dir, branch, commit_sha, exclude_patterns)
        
        # 获取增强仓库索引
        index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        
        # 创建增强静态分析器
        analyzer = StaticAnalyzer(index)
        
        # 获取变更函数
        affected_functions = index.get_changed_functions(
            file_content=file_content,
            file_path=file_path,
            changed_lines=changed_lines
        )


        
        # 补充调用链和相关函数信息
        for func in affected_functions:
            func.call_chains = analyzer.get_bidirectional_call_chains(
                func.name, func.filepath, max_depth=depth
            )
            func.related_definitions = analyzer.get_related_functions(
                func.name, func.filepath, radius=depth
            )
            # 补充调用关系信息
            related_calls = index.get_related_calls(func.name)
            func.related_calls = related_calls
            


        return affected_functions

    def analyze_specific_function(self, repo_dir: str, file_path: str, function_name: str,
                                 branch: str = "main", commit_sha: str = None,
                                 exclude_patterns: list = None, depth: int = None) -> Optional[AffectedFunction]:
        """
        分析特定函数 - 为评估用例提供支持
        
        Args:
            repo_dir: 仓库目录
            file_path: 文件路径
            function_name: 函数名称
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            depth: 调用链深度
            
        Returns:
            Optional[AffectedFunction]: 目标函数信息，如果不存在则返回None
        """
        try:
            # 获取文件内容
            file_content = self.git_service.get_file_content_by_ref(repo_dir, branch, file_path)
            if not file_content:
                logger.warning(f"文件不存在或为空: {file_path}")
                return None
            
            # 为服务编排器设置StaticAnalyzer
            self._setup_static_analyzer_for_orchestrator(repo_dir, branch, commit_sha, exclude_patterns)
            
            # 获取仓库索引
            index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
            
            # 创建静态分析器
            analyzer = StaticAnalyzer(index)
            
            # 查找目标函数
            target_function = None
            
            # 使用进度条显示函数查找过程
            total_functions = sum(len(func_list) for func_list in index.function_definitions.values())
            with tqdm(total=total_functions, desc=f"🔍 查找函数 {function_name}", unit="函数", leave=False) as pbar:
                # 遍历所有函数定义，查找匹配的函数
                for func_name, func_list in index.function_definitions.items():
                    for func in func_list:
                        pbar.set_postfix({
                            "当前函数": func.name,
                            "文件": func.filepath
                        })
                        
                        if func.name == function_name and self._paths_equal(func.filepath, file_path):
                            target_function = func
                            pbar.set_postfix({
                                "当前函数": func.name,
                                "状态": "找到"
                            })
                            break
                        
                        pbar.update(1)
                    
                    if target_function:
                        break
            
            if not target_function:
                logger.warning(f"函数 {function_name} 在文件 {file_path} 中不存在")
                return None
            
            # 创建AffectedFunction对象
            affected_function = AffectedFunction(
                name=target_function.name,
                type="function",
                start_line=target_function.range.start_line,
                end_line=target_function.range.end_line,
                changed_lines=list(range(target_function.range.start_line, target_function.range.end_line + 1)),
                code=target_function.code or "",
                filepath=file_path,
                related_calls=[],
                related_definitions=[],
                call_chains=[],
                llm_results=[]
            )
            
            # 补充调用链和相关函数信息
            if depth is None:
                depth = getattr(config, 'max_context_chain_depth', 3)
            
            affected_function.call_chains = analyzer.get_bidirectional_call_chains(
                target_function.name, file_path, max_depth=depth
            )
            affected_function.related_definitions = analyzer.get_related_functions(
                target_function.name, file_path, radius=depth
            )
            affected_function.related_calls = index.get_related_calls(target_function.name)
            
            logger.info(f"成功分析函数 {function_name}，调用链数量: {len(affected_function.call_chains)}")
            return affected_function
            
        except Exception as e:
            logger.error(f"分析特定函数失败: {e}")
            return None

    def analyze_functions_by_names(self, repo_dir: str, function_specs: List[Dict],
                                  branch: str = "main", commit_sha: str = None,
                                  exclude_patterns: list = None, depth: int = None, 
                                  tracker=None) -> List[AffectedFunction]:
        """
        批量分析指定函数列表 - 为评估用例提供支持
        
        Args:
            repo_dir: 仓库目录
            function_specs: 函数规格列表，每个包含 file_path 和 function_name
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            depth: 调用链深度
            
        Returns:
            List[AffectedFunction]: 分析结果列表
        """
        results = []
        
        # 使用进度跟踪器或默认进度条
        if tracker:
            # 使用进度跟踪器
            for i, spec in enumerate(function_specs):
                file_path = spec.get('file_path')
                function_name = spec.get('function_name')
                
                if not file_path or not function_name:
                    logger.warning(f"跳过无效的函数规格: {spec}")
                    continue
                
                # 更新函数分析进度
                tracker.track_function_analysis(i + 1, len(function_specs), function_name)
                
                result = self.analyze_specific_function(
                    repo_dir=repo_dir,
                    file_path=file_path,
                    function_name=function_name,
                    branch=branch,
                    commit_sha=commit_sha,
                    exclude_patterns=exclude_patterns,
                    depth=depth
                )
                
                if result:
                    results.append(result)
        else:
            # 使用默认进度条
            with tqdm(total=len(function_specs), desc="📊 批量函数分析", unit="函数") as pbar:
                for spec in function_specs:
                    file_path = spec.get('file_path')
                    function_name = spec.get('function_name')
                    
                    if not file_path or not function_name:
                        pbar.set_postfix({
                            "函数": "无效规格",
                            "状态": "跳过"
                        })
                        pbar.update(1)
                        logger.warning(f"跳过无效的函数规格: {spec}")
                        continue
                    
                    pbar.set_postfix({
                        "函数": function_name,
                        "文件": file_path
                    })
                    
                    result = self.analyze_specific_function(
                        repo_dir=repo_dir,
                        file_path=file_path,
                        function_name=function_name,
                        branch=branch,
                        commit_sha=commit_sha,
                        exclude_patterns=exclude_patterns,
                        depth=depth
                    )
                    
                    if result:
                        results.append(result)
                        pbar.set_postfix({
                            "函数": function_name,
                            "状态": "成功",
                            "调用链": len(result.call_chains)
                        })
                    else:
                        pbar.set_postfix({
                            "函数": function_name,
                            "状态": "未找到"
                        })
                    
                    pbar.update(1)
        
        logger.info(f"批量分析完成，成功分析 {len(results)}/{len(function_specs)} 个函数")
        return results

    def get_module_analysis(self, repo_dir: str, module_path: str,
                           branch: str = "main", commit_sha: str = None,
                           exclude_patterns: list = None) -> Dict:
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        """
        获取模块分析结果
        
        Args:
            repo_dir: 仓库目录
            module_path: 模块路径
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            
        Returns:
            Dict: 模块分析结果
        """
        index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        analyzer = StaticAnalyzer(index)
        
        return analyzer.get_module_dependencies(module_path)

    def get_import_impact_analysis(self, repo_dir: str, module_path: str,
                                  branch: str = "main", commit_sha: str = None,
                                  exclude_patterns: list = None) -> Dict:
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        """
        获取导入影响分析
        
        Args:
            repo_dir: 仓库目录
            module_path: 模块路径
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            
        Returns:
            Dict: 导入影响分析结果
        """
        index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        analyzer = StaticAnalyzer(index)
        
        return analyzer.analyze_import_impact(module_path)

    def analyze_codebase_structure(self, repo_dir: str,
                                  branch: str = "main", commit_sha: str = None,
                                  exclude_patterns: list = None) -> Dict:
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        """
        分析代码库结构
        
        Args:
            repo_dir: 仓库目录
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            
        Returns:
            Dict: 代码库结构分析结果
        """
        index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        
        return {
            "overview": {
                "total_modules": len(index.graph_builder.get_modules()),
                "total_functions": len(index.function_definitions),
                "total_calls": len(index.function_calls),
                "average_coupling": 0.0  # 可以进一步计算
            },
            "language_distribution": {},
            "dependency_matrix": {},
            "graph_statistics": index.get_graph_statistics()
        }

    def find_high_coupling_modules(self, repo_dir: str, threshold: float = 0.5,
                                  branch: str = "main", commit_sha: str = None,
                                  exclude_patterns: list = None) -> List[Dict]:
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        """
        查找高耦合模块
        
        Args:
            repo_dir: 仓库目录
            threshold: 耦合度阈值
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            
        Returns:
            List[Dict]: 高耦合模块列表
        """
        index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        analyzer = StaticAnalyzer(index)
        
        high_coupling_modules = []
        modules = index.graph_builder.get_modules()
        
        for module_path in modules.keys():
            impact = analyzer.analyze_import_impact(module_path)
            coupling_score = impact["statistics"]["coupling_score"]
            
            if coupling_score > threshold:
                high_coupling_modules.append({
                    "module_path": module_path,
                    "coupling_score": coupling_score,
                    "import_count": impact["statistics"]["import_count"],
                    "dependent_count": impact["statistics"]["dependent_count"],
                    "cross_call_count": impact["statistics"]["cross_call_count"]
                })
        
        # 按耦合度排序
        high_coupling_modules.sort(key=lambda x: x["coupling_score"], reverse=True)
        return high_coupling_modules

    def find_isolated_modules(self, repo_dir: str,
                             branch: str = "main", commit_sha: str = None,
                             exclude_patterns: list = None) -> List[str]:
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        """
        查找孤立模块
        
        Args:
            repo_dir: 仓库目录
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            
        Returns:
            List[str]: 孤立模块路径列表
        """
        index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        analyzer = StaticAnalyzer(index)
        
        isolated_modules = []
        modules = index.graph_builder.get_modules()
        
        for module_path in modules.keys():
            deps = analyzer.get_module_dependencies(module_path)
            if len(deps["imports"]) == 0 and len(deps["dependents"]) == 0:
                isolated_modules.append(module_path)
        
        return isolated_modules

    def get_all_functions(self, repo_dir: str, branch: str = "main", commit_sha: str = None,
                         exclude_patterns: list = None, file_pattern: str = "*.c") -> List[AffectedFunction]:
        """
        获取仓库中所有符合条件的函数
        
        Args:
            repo_dir: 仓库目录
            branch: 分支名
            commit_sha: 提交SHA（可选）
            exclude_patterns: 排除模式列表（可选）
            file_pattern: 文件模式，默认为"*.c"
            
        Returns:
            List[AffectedFunction]: 函数列表
        """
        if exclude_patterns is None:
            exclude_patterns = getattr(config, 'exclude_patterns', [])
        
        logger.info(f"获取仓库 {repo_dir} 中所有 {file_pattern} 文件的函数")
        
        # 获取仓库索引
        repo_index = self.get_index(repo_dir, branch, commit_sha, exclude_patterns)
        
        # 获取所有函数
        all_functions = []
        
        # 调试信息
        logger.info(f"RepositoryIndex function_definitions 数量: {len(repo_index.function_definitions)}")
        logger.info(f"RepositoryIndex function_definitions 键: {list(repo_index.function_definitions.keys())[:5]}")
        
        # 遍历所有函数定义
        for function_name, func_list in repo_index.function_definitions.items():
            # 调试信息
            logger.debug(f"处理函数: {function_name}, 类型: {type(func_list)}")
            
            # 如果func_list是列表，遍历其中的函数对象
            if isinstance(func_list, list):
                for func in func_list:
                    # 检查文件模式
                    from pathlib import Path
                    path_obj = Path(func.filepath) if hasattr(func, 'filepath') else Path(func.file_path) if hasattr(func, 'file_path') else None
                    logger.debug(f"函数 {function_name} 文件路径: {path_obj}")
                    
                    if not path_obj or path_obj.suffix != file_pattern.replace("*", ""):
                        logger.debug(f"跳过函数 {function_name}: 文件路径不匹配")
                        continue
                    
                    # 使用头文件分析器判断是否应该跳过
                    if path_obj.suffix == '.h':
                        if header_analyzer.should_skip_function(func):
                            logger.debug(f"跳过头文件内容 {function_name}: {header_analyzer.should_skip_function.__name__}")
                            continue
                        
                    # 调试函数对象属性
                    logger.debug(f"函数对象属性: {dir(func)}")
                    
                    # 转换为AffectedFunction格式
                    try:
                        affected_function = AffectedFunction(
                            name=func.name,
                            type="function",
                            start_line=getattr(func, 'start_line', 1),
                            end_line=getattr(func, 'end_line', 1),
                            changed_lines=list(range(getattr(func, 'start_line', 1), getattr(func, 'end_line', 1) + 1)),
                            code=getattr(func, 'code', ''),
                            filepath=str(path_obj),
                            related_calls=[],
                            related_definitions=[],
                            call_chains=[]
                        )
                        all_functions.append(affected_function)
                    except Exception as e:
                        logger.error(f"创建AffectedFunction失败: {e}")
                        continue
            else:
                # 如果func_list不是列表，直接处理
                from pathlib import Path
                path_obj = Path(func_list.filepath) if hasattr(func_list, 'filepath') else Path(func_list.file_path) if hasattr(func_list, 'file_path') else None
                logger.debug(f"函数 {function_name} 文件路径: {path_obj}")
                
                if not path_obj or path_obj.suffix != file_pattern.replace("*", ""):
                    logger.debug(f"跳过函数 {function_name}: 文件路径不匹配")
                    continue
                
                # 跳过头文件中的函数声明（没有实现内容）
                if path_obj.suffix == '.h' and getattr(func_list, 'is_declaration', False):
                    logger.debug(f"跳过头文件函数声明 {function_name}: 没有实现内容")
                    continue
                    
                # 转换为AffectedFunction格式
                affected_function = AffectedFunction(
                    name=func_list.name,
                    type="function",
                    start_line=func_list.start_line,
                    end_line=func_list.end_line,
                    changed_lines=list(range(func_list.start_line, func_list.end_line + 1)),
                    code=func_list.code,
                    filepath=str(path_obj),
                    related_calls=[],
                    related_definitions=[],
                    call_chains=[]
                )
                all_functions.append(affected_function)
        
        logger.info(f"从仓库中获取到 {len(all_functions)} 个函数")
        return all_functions

    def clear_cache(self):
        """清空索引缓存"""
        self._index_cache.clear()