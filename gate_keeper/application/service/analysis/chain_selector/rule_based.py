# gate_keeper/application/service/chain_selector/rule_based.py
from typing import List, Optional

from .base import CallChainSelectorStrategy


class RuleBasedSelector(CallChainSelectorStrategy):
    def __init__(self):
        pass


    def select(self, chains: List[List[str]]) -> List[str]:
        if not chains:
            return []

        best = self.pick_best_path(chains)
        return best



    def pick_best_path(
        self,
        paths: List[List[str]],
        entry_keywords: List[str] = None,
        prefer_keywords: List[str] = None,
        prefer_function: str = None,
        strategy_order: List[str] = None
    ) -> Optional[List[str]]:
        strategy_order = strategy_order or [
            "entry_function",
            "keyword",
            "target_function",
            "shortest"
        ]
        
        for strategy in strategy_order:
            if strategy == "entry_function" and entry_keywords:
                path = pick_path_with_entry_function(paths, entry_keywords)
                if path:
                    return path
            elif strategy == "keyword" and prefer_keywords:
                path = pick_path_with_keyword(paths, prefer_keywords)
                if path:
                    return path
            elif strategy == "target_function" and prefer_function:
                path = pick_path_including_function(paths, prefer_function)
                if path:
                    return path
            elif strategy == "shortest":
                return pick_shortest_path(paths)
            elif strategy == "longest":
                return pick_longest_path(paths)

        return paths[0] if paths else None


def pick_shortest_path(paths: List[List[str]]) -> Optional[List[str]]:
    return min(paths, key=len) if paths else None

def pick_longest_path(paths: List[List[str]]) -> Optional[List[str]]:
    return max(paths, key=len) if paths else None

def pick_path_with_entry_function(paths: List[List[str]], entry_keywords: List[str]) -> Optional[List[str]]:
    # 优先选择以入口函数命名开头的路径
    for path in paths:
        if any(kw in path[0] for kw in entry_keywords):
            return path
    return paths[0] if paths else None

def pick_path_with_keyword(paths: List[List[str]], keywords: List[str]) -> Optional[List[str]]:
    for path in paths:
        if any(any(kw in fn for kw in keywords) for fn in path):
            return path
    return paths[0] if paths else None

def pick_path_including_function(paths: List[List[str]], target_function: str) -> Optional[List[str]]:
    for path in paths:
        if target_function in path:
            return path
    return paths[0] if paths else None