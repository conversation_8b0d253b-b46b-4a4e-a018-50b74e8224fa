# gate_keeper/application/service/chain_selector/scoring_based.py
from typing import List, Optional

from .base import CallChainSelectorStrategy


class ScoringBasedSelector(CallChainSelectorStrategy):
    def __init__(self, keywords: List[str] = None, prefer_depth: str = 'shortest'):
        self.keywords = keywords or []
        self.prefer_depth = prefer_depth  # 'shortest', 'longest', 'balanced'

    def score(self, chain: List[str]) -> float:
        score = 0.0
        # 长度评分
        length = len(chain)
        if self.prefer_depth == 'shortest':
            score -= length
        elif self.prefer_depth == 'longest':
            score += length
        else:  # balanced
            score += -abs(length - 5)  # 假设最理想是长度为5

        # 关键字命中提升分
        for func in chain:
            if any(kw in func for kw in self.keywords):
                score += 10
        return score

    def select(self, chains: List[List[str]]) -> List[str]:
        if not chains:
            return []
        best = self.pick_best_path(chains)
        return best

    def score_path(
        self,
        path: List[str],
        entry_keywords: List[str] = None,
        prefer_keywords: List[str] = None,
        prefer_function: str = None,
        changed_functions: List[str] = None
    ) -> float:
        score = 0.0

        # 越短越好（可调权重）
        score += 1.0 / len(path)

        # 包含入口函数（越靠前越好）
        if entry_keywords:
            if any(kw in path[0] for kw in entry_keywords):
                score += 2.0

        # 包含关键字函数越多越好
        if prefer_keywords:
            keyword_matches = sum(any(kw in fn for kw in prefer_keywords) for fn in path)
            score += keyword_matches * 0.5

        # 包含目标函数，位置越靠中间越好
        if prefer_function and prefer_function in path:
            center_index = len(path) // 2
            dist = abs(path.index(prefer_function) - center_index)
            score += 1.0 / (1 + dist)

        # 包含变更函数
        if changed_functions:
            overlap = len(set(path) & set(changed_functions))
            score += overlap * 1.0

        return score
    
    def pick_best_path(
        self,
        paths: List[List[str]],
        entry_keywords: List[str] = None,
        prefer_keywords: List[str] = None,
        prefer_function: str = None,
        changed_functions: List[str] = None
    ) -> Optional[List[str]]:
        if not paths:
            return None

        best_path = max(
            paths,
            key=lambda p: self.score_path(
                p,
                entry_keywords=entry_keywords,
                prefer_keywords=prefer_keywords,
                prefer_function=prefer_function,
                changed_functions=changed_functions
            )
        )
        return best_path