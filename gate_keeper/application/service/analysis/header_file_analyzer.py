"""
头文件分析器

实现系统化的头文件检查策略，区分不同类型的头文件内容并进行相应的检查。
"""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from gate_keeper.external.code_analyzer.models.function import Function
from gate_keeper.external.code_analyzer.models.macro import Macro
from gate_keeper.external.code_analyzer.models.struct import Struct
from gate_keeper.external.code_analyzer.parsers.c_parser import CParser

logger = logging.getLogger(__name__)


@dataclass
class HeaderFileContent:
    """头文件内容分类"""
    file_path: str
    function_declarations: List[Function]  # 函数声明（不检查）
    inline_functions: List[Function]       # 内联函数（需要检查）
    macros: List[Macro]                   # 宏定义（需要检查）
    structs: List[Struct]                 # 结构体定义（需要检查）
    typedefs: List[Any]                   # 类型定义（需要检查）
    constants: List[Any]                  # 常量定义（需要检查）
    includes: List[str]                   # 头文件包含（需要检查）


class HeaderFileAnalyzer:
    """头文件分析器"""
    
    def __init__(self):
        self.c_parser = CParser()
    
    def analyze_header_file(self, file_path: str, file_content: Optional[str] = None) -> HeaderFileContent:
        """
        分析头文件内容，按类型分类
        
        Args:
            file_path: 头文件路径
            file_content: 文件内容（可选）
            
        Returns:
            HeaderFileContent: 分类后的头文件内容
        """
        if not file_path.endswith('.h'):
            raise ValueError(f"不是头文件: {file_path}")
        
        # 提取所有函数
        all_functions = self.c_parser.extract_functions(file_path, file_content)
        
        # 分类函数
        function_declarations = []
        inline_functions = []
        
        for func in all_functions:
            if getattr(func, 'is_declaration', False):
                # 函数声明
                function_declarations.append(func)
            elif self._is_inline_function(func):
                # 内联函数
                inline_functions.append(func)
            else:
                # 其他函数定义（在头文件中较少见）
                inline_functions.append(func)
        
        # 提取宏定义
        macros = self.c_parser.extract_macros(file_path, file_content)
        
        # 提取结构体定义
        structs = self.c_parser.extract_structs(file_path, file_content)
        
        # 提取类型定义（从结构体中筛选）
        typedefs = [s for s in structs if self._is_typedef(s)]
        
        # 提取常量定义（从宏中筛选）
        constants = [m for m in macros if self._is_constant_macro(m)]
        
        # 提取头文件包含
        includes = self._extract_includes(file_content or self._read_file(file_path))
        
        return HeaderFileContent(
            file_path=file_path,
            function_declarations=function_declarations,
            inline_functions=inline_functions,
            macros=macros,
            structs=structs,
            typedefs=typedefs,
            constants=constants,
            includes=includes
        )
    
    def get_checkable_content(self, header_content: HeaderFileContent) -> List[Dict[str, Any]]:
        """
        获取需要检查的内容列表
        
        Args:
            header_content: 头文件内容
            
        Returns:
            List[Dict]: 需要检查的内容列表
        """
        checkable_items = []
        
        # 1. 内联函数
        for func in header_content.inline_functions:
            checkable_items.append({
                'type': 'inline_function',
                'name': func.name,
                'content': func,
                'start_line': func.range.start_line,
                'end_line': func.range.end_line,
                'description': f'内联函数: {func.name}'
            })
        
        # 2. 宏定义
        for macro in header_content.macros:
            checkable_items.append({
                'type': 'macro',
                'name': macro.name,
                'content': macro,
                'start_line': macro.range.start_line,
                'end_line': macro.range.end_line,
                'description': f'宏定义: {macro.name} ({macro.type})'
            })
        
        # 3. 结构体定义
        for struct in header_content.structs:
            checkable_items.append({
                'type': 'struct',
                'name': struct.name,
                'content': struct,
                'start_line': struct.start_line,
                'end_line': struct.end_line,
                'description': f'结构体定义: {struct.name}'
            })
        
        # 4. 类型定义
        for typedef in header_content.typedefs:
            checkable_items.append({
                'type': 'typedef',
                'name': typedef.name,
                'content': typedef,
                'start_line': typedef.start_line,
                'end_line': typedef.end_line,
                'description': f'类型定义: {typedef.name}'
            })
        
        # 5. 常量定义
        for constant in header_content.constants:
            checkable_items.append({
                'type': 'constant',
                'name': constant.name,
                'content': constant,
                'start_line': constant.range.start_line,
                'end_line': constant.range.end_line,
                'description': f'常量定义: {constant.name}'
            })
        
        # 6. 头文件包含
        for include in header_content.includes:
            checkable_items.append({
                'type': 'include',
                'name': include,
                'content': include,
                'start_line': 0,  # 需要从原始内容中获取
                'end_line': 0,
                'description': f'头文件包含: {include}'
            })
        
        return checkable_items
    
    def should_skip_function(self, func: Function) -> bool:
        """
        判断是否应该跳过函数检查
        
        Args:
            func: 函数对象
            
        Returns:
            bool: 是否跳过
        """
        # 1. 函数声明（没有实现内容）
        if getattr(func, 'is_declaration', False):
            return True
        
        # 2. 头文件中的非内联函数（没有实现内容）
        if func.filepath.endswith('.h'):
            # 检查是否为内联函数
            if not self._is_inline_function(func):
                return True
        
        return False
    
    def _is_inline_function(self, func: Function) -> bool:
        """判断是否为内联函数"""
        if not func.code:
            return False
        
        # 检查是否包含 inline 关键字
        return 'inline' in func.code.lower()
    
    def _is_typedef(self, struct: Struct) -> bool:
        """判断是否为类型定义"""
        if not struct.code:
            return False
        
        return 'typedef' in struct.code.lower()
    
    def _is_constant_macro(self, macro: Macro) -> bool:
        """判断是否为常量宏"""
        return macro.type == "object" and not macro.parameters
    
    def _extract_includes(self, content: str) -> List[str]:
        """提取头文件包含"""
        includes = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('#include'):
                # 提取包含的文件名
                if '"' in line:
                    start = line.find('"') + 1
                    end = line.find('"', start)
                    if end > start:
                        includes.append(line[start:end])
                elif '<' in line:
                    start = line.find('<') + 1
                    end = line.find('>', start)
                    if end > start:
                        includes.append(line[start:end])
        
        return includes
    
    def _read_file(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return ""


# 全局实例
header_analyzer = HeaderFileAnalyzer() 