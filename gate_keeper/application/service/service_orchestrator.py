"""
服务编排器V2 - 重构版本

解决当前服务编排器的依赖时序问题，引入：
1. 服务生命周期管理
2. 依赖关系图
3. 配置驱动的初始化
4. 条件服务初始化
"""

import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from gate_keeper.domain.rule.check_rule import CodeCheckRule
from gate_keeper.shared.log import app_logger as logger


class ServiceState(Enum):
    UNINITIALIZED = "uninitialized"
    INITIALIZING = "initializing"
    READY = "ready"
    ERROR = "error"


@dataclass
class ServiceDependency:
    service_name: str
    required: bool = True
    condition: Optional[Callable] = None


class ServiceLifecycle:
    """服务生命周期管理"""
    
    def __init__(self, name: str, factory: Callable, dependencies: List[ServiceDependency] = None, condition: Optional[Callable] = None):
        self.name = name
        self.factory = factory
        self.dependencies = dependencies or []
        self.condition = condition
        self.state = ServiceState.UNINITIALIZED
        self.instance = None
        self.error = None
    
    def can_initialize(self, orchestrator) -> bool:
        """检查是否可以初始化"""
        # 检查自身条件
        if self.condition and not self.condition():
            return False
        
        # 检查依赖
        for dep in self.dependencies:
            if dep.required:
                if not orchestrator._is_service_ready(dep.service_name):
                    return False
        return True
    
    def initialize(self, orchestrator):
        """初始化服务"""
        try:
            self.state = ServiceState.INITIALIZING
            self.instance = self.factory(orchestrator)
            self.state = ServiceState.READY
            logger.info(f"服务 {self.name} 初始化完成")
        except Exception as e:
            self.state = ServiceState.ERROR
            self.error = e
            logger.error(f"服务 {self.name} 初始化失败: {e}")
            raise


class DependencyGraph:
    """依赖关系图管理"""
    
    def __init__(self):
        self.services: Dict[str, ServiceLifecycle] = {}
        self.dependency_map: Dict[str, List[str]] = {}
    
    def add_service(self, service: ServiceLifecycle):
        """添加服务到依赖图"""
        self.services[service.name] = service
        self._build_dependency_map()
    
    def _build_dependency_map(self):
        """构建依赖映射"""
        self.dependency_map.clear()
        for service_name, service in self.services.items():
            self.dependency_map[service_name] = [
                dep.service_name for dep in service.dependencies if dep.required
            ]
    
    def get_initialization_order(self) -> List[str]:
        """获取服务初始化顺序（拓扑排序）"""
        # 简单的拓扑排序实现
        in_degree = {name: 0 for name in self.services.keys()}
        
        # 计算入度
        for deps in self.dependency_map.values():
            for dep in deps:
                if dep in in_degree:
                    in_degree[dep] += 1
        
        # 拓扑排序
        queue = [name for name, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            service_name = queue.pop(0)
            result.append(service_name)
            
            # 更新依赖该服务的其他服务的入度
            for name, deps in self.dependency_map.items():
                if service_name in deps:
                    in_degree[name] -= 1
                    if in_degree[name] == 0:
                        queue.append(name)
        
        return result


@dataclass
class ServiceConfig:
    """服务配置"""
    # Git配置
    git_token: str
    git_platform: str = "gitee"
    
    # LLM配置
    llm_endpoint: str = "http://localhost:11434"
    llm_model: str = "qwen2.5:7b"
    
    # 功能开关
    use_static_analysis: bool = True
    use_optimized_context: bool = True
    
    # 性能配置
    max_workers: int = 5
    max_context_chain_depth: int = 3
    max_context_chains: int = 3
    max_context_size: int = 8000


class ServiceOrchestrator:
    """服务编排器V2 - 重构版本"""
    
    def __init__(self, config: ServiceConfig):
        self.config = config
        self.dependency_graph = DependencyGraph()
        self.service_registry: Dict[str, Any] = {}
        self._llm_service = None
        self._context_manager = None
        self._static_analyzer = None  # 添加静态分析器引用
        self._setup_services()
        logger.info("服务编排器V2初始化完成")
    
    def _setup_services(self):
        """设置服务依赖关系"""
        # 1. Git客户端（无依赖）
        git_client = ServiceLifecycle(
            name="git_client",
            factory=self._create_git_client
        )
        
        # 2. LLM客户端（无依赖）
        llm_client = ServiceLifecycle(
            name="llm_client",
            factory=self._create_llm_client
        )
        
        # 3. Git服务（依赖git_client）
        git_service = ServiceLifecycle(
            name="git_service",
            factory=self._create_git_service,
            dependencies=[ServiceDependency("git_client")]
        )
        
        # 4. 代码分析器（依赖git_service）
        code_analyzer = ServiceLifecycle(
            name="code_analyzer",
            factory=self._create_code_analyzer,
            dependencies=[ServiceDependency("git_service")]
        )
        
        # 5. 上下文管理器（依赖code_analyzer，条件初始化）
        # 注意：StaticAnalyzer不再作为独立服务，而是在RepositoryAnalyzer中按需创建
        context_manager = ServiceLifecycle(
            name="context_manager",
            factory=self._create_context_manager,
            dependencies=[ServiceDependency("code_analyzer")],
            condition=lambda: self.config.use_optimized_context
        )
        
        # 6. LLM服务（依赖llm_client, context_manager）
        llm_service = ServiceLifecycle(
            name="llm_service",
            factory=self._create_llm_service,
            dependencies=[
                ServiceDependency("llm_client"),
                ServiceDependency("context_manager", required=False)
            ]
        )
        
        # 添加到依赖图
        for service in [git_client, llm_client, git_service, code_analyzer, 
                       context_manager, llm_service]:
            self.dependency_graph.add_service(service)
    
    def get_service(self, service_name: str) -> Any:
        """获取服务实例，自动处理依赖"""
        if service_name not in self.service_registry:
            self._initialize_service(service_name)
        return self.service_registry[service_name]
    
    def _initialize_service(self, service_name: str):
        """初始化服务及其依赖"""
        if service_name not in self.dependency_graph.services:
            raise ValueError(f"未知服务: {service_name}")
        
        service = self.dependency_graph.services[service_name]
        
        # 如果已经初始化，直接返回
        if service.state == ServiceState.READY:
            self.service_registry[service_name] = service.instance
            return
        
        # 检查是否可以初始化
        if not service.can_initialize(self):
            # 初始化依赖
            for dep in service.dependencies:
                if dep.required and not self._is_service_ready(dep.service_name):
                    self._initialize_service(dep.service_name)
        
        # 初始化服务
        service.initialize(self)
        self.service_registry[service_name] = service.instance
    
    def _is_service_ready(self, service_name: str) -> bool:
        """检查服务是否就绪"""
        if service_name not in self.dependency_graph.services:
            return False
        return self.dependency_graph.services[service_name].state == ServiceState.READY
    
    def set_static_analyzer(self, static_analyzer):
        """设置静态分析器（由代码分析器调用）"""
        # 存储静态分析器引用
        self._static_analyzer = static_analyzer
        logger.info("静态分析器已设置")
        
        # 如果LLM服务已经初始化，更新其static_analyzer
        if self._llm_service is not None:
            self._llm_service.set_static_analyzer(static_analyzer)
            logger.info("已更新LLM服务的静态分析器")
        elif 'llm_service' in self.service_registry:
            # 如果LLM服务在注册表中，也更新它
            llm_service = self.service_registry['llm_service']
            llm_service.set_static_analyzer(static_analyzer)
            logger.info("已更新LLM服务的静态分析器")
        
        # 初始化或重新初始化上下文管理器
        try:
            from gate_keeper.application.service.context_management import \
                ContextManager
            self._context_manager = ContextManager(static_analyzer)
            logger.info("上下文管理器初始化完成")
        except Exception as e:
            logger.error(f"初始化上下文管理器失败: {e}")
    
    @property
    def static_analyzer(self):
        """获取静态分析器"""
        return self._static_analyzer
    
    @property
    def context_manager(self):
        """获取上下文管理器（延迟初始化）"""
        if self._context_manager is None and self._static_analyzer is not None:
            try:
                from pathlib import Path

                from gate_keeper.application.service.context_management import \
                    ContextManager
                from gate_keeper.application.service.rule import RuleManager
                from gate_keeper.config import config

                # 检查是否有自定义规则管理器
                if hasattr(self, '_custom_rule_manager') and self._custom_rule_manager is not None:
                    rule_manager = self._custom_rule_manager
                    logger.info("使用自定义规则管理器")
                else:
                    # 检查是否处于YAML评估模式
                    yaml_evaluation_mode = getattr(config, 'yaml_evaluation_mode', False)
                    if yaml_evaluation_mode:
                        logger.info("检测到YAML评估模式，跳过配置文件规则加载")
                        # 在YAML评估模式下，如果没有自定义规则管理器，创建一个空的规则管理器
                        # 使用一个不存在的文件路径，但重写_load_from_file方法避免文件访问
                        class EmptyRuleManager(RuleManager):
                            def __init__(self):
                                # 不调用父类的__init__，避免文件路径检查
                                self.rule_file_path = Path("yaml_evaluation_dummy.md")
                                self.merge_on = None
                                self.include_sheets = None
                                self._rules = []
                                self._loaded = True
                                self._cache_key = "yaml_evaluation_empty"
                                self._grouping_cache = {}
                            
                            def _load_from_file(self) -> List[CodeCheckRule]:
                                """重写方法，返回空列表"""
                                return []
                            
                            def load_rules(self, force_reload: bool = False) -> List[CodeCheckRule]:
                                """重写方法，始终返回空列表"""
                                return []
                        
                        rule_manager = EmptyRuleManager()
                    else:
                        # 创建默认规则管理器
                        rule_file_path = getattr(config, 'rule_file_path', None)
                        if rule_file_path and rule_file_path != "not_set":
                            rule_manager = RuleManager(
                                rule_file_path=Path(rule_file_path),
                                merge_on=config.rule_merge_on,
                                include_sheets=config.include_sheets if hasattr(config, 'include_sheets') else None,
                            )
                            logger.info("使用默认规则管理器")
                        else:
                            logger.warning("配置中未设置有效的rule_file_path，创建空的规则管理器")
                            # 使用相同的EmptyRuleManager
                            class EmptyRuleManager(RuleManager):
                                def __init__(self):
                                    # 不调用父类的__init__，避免文件路径检查
                                    self.rule_file_path = Path("empty_rules.md")
                                    self.merge_on = None
                                    self.include_sheets = None
                                    self._rules = []
                                    self._loaded = True
                                    self._cache_key = "empty_rules"
                                    self._grouping_cache = {}
                                
                                def _load_from_file(self) -> List[CodeCheckRule]:
                                    """重写方法，返回空列表"""
                                    return []
                                
                                def load_rules(self, force_reload: bool = False) -> List[CodeCheckRule]:
                                    """重写方法，始终返回空列表"""
                                    return []
                            
                            rule_manager = EmptyRuleManager()
                
                # 创建上下文管理器配置，使用ServiceConfig中的值
                from gate_keeper.application.service.context_management.context_manager_config import \
                    ContextManagerConfig
                context_config = ContextManagerConfig(
                    max_context_size=self.config.max_context_size,
                    max_chain_depth=self.config.max_context_chain_depth,
                    max_chains=self.config.max_context_chains
                )
                
                # 创建包含规则管理器的上下文管理器
                self._context_manager = ContextManager(
                    static_analyzer=self._static_analyzer,
                    config=context_config,
                    rule_manager=rule_manager
                )
                logger.info("上下文管理器初始化完成（包含规则管理器）")
            except Exception as e:
                logger.error(f"上下文管理器初始化失败: {e}")
                return None
        return self._context_manager
    
    def set_custom_rule_manager(self, rule_manager):
        """设置自定义规则管理器"""
        self._custom_rule_manager = rule_manager
        # 如果context_manager已经存在，需要重新创建
        if self._context_manager is not None:
            self._context_manager = None
            logger.info("已清除现有上下文管理器，将在下次访问时重新创建")
    
    def set_rules(self, rules: List[CodeCheckRule]) -> None:
        """
        直接设置规则列表（推荐用于评估模式）
        
        Args:
            rules: 规则列表
        """
        # 获取现有的规则管理器
        rule_manager = None
        try:
            rule_manager = self.get_service("rule_manager")
        except:
            # 如果没有规则管理器，创建一个
            from gate_keeper.application.service.rule.rule_service.manager import \
                RuleManager
            rule_manager = RuleManager()
            self._custom_rule_manager = rule_manager
        
        # 设置规则
        rule_manager.set_rules(rules)
        logger.info(f"服务编排器已设置 {len(rules)} 条规则")
    
    # 服务工厂方法
    def _create_git_client(self, orchestrator):
        """创建Git客户端"""
        from gate_keeper.infrastructure.git.gitee.client import Gitee
        return Gitee(self.config.git_token)
    
    def _create_llm_client(self, orchestrator):
        """创建LLM客户端"""
        from gate_keeper.infrastructure.llm.client.client_factory import \
            LLMFactory
        return LLMFactory.create("ollama")
    
    def _create_git_service(self, orchestrator):
        """创建Git服务"""
        from gate_keeper.application.service.git import GitService
        git_client = orchestrator.get_service("git_client")
        return GitService(comment_service=git_client)
    
    def _create_code_analyzer(self, orchestrator):
        """创建代码分析器"""
        from gate_keeper.application.service.analysis import RepositoryAnalyzer
        git_service = orchestrator.get_service("git_service")
        repo_analyzer = RepositoryAnalyzer(git_service, orchestrator)
        return repo_analyzer
    
    def _create_context_manager(self, orchestrator):
        """创建上下文管理器"""
        from gate_keeper.application.service.context_management import \
            ContextManager

        # 上下文管理器需要等待StaticAnalyzer被设置
        # 这里先返回None，等StaticAnalyzer设置后再重新初始化
        return None
    
    def _create_llm_service(self, orchestrator):
        """创建LLM服务"""
        from gate_keeper.application.service.llm import LLMService
        llm_client = orchestrator.get_service("llm_client")
        
        # LLM服务初始化时不传入static_analyzer，等后续设置
        llm_service = LLMService(
            client=llm_client,
            static_analyzer=None,  # 初始化为None，后续通过set_static_analyzer设置
            use_optimized_context=self.config.use_optimized_context,
            orchestrator=orchestrator  # 传递orchestrator引用，用于评估模式
        )
        
        # 更新引用
        orchestrator._llm_service = llm_service
        return llm_service
    
    def get_service_status(self) -> dict:
        """获取服务状态信息"""
        status = {}
        for name, service in self.dependency_graph.services.items():
            status[name] = {
                "state": service.state.value,
                "error": str(service.error) if service.error else None,
                "initialized": service.state == ServiceState.READY
            }
        return status
    
    def __str__(self) -> str:
        status = self.get_service_status()
        return f"ServiceOrchestrator(status={status})"


class ServiceOrchestratorFactoryV2:
    """服务编排器V2工厂"""
    
    @staticmethod
    def create_from_config(git_token: str, **kwargs) -> ServiceOrchestrator:
        """从配置创建服务编排器V2"""
        # 从config模块读取配置
        from gate_keeper.config import config as app_config
        
        config = ServiceConfig(
            git_token=git_token,
            git_platform=kwargs.get('git_platform', getattr(app_config, 'git_platform', 'gitee')),
            llm_endpoint=kwargs.get('llm_endpoint', getattr(app_config, 'llm_endpoint', 'http://localhost:11434')),
            llm_model=kwargs.get('llm_model', getattr(app_config, 'llm_model_id', 'qwen2.5:7b')),
            use_static_analysis=kwargs.get('use_static_analysis', getattr(app_config, 'use_static_analysis', True)),
            use_optimized_context=kwargs.get('use_optimized_context', getattr(app_config, 'use_optimized_context', True)),
            max_workers=kwargs.get('max_workers', getattr(app_config, 'llm_concurrent', 5)),
            max_context_chain_depth=kwargs.get('max_context_chain_depth', getattr(app_config, 'max_context_chain_depth', 3)),
            max_context_chains=kwargs.get('max_context_chains', getattr(app_config, 'max_context_chains', 3)),
            max_context_size=kwargs.get('max_context_size', getattr(app_config, 'max_context_size', 8000))
        )
        return ServiceOrchestrator(config) 