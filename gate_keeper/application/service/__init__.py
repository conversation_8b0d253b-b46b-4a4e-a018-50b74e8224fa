"""
应用服务层

提供代码检查应用的核心业务服务，按功能模块组织：

- context_management: 上下文管理
- llm: LLM服务
- git: Git服务  
- rule: 规则管理
- report: 报告服务
- prompt: 提示词管理
- analysis: 代码分析
- deduplication: 去重服务
"""

# 代码分析
from .analysis import (CallChainSelectorStrategy, RepositoryAnalyzer,
                       RuleBasedSelector, ScoringBasedSelector)
# 上下文管理
from .context_management import (CallChainContext, ContextManager,
                                 ContextManagerConfig,
                                 ContextRelevanceCalculator, ContextSelector)
# 去重服务
from .deduplication import (CommentDeduplicationService,
                            ViolationDeduplicationService)
# Git服务
from .git import CodeRepositoryService, GitService
# LLM服务
from .llm import LLMService, ResultParser
# 提示词管理
from .prompt import (PromptLengthManager, SmartPromptConfig,
                     SmartPromptManager, SmartPromptResult)
# 报告服务
from .report import ReportService
# 规则管理
from .rule import RuleManager

__all__ = [
    # 上下文管理
    'ContextManager', 'CallChainContext', 'ContextManagerConfig',
    'ContextRelevanceCalculator', 'ContextSelector',
    
    # LLM服务
    'LLMService', 'ResultParser',
    
    # Git服务
    'GitService', 'CodeRepositoryService',
    
    # 规则管理
    'RuleManager',
    
    # 报告服务
    'ReportService',
    
    # 提示词管理
    'PromptLengthManager', 'SmartPromptManager',
    'SmartPromptConfig', 'SmartPromptResult',
    
    # 代码分析
    'RepositoryAnalyzer', 'CallChainSelectorStrategy',
    'RuleBasedSelector', 'ScoringBasedSelector',
    
    # 去重服务
    'ViolationDeduplicationService', 'CommentDeduplicationService'
]
