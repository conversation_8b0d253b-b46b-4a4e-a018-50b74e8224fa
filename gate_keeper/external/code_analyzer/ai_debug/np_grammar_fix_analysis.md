# NP语法解析失败问题分析与修复方案

## 问题描述

`hello.asm`文件在NP解析器中解析失败，被标记为`ERROR`节点，而`main.asm`文件能够成功解析。

## 根本原因

**NP的grammar.js中缺少字符串字面量的定义**，导致包含字符串字面量的代码无法正确解析。

## 详细分析

### 1. 失败的代码示例

`hello.asm`中的问题代码：
```asm
static void greet(char name) 
{
    printf("Hello, %s!\n", name);  // ← 这里的字符串字面量无法解析
}
```

### 2. 成功的代码示例

`main.asm`中的成功代码：
```asm
void main() {
    print(MSG);  // ← 使用宏，不是字符串字面量
    // ...
}
```

### 3. Grammar.js中的问题

#### 缺少字符串字面量规则

当前的`argument_expression`规则：
```javascript
argument_expression: $=> prec(1,choice(
    $.identifier,
    $.constant,
    $.object_field_access,
    $.binary_expression,
    $.expression_function_call,
    $.parenthesized_expression,      
)),
```

**问题**：没有字符串字面量的支持。

#### 缺少字符串字面量定义

整个grammar.js文件中没有定义字符串字面量的规则，如：
- `string_literal`
- 双引号字符串处理
- 转义字符处理

## 修复方案

### 1. 添加字符串字面量规则

在grammar.js中添加以下规则：

```javascript
// 字符串字面量
string_literal: $ => token(seq(
  '"',
  repeat(choice(
    /[^"\\\n]/,
    $.escape_sequence
  )),
  '"'
)),

// 转义序列
escape_sequence: $ => token(seq(
  '\\',
  choice(
    /[abfnrtv\\"']/,
    /[0-7]{1,3}/,
    /x[0-9a-fA-F]{2}/,
    /u[0-9a-fA-F]{4}/,
    /U[0-9a-fA-F]{8}/
  )
)),
```

### 2. 更新argument_expression规则

```javascript
argument_expression: $=> prec(1,choice(
    $.identifier,
    $.constant,
    $.string_literal,  // ← 添加字符串字面量支持
    $.object_field_access,
    $.binary_expression,
    $.expression_function_call,
    $.parenthesized_expression,      
)),
```

### 3. 更新expression规则

```javascript
expression: $ => choice(
    $.expression_function_call,
    $.register_bit_access,
    $.assembly_expression,
    $.condition_expression,
    $.binary_expression,
    $.expression_assignment,
    $.parenthesized_expression,
    $.constant,
    $.string_literal,  // ← 添加字符串字面量支持
    $.primary_expression,
),
```

### 4. 更新variable规则

```javascript
variable: $ => prec(-1,choice(
    $.object_field_access,
    $.identifier,
    $.constant,
    $.string_literal,  // ← 添加字符串字面量支持
)),
```

## 预期效果

修复后，`hello.asm`应该能够成功解析：

```asm
static void greet(char name) 
{
    printf("Hello, %s!\n", name);  // ← 现在可以正确解析字符串字面量
}
```

解析结果应该包含：
- `statement_function_def`节点
- 正确的函数名`greet`
- 正确的参数`char name`
- 正确的函数体

## 验证方法

1. 更新grammar.js文件
2. 重新编译tree-sitter-np
3. 运行调试脚本验证解析结果
4. 确认`hello.asm`不再出现`ERROR`节点

## 总结

NP解析器解析失败的根本原因是grammar.js中缺少字符串字面量的定义。通过添加相应的语法规则，可以解决这个问题，使NP解析器能够正确处理包含字符串字面量的代码。 