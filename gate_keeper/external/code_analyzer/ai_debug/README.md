# NP解析器调试工具

本目录包含用于调试NP解析器问题的专用工具。

## 工具列表

### 1. `np_parser_debug.py` - 完整调试器

**功能**：
- 🌳 完整的语法树结构分析
- 📊 详细的节点类型统计
- ❌ 错误节点深度分析
- 🔍 函数相关节点识别
- 🔬 Tree-sitter原始解析结果
- 📊 解析质量评估
- 🔧 函数提取测试
- 📞 调用提取测试
- 🔄 降级方案对比
- 📋 生成JSON调试报告

**使用方法**：
```bash
python ai_debug/np_parser_debug.py
```

**输出示例**：
```
🔧 NP解析器调试器已初始化
============================================================
🔍 调试文件: examples/np/hello.asm
============================================================

🌳 语法树分析
----------------------------------------
📋 完整语法树结构:
program: "#include "stdio.np"\n#define PI 3.14\n\nsta"
  statement: "#include "stdio.np""
    include_statement: "#include "stdio.np""
  ERROR ❌: "static void greet(name) {\n    printf("He"
    linkage_type: "static"
    identifier 🔍: "greet"
    ...

🔬 Tree-sitter原始解析结果:
----------------------------------------
根节点类型: program
根节点文本长度: 327
子节点数量: 4

📊 解析质量评估:
  总节点数: 171
  错误节点数: 10
  错误率: 5.85%
  ⚠️ 错误率中等，部分功能可能受影响
```

### 2. `quick_debug.py` - 快速诊断脚本

**功能**：
- 快速检查错误节点数量
- 函数定义节点统计
- 标识符分析
- 基本解析测试

**使用方法**：
```bash
python ai_debug/quick_debug.py
```

**输出示例**：
```
🔍 快速调试: examples/np/hello.asm
==================================================
文件大小: 315 字符
错误节点数量: 10
函数定义节点: 0
标识符节点: 23
NP解析器提取函数: 2
  - greet
  - main
```

### 3. `diagnose_issues.py` - 问题诊断脚本

**功能**：
- 深入分析错误原因
- 语法结构分析
- 字符串字面量问题检测
- 函数定义模式匹配
- 修复建议

**使用方法**：
```bash
python ai_debug/diagnose_issues.py
```

## 调试信息解读

### 语法树结构说明

- **program**: 根节点，包含整个文件
- **statement**: 语句节点
- **ERROR ❌**: 错误节点，表示解析失败
- **identifier 🔍**: 标识符节点，可能包含函数名
- **statement_function_def**: 函数定义节点（理想情况）
- **declare_function**: 函数声明节点

### 错误节点分析

错误节点通常由以下原因产生：
1. **字符串字面量解析失败** - 包含特殊字符的字符串
2. **函数定义语法不匹配** - NP语法与tree-sitter规则不符
3. **宏和特殊语法结构** - NP特有语法不被支持

### 解析质量评估

- **错误率 < 5%**: ✅ 解析质量良好
- **错误率 5-10%**: ⚠️ 错误率中等，部分功能可能受影响
- **错误率 > 10%**: ⚠️ 错误率较高，建议使用降级方案

### 降级方案说明

当tree-sitter解析失败时，系统会自动切换到基于正则表达式的降级方案：

```python
patterns = [
    r'(?:static\s+)?(?:void|int|uint8|uint16|uint32)\s+(\w+)\s*\(',
    r'function\s+(\w+)',
    r'bundle\s+(\w+)'
]
```

## 常见问题排查

### 1. 为什么tree-sitter解析失败？

**可能原因**：
- NP语言语法与tree-sitter-np规则不匹配
- 字符串字面量包含特殊字符
- 使用了NP特有的语法结构

**解决方案**：
- 使用降级方案（已自动启用）
- 检查代码语法是否符合NP语言规范

### 2. 如何判断解析质量？

**查看解析质量评估部分**：
```
📊 解析质量评估:
  总节点数: 171
  错误节点数: 10
  错误率: 5.85%
```

**判断标准**：
- 错误率 < 5%: 质量良好
- 错误率 5-10%: 质量中等
- 错误率 > 10%: 质量较差

### 3. 函数提取失败怎么办？

**检查步骤**：
1. 查看"函数相关节点"部分
2. 检查是否有`statement_function_def`节点
3. 查看"降级方案对比"部分
4. 确认函数定义模式是否匹配

**常见解决方案**：
- 检查函数定义语法
- 确认参数格式正确
- 使用降级方案（通常有效）

## 调试报告

调试器会生成JSON格式的调试报告，包含：
- 文件信息
- 节点统计
- 错误节点详情
- 函数提取结果
- 调用提取结果

报告文件：`debug_report_[文件名].json`

## 使用建议

1. **首次调试**：使用`quick_debug.py`快速了解问题
2. **深入分析**：使用`np_parser_debug.py`获取详细信息
3. **问题诊断**：使用`diagnose_issues.py`分析根本原因
4. **保存报告**：生成JSON报告用于后续分析

## 注意事项

- 调试工具会显示大量信息，建议在需要时使用
- 错误节点是正常的，降级方案会自动处理
- 解析质量评估仅供参考，实际功能可能不受影响
- 调试报告文件较大，注意磁盘空间 