#!/usr/bin/env python3
"""
测试prompt_sft_example中的流式保存功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.generator import SFTDataGenerator
from core.data_gen.sft.prompt_sft.generator import PromptSFTGenerator
from core.data_gen.sft.prompt_sft.models import ModelType


def test_streaming_save():
    """测试流式保存功能"""
    print("=== 测试Prompt SFT流式保存功能 ===")
    
    # 初始化
    examples_dir = project_root / "examples" / "np"
    output_dir = Path("ai_debug/streaming_test_output")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("1. 初始化分析器...")
    analyzer = RealNPAnalyzer(str(examples_dir))
    analyzer.analyze_project()
    
    print("2. 初始化生成器...")
    sft_generator = SFTDataGenerator(analyzer)
    prompt_generator = PromptSFTGenerator(sft_generator)
    
    print("3. 生成少量测试数据...")
    test_data = prompt_generator.generate_prompt_sft_data(
        max_samples=10,
        model_type=ModelType.DEEPSEEK_CODER
    )
    
    print(f"   生成了 {len(test_data)} 个样本")
    
    print("4. 测试流式保存...")
    streaming_file = output_dir / "test_streaming.jsonl"
    
    with prompt_generator.create_streaming_saver(
        str(streaming_file),
        max_items_per_file=3,  # 小文件便于测试
        file_format="jsonl"
    ) as saver:
        for i, item in enumerate(test_data):
            saver.save_sft_item(item)
            print(f"   已保存第 {i+1} 个样本")
    
    print("5. 测试批量保存...")
    batch_file = output_dir / "test_batch.json"
    saved_files = prompt_generator.save_to_json(
        test_data,
        str(batch_file),
        max_items_per_file=3
    )
    print(f"   批量保存完成，文件数: {len(saved_files)}")
    
    print("6. 检查生成的文件...")
    for file in output_dir.glob("*"):
        if file.is_file():
            size = file.stat().st_size
            print(f"   - {file.name}: {size} 字节")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    try:
        test_streaming_save()
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc() 