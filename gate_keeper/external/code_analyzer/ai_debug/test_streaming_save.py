#!/usr/bin/env python3
"""
测试流式数据保存功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.utils import SFTDataSaver, StreamingDataSaver


def test_streaming_save():
    """测试流式保存功能"""
    print("=== 测试流式保存功能 ===")
    
    # 创建测试数据
    test_data = [
        {"id": i, "name": f"item_{i}", "value": i * 10}
        for i in range(25)
    ]
    
    # 测试流式保存器
    print("测试流式保存器...")
    with StreamingDataSaver(
        "ai_debug/test_streaming_output.jsonl",
        max_items_per_file=10,
        file_format="jsonl"
    ) as saver:
        
        for item in test_data:
            saver.save_item(item)
            print(f"  已保存: {item['name']}")
    
    print("流式保存完成！")
    
    # 测试批量保存器
    print("\n测试批量保存器...")
    saver = SFTDataSaver(
        max_items_per_file=10,
        output_dir="ai_debug",
        data_type="test"
    )
    
    saved_files = saver.save_data(test_data)
    print(f"批量保存完成，文件数: {len(saved_files)}")
    
    # 显示文件信息
    file_info = saver.get_file_info(test_data)
    print(f"文件信息: {file_info}")


def test_json_format():
    """测试JSON格式保存"""
    print("\n=== 测试JSON格式保存 ===")
    
    test_data = [
        {"id": i, "name": f"json_item_{i}"}
        for i in range(15)
    ]
    
    with StreamingDataSaver(
        "ai_debug/test_json_output.json",
        max_items_per_file=5,
        file_format="json"
    ) as saver:
        
        for item in test_data:
            saver.save_item(item)
    
    print("JSON格式保存完成！")


if __name__ == "__main__":
    # 确保输出目录存在
    os.makedirs("ai_debug", exist_ok=True)
    
    try:
        test_streaming_save()
        test_json_format()
        
        print("\n=== 测试完成 ===")
        print("生成的文件:")
        for file in os.listdir("ai_debug"):
            if file.startswith("test_") and file.endswith((".json", ".jsonl")):
                print(f"  - ai_debug/{file}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc() 