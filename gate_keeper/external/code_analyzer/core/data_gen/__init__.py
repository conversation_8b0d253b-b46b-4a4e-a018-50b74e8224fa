"""
数据生成模块

提供多种数据生成功能，包括SFT、Embedding等
"""

# 数据清洗模块
from .clean import AnomalyDetector, EncodingDetector, TextCleaner
# Embedding模块
from .embedding import (CandidateSampler, ContextCandidate, ContextExtractor,
                        ContextType, EmbeddingDataGenerator, EmbeddingPair,
                        EmbeddingTrainingData, ExtractionConfig,
                        GeneratorConfig, QueryContext, RerankTrainingData,
                        SamplingConfig)
# SFT模块
from .sft import AlpacaSFTData  # 基础模型; 基础类; 核心组件; 子模块; 独立版本
from .sft import (AlpacaSFTGenerator, AlpacaTaskType, BaseSFTData,
                  BaseSFTGenerator, CodeCompletionTask, ContextNode,
                  ContextType, DataQualityValidator, DependencyEdge,
                  DependencyGraph, DependencyNode, EnhancedStatementMaskData,
                  ModelType, PromptSFTData, PromptSFTGenerator, RealNPAnalyzer,
                  RealStatementMaskData, RelatedStruct, SFTDataGenerator,
                  SFTUtils, SimpleSFTData, SimpleSFTGenerator,
                  StandaloneDataQualityValidator, StandaloneNPAnalyzer,
                  StandaloneSFTDataGenerator)

__all__ = [
    # SFT模块
    'ContextNode', 'RelatedStruct', 'DependencyNode', 'DependencyEdge',
    'DependencyGraph', 'EnhancedStatementMaskData', 'RealStatementMaskData', 'BaseSFTData',
    'BaseSFTGenerator', 'SFTUtils',
    'RealNPAnalyzer', 'SFTDataGenerator', 'DataQualityValidator',
    'SimpleSFTData', 'SimpleSFTGenerator',
    'PromptSFTData', 'PromptSFTGenerator', 'ModelType', 'ContextType',
    'AlpacaSFTData', 'AlpacaSFTGenerator', 'AlpacaTaskType', 'CodeCompletionTask',
    'StandaloneSFTDataGenerator', 'StandaloneNPAnalyzer', 'StandaloneDataQualityValidator',
    
    # Embedding模块
    'ContextCandidate', 'QueryContext', 'EmbeddingPair', 'EmbeddingTrainingData', 'RerankTrainingData',
    'ContextType', 'ContextExtractor', 'CandidateSampler', 'EmbeddingDataGenerator',
    'ExtractionConfig', 'SamplingConfig', 'GeneratorConfig',
    
    # 数据清洗模块
    'AnomalyDetector', 'EncodingDetector', 'TextCleaner'
]
