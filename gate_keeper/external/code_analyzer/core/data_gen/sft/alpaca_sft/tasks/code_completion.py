"""
代码补全任务

将prompt_sft数据转换为代码补全的Alpaca格式
"""

from ..models import AlpacaTaskType
from .base_task import BaseAlpacaTask


class CodeCompletionTask(BaseAlpacaTask):
    """代码补全任务"""
    
    def __init__(self):
        """初始化代码补全任务"""
        super().__init__(AlpacaTaskType.CODE_COMPLETION)
    
    def get_instruction(self) -> str:
        """获取任务指令"""
        return "请根据给定的代码上下文，在指定位置补全代码。"
    
    def format_input(self, **kwargs) -> str:
        """格式化输入内容"""
        filepath = kwargs.get('filepath', '')
        function_name = kwargs.get('function_name', '')
        line_number = kwargs.get('line_number', 0)
        before = kwargs.get('before', '')
        after = kwargs.get('after', '')
        hole_marker = kwargs.get('hole_marker', '<HOLE>')
        
        # 如果传入的标记为空，使用默认标记
        if not hole_marker:
            hole_marker = '<HOLE>'
        
        return f"""文件路径: {filepath}
函数名称: {function_name}
行号: {line_number}

代码上下文:
```c
{before}
{hole_marker}
{after}
```

请补全{hole_marker}位置的代码，保持代码风格一致，符合上下文逻辑。"""
    
    def format_output(self, **kwargs) -> str:
        """格式化输出内容"""
        expected = kwargs.get('expected', '')
        return expected 