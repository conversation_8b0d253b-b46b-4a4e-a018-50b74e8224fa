"""
Alpaca SFT任务基类

定义所有任务类型的通用接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from ..models import AlpacaSFTData, AlpacaTaskType


class BaseAlpacaTask(ABC):
    """Alpaca SFT任务基类"""
    
    def __init__(self, task_type: AlpacaTaskType):
        """初始化任务"""
        self.task_type = task_type
    
    @abstractmethod
    def get_instruction(self) -> str:
        """获取任务指令"""
        pass
    
    @abstractmethod
    def format_input(self, **kwargs) -> str:
        """格式化输入内容"""
        pass
    
    @abstractmethod
    def format_output(self, **kwargs) -> str:
        """格式化输出内容"""
        pass
    
    def convert_to_alpaca_format(self, prompt_item, **kwargs) -> Optional[AlpacaSFTData]:
        """将prompt_sft数据转换为Alpaca格式"""
        try:
            # 获取基础数据
            filepath = getattr(prompt_item, 'filepath', '')
            function_name = getattr(prompt_item, 'function_name', '')
            line_number = getattr(prompt_item, 'line_number', 0)
            before = getattr(prompt_item, 'before', '')
            expected = getattr(prompt_item, 'expected', '')
            after = getattr(prompt_item, 'after', '')
            complexity_score = getattr(prompt_item, 'complexity_score', 0.0)
            context_relevance_score = getattr(prompt_item, 'context_relevance_score', 0.0)
            
            # 格式化模板
            instruction = self.get_instruction()
            input_text = self.format_input(
                filepath=filepath,
                function_name=function_name,
                line_number=line_number,
                before=before,
                expected=expected,
                after=after,
                **kwargs
            )
            output_text = self.format_output(
                expected=expected,
                **kwargs
            )
            
            # 创建Alpaca数据
            alpaca_data = AlpacaSFTData(
                instruction=instruction,
                input=input_text,
                output=output_text,
                filepath=filepath,
                function_name=function_name,
                line_number=line_number,
                task_type=self.task_type,
                before=before,
                expected=expected,
                after=after,
                complexity_score=complexity_score,
                context_relevance_score=context_relevance_score
            )
            
            return alpaca_data
            
        except Exception as e:
            print(f"    转换Alpaca格式失败: {e}")
            return None
    
    def validate_data(self, alpaca_data: AlpacaSFTData) -> bool:
        """验证数据质量"""
        return alpaca_data.validate() 