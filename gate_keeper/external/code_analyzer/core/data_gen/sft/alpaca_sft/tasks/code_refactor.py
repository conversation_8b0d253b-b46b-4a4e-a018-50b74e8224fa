"""
代码重构任务

将prompt_sft数据转换为代码重构的Alpaca格式
"""

from ..models import AlpacaTaskType
from .base_task import BaseAlpacaTask


class CodeRefactorTask(BaseAlpacaTask):
    """代码重构任务"""
    
    def __init__(self):
        """初始化代码重构任务"""
        super().__init__(AlpacaTaskType.CODE_REFACTOR)
    
    def get_instruction(self) -> str:
        """获取任务指令"""
        return "请重构以下代码，提高代码质量和可读性。"
    
    def format_input(self, **kwargs) -> str:
        """格式化输入内容"""
        filepath = kwargs.get('filepath', '')
        function_name = kwargs.get('function_name', '')
        before = kwargs.get('before', '')
        expected = kwargs.get('expected', '')
        after = kwargs.get('after', '')
        
        return f"""文件路径: {filepath}
函数名称: {function_name}

原始代码:
```c
{before}
{expected}
{after}
```

请重构这段代码，提高代码质量、可读性和维护性。"""
    
    def format_output(self, **kwargs) -> str:
        """格式化输出内容"""
        expected = kwargs.get('expected', '')
        return f"重构后的代码：\n```c\n{expected}\n```" 