"""
Bug修复任务

将prompt_sft数据转换为Bug修复的Alpaca格式
"""

from ..models import AlpacaTaskType
from .base_task import BaseAlpacaTask


class BugFixTask(BaseAlpacaTask):
    """Bug修复任务"""
    
    def __init__(self):
        """初始化Bug修复任务"""
        super().__init__(AlpacaTaskType.BUG_FIX)
    
    def get_instruction(self) -> str:
        """获取任务指令"""
        return "请分析以下代码中的问题并提供修复方案。"
    
    def format_input(self, **kwargs) -> str:
        """格式化输入内容"""
        filepath = kwargs.get('filepath', '')
        function_name = kwargs.get('function_name', '')
        before = kwargs.get('before', '')
        expected = kwargs.get('expected', '')
        after = kwargs.get('after', '')
        
        return f"""文件路径: {filepath}
函数名称: {function_name}

有问题的代码:
```c
{before}
{expected}
{after}
```

请分析代码中的问题并提供修复后的代码。"""
    
    def format_output(self, **kwargs) -> str:
        """格式化输出内容"""
        expected = kwargs.get('expected', '')
        return f"问题分析：...\n修复方案：\n```c\n{expected}\n```" 