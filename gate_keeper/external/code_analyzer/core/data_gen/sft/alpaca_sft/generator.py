"""
Alpaca SFT数据生成器

将prompt_sft的输出结果转换为Alpaca格式的训练数据
简化版本：只做字段剔除，直接使用prompt_sft的三元组数据
"""

import json
from typing import Any, Dict, List, Optional

from ..base_generator import BaseSFTGenerator
from ..prompt_sft.generator import PromptSFTGenerator
from ..prompt_sft.models import ModelType, PromptSFTData
from .models import AlpacaSFTData, AlpacaTaskType


class AlpacaSFTGenerator:
    """Alpaca SFT数据生成器 - 简化版本，只做字段剔除"""
    
    def __init__(self, sft_generator: BaseSFTGenerator):
        """初始化生成器"""
        self.sft_generator = sft_generator
        self.prompt_generator = PromptSFTGenerator(sft_generator)
    
    def generate_alpaca_sft_data(self, 
                                max_samples: int = 10,
                                model_type: ModelType = ModelType.DEEPSEEK_CODER,
                                task_type: AlpacaTaskType = AlpacaTaskType.CODE_COMPLETION) -> List[AlpacaSFTData]:
        """生成Alpaca格式的SFT数据"""
        print(f"\n=== 生成Alpaca SFT数据 (最多{max_samples}个样本) ===")
        print(f"模型类型: {model_type.value}")
        print(f"任务类型: {task_type.value}")
        
        # 1. 生成prompt_sft数据
        print("1. 生成prompt_sft数据...")
        prompt_sft_data = self.prompt_generator.generate_prompt_sft_data(
            max_samples=max_samples,
            model_type=model_type
        )
        print(f"   生成了 {len(prompt_sft_data)} 个prompt_sft数据样本")
        
        # 2. 转换为Alpaca格式（简单字段剔除）
        print("2. 转换为Alpaca格式...")
        alpaca_data = []
        
        for prompt_item in prompt_sft_data:
            try:
                # 直接使用prompt_sft的三元组数据
                alpaca_item = AlpacaSFTData(
                    instruction=prompt_item.instruction,
                    input=prompt_item.input,
                    output=prompt_item.output,
                    # 保留必要的元数据
                    filepath=prompt_item.filepath,
                    function_name=prompt_item.function_name,
                    line_number=prompt_item.line_number,
                    model_type=prompt_item.model_type.value,
                    task_type=task_type.value,
                    # 剔除复杂的上下文信息，只保留核心数据
                    before=prompt_item.before,
                    expected=prompt_item.expected,
                    after=prompt_item.after
                )
                alpaca_data.append(alpaca_item)
            except Exception as e:
                print(f"    转换样本时出错: {e}")
                continue
        
        print(f"  生成了 {len(alpaca_data)} 个Alpaca格式数据样本")
        return alpaca_data
    
    def save_to_json(self, alpaca_data: List[AlpacaSFTData], output_file: str, 
                     alpaca_format_only: bool = False,
                     max_items_per_file: int = 10000):
        """保存Alpaca数据到JSON文件（支持分文件存储）"""
        from pathlib import Path

        from ..utils import SFTDataSaver
        
        print(f"保存Alpaca数据到: {output_file}")
        
        # 创建数据保存器
        output_dir = Path(output_file).parent
        filename_prefix = Path(output_file).stem
        
        saver = SFTDataSaver(
            max_items_per_file=max_items_per_file,
            output_dir=str(output_dir),
            data_type="alpaca"
        )
        
        # 保存数据
        saved_files = saver.save_alpaca_data(alpaca_data, alpaca_format_only)
        
        print(f"  已保存 {len(alpaca_data)} 个样本到 {len(saved_files)} 个文件")
        for file_path in saved_files:
            print(f"    - {file_path}")
        
        return saved_files
    
    def save_to_jsonl(self, alpaca_data: List[AlpacaSFTData], output_file: str,
                      alpaca_format_only: bool = False,
                      max_items_per_file: int = 10000):
        """保存Alpaca数据到JSONL文件（支持分文件存储）"""
        from pathlib import Path

        from ..utils import SFTDataSaver
        
        print(f"保存Alpaca数据到JSONL: {output_file}")
        
        # 创建数据保存器
        output_dir = Path(output_file).parent
        filename_prefix = Path(output_file).stem
        
        saver = SFTDataSaver(
            max_items_per_file=max_items_per_file,
            output_dir=str(output_dir),
            data_type="alpaca"
        )
        
        # 保存数据为JSONL格式
        saved_files = saver.save_jsonl_alpaca_data(alpaca_data, alpaca_format_only)
        
        print(f"  已保存 {len(alpaca_data)} 个样本到 {len(saved_files)} 个JSONL文件")
        for file_path in saved_files:
            print(f"    - {file_path}")
        
        return saved_files
    
    def create_streaming_saver(self, output_file: str, 
                             max_items_per_file: int = 10000,
                             file_format: str = "jsonl") -> "StreamingSFTDataSaver":
        """创建流式数据保存器
        
        Args:
            output_file: 输出文件路径
            max_items_per_file: 每个文件最大条数，默认10000
            file_format: 文件格式，"json" 或 "jsonl"
            
        Returns:
            流式数据保存器实例
        """
        from ..utils import StreamingSFTDataSaver
        
        return StreamingSFTDataSaver(
            output_file=output_file,
            max_items_per_file=max_items_per_file,
            file_format=file_format,
            data_type="alpaca"
        )
    
    def get_statistics(self, alpaca_data: List[AlpacaSFTData]) -> Dict[str, Any]:
        """获取数据统计信息"""
        stats = {
            "total_samples": len(alpaca_data) if alpaca_data else 0,
            "model_types": {},
            "task_types": {},
            "avg_instruction_length": 0,
            "avg_input_length": 0,
            "avg_output_length": 0
        }
        
        if not alpaca_data:
            return stats
        
        total_instruction_length = 0
        total_input_length = 0
        total_output_length = 0
        
        for item in alpaca_data:
            # 统计模型类型
            model_type = item.model_type
            stats["model_types"][model_type] = stats["model_types"].get(model_type, 0) + 1
        
            # 统计任务类型
            task_type = item.task_type
            stats["task_types"][task_type] = stats["task_types"].get(task_type, 0) + 1
            
            # 累计长度
            total_instruction_length += len(item.instruction)
            total_input_length += len(item.input)
            total_output_length += len(item.output)
        
        # 计算平均值
        stats["avg_instruction_length"] = total_instruction_length / len(alpaca_data)
        stats["avg_input_length"] = total_input_length / len(alpaca_data)
        stats["avg_output_length"] = total_output_length / len(alpaca_data)
        
        return stats