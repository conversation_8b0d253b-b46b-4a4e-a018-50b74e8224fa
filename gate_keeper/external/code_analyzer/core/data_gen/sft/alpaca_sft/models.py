"""
Alpaca SFT数据模型

定义Alpaca格式的训练数据结构
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional


class AlpacaTaskType(Enum):
    """Alpaca任务类型"""
    CODE_COMPLETION = "code_completion"
    CODE_EDIT = "code_edit"
    CODE_EXPLANATION = "code_explanation"
    BUG_FIX = "bug_fix"
    CODE_REFACTOR = "code_refactor"


@dataclass
class AlpacaSFTData:
    """Alpaca格式的SFT训练数据"""
    # 核心字段（符合Alpaca格式）
    instruction: str
    input: str
    output: str
    
    # 元数据（用于调试和验证）
    filepath: str = ""
    function_name: str = ""
    line_number: int = 0
    model_type: str = ""  # 添加模型类型字段
    task_type: str = ""   # 改为字符串类型，与prompt_sft兼容
    
    # 原始数据（用于调试）
    before: str = ""
    expected: str = ""
    after: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（包含元数据）"""
        return {
            "instruction": self.instruction,
            "input": self.input,
            "output": self.output,
            # 元数据字段
            "filepath": self.filepath,
            "function_name": self.function_name,
            "line_number": self.line_number,
            "model_type": self.model_type,
            "task_type": self.task_type,
            "before": self.before,
            "expected": self.expected,
            "after": self.after
        }
    
    def to_alpaca_dict(self) -> Dict[str, Any]:
        """转换为标准Alpaca格式字典（仅包含核心字段）"""
        return {
            "instruction": self.instruction,
            "input": self.input,
            "output": self.output
        }
    
    def validate(self) -> bool:
        """验证数据质量"""
        if not self.instruction or not self.output:
            return False
        
        if len(self.instruction) < 5 or len(self.output) < 1:
            return False
        
        return True