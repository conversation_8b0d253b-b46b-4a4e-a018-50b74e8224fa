"""
SFT数据生成模块

提供多种SFT数据生成功能
"""

from .alpaca_sft import (AlpacaSFTData, AlpacaSFTGenerator, AlpacaTaskType,
                         CodeCompletionTask)
# 分析器和生成器
from .analyzer import RealNPAnalyzer
from .base_generator import BaseSFTGenerator
# 基础模块
from .base_models import (BaseSFTData, ContextNode, DependencyEdge,
                          DependencyGraph, DependencyNode,
                          EnhancedStatementMaskData, RealStatementMaskData,
                          RelatedStruct)
from .generator import SFTDataGenerator
from .prompt_sft import (ContextType, ModelType, PromptSFTData,
                         PromptSFTGenerator)
# 子模块
from .simple_sft import SimpleSFTData, SimpleSFTGenerator
# 独立版本
from .standalone import (StandaloneDataQualityValidator, StandaloneNPAnalyzer,
                         StandaloneSFTDataGenerator)
from .utils import SFTUtils
from .validator import DataQualityValidator

__all__ = [
    # 基础模型
    'ContextNode', 'RelatedStruct', 'DependencyNode', 'DependencyEdge',
    'DependencyGraph', 'EnhancedStatementMaskData', 'RealStatementMaskData', 'BaseSFTData',
    
    # 基础类
    'BaseSFTGenerator', 'SFTUtils',
    
    # 核心组件
    'RealNPAnalyzer', 'SFTDataGenerator', 'DataQualityValidator',
    
    # 子模块
    'SimpleSFTData', 'SimpleSFTGenerator',
    'PromptSFTData', 'PromptSFTGenerator', 'ModelType', 'ContextType',
    'AlpacaSFTData', 'AlpacaSFTGenerator', 'AlpacaTaskType', 'CodeCompletionTask',
    
    # 独立版本
    'StandaloneSFTDataGenerator', 'StandaloneNPAnalyzer', 'StandaloneDataQualityValidator'
] 