"""
SFT数据生成模块 - 生成器

提供SFT（Supervised Fine-Tuning）数据生成功能，包括：
- 增强的语句遮盖数据生成
- 多种提取策略（按行、注释下、多行、语句级、代码块级、语义单元）
- 上下文建模和依赖图构建
"""

import random
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from tqdm import tqdm

from .base_generator import BaseSFTGenerator
from .base_models import (ContextNode, DependencyEdge, DependencyGraph,
                          DependencyNode, EnhancedStatementMaskData,
                          RealStatementMaskData, RelatedStruct)
from .extract_strategies import (EnhancedExtractionManager, ExtractionConfig,
                                 ExtractionStrategy, ExtractionStrategyFactory)
from .utils import SFTUtils


class SFTDataGenerator(BaseSFTGenerator):
    """SFT数据生成器"""
    
    def __init__(self, analyzer):
        """初始化生成器"""
        super().__init__(analyzer)
        self.validator = None  # 延迟初始化验证器
    
    def generate_enhanced_sft_data(self, max_samples: int = 10, 
                                  strategy: ExtractionStrategy = ExtractionStrategy.WATERFALL_SEQUENTIAL,
                                  config: Optional[ExtractionConfig] = None) -> List[EnhancedStatementMaskData]:
        """生成增强的SFT数据（v2.3规范）"""
        if max_samples <= 0:
            raise ValueError("max_samples必须大于0")
        if max_samples > 10000:
            raise ValueError("max_samples不能超过10000")
        
        # 使用默认配置或自定义配置
        if config is None:
            config = ExtractionConfig()
            config.strategy_name = strategy.value
        else:
            config.strategy_name = strategy.value
        
        print(f"\n=== 生成增强SFT数据 (最多{max_samples}个样本, 策略: {strategy.value}) ===")
        
        enhanced_data = []
        
        # 计算总函数数量用于进度条
        total_functions = sum(len(functions) for functions in self.analyzer._functions_cache.values())
        
        with tqdm(total=total_functions, desc="处理函数", unit="函数") as pbar:
            for file_path, functions in self.analyzer._functions_cache.items():
                for func in functions:
                    try:
                        # 使用新的提取策略
                        strategy_data = self._generate_enhanced_strategy_data(func, file_path, config)
                        enhanced_data.extend(strategy_data)
                        
                        if len(enhanced_data) >= max_samples:
                            break
                    except Exception as e:
                        print(f"    生成函数 {func.name} 的增强数据时出错: {e}")
                        continue
                    finally:
                        pbar.update(1)
                
                if len(enhanced_data) >= max_samples:
                    break
        
        # 限制样本数量
        enhanced_data = enhanced_data[:max_samples]
        
        print(f"  生成了 {len(enhanced_data)} 个增强SFT数据样本")
        return enhanced_data
    
    def generate_enhanced_sft_data_multi_strategy(self, max_samples: int = 10, 
                                                 strategies: List[ExtractionStrategy] = None,
                                                 strategy_max_samples: Dict[ExtractionStrategy, int] = None) -> List[EnhancedStatementMaskData]:
        """使用多种策略生成增强SFT数据
        
        Args:
            max_samples: 总样本数量限制（当strategy_max_samples为None时使用）
            strategies: 要使用的策略列表
            strategy_max_samples: 每个策略的独立样本数量限制，None表示无限制
        """
        if strategies is None:
            strategies = [
                ExtractionStrategy.WATERFALL_SEQUENTIAL,
                ExtractionStrategy.RANDOM_INPLACE,
                ExtractionStrategy.COMMENT_TO_CODE,
                ExtractionStrategy.CODE_TO_COMMENT,
                ExtractionStrategy.CONTROL_FLOW_HALLUCINATION
            ]
        
        print(f"\n=== 使用多种策略生成增强SFT数据 ===")
        print(f"  使用策略: {[s.value for s in strategies]}")
        
        if strategy_max_samples:
            print(f"  策略样本配置: {[(s.value, strategy_max_samples.get(s, '无限制')) for s in strategies]}")
        else:
            print(f"  总样本限制: {max_samples}个样本")
        
        all_data = []
        
        with tqdm(total=len(strategies), desc="处理策略", unit="策略") as pbar:
            for strategy in strategies:
                try:
                    # 确定当前策略的样本数量限制
                    if strategy_max_samples and strategy in strategy_max_samples:
                        strategy_limit = strategy_max_samples[strategy]
                        if strategy_limit is None or strategy_limit <= 0:
                            strategy_limit = None  # 无限制
                            print(f"    策略 {strategy.value}: 无样本数量限制")
                        else:
                            print(f"    策略 {strategy.value}: 最多{strategy_limit}个样本")
                    else:
                        # 使用平均分配
                        strategy_limit = max_samples // len(strategies)
                        print(f"    策略 {strategy.value}: 最多{strategy_limit}个样本")
                    
                    config = ExtractionConfig()
                    config.strategy_name = strategy.value
                    
                    # 生成策略数据
                    if strategy_limit is None:
                        # 无限制：使用一个很大的数字
                        strategy_data = self.generate_enhanced_sft_data(10000, strategy, config)
                    else:
                        strategy_data = self.generate_enhanced_sft_data(strategy_limit, strategy, config)
                    
                    all_data.extend(strategy_data)
                    print(f"    策略 {strategy.value}: 实际生成了 {len(strategy_data)} 个样本")
                    
                except Exception as e:
                    print(f"    策略 {strategy.value} 生成失败: {e}")
                    continue
                finally:
                    pbar.update(1)
        
        # 如果设置了总样本限制，则进行限制
        if not strategy_max_samples and max_samples > 0:
            all_data = all_data[:max_samples]
            print(f"  应用总样本限制: {len(all_data)} 个样本")
        
        print(f"  总计生成了 {len(all_data)} 个增强SFT数据样本")
        return all_data
    
    def generate_real_statement_mask_data(self, max_samples: int = 10) -> List[RealStatementMaskData]:
        """生成真实的语句遮盖数据（向后兼容）"""
        if max_samples <= 0:
            raise ValueError("max_samples必须大于0")
        if max_samples > 10000:
            raise ValueError("max_samples不能超过10000")
        
        print(f"\n=== 生成真实遮盖数据 (最多{max_samples}个样本) ===")
        
        mask_data = []
        
        for file_path, functions in self.analyzer._functions_cache.items():
            for func in functions:
                try:
                    # 生成瀑布式遮盖数据
                    waterfall_data = self._generate_real_waterfall_data(func, file_path)
                    mask_data.extend(waterfall_data)
                    
                    # 生成随机遮盖数据
                    random_data = self._generate_real_random_data(func, file_path)
                    mask_data.extend(random_data)
                    
                    if len(mask_data) >= max_samples:
                        break
                except Exception as e:
                    print(f"    生成函数 {func.name} 的遮盖数据时出错: {e}")
                    continue
            
            if len(mask_data) >= max_samples:
                break
        
        # 限制样本数量
        mask_data = mask_data[:max_samples]
        
        print(f"  生成了 {len(mask_data)} 个遮盖数据样本")
        return mask_data
    
    def _generate_enhanced_waterfall_data(self, func, file_path: str) -> List[EnhancedStatementMaskData]:
        """生成增强的瀑布式顺序遮盖数据"""
        data = []
        
        if not func.code:
            return data
        
        lines = func.code.split('\n')
        func_body_lines = self._extract_function_body_lines(lines)
        
        if len(func_body_lines) < 2:
            return data
        
        # 瀑布式遮盖：逐行遮盖
        for i in range(len(func_body_lines) - 1):
            try:
                before_lines = func_body_lines[:i+1]
                expected_line = func_body_lines[i+1]
                after_lines = func_body_lines[i+2:]
                
                # 构建before、expected、after
                before = '\n'.join(before_lines)
                expected = expected_line
                after = '\n'.join(after_lines)
                
                # 质量检查
                if not self._validate_enhanced_data_quality(before, expected, after):
                    continue
                
                # 构建上下文节点
                context_nodes = self._build_context_nodes(func, file_path)
                
                # 构建依赖图
                dependency_graph = self._build_dependency_graph(func, file_path)
                
                # 构建元数据
                metadata = self._build_enhanced_metadata(
                    func, file_path, "waterfall_sequential", "statement", 
                    before, expected, after, i+1
                )
                
                # 创建增强数据
                enhanced_data = EnhancedStatementMaskData(
                    before=before,
                    expected=expected,
                    after=after,
                    context_nodes=context_nodes,
                    dependency_graph=dependency_graph,
                    metadata=metadata
                )
                
                data.append(enhanced_data)
                
            except Exception as e:
                print(f"    生成瀑布式数据时出错: {e}")
                continue
        
        return data
    
    def _generate_enhanced_strategy_data(self, func, file_path: str, config: ExtractionConfig) -> List[EnhancedStatementMaskData]:
        """使用指定策略生成增强数据"""
        data = []
        
        if not func.code:
            return data
        
        try:
            # 创建提取管理器
            extraction_manager = EnhancedExtractionManager(config)
            
            # 提取SFT数据
            raw_extractions = extraction_manager.extract_sft_data(func.code, func.name)
            
            # 转换为增强数据格式
            for extraction in raw_extractions:
                try:
                    # 构建完整的before字段（包含函数签名）
                    complete_before = self._build_complete_before(func, extraction.before)
                    
                    # 构建上下文节点
                    context_nodes = self._build_context_nodes(func, file_path)
                    
                    # 构建依赖图
                    dependency_graph = self._build_dependency_graph(func, file_path)
                    
                    # 构建元数据
                    metadata = self._build_enhanced_metadata(
                        func, file_path, config.strategy_name, "enhanced", 
                        complete_before, extraction.expected, extraction.after, 0
                    )
                    
                    # 更新元数据
                    extraction.context_nodes = context_nodes
                    extraction.dependency_graph = dependency_graph
                    extraction.metadata.update(metadata)
                    
                    # 更新before字段
                    extraction.before = complete_before
                    
                    # 质量检查
                    if self._validate_enhanced_data_quality(
                        complete_before, extraction.expected, extraction.after,
                        context_nodes, metadata
                    ):
                        data.append(extraction)
                
                except Exception as e:
                    print(f"    处理提取数据时出错: {e}")
                    continue
        
        except Exception as e:
            print(f"    使用策略 {config.strategy_name} 生成数据时出错: {e}")
        
        return data
    
    def _generate_enhanced_random_data(self, func, file_path: str) -> List[EnhancedStatementMaskData]:
        """生成增强的随机遮盖数据"""
        data = []
        
        if not func.code:
            return data
        
        lines = func.code.split('\n')
        func_body_lines = self._extract_function_body_lines(lines)
        
        if len(func_body_lines) < 3:
            return data
        
        # 随机遮盖：随机选择语句进行遮盖
        num_samples = min(3, len(func_body_lines) // 2)  # 最多生成3个随机样本
        
        for _ in range(num_samples):
            try:
                # 随机选择遮盖位置
                mask_index = random.randint(1, len(func_body_lines) - 2)
                
                before_lines = func_body_lines[:mask_index]
                expected_line = func_body_lines[mask_index]
                after_lines = func_body_lines[mask_index+1:]
                
                # 构建before、expected、after
                before = '\n'.join(before_lines)
                expected = expected_line
                after = '\n'.join(after_lines)
                
                # 质量检查
                if not self._validate_enhanced_data_quality(before, expected, after):
                    continue
                
                # 构建上下文节点
                context_nodes = self._build_context_nodes(func, file_path)
                
                # 构建依赖图
                dependency_graph = self._build_dependency_graph(func, file_path)
                
                # 构建元数据
                metadata = self._build_enhanced_metadata(
                    func, file_path, "random_inplace", "statement", 
                    before, expected, after, mask_index+1
                )
                
                # 创建增强数据
                enhanced_data = EnhancedStatementMaskData(
                    before=before,
                    expected=expected,
                    after=after,
                    context_nodes=context_nodes,
                    dependency_graph=dependency_graph,
                    metadata=metadata
                )
                
                data.append(enhanced_data)
                
            except Exception as e:
                print(f"    生成随机遮盖数据时出错: {e}")
                continue
        
        return data
    
    def _generate_real_waterfall_data(self, func, file_path: str) -> List[RealStatementMaskData]:
        """生成真实的瀑布式遮盖数据（向后兼容）"""
        data = []
        
        if not func.code:
            return data
        
        lines = func.code.split('\n')
        func_body_lines = self._extract_function_body_lines(lines)
        
        if len(func_body_lines) < 2:
            return data
        
        # 瀑布式遮盖：逐行遮盖
        for i in range(len(func_body_lines) - 1):
            try:
                before_lines = func_body_lines[:i+1]
                expected_line = func_body_lines[i+1]
                after_lines = func_body_lines[i+2:]
                
                # 构建before、expected、after
                before = '\n'.join(before_lines)
                expected = expected_line
                after = '\n'.join(after_lines)
                
                # 质量检查
                if not self._validate_real_data_quality(before, expected, after):
                    continue
                
                # 构建上下文列表
                context_list = self._build_context_list(func, file_path)
                
                # 创建真实数据
                real_data = RealStatementMaskData(
                    context_list=context_list,
                    filepath=file_path,
                    before=before,
                    expected=expected,
                    after=after,
                    function_name=func.name,
                    statement_type="statement",
                    line_number=func.range.start_line + i + 1,
                    call_chain=self._get_call_chain(func.name),
                    related_structs=self._get_related_structs(func, file_path),
                    variable_references=self._get_variable_references(func),
                    complexity_score=self._calculate_complexity_score(func),
                    ast_node_type="statement"
                )
                
                data.append(real_data)
                
            except Exception as e:
                print(f"    生成真实瀑布式数据时出错: {e}")
                continue
        
        return data
    
    def _generate_real_random_data(self, func, file_path: str) -> List[RealStatementMaskData]:
        """生成真实的随机遮盖数据（向后兼容）"""
        data = []
        
        if not func.code:
            return data
        
        lines = func.code.split('\n')
        func_body_lines = self._extract_function_body_lines(lines)
        
        if len(func_body_lines) < 3:
            return data
        
        # 随机遮盖：随机选择语句进行遮盖
        num_samples = min(2, len(func_body_lines) // 2)  # 最多生成2个随机样本
        
        for _ in range(num_samples):
            try:
                # 随机选择遮盖位置
                mask_index = random.randint(1, len(func_body_lines) - 2)
                
                before_lines = func_body_lines[:mask_index]
                expected_line = func_body_lines[mask_index]
                after_lines = func_body_lines[mask_index+1:]
                
                # 构建before、expected、after
                before = '\n'.join(before_lines)
                expected = expected_line
                after = '\n'.join(after_lines)
                
                # 质量检查
                if not self._validate_real_data_quality(before, expected, after):
                    continue
                
                # 构建上下文列表
                context_list = self._build_context_list(func, file_path)
                
                # 创建真实数据
                real_data = RealStatementMaskData(
                    context_list=context_list,
                    filepath=file_path,
                    before=before,
                    expected=expected,
                    after=after,
                    function_name=func.name,
                    statement_type="statement",
                    line_number=func.range.start_line + mask_index + 1,
                    call_chain=self._get_call_chain(func.name),
                    related_structs=self._get_related_structs(func, file_path),
                    variable_references=self._get_variable_references(func),
                    complexity_score=self._calculate_complexity_score(func),
                    ast_node_type="statement"
                )
                
                data.append(real_data)
                
            except Exception as e:
                print(f"    生成真实随机遮盖数据时出错: {e}")
                continue
        
        return data
    
    def _extract_function_body_lines(self, lines: List[str]) -> List[str]:
        """提取函数体行"""
        func_body_lines = []
        in_function = False
        brace_count = 0
        
        for line in lines:
            stripped_line = line.strip()
            
            # 检测函数开始
            if '{' in stripped_line and not in_function:
                in_function = True
                brace_count += stripped_line.count('{')
                continue
            
            if in_function:
                if '{' in stripped_line:
                    brace_count += stripped_line.count('{')
                
                if '}' in stripped_line:
                    brace_count -= stripped_line.count('}')
                    if brace_count == 0:
                        break
                
                # 添加函数体行
                if stripped_line and not stripped_line.startswith('//'):
                    func_body_lines.append(stripped_line)
        
        return func_body_lines
    
    def _validate_enhanced_data_quality(self, before: str, expected: str, after: str, context_nodes=None, metadata=None) -> bool:
        """验证增强数据质量"""
        # 基本长度检查
        if len(before) > self.max_before_length:
            return False
        
        if len(expected) > self.max_expected_length:
            return False
        
        if len(after) > self.max_after_length:
            return False
        
        # 内容检查
        if not before.strip() or not expected.strip():
            return False
        
        # 检查expected是否为注释
        if expected.strip().startswith('//') or expected.strip().startswith('/*'):
            return False
        
        # 检查上下文节点（如果提供）
        if context_nodes is not None and len(context_nodes) == 0:
            return False
        
        # 检查metadata（如果提供）
        if metadata is not None:
            required_fields = ['task_type', 'strategy', 'mask_level', 'function_name', 'filepath']
            for field in required_fields:
                if field not in metadata:
                    return False
        
        return True
    
    def _validate_real_data_quality(self, before: str, expected: str, after: str) -> bool:
        """验证真实数据质量"""
        # 基本长度检查
        if len(before) > 10000:
            return False
        
        if len(expected) > 1000:
            return False
        
        if len(after) > 10000:
            return False
        
        # 内容检查
        if not before.strip() or not expected.strip():
            return False
        
        # 检查expected是否为注释
        if expected.strip().startswith('//') or expected.strip().startswith('/*'):
            return False
        
        return True
    
    def _build_context_nodes(self, func, file_path: str) -> List[ContextNode]:
        """构建上下文节点 - 使用分析器缓存"""
        context_nodes = []
        
        # 添加函数本身
        context_nodes.append(ContextNode(
            node_type="function",
            name=func.name,
            filepath=file_path,
            code=func.code[:200] + "..." if len(func.code) > 200 else func.code,
            scope="global",
            reference_line=func.range.start_line
        ))
        
        # 添加相关函数（从调用关系中获取）
        calls = self.analyzer._calls_cache.get(file_path, [])
        for call in calls:
            if call.caller == func.name:
                context_nodes.append(ContextNode(
                    node_type="function",
                    name=call.callee,
                    filepath=file_path,
                    code=f"// 调用函数: {call.callee}",
                    scope="global",
                    reference_line=call.line[0] if call.line else 0
                ))
        
        # 添加相关结构体
        usages = self.analyzer._usages_cache.get(file_path, [])
        for usage in usages:
            if usage.function_name == func.name:
                context_nodes.append(ContextNode(
                    node_type="struct",
                    name=usage.struct_name,
                    filepath=file_path,
                    code=f"// 使用结构体: {usage.struct_name}",
                    scope="global",
                    reference_line=usage.line[0] if usage.line else 0
                ))
        
        # 限制节点数量
        return context_nodes[:self.max_context_nodes]
    
    def _build_dependency_graph(self, func, file_path: str) -> DependencyGraph:
        """构建依赖图 - 使用分析器缓存"""
        nodes = []
        edges = []
        
        # 添加函数节点
        nodes.append(DependencyNode(
            id=f"fn_{func.name}",
            type="function"
        ))
        
        # 添加调用关系
        calls = self.analyzer._calls_cache.get(file_path, [])
        for call in calls:
            if call.caller == func.name:
                # 添加被调用函数节点
                nodes.append(DependencyNode(
                    id=f"fn_{call.callee}",
                    type="function"
                ))
                
                # 添加调用边
                edges.append(DependencyEdge(
                    source=f"fn_{call.caller}",
                    target=f"fn_{call.callee}",
                    relation="calls"
                ))
        
        # 添加结构体使用关系
        usages = self.analyzer._usages_cache.get(file_path, [])
        for usage in usages:
            if usage.function_name == func.name:
                # 添加结构体节点
                nodes.append(DependencyNode(
                    id=f"struct_{usage.struct_name}",
                    type="struct"
                ))
                
                # 添加使用边
                edges.append(DependencyEdge(
                    source=f"fn_{func.name}",
                    target=f"struct_{usage.struct_name}",
                    relation="uses"
                ))
        
        return DependencyGraph(nodes=nodes, edges=edges)
    
    def _build_enhanced_metadata(self, func, file_path: str, strategy: str, mask_level: str,
                                before: str, expected: str, after: str, line_number: int) -> Dict[str, Any]:
        """构建增强元数据"""
        # 检查是否为控制流语句
        is_control_flow = self._is_control_flow_statement(expected)
        
        # 检查是否包含注释
        comment_hint = self._has_comment_hint(expected)
        
        # 计算复杂度分数
        complexity_score = self._calculate_complexity_score(func)
        
        # 获取调用链
        call_chain = self._get_call_chain(func.name)
        
        # 获取变量引用
        variable_references = self._get_variable_references(func)
        
        # 获取相关结构体
        related_structs = self._get_related_structs(func, file_path)
        
        return {
            "task_type": "code_completion",
            "strategy": strategy,
            "mask_level": mask_level,
            "function_name": func.name,
            "filepath": file_path,
            "line_number": line_number,
            "ast_node_type": "statement",
            "is_control_flow": is_control_flow,
            "comment_hint": comment_hint,
            "complexity_score": complexity_score,
            "call_chain": call_chain,
            "variable_references": variable_references,
            "related_structs": related_structs,
            "data_id": f"np_{random.randint(1000000, 9999999)}",
            "source_type": "inhouse_trace_module",
            "author": "auto_extract",
            "version": "v2.3",
            "seed": self.seed,
            "masking_distribution": "uniform",
            "generation_time": datetime.now().isoformat()
        }
    
    def _build_context_list(self, func, file_path: str) -> List[str]:
        """构建上下文列表（向后兼容）"""
        context_list = []
        
        # 添加函数签名
        context_list.append(f"函数: {func.name}")
        
        # 添加相关函数
        calls = self.analyzer._calls_cache.get(file_path, [])
        for call in calls:
            if call.caller == func.name:
                context_list.append(f"调用: {call.callee}")
        
        # 添加相关结构体
        usages = self.analyzer._usages_cache.get(file_path, [])
        for usage in usages:
            if usage.function_name == func.name:
                context_list.append(f"使用结构体: {usage.struct_name}")
        
        return context_list
    
    def _is_control_flow_statement(self, statement: str) -> bool:
        """检查是否为控制流语句"""
        return SFTUtils.is_control_flow_statement(statement)
    
    def _has_comment_hint(self, statement: str) -> bool:
        """检查是否包含注释提示"""
        return SFTUtils.has_comment_hint(statement)
    
    def _calculate_complexity_score(self, func) -> float:
        """计算复杂度分数"""
        if not func.code:
            return 0.0
        
        complexity = 0.0
        lines = func.code.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):
                continue
            
            # 控制流复杂度
            if any(keyword in line for keyword in ['if', 'for', 'while', 'switch']):
                complexity += 1.0
            
            # 函数调用复杂度
            if '(' in line and ')' in line:
                complexity += 0.5
            
            # 嵌套复杂度
            if line.startswith('{'):
                complexity += 0.2
            elif line.startswith('}'):
                complexity -= 0.2
        
        return max(0.0, min(1.0, complexity / 10.0))  # 归一化到0-1
    
    def _get_call_chain(self, func_name: str) -> List[str]:
        """获取调用链"""
        chain = [func_name]
        visited = set()
        
        def dfs(current_func, depth):
            if depth >= 5 or current_func in visited:
                return
            visited.add(current_func)
            
            for calls in self.analyzer._calls_cache.values():
                for call in calls:
                    if call.caller == current_func and call.callee not in chain:
                        chain.append(call.callee)
                        dfs(call.callee, depth + 1)
        
        dfs(func_name, 0)
        return chain
    
    def _get_variable_references(self, func) -> List[str]:
        """获取变量引用"""
        variables = []
        
        if not func.code:
            return variables
        
        # 简单的变量提取
        lines = func.code.split('\n')
        for line in lines:
            # 匹配变量声明
            var_match = re.search(r'(uint8|uint16|uint32|int)\s+(\w+)', line)
            if var_match:
                variables.append(var_match.group(2))
        
        return variables
    
    def _build_complete_before(self, func, original_before: str) -> str:
        """构建完整的before字段，包含函数签名和光标之前的代码"""
        if not func.code:
            return original_before
        
        lines = func.code.split('\n')
        
        # 找到函数签名（通常是第一行或前几行）
        function_signature = ""
        body_start_index = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            # 改进函数签名识别逻辑
            if line and (
                line.startswith('void ') or 
                line.startswith('bundle ') or 
                line.startswith('inline ') or 
                line.startswith('extern ') or
                (line.startswith('(') and ')' in line and '{' in line) or  # 处理 (TYPE) func() 格式
                ('(' in line and ')' in line and '{' in line) or  # 处理 func() { 格式
                (func.name in line and '(' in line and ')' in line)  # 通过函数名匹配
            ):
                function_signature = line
                body_start_index = i + 1
                break
        
        # 如果找到了函数签名，构建完整的before
        if function_signature:
            # 提取函数体（从第一个{开始到最后一个}结束）
            body_lines = []
            brace_count = 0
            in_body = False
            
            for line in lines[body_start_index:]:
                if '{' in line:
                    brace_count += line.count('{')
                    in_body = True
                
                if in_body:
                    body_lines.append(line)
                
                if '}' in line:
                    brace_count -= line.count('}')
                    if brace_count <= 0:
                        break
            
            # 找到original_before在函数体中的位置
            if original_before.strip():
                # 在函数体中查找original_before的位置
                body_text = '\n'.join(body_lines)
                before_pos = body_text.find(original_before.strip())
                
                if before_pos != -1:
                    # 构建完整的before：函数签名 + 光标之前的代码
                    complete_before = function_signature + '\n'
                    
                    # 添加光标之前的函数体代码
                    before_body = body_text[:before_pos].strip()
                    if before_body:
                        complete_before += before_body + '\n'
                    
                    return complete_before.rstrip()
        
        # 如果无法解析，尝试使用函数名构建基本签名
        if func.name:
            # 尝试从原始代码中找到函数签名
            for line in lines:
                if func.name in line and '(' in line and ')' in line:
                    function_signature = line.strip()
                    break
            
            if function_signature:
                return function_signature + '\n' + original_before
        
        # 如果无法解析，返回原始before
        return original_before
    
    def _get_related_structs(self, func, file_path: str) -> List[str]:
        """获取相关结构体"""
        structs = []
        
        usages = self.analyzer._usages_cache.get(file_path, [])
        for usage in usages:
            if usage.function_name == func.name:
                structs.append(usage.struct_name)
        
        return structs 