"""
提示词SFT训练数据模型

定义面向提示词的SFT训练数据生成过程中使用的各种数据结构
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from models.function import Function
from models.macro import Macro
from models.struct import Struct


class ModelType(Enum):
    """支持的模型类型"""
    QWEN_CODER = "qwen_coder"
    DEEPSEEK_CODER = "deepseek_coder"


class ContextType(Enum):
    """上下文类型"""
    VISIBLE = "visible"  # 已显式使用的上下文
    INFERRED = "inferred"  # 推测使用的上下文


@dataclass
class ModelTokenConfig:
    """模型token配置"""
    model_type: ModelType
    start_token: str
    middle_token: str
    end_token: str
    
    @classmethod
    def get_config(cls, model_type: ModelType) -> 'ModelTokenConfig':
        """获取指定模型的token配置"""
        configs = {
            ModelType.QWEN_CODER: cls(
                model_type=ModelType.QWEN_CODER,
                start_token="<|fim_prefix|>",
                middle_token="<|fim_suffix|>",
                end_token="<|fim_middle|>"
            ),
            ModelType.DEEPSEEK_CODER: cls(
                model_type=ModelType.DEEPSEEK_CODER,
                start_token="<｜fim▁begin｜>",
                middle_token="<｜fim▁hole｜>", 
                end_token="<｜fim▁end｜>"
            ),
        }
        return configs[model_type]


@dataclass
class ContextSection:
    """上下文部分"""
    context_type: ContextType
    functions: List[Function] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)  # 变量暂时保持字符串，因为变量定义比较复杂
    macros: List[Macro] = field(default_factory=list)
    structs: List[Struct] = field(default_factory=list)
    
    def to_text(self) -> str:
        """转换为文本格式"""
        sections = []
        
        if self.functions:
            function_names = [func.name for func in self.functions]
            sections.append(f"函数: {', '.join(function_names)}")
        if self.variables:
            sections.append(f"变量: {', '.join(self.variables)}")
        if self.macros:
            macro_names = [macro.name for macro in self.macros]
            sections.append(f"宏: {', '.join(macro_names)}")
        if self.structs:
            struct_names = [struct.name for struct in self.structs]
            sections.append(f"结构体: {', '.join(struct_names)}")
            
        return '\n'.join(sections)


@dataclass
class SimilarContext:
    """相似上下文"""
    function_name: str
    code: str
    similarity_score: float = 0.0
    reason: str = ""
    
    def to_text(self) -> str:
        """转换为文本格式"""
        return f"""函数: {self.function_name}
代码:
{self.code}"""


@dataclass
class PromptTemplate:
    """提示词模板"""
    # 系统角色设定
    system: str = ""
    
    # 任务指令
    instruction: str = ""
    
    # 系统规则
    system_rules: str = ""
    
    # 环境信息
    env: str = ""
    
    # 用户规则
    user_rules: str = ""
    
    # 相关上下文
    related_context: str = ""
    
    # 相似上下文
    similar_context: str = ""
    
    def to_prompt(self, before: str, after: str, token_config: ModelTokenConfig) -> tuple[str, str]:
        """生成分离的instruction和input"""
        instruction_parts = []
        
        # 添加系统部分（不使用XML标签包裹）
        if self.system:
            instruction_parts.append(f"{self.system}")
        
        # 添加指令部分（不使用XML标签包裹）
        if self.instruction:
            instruction_parts.append(f"{self.instruction}")
        
        # 添加系统规则
        if self.system_rules:
            instruction_parts.append(f"<system_rules>\n{self.system_rules}\n</system_rules>")
        
        # 添加环境信息
        if self.env:
            instruction_parts.append(f"<env>\n{self.env}\n</env>")
        
        # 添加相关上下文
        if self.related_context:
            instruction_parts.append(f"<related_context>\n{self.related_context}\n</related_context>")
        
        # 添加相似上下文
        if self.similar_context:
            instruction_parts.append(f"<similar_context>\n{self.similar_context}\n</similar_context>")
        
        # 添加用户规则
        if self.user_rules:
            instruction_parts.append(f"<user_rules>\n{self.user_rules}\n</user_rules>")
        
        # 添加代码补全部分
        instruction_parts.append("## 补全如下代码:")
        
        instruction = "\n\n".join(instruction_parts)
        
        # 根据模型类型选择合适的token格式生成input
        if token_config.model_type == ModelType.QWEN_CODER:
            # Qwen格式: <|fim_prefix|> before <|fim_suffix|> after <|fim_middle|>
            input_text = f"{token_config.start_token}{before}{token_config.middle_token}{after}{token_config.end_token}"
        elif token_config.model_type == ModelType.DEEPSEEK_CODER:
            # DeepSeek格式: REDACTED_SPECIAL_TOKEN before REDACTED_SPECIAL_TOKEN after REDACTED_SPECIAL_TOKEN
            input_text = f"{token_config.start_token}{before}{token_config.middle_token}{after}{token_config.end_token}"
        else:
            # 默认格式
            input_text = f"{token_config.start_token}{before}{token_config.middle_token}{after}{token_config.end_token}"
        
        return instruction, input_text


@dataclass
class PromptSFTData:
    """提示词SFT训练数据"""
    # 核心数据 - 拆解为三元组
    instruction: str  # 指令部分（系统角色+任务指令+规则等）
    input: str        # 输入部分（包含特殊token的代码）
    output: str       # 输出部分（期望的补全内容）
    
    # 元数据
    filepath: str
    function_name: str
    line_number: int
    model_type: ModelType
    
    # 原始数据（用于调试和验证）
    before: str
    expected: str
    after: str
    
    # 上下文信息
    context_sections: Dict[ContextType, ContextSection] = field(default_factory=dict)
    similar_contexts: List[SimilarContext] = field(default_factory=list)
    
    # 模板信息
    template_name: str = ""
    
    # 生成策略信息
    generation_strategy: str = ""
    
    # 质量指标
    complexity_score: float = 0.0
    context_relevance_score: float = 0.0
    
    @property
    def prompt(self) -> str:
        """兼容性属性：返回完整的prompt"""
        return f"{self.instruction}\n\n{self.input}"
    
    @property
    def completion(self) -> str:
        """兼容性属性：返回输出"""
        return self.output
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            # 核心三元组
            "instruction": self.instruction,
            "input": self.input,
            "output": self.output,
            
            # 兼容性字段
            "prompt": self.prompt,
            "completion": self.completion,
            
            # 元数据
            "filepath": self.filepath,
            "function_name": self.function_name,
            "line_number": self.line_number,
            "model_type": self.model_type.value,
            "before": self.before,
            "expected": self.expected,
            "after": self.after,
            "context_sections": {
                ctx_type.value: {
                    "context_type": section.context_type.value,
                    "functions": [func.model_dump() for func in section.functions],
                    "variables": section.variables,
                    "macros": [macro.model_dump() for macro in section.macros],
                    "structs": [struct.model_dump() for struct in section.structs]
                }
                for ctx_type, section in self.context_sections.items()
            },
            "similar_contexts": [
                {
                    "function_name": ctx.function_name,
                    "code": ctx.code,
                    "similarity_score": ctx.similarity_score,
                    "reason": ctx.reason
                }
                for ctx in self.similar_contexts
            ],
            "template_name": self.template_name,
            "generation_strategy": self.generation_strategy,
            "complexity_score": self.complexity_score,
            "context_relevance_score": self.context_relevance_score
        }
    
    def validate(self) -> bool:
        """验证数据质量"""
        if not self.instruction or not self.input or not self.output:
            return False
        
        if len(self.instruction) < 5 or len(self.input) < 1 or len(self.output) < 1:
            return False
        
        # 放宽filepath和function_name的要求，因为可能为空
        if self.line_number < 0:  # 允许0
            return False
        
        return True


@dataclass
class TaskMetadata:
    """任务元数据"""
    filepath: str = ""
    function_name: str = ""
    line_number: int = 0
    source_type: str = ""
    complexity_score: float = 0.0
    context_relevance_score: float = 0.0
    
    @classmethod
    def from_dict(cls, metadata_dict: Dict[str, Any]) -> 'TaskMetadata':
        """从字典创建TaskMetadata"""
        return cls(
            filepath=metadata_dict.get('filepath', ''),
            function_name=metadata_dict.get('function_name', ''),
            line_number=metadata_dict.get('line_number', 0),
            source_type=metadata_dict.get('source_type', ''),
            complexity_score=metadata_dict.get('complexity_score', 0.0),
            context_relevance_score=metadata_dict.get('context_relevance_score', 0.0)
        )