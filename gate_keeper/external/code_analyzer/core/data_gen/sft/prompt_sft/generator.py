"""
提示词SFT数据生成器

实现面向提示词的SFT训练数据生成功能
"""

import os
import random
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from tqdm import tqdm

from models.function import Function
from models.macro import Macro
from models.struct import Struct

from ..base_generator import BaseSFTGenerator
from ..base_models import ContextNode, EnhancedStatementMaskData
from ..extract_strategies import ExtractionStrategy
from .models import (ContextSection, ContextType, ModelTokenConfig, ModelType,
                     PromptSFTData, PromptTemplate, SimilarContext,
                     TaskMetadata)
from .tasks import BasePromptTask, CodeCompletionTask


class PromptSFTGenerator:
    """提示词SFT数据生成器"""
    
    def __init__(self, sft_generator: BaseSFTGenerator, 
                 max_hidden_variables: int = 10,
                 variable_selection_strategy: str = "priority_based"):
        """
        初始化提示词SFT数据生成器
        
        Args:
            sft_generator: SFT数据生成器
            max_hidden_variables: 最大隐藏变量数量，默认10个
            variable_selection_strategy: 变量选择策略
                - "priority_based": 基于优先级选择（推荐）
                - "random": 随机选择
                - "first_n": 选择前N个
        """
        self.sft_generator = sft_generator
        self.max_hidden_variables = max_hidden_variables
        self.variable_selection_strategy = variable_selection_strategy
        
        # 初始化任务处理器
        self.task_processors = {}
        self._register_default_tasks()
        
        # 导入random模块用于随机选择
        import random

        # 变量优先级配置（从高到低）
        self.variable_priorities = {
            "function_param": 10,      # 函数参数优先级最高
            "imported": 9,             # 导入的模块/变量
            "global": 8,               # 全局变量
            "class_property": 7,       # 类属性
            "register": 6,             # NP寄存器
            "struct/type_def": 5,      # 结构体/类型定义
            "macro_def": 4,            # 宏定义
            "local": 3,                # 局部变量
            "constant": 2,             # 常量
            "unknown": 1               # 未知变量优先级最低
        }
    
    def _register_default_tasks(self):
        """注册默认任务处理器"""
        from .tasks.code_completion import CodeCompletionTask
        self.task_processors = {
            "code_completion": CodeCompletionTask()
        }
        
        # 配置参数
        self.max_similar_contexts = 2
        self.max_context_length = 1000
        self.min_completion_length = 5
        self.max_completion_length = 500
        
        # 随机种子
        self.seed = 42
        random.seed(self.seed)
    
    def generate_prompt_sft_data(self, 
                                max_samples: int = 10,
                                model_type: ModelType = ModelType.QWEN_CODER,
                                template_name: Optional[str] = None,
                                task_type: str = "code_completion",
                                strategies: List[ExtractionStrategy] = None,
                                strategy_max_samples: Dict[ExtractionStrategy, int] = None) -> List[PromptSFTData]:
        """生成提示词SFT训练数据"""
        if max_samples <= 0:
            raise ValueError("max_samples必须大于0")
        
        # 验证model_type类型
        if not isinstance(model_type, ModelType):
            raise ValueError(f"model_type必须是ModelType类型，当前类型: {type(model_type)}")
        
        # 获取任务处理器
        task_processor = self.task_processors.get(task_type)
        if not task_processor:
            raise ValueError(f"不支持的任务类型: {task_type}")
        
        # 验证模型类型是否被任务支持
        if model_type not in task_processor.supported_models:
            raise ValueError(f"任务 {task_type} 不支持模型类型 {model_type.value}")
        
        print(f"\n=== 生成提示词SFT数据 (最多{max_samples}个样本, 模型: {model_type.value}, 任务: {task_type}) ===")
        
        # 生成基础SFT数据
        if strategies:
            # 使用多策略生成
            enhanced_data = self.sft_generator.generate_enhanced_sft_data_multi_strategy(
                max_samples=max_samples * 2,
                strategies=strategies,
                strategy_max_samples=strategy_max_samples
            )
            
            # 随机打乱数据，确保各策略的数据都有机会被选中
            import random
            random.shuffle(enhanced_data)
        else:
            # 使用单策略生成（向后兼容）
            enhanced_data = self.sft_generator.generate_enhanced_sft_data(max_samples * 2)
        
        prompt_sft_data = []
        
        with tqdm(total=len(enhanced_data), desc="生成提示词SFT数据", unit="样本") as pbar:
            for sft_item in enhanced_data:
                try:
                    # 创建TaskMetadata对象
                    task_metadata = TaskMetadata.from_dict(sft_item.metadata)
                    
                    # 验证元数据是否适合当前任务
                    if not task_processor.validate_metadata(task_metadata):
                        continue
                    
                    # 使用任务处理器构建模板
                    template = task_processor.get_template(model_type)
                    
                    # 构建上下文信息
                    context_sections = self._build_context_sections(sft_item)
                    
                    # 构建相似上下文
                    similar_contexts = self._build_similar_contexts(sft_item)
                    
                    # 使用任务处理器构建环境信息
                    env_info = task_processor.build_env_info(task_metadata)
                    template.env = env_info
                    
                    # 使用任务处理器构建系统规则
                    system_rules = task_processor.build_system_rules(task_metadata)
                    template.system_rules = system_rules
                    
                    # 使用任务处理器构建用户规则
                    user_rules = task_processor.build_user_rules(task_metadata)
                    template.user_rules = user_rules
                    
                    # 使用任务处理器构建指令
                    instruction = task_processor.build_instruction(task_metadata, model_type)
                    template.instruction = instruction
                    
                    # 构建相关上下文文本
                    related_context_text = self._build_related_context_text(context_sections, sft_item, 'completion')
                    template.related_context = related_context_text
                    
                    # 构建相似上下文文本
                    similar_context_text = self._build_similar_context_text(similar_contexts)
                    template.similar_context = similar_context_text
                    
                    # 生成分离的instruction和input
                    token_config = task_processor.get_token_config(model_type)
                    instruction, input_text = template.to_prompt(sft_item.before, sft_item.after, token_config)
                    
                    # 创建PromptSFTData
                    prompt_sft_item = PromptSFTData(
                        instruction=instruction,
                        input=input_text,
                        output=sft_item.expected,
                        filepath=sft_item.metadata.get('filepath', ''),
                        function_name=sft_item.metadata.get('function_name', ''),
                        line_number=sft_item.metadata.get('line_number', 0),
                        model_type=model_type,
                        before=sft_item.before,
                        expected=sft_item.expected,
                        after=sft_item.after,
                        context_sections=context_sections,
                        similar_contexts=similar_contexts,
                        template_name=task_type,
                        generation_strategy=sft_item.metadata.get('strategy', ''),
                        complexity_score=sft_item.metadata.get('complexity_score', 0.0),
                        context_relevance_score=self._calculate_context_relevance(context_sections, sft_item)
                    )
                    
                    # 验证数据质量
                    if prompt_sft_item.validate():
                        prompt_sft_data.append(prompt_sft_item)
                    else:
                        print(f"    验证失败: instruction长度={len(prompt_sft_item.instruction)}, input长度={len(prompt_sft_item.input)}, output长度={len(prompt_sft_item.output)}")
                    
                    if len(prompt_sft_data) >= max_samples:
                        break
                        
                except Exception as e:
                    print(f"    生成提示词SFT数据时出错: {e}")
                    continue
                finally:
                    pbar.update(1)
        
        print(f"  生成了 {len(prompt_sft_data)} 个提示词SFT数据样本")
        return prompt_sft_data
    
    def add_task(self, task_name: str, task_processor: BasePromptTask):
        """添加新任务"""
        self.task_processors[task_name] = task_processor
    
    def get_supported_tasks(self) -> List[str]:
        """获取支持的任务列表"""
        return list(self.task_processors.keys())
    
    def get_task_processor(self, task_name: str):
        """获取任务处理器"""
        return self.task_processors.get(task_name)
    

    
    def _build_context_sections(self, sft_item: EnhancedStatementMaskData) -> Dict[ContextType, ContextSection]:
        """构建上下文部分"""
        context_sections = {}
        
        # 分析已使用的上下文
        visible_context = self._extract_visible_context(sft_item)
        context_sections[ContextType.VISIBLE] = visible_context
        
        # 分析推测使用的上下文
        inferred_context = self._extract_inferred_context(sft_item)
        context_sections[ContextType.INFERRED] = inferred_context
        
        return context_sections
    
    def _extract_visible_context(self, sft_item: EnhancedStatementMaskData) -> ContextSection:
        """提取已显式使用的上下文"""
        visible_context = ContextSection(context_type=ContextType.VISIBLE)
        
        # 获取文件路径用于tree-sitter解析
        file_path = sft_item.metadata.get('filepath', '')
        
        # 读取完整文件内容用于tree-sitter解析
        full_file_content = ""
        if file_path and os.path.exists(file_path):
            try:
                from utils.file_utils import read_file_safe
                full_file_content = read_file_safe(file_path)
            except Exception as e:
                print(f"    读取文件失败: {e}")
                # 如果读取失败，回退到使用代码片段
                full_file_content = sft_item.before + sft_item.after
        
        # 提取函数调用并获取完整定义
        function_names = self._extract_function_calls(full_file_content, file_path)
        visible_context.functions = self._get_function_definitions(function_names, file_path)
        
        # 提取变量引用
        variables = self._extract_variables(full_file_content, file_path)
        visible_context.variables = variables
        
        # 提取宏使用并获取完整定义
        macro_names = self._extract_macros(full_file_content, file_path)
        visible_context.macros = self._get_macro_definitions(macro_names, file_path)
        
        # 提取结构体引用并获取完整定义
        struct_names = self._extract_structs(full_file_content, file_path)
        visible_context.structs = self._get_struct_definitions(struct_names, file_path)
        
        return visible_context
    
    def _extract_inferred_context(self, sft_item: EnhancedStatementMaskData) -> ContextSection:
        """提取推测使用的上下文"""
        inferred_context = ContextSection(context_type=ContextType.INFERRED)
        
        # 从expected中提取可能需要的实体（expected本身就是代码片段）
        expected_text = sft_item.expected
        
        # 获取文件路径用于tree-sitter解析
        file_path = sft_item.metadata.get('filepath', '')
        
        # 对于expected，直接传入代码片段，因为它是用户期望补全的内容
        # 提取函数调用并获取完整定义
        function_names = self._extract_function_calls(expected_text, file_path)
        inferred_context.functions = self._get_function_definitions(function_names, file_path)
        
        # 提取变量引用
        variables = self._extract_variables(expected_text, file_path)
        inferred_context.variables = variables
        
        # 提取宏使用并获取完整定义
        macro_names = self._extract_macros(expected_text, file_path)
        inferred_context.macros = self._get_macro_definitions(macro_names, file_path)
        
        # 提取结构体引用并获取完整定义
        struct_names = self._extract_structs(expected_text, file_path)
        inferred_context.structs = self._get_struct_definitions(struct_names, file_path)
        
        return inferred_context
    
    def _extract_function_calls(self, text: str, file_path: str = "") -> List[str]:
        """提取函数调用"""
        from ..utils import SFTUtils
        return SFTUtils.extract_function_calls(text, file_path)
    
    def _extract_variables(self, text: str, file_path: str = "") -> List[str]:
        """提取变量引用"""
        from ..utils import SFTUtils
        return SFTUtils.extract_variables(text, file_path)
    
    def _extract_macros(self, text: str, file_path: str = "") -> List[str]:
        """提取宏使用"""
        from ..utils import SFTUtils
        return SFTUtils.extract_macros(text, file_path)
    
    def _extract_structs(self, text: str, file_path: str = "") -> List[str]:
        """提取结构体引用"""
        from ..utils import SFTUtils
        return SFTUtils.extract_structs(text, file_path)
    
    def _build_similar_contexts(self, sft_item: EnhancedStatementMaskData) -> List[SimilarContext]:
        """构建相似上下文"""
        similar_contexts = []
        
        # 这里可以实现更复杂的相似性搜索
        # 目前使用简单的启发式方法
        
        # 从同一文件中查找相似函数
        filepath = sft_item.metadata.get('filepath', '')
        if filepath and filepath in self.sft_generator.analyzer._functions_cache:
            functions = self.sft_generator.analyzer._functions_cache[filepath]
            
            for func in functions:
                if func.name != sft_item.metadata.get('function_name', ''):
                    # 简单的相似性判断
                    if self._is_similar_function(func, sft_item):
                        similar_context = SimilarContext(
                            function_name=func.name,
                            code=self._extract_function_summary(func),
                            similarity_score=0.7,  # 可以改进为更精确的相似度计算
                            reason="同文件相似函数"
                        )
                        similar_contexts.append(similar_context)
                        
                        if len(similar_contexts) >= self.max_similar_contexts:
                            break
        
        return similar_contexts
    
    def _is_similar_function(self, func, sft_item: EnhancedStatementMaskData) -> bool:
        """判断函数是否相似"""
        # 简单的相似性判断，可以根据需要改进
        func_name = func.name.lower()
        expected_text = sft_item.expected.lower()
        
        # 检查函数名是否在expected中出现
        if func_name in expected_text:
            return True
        
        # 检查是否有相似的命名模式
        if any(keyword in func_name for keyword in ['init', 'check', 'validate', 'process']):
            return True
        
        return False
    
    def _extract_function_summary(self, func) -> str:
        """提取函数摘要"""
        # 提取函数的核心逻辑，去除注释等
        lines = func.code.split('\n')
        summary_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('//') and not line.startswith('/*'):
                summary_lines.append(line)
                if len(summary_lines) >= 10:  # 限制行数
                    break
        
        return '\n'.join(summary_lines)
    

    
    def _build_related_context_text(self, context_sections: Dict[ContextType, ContextSection], 
                                  sft_item: EnhancedStatementMaskData, mode: str = 'completion') -> str:
        """
        构建相关上下文文本（优化版）
        
        Args:
            context_sections: 上下文部分
            sft_item: SFT数据项
            mode: 模式，'completion' 或 'check'
        """
        text_parts = []
        text_parts.append("### Related Context ###")
        text_parts.append("")
        
        # Current Function
        current_func = self._extract_current_function(sft_item)
        if current_func:
            text_parts.append("# Current Function:")
            text_parts.append(current_func)
            text_parts.append("")
        
        # Enclosing Class (可选)
        enclosing_class = self._extract_enclosing_class(sft_item)
        if enclosing_class:
            text_parts.append("# Enclosing Class:")
            text_parts.append(enclosing_class)
            text_parts.append("")
        
        # Called By
        called_by = self._extract_called_by(sft_item, mode)
        if called_by:
            text_parts.append("# Called By:")
            text_parts.append(called_by)
            text_parts.append("")
        
        # Calls
        calls = self._extract_calls(sft_item, mode)
        if calls:
            text_parts.append("# Calls:")
            text_parts.append(calls)
            text_parts.append("")
        
        # Hidden Variables Model Might Need
        hidden_vars = self._extract_hidden_variables(sft_item, context_sections)
        if hidden_vars:
            text_parts.append("# Hidden Variables Model Might Need:")
            text_parts.append(hidden_vars)
            text_parts.append("")
        
        return '\n'.join(text_parts)
    
    def _build_similar_context_text(self, similar_contexts: List[SimilarContext]) -> str:
        """构建相似上下文文本"""
        if not similar_contexts:
            return ""
        
        text_parts = []
        for ctx in similar_contexts:
            text_parts.append(ctx.to_text())
        
        return '\n\n'.join(text_parts)
    
    def _calculate_context_relevance(self, context_sections: Dict[ContextType, ContextSection], 
                                   sft_item: EnhancedStatementMaskData) -> float:
        """计算上下文相关性分数"""
        # 简单的相关性计算，可以根据需要改进
        total_entities = 0
        relevant_entities = 0
        
        for section in context_sections.values():
            total_entities += len(section.functions) + len(section.variables) + len(section.macros) + len(section.structs)
            
            # 检查实体是否在expected中出现
            expected_text = sft_item.expected.lower()
            for func in section.functions:
                if func.name.lower() in expected_text:
                    relevant_entities += 1
            for var in section.variables:
                if var.lower() in expected_text:
                    relevant_entities += 1
            for macro in section.macros:
                if macro.name.lower() in expected_text:
                    relevant_entities += 1
            for struct in section.structs:
                if struct.name.lower() in expected_text:
                    relevant_entities += 1
        
        if total_entities == 0:
            return 0.0
        
        return relevant_entities / total_entities
    
    def save_to_json(self, prompt_sft_data: List[PromptSFTData], output_file: str, 
                    max_items_per_file: int = 10000):
        """保存数据到JSON文件（支持分文件存储）"""
        from pathlib import Path

        from ..utils import SFTDataSaver

        # 创建数据保存器
        output_dir = Path(output_file).parent
        filename_prefix = Path(output_file).stem
        
        saver = SFTDataSaver(
            max_items_per_file=max_items_per_file,
            output_dir=str(output_dir),
            data_type="prompt_sft"
        )
        
        # 保存数据
        saved_files = saver.save_sft_data(prompt_sft_data)
        
        print(f"  数据已保存到: {len(saved_files)} 个文件")
        for file_path in saved_files:
            print(f"    - {file_path}")
        
        return saved_files
    
    def save_to_jsonl(self, prompt_sft_data: List[PromptSFTData], output_file: str, 
                     max_items_per_file: int = 10000):
        """保存数据到JSONL文件（支持分文件存储）"""
        from pathlib import Path

        from ..utils import SFTDataSaver

        # 创建数据保存器
        output_dir = Path(output_file).parent
        filename_prefix = Path(output_file).stem
        
        saver = SFTDataSaver(
            max_items_per_file=max_items_per_file,
            output_dir=str(output_dir),
            data_type="prompt_sft"
        )
        
        # 保存数据为JSONL格式
        saved_files = saver.save_jsonl_data(prompt_sft_data, lambda item: item.to_dict())
        
        print(f"  数据已保存到: {len(saved_files)} 个JSONL文件")
        for file_path in saved_files:
            print(f"    - {file_path}")
        
        return saved_files
    
    def create_streaming_saver(self, output_file: str, 
                             max_items_per_file: int = 10000,
                             file_format: str = "jsonl") -> "StreamingSFTDataSaver":
        """创建流式数据保存器
        
        Args:
            output_file: 输出文件路径
            max_items_per_file: 每个文件最大条数，默认10000
            file_format: 文件格式，"json" 或 "jsonl"
            
        Returns:
            流式数据保存器实例
        """
        from ..utils import StreamingSFTDataSaver
        
        return StreamingSFTDataSaver(
            output_file=output_file,
            max_items_per_file=max_items_per_file,
            file_format=file_format,
            data_type="prompt_sft"
        )
    
    def get_statistics(self, prompt_sft_data: List[PromptSFTData]) -> Dict[str, Any]:
        """获取数据统计信息"""
        stats = {
            "total_samples": len(prompt_sft_data) if prompt_sft_data else 0,
            "model_types": {},
            "template_names": {},
            "generation_strategies": {},
            "avg_instruction_length": 0,
            "avg_input_length": 0,
            "avg_output_length": 0,
            "avg_complexity_score": 0,
            "avg_context_relevance_score": 0
        }
        
        if not prompt_sft_data:
            return stats
        
        total_instruction_length = 0
        total_input_length = 0
        total_output_length = 0
        total_complexity_score = 0
        total_context_relevance_score = 0
        
        for item in prompt_sft_data:
            # 统计模型类型
            model_type = item.model_type.value
            stats["model_types"][model_type] = stats["model_types"].get(model_type, 0) + 1
            
            # 统计模板名称
            template_name = item.template_name
            stats["template_names"][template_name] = stats["template_names"].get(template_name, 0) + 1
            
            # 统计生成策略
            strategy = item.generation_strategy
            if strategy:
                stats["generation_strategies"][strategy] = stats["generation_strategies"].get(strategy, 0) + 1
            
            # 累计长度和分数
            total_instruction_length += len(item.instruction)
            total_input_length += len(item.input)
            total_output_length += len(item.output)
            total_complexity_score += item.complexity_score
            total_context_relevance_score += item.context_relevance_score
        
        # 计算平均值
        stats["avg_instruction_length"] = total_instruction_length / len(prompt_sft_data)
        stats["avg_input_length"] = total_input_length / len(prompt_sft_data)
        stats["avg_output_length"] = total_output_length / len(prompt_sft_data)
        stats["avg_complexity_score"] = total_complexity_score / len(prompt_sft_data)
        stats["avg_context_relevance_score"] = total_context_relevance_score / len(prompt_sft_data)
        
        return stats
    
    def _get_function_definitions(self, function_names: List[str], file_path: str) -> List[Function]:
        """从代码图中获取函数定义"""
        functions = []
        
        # 获取分析器的仓库索引
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        if not repo_index:
            return functions
        
        for func_name in function_names:
            # 在仓库索引中查找函数定义
            func_def = repo_index.find_function_definition(func_name, file_path)
            if func_def:
                functions.append(func_def)
        
        return functions
    
    def _get_macro_definitions(self, macro_names: List[str], file_path: str) -> List[Macro]:
        """从代码图中获取宏定义"""
        macros = []
        
        # 获取分析器的仓库索引
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        if not repo_index:
            return macros
        
        # 从仓库索引的宏定义列表中查找
        for macro_name in macro_names:
            for macro in repo_index.macro_definitions:
                if macro.name == macro_name:
                    macros.append(macro)
                    break
        
        return macros
    
    def _get_struct_definitions(self, struct_names: List[str], file_path: str) -> List[Struct]:
        """从代码图中获取结构体定义"""
        structs = []
        
        # 获取分析器的仓库索引
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        if not repo_index:
            return structs
        
        # 从仓库索引的结构体定义列表中查找
        # 注意：这里需要根据实际的代码图结构来获取结构体定义
        # 暂时返回空列表，因为结构体定义的获取方式需要进一步确认
        return structs

    def _extract_current_function(self, sft_item: EnhancedStatementMaskData) -> str:
        """提取当前函数信息"""
        function_name = sft_item.metadata.get("function_name", "")
        if not function_name:
            return ""
        
        # 尝试从代码图中获取函数定义
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        if repo_index:
            filepath = sft_item.metadata.get("filepath", "")
            func_def = repo_index.find_function_definition(function_name, filepath)
            if func_def and func_def.signature:
                return f"{func_def.signature.name}({', '.join([p.name for p in func_def.signature.parameters])})"
        
        # 如果无法获取完整签名，返回函数名
        return f"{function_name}(...)"
    
    def _extract_enclosing_class(self, sft_item: EnhancedStatementMaskData) -> str:
        """提取所属类信息（可选）"""
        # 暂时返回空，因为需要分析代码结构来确定类关系
        # 可以通过分析文件内容或AST来提取类信息
        return ""
    
    def _extract_called_by(self, sft_item: EnhancedStatementMaskData, mode: str = 'completion') -> str:
        """提取调用当前函数的函数信息"""
        function_name = sft_item.metadata.get("function_name", "")
        if not function_name:
            return ""
        
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        if not repo_index:
            return ""
        
        # 查找调用当前函数的函数
        callers = []
        for call in repo_index.function_calls:
            if call.callee == function_name:
                caller_func = repo_index.find_function_definition(call.caller, call.file_path)
                if caller_func:
                    if mode == 'completion':
                        # 代码补全模式：只提供函数签名和调用关系
                        callers.append(f"{call.file_path}:{call.line[0]} -> {caller_func.signature.name}(...)")
                    else:
                        # 代码检查模式：可包含更多上下文
                        callers.append(f"{call.file_path}:{call.line[0]} -> {caller_func.signature.name}(...)")
        
        return '\n'.join(callers) if callers else ""
    
    def _extract_calls(self, sft_item: EnhancedStatementMaskData, mode: str = 'completion') -> str:
        """提取当前函数调用的函数信息"""
        function_name = sft_item.metadata.get("function_name", "")
        if not function_name:
            return ""
        
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        if not repo_index:
            return ""
        
        # 查找当前函数调用的函数
        callees = []
        for call in repo_index.function_calls:
            if call.caller == function_name:
                callee_func = repo_index.find_function_definition(call.callee, call.file_path)
                if callee_func:
                    if mode == 'completion':
                        # 代码补全模式：只提供函数签名
                        callees.append(f"{call.file_path}:{call.line[0]} -> {callee_func.signature.name}(...)")
                    else:
                        # 代码检查模式：可包含更多上下文
                        callees.append(f"{call.file_path}:{call.line[0]} -> {callee_func.signature.name}(...)")
        
        return '\n'.join(callees) if callees else ""
    
    def _extract_hidden_variables(self, sft_item: EnhancedStatementMaskData, 
                                context_sections: Dict[ContextType, ContextSection]) -> str:
        """提取续写区域不可见但关键变量（智能选择）"""
        hidden_vars = []
        
        # 获取当前函数的参数
        function_name = sft_item.metadata.get("function_name", "")
        filepath = sft_item.metadata.get("filepath", "")
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        
        if function_name and repo_index:
            func_def = repo_index.find_function_definition(function_name, filepath)
            if func_def and func_def.signature:
                for param in func_def.signature.parameters:
                    # 检查参数是否在before中已经出现
                    if not self._is_variable_visible_in_context(param.name, sft_item):
                        param_type = param.type_hint if param.type_hint else "unknown"
                        # 只提取顶级变量，不展开属性路径
                        top_level_var = self._extract_top_level_variable(param.name)
                        if top_level_var:
                            hidden_vars.append(f"- {top_level_var}: function_param, type={param_type}")
        
        # 从上下文部分提取隐藏变量
        for ctx_type, section in context_sections.items():
            for var in section.variables:
                # 检查变量是否在before中已经出现
                if not self._is_variable_visible_in_context(var, sft_item):
                    # 只提取顶级变量，避免变量爆炸
                    top_level_var = self._extract_top_level_variable(var)
                    if top_level_var:
                        # 分析变量来源和类型
                        var_source, var_type, var_location = self._analyze_variable_source_enhanced(
                            top_level_var, sft_item, repo_index
                        )
                        # 构建变量信息字符串
                        var_info = f"- {top_level_var}: {var_source}, type={var_type}"
                        if var_location:
                            var_info += f" (from {var_location})"
                        hidden_vars.append(var_info)
        
        # 根据优先级和数量限制选择隐藏变量
        if len(hidden_vars) > self.max_hidden_variables:
            if self.variable_selection_strategy == "priority_based":
                # 按优先级排序
                def get_priority(var_info):
                    # 从变量信息中提取变量来源类型
                    # 格式: "- var_name: source_type, type=type_info (from location)"
                    try:
                        source_part = var_info.split(':')[1].split(',')[0].strip()
                        return self.variable_priorities.get(source_part, 0)
                    except:
                        return 0
                
                hidden_vars.sort(key=get_priority, reverse=True)
                hidden_vars = hidden_vars[:self.max_hidden_variables]
            elif self.variable_selection_strategy == "random":
                import random
                random.shuffle(hidden_vars)
                hidden_vars = hidden_vars[:self.max_hidden_variables]
            elif self.variable_selection_strategy == "first_n":
                hidden_vars = hidden_vars[:self.max_hidden_variables]
        
        return '\n'.join(hidden_vars) if hidden_vars else ""
    
    def _is_variable_visible_in_context(self, var_name: str, sft_item: EnhancedStatementMaskData) -> bool:
        """
        判断变量在当前提示词上下文中是否可见
        
        Args:
            var_name: 变量名
            sft_item: SFT数据项
            
        Returns:
            bool: True表示变量可见，False表示变量不可见
        """
        if not var_name:
            return False
        
        # 1. 检查变量是否在before文本中直接出现
        if var_name in sft_item.before:
            return True
        
        # 2. 检查变量的顶级标识符是否在before中
        top_level_var = self._extract_top_level_variable(var_name)
        if top_level_var and top_level_var in sft_item.before:
            return True
        
        # 3. 检查变量是否在函数签名中声明（函数参数）
        function_name = sft_item.metadata.get("function_name", "")
        filepath = sft_item.metadata.get("filepath", "")
        repo_index = getattr(self.sft_generator.analyzer, 'repo_index', None)
        
        if function_name and repo_index:
            func_def = repo_index.find_function_definition(function_name, filepath)
            if func_def and func_def.signature:
                # 检查是否为函数参数
                for param in func_def.signature.parameters:
                    if param.name == var_name or param.name == top_level_var:
                        # 函数参数在函数作用域内始终可见
                        return True
        
        # 4. 检查变量是否在before文本中的变量声明部分
        if self._is_variable_declared_in_before(var_name, sft_item.before):
            return True
        
        # 5. 检查变量是否为全局变量或常量（在文件级别可见）
        if self._is_global_or_constant_variable(var_name, sft_item):
            return True
        
        # 6. 检查变量是否在import语句中
        if self._is_imported_variable(var_name, sft_item.before):
            return True
        
        # 7. 检查变量是否为NP寄存器（在NP语言中全局可见）
        if self._is_np_register_variable(var_name):
            return True
        
        # 8. 检查变量是否在结构体或类型定义中
        if self._is_variable_in_struct_or_type_def(var_name, sft_item.before):
            return True
        
        # 9. 检查变量是否在宏定义中
        if self._is_variable_in_macro_def(var_name, sft_item.before):
            return True
        
        return False
    
    def _is_np_register_variable(self, var_name: str) -> bool:
        """
        检查变量是否为NP寄存器变量
        
        Args:
            var_name: 变量名
            
        Returns:
            bool: True表示是NP寄存器，False表示不是
        """
        # NP寄存器命名模式
        np_register_patterns = [
            # 通用寄存器
            r'^r[0-9]+$',  # r0, r1, r2, ...
            r'^x[0-9]+$',  # x0, x1, x2, ...
            r'^v[0-9]+$',  # v0, v1, v2, ...
            
            # 特殊寄存器
            r'^sp$',       # 栈指针
            r'^fp$',       # 帧指针
            r'^lr$',       # 链接寄存器
            r'^pc$',       # 程序计数器
            
            # 条件码寄存器
            r'^cc[0-9]+$', # cc0, cc1, cc2, ...
            
            # 长字寄存器
            r'^LW[0-9]+$', # LW0, LW1, LW2, ...
            r'^lw[0-9]+$', # lw0, lw1, lw2, ...
            
            # 字节寄存器
            r'^rb[A-Za-z][A-Za-z0-9]*$',  # rb开头
            r'^rw[A-Za-z][A-Za-z0-9]*$',  # rw开头
            r'^rh[A-Za-z][A-Za-z0-9]*$',  # rh开头
            
            # 特殊寄存器
            r'^g_[A-Za-z][A-Za-z0-9_]*$', # g_开头（全局寄存器）
            r'^rs[A-Za-z][A-Za-z0-9_]*$', # rs开头（寄存器结构）
        ]
        
        import re
        for pattern in np_register_patterns:
            if re.match(pattern, var_name):
                return True
        
        return False
    
    def _is_variable_in_struct_or_type_def(self, var_name: str, before_text: str) -> bool:
        """
        检查变量是否在结构体或类型定义中
        
        Args:
            var_name: 变量名
            before_text: before文本
            
        Returns:
            bool: True表示在结构体或类型定义中，False表示不在
        """
        if not var_name or not before_text:
            return False
        
        # 检查是否在结构体定义中
        struct_patterns = [
            f'struct {var_name}',
            f'typedef struct {var_name}',
            f'class {var_name}',
            f'enum {var_name}',
            f'union {var_name}',
            f'({var_name})',  # 类型转换
            f'({var_name}_S)',  # NP语言结构体后缀
            f'({var_name}_T)',  # NP语言类型后缀
        ]
        
        for pattern in struct_patterns:
            if pattern in before_text:
                return True
        
        # 检查是否在结构体成员中
        lines = before_text.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为结构体成员声明
            # 例如: (TYPE) var_name;
            if f'({var_name})' in line or f'({var_name}_S)' in line:
                return True
            
            # 检查是否为结构体成员访问
            # 例如: struct.var_name
            if f'.{var_name}' in line:
                return True
        
        return False
    
    def _is_variable_in_macro_def(self, var_name: str, before_text: str) -> bool:
        """
        检查变量是否在宏定义中
        
        Args:
            var_name: 变量名
            before_text: before文本
            
        Returns:
            bool: True表示在宏定义中，False表示不在
        """
        if not var_name or not before_text:
            return False
        
        # 检查是否在宏定义中
        macro_patterns = [
            f'#define {var_name}',
            f'#define {var_name}_',
            f'#define {var_name.upper()}',
            f'#define {var_name.upper()}_',
        ]
        
        for pattern in macro_patterns:
            if pattern in before_text:
                return True
        
        return False
    
    def _is_variable_declared_in_before(self, var_name: str, before_text: str) -> bool:
        """
        检查变量是否在before文本中的变量声明部分
        
        Args:
            var_name: 变量名
            before_text: before文本
            
        Returns:
            bool: True表示变量在声明部分，False表示不在
        """
        if not var_name or not before_text:
            return False
        
        # 分割before文本为行
        lines = before_text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为变量声明行
            # 常见的变量声明模式：
            # - (TYPE) var_name;
            # - TYPE var_name;
            # - var_name = value;
            # - var_name: TYPE;
            
            # 移除注释
            if '//' in line:
                line = line.split('//')[0].strip()
            if '/*' in line and '*/' in line:
                # 简单的多行注释处理
                line = line.replace('/*', '').replace('*/', '').strip()
            
            # 检查变量名是否在声明行中
            if var_name in line:
                # 进一步检查是否为真正的声明（不是使用）
                # 检查是否包含声明关键字或模式
                declaration_patterns = [
                    f'({var_name}',  # (var_name
                    f' {var_name}',  # 空格+变量名
                    f'{var_name} ',  # 变量名+空格
                    f'{var_name};',  # 变量名+分号
                    f'{var_name}='   # 变量名+等号
                ]
                
                for pattern in declaration_patterns:
                    if pattern in line:
                        return True
        
        return False
    
    def _is_global_or_constant_variable(self, var_name: str, sft_item: EnhancedStatementMaskData) -> bool:
        """
        检查变量是否为全局变量或常量
        
        Args:
            var_name: 变量名
            sft_item: SFT数据项
            
        Returns:
            bool: True表示是全局变量或常量，False表示不是
        """
        # 检查是否为常量（全大写）
        if var_name.isupper():
            return True
        
        # 检查是否为全局变量（以g_开头）
        if var_name.startswith('g_'):
            return True
        
        # 检查是否为全局变量（以Global开头）
        if var_name.startswith('Global'):
            return True
        
        # 检查是否为全局变量（以_global结尾）
        if var_name.endswith('_global') or var_name.endswith('_Global'):
            return True
        
        # 检查是否为全局变量（以_Global结尾）
        if var_name.endswith('_Global'):
            return True
        
        return False
    
    def _is_imported_variable(self, var_name: str, before_text: str) -> bool:
        """
        检查变量是否在import语句中
        
        Args:
            var_name: 变量名
            before_text: before文本
            
        Returns:
            bool: True表示是导入的变量，False表示不是
        """
        if not var_name or not before_text:
            return False
        
        # 检查是否在import语句中
        import_patterns = [
            f'import {var_name}',
            f'#include "{var_name}',
            f'#include <{var_name}',
            f'from {var_name} import',
            f'using {var_name}'
        ]
        
        for pattern in import_patterns:
            if pattern in before_text:
                return True
        
        # 检查是否为常见的模块名
        common_modules = [
            'stdio', 'stdlib', 'string', 'vector', 'map', 'set',
            'iostream', 'fstream', 'sstream', 'algorithm', 'memory',
            'thread', 'mutex', 'chrono', 'functional', 'tuple',
            'array', 'deque', 'list', 'queue', 'stack', 'priority_queue'
        ]
        
        if var_name in common_modules:
            return True
        
        return False
    
    def _extract_top_level_variable(self, var_name: str) -> str:
        """提取顶级变量标识符，避免变量爆炸"""
        if not var_name:
            return ""
        
        # 移除属性访问，只保留顶级变量
        # 例如：a.b.c -> a, self.data.cache -> self.data, config.items -> config
        parts = var_name.split('.')
        
        # 特殊处理：保留self.前缀的类属性
        if len(parts) >= 2 and parts[0] == 'self':
            # self.data.cache -> self.data
            return '.'.join(parts[:2])
        
        # 其他情况：只保留第一部分
        return parts[0]
    
    def _analyze_variable_source_enhanced(self, var_name: str, sft_item: EnhancedStatementMaskData, repo_index) -> tuple[str, str, str]:
        """增强的变量来源分析，包含位置信息"""
        function_name = sft_item.metadata.get("function_name", "")
        filepath = sft_item.metadata.get("filepath", "")
        
        # 1. 检查是否为函数参数
        if function_name and repo_index:
            func_def = repo_index.find_function_definition(function_name, filepath)
            if func_def and func_def.signature:
                for param in func_def.signature.parameters:
                    if param.name == var_name:
                        param_type = param.type_hint if param.type_hint else "unknown"
                        return "function_param", param_type, f"{filepath}:{func_def.line_number}"
        
        # 2. 检查是否为导入的模块/变量
        if self._is_likely_imported_variable(var_name, sft_item):
            return "imported", "module", "import statement"
        
        # 3. 检查是否为全局变量
        if self._is_likely_global_variable(var_name):
            return "global", "unknown", "global scope"
        
        # 4. 检查是否为类属性
        if self._is_likely_class_property(var_name):
            return "class_property", "unknown", "class definition"
        
        # 5. 检查是否为局部变量
        if self._is_likely_local_variable(var_name):
            return "local", "unknown", "function scope"
        
        # 6. 检查是否为常量/宏定义
        if self._is_likely_constant(var_name):
            return "constant", "unknown", "constant definition"
        
        # 7. 检查是否为寄存器（NP语言特有）
        if self._is_likely_register(var_name):
            return "register", "unknown", "NP register"
        
        # 8. 检查是否为NP寄存器
        if self._is_np_register_variable(var_name):
            return "register", "unknown", "NP register"
        
        # 9. 检查是否在结构体或类型定义中
        if self._is_variable_in_struct_or_type_def(var_name, sft_item.before):
            return "struct/type_def", "unknown", "struct/type definition"
        
        # 10. 检查是否在宏定义中
        if self._is_variable_in_macro_def(var_name, sft_item.before):
            return "macro_def", "unknown", "macro definition"
        
        # 默认情况
        return "unknown", "unknown", "unknown"
    
    def _is_likely_imported_variable(self, var_name: str, sft_item: EnhancedStatementMaskData) -> bool:
        """判断是否为导入的变量/模块"""
        # 常见的导入模块名称
        common_modules = [
            'np', 'numpy', 'pd', 'pandas', 'plt', 'matplotlib', 'tf', 'tensorflow',
            'torch', 'sklearn', 'os', 'sys', 're', 'json', 'time', 'datetime',
            'random', 'math', 'collections', 'itertools', 'functools'
        ]
        
        # 检查是否为常见模块名
        if var_name in common_modules:
            return True
        
        # 检查是否在before中包含import语句
        before_text = sft_item.before.lower()
        import_patterns = [
            f'import {var_name}',
            f'from {var_name} import',
            f'#include.*{var_name}',
            f'using {var_name}'
        ]
        
        return any(pattern in before_text for pattern in import_patterns)
    
    def _is_likely_global_variable(self, var_name: str) -> bool:
        """判断是否为全局变量（增强版）"""
        # 全局变量通常有特定前缀或命名约定
        global_prefixes = ['g_', 'global_', 'G_', 'Global']
        global_suffixes = ['_global', '_Global']
        
        # 检查前缀
        if any(var_name.startswith(prefix) for prefix in global_prefixes):
            return True
        
        # 检查后缀
        if any(var_name.endswith(suffix) for suffix in global_suffixes):
            return True
        
        # 检查是否为全大写（全局常量）
        if var_name.isupper() and '_' in var_name:
            return True
        
        return False
    
    def _is_likely_class_property(self, var_name: str) -> bool:
        """判断是否为类属性（增强版）"""
        # 类属性通常有特定前缀
        class_prefixes = ['self.', 'this.', 'm_', 'member_', 'p_', 'private_']
        class_suffixes = ['_member', '_property', '_attr']
        
        # 检查前缀
        if any(var_name.startswith(prefix) for prefix in class_prefixes):
            return True
        
        # 检查后缀
        if any(var_name.endswith(suffix) for suffix in class_suffixes):
            return True
        
        return False
    
    def _is_likely_local_variable(self, var_name: str) -> bool:
        """判断是否为局部变量（增强版）"""
        # 局部变量通常有特定命名模式
        local_patterns = ['temp', 'tmp', 'local', 'var', 'i', 'j', 'k', 'idx', 'count', 'sum', 'result']
        local_suffixes = ['_tmp', '_temp', '_local', '_var']
        
        # 检查是否包含局部变量模式
        if any(pattern in var_name.lower() for pattern in local_patterns):
            return True
        
        # 检查后缀
        if any(var_name.endswith(suffix) for suffix in local_suffixes):
            return True
        
        # 检查是否为简单的单字母或短变量名
        if len(var_name) <= 3 and var_name.islower():
            return True
        
        return False
    
    def _is_likely_constant(self, var_name: str) -> bool:
        """判断是否为常量（增强版）"""
        # 常量通常全大写
        if var_name.isupper() and '_' in var_name:
            return True
        
        # 检查是否为数字常量
        if var_name.isdigit():
            return True
        
        # 检查是否为特殊常量
        special_constants = ['true', 'false', 'null', 'none', 'undefined']
        if var_name.lower() in special_constants:
            return True
        
        return False
    
    def _is_likely_register(self, var_name: str) -> bool:
        """判断是否为寄存器（NP语言特有，扩展版）"""
        # NP语言的寄存器命名约定（扩展）
        register_patterns = [
            'cc', 'rb', 'rw', 'rh', 'q', 'rs', 'ps', 'qs', 'g_',  # 原有模式
            'LW', 'lw',  # 内存位置
            'r0', 'r1', 'r2', 'r3', 'r4', 'r5', 'r6', 'r7', 'r8', 'r9',  # 通用寄存器
            'r10', 'r11', 'r12', 'r13', 'r14', 'r15',
            'sp', 'fp', 'lr', 'pc',  # 特殊寄存器
            'x0', 'x1', 'x2', 'x3', 'x4', 'x5', 'x6', 'x7',  # 扩展寄存器
            'x8', 'x9', 'x10', 'x11', 'x12', 'x13', 'x14', 'x15',
            'v0', 'v1', 'v2', 'v3', 'v4', 'v5', 'v6', 'v7',  # 向量寄存器
            'v8', 'v9', 'v10', 'v11', 'v12', 'v13', 'v14', 'v15'
        ]
        
        # 检查是否匹配寄存器模式
        for pattern in register_patterns:
            if var_name.startswith(pattern):
                return True
        
        # 检查是否为数字后缀的寄存器
        import re
        if re.match(r'^[a-zA-Z]+\d+$', var_name):
            return True
        
        return False 