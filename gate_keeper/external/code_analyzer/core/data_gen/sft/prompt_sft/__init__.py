"""
面向提示词的SFT训练数据生成模块

提供基于提示词模板的代码补全训练数据生成功能，包括：
- 提示词模板数据建模
- 多模型token适配（Qwen、DeepSeek等）
- 结构化上下文构建
- 自动提示词生成
"""

from .generator import PromptSFTGenerator
from .models import (ContextSection, ContextType, ModelTokenConfig, ModelType,
                     PromptSFTData, PromptTemplate, SimilarContext)
from .tasks import BasePromptTask, CodeCompletionTask

__all__ = [
    'PromptTemplate',
    'PromptSFTData', 
    'ModelTokenConfig',
    'ModelType',
    'ContextSection',
    'ContextType',
    'SimilarContext',
    'PromptSFTGenerator',

    'BasePromptTask',
    'CodeCompletionTask'
] 