"""
代码补全任务

专门处理代码补全任务的模板和逻辑
"""

from typing import Any, Dict, List

from ..models import ModelType, PromptTemplate, TaskMetadata
from .base_task import BasePromptTask


class CodeCompletionTask(BasePromptTask):
    """代码补全任务"""
    
    def get_task_name(self) -> str:
        """获取任务名称"""
        return "code_completion"
    
    def get_supported_models(self) -> List[ModelType]:
        """获取支持的模型类型"""
        return [ModelType.QWEN_CODER, ModelType.DEEPSEEK_CODER]
    
    def get_template(self, model_type: ModelType) -> PromptTemplate:
        """获取任务模板"""
        # 根据文件类型和模型类型选择合适的模板
        template = PromptTemplate()
        
        # 设置系统角色
        template.system = self._get_system_role(model_type)
        
        # 设置指令
        template.instruction = self._get_instruction(model_type)
        
        return template
    
    def build_env_info(self, metadata: TaskMetadata) -> str:
        """构建环境信息"""
        env_parts = []
        
        # 文件路径
        if metadata.filepath:
            env_parts.append(f"文件路径: {metadata.filepath}")
        
        # 当前函数
        if metadata.function_name:
            env_parts.append(f"当前函数: {metadata.function_name}")
        
        # 语言类型
        if metadata.filepath.endswith('.asm'):
            env_parts.append("语言: np")
        elif metadata.filepath.endswith('.c') or metadata.filepath.endswith('.h'):
            env_parts.append("语言: c")
        else:
            env_parts.append("语言: unknown")
        
        # 目标芯片（针对NP汇编）
        if metadata.filepath.endswith('.asm'):
            env_parts.append("目标芯片: SD5897")
        
        # 源码类型
        if metadata.source_type:
            env_parts.append(f"源码类型: {metadata.source_type}")
        
        return '\n'.join(env_parts)
    
    def build_system_rules(self, metadata: TaskMetadata) -> str:
        """构建系统规则"""
        if metadata.filepath.endswith('.asm'):
            # NP汇编规则
            return """- 寄存器使用必须符合NP架构规范
- 指令序列必须遵循NP流水线约束
- 内存访问必须对齐
- 分支指令必须考虑延迟槽"""
        else:
            # C语言规则
            return """- 所有变量必须命名规范
- 不得出现未定义行为
- 优先使用已有的宏封装逻辑
- 注释需对边界行为进行解释"""
    
    def build_user_rules(self, metadata: TaskMetadata) -> str:
        """构建用户规则"""
        if metadata.filepath.endswith('.asm'):
            return "请遵守NP汇编语法；合理使用寄存器；避免数据冒险。"
        else:
            return "请不要生成未声明变量；遵守 C 语言语法。"
    
    def build_instruction(self, metadata: TaskMetadata, model_type: ModelType = None) -> str:
        """构建任务指令"""
        # 根据模型类型获取对应的补全token
        if model_type:
            token_config = self.get_token_config(model_type)
            completion_token = token_config.middle_token
        else:
            # 默认使用通用表述
            completion_token = "REDACTED_SPECIAL_TOKEN"
        

        return f"请结合上下文在{completion_token}处补全代码"
    
    def _get_system_role(self, model_type: ModelType) -> str:
        """获取系统角色"""
        return "You are a code completion assistant."
    
    def _get_instruction(self, model_type: ModelType) -> str:
        """获取指令"""
        return "请在 Fill token 处补全代码，保持变量命名风格一致，避免重复声明。" 