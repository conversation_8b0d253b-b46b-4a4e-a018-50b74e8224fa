"""
Prompt SFT 基础任务类

定义所有Prompt SFT任务的通用接口
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List

from ..models import ModelTokenConfig, ModelType, PromptTemplate, TaskMetadata


class BasePromptTask(ABC):
    """Prompt SFT任务基类"""
    
    def __init__(self):
        """初始化任务"""
        self.task_name = self.get_task_name()
        self.supported_models = self.get_supported_models()
    
    @abstractmethod
    def get_task_name(self) -> str:
        """获取任务名称"""
        pass
    
    @abstractmethod
    def get_supported_models(self) -> List[ModelType]:
        """获取支持的任务类型"""
        pass
    
    @abstractmethod
    def get_template(self, model_type: ModelType) -> PromptTemplate:
        """获取任务模板"""
        pass
    
    @abstractmethod
    def build_env_info(self, metadata: TaskMetadata) -> str:
        """构建环境信息"""
        pass
    
    @abstractmethod
    def build_system_rules(self, metadata: TaskMetadata) -> str:
        """构建系统规则"""
        pass
    
    @abstractmethod
    def build_user_rules(self, metadata: TaskMetadata) -> str:
        """构建用户规则"""
        pass
    
    @abstractmethod
    def build_instruction(self, metadata: TaskMetadata, model_type: ModelType = None) -> str:
        """构建任务指令"""
        pass
    
    def validate_metadata(self, metadata: TaskMetadata) -> bool:
        """验证元数据是否适合当前任务"""
        return True
    
    def get_token_config(self, model_type: ModelType) -> ModelTokenConfig:
        """获取token配置"""
        return ModelTokenConfig.get_config(model_type) 