"""
SFT数据生成模块 - 分析器

提供NP代码分析功能，包括函数提取、调用关系分析等
"""

import os
import re
from typing import Any, Dict, List, Optional

from tqdm import tqdm

from core.ast_parser import ASTParserFactory
from core.repository_index_factory import RepositoryIndexFactory
from models.call_relation import FunctionCall
from models.function import Function, FunctionSignature, Parameter
from models.struct import Struct, StructUsage
from utils.file_utils import LANGUAGE_MAP

from .generator import SFTDataGenerator
from .models import EnhancedStatementMaskData, RealStatementMaskData

# NOTE: 运行/测试时需保证PYTHONPATH包含项目根目录，否则models包无法导入


class RealNPAnalyzer:
    """真实的多语言项目分析器（支持NP、C、Python等）"""
    
    def __init__(self, project_path: str):
        """初始化分析器"""
        self.project_path = project_path
        
        # 初始化核心组件
        self.repo_index = RepositoryIndexFactory.get_or_build(
            repo_dir=project_path,
            branch="main",
            exclude_patterns=None
        )
        
        # 缓存分析结果
        self._functions_cache = {}
        self._calls_cache = {}
        self._structs_cache = {}
        self._usages_cache = {}
        
        print("✓ 成功创建多语言解析器")
    
    def analyze_project(self):
        """分析整个项目"""
        print(f"开始分析多语言项目: {self.project_path}")
        
        # 1. 仓库索引已通过RepositoryIndexFactory构建
        print("1. 仓库索引已通过RepositoryIndexFactory构建")
        
        # 2. 查找支持的文件
        np_files = self._find_np_files()
        print(f"找到 {len(np_files)} 个支持的文件")
        
        if not np_files:
            print("❌ 未找到支持的文件")
            return None
        
        # 3. 解析每个文件
        all_functions = []
        all_calls = []
        all_structs = []
        all_usages = []
        
        with tqdm(total=len(np_files), desc="解析文件", unit="文件") as pbar:
            for file_path in np_files:
                print(f"解析文件: {file_path}")
                
                # 显示文件内容前几行
                try:
                    from utils.file_utils import read_file_safe
                    content = read_file_safe(file_path)
                    print(f"  文件大小: {len(content)} 字符")
                    print(f"  前200字符: {content[:200]}...")
                except Exception as e:
                    print(f"  读取文件失败: {e}")
                
                try:
                    # 根据文件类型选择合适的parser
                    parser = self._get_parser_for_file(file_path)
                    file_ext = os.path.splitext(file_path)[1].lower()
                    print(f"  使用 {file_ext} 解析器")
                    
                    # 使用真实的tree-sitter解析
                    functions = parser.extract_functions(file_path)
                    calls = parser.extract_calls(file_path)
                    
                    # 如果tree-sitter解析失败，使用降级方案
                    if not functions:
                        print(f"  ⚠️ tree-sitter解析未提取到函数，使用降级方案")
                        functions = self._extract_functions_fallback(file_path, content)
                        calls = self._extract_calls_fallback(file_path, content)
                    
                    # 尝试提取结构体（如果支持）
                    structs = []
                    usages = []
                    try:
                        structs = parser.extract_structs(file_path)
                    except:
                        pass
                    
                    try:
                        usages = parser.extract_struct_usages(file_path)
                    except:
                        pass
                    
                    all_functions.extend(functions)
                    all_calls.extend(calls)
                    all_structs.extend(structs)
                    all_usages.extend(usages)
                    
                    self._functions_cache[file_path] = functions
                    self._calls_cache[file_path] = calls
                    self._structs_cache[file_path] = structs
                    self._usages_cache[file_path] = usages
                    
                    print(f"  ✓ 提取到 {len(functions)} 个函数")
                    print(f"  ✓ 提取到 {len(calls)} 个函数调用")
                    print(f"  ✓ 提取到 {len(structs)} 个结构体")
                    print(f"  ✓ 提取到 {len(usages)} 个结构体使用")
                    
                except Exception as e:
                    print(f"  ❌ 解析文件失败: {e}")
                    continue
                finally:
                    pbar.update(1)
        
        # 4. 构建代码图
        print("2. 构建代码图...")
        # 注意：CodeGraph已经通过RepositoryIndex初始化，不需要手动添加数据
        print("✓ 代码图已通过RepositoryIndex初始化")
        
        
        print(f"总计提取到 {len(all_functions)} 个函数")
        print(f"总计提取到 {len(all_calls)} 个函数调用")
        print(f"总计提取到 {len(all_structs)} 个结构体")
        print(f"总计提取到 {len(all_usages)} 个结构体使用")
        
        return {
            'functions': all_functions,
            'calls': all_calls,
            'structs': all_structs,
            'usages': all_usages,
        }
    
    def _get_parser_for_file(self, file_path: str):
        """根据文件扩展名获取合适的parser"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # 根据文件扩展名选择parser
        if file_ext in ['.c', '.h']:
            return ASTParserFactory.create("c")
        elif file_ext == '.asm':
            return ASTParserFactory.create("np")
        elif file_ext == '.py':
            return ASTParserFactory.create("python")
        else:
            # 默认使用C parser
            return ASTParserFactory.create("c")
    
    def _find_np_files(self) -> List[str]:
        """查找NP文件（兼容C语言和NP汇编）"""
        c_file_exts = LANGUAGE_MAP.get("c", [])
        np_file_exts = LANGUAGE_MAP.get("np", [])
        # 合并NP和C语言的文件扩展名
        all_extensions = np_file_exts + c_file_exts
        
        np_files = []
        for root, dirs, files in os.walk(self.project_path):
            for file in files:
                # 检查文件是否匹配任何支持的扩展名
                if any(file.endswith(ext) for ext in all_extensions):
                    np_files.append(os.path.join(root, file))
        return np_files
        
    def extract_functions_and_bundles_real(self) -> List[Dict[str, Any]]:
        """提取真实的函数和bundle信息"""
        print("\n=== 提取真实函数和Bundle ===")
        
        functions_data = []
        
        for file_path, functions in self._functions_cache.items():
            for func in functions:
                # 获取真实上下文信息
                context_info = self._get_real_function_context(func, file_path)
                
                func_data = {
                    'name': func.name,
                    'filepath': func.filepath,
                    'start_line': func.range.start_line,
                    'end_line': func.range.end_line,
                    'signature': {
                        'parameters': [{'name': p.name, 'type': p.type_hint} for p in func.signature.parameters],
                        'return_type': func.signature.return_type
                    },
                    'code': func.code,
                    'context': context_info,
                    'complexity': self._calculate_real_function_complexity(func),
                    'call_chain_depth': self._calculate_real_call_chain_depth(func.name)
                }
                
                functions_data.append(func_data)
                print(f"  ✓ 函数: {func.name} (复杂度: {func_data['complexity']:.2f}, 调用链深度: {func_data['call_chain_depth']})")
        
        return functions_data
    
    def _get_real_function_context(self, func: Function, file_path: str) -> Dict[str, Any]:
        """获取真实的函数上下文信息"""
        context = {
            'callers': [],
            'callees': [],
            'related_structs': [],
            'related_variables': [],
            'call_chain': [],
            'struct_usages': [],
            'complexity_factors': []
        }
        
        # 查找调用关系
        for call in self._calls_cache.get(file_path, []):
            if call.callee == func.name:
                context['callers'].append(call.caller)
            elif call.caller == func.name:
                context['callees'].append(call.callee)
        
        # 构建调用链
        context['call_chain'] = self._build_real_call_chain(func.name)
        
        # 查找相关结构体
        for usage in self._usages_cache.get(file_path, []):
            if usage.function_name == func.name:
                context['struct_usages'].append({
                    'struct': usage.struct_name,
                    'type': usage.usage_type,
                    'variable': usage.variable_name
                })
                if usage.struct_name not in context['related_structs']:
                    context['related_structs'].append(usage.struct_name)
        
        # 复杂度因子
        context['complexity_factors'] = self._analyze_real_complexity_factors(func)
        
        return context
    
    def _build_real_call_chain(self, func_name: str, max_depth: int = 5) -> List[str]:
        """构建真实的调用链"""
        chain = [func_name]
        visited = set()
        
        def dfs(current_func, depth):
            if depth >= max_depth or current_func in visited:
                return
            visited.add(current_func)
            
            # 遍历所有文件的调用
            for calls in self._calls_cache.values():
                for call in calls:
                    if call.caller == current_func and call.callee not in chain:
                        chain.append(call.callee)
                        dfs(call.callee, depth + 1)
        
        dfs(func_name, 0)
        return chain
    
    def _calculate_real_function_complexity(self, func: Function) -> float:
        """计算真实的函数复杂度"""
        if not func.code:
            return 0.0
        
        complexity = 0.0
        lines = func.code.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):
                continue
            
            # 控制流复杂度
            if any(keyword in line for keyword in ['if', 'for', 'while', 'switch']):
                complexity += 1.0
            
            # 函数调用复杂度
            if '(' in line and ')' in line:
                complexity += 0.5
            
            # 嵌套复杂度
            if line.startswith('{'):
                complexity += 0.2
            elif line.startswith('}'):
                complexity -= 0.2
        
        return max(0.0, complexity)
    
    def _calculate_real_call_chain_depth(self, func_name: str) -> int:
        """计算真实的调用链深度"""
        def dfs(current_func, depth, visited=None):
            if visited is None:
                visited = set()
            
            if depth > 10 or current_func in visited:  # 防止无限递归
                return depth
            
            visited.add(current_func)
            max_depth = depth
            
            for calls in self._calls_cache.values():
                for call in calls:
                    if call.caller == current_func and call.callee not in visited:
                        max_depth = max(max_depth, dfs(call.callee, depth + 1, visited.copy()))
            
            return max_depth
        
        return dfs(func_name, 0)
    
    def _analyze_real_complexity_factors(self, func: Function) -> List[str]:
        """分析真实的复杂度因子"""
        factors = []
        
        if not func.code:
            return factors
        
        lines = func.code.split('\n')
        
        # 检查各种复杂度因子
        if any('if' in line for line in lines):
            factors.append('conditional_statements')
        
        if any('for' in line or 'while' in line for line in lines):
            factors.append('loops')
        
        if any('(' in line and ')' in line for line in lines):
            factors.append('function_calls')
        
        if len(lines) > 50:
            factors.append('large_function')
        
        if not factors:
            factors.append('simple_function')
        
        return factors
    
    def _extract_functions_fallback(self, file_path: str, content: str) -> List[Function]:
        """降级方案：使用简单文本解析提取函数"""
        functions = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检测函数定义
            if (line.startswith('void ') or line.startswith('int ') or 
                line.startswith('static void ') or line.startswith('static int ') or
                line.startswith('uint8 ') or line.startswith('uint16 ') or line.startswith('uint32 ')):
                
                func_name = self._extract_function_name_from_line(line)
                if func_name:
                    # 查找函数结束位置
                    end_line = self._find_function_end(lines, i)
                    
                    # 提取函数代码
                    func_code = '\n'.join(lines[i:end_line+1])
                    
                    # 创建函数对象
                    func = Function.create_simple(
                        name=func_name,
                        signature=self._create_simple_signature(func_name, line),
                        start_line=i + 1,
                        end_line=end_line + 1,
                        filepath=file_path,
                        code=func_code
                    )
                    functions.append(func)
                    print(f"    ✓ 降级提取函数: {func_name}")
        
        return functions
    
    def _extract_calls_fallback(self, file_path: str, content: str) -> List[FunctionCall]:
        """降级方案：使用简单文本解析提取函数调用"""
        calls = []
        lines = content.split('\n')
        current_func = None
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检测函数开始
            if (line.startswith('void ') or line.startswith('int ') or line.startswith('static void ') or 
                line.startswith('static int ') or line.startswith('function ') or line.startswith('bundle ')):
                current_func = self._extract_function_name_from_line(line)
            
            # 检测函数调用
            if current_func and '(' in line and ')' in line:
                # 简单的函数调用检测
                call_match = self._extract_function_call_from_line(line)
                if call_match:
                    call = FunctionCall(
                        caller=current_func,
                        callee=call_match,
                        file_path=file_path,
                        line=[i + 1, 0]
                    )
                    calls.append(call)
        
        return calls
    
    def _extract_function_name_from_line(self, line: str) -> Optional[str]:
        """从行中提取函数名"""
        # 简单的函数名提取逻辑
        patterns = [
            r'(?:void|int|static void|static int|uint8|uint16|uint32)\s+(\w+)\s*\(',
            r'function\s+(\w+)',
            r'bundle\s+(\w+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_function_call_from_line(self, line: str) -> Optional[str]:
        """从行中提取函数调用"""
        # 简单的函数调用提取逻辑
        patterns = [
            r'(\w+)\s*\(',
            r'(\w+)\s*\([^)]*\)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                func_name = match.group(1)
                # 过滤掉关键字
                if func_name not in ['if', 'for', 'while', 'switch', 'sizeof']:
                    return func_name
        
        return None
    
    def _find_function_end(self, lines: List[str], start_line: int) -> int:
        """查找函数结束位置"""
        brace_count = 0
        started = False
        
        for i in range(start_line, len(lines)):
            line = lines[i].strip()
            
            if '{' in line:
                brace_count += line.count('{')
                started = True
            
            if '}' in line:
                brace_count -= line.count('}')
            
            if started and brace_count == 0:
                return i
        
        return len(lines) - 1
    
    def _create_simple_signature(self, func_name: str, line: str) -> FunctionSignature:
        """创建简单的函数签名"""
        # 简单的参数提取
        params = []
        param_match = re.search(r'\(([^)]*)\)', line)
        if param_match:
            param_str = param_match.group(1).strip()
            if param_str:
                for param in param_str.split(','):
                    param = param.strip()
                    if param:
                        param_parts = param.split()
                        if len(param_parts) >= 2:
                            param_type = param_parts[0]
                            param_name = param_parts[1]
                            params.append(Parameter(name=param_name, type_hint=param_type))
        
        return FunctionSignature(
            name=func_name,
            parameters=params,
            return_type="void"  # 默认返回类型
        )
    
    # 代理方法，委托给SFTDataGenerator
    def generate_enhanced_sft_data(self, max_samples: int = 10) -> List[EnhancedStatementMaskData]:
        """生成增强的SFT数据（代理方法）"""
        generator = SFTDataGenerator(self)
        return generator.generate_enhanced_sft_data(max_samples)
    
    def generate_real_statement_mask_data(self, max_samples: int = 10) -> List[RealStatementMaskData]:
        """生成真实的语句遮盖数据（代理方法）"""
        generator = SFTDataGenerator(self)
        return generator.generate_real_statement_mask_data(max_samples)
    
    def _validate_enhanced_mask_data_quality(self, data: EnhancedStatementMaskData) -> bool:
        """验证增强遮盖数据质量（代理方法）"""
        generator = SFTDataGenerator(self)
        return generator._validate_enhanced_data_quality(
            data.before, data.expected, data.after, 
            data.context_nodes, data.metadata
        )