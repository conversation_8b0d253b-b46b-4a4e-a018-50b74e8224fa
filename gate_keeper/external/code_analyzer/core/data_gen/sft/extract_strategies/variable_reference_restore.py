"""
变量恢复策略

遮盖一个变量名，让模型根据上下文推断其含义
"""

import re
from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class VariableReferenceRestoreStrategy(BaseExtractionStrategy):
    """变量恢复策略"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """变量恢复：遮盖一个变量名"""
        extractions = []
        
        # 使用tree-sitter解析代码
        ast_info = self._parse_code_with_tree_sitter(func_code)
        
        # 提取函数体行
        lines = self._extract_function_body_lines(func_code)
        
        if not context_info or 'available_variables' not in context_info:
            return extractions
        
        available_variables = context_info['available_variables']
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检查是否包含变量引用
            variables = self._extract_variables_from_line(line)
            
            for var in variables:
                if var in available_variables:
                    # 遮盖变量
                    before_part = line[:line.find(var)]
                    after_part = line[line.find(var) + len(var):]
                    
                    before_lines = lines[:i]
                    before_lines.append(before_part)
                    after_lines = [after_part] + lines[i+1:]
                    
                    before = '\n'.join(before_lines)
                    expected = var
                    after = '\n'.join(after_lines)
                    
                    if self._validate_extraction(before, expected, after):
                        # 分析变量的AST信息
                        var_info = self._analyze_variable_ast(var, line, ast_info, i, available_variables)
                        
                        metadata = self._build_metadata(
                            "variable_reference_restore",
                            masking_type="variable",
                            line_number=i+1,
                            **var_info
                        )
                        
                        extractions.append({
                            "before": before,
                            "expected": expected,
                            "after": after,
                            "metadata": metadata
                        })
        
        return extractions
    
    def _extract_variables_from_line(self, line: str) -> List[str]:
        """从行中提取变量"""
        # 使用tree-sitter解析的变量提取
        variables = []
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', line)
        
        for word in words:
            if word not in ['if', 'for', 'while', 'return', 'int', 'void', 'char', 'const', 'static']:
                variables.append(word)
        
        return variables
    
    def _analyze_variable_ast(self, var_name: str, line: str, ast_info: Dict[str, Any], line_index: int, available_variables: List[str]) -> Dict[str, Any]:
        """分析变量的AST信息"""
        var_info = {
            "ast_parent_type": "variable_reference",
            "target_is_expression": False,
            "target_token_count": 1,
            "refers_to_function": [],
            "refers_to_variable": [var_name]
        }
        
        # 从AST中查找对应的变量信息
        for var in ast_info["variables"]:
            if var["name"] == var_name:
                var_info["variable_definition"] = var["text"]
                break
        
        # 检查变量是否在可用变量列表中
        if var_name in available_variables:
            var_info["variable_available"] = True
        else:
            var_info["variable_available"] = False
        
        # 检查是否包含函数调用
        for call in ast_info["function_calls"]:
            if call["name"] in line:
                var_info["refers_to_function"].append(call["name"])
        
        # 检查是否为表达式的一部分
        if '=' in line or '+' in line or '-' in line or '*' in line or '/' in line:
            var_info["target_is_expression"] = True
        
        return var_info 