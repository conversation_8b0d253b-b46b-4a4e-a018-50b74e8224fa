"""
基础提取策略类

提供基于tree-sitter的代码解析和提取功能
"""

import random
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from core.ast_parser import ASTParserFactory
from core.data_gen.sft.models import EnhancedStatementMaskData


@dataclass
class ExtractionConfig:
    """提取配置"""
    strategy_name: str = "waterfall_sequential"
    min_lines: int = 1                      # 最少行数
    max_lines: int = 5                      # 最多行数
    include_comments: bool = True           # 是否包含注释
    include_control_flow: bool = True       # 是否包含控制流语句
    semantic_aware: bool = True             # 是否语义感知
    random_seed: int = 42                   # 随机种子
    comment_hint: bool = False              # 是否启用注释提示


class BaseExtractionStrategy(ABC):
    """提取策略基类"""
    
    def __init__(self, config: ExtractionConfig):
        self.config = config
        random.seed(config.random_seed)
        self.parsers = {
            "np": ASTParserFactory.create("np"),
            "c": ASTParserFactory.create("c"),
            "python": ASTParserFactory.create("python")
        }
    
    @abstractmethod
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        提取SFT数据
        
        Args:
            func_code: 完整函数代码
            func_name: 函数名（用于上下文分析）
            context_info: 上下文信息（函数调用、变量等）
            
        Returns:
            List of extraction dictionaries with before, expected, after, metadata
        """
        pass
    
    def _validate_extraction(self, before: str, expected: str, after: str) -> bool:
        """验证提取结果"""
        if not before.strip() or not expected.strip():
            return False
        
        # 长度检查
        if len(expected) > 500:  # 避免过长的补全
            return False
        
        return True
    
    def _build_metadata(self, strategy: str, **kwargs) -> Dict[str, Any]:
        """构建元数据"""
        metadata = {
            "strategy": strategy,
            "comment_hint": self.config.comment_hint,
            **kwargs
        }
        return metadata
    
    def _get_parser_for_code(self, func_code: str, file_path: str = "") -> Any:
        """根据代码内容选择合适的解析器"""
        # 根据文件路径或代码内容判断语言
        if file_path:
            if file_path.endswith('.asm'):
                return self.parsers["np"]
            elif file_path.endswith(('.c', '.h')):
                return self.parsers["c"]
            elif file_path.endswith('.py'):
                return self.parsers["python"]
        
        # 根据代码内容判断
        if 'bundle' in func_code or 'inline' in func_code:
            return self.parsers["np"]
        elif 'def ' in func_code or 'import ' in func_code:
            return self.parsers["python"]
        else:
            return self.parsers["c"]
    
    def _parse_code_with_tree_sitter(self, func_code: str, file_path: str = "") -> Dict[str, Any]:
        """使用tree-sitter解析代码"""
        parser = self._get_parser_for_code(func_code, file_path)
        
        try:
            # 解析代码
            tree = parser._parse_code(func_code)
            root_node = tree.root_node
            
            # 提取AST信息
            ast_info = {
                "functions": [],
                "variables": [],
                "comments": [],
                "control_flow": [],
                "function_calls": [],
                "statements": []
            }
            
            # 遍历AST节点
            self._extract_ast_nodes(root_node, ast_info)
            
            return ast_info
            
        except Exception as e:
            print(f"Tree-sitter解析失败: {e}")
            return self._fallback_parse(func_code)
    
    def _extract_ast_nodes(self, node, ast_info: Dict[str, Any]):
        """递归提取AST节点信息"""
        try:
            # 检查节点类型
            if not hasattr(node, 'type'):
                return
            
            # 提取函数定义
            if node.type in ['function_definition', 'function_declaration']:
                try:
                    func_info = {
                        'name': self._extract_function_name(node),
                        'start_line': node.start_point[0] if hasattr(node, 'start_point') else 0,
                        'end_line': node.end_point[0] if hasattr(node, 'end_point') else 0,
                        'body': self._extract_node_text(node)
                    }
                    ast_info["functions"].append(func_info)
                except (AttributeError, IndexError):
                    pass
            
            # 提取变量声明
            elif node.type in ['declaration', 'variable_declaration']:
                try:
                    var_info = {
                        'name': self._extract_variable_name(node),
                        'start_line': node.start_point[0] if hasattr(node, 'start_point') else 0,
                        'end_line': node.end_point[0] if hasattr(node, 'end_point') else 0,
                        'text': self._extract_node_text(node)
                    }
                    ast_info["variables"].append(var_info)
                except (AttributeError, IndexError):
                    pass
            
            # 提取注释
            elif node.type in ['comment', 'line_comment', 'block_comment']:
                try:
                    comment_info = {
                        'text': self._extract_node_text(node),
                        'start_line': node.start_point[0] if hasattr(node, 'start_point') else 0,
                        'end_line': node.end_point[0] if hasattr(node, 'end_point') else 0,
                        'type': 'line' if node.type == 'line_comment' else 'block'
                    }
                    ast_info["comments"].append(comment_info)
                except (AttributeError, IndexError):
                    pass
            
            # 提取控制流语句
            elif node.type in ['if_statement', 'for_statement', 'while_statement', 'switch_statement']:
                try:
                    control_info = {
                        'type': node.type,
                        'start_line': node.start_point[0] if hasattr(node, 'start_point') else 0,
                        'end_line': node.end_point[0] if hasattr(node, 'end_point') else 0,
                        'text': self._extract_node_text(node),
                        'condition': self._extract_condition(node)
                    }
                    ast_info["control_flow"].append(control_info)
                except (AttributeError, IndexError):
                    pass
            
            # 提取函数调用
            elif node.type == 'call_expression':
                try:
                    call_info = {
                        'name': self._extract_function_call_name(node),
                        'start_line': node.start_point[0] if hasattr(node, 'start_point') else 0,
                        'end_line': node.end_point[0] if hasattr(node, 'end_point') else 0,
                        'text': self._extract_node_text(node)
                    }
                    ast_info["function_calls"].append(call_info)
                except (AttributeError, IndexError):
                    pass
            
            # 提取语句
            elif node.type in ['expression_statement', 'return_statement', 'assignment_expression']:
                try:
                    stmt_info = {
                        'type': node.type,
                        'start_line': node.start_point[0] if hasattr(node, 'start_point') else 0,
                        'end_line': node.end_point[0] if hasattr(node, 'end_point') else 0,
                        'text': self._extract_node_text(node)
                    }
                    ast_info["statements"].append(stmt_info)
                except (AttributeError, IndexError):
                    pass
            
            # 递归处理子节点
            if hasattr(node, 'children') and node.children:
                try:
                    # 确保children是可迭代的
                    if isinstance(node.children, (list, tuple)):
                        for child in node.children:
                            if hasattr(child, 'type'):  # 确保是有效的节点
                                self._extract_ast_nodes(child, ast_info)
                    else:
                        # 如果不是列表，尝试转换为列表
                        children_list = list(node.children)
                        for child in children_list:
                            if hasattr(child, 'type'):
                                self._extract_ast_nodes(child, ast_info)
                except (TypeError, AttributeError) as e:
                    # 如果无法迭代children，跳过这个节点
                    print(f"    无法迭代节点children: {e}")
                    pass
        except Exception as e:
            # 如果处理某个节点时出错，记录错误但继续处理其他节点
            print(f"    处理AST节点时出错: {e}")
            pass
    
    def _extract_function_name(self, node) -> str:
        """提取函数名"""
        if hasattr(node, 'children') and node.children:
            try:
                if isinstance(node.children, (list, tuple)):
                    for child in node.children:
                        if hasattr(child, 'type') and child.type == 'identifier':
                            return self._extract_node_text(child)
                else:
                    children_list = list(node.children)
                    for child in children_list:
                        if hasattr(child, 'type') and child.type == 'identifier':
                            return self._extract_node_text(child)
            except (TypeError, AttributeError):
                pass
        return ""
    
    def _extract_variable_name(self, node) -> str:
        """提取变量名"""
        if hasattr(node, 'children') and node.children:
            try:
                if isinstance(node.children, (list, tuple)):
                    for child in node.children:
                        if hasattr(child, 'type') and child.type == 'identifier':
                            return self._extract_node_text(child)
                else:
                    children_list = list(node.children)
                    for child in children_list:
                        if hasattr(child, 'type') and child.type == 'identifier':
                            return self._extract_node_text(child)
            except (TypeError, AttributeError):
                pass
        return ""
    
    def _extract_function_call_name(self, node) -> str:
        """提取函数调用名"""
        if hasattr(node, 'children') and node.children:
            try:
                if isinstance(node.children, (list, tuple)):
                    for child in node.children:
                        if hasattr(child, 'type') and child.type == 'identifier':
                            return self._extract_node_text(child)
                else:
                    children_list = list(node.children)
                    for child in children_list:
                        if hasattr(child, 'type') and child.type == 'identifier':
                            return self._extract_node_text(child)
            except (TypeError, AttributeError):
                pass
        return ""
    
    def _extract_condition(self, node) -> str:
        """提取条件表达式"""
        if hasattr(node, 'children') and node.children:
            try:
                if isinstance(node.children, (list, tuple)):
                    for child in node.children:
                        if hasattr(child, 'type') and child.type == 'parenthesized_expression':
                            return self._extract_node_text(child)
                else:
                    children_list = list(node.children)
                    for child in children_list:
                        if hasattr(child, 'type') and child.type == 'parenthesized_expression':
                            return self._extract_node_text(child)
            except (TypeError, AttributeError):
                pass
        return ""
    
    def _extract_node_text(self, node) -> str:
        """提取节点文本"""
        try:
            if hasattr(node, 'text') and node.text:
                return node.text.decode('utf-8')
            return ""
        except (AttributeError, UnicodeDecodeError):
            return ""
    
    def _fallback_parse(self, func_code: str) -> Dict[str, Any]:
        """降级解析方案"""
        lines = func_code.split('\n')
        ast_info = {
            "functions": [],
            "variables": [],
            "comments": [],
            "control_flow": [],
            "function_calls": [],
            "statements": []
        }
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 简单解析注释
            if line.startswith('//') or line.startswith('/*'):
                ast_info["comments"].append({
                    'text': line,
                    'start_line': i,
                    'end_line': i,
                    'type': 'line' if line.startswith('//') else 'block'
                })
            
            # 简单解析控制流
            elif line.startswith(('if', 'for', 'while', 'switch')):
                ast_info["control_flow"].append({
                    'type': line.split('(')[0] + '_statement',
                    'start_line': i,
                    'end_line': i,
                    'text': line,
                    'condition': self._extract_condition_simple(line)
                })
            
            # 简单解析函数调用
            elif '(' in line and ')' in line and ';' in line:
                func_name = line.split('(')[0].strip()
                if func_name and not func_name.startswith(('if', 'for', 'while')):
                    ast_info["function_calls"].append({
                        'name': func_name,
                        'start_line': i,
                        'end_line': i,
                        'text': line
                    })
        
        return ast_info
    
    def _extract_condition_simple(self, line: str) -> str:
        """简单提取条件表达式"""
        if '(' in line and ')' in line:
            start = line.find('(') + 1
            end = line.rfind(')')
            return line[start:end].strip()
        return ""
    
    def _extract_function_body_lines(self, func_code: str) -> List[str]:
        """提取函数体行"""
        lines = func_code.split('\n')
        func_body_lines = []
        in_function = False
        brace_count = 0
        
        for line in lines:
            stripped_line = line.strip()
            
            # 检测函数开始
            if '{' in stripped_line and not in_function:
                in_function = True
                brace_count += stripped_line.count('{')
                continue
            
            if in_function:
                if '{' in stripped_line:
                    brace_count += stripped_line.count('{')
                
                if '}' in stripped_line:
                    brace_count -= stripped_line.count('}')
                    if brace_count == 0:
                        break
                
                # 添加函数体行
                if stripped_line:
                    func_body_lines.append(stripped_line)
        
        return func_body_lines 