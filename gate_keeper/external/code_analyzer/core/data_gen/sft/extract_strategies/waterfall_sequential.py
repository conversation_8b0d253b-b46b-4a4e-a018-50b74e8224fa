"""
瀑布式顺序遮盖策略

模拟从上至下写代码的过程，按顺序遮盖语句
"""

from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class WaterfallSequentialStrategy(BaseExtractionStrategy):
    """瀑布式顺序遮盖策略"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """瀑布式顺序遮盖：模拟从上至下写代码"""
        extractions = []
        
        # 使用tree-sitter解析代码
        ast_info = self._parse_code_with_tree_sitter(func_code)
        
        # 提取函数体行
        lines = self._extract_function_body_lines(func_code)
        
        if len(lines) < 2:
            return extractions
        
        # 瀑布式遮盖：逐行遮盖
        for i in range(len(lines) - 1):
            before_lines = lines[:i+1]
            expected_line = lines[i+1]
            after_lines = lines[i+2:]
            
            before = '\n'.join(before_lines)
            expected = expected_line
            after = '\n'.join(after_lines)
            
            if self._validate_extraction(before, expected, after):
                # 分析当前语句的AST信息
                statement_info = self._analyze_statement_ast(expected, ast_info, i+1)
                
                metadata = self._build_metadata(
                    "waterfall_sequential",
                    masking_type="sequential",
                    line_number=i+2,
                    **statement_info
                )
                
                extractions.append({
                    "before": before,
                    "expected": expected,
                    "after": after,
                    "metadata": metadata
                })
        
        return extractions
    
    def _analyze_statement_ast(self, statement: str, ast_info: Dict[str, Any], line_index: int) -> Dict[str, Any]:
        """分析语句的AST信息"""
        statement_info = {
            "ast_parent_type": "statement",
            "target_is_expression": False,
            "target_token_count": len(statement.split()),
            "refers_to_function": [],
            "refers_to_variable": []
        }
        
        # 检查是否为控制流语句
        for control in ast_info["control_flow"]:
            if control["start_line"] == line_index:
                statement_info["ast_parent_type"] = control["type"]
                break
        
        # 检查是否包含函数调用
        for call in ast_info["function_calls"]:
            if call["start_line"] == line_index:
                statement_info["refers_to_function"].append(call["name"])
        
        # 检查是否包含变量引用
        for var in ast_info["variables"]:
            if var["name"] in statement:
                statement_info["refers_to_variable"].append(var["name"])
        
        # 检查是否为表达式
        if '=' in statement or '+' in statement or '-' in statement or '*' in statement or '/' in statement:
            statement_info["target_is_expression"] = True
        
        return statement_info 