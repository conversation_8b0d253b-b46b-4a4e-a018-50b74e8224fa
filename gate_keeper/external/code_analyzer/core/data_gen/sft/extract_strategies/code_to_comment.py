"""
注释生成策略

模拟文档生成或代码解释任务，为代码生成注释
"""

from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class CodeToCommentStrategy(BaseExtractionStrategy):
    """注释生成策略"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """注释生成：模拟文档生成或代码解释任务"""
        extractions = []
        
        # 使用tree-sitter解析代码
        ast_info = self._parse_code_with_tree_sitter(func_code)
        
        # 提取函数体行
        lines = self._extract_function_body_lines(func_code)
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 跳过注释行
            if self._is_comment(line):
                continue
            
            # 跳过空行
            if not line:
                continue
            
            # 为代码行生成注释
            before_lines = lines[:i]
            expected_comment = self._generate_comment_for_code(line, ast_info)
            after_lines = lines[i+1:]
            
            before = '\n'.join(before_lines)
            expected = expected_comment
            after = '\n'.join(after_lines)
            
            if self._validate_extraction(before, expected, after):
                # 分析代码的AST信息
                code_info = self._analyze_code_ast(line, ast_info, i)
                
                metadata = self._build_metadata(
                    "code_to_comment",
                    masking_type="code_to_comment",
                    comment_hint=True,
                    line_number=i+1,
                    **code_info
                )
                
                extractions.append({
                    "before": before,
                    "expected": expected,
                    "after": after,
                    "metadata": metadata
                })
        
        return extractions
    
    def _is_comment(self, line: str) -> bool:
        """判断是否为注释"""
        line = line.strip()
        return (line.startswith('//') or 
                line.startswith('/*') or 
                line.startswith('*') or
                line.startswith('#'))
    
    def _generate_comment_for_code(self, code_line: str, ast_info: Dict[str, Any]) -> str:
        """为代码行生成注释"""
        code_line = code_line.strip()
        
        # 基于AST信息生成更准确的注释
        for call in ast_info["function_calls"]:
            if call["name"] in code_line:
                return f"// 调用函数 {call['name']}"
        
        for control in ast_info["control_flow"]:
            if control["type"] in code_line:
                if control["type"] == "if_statement":
                    return "// 条件判断"
                elif control["type"] == "for_statement":
                    return "// 循环处理"
                elif control["type"] == "while_statement":
                    return "// 循环处理"
                elif control["type"] == "switch_statement":
                    return "// 分支选择"
        
        # 简单的注释生成逻辑
        if 'if' in code_line:
            return "// 条件判断"
        elif 'for' in code_line:
            return "// 循环处理"
        elif 'while' in code_line:
            return "// 循环处理"
        elif 'return' in code_line:
            return "// 返回结果"
        elif '=' in code_line:
            return "// 变量赋值"
        elif '(' in code_line and ')' in code_line:
            return "// 函数调用"
        else:
            return "// 代码执行"
    
    def _analyze_code_ast(self, code: str, ast_info: Dict[str, Any], line_index: int) -> Dict[str, Any]:
        """分析代码的AST信息"""
        code_info = {
            "ast_parent_type": "statement",
            "target_is_expression": False,
            "target_token_count": len(code.split()),
            "refers_to_function": [],
            "refers_to_variable": []
        }
        
        # 检查是否为控制流语句
        for control in ast_info["control_flow"]:
            if control["start_line"] == line_index:
                code_info["ast_parent_type"] = control["type"]
                break
        
        # 检查是否包含函数调用
        for call in ast_info["function_calls"]:
            if call["start_line"] == line_index:
                code_info["refers_to_function"].append(call["name"])
        
        # 检查是否包含变量引用
        for var in ast_info["variables"]:
            if var["name"] in code:
                code_info["refers_to_variable"].append(var["name"])
        
        # 检查是否为表达式
        if '=' in code or '+' in code or '-' in code or '*' in code or '/' in code:
            code_info["target_is_expression"] = True
        
        return code_info 