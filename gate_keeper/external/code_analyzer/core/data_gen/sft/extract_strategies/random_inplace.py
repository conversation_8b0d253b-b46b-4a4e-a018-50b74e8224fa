"""
随机插入/替换策略

模拟中间编辑或修改的过程，随机选择语句进行遮盖
"""

import random
from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class RandomInplaceStrategy(BaseExtractionStrategy):
    """随机插入/替换策略"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """随机插入/替换：模拟中间编辑或修改"""
        extractions = []
        
        # 使用tree-sitter解析代码
        ast_info = self._parse_code_with_tree_sitter(func_code)
        
        # 提取函数体行
        lines = self._extract_function_body_lines(func_code)
        
        if len(lines) < 3:
            return extractions
        
        # 随机选择遮盖位置
        num_samples = min(3, len(lines) // 2)
        
        for _ in range(num_samples):
            # 随机选择遮盖位置
            mask_index = random.randint(1, len(lines) - 2)
            
            before_lines = lines[:mask_index]
            expected_line = lines[mask_index]
            after_lines = lines[mask_index+1:]
            
            before = '\n'.join(before_lines)
            expected = expected_line
            after = '\n'.join(after_lines)
            
            if self._validate_extraction(before, expected, after):
                # 分析当前语句的AST信息
                statement_info = self._analyze_statement_ast(expected, ast_info, mask_index)
                
                metadata = self._build_metadata(
                    "random_inplace",
                    masking_type="inplace",
                    line_number=mask_index+1,
                    **statement_info
                )
                
                extractions.append({
                    "before": before,
                    "expected": expected,
                    "after": after,
                    "metadata": metadata
                })
        
        return extractions
    
    def _analyze_statement_ast(self, statement: str, ast_info: Dict[str, Any], line_index: int) -> Dict[str, Any]:
        """分析语句的AST信息"""
        statement_info = {
            "ast_parent_type": "statement",
            "target_is_expression": False,
            "target_token_count": len(statement.split()),
            "refers_to_function": [],
            "refers_to_variable": []
        }
        
        # 检查是否为控制流语句
        for control in ast_info["control_flow"]:
            if control["start_line"] == line_index:
                statement_info["ast_parent_type"] = control["type"]
                break
        
        # 检查是否包含函数调用
        for call in ast_info["function_calls"]:
            if call["start_line"] == line_index:
                statement_info["refers_to_function"].append(call["name"])
        
        # 检查是否包含变量引用
        for var in ast_info["variables"]:
            if var["name"] in statement:
                statement_info["refers_to_variable"].append(var["name"])
        
        # 检查是否为表达式
        if '=' in statement or '+' in statement or '-' in statement or '*' in statement or '/' in statement:
            statement_info["target_is_expression"] = True
        
        return statement_info 