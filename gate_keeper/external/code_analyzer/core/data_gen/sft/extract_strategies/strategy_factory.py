"""
策略工厂

负责创建和管理各种提取策略
"""

from enum import Enum
from typing import List

from .base_strategy import BaseExtractionStrategy, ExtractionConfig
from .call_graph_completion import CallGraphCompletionStrategy
from .code_to_comment import CodeToCommentStrategy
from .comment_to_code import CommentToCodeStrategy
from .control_flow_hallucination import ControlFlowHallucinationStrategy
from .random_inplace import RandomInplaceStrategy
from .variable_reference_restore import VariableReferenceRestoreStrategy
from .waterfall_sequential import WaterfallSequentialStrategy


class ExtractionStrategy(Enum):
    """提取策略枚举"""
    WATERFALL_SEQUENTIAL = "waterfall_sequential"           # 瀑布式顺序遮盖
    RANDOM_INPLACE = "random_inplace"                       # 随机插入/替换
    COMMENT_TO_CODE = "comment_to_code"                     # 注释驱动补全
    CODE_TO_COMMENT = "code_to_comment"                     # 注释生成
    CONTROL_FLOW_HALLUCINATION = "control_flow_hallucination"  # 逻辑填空
    CALL_GRAPH_COMPLETION = "call_graph_completion"         # 调用链恢复
    VARIABLE_REFERENCE_RESTORE = "variable_reference_restore"  # 变量恢复


class ExtractionStrategyFactory:
    """提取策略工厂"""
    
    _strategies = {
        ExtractionStrategy.WATERFALL_SEQUENTIAL: WaterfallSequentialStrategy,
        ExtractionStrategy.RANDOM_INPLACE: RandomInplaceStrategy,
        ExtractionStrategy.COMMENT_TO_CODE: CommentToCodeStrategy,
        ExtractionStrategy.CODE_TO_COMMENT: CodeToCommentStrategy,
        ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: ControlFlowHallucinationStrategy,
        ExtractionStrategy.CALL_GRAPH_COMPLETION: CallGraphCompletionStrategy,
        ExtractionStrategy.VARIABLE_REFERENCE_RESTORE: VariableReferenceRestoreStrategy,
    }
    
    @classmethod
    def create_strategy(cls, strategy: ExtractionStrategy, config: ExtractionConfig) -> BaseExtractionStrategy:
        """创建提取策略"""
        strategy_class = cls._strategies.get(strategy)
        if not strategy_class:
            raise ValueError(f"不支持的提取策略: {strategy}")
        
        # 更新配置的策略名称
        config.strategy_name = strategy.value
        
        return strategy_class(config)
    
    @classmethod
    def get_supported_strategies(cls) -> List[ExtractionStrategy]:
        """获取支持的策略列表"""
        return list(cls._strategies.keys())
    
    @classmethod
    def get_strategy_description(cls, strategy: ExtractionStrategy) -> str:
        """获取策略描述"""
        descriptions = {
            ExtractionStrategy.WATERFALL_SEQUENTIAL: "瀑布式顺序遮盖：模拟从上至下写代码",
            ExtractionStrategy.RANDOM_INPLACE: "随机插入/替换：模拟中间编辑或修改",
            ExtractionStrategy.COMMENT_TO_CODE: "注释驱动补全：训练模型通过注释补全代码",
            ExtractionStrategy.CODE_TO_COMMENT: "注释生成：模拟文档生成或代码解释任务",
            ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: "逻辑填空：遮盖完整控制块头部或其条件表达式",
            ExtractionStrategy.CALL_GRAPH_COMPLETION: "调用链恢复：遮盖函数调用语句",
            ExtractionStrategy.VARIABLE_REFERENCE_RESTORE: "变量恢复：遮盖一个变量名",
        }
        return descriptions.get(strategy, "未知策略") 