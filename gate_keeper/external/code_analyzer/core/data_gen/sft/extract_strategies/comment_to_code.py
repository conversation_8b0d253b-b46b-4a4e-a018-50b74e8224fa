"""
注释驱动补全策略

训练模型通过注释补全代码，特别适合中国/嵌入式代码常出现的大量语义注释
"""

from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class CommentToCodeStrategy(BaseExtractionStrategy):
    """注释驱动补全策略"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """注释驱动补全：训练模型通过注释补全代码"""
        extractions = []
        
        # 使用tree-sitter解析代码
        ast_info = self._parse_code_with_tree_sitter(func_code)
        
        # 提取函数体行
        lines = self._extract_function_body_lines(func_code)
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检查是否为注释
            if self._is_comment(line):
                # 查找注释后的代码
                code_lines = self._find_code_after_comment(lines, i)
                if code_lines:
                    before_lines = lines[:i]
                    expected_lines = code_lines
                    after_lines = lines[i + len(code_lines) + 1:]
                    
                    before = '\n'.join(before_lines)
                    expected = '\n'.join(expected_lines)
                    after = '\n'.join(after_lines)
                    
                    if self._validate_extraction(before, expected, after):
                        # 分析注释和代码的AST信息
                        comment_info = self._analyze_comment_ast(line, expected, ast_info, i)
                        
                        metadata = self._build_metadata(
                            "comment_to_code",
                            masking_type="comment_to_code",
                            comment_hint=True,
                            line_number=i+1,
                            **comment_info
                        )
                        
                        extractions.append({
                            "before": before,
                            "expected": expected,
                            "after": after,
                            "metadata": metadata
                        })
        
        return extractions
    
    def _is_comment(self, line: str) -> bool:
        """判断是否为注释"""
        line = line.strip()
        return (line.startswith('//') or 
                line.startswith('/*') or 
                line.startswith('*') or
                line.startswith('#'))
    
    def _get_comment_type(self, comment: str) -> str:
        """获取注释类型"""
        comment = comment.strip().lower()
        if 'todo' in comment:
            return "todo"
        elif 'doc' in comment or 'document' in comment:
            return "doc"
        elif 'hint' in comment:
            return "hint"
        else:
            return "inline"
    
    def _find_code_after_comment(self, lines: List[str], comment_index: int) -> List[str]:
        """查找注释后的代码"""
        code_lines = []
        
        for i in range(comment_index + 1, len(lines)):
            line = lines[i].strip()
            
            # 跳过空行和连续注释
            if not line or self._is_comment(line):
                continue
            
            # 找到代码行
            code_lines.append(line)
            
            # 限制代码行数
            if len(code_lines) >= self.config.max_lines:
                break
        
        return code_lines
    
    def _analyze_comment_ast(self, comment: str, code: str, ast_info: Dict[str, Any], comment_line: int) -> Dict[str, Any]:
        """分析注释和代码的AST信息"""
        comment_info = {
            "comment_type": self._get_comment_type(comment),
            "comment_associated_code": code,
            "ast_parent_type": "comment_to_code",
            "target_is_expression": False,
            "target_token_count": len(code.split()),
            "refers_to_function": [],
            "refers_to_variable": []
        }
        
        # 检查代码是否包含函数调用
        for call in ast_info["function_calls"]:
            if call["name"] in code:
                comment_info["refers_to_function"].append(call["name"])
        
        # 检查代码是否包含变量引用
        for var in ast_info["variables"]:
            if var["name"] in code:
                comment_info["refers_to_variable"].append(var["name"])
        
        # 检查是否为表达式
        if '=' in code or '+' in code or '-' in code or '*' in code or '/' in code:
            comment_info["target_is_expression"] = True
        
        return comment_info 