"""
SFT数据提取策略模块

提供多种基于tree-sitter的代码提取策略：
1. waterfall_sequential - 瀑布式顺序遮盖
2. random_inplace - 随机插入/替换
3. comment_to_code - 注释驱动补全
4. code_to_comment - 注释生成
5. control_flow_hallucination - 逻辑填空
6. call_graph_completion - 调用链恢复
7. variable_reference_restore - 变量恢复
"""

# 创建增强的提取管理器
from typing import Any, Dict, List

from core.data_gen.sft.models import EnhancedStatementMaskData

from .base_strategy import BaseExtractionStrategy, ExtractionConfig
from .call_graph_completion import CallGraphCompletionStrategy
from .code_to_comment import CodeToCommentStrategy
from .comment_to_code import CommentToCodeStrategy
from .control_flow_hallucination import ControlFlowHallucinationStrategy
from .random_inplace import RandomInplaceStrategy
from .strategy_factory import ExtractionStrategy, ExtractionStrategyFactory
from .variable_reference_restore import VariableReferenceRestoreStrategy
from .waterfall_sequential import WaterfallSequentialStrategy


class EnhancedExtractionManager:
    """增强的提取管理器"""
    
    def __init__(self, config: ExtractionConfig):
        self.config = config
        # 根据策略名称创建策略
        strategy_enum = None
        for strategy in ExtractionStrategy:
            if strategy.value == config.strategy_name:
                strategy_enum = strategy
                break
        
        if strategy_enum is None:
            strategy_enum = ExtractionStrategy.WATERFALL_SEQUENTIAL
        
        self.strategy = ExtractionStrategyFactory.create_strategy(strategy_enum, config)
    
    def extract_sft_data(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[EnhancedStatementMaskData]:
        """提取SFT数据"""
        if not func_code:
            return []
        
        # 使用策略提取
        extractions = self.strategy.extract(func_code, func_name, context_info)
        
        # 转换为EnhancedStatementMaskData
        sft_data = []
        for extraction in extractions:
            metadata = extraction["metadata"]
            metadata.update({
                "func_name": func_name,
                "extraction_type": "enhanced"
            })
            
            sft_item = EnhancedStatementMaskData(
                before=extraction["before"],
                expected=extraction["expected"],
                after=extraction["after"],
                context_nodes=[],
                dependency_graph=None,
                metadata=metadata
            )
            
            sft_data.append(sft_item)
        
        return sft_data

__all__ = [
    'BaseExtractionStrategy',
    'ExtractionConfig',
    'ExtractionStrategy',
    'ExtractionStrategyFactory',
    'WaterfallSequentialStrategy',
    'RandomInplaceStrategy',
    'CommentToCodeStrategy',
    'CodeToCommentStrategy',
    'ControlFlowHallucinationStrategy',
    'CallGraphCompletionStrategy',
    'VariableReferenceRestoreStrategy',
    'EnhancedExtractionManager',
] 