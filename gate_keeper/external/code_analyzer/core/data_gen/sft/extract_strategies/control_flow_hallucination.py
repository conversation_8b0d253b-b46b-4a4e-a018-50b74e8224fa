"""
逻辑填空策略

遮盖完整控制块头部或其条件表达式，要求模型"还原意图"
"""

from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class ControlFlowHallucinationStrategy(BaseExtractionStrategy):
    """逻辑填空策略（基于tree-sitter AST）"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        基于tree-sitter AST递归提取所有控制流语句（if/for/while/switch），
        对每个控制流节点生成一个样本，expected为条件表达式。
        """
        extractions = []
        ast_info = self._parse_code_with_tree_sitter(func_code)
        parser = self._get_parser_for_code(func_code)
        tree = parser._parse_code(func_code)
        root = tree.root_node
        
        control_types = {"if_statement", "for_statement", "while_statement", "switch_statement"}
        code_bytes = func_code.encode("utf-8")

        def find_control_nodes(node):
            results = []
            if hasattr(node, "type") and node.type in control_types:
                results.append(node)
            for child in getattr(node, "children", []):
                results.extend(find_control_nodes(child))
            return results

        control_nodes = find_control_nodes(root)

        for ctrl_node in control_nodes:
            # 提取条件表达式
            cond_text = self._extract_condition_from_node(ctrl_node, code_bytes)
            # 生成before/expected/after
            start_byte = ctrl_node.start_byte
            end_byte = ctrl_node.end_byte
            before = code_bytes[:start_byte].decode("utf-8", errors="ignore")
            after = code_bytes[end_byte:].decode("utf-8", errors="ignore")
            expected = cond_text
            if self._validate_extraction(before, expected, after):
                metadata = self._build_metadata(
                    "control_flow_hallucination",
                    masking_type="expression",
                    line_number=ctrl_node.start_point[0] + 1,
                    control_type=ctrl_node.type
                )
                extractions.append({
                    "before": before,
                    "expected": expected,
                    "after": after,
                    "metadata": metadata
                })
        return extractions

    def _extract_condition_from_node(self, node, code_bytes: bytes) -> str:
        """从控制流AST节点中提取条件表达式文本"""
        # 针对不同控制流类型，查找条件表达式子节点
        # 适配C/NP等常见语法树结构
        for child in getattr(node, "children", []):
            # C/NP: if/for/while/switch的条件一般在parenthesized_expression或condition
            if hasattr(child, "type") and child.type in ("parenthesized_expression", "condition"):
                # 取括号内内容
                return code_bytes[child.start_byte:child.end_byte].decode("utf-8", errors="ignore")
        # fallback: 返回空
        return "" 