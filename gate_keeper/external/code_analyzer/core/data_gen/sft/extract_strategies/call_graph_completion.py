"""
调用链恢复策略

遮盖函数调用语句，让模型推断"此处应调用什么函数"
"""

from typing import Any, Dict, List

from .base_strategy import BaseExtractionStrategy


class CallGraphCompletionStrategy(BaseExtractionStrategy):
    """调用链恢复策略"""
    
    def extract(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """调用链恢复：遮盖函数调用语句"""
        extractions = []
        
        # 使用tree-sitter解析代码
        ast_info = self._parse_code_with_tree_sitter(func_code)
        
        # 提取函数体行
        lines = self._extract_function_body_lines(func_code)
        
        if not context_info or 'available_functions' not in context_info:
            return extractions
        
        available_functions = context_info['available_functions']
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检查是否为函数调用
            if self._is_function_call(line):
                before_lines = lines[:i]
                expected_call = self._extract_function_call(line)
                after_lines = lines[i+1:]
                
                before = '\n'.join(before_lines)
                expected = expected_call
                after = '\n'.join(after_lines)
                
                if self._validate_extraction(before, expected, after):
                    # 分析函数调用的AST信息
                    call_info = self._analyze_function_call_ast(line, ast_info, i, available_functions)
                    
                    metadata = self._build_metadata(
                        "call_graph_completion",
                        masking_type="function_call",
                        line_number=i+1,
                        **call_info
                    )
                    
                    extractions.append({
                        "before": before,
                        "expected": expected,
                        "after": after,
                        "metadata": metadata
                    })
        
        return extractions
    
    def _is_function_call(self, line: str) -> bool:
        """判断是否为函数调用"""
        line = line.strip()
        return '(' in line and ')' in line and ';' in line
    
    def _extract_function_call(self, line: str) -> str:
        """提取函数调用"""
        # 使用tree-sitter解析的函数调用提取
        if '(' in line:
            func_name = line[:line.find('(')].strip()
            return func_name
        return line
    
    def _analyze_function_call_ast(self, line: str, ast_info: Dict[str, Any], line_index: int, available_functions: List[str]) -> Dict[str, Any]:
        """分析函数调用的AST信息"""
        call_info = {
            "ast_parent_type": "function_call",
            "target_is_expression": False,
            "target_token_count": len(line.split()),
            "refers_to_function": [],
            "refers_to_variable": []
        }
        
        # 从AST中查找对应的函数调用信息
        for call in ast_info["function_calls"]:
            if call["start_line"] == line_index:
                call_info["refers_to_function"].append(call["name"])
                break
        
        # 检查函数是否在可用函数列表中
        func_name = self._extract_function_call(line)
        if func_name in available_functions:
            call_info["function_available"] = True
        else:
            call_info["function_available"] = False
        
        # 检查是否包含变量引用
        for var in ast_info["variables"]:
            if var["name"] in line:
                call_info["refers_to_variable"].append(var["name"])
        
        return call_info 