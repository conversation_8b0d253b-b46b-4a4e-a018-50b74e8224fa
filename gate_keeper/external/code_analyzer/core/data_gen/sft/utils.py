"""
SFT模块公共工具类

提供SFT数据生成过程中使用的公共工具函数，基于tree-sitter解析
"""

import json
import os
import random
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

from tqdm import tqdm

from core.ast_parser import ASTParserFactory
from models.call_relation import FunctionCall
from models.function import Function
from models.macro import Macro
from models.struct import Struct, StructUsage


class SFTUtils:
    """SFT工具类 - 基于tree-sitter解析"""
    
    @staticmethod
    def extract_function_calls(text: str, file_path: str = "", language: str = "np") -> List[str]:
        """提取函数调用 - 使用tree-sitter解析"""
        try:
            # 如果没有文件路径，或者传入的是代码片段（不是完整文件），使用降级方案
            # 判断是否为代码片段：检查是否包含完整的函数定义结构
            is_code_fragment = (not file_path or 
                              len(text) < 200 or  # 短文本
                              not any(keyword in text for keyword in ['void ', 'function ', 'bundle ', 'inline ']) or  # 没有函数定义关键字
                              text.count('{') != text.count('}'))  # 括号不匹配
            if is_code_fragment:
                import re
                pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
                matches = re.findall(pattern, text)
                # 过滤掉一些明显不是函数调用的内容
                filtered_calls = []
                for call in set(matches):
                    if (call and 
                        len(call) > 1 and
                        call not in ['if', 'for', 'while', 'return', 'break', 'continue', 'sizeof']):
                        filtered_calls.append(call)
                return filtered_calls
            
            # 根据文件扩展名确定语言
            if file_path.endswith('.asm'):
                language = "np"
            elif file_path.endswith(('.c', '.h')):
                language = "c"
            elif file_path.endswith('.py'):
                language = "python"
            
            # 创建对应的解析器
            parser = ASTParserFactory.create(language)
            
            # 提取函数调用 - 传入代码片段作为file_content
            calls = parser.extract_calls(file_path, file_content=text)
            
            # 返回被调用的函数名列表
            return list(set([call.callee for call in calls if call.callee]))
            
        except Exception as e:
            print(f"    使用tree-sitter提取函数调用失败: {e}")
            # 降级方案
            import re
            pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
            matches = re.findall(pattern, text)
            # 过滤掉一些明显不是函数调用的内容
            filtered_calls = []
            for call in set(matches):
                if (call and 
                    len(call) > 1 and
                    call not in ['if', 'for', 'while', 'return', 'break', 'continue', 'sizeof']):
                    filtered_calls.append(call)
            return filtered_calls
    
    @staticmethod
    def extract_variables(text: str, file_path: str = "", language: str = "np") -> List[str]:
        """提取变量 - 使用tree-sitter解析（支持顶级变量提取）"""
        try:
            # 如果没有文件路径，或者传入的是代码片段（不是完整文件），使用降级方案
            # 判断是否为代码片段：检查是否包含完整的函数定义结构
            is_code_fragment = (not file_path or 
                              len(text) < 200 or  # 短文本
                              not any(keyword in text for keyword in ['void ', 'function ', 'bundle ', 'inline ']) or  # 没有函数定义关键字
                              text.count('{') != text.count('}'))  # 括号不匹配
            if is_code_fragment:
                import re
                var_patterns = [
                    r'\b(?:int|uint8|uint16|uint32|char|float|double)\s+(\w+)\s*[=;]',  # 变量声明
                    r'\b(?:let|var|const)\s+(\w+)\s*[=:]',  # 其他语言变量声明
                    r'\bmove\s+(\w+)\s*=',  # NP汇编的move指令
                    r'\b(\w+)\s*=\s*[^=]',  # 赋值语句
                    r'\b(\w+)\s*[:=]\s*[^=]',  # 冒号赋值
                ]
                variables = []
                for pattern in var_patterns:
                    matches = re.findall(pattern, text)
                    variables.extend(matches)
                
                # 过滤掉一些明显不是变量的内容
                filtered_variables = []
                for var in set(variables):
                    if (var and 
                        not var.startswith('_') and 
                        not var.isupper() and  # 排除全大写（通常是常量）
                        len(var) > 1 and
                        var not in ['if', 'for', 'while', 'return', 'break', 'continue']):
                        # 提取顶级变量
                        top_level_var = SFTUtils._extract_top_level_variable(var)
                        if top_level_var:
                            filtered_variables.append(top_level_var)
                
                return filtered_variables
            
            # 根据文件扩展名确定语言
            if file_path.endswith('.asm'):
                language = "np"
            elif file_path.endswith(('.c', '.h')):
                language = "c"
            elif file_path.endswith('.py'):
                language = "python"
            
            # 创建对应的解析器
            parser = ASTParserFactory.create(language)
            
            # 提取函数定义，从中获取变量信息 - 传入代码片段作为file_content
            functions = parser.extract_functions(file_path, file_content=text)
            
            variables = []
            for func in functions:
                # 从函数参数中提取变量
                if func.signature and func.signature.parameters:
                    for param in func.signature.parameters:
                        if param.name:
                            # 提取顶级变量
                            top_level_var = SFTUtils._extract_top_level_variable(param.name)
                            if top_level_var:
                                variables.append(top_level_var)
                
                # 从函数代码中提取局部变量（这里需要更复杂的AST分析）
                # 暂时使用简单的文本分析作为补充
                if func.code:
                    # 简单的变量声明模式匹配（作为tree-sitter的补充）
                    import re
                    var_patterns = [
                        r'\b(?:int|uint8|uint16|uint32|char|float|double)\s+(\w+)\s*[=;]',
                        r'\b(?:let|var|const)\s+(\w+)\s*[=:]',
                        r'\b(\w+)\s*=\s*[^=]'  # 赋值语句
                    ]
                    
                    for pattern in var_patterns:
                        matches = re.findall(pattern, func.code)
                        for match in matches:
                            # 提取顶级变量
                            top_level_var = SFTUtils._extract_top_level_variable(match)
                            if top_level_var:
                                variables.append(top_level_var)
            
            return list(set(variables))
            
        except Exception as e:
            print(f"    使用tree-sitter提取变量失败: {e}")
            # 降级方案
            import re
            var_patterns = [
                r'\b(?:int|uint8|uint16|uint32|char|float|double)\s+(\w+)\s*[=;]',  # 变量声明
                r'\b(?:let|var|const)\s+(\w+)\s*[=:]',  # 其他语言变量声明
                r'\bmove\s+(\w+)\s*=',  # NP汇编的move指令
                r'\b(\w+)\s*=\s*[^=]',  # 赋值语句
                r'\b(\w+)\s*[:=]\s*[^=]',  # 冒号赋值
            ]
            variables = []
            for pattern in var_patterns:
                matches = re.findall(pattern, text)
                variables.extend(matches)
            
            # 过滤掉一些明显不是变量的内容
            filtered_variables = []
            for var in set(variables):
                if (var and 
                    not var.startswith('_') and 
                    not var.isupper() and  # 排除全大写（通常是常量）
                    len(var) > 1 and
                    var not in ['if', 'for', 'while', 'return', 'break', 'continue']):
                    # 提取顶级变量
                    top_level_var = SFTUtils._extract_top_level_variable(var)
                    if top_level_var:
                        filtered_variables.append(top_level_var)
            
            return filtered_variables
    
    @staticmethod
    def _extract_top_level_variable(var_name: str) -> str:
        """提取顶级变量标识符，避免变量爆炸"""
        if not var_name:
            return ""
        
        # 移除属性访问，只保留顶级变量
        # 例如：a.b.c -> a, self.data.cache -> self.data, config.items -> config
        parts = var_name.split('.')
        
        # 特殊处理：保留self.前缀的类属性
        if len(parts) >= 2 and parts[0] == 'self':
            # self.data.cache -> self.data
            return '.'.join(parts[:2])
        
        # 其他情况：只保留第一部分
        return parts[0]
    
    @staticmethod
    def extract_macros(text: str, file_path: str = "", language: str = "np") -> List[str]:
        """提取宏 - 使用tree-sitter解析"""
        try:
            # 如果没有文件路径，或者传入的是代码片段（不是完整文件），使用降级方案
            if not file_path or len(text) < 100:  # 简单判断：如果文本太短，可能是代码片段
                import re
                pattern = r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)'
                matches = re.findall(pattern, text)
                return list(set(matches))
            
            # 根据文件扩展名确定语言
            if file_path.endswith('.asm'):
                language = "np"
            elif file_path.endswith(('.c', '.h')):
                language = "c"
            elif file_path.endswith('.py'):
                language = "python"
            
            # 创建对应的解析器
            parser = ASTParserFactory.create(language)
            
            # 提取宏定义 - 传入代码片段作为file_content
            macros = parser.extract_macros(file_path, file_content=text)
            
            # 返回宏名列表
            return list(set([macro.name for macro in macros if macro.name]))
            
        except Exception as e:
            print(f"    使用tree-sitter提取宏失败: {e}")
            # 降级方案
            import re
            pattern = r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)'
            matches = re.findall(pattern, text)
            return list(set(matches))
    
    @staticmethod
    def extract_structs(text: str, file_path: str = "", language: str = "np") -> List[str]:
        """提取结构体引用 - 使用tree-sitter解析"""
        try:
            # 如果没有文件路径，或者传入的是代码片段（不是完整文件），使用降级方案
            if not file_path or len(text) < 100:  # 简单判断：如果文本太短，可能是代码片段
                import re
                pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\.'
                matches = re.findall(pattern, text)
                return list(set(matches))
            
            # 根据文件扩展名确定语言
            if file_path.endswith('.asm'):
                language = "np"
            elif file_path.endswith(('.c', '.h')):
                language = "c"
            elif file_path.endswith('.py'):
                language = "python"
            
            # 创建对应的解析器
            parser = ASTParserFactory.create(language)
            
            # 提取结构体定义和使用 - 传入代码片段作为file_content
            structs = parser.extract_structs(file_path, file_content=text)
            struct_usages = parser.extract_struct_usages(file_path, file_content=text)
            
            struct_names = []
            
            # 从结构体定义中提取名称
            for struct in structs:
                if struct.name:
                    struct_names.append(struct.name)
            
            # 从结构体使用中提取名称
            for usage in struct_usages:
                if usage.struct_name:
                    struct_names.append(usage.struct_name)
            
            return list(set(struct_names))
            
        except Exception as e:
            print(f"    使用tree-sitter提取结构体失败: {e}")
            # 降级方案
            import re
            pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\.'
            matches = re.findall(pattern, text)
            return list(set(matches))
    
    @staticmethod
    def extract_functions(text: str, file_path: str = "", language: str = "np") -> List[str]:
        """提取函数定义 - 使用tree-sitter解析"""
        try:
            # 如果没有文件路径，或者传入的是代码片段（不是完整文件），使用降级方案
            if not file_path or len(text) < 100:  # 简单判断：如果文本太短，可能是代码片段
                import re
                patterns = [
                    r'(?:static\s+)?(?:void|int|uint8|uint16|uint32)\s+(\w+)\s*\(',
                    r'function\s+(\w+)',
                    r'bundle\s+(\w+)'
                ]
                functions = []
                for pattern in patterns:
                    matches = re.findall(pattern, text)
                    functions.extend(matches)
                return list(set(functions))
            
            # 根据文件扩展名确定语言
            if file_path.endswith('.asm'):
                language = "np"
            elif file_path.endswith(('.c', '.h')):
                language = "c"
            elif file_path.endswith('.py'):
                language = "python"
            
            # 创建对应的解析器
            parser = ASTParserFactory.create(language)
            
            # 提取函数定义 - 传入代码片段作为file_content
            functions = parser.extract_functions(file_path, file_content=text)
            
            # 返回函数名列表
            return list(set([func.name for func in functions if func.name]))
            
        except Exception as e:
            print(f"    使用tree-sitter提取函数失败: {e}")
            # 降级方案
            import re
            patterns = [
                r'(?:static\s+)?(?:void|int|uint8|uint16|uint32)\s+(\w+)\s*\(',
                r'function\s+(\w+)',
                r'bundle\s+(\w+)'
            ]
            functions = []
            for pattern in patterns:
                matches = re.findall(pattern, text)
                functions.extend(matches)
            return list(set(functions))
    
    @staticmethod
    def is_control_flow_statement(statement: str) -> bool:
        """判断是否为控制流语句"""
        control_keywords = ['if', 'else', 'for', 'while', 'switch', 'case', 'break', 'continue', 'return']
        return any(keyword in statement.lower() for keyword in control_keywords)
    
    @staticmethod
    def has_comment_hint(statement: str) -> bool:
        """判断是否包含注释提示"""
        return '//' in statement or '/*' in statement
    
    @staticmethod
    def calculate_complexity_score(func: Function) -> float:
        """计算复杂度分数 - 基于tree-sitter解析结果"""
        try:
            if not func.code:
                return 0.5
            
            lines = func.code.split('\n')
            
            # 基础分数
            base_score = len(lines) / 100.0
            
            # 控制流复杂度
            control_flow_count = sum(1 for line in lines if SFTUtils.is_control_flow_statement(line))
            control_score = control_flow_count / 10.0
            
            # 函数调用复杂度 - 使用tree-sitter解析
            function_calls = SFTUtils.extract_function_calls(func.code, func.file_path)
            call_score = len(function_calls) / 20.0
            
            # 变量复杂度 - 使用tree-sitter解析
            variables = SFTUtils.extract_variables(func.code, func.file_path)
            var_score = len(variables) / 30.0
            
            # 参数复杂度
            param_score = 0.0
            if func.signature and func.signature.parameters:
                param_score = len(func.signature.parameters) / 10.0
            
            # 综合分数
            total_score = min(base_score + control_score + call_score + var_score + param_score, 1.0)
            
            return round(total_score, 3)
        except Exception:
            return 0.5
    
    @staticmethod
    def build_context_list(func: Function, file_path: str) -> List[str]:
        """构建上下文列表 - 基于tree-sitter解析结果"""
        context_list = []
        
        try:
            # 添加函数名
            context_list.append(f"function: {func.name}")
            
            # 添加文件路径
            context_list.append(f"file: {file_path}")
            
            # 添加函数签名
            if func.signature:
                signature_parts = []
                if func.signature.return_type:
                    signature_parts.append(func.signature.return_type)
                signature_parts.append(func.name)
                if func.signature.parameters:
                    param_str = ", ".join([f"{p.type} {p.name}" if p.type else p.name 
                                         for p in func.signature.parameters])
                    signature_parts.append(f"({param_str})")
                else:
                    signature_parts.append("()")
                
                context_list.append(f"signature: {' '.join(signature_parts)}")
            
            # 添加函数调用 - 使用tree-sitter解析
            function_calls = SFTUtils.extract_function_calls(func.code, file_path)
            if function_calls:
                context_list.append(f"calls: {', '.join(function_calls)}")
            
            # 添加变量 - 使用tree-sitter解析
            variables = SFTUtils.extract_variables(func.code, file_path)
            if variables:
                context_list.append(f"variables: {', '.join(variables)}")
            
            # 添加宏使用 - 使用tree-sitter解析
            macros = SFTUtils.extract_macros(func.code, file_path)
            if macros:
                context_list.append(f"macros: {', '.join(macros)}")
            
            # 添加结构体使用 - 使用tree-sitter解析
            structs = SFTUtils.extract_structs(func.code, file_path)
            if structs:
                context_list.append(f"structs: {', '.join(structs)}")
            
        except Exception as e:
            print(f"    构建上下文列表时出错: {e}")
        
        return context_list
    
    @staticmethod
    def validate_data_quality(before: str, expected: str, after: str, 
                            min_before_length: int = 10, 
                            min_expected_length: int = 1,
                            min_after_length: int = 5) -> bool:
        """验证数据质量"""
        # 检查长度
        if len(before.strip()) < min_before_length:
            return False
        
        if len(expected.strip()) < min_expected_length:
            return False
        
        if len(after.strip()) < min_after_length:
            return False
        
        # 检查内容
        if not before.strip() or not expected.strip() or not after.strip():
            return False
        
        # 检查是否包含有效代码
        if not any(char in expected for char in [';', '{', '}', '(', ')', '=']):
            return False
        
        return True
    
    @staticmethod
    def extract_function_body_lines(lines: List[str]) -> List[str]:
        """提取函数体行"""
        body_lines = []
        brace_count = 0
        in_function = False
        
        for line in lines:
            stripped_line = line.strip()
            
            # 跳过空行和注释
            if not stripped_line or stripped_line.startswith('//') or stripped_line.startswith('/*'):
                continue
            
            # 检测函数开始
            if '{' in stripped_line and not in_function:
                in_function = True
            
            if in_function:
                body_lines.append(line)
                brace_count += stripped_line.count('{') - stripped_line.count('}')
                
                # 检测函数结束
                if brace_count <= 0:
                    break
        
        return body_lines
    
    @staticmethod
    def get_random_seed() -> int:
        """获取随机种子"""
        return random.randint(1, 10000)
    
    @staticmethod
    def set_random_seed(seed: int):
        """设置随机种子"""
        random.seed(seed)
    
    @staticmethod
    def get_call_chain(func_name: str, functions_cache: Dict[str, List[Function]], 
                      calls_cache: Dict[str, List[FunctionCall]], max_depth: int = 5) -> List[str]:
        """获取调用链 - 基于tree-sitter解析结果"""
        call_chain = []
        
        def dfs(current_func: str, depth: int):
            if depth > max_depth:
                return
            
            if current_func in call_chain:
                return
            
            call_chain.append(current_func)
            
            # 查找调用关系
            for file_path, file_calls in calls_cache.items():
                for call in file_calls:
                    if call.caller == current_func:
                        dfs(call.callee, depth + 1)
        
        dfs(func_name, 0)
        return call_chain
    
    @staticmethod
    def get_variable_references(func: Function, file_path: str) -> List[str]:
        """获取变量引用 - 基于tree-sitter解析结果"""
        try:
            variables = SFTUtils.extract_variables(func.code, file_path)
            return variables[:10]  # 限制数量
        except Exception:
            return []
    
    @staticmethod
    def get_related_structs(func: Function, file_path: str) -> List[str]:
        """获取相关结构体 - 基于tree-sitter解析结果"""
        try:
            structs = SFTUtils.extract_structs(func.code, file_path)
            return structs[:5]  # 限制数量
        except Exception:
            return []


class DataSaver:
    """通用数据保存工具
    
    支持按条数分文件存储，避免单文件过大导致的性能问题
    """
    
    def __init__(self, 
                 max_items_per_file: int = 10000,
                 output_dir: Optional[str] = None,
                 filename_prefix: str = "data",
                 filename_suffix: str = "json"):
        """
        初始化数据保存器
        
        Args:
            max_items_per_file: 每个文件最大条数，默认10000
            output_dir: 输出目录，如果为None则使用当前目录
            filename_prefix: 文件名前缀
            filename_suffix: 文件扩展名（不包含点）
        """
        self.max_items_per_file = max_items_per_file
        self.output_dir = Path(output_dir) if output_dir else Path.cwd()
        self.filename_prefix = filename_prefix
        self.filename_suffix = filename_suffix
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def save_data(self, 
                  data: List[Any], 
                  to_dict_func: Optional[Callable[[Any], Dict]] = None,
                  show_progress: bool = True) -> List[str]:
        """
        保存数据到多个文件
        
        Args:
            data: 要保存的数据列表
            to_dict_func: 将数据项转换为字典的函数，如果为None则直接使用数据项
            show_progress: 是否显示进度条
            
        Returns:
            保存的文件路径列表
        """
        if not data:
            print("  没有数据需要保存")
            return []
        
        total_items = len(data)
        num_files = (total_items + self.max_items_per_file - 1) // self.max_items_per_file
        
        print(f"  数据总量: {total_items} 条")
        print(f"  分文件数: {num_files} 个")
        print(f"  每文件最大条数: {self.max_items_per_file}")
        
        saved_files = []
        
        # 使用进度条
        iterator = tqdm(range(num_files), desc="保存文件") if show_progress else range(num_files)
        
        for file_index in iterator:
            start_idx = file_index * self.max_items_per_file
            end_idx = min(start_idx + self.max_items_per_file, total_items)
            
            # 生成文件名
            if num_files == 1:
                filename = f"{self.filename_prefix}.{self.filename_suffix}"
            else:
                filename = f"{self.filename_prefix}_part{file_index + 1:03d}.{self.filename_suffix}"
            
            file_path = self.output_dir / filename
            
            # 提取当前文件的数据
            file_data = data[start_idx:end_idx]
            
            # 转换为字典格式
            if to_dict_func:
                dict_data = [to_dict_func(item) for item in file_data]
            else:
                dict_data = file_data
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(dict_data, f, ensure_ascii=False, indent=2)
            
            saved_files.append(str(file_path))
            
            if show_progress:
                iterator.set_postfix({
                    'file': filename,
                    'items': len(file_data)
                })
        
        print(f"  已保存到 {len(saved_files)} 个文件")
        return saved_files
    
    def save_jsonl_data(self, 
                       data: List[Any], 
                       to_dict_func: Optional[Callable[[Any], Dict]] = None,
                       show_progress: bool = True) -> List[str]:
        """
        保存数据到多个JSONL文件（每行一个JSON）
        
        Args:
            data: 要保存的数据列表
            to_dict_func: 将数据项转换为字典的函数，如果为None则直接使用数据项
            show_progress: 是否显示进度条
            
        Returns:
            保存的文件路径列表
        """
        if not data:
            print("  没有数据需要保存")
            return []
        
        total_items = len(data)
        num_files = (total_items + self.max_items_per_file - 1) // self.max_items_per_file
        
        print(f"  数据总量: {total_items} 条")
        print(f"  分文件数: {num_files} 个")
        print(f"  每文件最大条数: {self.max_items_per_file}")
        
        saved_files = []
        
        # 使用进度条
        iterator = tqdm(range(num_files), desc="保存JSONL文件") if show_progress else range(num_files)
        
        for file_index in iterator:
            start_idx = file_index * self.max_items_per_file
            end_idx = min(start_idx + self.max_items_per_file, total_items)
            
            # 生成文件名
            if num_files == 1:
                filename = f"{self.filename_prefix}.jsonl"
            else:
                filename = f"{self.filename_prefix}_part{file_index + 1:03d}.jsonl"
            
            file_path = self.output_dir / filename
            
            # 提取当前文件的数据
            file_data = data[start_idx:end_idx]
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                for item in file_data:
                    if to_dict_func:
                        dict_item = to_dict_func(item)
                    else:
                        dict_item = item
                    
                    json.dump(dict_item, f, ensure_ascii=False)
                    f.write('\n')
            
            saved_files.append(str(file_path))
            
            if show_progress:
                iterator.set_postfix({
                    'file': filename,
                    'items': len(file_data)
                })
        
        print(f"  已保存到 {len(saved_files)} 个JSONL文件")
        return saved_files
    
    def get_file_info(self, data: List[Any]) -> Dict[str, Any]:
        """
        获取文件分割信息
        
        Args:
            data: 数据列表
            
        Returns:
            文件分割信息字典
        """
        if not data:
            return {
                "total_items": 0,
                "num_files": 0,
                "max_items_per_file": self.max_items_per_file,
                "files": []
            }
        
        total_items = len(data)
        num_files = (total_items + self.max_items_per_file - 1) // self.max_items_per_file
        
        files_info = []
        for file_index in range(num_files):
            start_idx = file_index * self.max_items_per_file
            end_idx = min(start_idx + self.max_items_per_file, total_items)
            
            if num_files == 1:
                filename = f"{self.filename_prefix}.{self.filename_suffix}"
            else:
                filename = f"{self.filename_prefix}_part{file_index + 1:03d}.{self.filename_suffix}"
            
            files_info.append({
                "file_index": file_index,
                "filename": filename,
                "start_index": start_idx,
                "end_index": end_idx,
                "item_count": end_idx - start_idx
            })
        
        return {
            "total_items": total_items,
            "num_files": num_files,
            "max_items_per_file": self.max_items_per_file,
            "files": files_info
        }


class SFTDataSaver(DataSaver):
    """SFT数据专用保存器
    
    继承自DataSaver，提供SFT数据特定的保存功能
    """
    
    def __init__(self, 
                 max_items_per_file: int = 10000,
                 output_dir: Optional[str] = None,
                 data_type: str = "sft"):
        """
        初始化SFT数据保存器
        
        Args:
            max_items_per_file: 每个文件最大条数，默认10000
            output_dir: 输出目录
            data_type: 数据类型（sft, alpaca, prompt_sft等）
        """
        filename_prefix = f"{data_type}_data"
        super().__init__(max_items_per_file, output_dir, filename_prefix, "json")
        self.data_type = data_type
    
    def save_sft_data(self, 
                     data: List[Any], 
                     show_progress: bool = True) -> List[str]:
        """
        保存SFT数据
        
        Args:
            data: SFT数据列表
            show_progress: 是否显示进度条
            
        Returns:
            保存的文件路径列表
        """
        # 假设数据项有to_dict方法
        return self.save_data(data, lambda item: item.to_dict(), show_progress)
    
    def save_alpaca_data(self, 
                        data: List[Any], 
                        alpaca_format_only: bool = False,
                        show_progress: bool = True) -> List[str]:
        """
        保存Alpaca格式数据
        
        Args:
            data: Alpaca数据列表
            alpaca_format_only: 是否只保存标准Alpaca格式
            show_progress: 是否显示进度条
            
        Returns:
            保存的文件路径列表
        """
        if alpaca_format_only:
            to_dict_func = lambda item: item.to_alpaca_dict()
        else:
            to_dict_func = lambda item: item.to_dict()
        
        return self.save_data(data, to_dict_func, show_progress)
    
    def save_jsonl_alpaca_data(self, 
                              data: List[Any], 
                              alpaca_format_only: bool = False,
                              show_progress: bool = True) -> List[str]:
        """
        保存Alpaca数据为JSONL格式
        
        Args:
            data: Alpaca数据列表
            alpaca_format_only: 是否只保存标准Alpaca格式
            show_progress: 是否显示进度条
            
        Returns:
            保存的文件路径列表
        """
        if alpaca_format_only:
            to_dict_func = lambda item: item.to_alpaca_dict()
        else:
            to_dict_func = lambda item: item.to_dict()
        
        return self.save_jsonl_data(data, to_dict_func, show_progress)


class StreamingDataSaver:
    """流式数据保存器
    
    支持逐条保存数据，避免将所有数据加载到内存中
    """
    
    def __init__(self, 
                 output_file: str,
                 max_items_per_file: int = 10000,
                 file_format: str = "jsonl"):
        """
        初始化流式数据保存器
        
        Args:
            output_file: 输出文件路径
            max_items_per_file: 每个文件最大条数，默认10000
            file_format: 文件格式，"json" 或 "jsonl"
        """
        self.output_file = Path(output_file)
        self.max_items_per_file = max_items_per_file
        self.file_format = file_format.lower()
        
        # 确保输出目录存在
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 当前文件信息
        self.current_file_index = 0
        self.current_file_items = 0
        self.current_file = None
        self.current_file_path = None
        
        # 统计信息
        self.total_items = 0
        self.saved_files = []
        
        # 开始第一个文件
        self._start_new_file()
    
    def _start_new_file(self):
        """开始新文件"""
        if self.current_file:
            self.current_file.close()
        
        # 生成文件名
        if self.max_items_per_file == float('inf'):
            # 不分文件
            filename = self.output_file.name
        else:
            # 分文件
            stem = self.output_file.stem
            suffix = self.output_file.suffix
            filename = f"{stem}_part{self.current_file_index + 1:03d}{suffix}"
        
        self.current_file_path = self.output_file.parent / filename
        self.current_file = open(self.current_file_path, 'w', encoding='utf-8')
        self.saved_files.append(str(self.current_file_path))
        
        # 如果是JSON格式，写入数组开始标记
        if self.file_format == "json":
            self.current_file.write("[\n")
        
        print(f"  开始新文件: {filename}")
    
    def _close_current_file(self):
        """关闭当前文件"""
        if not self.current_file:
            return
        
        # 如果是JSON格式，写入数组结束标记
        if self.file_format == "json":
            self.current_file.write("\n]")
        
        self.current_file.close()
        self.current_file = None
        
        print(f"  完成文件: {self.current_file_path.name} ({self.current_file_items} 条)")
    
    def save_item(self, item: Any, to_dict_func: Optional[Callable[[Any], Dict]] = None):
        """
        保存单个数据项
        
        Args:
            item: 要保存的数据项
            to_dict_func: 将数据项转换为字典的函数
        """
        # 检查是否需要开始新文件
        if (self.current_file_items >= self.max_items_per_file and 
            self.max_items_per_file != float('inf')):
            self._close_current_file()
            self.current_file_index += 1
            self.current_file_items = 0
            self._start_new_file()
        
        # 转换为字典
        if to_dict_func:
            dict_item = to_dict_func(item)
        else:
            dict_item = item
        
        # 写入数据
        if self.file_format == "json":
            # JSON格式：添加逗号分隔符
            if self.current_file_items > 0:
                self.current_file.write(",\n")
            json.dump(dict_item, self.current_file, ensure_ascii=False, indent=2)
        else:
            # JSONL格式：每行一个JSON
            json.dump(dict_item, self.current_file, ensure_ascii=False)
            self.current_file.write('\n')
        
        self.current_file_items += 1
        self.total_items += 1
    
    def save_items(self, items: List[Any], to_dict_func: Optional[Callable[[Any], Dict]] = None):
        """
        批量保存数据项
        
        Args:
            items: 要保存的数据项列表
            to_dict_func: 将数据项转换为字典的函数
        """
        for item in items:
            self.save_item(item, to_dict_func)
    
    def close(self):
        """关闭保存器"""
        if self.current_file:
            self._close_current_file()
        
        print(f"  保存完成，总计 {self.total_items} 条数据到 {len(self.saved_files)} 个文件")
        return self.saved_files
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


class StreamingSFTDataSaver(StreamingDataSaver):
    """SFT数据流式保存器
    
    继承自StreamingDataSaver，提供SFT数据特定的保存功能
    """
    
    def __init__(self, 
                 output_file: str,
                 max_items_per_file: int = 10000,
                 file_format: str = "jsonl",
                 data_type: str = "sft"):
        """
        初始化SFT数据流式保存器
        
        Args:
            output_file: 输出文件路径
            max_items_per_file: 每个文件最大条数，默认10000
            file_format: 文件格式，"json" 或 "jsonl"
            data_type: 数据类型（sft, alpaca, prompt_sft等）
        """
        super().__init__(output_file, max_items_per_file, file_format)
        self.data_type = data_type
    
    def save_sft_item(self, item: Any):
        """保存SFT数据项"""
        self.save_item(item, lambda x: x.to_dict())
    
    def save_alpaca_item(self, item: Any, alpaca_format_only: bool = False):
        """保存Alpaca数据项"""
        if alpaca_format_only:
            to_dict_func = lambda x: x.to_alpaca_dict()
        else:
            to_dict_func = lambda x: x.to_dict()
        
        self.save_item(item, to_dict_func)