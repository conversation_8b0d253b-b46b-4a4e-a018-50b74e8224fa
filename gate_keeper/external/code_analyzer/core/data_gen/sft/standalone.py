"""
SFT数据生成模块 - 独立版本

完全独立的SFT数据生成实现，不依赖现有core模块
"""

import json
import os
import random
import re
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional


@dataclass
class ContextNode:
    """代码图中的上下文节点"""
    node_type: str  # function / struct / var / macro 等
    name: str
    filepath: str
    code: str
    scope: str = ""  # 变量作用域
    reference_line: int = 0  # 引用位置


@dataclass
class RelatedStruct:
    """相关结构体信息"""
    name: str
    filepath: str
    code: str


@dataclass
class DependencyNode:
    """依赖图节点"""
    id: str
    type: str  # function / variable / struct / macro


@dataclass
class DependencyEdge:
    """依赖图边"""
    source: str
    target: str
    relation: str  # calls / uses / contains / references


@dataclass
class DependencyGraph:
    """依赖图结构"""
    nodes: List[DependencyNode]
    edges: List[DependencyEdge]


@dataclass
class EnhancedStatementMaskData:
    """增强的语句遮盖数据结构 - 符合SFT数据设计规范v2.3"""
    # 核心遮盖内容（顶层结构）
    before: str
    expected: str
    after: str
    
    # 上下文建模结构
    context_nodes: List[ContextNode]
    dependency_graph: DependencyGraph  # 显式图结构
    
    # 元数据信息（包含所有控制与描述信息）
    metadata: Dict[str, Any]
    
    # 兼容性字段（保持向后兼容）
    context_list: List[str] = None
    statement_type: str = None


@dataclass
class RealStatementMaskData:
    """真实的语句遮盖数据结构（保持向后兼容）"""
    context_list: List[str]
    filepath: str
    before: str
    expected: str
    after: str
    function_name: str
    statement_type: str
    line_number: int
    # 真实分析字段
    call_chain: List[str]
    related_structs: List[str]
    variable_references: List[str]
    complexity_score: float
    ast_node_type: str


class StandaloneNPAnalyzer:
    """独立的NP项目分析器"""
    
    def __init__(self, project_path: str):
        """初始化分析器"""
        self.project_path = project_path
        
        # 缓存分析结果
        self._functions_cache = {}
        self._calls_cache = {}
        self._structs_cache = {}
        self._usages_cache = {}
        
        print("✓ 成功创建独立NP解析器")
    
    def analyze_project(self):
        """分析整个项目"""
        print(f"开始分析NP项目: {self.project_path}")
        
        # 1. 查找NP文件
        np_files = self._find_np_files()
        print(f"找到 {len(np_files)} 个NP文件")
        
        if not np_files:
            print("❌ 未找到NP文件")
            return None
        
        # 2. 解析每个文件
        all_functions = []
        all_calls = []
        all_structs = []
        all_usages = []
        
        for file_path in np_files:
            print(f"解析文件: {file_path}")
            
            # 显示文件内容前几行
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"  文件大小: {len(content)} 字符")
                print(f"  前200字符: {content[:200]}...")
            except Exception as e:
                print(f"  读取文件失败: {e}")
            
            try:
                # 使用简单的文本解析
                functions = self._extract_functions_simple(file_path, content)
                calls = self._extract_calls_simple(file_path, content)
                
                all_functions.extend(functions)
                all_calls.extend(calls)
                all_structs.extend([])  # 暂时为空
                all_usages.extend([])   # 暂时为空
                
                self._functions_cache[file_path] = functions
                self._calls_cache[file_path] = calls
                self._structs_cache[file_path] = []
                self._usages_cache[file_path] = []
                
                print(f"  ✓ 提取到 {len(functions)} 个函数")
                print(f"  ✓ 提取到 {len(calls)} 个函数调用")
                
            except Exception as e:
                print(f"  ❌ 解析文件失败: {e}")
                continue
        
        print(f"总计提取到 {len(all_functions)} 个函数")
        print(f"总计提取到 {len(all_calls)} 个函数调用")
        
        return {
            'functions': all_functions,
            'calls': all_calls,
            'structs': all_structs,
            'usages': all_usages,
        }
    
    def _find_np_files(self) -> List[str]:
        """查找NP文件"""
        np_files = []
        for root, dirs, files in os.walk(self.project_path):
            for file in files:
                if file.endswith('.asm'):
                    np_files.append(os.path.join(root, file))
        return np_files
    
    def _extract_functions_simple(self, file_path: str, content: str) -> List[Any]:
        """简单函数提取"""
        functions = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检测函数定义
            if (line.startswith('void ') or line.startswith('int ') or 
                line.startswith('static void ') or line.startswith('static int ') or
                line.startswith('uint8 ') or line.startswith('uint16 ') or line.startswith('uint32 ')):
                
                func_name = self._extract_function_name_from_line(line)
                if func_name:
                    # 查找函数结束位置
                    end_line = self._find_function_end(lines, i)
                    
                    # 提取函数代码
                    func_code = '\n'.join(lines[i:end_line+1])
                    
                    # 创建简单的函数对象
                    func = {
                        'name': func_name,
                        'filepath': file_path,
                        'start_line': i + 1,
                        'end_line': end_line + 1,
                        'code': func_code,
                        'range': type('obj', (object,), {'start_line': i + 1, 'end_line': end_line + 1})(),
                        'signature': type('obj', (object,), {'parameters': []})()
                    }
                    functions.append(func)
                    print(f"    ✓ 提取函数: {func_name}")
        
        return functions
    
    def _extract_calls_simple(self, file_path: str, content: str) -> List[Any]:
        """简单函数调用提取"""
        calls = []
        lines = content.split('\n')
        current_func = None
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检测函数开始
            if (line.startswith('void ') or line.startswith('int ') or line.startswith('static void ') or 
                line.startswith('static int ') or line.startswith('function ') or line.startswith('bundle ')):
                current_func = self._extract_function_name_from_line(line)
            
            # 检测函数调用
            if current_func and '(' in line and ')' in line:
                # 简单的函数调用检测
                call_match = self._extract_function_call_from_line(line)
                if call_match:
                    call = {
                        'caller': current_func,
                        'callee': call_match,
                        'filepath': file_path,
                        'line_number': i + 1
                    }
                    calls.append(call)
        
        return calls
    
    def _extract_function_name_from_line(self, line: str) -> Optional[str]:
        """从行中提取函数名"""
        # 简单的函数名提取逻辑
        patterns = [
            r'(?:void|int|static void|static int|uint8|uint16|uint32)\s+(\w+)\s*\(',
            r'function\s+(\w+)',
            r'bundle\s+(\w+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_function_call_from_line(self, line: str) -> Optional[str]:
        """从行中提取函数调用"""
        # 简单的函数调用提取逻辑
        patterns = [
            r'(\w+)\s*\(',
            r'(\w+)\s*\([^)]*\)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                func_name = match.group(1)
                # 过滤掉关键字
                if func_name not in ['if', 'for', 'while', 'switch', 'sizeof']:
                    return func_name
        
        return None
    
    def _find_function_end(self, lines: List[str], start_line: int) -> int:
        """查找函数结束位置"""
        brace_count = 0
        started = False
        
        for i in range(start_line, len(lines)):
            line = lines[i].strip()
            
            if '{' in line:
                brace_count += line.count('{')
                started = True
            
            if '}' in line:
                brace_count -= line.count('}')
            
            if started and brace_count == 0:
                return i
        
        return len(lines) - 1
    
    def extract_functions_and_bundles_real(self) -> List[Dict[str, Any]]:
        """提取真实的函数和bundle信息"""
        print("\n=== 提取真实函数和Bundle ===")
        
        functions_data = []
        
        for file_path, functions in self._functions_cache.items():
            for func in functions:
                # 获取真实上下文信息
                context_info = self._get_real_function_context(func, file_path)
                
                func_data = {
                    'name': func['name'],
                    'filepath': func['filepath'],
                    'start_line': func['start_line'],
                    'end_line': func['end_line'],
                    'signature': {
                        'parameters': [],  # 简化处理
                        'return_type': 'void'
                    },
                    'code': func['code'],
                    'context': context_info,
                    'complexity': self._calculate_real_function_complexity(func),
                    'call_chain_depth': self._calculate_real_call_chain_depth(func['name'])
                }
                
                functions_data.append(func_data)
                print(f"  ✓ 函数: {func['name']} (复杂度: {func_data['complexity']:.2f}, 调用链深度: {func_data['call_chain_depth']})")
        
        return functions_data
    
    def _get_real_function_context(self, func: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """获取真实的函数上下文信息"""
        context = {
            'callers': [],
            'callees': [],
            'related_structs': [],
            'related_variables': [],
            'call_chain': [],
            'struct_usages': [],
            'complexity_factors': []
        }
        
        # 查找调用关系
        for call in self._calls_cache.get(file_path, []):
            if call['callee'] == func['name']:
                context['callers'].append(call['caller'])
            elif call['caller'] == func['name']:
                context['callees'].append(call['callee'])
        
        # 构建调用链
        context['call_chain'] = self._build_real_call_chain(func['name'])
        
        # 复杂度因子
        context['complexity_factors'] = self._analyze_real_complexity_factors(func)
        
        return context
    
    def _build_real_call_chain(self, func_name: str, max_depth: int = 5) -> List[str]:
        """构建真实的调用链"""
        chain = [func_name]
        visited = set()
        
        def dfs(current_func, depth):
            if depth >= max_depth or current_func in visited:
                return
            visited.add(current_func)
            
            # 遍历所有文件的调用
            for calls in self._calls_cache.values():
                for call in calls:
                    if call['caller'] == current_func and call['callee'] not in chain:
                        chain.append(call['callee'])
                        dfs(call['callee'], depth + 1)
        
        dfs(func_name, 0)
        return chain
    
    def _calculate_real_function_complexity(self, func: Dict[str, Any]) -> float:
        """计算真实的函数复杂度"""
        if not func['code']:
            return 0.0
        
        complexity = 0.0
        lines = func['code'].split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):
                continue
            
            # 控制流复杂度
            if any(keyword in line for keyword in ['if', 'for', 'while', 'switch']):
                complexity += 1.0
            
            # 函数调用复杂度
            if '(' in line and ')' in line:
                complexity += 0.5
            
            # 嵌套复杂度
            if line.startswith('{'):
                complexity += 0.2
            elif line.startswith('}'):
                complexity -= 0.2
        
        return max(0.0, complexity)
    
    def _calculate_real_call_chain_depth(self, func_name: str) -> int:
        """计算真实的调用链深度"""
        def dfs(current_func, depth):
            max_depth = depth
            visited = set()
            
            for calls in self._calls_cache.values():
                for call in calls:
                    if call['caller'] == current_func and call['callee'] not in visited:
                        visited.add(call['callee'])
                        max_depth = max(max_depth, dfs(call['callee'], depth + 1))
            
            return max_depth
        
        return dfs(func_name, 0)
    
    def _analyze_real_complexity_factors(self, func: Dict[str, Any]) -> List[str]:
        """分析真实的复杂度因子"""
        factors = []
        
        if not func['code']:
            return factors
        
        lines = func['code'].split('\n')
        
        # 检查各种复杂度因子
        if any('if' in line for line in lines):
            factors.append('conditional_statements')
        
        if any('for' in line or 'while' in line for line in lines):
            factors.append('loops')
        
        if any('(' in line and ')' in line for line in lines):
            factors.append('function_calls')
        
        if len(lines) > 50:
            factors.append('large_function')
        
        if not factors:
            factors.append('simple_function')
        
        return factors


class StandaloneSFTDataGenerator:
    """独立的SFT数据生成器"""
    
    def __init__(self, analyzer):
        """初始化生成器"""
        self.analyzer = analyzer
        
        # 配置参数
        self.max_context_nodes = 15
        self.max_before_length = 5000
        self.max_expected_length = 1000
        self.max_after_length = 5000
        self.min_complexity_score = 0.1
        
        # 随机种子
        self.seed = 42
        random.seed(self.seed)
    
    def generate_enhanced_sft_data(self, max_samples: int = 10) -> List[EnhancedStatementMaskData]:
        """生成增强的SFT数据"""
        print(f"\n=== 生成增强SFT数据 (最多{max_samples}个样本) ===")
        
        enhanced_data = []
        
        for file_path, functions in self.analyzer._functions_cache.items():
            for func in functions:
                try:
                    # 生成瀑布式数据
                    waterfall_data = self._generate_enhanced_waterfall_data(func, file_path)
                    enhanced_data.extend(waterfall_data)
                    
                    if len(enhanced_data) >= max_samples:
                        break
                except Exception as e:
                    print(f"    生成函数 {func['name']} 的增强数据时出错: {e}")
                    continue
            
            if len(enhanced_data) >= max_samples:
                break
        
        # 限制样本数量
        enhanced_data = enhanced_data[:max_samples]
        
        print(f"  生成了 {len(enhanced_data)} 个增强SFT数据样本")
        return enhanced_data
    
    def _generate_enhanced_waterfall_data(self, func: Dict[str, Any], file_path: str) -> List[EnhancedStatementMaskData]:
        """生成增强的瀑布式顺序遮盖数据"""
        data = []
        
        if not func['code']:
            return data
        
        lines = func['code'].split('\n')
        func_body_lines = self._extract_function_body_lines(lines)
        
        if len(func_body_lines) < 2:
            return data
        
        # 瀑布式遮盖：逐行遮盖
        for i in range(len(func_body_lines) - 1):
            try:
                before_lines = func_body_lines[:i+1]
                expected_line = func_body_lines[i+1]
                after_lines = func_body_lines[i+2:]
                
                # 构建before、expected、after
                before = '\n'.join(before_lines)
                expected = expected_line
                after = '\n'.join(after_lines)
                
                # 质量检查
                if not self._validate_enhanced_data_quality(before, expected, after):
                    continue
                
                # 构建上下文节点
                context_nodes = self._build_context_nodes(func, file_path)
                
                # 构建依赖图
                dependency_graph = self._build_dependency_graph(func, file_path)
                
                # 构建元数据
                metadata = self._build_enhanced_metadata(
                    func, file_path, "waterfall_sequential", "statement", 
                    before, expected, after, i+1
                )
                
                # 创建增强数据
                enhanced_data = EnhancedStatementMaskData(
                    before=before,
                    expected=expected,
                    after=after,
                    context_nodes=context_nodes,
                    dependency_graph=dependency_graph,
                    metadata=metadata
                )
                
                data.append(enhanced_data)
                
            except Exception as e:
                print(f"    生成瀑布式数据时出错: {e}")
                continue
        
        return data
    
    def _extract_function_body_lines(self, lines: List[str]) -> List[str]:
        """提取函数体行"""
        func_body_lines = []
        in_function = False
        brace_count = 0
        
        for line in lines:
            stripped_line = line.strip()
            
            # 检测函数开始
            if '{' in stripped_line and not in_function:
                in_function = True
                brace_count += stripped_line.count('{')
                continue
            
            if in_function:
                if '{' in stripped_line:
                    brace_count += stripped_line.count('{')
                
                if '}' in stripped_line:
                    brace_count -= stripped_line.count('}')
                    if brace_count == 0:
                        break
                
                # 添加函数体行
                if stripped_line and not stripped_line.startswith('//'):
                    func_body_lines.append(stripped_line)
        
        return func_body_lines
    
    def _validate_enhanced_data_quality(self, before: str, expected: str, after: str) -> bool:
        """验证增强数据质量"""
        # 基本长度检查
        if len(before) > self.max_before_length:
            return False
        
        if len(expected) > self.max_expected_length:
            return False
        
        if len(after) > self.max_after_length:
            return False
        
        # 内容检查
        if not before.strip() or not expected.strip():
            return False
        
        # 检查expected是否为注释
        if expected.strip().startswith('//') or expected.strip().startswith('/*'):
            return False
        
        return True
    
    def _build_context_nodes(self, func: Dict[str, Any], file_path: str) -> List[ContextNode]:
        """构建上下文节点"""
        context_nodes = []
        
        # 添加函数本身
        context_nodes.append(ContextNode(
            node_type="function",
            name=func['name'],
            filepath=file_path,
            code=func['code'][:200] + "..." if len(func['code']) > 200 else func['code'],
            scope="global",
            reference_line=func['start_line']
        ))
        
        # 添加相关函数（从调用关系中获取）
        calls = self.analyzer._calls_cache.get(file_path, [])
        for call in calls:
            if call['caller'] == func['name']:
                context_nodes.append(ContextNode(
                    node_type="function",
                    name=call['callee'],
                    filepath=file_path,
                    code=f"// 调用函数: {call['callee']}",
                    scope="global",
                    reference_line=call['line_number']
                ))
        
        # 限制节点数量
        return context_nodes[:self.max_context_nodes]
    
    def _build_dependency_graph(self, func: Dict[str, Any], file_path: str) -> DependencyGraph:
        """构建依赖图"""
        nodes = []
        edges = []
        
        # 添加函数节点
        nodes.append(DependencyNode(
            id=f"fn_{func['name']}",
            type="function"
        ))
        
        # 添加调用关系
        calls = self.analyzer._calls_cache.get(file_path, [])
        for call in calls:
            if call['caller'] == func['name']:
                # 添加被调用函数节点
                nodes.append(DependencyNode(
                    id=f"fn_{call['callee']}",
                    type="function"
                ))
                
                # 添加调用边
                edges.append(DependencyEdge(
                    source=f"fn_{call['caller']}",
                    target=f"fn_{call['callee']}",
                    relation="calls"
                ))
        
        return DependencyGraph(nodes=nodes, edges=edges)
    
    def _build_enhanced_metadata(self, func: Dict[str, Any], file_path: str, strategy: str, mask_level: str,
                                before: str, expected: str, after: str, line_number: int) -> Dict[str, Any]:
        """构建增强元数据"""
        # 检查是否为控制流语句
        is_control_flow = self._is_control_flow_statement(expected)
        
        # 检查是否包含注释
        comment_hint = self._has_comment_hint(expected)
        
        # 计算复杂度分数
        complexity_score = self._calculate_complexity_score(func)
        
        # 获取调用链
        call_chain = self._get_call_chain(func['name'])
        
        # 获取变量引用
        variable_references = self._get_variable_references(func)
        
        return {
            "task_type": "code_completion",
            "strategy": strategy,
            "mask_level": mask_level,
            "function_name": func['name'],
            "filepath": file_path,
            "line_number": line_number,
            "ast_node_type": "statement",
            "is_control_flow": is_control_flow,
            "comment_hint": comment_hint,
            "complexity_score": complexity_score,
            "call_chain": call_chain,
            "variable_references": variable_references,
            "related_structs": [],
            "data_id": f"np_{random.randint(1000000, 9999999)}",
            "source_type": "inhouse_trace_module",
            "author": "auto_extract",
            "version": "v2.3",
            "seed": self.seed,
            "masking_distribution": "uniform",
            "generation_time": datetime.now().isoformat()
        }
    
    def _is_control_flow_statement(self, statement: str) -> bool:
        """检查是否为控制流语句"""
        control_flow_keywords = ['if', 'for', 'while', 'switch', 'case', 'break', 'continue', 'return']
        statement_lower = statement.lower()
        return any(keyword in statement_lower for keyword in control_flow_keywords)
    
    def _has_comment_hint(self, statement: str) -> bool:
        """检查是否包含注释提示"""
        return '//' in statement or '/*' in statement or '*/' in statement
    
    def _calculate_complexity_score(self, func: Dict[str, Any]) -> float:
        """计算复杂度分数"""
        if not func['code']:
            return 0.0
        
        complexity = 0.0
        lines = func['code'].split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):
                continue
            
            # 控制流复杂度
            if any(keyword in line for keyword in ['if', 'for', 'while', 'switch']):
                complexity += 1.0
            
            # 函数调用复杂度
            if '(' in line and ')' in line:
                complexity += 0.5
            
            # 嵌套复杂度
            if line.startswith('{'):
                complexity += 0.2
            elif line.startswith('}'):
                complexity -= 0.2
        
        return max(0.0, min(1.0, complexity / 10.0))  # 归一化到0-1
    
    def _get_call_chain(self, func_name: str) -> List[str]:
        """获取调用链"""
        chain = [func_name]
        visited = set()
        
        def dfs(current_func, depth):
            if depth >= 5 or current_func in visited:
                return
            visited.add(current_func)
            
            for calls in self.analyzer._calls_cache.values():
                for call in calls:
                    if call['caller'] == current_func and call['callee'] not in chain:
                        chain.append(call['callee'])
                        dfs(call['callee'], depth + 1)
        
        dfs(func_name, 0)
        return chain
    
    def _get_variable_references(self, func: Dict[str, Any]) -> List[str]:
        """获取变量引用"""
        variables = []
        
        if not func['code']:
            return variables
        
        # 简单的变量提取
        lines = func['code'].split('\n')
        for line in lines:
            # 匹配变量声明
            var_match = re.search(r'(uint8|uint16|uint32|int)\s+(\w+)', line)
            if var_match:
                variables.append(var_match.group(2))
        
        return variables


class StandaloneDataQualityValidator:
    """独立的数据质量验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.validation_rules = {
            'required_fields': ['before', 'expected', 'after'],
            'metadata_required_fields': [
                'task_type', 'strategy', 'mask_level', 'function_name', 
                'filepath', 'is_control_flow', 'comment_hint', 'complexity_score'
            ],
            'context_required_fields': ['context_nodes', 'dependency_graph'],
            'max_before_length': 10000,
            'max_expected_length': 1000,
            'max_after_length': 10000
        }
    
    def validate_enhanced_mask_data(self, mask_data: EnhancedStatementMaskData) -> bool:
        """验证增强遮盖数据质量"""
        # 基本验证
        if not mask_data.before.strip() and not mask_data.expected.strip():
            print("    质量检查失败：before和expected都为空")
            return False
        
        if not mask_data.before.strip():
            print("    质量检查失败：before为空")
            return False
        
        if not mask_data.expected.strip():
            print("    质量检查失败：expected为空")
            return False
        
        # 检查上下文节点
        if not mask_data.context_nodes:
            print("    质量检查失败：缺少上下文节点")
            return False
        
        # 检查依赖图
        if not mask_data.dependency_graph.nodes:
            print("    质量检查失败：缺少依赖图节点")
            return False
        
        # 检查metadata中的必需字段
        metadata = mask_data.metadata
        required_fields = self.validation_rules['metadata_required_fields']
        
        for field in required_fields:
            if field not in metadata:
                print(f"    质量检查失败：metadata中缺少{field}字段")
                return False
        
        print("    质量检查通过：所有条件满足")
        return True
    
    def validate_real_mask_data(self, mask_data: RealStatementMaskData) -> bool:
        """验证真实遮盖数据质量"""
        # 基本验证
        if not mask_data.before.strip():
            print("    质量检查失败：before为空")
            return False
        
        if not mask_data.expected.strip():
            print("    质量检查失败：expected为空")
            return False
        
        # 检查expected是否为注释
        if mask_data.expected.strip().startswith('//') or mask_data.expected.strip().startswith('/*'):
            print("    质量检查失败：expected是注释")
            return False
        
        # 检查基本字段
        if not mask_data.function_name:
            print("    质量检查失败：缺少函数名")
            return False
        
        if not mask_data.filepath:
            print("    质量检查失败：缺少文件路径")
            return False
        
        return True 