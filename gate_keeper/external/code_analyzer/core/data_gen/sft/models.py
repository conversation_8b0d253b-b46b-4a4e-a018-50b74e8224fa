"""
SFT数据生成模块 - 数据模型

定义SFT数据生成过程中使用的各种数据结构
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional


@dataclass
class ContextNode:
    """代码图中的上下文节点"""
    node_type: str  # function / struct / var / macro 等
    name: str
    filepath: str
    code: str
    scope: str = ""  # 变量作用域
    reference_line: int = 0  # 引用位置


@dataclass
class RelatedStruct:
    """相关结构体信息"""
    name: str
    filepath: str
    code: str


@dataclass
class DependencyNode:
    """依赖图节点"""
    id: str
    type: str  # function / variable / struct / macro


@dataclass
class DependencyEdge:
    """依赖图边"""
    source: str
    target: str
    relation: str  # calls / uses / contains / references


@dataclass
class DependencyGraph:
    """依赖图结构"""
    nodes: List[DependencyNode]
    edges: List[DependencyEdge]


@dataclass
class EnhancedStatementMaskData:
    """增强的语句遮盖数据结构 - 符合SFT数据设计规范v2.3"""
    # 核心遮盖内容（顶层结构）
    before: str
    expected: str
    after: str
    
    # 上下文建模结构
    context_nodes: List[ContextNode]
    dependency_graph: DependencyGraph  # 显式图结构
    
    # 元数据信息（包含所有控制与描述信息）
    metadata: Dict[str, Any]
    
    # 兼容性字段（保持向后兼容）
    context_list: List[str] = None
    statement_type: str = None


@dataclass
class RealStatementMaskData:
    """真实的语句遮盖数据结构（保持向后兼容）"""
    context_list: List[str]
    filepath: str
    before: str
    expected: str
    after: str
    function_name: str
    statement_type: str
    line_number: int
    # 真实分析字段
    call_chain: List[str]
    related_structs: List[str]
    variable_references: List[str]
    complexity_score: float
    ast_node_type: str 