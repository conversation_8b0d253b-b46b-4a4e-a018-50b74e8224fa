"""
SFT数据提取策略系统 v2.4 - 模块化版本

重新组织为模块化结构，使用tree-sitter进行代码解析
"""

# 导入必要的类型
from typing import Any, Dict, List

from core.data_gen.sft.models import EnhancedStatementMaskData

# 导入新的模块化策略
from .extract_strategies import (BaseExtractionStrategy, ExtractionConfig,
                                 ExtractionStrategy, ExtractionStrategyFactory)


# 创建增强的提取管理器
class EnhancedExtractionManager:
    """增强的提取管理器"""
    
    def __init__(self, config: ExtractionConfig):
        self.config = config
        self.strategy = ExtractionStrategyFactory.create_strategy(config.strategy, config)
    
    def extract_sft_data(self, func_code: str, func_name: str = "", context_info: Dict[str, Any] = None) -> List[EnhancedStatementMaskData]:
        """提取SFT数据"""
        if not func_code:
            return []
        
        # 使用策略提取
        extractions = self.strategy.extract(func_code, func_name, context_info)
        
        # 转换为EnhancedStatementMaskData
        sft_data = []
        for extraction in extractions:
            metadata = extraction["metadata"]
            metadata.update({
                "func_name": func_name,
                "extraction_type": "enhanced"
            })
            
            sft_item = EnhancedStatementMaskData(
                before=extraction["before"],
                expected=extraction["expected"],
                after=extraction["after"],
                context_nodes=[],
                dependency_graph=None,
                metadata=metadata
            )
            
            sft_data.append(sft_item)
        
        return sft_data

__all__ = [
    'ExtractionStrategy',
    'ExtractionConfig', 
    'BaseExtractionStrategy',
    'ExtractionStrategyFactory',
    'EnhancedExtractionManager'
]
 