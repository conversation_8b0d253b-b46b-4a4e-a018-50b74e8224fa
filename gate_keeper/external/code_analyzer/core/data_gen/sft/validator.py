"""
SFT数据生成模块 - 验证器

提供SFT数据质量验证功能
"""

import json
from typing import Any, Dict, List, Optional

from .models import EnhancedStatementMaskData, RealStatementMaskData


class DataQualityValidator:
    """SFT数据质量验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.validation_rules = {
            'required_fields': ['before', 'expected', 'after'],
            'metadata_required_fields': [
                'task_type', 'strategy', 'mask_level', 'function_name', 
                'filepath', 'is_control_flow', 'comment_hint', 'complexity_score'
            ],
            'context_required_fields': ['context_nodes', 'dependency_graph'],
            'max_before_length': 10000,
            'max_expected_length': 1000,
            'max_after_length': 10000
        }
    
    def validate_enhanced_mask_data(self, mask_data: EnhancedStatementMaskData) -> bool:
        """验证增强遮盖数据质量"""
        # 基本验证
        if not mask_data.before.strip() and not mask_data.expected.strip():
            print("    质量检查失败：before和expected都为空")
            return False
        
        if not mask_data.before.strip():
            print("    质量检查失败：before为空")
            return False
        
        if not mask_data.expected.strip():
            print("    质量检查失败：expected为空")
            return False
        
        # 检查上下文节点
        if not mask_data.context_nodes:
            print("    质量检查失败：缺少上下文节点")
            return False
        
        # 检查依赖图
        if not mask_data.dependency_graph.nodes:
            print("    质量检查失败：缺少依赖图节点")
            return False
        
        # 检查metadata中的必需字段
        metadata = mask_data.metadata
        required_fields = self.validation_rules['metadata_required_fields']
        
        for field in required_fields:
            if field not in metadata:
                print(f"    质量检查失败：metadata中缺少{field}字段")
                return False
        
        print("    质量检查通过：所有条件满足")
        return True
    
    def validate_real_mask_data(self, mask_data: RealStatementMaskData) -> bool:
        """验证真实遮盖数据质量"""
        # 基本验证
        if not mask_data.before.strip():
            print("    质量检查失败：before为空")
            return False
        
        if not mask_data.expected.strip():
            print("    质量检查失败：expected为空")
            return False
        
        # 检查expected是否为注释
        if mask_data.expected.strip().startswith('//') or mask_data.expected.strip().startswith('/*'):
            print("    质量检查失败：expected是注释")
            return False
        
        # 检查基本字段
        if not mask_data.function_name:
            print("    质量检查失败：缺少函数名")
            return False
        
        if not mask_data.filepath:
            print("    质量检查失败：缺少文件路径")
            return False
        
        return True
    
    def validate_sft_data_file(self, file_path: str) -> Dict[str, Any]:
        """验证SFT数据文件"""
        print(f"🔍 验证SFT数据文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            return {
                'valid': False,
                'error': f"文件读取失败: {e}",
                'total_samples': 0,
                'valid_samples': 0,
                'invalid_samples': 0,
                'warnings': []
            }
        
        if not isinstance(data, list):
            return {
                'valid': False,
                'error': "数据格式错误：根元素应该是数组",
                'total_samples': 0,
                'valid_samples': 0,
                'invalid_samples': 0,
                'warnings': []
            }
        
        total_samples = len(data)
        valid_samples = 0
        invalid_samples = 0
        warnings = []
        
        print("============================================================")
        print("📊 SFT数据验证报告")
        print("============================================================")
        
        for i, sample in enumerate(data):
            try:
                # 验证基本结构
                if not self._validate_sample_structure(sample):
                    invalid_samples += 1
                    warnings.append(f"样本 {i+1}: 结构验证失败")
                    continue
                
                # 验证必需字段
                if not self._validate_required_fields(sample):
                    invalid_samples += 1
                    warnings.append(f"样本 {i+1}: 必需字段缺失")
                    continue
                
                # 验证字段类型
                if not self._validate_field_types(sample):
                    invalid_samples += 1
                    warnings.append(f"样本 {i+1}: 字段类型错误")
                    continue
                
                # 验证字段值
                if not self._validate_field_values(sample):
                    invalid_samples += 1
                    warnings.append(f"样本 {i+1}: 字段值无效")
                    continue
                
                valid_samples += 1
                
            except Exception as e:
                invalid_samples += 1
                warnings.append(f"样本 {i+1}: 验证异常 - {e}")
        
        print(f"总样本数: {total_samples}")
        print(f"有效样本: {valid_samples}")
        print(f"无效样本: {invalid_samples}")
        print(f"警告数量: {len(warnings)}")
        
        if invalid_samples == 0:
            print("\n✅ 所有数据验证通过！")
        else:
            print(f"\n❌ 发现 {invalid_samples} 个无效样本")
            for warning in warnings[:5]:  # 只显示前5个警告
                print(f"  - {warning}")
            if len(warnings) > 5:
                print(f"  ... 还有 {len(warnings) - 5} 个警告")
        
        return {
            'valid': invalid_samples == 0,
            'total_samples': total_samples,
            'valid_samples': valid_samples,
            'invalid_samples': invalid_samples,
            'warnings': warnings
        }
    
    def _validate_sample_structure(self, sample: Dict[str, Any]) -> bool:
        """验证样本结构"""
        if not isinstance(sample, dict):
            return False
        
        # 检查顶层必需字段
        required_fields = self.validation_rules['required_fields']
        for field in required_fields:
            if field not in sample:
                return False
        
        return True
    
    def _validate_required_fields(self, sample: Dict[str, Any]) -> bool:
        """验证必需字段"""
        # 检查核心字段
        core_fields = ['before', 'expected', 'after']
        for field in core_fields:
            if field not in sample or not isinstance(sample[field], str):
                return False
        
        # 检查增强字段（如果存在）
        if 'context_nodes' in sample:
            if not isinstance(sample['context_nodes'], list):
                return False
        
        if 'dependency_graph' in sample:
            if not isinstance(sample['dependency_graph'], dict):
                return False
        
        if 'metadata' in sample:
            if not isinstance(sample['metadata'], dict):
                return False
        
        return True
    
    def _validate_field_types(self, sample: Dict[str, Any]) -> bool:
        """验证字段类型"""
        # 验证字符串字段
        string_fields = ['before', 'expected', 'after']
        for field in string_fields:
            if field in sample and not isinstance(sample[field], str):
                return False
        
        # 验证列表字段
        list_fields = ['context_nodes', 'context_list']
        for field in list_fields:
            if field in sample and not isinstance(sample[field], list):
                return False
        
        # 验证字典字段
        dict_fields = ['dependency_graph', 'metadata']
        for field in dict_fields:
            if field in sample and not isinstance(sample[field], dict):
                return False
        
        return True
    
    def _validate_field_values(self, sample: Dict[str, Any]) -> bool:
        """验证字段值"""
        # 验证非空字符串
        string_fields = ['before', 'expected', 'after']
        for field in string_fields:
            if field in sample:
                value = sample[field]
                if not isinstance(value, str) or not value.strip():
                    return False
        
        # 验证长度限制
        if 'before' in sample and len(sample['before']) > self.validation_rules['max_before_length']:
            return False
        
        if 'expected' in sample and len(sample['expected']) > self.validation_rules['max_expected_length']:
            return False
        
        if 'after' in sample and len(sample['after']) > self.validation_rules['max_after_length']:
            return False
        
        # 验证metadata字段
        if 'metadata' in sample:
            metadata = sample['metadata']
            required_metadata_fields = self.validation_rules['metadata_required_fields']
            for field in required_metadata_fields:
                if field not in metadata:
                    return False
        
        return True
    
    def generate_validation_report(self, validation_result: Dict[str, Any]) -> str:
        """生成验证报告"""
        report = []
        report.append("SFT数据验证报告")
        report.append("=" * 50)
        report.append(f"总样本数: {validation_result['total_samples']}")
        report.append(f"有效样本: {validation_result['valid_samples']}")
        report.append(f"无效样本: {validation_result['invalid_samples']}")
        report.append(f"验证状态: {'通过' if validation_result['valid'] else '失败'}")
        
        if validation_result['warnings']:
            report.append("\n警告信息:")
            for warning in validation_result['warnings'][:10]:  # 只显示前10个警告
                report.append(f"  - {warning}")
            if len(validation_result['warnings']) > 10:
                report.append(f"  ... 还有 {len(validation_result['warnings']) - 10} 个警告")
        
        return '\n'.join(report) 