"""
SFT模块公共基础生成器

提供SFT数据生成的基础功能
"""

import random
from datetime import datetime
from typing import Any, Dict, List, Optional

from .base_models import (ContextNode, DependencyEdge, DependencyGraph,
                          DependencyNode, EnhancedStatementMaskData,
                          RealStatementMaskData)
from .utils import SFTUtils


class BaseSFTGenerator:
    """SFT基础生成器"""
    
    def __init__(self, analyzer):
        """初始化生成器"""
        self.analyzer = analyzer
        
        # 配置参数
        self.max_context_nodes = 15
        self.max_before_length = 5000
        self.max_expected_length = 1000
        self.max_after_length = 5000
        self.min_complexity_score = 0.1
        
        # 随机种子
        self.seed = 42
        SFTUtils.set_random_seed(self.seed)
    
    def _build_context_nodes(self, func, file_path: str) -> List[ContextNode]:
        """构建上下文节点"""
        context_nodes = []
        
        try:
            # 添加函数节点
            func_node = ContextNode(
                node_type="function",
                name=func.name,
                filepath=file_path,
                code=func.code[:200] + "..." if len(func.code) > 200 else func.code
            )
            context_nodes.append(func_node)
            
            # 添加函数调用节点
            function_calls = SFTUtils.extract_function_calls(func.code, file_path)
            for call in function_calls[:5]:  # 限制数量
                call_node = ContextNode(
                    node_type="function_call",
                    name=call,
                    filepath=file_path,
                    code=f"{call}()"
                )
                context_nodes.append(call_node)
            
            # 添加变量节点
            variables = SFTUtils.extract_variables(func.code, file_path)
            for var in variables[:5]:  # 限制数量
                var_node = ContextNode(
                    node_type="variable",
                    name=var,
                    filepath=file_path,
                    code=f"var {var}"
                )
                context_nodes.append(var_node)
            
            # 限制总数量
            context_nodes = context_nodes[:self.max_context_nodes]
            
        except Exception as e:
            print(f"    构建上下文节点时出错: {e}")
        
        return context_nodes
    
    def _build_dependency_graph(self, func, file_path: str) -> DependencyGraph:
        """构建依赖图"""
        nodes = []
        edges = []
        
        try:
            # 添加函数节点
            func_node = DependencyNode(
                id=f"{file_path}::{func.name}",
                type="function"
            )
            nodes.append(func_node)
            
            # 添加函数调用依赖
            function_calls = SFTUtils.extract_function_calls(func.code, file_path)
            for call in function_calls[:10]:  # 限制数量
                call_node = DependencyNode(
                    id=f"{file_path}::{call}",
                    type="function"
                )
                nodes.append(call_node)
                
                # 添加调用关系边
                call_edge = DependencyEdge(
                    source=f"{file_path}::{func.name}",
                    target=f"{file_path}::{call}",
                    relation="calls"
                )
                edges.append(call_edge)
            
            # 添加变量依赖
            variables = SFTUtils.extract_variables(func.code, file_path)
            for var in variables[:10]:  # 限制数量
                var_node = DependencyNode(
                    id=f"{file_path}::{var}",
                    type="variable"
                )
                nodes.append(var_node)
                
                # 添加使用关系边
                use_edge = DependencyEdge(
                    source=f"{file_path}::{func.name}",
                    target=f"{file_path}::{var}",
                    relation="uses"
                )
                edges.append(use_edge)
            
        except Exception as e:
            print(f"    构建依赖图时出错: {e}")
        
        return DependencyGraph(nodes=nodes, edges=edges)
    
    def _build_enhanced_metadata(self, func, file_path: str, strategy: str, mask_level: str,
                                before: str, expected: str, after: str, line_number: int) -> Dict[str, Any]:
        """构建增强元数据"""
        metadata = {
            # 基础信息
            "filepath": file_path,
            "function_name": func.name,
            "line_number": line_number,
            
            # 生成策略信息
            "strategy": strategy,
            "mask_level": mask_level,
            
            # 内容信息
            "before_length": len(before),
            "expected_length": len(expected),
            "after_length": len(after),
            
            # 质量指标
            "complexity_score": SFTUtils.calculate_complexity_score(func),
            
            # 时间戳
            "generated_at": datetime.now().isoformat(),
            
            # 分析信息
            "function_calls": SFTUtils.extract_function_calls(func.code, file_path),
            "variables": SFTUtils.extract_variables(func.code, file_path),
            "macros": SFTUtils.extract_macros(func.code, file_path),
            "structs": SFTUtils.extract_structs(func.code, file_path),
        }
        
        # 添加函数签名信息
        if hasattr(func, 'signature') and func.signature:
            metadata["function_signature"] = func.signature
        
        # 添加参数信息
        if hasattr(func, 'parameters') and func.parameters:
            metadata["parameters"] = [str(param) for param in func.parameters]
        
        return metadata
    
    def _validate_enhanced_data_quality(self, before: str, expected: str, after: str, 
                                       context_nodes=None, metadata=None) -> bool:
        """验证增强数据质量"""
        # 基础质量验证
        if not SFTUtils.validate_data_quality(before, expected, after):
            return False
        
        # 长度限制
        if len(before) > self.max_before_length:
            return False
        
        if len(expected) > self.max_expected_length:
            return False
        
        if len(after) > self.max_after_length:
            return False
        
        # 复杂度检查
        if metadata and metadata.get('complexity_score', 0) < self.min_complexity_score:
            return False
        
        return True
    
    def _validate_real_data_quality(self, before: str, expected: str, after: str) -> bool:
        """验证真实数据质量"""
        return SFTUtils.validate_data_quality(before, expected, after)
    
    def _get_call_chain(self, func_name: str) -> List[str]:
        """获取调用链"""
        # 使用analyzer中的调用缓存
        if hasattr(self.analyzer, '_calls_cache'):
            return SFTUtils.get_call_chain(func_name, self.analyzer._functions_cache, self.analyzer._calls_cache)
        else:
            # 降级方案：使用简单的函数名匹配
            call_chain = []
            
            def dfs(current_func, depth):
                if depth > 5:  # 限制深度
                    return
                
                if current_func in call_chain:
                    return
                
                call_chain.append(current_func)
                
                # 查找调用关系
                for file_path, functions in self.analyzer._functions_cache.items():
                    for func in functions:
                        if func.name == current_func:
                            calls = SFTUtils.extract_function_calls(func.code, file_path)
                            for call in calls[:3]:  # 限制数量
                                dfs(call, depth + 1)
                            break
            
            dfs(func_name, 0)
            return call_chain
    
    def _get_variable_references(self, func) -> List[str]:
        """获取变量引用"""
        try:
            variables = SFTUtils.extract_variables(func.code, func.file_path)
            return variables[:10]  # 限制数量
        except Exception:
            return []
    
    def _get_related_structs(self, func, file_path: str) -> List[str]:
        """获取相关结构体"""
        try:
            structs = SFTUtils.extract_structs(func.code, file_path)
            return structs[:5]  # 限制数量
        except Exception:
            return [] 