"""
Simple SFT数据生成器

基于公共基础模块实现简单的SFT数据生成功能
"""

import random
from typing import List

from ..base_generator import BaseSFTGenerator
from ..utils import SFTUtils
from .models import SimpleSFTData


class SimpleSFTGenerator(BaseSFTGenerator):
    """简单SFT数据生成器"""
    
    def __init__(self, analyzer):
        """初始化生成器"""
        super().__init__(analyzer)
    
    def generate_simple_sft_data(self, max_samples: int = 10) -> List[SimpleSFTData]:
        """生成简单SFT数据"""
        if max_samples <= 0:
            raise ValueError("max_samples必须大于0")
        if max_samples > 10000:
            raise ValueError("max_samples不能超过10000")
        
        print(f"\n=== 生成简单SFT数据 (最多{max_samples}个样本) ===")
        
        simple_data = []
        
        for file_path, functions in self.analyzer._functions_cache.items():
            for func in functions:
                try:
                    # 生成瀑布式数据
                    waterfall_data = self._generate_simple_waterfall_data(func, file_path)
                    simple_data.extend(waterfall_data)
                    
                    # 生成随机遮盖数据
                    random_data = self._generate_simple_random_data(func, file_path)
                    simple_data.extend(random_data)
                    
                    if len(simple_data) >= max_samples:
                        break
                except Exception as e:
                    print(f"    生成函数 {func.name} 的简单数据时出错: {e}")
                    continue
            
            if len(simple_data) >= max_samples:
                break
        
        # 限制样本数量
        simple_data = simple_data[:max_samples]
        
        print(f"  生成了 {len(simple_data)} 个简单SFT数据样本")
        return simple_data
    
    def _generate_simple_waterfall_data(self, func, file_path: str) -> List[SimpleSFTData]:
        """生成瀑布式简单数据"""
        simple_data = []
        
        try:
            lines = func.code.split('\n')
            body_lines = SFTUtils.extract_function_body_lines(lines)
            
            if len(body_lines) < 3:
                return simple_data
            
            # 瀑布式遮盖：从前往后逐步遮盖
            for i in range(1, len(body_lines) - 1):
                before_lines = body_lines[:i]
                current_line = body_lines[i]
                after_lines = body_lines[i + 1:]
                
                # 跳过控制流语句和注释
                if SFTUtils.is_control_flow_statement(current_line):
                    continue
                
                if SFTUtils.has_comment_hint(current_line):
                    continue
                
                before = '\n'.join(before_lines)
                expected = current_line.strip()
                after = '\n'.join(after_lines)
                
                # 验证数据质量
                if not self._validate_real_data_quality(before, expected, after):
                    continue
                
                # 构建上下文列表
                context_list = SFTUtils.build_context_list(func, file_path)
                
                # 获取分析信息
                call_chain = self._get_call_chain(func.name)
                variable_references = self._get_variable_references(func)
                related_structs = self._get_related_structs(func, file_path)
                
                # 创建简单SFT数据
                simple_item = SimpleSFTData(
                    filepath=file_path,
                    function_name=func.name,
                    line_number=i + 1,
                    before=before,
                    expected=expected,
                    after=after,
                    context_list=context_list,
                    call_chain=call_chain,
                    variable_references=variable_references,
                    related_structs=related_structs,
                    complexity_score=SFTUtils.calculate_complexity_score(func),
                    statement_type="statement",
                    ast_node_type="expression_statement",
                    metadata={
                        "strategy": "waterfall",
                        "mask_level": "line",
                        "function_signature": getattr(func, 'signature', ''),
                        "generated_at": self._get_timestamp()
                    }
                )
                
                if simple_item.validate():
                    simple_data.append(simple_item)
                
                # 限制每个函数的数据量
                if len(simple_data) >= 5:
                    break
                    
        except Exception as e:
            print(f"    生成瀑布式简单数据时出错: {e}")
        
        return simple_data
    
    def _generate_simple_random_data(self, func, file_path: str) -> List[SimpleSFTData]:
        """生成随机遮盖简单数据"""
        simple_data = []
        
        try:
            lines = func.code.split('\n')
            body_lines = SFTUtils.extract_function_body_lines(lines)
            
            if len(body_lines) < 3:
                return simple_data
            
            # 随机遮盖：随机选择语句进行遮盖
            valid_lines = []
            for i, line in enumerate(body_lines):
                stripped_line = line.strip()
                if (stripped_line and 
                    not SFTUtils.is_control_flow_statement(stripped_line) and
                    not SFTUtils.has_comment_hint(stripped_line)):
                    valid_lines.append((i, stripped_line))
            
            if len(valid_lines) < 2:
                return simple_data
            
            # 随机选择要遮盖的语句
            num_masks = min(3, len(valid_lines) // 2)  # 最多遮盖3个语句
            mask_indices = random.sample(range(len(valid_lines)), num_masks)
            
            for mask_idx in mask_indices:
                line_idx, current_line = valid_lines[mask_idx]
                
                # 构建before和after
                before_lines = []
                after_lines = []
                
                for i, (orig_idx, line) in enumerate(valid_lines):
                    if i < mask_idx:
                        before_lines.append(body_lines[orig_idx])
                    elif i > mask_idx:
                        after_lines.append(body_lines[orig_idx])
                
                before = '\n'.join(before_lines)
                expected = current_line
                after = '\n'.join(after_lines)
                
                # 验证数据质量
                if not self._validate_real_data_quality(before, expected, after):
                    continue
                
                # 构建上下文列表
                context_list = SFTUtils.build_context_list(func, file_path)
                
                # 获取分析信息
                call_chain = self._get_call_chain(func.name)
                variable_references = self._get_variable_references(func)
                related_structs = self._get_related_structs(func, file_path)
                
                # 创建简单SFT数据
                simple_item = SimpleSFTData(
                    filepath=file_path,
                    function_name=func.name,
                    line_number=line_idx + 1,
                    before=before,
                    expected=expected,
                    after=after,
                    context_list=context_list,
                    call_chain=call_chain,
                    variable_references=variable_references,
                    related_structs=related_structs,
                    complexity_score=SFTUtils.calculate_complexity_score(func),
                    statement_type="statement",
                    ast_node_type="expression_statement",
                    metadata={
                        "strategy": "random",
                        "mask_level": "line",
                        "function_signature": getattr(func, 'signature', ''),
                        "generated_at": self._get_timestamp()
                    }
                )
                
                if simple_item.validate():
                    simple_data.append(simple_item)
                
                # 限制每个函数的数据量
                if len(simple_data) >= 3:
                    break
                    
        except Exception as e:
            print(f"    生成随机遮盖简单数据时出错: {e}")
        
        return simple_data
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat() 