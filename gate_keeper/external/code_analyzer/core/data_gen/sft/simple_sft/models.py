"""
Simple SFT数据模型

定义简单SFT数据生成过程中使用的数据结构
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

from ..base_models import BaseSFTData


@dataclass
class SimpleSFTData(BaseSFTData):
    """简单SFT训练数据"""
    # 上下文信息
    context_list: List[str] = field(default_factory=list)
    
    # 分析信息
    call_chain: List[str] = field(default_factory=list)
    related_structs: List[str] = field(default_factory=list)
    variable_references: List[str] = field(default_factory=list)
    
    # 语句类型
    statement_type: str = ""
    ast_node_type: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "context_list": self.context_list,
            "filepath": self.filepath,
            "before": self.before,
            "expected": self.expected,
            "after": self.after,
            "function_name": self.function_name,
            "statement_type": self.statement_type,
            "line_number": self.line_number,
            "call_chain": self.call_chain,
            "related_structs": self.related_structs,
            "variable_references": self.variable_references,
            "complexity_score": self.complexity_score,
            "ast_node_type": self.ast_node_type,
            "metadata": self.metadata
        }
    
    def validate(self) -> bool:
        """验证数据质量"""
        if not super().validate():
            return False
        
        if not self.context_list:
            return False
        
        if not self.statement_type:
            return False
        
        return True 