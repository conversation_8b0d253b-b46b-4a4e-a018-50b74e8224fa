"""
文本清洗器

整合异常字符检测和编码处理，提供完整的文本清洗功能
"""

import os
import re
import unicodedata
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

from .anomaly_detector import AnomalyDetector, AnomalyReport
from .encoding_detector import EncodingDetector, EncodingInfo


@dataclass
class CleaningResult:
    """清洗结果"""
    original_content: str
    cleaned_content: str
    encoding_info: EncodingInfo
    anomalies: List[AnomalyReport]
    removed_chars: int
    cleaning_stats: Dict[str, int]
    is_successful: bool
    error_message: str = ""


class TextCleaner:
    """文本清洗器"""
    
    def __init__(self):
        """初始化清洗器"""
        self.anomaly_detector = AnomalyDetector()
        self.encoding_detector = EncodingDetector()
        
        # 清洗配置
        self.remove_control_chars = True
        self.remove_replacement_chars = True
        self.normalize_whitespace = True
        self.remove_zero_width_chars = True
        self.fix_common_issues = True
        
        # 保留的字符类型
        self.keep_categories = {
            'Lu',  # 大写字母
            'Ll',  # 小写字母
            'Lt',  # 标题字母
            'Lm',  # 修饰字母
            'Lo',  # 其他字母
            'Nd',  # 十进制数字
            'Nl',  # 字母数字
            'No',  # 其他数字
            'Pc',  # 连接标点
            'Pd',  # 破折号标点
            'Pe',  # 结束标点
            'Pf',  # 最终标点
            'Pi',  # 初始标点
            'Po',  # 其他标点
            'Ps',  # 开始标点
            'Sc',  # 货币符号
            'Sk',  # 修饰符号
            'Sm',  # 数学符号
            'So',  # 其他符号
            'Zs',  # 空格分隔符
            'Zl',  # 行分隔符
            'Zp',  # 段落分隔符
        }
    
    def clean_file(self, file_path: str, output_path: str = None) -> CleaningResult:
        """清洗文件"""
        try:
            # 1. 检测编码
            print(f"🔍 检测文件编码: {file_path}")
            encoding_infos = self.encoding_detector.try_multiple_encodings(file_path)
            self.encoding_detector.print_multiple_encodings_report(encoding_infos, file_path)
            
            # 2. 读取文件内容
            content, encoding_info = self.encoding_detector.read_file_with_encoding(file_path)
            
            # 3. 检测异常字符
            print(f"🔍 检测异常字符: {file_path}")
            anomalies = self.anomaly_detector.detect_anomalies(content, file_path)
            self.anomaly_detector.print_anomaly_report(anomalies, file_path)
            
            # 4. 清洗文本
            print(f"🧹 开始清洗文本: {file_path}")
            cleaned_content, cleaning_stats = self._clean_text(content)
            
            # 5. 确保内容为UTF-8编码
            cleaned_content = self.encoding_detector.ensure_utf8_content(cleaned_content, encoding_info.encoding)
            
            # 6. 保存清洗后的文件
            if output_path:
                self._save_cleaned_file(cleaned_content, output_path)
            
            # 7. 生成清洗报告
            removed_chars = len(content) - len(cleaned_content)
            
            result = CleaningResult(
                original_content=content,
                cleaned_content=cleaned_content,
                encoding_info=encoding_info,
                anomalies=anomalies,
                removed_chars=removed_chars,
                cleaning_stats=cleaning_stats,
                is_successful=True
            )
            
            self._print_cleaning_report(result, file_path)
            return result
            
        except Exception as e:
            print(f"❌ 清洗文件失败: {file_path} - {str(e)}")
            return CleaningResult(
                original_content="",
                cleaned_content="",
                encoding_info=EncodingInfo("unknown", 0.0, "unknown", False, 0, ""),
                anomalies=[],
                removed_chars=0,
                cleaning_stats={},
                is_successful=False,
                error_message=str(e)
            )
    
    def _clean_text(self, text: str) -> Tuple[str, Dict[str, int]]:
        """清洗文本内容"""
        original_text = text
        cleaning_stats = {
            'control_chars_removed': 0,
            'replacement_chars_removed': 0,
            'zero_width_chars_removed': 0,
            'whitespace_normalized': 0,
            'other_anomalies_removed': 0,
        }
        
        # 1. 移除控制字符
        if self.remove_control_chars:
            text, count = self._remove_control_characters(text)
            cleaning_stats['control_chars_removed'] = count
        
        # 2. 移除替换字符
        if self.remove_replacement_chars:
            text, count = self._remove_replacement_characters(text)
            cleaning_stats['replacement_chars_removed'] = count
        
        # 3. 移除零宽字符
        if self.remove_zero_width_chars:
            text, count = self._remove_zero_width_characters(text)
            cleaning_stats['zero_width_chars_removed'] = count
        
        # 4. 规范化空白字符
        if self.normalize_whitespace:
            text, count = self._normalize_whitespace(text)
            cleaning_stats['whitespace_normalized'] = count
        
        # 5. 修复常见问题
        if self.fix_common_issues:
            text, count = self._fix_common_issues(text)
            cleaning_stats['other_anomalies_removed'] = count
        
        return text, cleaning_stats
    
    def _remove_control_characters(self, text: str) -> Tuple[str, int]:
        """移除控制字符"""
        original_length = len(text)
        
        # 移除控制字符（除了制表符、换行符、回车符）
        cleaned_text = ""
        for char in text:
            char_code = ord(char)
            if char_code in {0x0009, 0x000A, 0x000D}:  # 保留制表符、换行符、回车符
                cleaned_text += char
            elif char_code < 0x0020 or (0x007F <= char_code <= 0x009F):
                continue  # 移除控制字符
            else:
                cleaned_text += char
        
        removed_count = original_length - len(cleaned_text)
        return cleaned_text, removed_count
    
    def _remove_replacement_characters(self, text: str) -> Tuple[str, int]:
        """移除替换字符"""
        original_length = len(text)
        cleaned_text = text.replace('\uFFFD', '')  # Unicode替换字符
        removed_count = original_length - len(cleaned_text)
        return cleaned_text, removed_count
    
    def _remove_zero_width_characters(self, text: str) -> Tuple[str, int]:
        """移除零宽字符"""
        original_length = len(text)
        
        # 移除零宽字符
        zero_width_chars = [
            '\u200B',  # 零宽空格
            '\u200C',  # 零宽非连接符
            '\u200D',  # 零宽连接符
            '\uFEFF',  # 零宽非换行空格
            '\u2060',  # 词连接符
            '\u2061',  # 函数应用
            '\u2062',  # 不可见乘号
            '\u2063',  # 不可见分隔符
            '\u2064',  # 不可见加号
        ]
        
        cleaned_text = text
        for char in zero_width_chars:
            cleaned_text = cleaned_text.replace(char, '')
        
        removed_count = original_length - len(cleaned_text)
        return cleaned_text, removed_count
    
    def _normalize_whitespace(self, text: str) -> Tuple[str, int]:
        """规范化空白字符"""
        original_length = len(text)
        
        # 规范化空白字符
        cleaned_text = re.sub(r'[ \t]+', ' ', text)  # 多个空格或制表符替换为单个空格
        cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)  # 多个空行替换为两个换行
        
        removed_count = original_length - len(cleaned_text)
        return cleaned_text, removed_count
    
    def _fix_common_issues(self, text: str) -> Tuple[str, int]:
        """修复常见问题"""
        original_length = len(text)
        
        # 修复常见的编码问题
        fixes = [
            # 修复常见的乱码字符
            ('', ''),  # 替换字符
            ('\x00', ''),  # 空字符
            ('\x01', ''),  # 标题开始
            ('\x02', ''),  # 文本开始
            ('\x03', ''),  # 文本结束
            ('\x04', ''),  # 传输结束
            ('\x05', ''),  # 查询
            ('\x06', ''),  # 确认
            ('\x07', ''),  # 响铃
            ('\x08', ''),  # 退格
            ('\x0B', ''),  # 垂直制表符
            ('\x0C', ''),  # 换页
            ('\x0E', ''),  # 移出
            ('\x0F', ''),  # 移入
            ('\x10', ''),  # 数据链路转义
            ('\x11', ''),  # 设备控制1
            ('\x12', ''),  # 设备控制2
            ('\x13', ''),  # 设备控制3
            ('\x14', ''),  # 设备控制4
            ('\x15', ''),  # 否定确认
            ('\x16', ''),  # 同步空闲
            ('\x17', ''),  # 传输块结束
            ('\x18', ''),  # 取消
            ('\x19', ''),  # 介质结束
            ('\x1A', ''),  # 替换
            ('\x1B', ''),  # 转义
            ('\x1C', ''),  # 文件分隔符
            ('\x1D', ''),  # 组分隔符
            ('\x1E', ''),  # 记录分隔符
            ('\x1F', ''),  # 单元分隔符
        ]
        
        cleaned_text = text
        for old_char, new_char in fixes:
            cleaned_text = cleaned_text.replace(old_char, new_char)
        
        removed_count = original_length - len(cleaned_text)
        return cleaned_text, removed_count
    
    def _save_cleaned_file(self, content: str, output_path: str):
        """保存清洗后的文件"""
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 强制使用UTF-8编码保存
            with open(output_path, 'w', encoding='utf-8', errors='ignore') as f:
                f.write(content)
            
            print(f"✅ 清洗后的文件已保存: {output_path} (UTF-8编码)")
            
        except Exception as e:
            print(f"❌ 保存文件失败: {output_path} - {str(e)}")
    
    def _print_cleaning_report(self, result: CleaningResult, file_path: str):
        """打印清洗报告"""
        print(f"📊 文本清洗报告: {file_path}")
        print("-" * 60)
        print(f"原始文件大小: {len(result.original_content)} 字符")
        print(f"清洗后大小: {len(result.cleaned_content)} 字符")
        print(f"移除字符数: {result.removed_chars}")
        print(f"原始编码: {result.encoding_info.encoding} (置信度: {result.encoding_info.confidence:.2f})")
        print(f"输出编码: UTF-8")
        print(f"异常字符数: {len(result.anomalies)}")
        
        if result.cleaning_stats:
            print("清洗统计:")
            for stat_name, count in result.cleaning_stats.items():
                if count > 0:
                    print(f"  {stat_name}: {count}")
        
        if result.is_successful:
            print("✅ 文件清洗成功 (已转换为UTF-8编码)")
        else:
            print(f"❌ 文件清洗失败: {result.error_message}")
        print()
    
    def batch_clean_files(self, input_dir: str, output_dir: str = None, 
                         file_extensions: List[str] = None) -> List[CleaningResult]:
        """批量清洗文件"""
        if file_extensions is None:
            file_extensions = ['.asm', '.h', '.c', '.cpp', '.txt']
        
        results = []
        
        print(f"🚀 开始批量清洗文件: {input_dir}")
        print(f"目标扩展名: {file_extensions}")
        print("-" * 60)
        
        # 查找所有匹配的文件
        files_to_clean = []
        for root, dirs, files in os.walk(input_dir):
            for file in files:
                if any(file.endswith(ext) for ext in file_extensions):
                    file_path = os.path.join(root, file)
                    files_to_clean.append(file_path)
        
        print(f"找到 {len(files_to_clean)} 个文件需要清洗")
        
        # 清洗每个文件
        for i, file_path in enumerate(files_to_clean, 1):
            print(f"\n[{i}/{len(files_to_clean)}] 处理文件: {file_path}")
            
            # 生成输出路径
            if output_dir:
                rel_path = os.path.relpath(file_path, input_dir)
                output_path = os.path.join(output_dir, rel_path)
            else:
                output_path = None
            
            # 清洗文件
            result = self.clean_file(file_path, output_path)
            results.append(result)
        
        # 生成批量清洗报告
        self._print_batch_cleaning_report(results)
        
        return results
    
    def _print_batch_cleaning_report(self, results: List[CleaningResult]):
        """打印批量清洗报告"""
        successful_count = sum(1 for r in results if r.is_successful)
        failed_count = len(results) - successful_count
        total_removed = sum(r.removed_chars for r in results)
        
        print(f"\n📊 批量清洗完成报告")
        print("=" * 60)
        print(f"总文件数: {len(results)}")
        print(f"成功清洗: {successful_count}")
        print(f"清洗失败: {failed_count}")
        print(f"总移除字符数: {total_removed}")
        
        if failed_count > 0:
            print("\n失败的文件:")
            for result in results:
                if not result.is_successful:
                    print(f"  - {result.error_message}")
        
        print() 