"""
编码检测器

用于检测和处理文件编码问题
"""

import codecs
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import chardet


@dataclass
class EncodingInfo:
    """编码信息"""
    encoding: str
    confidence: float
    language: str
    is_valid: bool
    error_count: int
    suggestion: str


class EncodingDetector:
    """编码检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # 常见编码列表
        self.common_encodings = [
            'utf-8',
            'utf-8-sig',  # UTF-8 with BOM
            'utf-16',
            'utf-16-le',
            'utf-16-be',
            'gbk',
            'gb2312',
            'gb18030',
            'big5',
            'latin-1',
            'ascii',
            'cp1252',
            'iso-8859-1',
            'iso-8859-15',
            'shift_jis',
            'euc-jp',
            'euc-kr',
        ]
        
        # 编码别名映射
        self.encoding_aliases = {
            'utf8': 'utf-8',
            'utf8-sig': 'utf-8-sig',
            'utf16': 'utf-16',
            'utf16le': 'utf-16-le',
            'utf16be': 'utf-16-be',
            'gb2312-80': 'gb2312',
            'gbk2312': 'gb2312',
            'cp936': 'gbk',
            'ms936': 'gbk',
            'windows-936': 'gbk',
            'big5hkscs': 'big5',
            'latin1': 'latin-1',
            'latin_1': 'latin-1',
            'iso8859-1': 'iso-8859-1',
            'iso8859-15': 'iso-8859-15',
            'sjis': 'shift_jis',
            'ms932': 'shift_jis',
            'windows-932': 'shift_jis',
        }
    
    def detect_encoding(self, file_path: str) -> EncodingInfo:
        """检测文件编码"""
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            detected_encoding = result['encoding']
            confidence = result['confidence']
            
            # 标准化编码名称
            if detected_encoding:
                detected_encoding = detected_encoding.lower()
                detected_encoding = self.encoding_aliases.get(detected_encoding, detected_encoding)
            
            # 验证编码
            is_valid, error_count = self._validate_encoding(raw_data, detected_encoding)
            
            # 生成建议
            suggestion = self._generate_suggestion(detected_encoding, confidence, is_valid, error_count)
            
            return EncodingInfo(
                encoding=detected_encoding or 'unknown',
                confidence=confidence or 0.0,
                language=self._detect_language(detected_encoding),
                is_valid=is_valid,
                error_count=error_count,
                suggestion=suggestion
            )
            
        except Exception as e:
            return EncodingInfo(
                encoding='unknown',
                confidence=0.0,
                language='unknown',
                is_valid=False,
                error_count=0,
                suggestion=f'检测失败: {str(e)}'
            )
    
    def try_multiple_encodings(self, file_path: str) -> List[EncodingInfo]:
        """尝试多种编码"""
        results = []
        
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # 尝试常见编码
            for encoding in self.common_encodings:
                try:
                    is_valid, error_count = self._validate_encoding(raw_data, encoding)
                    
                    # 计算置信度（基于错误数量）
                    confidence = max(0.0, 1.0 - (error_count / len(raw_data)) * 10)
                    
                    suggestion = self._generate_suggestion(encoding, confidence, is_valid, error_count)
                    
                    results.append(EncodingInfo(
                        encoding=encoding,
                        confidence=confidence,
                        language=self._detect_language(encoding),
                        is_valid=is_valid,
                        error_count=error_count,
                        suggestion=suggestion
                    ))
                    
                except Exception:
                    # 编码失败，跳过
                    continue
            
            # 按置信度排序
            results.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            results.append(EncodingInfo(
                encoding='unknown',
                confidence=0.0,
                language='unknown',
                is_valid=False,
                error_count=0,
                suggestion=f'检测失败: {str(e)}'
            ))
        
        return results
    
    def _validate_encoding(self, raw_data: bytes, encoding: str) -> Tuple[bool, int]:
        """验证编码是否正确"""
        if not encoding:
            return False, len(raw_data)
        
        try:
            # 尝试解码
            decoded_text = raw_data.decode(encoding, errors='strict')
            return True, 0
        except UnicodeDecodeError as e:
            # 计算错误数量
            error_count = 1
            if hasattr(e, 'start') and hasattr(e, 'end'):
                error_count = e.end - e.start
            return False, error_count
        except Exception:
            return False, len(raw_data)
    
    def _detect_language(self, encoding: str) -> str:
        """检测语言"""
        if not encoding:
            return 'unknown'
        
        language_map = {
            'utf-8': 'universal',
            'utf-16': 'universal',
            'utf-16-le': 'universal',
            'utf-16-be': 'universal',
            'gbk': 'chinese',
            'gb2312': 'chinese',
            'gb18030': 'chinese',
            'big5': 'chinese',
            'shift_jis': 'japanese',
            'euc-jp': 'japanese',
            'euc-kr': 'korean',
            'latin-1': 'western',
            'ascii': 'english',
            'cp1252': 'western',
            'iso-8859-1': 'western',
            'iso-8859-15': 'western',
        }
        
        return language_map.get(encoding, 'unknown')
    
    def _generate_suggestion(self, encoding: str, confidence: float, is_valid: bool, error_count: int) -> str:
        """生成编码建议"""
        if not encoding:
            return "无法检测编码，建议手动指定"
        
        if is_valid and confidence > 0.8:
            return f"推荐使用 {encoding} 编码"
        elif is_valid and confidence > 0.5:
            return f"可以使用 {encoding} 编码，但建议验证"
        elif error_count > 0:
            return f"编码 {encoding} 存在 {error_count} 个错误，建议尝试其他编码"
        else:
            return f"编码 {encoding} 可能不正确，建议尝试其他编码"
    
    def read_file_with_encoding(self, file_path: str, encoding: str = None) -> Tuple[str, EncodingInfo]:
        """使用指定编码读取文件"""
        if not encoding:
            # 自动检测编码
            encoding_info = self.detect_encoding(file_path)
            encoding = encoding_info.encoding
        
        try:
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                content = f.read()
            
            # 重新验证编码
            is_valid, error_count = self._validate_encoding(content.encode(encoding, errors='replace'), encoding)
            
            encoding_info = EncodingInfo(
                encoding=encoding,
                confidence=1.0 if is_valid else 0.5,
                language=self._detect_language(encoding),
                is_valid=is_valid,
                error_count=error_count,
                suggestion=f"使用 {encoding} 编码读取成功"
            )
            
            return content, encoding_info
            
        except Exception as e:
            # 如果指定编码失败，尝试其他编码
            encoding_infos = self.try_multiple_encodings(file_path)
            if encoding_infos:
                best_encoding = encoding_infos[0].encoding
                try:
                    with open(file_path, 'r', encoding=best_encoding, errors='replace') as f:
                        content = f.read()
                    
                    return content, encoding_infos[0]
                except Exception:
                    pass
            
            # 最后尝试使用UTF-8 with errors='replace'
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                
                encoding_info = EncodingInfo(
                    encoding='utf-8',
                    confidence=0.1,
                    language='unknown',
                    is_valid=False,
                    error_count=0,
                    suggestion="使用UTF-8编码强制读取，可能存在字符替换"
                )
                
                return content, encoding_info
                
            except Exception as e2:
                raise Exception(f"无法读取文件 {file_path}: {str(e2)}")
    
    def ensure_utf8_content(self, content: str, original_encoding: str) -> str:
        """确保内容为UTF-8编码"""
        try:
            # 如果原始编码已经是UTF-8，直接返回
            if original_encoding.lower() in ['utf-8', 'utf8']:
                return content
            
            # 否则，先编码为原始编码的字节，再解码为UTF-8
            try:
                # 尝试使用原始编码编码
                original_bytes = content.encode(original_encoding, errors='replace')
                # 再解码为UTF-8
                utf8_content = original_bytes.decode('utf-8', errors='replace')
                return utf8_content
            except:
                # 如果失败，直接使用UTF-8编码
                return content.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
                
        except Exception as e:
            print(f"⚠️ UTF-8转换失败: {e}")
            # 最后的降级方案
            return content
    
    def print_encoding_report(self, encoding_info: EncodingInfo, file_path: str = ""):
        """打印编码检测报告"""
        print(f"📄 文件编码检测报告: {file_path}")
        print("-" * 60)
        print(f"检测到的编码: {encoding_info.encoding}")
        print(f"置信度: {encoding_info.confidence:.2f}")
        print(f"语言: {encoding_info.language}")
        print(f"是否有效: {'✅ 是' if encoding_info.is_valid else '❌ 否'}")
        print(f"错误数量: {encoding_info.error_count}")
        print(f"建议: {encoding_info.suggestion}")
        print()
    
    def print_multiple_encodings_report(self, encoding_infos: List[EncodingInfo], file_path: str = ""):
        """打印多编码检测报告"""
        print(f"📄 多编码检测报告: {file_path}")
        print("-" * 60)
        
        for i, info in enumerate(encoding_infos[:10], 1):  # 只显示前10个
            status = "✅" if info.is_valid else "❌"
            print(f"{i:2d}. {status} {info.encoding:12s} "
                  f"(置信度: {info.confidence:.2f}, 错误: {info.error_count})")
            print(f"    语言: {info.language}, 建议: {info.suggestion}")
        
        if len(encoding_infos) > 10:
            print(f"... 还有 {len(encoding_infos) - 10} 个编码未显示")
        print() 