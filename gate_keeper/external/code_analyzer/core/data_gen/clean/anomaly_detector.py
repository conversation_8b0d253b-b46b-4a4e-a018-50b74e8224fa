"""
异常字符检测器

专门用于检测和识别文本文件中的异常字符
"""

import re
import unicodedata
from dataclasses import dataclass
from typing import Dict, List, Set, Tuple


@dataclass
class AnomalyReport:
    """异常字符报告"""
    line_number: int
    column: int
    char: str
    char_code: int
    anomaly_type: str
    description: str
    suggestion: str


class AnomalyDetector:
    """异常字符检测器"""
    
    def __init__(self):
        """初始化检测器"""
        # 定义正常字符范围
        self.normal_ranges = [
            (0x0020, 0x007F),  # 基本拉丁字母
            (0x00A0, 0x00FF),  # 拉丁字母补充
            (0x0100, 0x017F),  # 拉丁字母扩展A
            (0x0180, 0x024F),  # 拉丁字母扩展B
            (0x2000, 0x206F),  # 通用标点符号
            (0x2100, 0x214F),  # 字母符号
            (0x2200, 0x22FF),  # 数学运算符
            (0x2300, 0x23FF),  # 杂项技术符号
            (0x2500, 0x257F),  # 制表符
            (0x2580, 0x259F),  # 方块元素
            (0x25A0, 0x25FF),  # 几何图形
            (0x2600, 0x26FF),  # 杂项符号
            (0x2700, 0x27BF),  # 装饰符号
            (0x2800, 0x28FF),  # 盲文模式
            (0x2900, 0x297F),  # 补充箭头B
            (0x2B00, 0x2BFF),  # 杂项符号和箭头
            (0x3000, 0x303F),  # 中文标点符号
            (0x3040, 0x309F),  # 平假名
            (0x30A0, 0x30FF),  # 片假名
            (0x4E00, 0x9FFF),  # 中文汉字
            (0xFF00, 0xFFEF),  # 全角ASCII、全角标点
        ]
        
        # 定义控制字符（除了常见的空白字符）
        self.control_chars = set(range(0x0000, 0x0020)) - {0x0009, 0x000A, 0x000D}  # 排除制表符、换行符、回车符
        
        # 定义常见的异常字符模式
        self.anomaly_patterns = [
            (r'[\uFFFD]', 'replacement_character', 'Unicode替换字符'),
            (r'[\u0000-\u0008\u000B\u000C\u000E-\u001F]', 'control_character', '控制字符'),
            (r'[\u007F-\u009F]', 'control_character', '控制字符'),
            (r'[\uD800-\uDFFF]', 'surrogate_pair', '代理对字符'),
            (r'[\uE000-\uF8FF]', 'private_use', '私有使用区域'),
            (r'[\uF900-\uFAFF]', 'compatibility', '兼容字符'),
            (r'[\uFB00-\uFB4F]', 'alphabetic_presentation', '字母表示形式'),
            (r'[\uFB50-\uFDFF]', 'arabic_presentation', '阿拉伯文表示形式'),
            (r'[\uFE00-\uFE0F]', 'variation_selector', '变体选择符'),
            (r'[\uFE20-\uFE2F]', 'combining_half_marks', '组合半标记'),
            (r'[\uFE30-\uFE4F]', 'cjk_compatibility', '中日韩兼容字符'),
            (r'[\uFE50-\uFE6F]', 'small_form_variants', '小写变体'),
            (r'[\uFE70-\uFEFF]', 'arabic_presentation_forms', '阿拉伯文表示形式B'),
            (r'[\uFF00-\uFFEF]', 'halfwidth_fullwidth', '半角全角字符'),
            (r'[\uFFF0-\uFFFF]', 'specials', '特殊字符'),
        ]
        
        # 定义需要特别关注的异常字符
        self.special_anomalies = {
            '\uFFFD': 'replacement_character',
            '\u0000': 'null_character',
            '\u0001': 'start_of_heading',
            '\u0002': 'start_of_text',
            '\u0003': 'end_of_text',
            '\u0004': 'end_of_transmission',
            '\u0005': 'enquiry',
            '\u0006': 'acknowledge',
            '\u0007': 'bell',
            '\u0008': 'backspace',
            '\u000B': 'vertical_tab',
            '\u000C': 'form_feed',
            '\u000E': 'shift_out',
            '\u000F': 'shift_in',
            '\u0010': 'data_link_escape',
            '\u0011': 'device_control_1',
            '\u0012': 'device_control_2',
            '\u0013': 'device_control_3',
            '\u0014': 'device_control_4',
            '\u0015': 'negative_acknowledge',
            '\u0016': 'synchronous_idle',
            '\u0017': 'end_of_transmission_block',
            '\u0018': 'cancel',
            '\u0019': 'end_of_medium',
            '\u001A': 'substitute',
            '\u001B': 'escape',
            '\u001C': 'file_separator',
            '\u001D': 'group_separator',
            '\u001E': 'record_separator',
            '\u001F': 'unit_separator',
        }
    
    def detect_anomalies(self, text: str, file_path: str = "") -> List[AnomalyReport]:
        """检测文本中的异常字符"""
        anomalies = []
        
        for line_num, line in enumerate(text.split('\n'), 1):
            for col_num, char in enumerate(line, 1):
                anomaly = self._check_character(char, line_num, col_num)
                if anomaly:
                    anomalies.append(anomaly)
        
        return anomalies
    
    def _check_character(self, char: str, line_num: int, col_num: int) -> AnomalyReport:
        """检查单个字符是否为异常字符"""
        char_code = ord(char)
        
        # 检查是否为控制字符
        if char_code in self.control_chars:
            return AnomalyReport(
                line_number=line_num,
                column=col_num,
                char=char,
                char_code=char_code,
                anomaly_type='control_character',
                description=f'控制字符: {self._get_control_char_name(char_code)}',
                suggestion='删除此字符'
            )
        
        # 检查特殊异常字符
        if char in self.special_anomalies:
            return AnomalyReport(
                line_number=line_num,
                column=col_num,
                char=char,
                char_code=char_code,
                anomaly_type=self.special_anomalies[char],
                description=f'特殊异常字符: {self._get_control_char_name(char_code)}',
                suggestion='删除此字符'
            )
        
        # 检查是否在正常字符范围内
        if not self._is_in_normal_range(char_code):
            return AnomalyReport(
                line_number=line_num,
                column=col_num,
                char=char,
                char_code=char_code,
                anomaly_type='out_of_range',
                description=f'超出正常范围的字符: U+{char_code:04X}',
                suggestion='检查字符编码或删除此字符'
            )
        
        # 检查Unicode类别
        category = unicodedata.category(char)
        if category in ['Cc', 'Cf', 'Cs', 'Co', 'Cn']:
            return AnomalyReport(
                line_number=line_num,
                column=col_num,
                char=char,
                char_code=char_code,
                anomaly_type='unicode_category',
                description=f'Unicode类别异常: {category}',
                suggestion='删除此字符'
            )
        
        return None
    
    def _is_in_normal_range(self, char_code: int) -> bool:
        """检查字符是否在正常范围内"""
        for start, end in self.normal_ranges:
            if start <= char_code <= end:
                return True
        return False
    
    def _get_control_char_name(self, char_code: int) -> str:
        """获取控制字符名称"""
        control_names = {
            0x0000: 'NULL',
            0x0001: 'START OF HEADING',
            0x0002: 'START OF TEXT',
            0x0003: 'END OF TEXT',
            0x0004: 'END OF TRANSMISSION',
            0x0005: 'ENQUIRY',
            0x0006: 'ACKNOWLEDGE',
            0x0007: 'BELL',
            0x0008: 'BACKSPACE',
            0x000B: 'VERTICAL TAB',
            0x000C: 'FORM FEED',
            0x000E: 'SHIFT OUT',
            0x000F: 'SHIFT IN',
            0x0010: 'DATA LINK ESCAPE',
            0x0011: 'DEVICE CONTROL ONE',
            0x0012: 'DEVICE CONTROL TWO',
            0x0013: 'DEVICE CONTROL THREE',
            0x0014: 'DEVICE CONTROL FOUR',
            0x0015: 'NEGATIVE ACKNOWLEDGE',
            0x0016: 'SYNCHRONOUS IDLE',
            0x0017: 'END OF TRANSMISSION BLOCK',
            0x0018: 'CANCEL',
            0x0019: 'END OF MEDIUM',
            0x001A: 'SUBSTITUTE',
            0x001B: 'ESCAPE',
            0x001C: 'FILE SEPARATOR',
            0x001D: 'GROUP SEPARATOR',
            0x001E: 'RECORD SEPARATOR',
            0x001F: 'UNIT SEPARATOR',
        }
        return control_names.get(char_code, f'CONTROL-{char_code:02X}')
    
    def get_anomaly_statistics(self, anomalies: List[AnomalyReport]) -> Dict[str, int]:
        """获取异常字符统计信息"""
        stats = {}
        for anomaly in anomalies:
            anomaly_type = anomaly.anomaly_type
            stats[anomaly_type] = stats.get(anomaly_type, 0) + 1
        return stats
    
    def print_anomaly_report(self, anomalies: List[AnomalyReport], file_path: str = ""):
        """打印异常字符报告"""
        if not anomalies:
            print(f"✅ 文件 {file_path} 中未发现异常字符")
            return
        
        print(f"❌ 在文件 {file_path} 中发现 {len(anomalies)} 个异常字符:")
        print("-" * 80)
        
        # 按行号排序
        anomalies.sort(key=lambda x: (x.line_number, x.column))
        
        for i, anomaly in enumerate(anomalies[:20], 1):  # 只显示前20个
            print(f"{i:2d}. 行 {anomaly.line_number:4d}, 列 {anomaly.column:3d}: "
                  f"字符 '{anomaly.char}' (U+{anomaly.char_code:04X})")
            print(f"    类型: {anomaly.anomaly_type}")
            print(f"    描述: {anomaly.description}")
            print(f"    建议: {anomaly.suggestion}")
            print()
        
        if len(anomalies) > 20:
            print(f"... 还有 {len(anomalies) - 20} 个异常字符未显示")
        
        # 显示统计信息
        stats = self.get_anomaly_statistics(anomalies)
        print("📊 异常字符统计:")
        for anomaly_type, count in sorted(stats.items()):
            print(f"  {anomaly_type}: {count} 个") 