"""
Embedding训练数据生成器

整合上下文提取、候选采样等功能，生成embedding模型训练数据
"""

import json
import os
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from tqdm import tqdm

from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.generator import SFTDataGenerator
from core.data_gen.sft.models import EnhancedStatementMaskData

from .candidate_sampler import CandidateSampler, SamplingConfig
from .context_extractor import ContextExtractor, ExtractionConfig
from .models import (ContextCandidate, EmbeddingPair, EmbeddingTrainingData,
                     QueryContext, RerankTrainingData)


@dataclass
class GeneratorConfig:
    """生成器配置"""
    # 提取配置
    extraction_config: ExtractionConfig = None
    # 采样配置
    sampling_config: SamplingConfig = None
    # 生成配置
    max_pairs_per_query: int = 5        # 每个查询的最大训练对数
    max_total_pairs: int = 10000        # 最大总训练对数
    output_dir: str = "embedding_data"  # 输出目录
    save_intermediate: bool = True      # 是否保存中间结果


class EmbeddingDataGenerator:
    """Embedding训练数据生成器"""
    
    def __init__(self, config: Optional[GeneratorConfig] = None):
        self.config = config or GeneratorConfig()
        
        # 初始化组件
        self.extractor = ContextExtractor(self.config.extraction_config)
        self.sampler = CandidateSampler(self.config.sampling_config)
        
        # 初始化SFT生成器用于生成查询上下文
        self.sft_analyzer = None  # 延迟初始化
        self.sft_generator = None  # 延迟初始化
    
    def generate_from_project(self, 
                            project_path: str,
                            max_samples: int = 100) -> EmbeddingTrainingData:
        """从项目中生成embedding训练数据"""
        print(f"🔍 开始从项目生成embedding训练数据: {project_path}")
        
        # 1. 提取所有候选上下文
        print("📝 提取候选上下文...")
        all_candidates = self.extractor.extract_from_directory(project_path)
        print(f"   提取到 {len(all_candidates)} 个候选上下文")
        
        # 2. 生成SFT数据作为查询上下文
        print("📝 生成SFT数据作为查询上下文...")
        
        # 延迟初始化SFT生成器
        if self.sft_analyzer is None:
            self.sft_analyzer = RealNPAnalyzer(project_path)
            self.sft_analyzer.analyze_project()  # 必须调用analyze_project
            self.sft_generator = SFTDataGenerator(self.sft_analyzer)
        
        sft_data = self.sft_generator.generate_enhanced_sft_data(max_samples)
        print(f"   生成 {len(sft_data)} 个SFT数据")
        
        # 3. 转换为查询上下文
        queries = self._convert_sft_to_queries(sft_data)
        print(f"   转换为 {len(queries)} 个查询上下文")
        
        # 4. 生成训练对
        print("📝 生成训练对...")
        training_data = self._generate_training_pairs(queries, all_candidates)
        
        # 5. 保存结果
        if self.config.save_intermediate:
            self._save_intermediate_results(all_candidates, queries, training_data)
        
        print(f"✅ 生成完成，共 {len(training_data.pairs)} 个训练对")
        return training_data
    
    def generate_rerank_data(self, 
                           embedding_data: EmbeddingTrainingData) -> RerankTrainingData:
        """生成Rerank训练数据"""
        print("📝 生成Rerank训练数据...")
        
        rerank_data = RerankTrainingData()
        
        with tqdm(total=len(embedding_data.pairs), desc="生成Rerank数据", unit="对") as pbar:
            for pair in embedding_data.pairs:
                # 正样本
                positive_input = self._create_rerank_input(pair.query, pair.positive)
                rerank_data.add_sample(positive_input, 1)
                
                # 负样本
                negative_input = self._create_rerank_input(pair.query, pair.negative)
                rerank_data.add_sample(negative_input, 0)
                
                pbar.update(1)
        
        print(f"✅ 生成Rerank数据完成，共 {len(rerank_data.inputs)} 个样本")
        return rerank_data
    
    def _convert_sft_to_queries(self, sft_data: List[EnhancedStatementMaskData]) -> List[QueryContext]:
        """将SFT数据转换为查询上下文"""
        queries = []
        
        for item in sft_data:
            query = QueryContext(
                before=item.before,
                after=item.after,
                expected=item.expected,
                file_path=item.metadata.get('filepath', ''),
                function_name=item.metadata.get('function_name', ''),
                line_number=item.metadata.get('line_number', 0),
                instruction=item.metadata.get('instruction', None),
                env_info=item.metadata.get('env_info', None),
                metadata=item.metadata
            )
            queries.append(query)
        
        return queries
    
    def _generate_training_pairs(self, 
                               queries: List[QueryContext],
                               all_candidates: List[ContextCandidate]) -> EmbeddingTrainingData:
        """生成训练对"""
        training_data = EmbeddingTrainingData()
        total_pairs = 0
        
        with tqdm(total=len(queries), desc="生成训练对", unit="查询") as pbar:
            for i, query in enumerate(queries):
                if total_pairs >= self.config.max_total_pairs:
                    break
                
                print(f"   处理查询 {i+1}/{len(queries)}: {query.function_name}")
                
                # 采样正样本候选
                positive_candidates = self.sampler.sample_positive_candidates(query, all_candidates)
                
                # 采样负样本候选
                negative_candidates = self.sampler.sample_negative_candidates(
                    query, all_candidates, positive_candidates
                )
                
                # 平衡候选数量
                positive_candidates, negative_candidates = self.sampler.balance_candidates(
                    positive_candidates, negative_candidates
                )
                
                # 创建训练对
                pairs = self.sampler.create_training_pairs(query, positive_candidates, negative_candidates)
                
                # 限制每个查询的训练对数量
                if len(pairs) > self.config.max_pairs_per_query:
                    pairs = pairs[:self.config.max_pairs_per_query]
                
                # 添加到训练数据
                for pair in pairs:
                    training_data.add_pair(pair)
                    total_pairs += 1
                
                pbar.update(1)
        
        return training_data
    
    def _create_rerank_input(self, query: QueryContext, candidate: ContextCandidate) -> str:
        """创建Rerank输入文本"""
        # 格式: [CLS] query_tokens [SEP] candidate_tokens [SEP]
        query_text = query.get_query_text()
        candidate_text = candidate.content
        
        # 简单的文本拼接，实际应用中可以使用tokenizer
        return f"[CLS] {query_text} [SEP] {candidate_text} [SEP]"
    
    def _save_intermediate_results(self, 
                                 candidates: List[ContextCandidate],
                                 queries: List[QueryContext],
                                 training_data: EmbeddingTrainingData):
        """保存中间结果"""
        os.makedirs(self.config.output_dir, exist_ok=True)
        
        # 保存候选上下文
        candidates_file = os.path.join(self.config.output_dir, "candidates.json")
        with open(candidates_file, 'w', encoding='utf-8') as f:
            json.dump([c.to_dict() for c in candidates], f, ensure_ascii=False, indent=2)
        
        # 保存查询上下文
        queries_file = os.path.join(self.config.output_dir, "queries.json")
        with open(queries_file, 'w', encoding='utf-8') as f:
            json.dump([q.to_dict() for q in queries], f, ensure_ascii=False, indent=2)
        
        # 保存训练数据
        training_file = os.path.join(self.config.output_dir, "embedding_training_data.json")
        training_data.save_to_json(training_file)
        
        print(f"   中间结果已保存到: {self.config.output_dir}")
    
    def save_results(self, 
                    embedding_data: EmbeddingTrainingData,
                    rerank_data: Optional[RerankTrainingData] = None):
        """保存最终结果"""
        os.makedirs(self.config.output_dir, exist_ok=True)
        
        # 保存embedding训练数据
        embedding_file = os.path.join(self.config.output_dir, "embedding_training_data.json")
        embedding_data.save_to_json(embedding_file)
        
        # 保存rerank训练数据
        if rerank_data:
            rerank_file = os.path.join(self.config.output_dir, "rerank_training_data.json")
            rerank_data.save_to_json(rerank_file)
        
        # 保存统计信息
        stats = self._generate_statistics(embedding_data, rerank_data)
        stats_file = os.path.join(self.config.output_dir, "statistics.json")
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存到: {self.config.output_dir}")
    
    def _generate_statistics(self, 
                           embedding_data: EmbeddingTrainingData,
                           rerank_data: Optional[RerankTrainingData] = None) -> Dict[str, Any]:
        """生成统计信息"""
        stats = {
            "embedding_data": {
                "total_pairs": len(embedding_data.pairs),
                "unique_queries": len(set(pair.query.function_name for pair in embedding_data.pairs)),
                "context_types": {},
                "file_distribution": {}
            }
        }
        
        # 统计上下文类型分布
        for pair in embedding_data.pairs:
            pos_type = pair.positive.context_type.value
            neg_type = pair.negative.context_type.value
            
            stats["embedding_data"]["context_types"][pos_type] = \
                stats["embedding_data"]["context_types"].get(pos_type, 0) + 1
            stats["embedding_data"]["context_types"][neg_type] = \
                stats["embedding_data"]["context_types"].get(neg_type, 0) + 1
        
        # 统计文件分布
        for pair in embedding_data.pairs:
            pos_file = pair.positive.file_path
            neg_file = pair.negative.file_path
            
            stats["embedding_data"]["file_distribution"][pos_file] = \
                stats["embedding_data"]["file_distribution"].get(pos_file, 0) + 1
            stats["embedding_data"]["file_distribution"][neg_file] = \
                stats["embedding_data"]["file_distribution"].get(neg_file, 0) + 1
        
        # Rerank数据统计
        if rerank_data and rerank_data.labels:
            total_samples = len(rerank_data.labels)
            positive_samples = sum(rerank_data.labels)
            stats["rerank_data"] = {
                "total_samples": total_samples,
                "positive_samples": positive_samples,
                "negative_samples": total_samples - positive_samples,
                "positive_ratio": positive_samples / total_samples if total_samples > 0 else 0
            }
        
        return stats 