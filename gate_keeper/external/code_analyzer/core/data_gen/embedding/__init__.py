"""
Embedding上下文训练数据生成模块

提供代码选择上下文的embedding模型训练数据生成功能
"""

from .candidate_sampler import CandidateSampler, SamplingConfig
from .context_extractor import ContextExtractor, ExtractionConfig
from .generator import EmbeddingDataGenerator, GeneratorConfig
from .models import (ContextCandidate, ContextType, EmbeddingPair,
                     EmbeddingTrainingData, QueryContext, RerankTrainingData)

__all__ = [
    # 数据模型
    'EmbeddingTrainingData',
    'ContextCandidate', 
    'QueryContext',
    'EmbeddingPair',
    'RerankTrainingData',
    'ContextType',
    
    # 生成器
    'EmbeddingDataGenerator',
    
    # 工具类
    'ContextExtractor',
    'CandidateSampler',
    
    # 配置类
    'ExtractionConfig',
    'SamplingConfig',
    'GeneratorConfig'
]