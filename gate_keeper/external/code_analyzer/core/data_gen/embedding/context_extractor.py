"""
上下文提取器

从代码中提取候选上下文片段，用于embedding训练
"""

import os
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from core.ast_parser import ASTParserFactory
from models.function import Function
from models.macro import Macro
from models.struct import Struct

from .models import ContextCandidate, ContextType


@dataclass
class ExtractionConfig:
    """提取配置"""
    max_context_length: int = 500        # 最大上下文长度
    min_context_length: int = 10         # 最小上下文长度
    include_comments: bool = True        # 是否包含注释
    include_imports: bool = True         # 是否包含导入语句
    max_functions_per_file: int = 50     # 每个文件最大函数数量
    max_structs_per_file: int = 20       # 每个文件最大结构体数量
    max_macros_per_file: int = 30        # 每个文件最大宏数量


class ContextExtractor:
    """上下文提取器"""
    
    def __init__(self, config: Optional[ExtractionConfig] = None):
        self.config = config or ExtractionConfig()
        self.parsers = {
            "np": ASTParserFactory.create("np"),
            "c": ASTParserFactory.create("c"),
            "python": ASTParserFactory.create("python")
        }
    
    def extract_from_file(self, file_path: str) -> List[ContextCandidate]:
        """从单个文件中提取上下文"""
        candidates = []
        
        try:
            # 确定语言类型
            language = self._get_language_from_path(file_path)
            if language not in self.parsers:
                print(f"    不支持的语言类型: {language}")
                return candidates
            
            parser = self.parsers[language]
            
            # 读取文件内容
            from utils.file_utils import read_file_safe
            content = read_file_safe(file_path)
            
            # 提取函数定义
            functions = parser.extract_functions(file_path, content)
            candidates.extend(self._extract_function_contexts(functions, file_path))
            
            # 提取结构体定义
            structs = parser.extract_structs(file_path, content)
            candidates.extend(self._extract_struct_contexts(structs, file_path))
            
            # 提取宏定义
            macros = parser.extract_macros(file_path, content)
            candidates.extend(self._extract_macro_contexts(macros, file_path))
            
            # 提取导入语句
            if self.config.include_imports:
                import_contexts = self._extract_import_contexts(content, file_path)
                candidates.extend(import_contexts)
            
            # 提取变量定义
            variable_contexts = self._extract_variable_contexts(content, file_path)
            candidates.extend(variable_contexts)
            
        except Exception as e:
            print(f"    提取文件 {file_path} 时出错: {e}")
        
        return candidates
    
    def extract_from_directory(self, directory_path: str) -> List[ContextCandidate]:
        """从目录中提取上下文"""
        all_candidates = []
        
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if self._is_code_file(file):
                    file_path = os.path.join(root, file)
                    candidates = self.extract_from_file(file_path)
                    all_candidates.extend(candidates)
        
        return all_candidates
    
    def _get_language_from_path(self, file_path: str) -> str:
        """根据文件路径确定语言类型"""
        ext = os.path.splitext(file_path)[1].lower()
        
        if ext == '.asm':
            return "np"
        elif ext in ['.c', '.h', '.cpp', '.hpp']:
            return "c"
        elif ext == '.py':
            return "python"
        else:
            return "c"  # 默认使用C解析器
    
    def _is_code_file(self, filename: str) -> bool:
        """判断是否为代码文件"""
        code_extensions = {'.c', '.h', '.cpp', '.hpp', '.asm', '.py'}
        ext = os.path.splitext(filename)[1].lower()
        return ext in code_extensions
    
    def _extract_function_contexts(self, functions: List[Function], file_path: str) -> List[ContextCandidate]:
        """提取函数上下文"""
        contexts = []
        
        for func in functions[:self.config.max_functions_per_file]:
            if not func.code or len(func.code) < self.config.min_context_length:
                continue
            
            # 清理代码内容
            cleaned_code = self._clean_code_content(func.code)
            if len(cleaned_code) > self.config.max_context_length:
                cleaned_code = cleaned_code[:self.config.max_context_length] + "..."
            
            context = ContextCandidate(
                content=cleaned_code,
                context_type=ContextType.FUNCTION_DEF,
                file_path=file_path,
                line_number=getattr(func.range, 'start_line', 0),
                entity_name=func.name,
                code_range={"start_line": getattr(func.range, 'start_line', 0), "end_line": getattr(func.range, 'end_line', 0)},
                metadata={
                    "return_type": func.signature.return_type if func.signature else None,
                    "parameters": [p.name for p in func.signature.parameters] if func.signature else []
                }
            )
            contexts.append(context)
        
        return contexts
    
    def _extract_struct_contexts(self, structs: List[Struct], file_path: str) -> List[ContextCandidate]:
        """提取结构体上下文"""
        contexts = []
        
        for struct in structs[:self.config.max_structs_per_file]:
            if not struct.fields:
                continue
            
            # 构建结构体定义文本
            struct_text = f"struct {struct.name} {{\n"
            for field in struct.fields:
                struct_text += f"    {field.type_name} {field.name};\n"
            struct_text += "};"
            
            if len(struct_text) > self.config.max_context_length:
                struct_text = struct_text[:self.config.max_context_length] + "..."
            
            context = ContextCandidate(
                content=struct_text,
                context_type=ContextType.STRUCT_DEF,
                file_path=file_path,
                line_number=getattr(struct, 'start_line', 0),
                entity_name=struct.name,
                code_range={"start_line": getattr(struct, 'start_line', 0), "end_line": getattr(struct, 'end_line', 0)},
                metadata={
                    "field_count": len(struct.fields),
                    "fields": [getattr(f, 'name', '') for f in struct.fields]
                }
            )
            contexts.append(context)
        
        return contexts
    
    def _extract_macro_contexts(self, macros: List[Macro], file_path: str) -> List[ContextCandidate]:
        """提取宏上下文"""
        contexts = []
        
        for macro in macros[:self.config.max_macros_per_file]:
            if not macro.value:
                continue
            
            macro_text = f"#define {macro.name} {macro.value}"
            
            if len(macro_text) > self.config.max_context_length:
                macro_text = macro_text[:self.config.max_context_length] + "..."
            
            context = ContextCandidate(
                content=macro_text,
                context_type=ContextType.MACRO_DEF,
                file_path=file_path,
                line_number=getattr(macro.range, 'start_line', 0),
                entity_name=macro.name,
                code_range={"start_line": getattr(macro.range, 'start_line', 0), "end_line": getattr(macro.range, 'end_line', 0)},
                metadata={
                    "definition": getattr(macro, 'value', ''),
                    "parameters": getattr(macro, 'parameters', [])
                }
            )
            contexts.append(context)
        
        return contexts
    
    def _extract_import_contexts(self, content: str, file_path: str) -> List[ContextCandidate]:
        """提取导入语句上下文"""
        contexts = []
        
        # 匹配导入语句
        import_patterns = [
            r'#include\s+["<][^">]+[">]',  # C/C++ include
            r'import\s+[\w\s,]+',          # Python import
            r'from\s+\w+\s+import',        # Python from import
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            for pattern in import_patterns:
                if re.match(pattern, line.strip()):
                    context = ContextCandidate(
                        content=line.strip(),
                        context_type=ContextType.IMPORT_STMT,
                        file_path=file_path,
                        line_number=i + 1,
                        entity_name=f"import_{i}",
                        code_range={"start_line": i + 1, "end_line": i + 1},
                        metadata={"import_type": "include" if "#include" in line else "import"}
                    )
                    contexts.append(context)
                    break
        
        return contexts
    
    def _extract_variable_contexts(self, content: str, file_path: str) -> List[ContextCandidate]:
        """提取变量定义上下文"""
        contexts = []
        
        # 匹配变量定义
        var_patterns = [
            r'(?:int|uint8|uint16|uint32|char|float|double)\s+(\w+)\s*[=;]',
            r'(?:let|var|const)\s+(\w+)\s*[=:]',
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            for pattern in var_patterns:
                match = re.search(pattern, line.strip())
                if match:
                    var_name = match.group(1)
                    context = ContextCandidate(
                        content=line.strip(),
                        context_type=ContextType.VARIABLE_DEF,
                        file_path=file_path,
                        line_number=i + 1,
                        entity_name=var_name,
                        code_range={"start_line": i + 1, "end_line": i + 1},
                        metadata={"variable_type": "declaration"}
                    )
                    contexts.append(context)
                    break
        
        return contexts
    
    def _clean_code_content(self, code: str) -> str:
        """清理代码内容"""
        if not self.config.include_comments:
            # 移除注释
            code = re.sub(r'//.*$', '', code, flags=re.MULTILINE)  # 单行注释
            code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)  # 多行注释
        
        # 移除多余空白
        lines = [line.strip() for line in code.split('\n') if line.strip()]
        return '\n'.join(lines) 