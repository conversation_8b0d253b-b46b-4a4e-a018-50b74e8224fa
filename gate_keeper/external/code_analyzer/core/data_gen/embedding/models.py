"""
Embedding训练数据模型

定义embedding模型训练过程中使用的数据结构
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional


class ContextType(Enum):
    """上下文类型"""
    FUNCTION_DEF = "function_definition"
    VARIABLE_DEF = "variable_definition"
    STRUCT_DEF = "struct_definition"
    MACRO_DEF = "macro_definition"
    IMPORT_STMT = "import_statement"
    CLASS_DEF = "class_definition"
    METHOD_DEF = "method_definition"


@dataclass
class ContextCandidate:
    """候选上下文片段"""
    content: str                    # 上下文内容
    context_type: ContextType       # 上下文类型
    file_path: str                  # 文件路径
    line_number: int                # 行号
    entity_name: str                # 实体名称
    code_range: Dict[str, int]      # 代码范围 {start_line, end_line}
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "content": self.content,
            "context_type": self.context_type.value,
            "file_path": self.file_path,
            "line_number": self.line_number,
            "entity_name": self.entity_name,
            "code_range": self.code_range,
            "metadata": self.metadata
        }


@dataclass
class QueryContext:
    """查询上下文（代码补全任务）"""
    before: str                     # 补全前的代码
    after: str                      # 补全后的代码
    expected: str                   # 期望的补全内容
    file_path: str                  # 文件路径
    function_name: str              # 函数名称
    line_number: int                # 补全位置行号
    instruction: Optional[str] = None  # 可选的指令
    env_info: Optional[str] = None     # 环境信息
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据
    
    def get_query_text(self) -> str:
        """获取查询文本（用于embedding）"""
        parts = [self.before, "<hole>", self.after]
        if self.instruction:
            parts.insert(0, f"<instruction>{self.instruction}</instruction>")
        if self.env_info:
            parts.insert(0, f"<env>{self.env_info}</env>")
        return "\n".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "before": self.before,
            "after": self.after,
            "expected": self.expected,
            "file_path": self.file_path,
            "function_name": self.function_name,
            "line_number": self.line_number,
            "instruction": self.instruction,
            "env_info": self.env_info,
            "metadata": self.metadata
        }


@dataclass
class EmbeddingPair:
    """Embedding训练对"""
    query: QueryContext             # 查询上下文
    positive: ContextCandidate      # 正样本（相关上下文）
    negative: ContextCandidate      # 负样本（不相关上下文）
    similarity_score: float = 0.0   # 相似度分数
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query.to_dict(),
            "positive": self.positive.to_dict(),
            "negative": self.negative.to_dict(),
            "similarity_score": self.similarity_score
        }


@dataclass
class EmbeddingTrainingData:
    """Embedding训练数据"""
    pairs: List[EmbeddingPair] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_pair(self, pair: EmbeddingPair):
        """添加训练对"""
        self.pairs.append(pair)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "pairs": [pair.to_dict() for pair in self.pairs],
            "metadata": self.metadata
        }
    
    def save_to_json(self, file_path: str):
        """保存到JSON文件"""
        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_json(cls, file_path: str) -> 'EmbeddingTrainingData':
        """从JSON文件加载"""
        import json
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        pairs = []
        for pair_data in data.get('pairs', []):
            query = QueryContext(**pair_data['query'])
            positive = ContextCandidate(**pair_data['positive'])
            negative = ContextCandidate(**pair_data['negative'])
            pair = EmbeddingPair(
                query=query,
                positive=positive,
                negative=negative,
                similarity_score=pair_data.get('similarity_score', 0.0)
            )
            pairs.append(pair)
        
        return cls(pairs=pairs, metadata=data.get('metadata', {}))


@dataclass
class RerankTrainingData:
    """Rerank训练数据"""
    inputs: List[str] = field(default_factory=list)  # 拼接的输入文本
    labels: List[int] = field(default_factory=list)  # 标签（1=相关，0=不相关）
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_sample(self, input_text: str, label: int):
        """添加训练样本"""
        self.inputs.append(input_text)
        self.labels.append(label)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "inputs": self.inputs,
            "labels": self.labels,
            "metadata": self.metadata
        }
    
    def save_to_json(self, file_path: str):
        """保存到JSON文件"""
        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_json(cls, file_path: str) -> 'RerankTrainingData':
        """从JSON文件加载"""
        import json
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return cls(
            inputs=data.get('inputs', []),
            labels=data.get('labels', []),
            metadata=data.get('metadata', {})
        ) 