"""
候选采样器

用于生成embedding训练的正负样本对
"""

import random
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from .context_extractor import ContextExtractor
from .models import ContextCandidate, EmbeddingPair, QueryContext


@dataclass
class SamplingConfig:
    """采样配置"""
    positive_ratio: float = 0.7          # 正样本比例
    negative_ratio: float = 0.3          # 负样本比例
    max_candidates_per_query: int = 10   # 每个查询的最大候选数
    min_similarity_score: float = 0.1    # 最小相似度分数
    max_similarity_score: float = 0.9    # 最大相似度分数
    use_hard_negative: bool = False      # 是否使用hard negative（预留配置）
    hard_negative_threshold: float = 0.3 # hard negative相似度阈值
    random_seed: int = 42                # 随机种子


class CandidateSampler:
    """候选采样器"""
    
    def __init__(self, config: Optional[SamplingConfig] = None):
        self.config = config or SamplingConfig()
        random.seed(self.config.random_seed)
    
    def sample_positive_candidates(self, 
                                 query: QueryContext, 
                                 all_candidates: List[ContextCandidate]) -> List[ContextCandidate]:
        """采样正样本候选 - 两级策略：优先expected引用，回退before/after使用"""
        positive_candidates = []
        
        # 策略一：从expected中精准提取引用实体（优先使用）
        expected_entities = self._extract_entities_from_expected(query.expected)
        
        # 精准匹配：expected中出现的实体就是正例
        for candidate in all_candidates:
            if candidate.entity_name in expected_entities:
                positive_candidates.append(candidate)
        
        # 策略二：如果expected中没有引用实体，回退到before/after中使用过的上下文
        if not positive_candidates:
            before_after_entities = self._extract_entities_from_context(query.before + " " + query.after)
            
            for candidate in all_candidates:
                if candidate.entity_name in before_after_entities:
                    positive_candidates.append(candidate)
        
        # 限制数量
        max_positive = int(self.config.max_candidates_per_query * self.config.positive_ratio)
        if len(positive_candidates) > max_positive:
            positive_candidates = random.sample(positive_candidates, max_positive)
        
        return positive_candidates
    
    def sample_negative_candidates(self, 
                                 query: QueryContext, 
                                 all_candidates: List[ContextCandidate],
                                 positive_candidates: List[ContextCandidate]) -> List[ContextCandidate]:
        """采样负样本候选 - 支持hard negative和随机采样"""
        negative_candidates = []
        
        # 排除正样本
        positive_entity_names = {c.entity_name for c in positive_candidates}
        available_candidates = [c for c in all_candidates if c.entity_name not in positive_entity_names]
        
        if self.config.use_hard_negative:
            # 使用hard negative：通过相似度查找困难负例
            negative_candidates = self._sample_hard_negatives(query, available_candidates)
        else:
            # 默认随机采样结构相似但未被引用的上下文片段
            negative_candidates = self._sample_random_negatives(available_candidates)
        
        # 限制数量
        max_negative = int(self.config.max_candidates_per_query * self.config.negative_ratio)
        if len(negative_candidates) > max_negative:
            negative_candidates = random.sample(negative_candidates, max_negative)
        
        return negative_candidates
    
    def create_training_pairs(self, 
                            query: QueryContext,
                            positive_candidates: List[ContextCandidate],
                            negative_candidates: List[ContextCandidate]) -> List[EmbeddingPair]:
        """创建训练对"""
        pairs = []
        
        # 确保有足够的候选
        if not positive_candidates or not negative_candidates:
            return pairs
        
        # 创建正负样本对
        for positive in positive_candidates:
            for negative in negative_candidates:
                # 计算相似度分数
                similarity_score = self._calculate_similarity_score(query, positive, negative)
                
                pair = EmbeddingPair(
                    query=query,
                    positive=positive,
                    negative=negative,
                    similarity_score=similarity_score
                )
                pairs.append(pair)
        
        return pairs
    
    def _extract_entities_from_expected(self, expected: str) -> List[str]:
        """从expected中精准提取引用实体名称"""
        entities = []
        
        # 提取函数调用 - 例如 func1(xxx) 中的 func1
        function_calls = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', expected)
        entities.extend(function_calls)
        
        # 提取变量引用 - 例如 var1 = xxx 中的 var1
        variable_refs = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*[=+\-*/<>!&|]', expected)
        entities.extend(variable_refs)
        
        # 提取结构体成员访问 - 例如 struct.field 中的 field
        struct_members = re.findall(r'\.\s*([a-zA-Z_][a-zA-Z0-9_]*)', expected)
        entities.extend(struct_members)
        
        # 提取宏使用 - 例如 MAX_SIZE 中的 MAX_SIZE
        macro_uses = re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', expected)
        entities.extend(macro_uses)
        
        # 提取类型名 - 例如 int, char, struct_name 等
        type_names = re.findall(r'\b(int|char|float|double|void|uint\d+|struct\s+[a-zA-Z_][a-zA-Z0-9_]*)\b', expected)
        entities.extend(type_names)
        
        # 去重并过滤掉常见的关键字
        common_keywords = {'if', 'else', 'for', 'while', 'return', 'break', 'continue', 'switch', 'case', 'default'}
        entities = [e for e in set(entities) if e not in common_keywords]
        
        return entities
    
    def _extract_entities_from_context(self, context: str) -> List[str]:
        """从before/after上下文中提取使用过的实体名称"""
        entities = []
        
        # 提取函数调用 - 例如 func1(xxx) 中的 func1
        function_calls = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', context)
        entities.extend(function_calls)
        
        # 提取变量声明和引用 - 例如 int var1 或 var1 = xxx 中的 var1
        variable_decls = re.findall(r'\b(int|char|float|double|void|uint\d+)\s+([a-zA-Z_][a-zA-Z0-9_]*)', context)
        entities.extend([var[1] for var in variable_decls])
        
        variable_refs = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*[=+\-*/<>!&|]', context)
        entities.extend(variable_refs)
        
        # 提取结构体成员访问 - 例如 struct.field 中的 field
        struct_members = re.findall(r'\.\s*([a-zA-Z_][a-zA-Z0-9_]*)', context)
        entities.extend(struct_members)
        
        # 提取宏使用 - 例如 MAX_SIZE 中的 MAX_SIZE
        macro_uses = re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', context)
        entities.extend(macro_uses)
        
        # 提取结构体定义 - 例如 struct Point 中的 Point
        struct_defs = re.findall(r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)', context)
        entities.extend(struct_defs)
        
        # 去重并过滤掉常见的关键字
        common_keywords = {'if', 'else', 'for', 'while', 'return', 'break', 'continue', 'switch', 'case', 'default', 'main'}
        entities = [e for e in set(entities) if e not in common_keywords]
        
        return entities
    
    def _sample_hard_negatives(self, 
                              query: QueryContext, 
                              available_candidates: List[ContextCandidate]) -> List[ContextCandidate]:
        """采样hard negative - 通过相似度查找困难负例"""
        hard_negatives = []
        
        for candidate in available_candidates:
            # 计算与查询的相似度
            similarity = self._calculate_semantic_similarity(query.get_query_text(), candidate.content)
            
            # 相似度在阈值范围内的候选作为hard negative
            # 这些候选与查询相似但不相关，是困难的负例
            if 0.1 <= similarity <= self.config.hard_negative_threshold:
                hard_negatives.append(candidate)
        
        return hard_negatives
    
    def _sample_random_negatives(self, available_candidates: List[ContextCandidate]) -> List[ContextCandidate]:
        """随机采样负例"""
        # 从可用候选中随机选择
        return available_candidates.copy()
    
    def _calculate_similarity_score(self, 
                                  query: QueryContext,
                                  positive: ContextCandidate,
                                  negative: ContextCandidate) -> float:
        """计算相似度分数 - 基于两级正样本策略和负样本相似度"""
        # 基础分数
        base_score = 0.5
        
        # 正样本：基于两级策略的分数
        expected_entities = self._extract_entities_from_expected(query.expected)
        if positive.entity_name in expected_entities:
            base_score += 0.4  # 类型一：expected中引用的上下文（最高分）
        else:
            # 检查是否为类型二：before/after中使用过的上下文
            before_after_entities = self._extract_entities_from_context(query.before + " " + query.after)
            if positive.entity_name in before_after_entities:
                base_score += 0.2  # 类型二：before/after中使用过的上下文（中等分）
        
        # 负样本：基于相似度的分数
        negative_similarity = self._calculate_semantic_similarity(query.get_query_text(), negative.content)
        if negative_similarity > 0.3:
            base_score -= 0.2  # 相似但不相关的负例降低分数
        
        # 根据上下文类型微调
        if positive.context_type.value == "function_definition":
            base_score += 0.1
        elif positive.context_type.value == "struct_definition":
            base_score += 0.05
        
        # 限制在配置范围内
        return max(self.config.min_similarity_score, 
                  min(self.config.max_similarity_score, base_score))
    
    def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度（简化版本）"""
        # 这里使用简单的词汇重叠度作为语义相似度
        # 实际应用中可以使用预训练的embedding模型
        
        words1 = set(re.findall(r'\b\w+\b', text1.lower()))
        words2 = set(re.findall(r'\b\w+\b', text2.lower()))
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def balance_candidates(self, 
                         positive_candidates: List[ContextCandidate],
                         negative_candidates: List[ContextCandidate]) -> Tuple[List[ContextCandidate], List[ContextCandidate]]:
        """平衡正负样本数量"""
        min_count = min(len(positive_candidates), len(negative_candidates))
        
        if len(positive_candidates) > min_count:
            positive_candidates = random.sample(positive_candidates, min_count)
        
        if len(negative_candidates) > min_count:
            negative_candidates = random.sample(negative_candidates, min_count)
        
        return positive_candidates, negative_candidates 