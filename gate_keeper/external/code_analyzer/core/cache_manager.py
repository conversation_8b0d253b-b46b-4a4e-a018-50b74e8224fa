"""
Cache Manager Core

仓库索引缓存管理器，负责缓存的管理和清理
"""

import base64
import hashlib
import json
import os
import threading
from collections import OrderedDict
from datetime import datetime
from pathlib import Path
from typing import Optional

from .repository_index import RepositoryIndex


class RepositoryIndexCacheManager:
    """仓库索引缓存管理器"""
    
    def __init__(
        self,
        cache_root: str = ".cache",
        max_repos: int = 10,
        max_branches_per_repo: int = 5,
        max_commits_per_branch: int = 20,
    ):
        self.cache_root = cache_root
        self.max_repos = max_repos
        self.max_branches_per_repo = max_branches_per_repo
        self.max_commits_per_branch = max_commits_per_branch
        os.makedirs(cache_root, exist_ok=True)
        self._lock = threading.Lock()  # 加锁保证线程安全

    def _hash_repo(self, repo_dir: str) -> str:
        """生成仓库哈希值"""
        return hashlib.sha1(str(repo_dir).encode()).hexdigest()[:8]

    def _sanitize_name(self, name: str) -> str:
        """清理文件名，移除不安全的字符"""
        # 移除或替换不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            name = name.replace(char, '_')
        return name

    def _get_commit_path(self, repo_dir: str, branch: str, commit_sha: str) -> str:
        """获取缓存文件路径"""
        relative_path = Path(repo_dir)
        repo_dir_abs = relative_path.resolve()
        repo_hash = self._hash_repo(repo_dir_abs)
        safe_branch = self._sanitize_name(branch)  
        return str(Path(self.cache_root) / repo_hash / safe_branch / f"{commit_sha}.json")

    def save(self, repo_index: RepositoryIndex):
        """保存仓库索引到缓存"""
        with self._lock:  # 使用锁保证写操作和清理操作的互斥执行
            path = self._get_commit_path(repo_index.repo_dir, repo_index.branch, repo_index.commit_sha)
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, "w", encoding="utf-8") as f:
                repo_dict = repo_index.model_dump()
                json.dump(repo_dict, f, ensure_ascii=False, indent=2)
            self._touch(path)
            self._enforce_limits()

    def load(self, repo_dir: str, branch: str, commit_sha: str) -> Optional[RepositoryIndex]:
        """从缓存加载仓库索引"""
        path = self._get_commit_path(repo_dir, branch, commit_sha)
        try:
            with open(path, 'r', encoding="utf-8") as f:
                data = json.load(f)
                repo_index = RepositoryIndex.from_dict(data)
                return repo_index
        except FileNotFoundError:
            return None
        except json.JSONDecodeError as e:
            print(f"Warning: Failed to parse cache file {path}: {e}")
            return None
        except Exception as e:
            print(f"Warning: Failed to load cache file {path}: {e}")
            return None

    def _touch(self, path: str):
        """更新文件访问时间"""
        now = datetime.now().timestamp()
        os.utime(path, (now, now))

    def _enforce_limits(self):
        """强制执行缓存限制"""
        # 注意：此方法只在 save() 中调用，且 save() 已加锁，故这里不再单独加锁
        repo_map = {}

        for repo_hash in os.listdir(self.cache_root):
            repo_path = os.path.join(self.cache_root, repo_hash)
            if not os.path.isdir(repo_path):
                continue
            repo_map[repo_hash] = {}

            for branch in os.listdir(repo_path):
                branch_path = os.path.join(repo_path, branch)
                if not os.path.isdir(branch_path):
                    continue
                commits = sorted(
                    (os.path.join(branch_path, f) for f in os.listdir(branch_path) if f.endswith(".json")),
                    key=os.path.getmtime,
                )
                # 限制 commit 数量
                if len(commits) > self.max_commits_per_branch:
                    for file in commits[:-self.max_commits_per_branch]:
                        os.remove(file)
                repo_map[repo_hash][branch] = commits[-self.max_commits_per_branch:]

            # 限制 branch 数量
            if len(repo_map[repo_hash]) > self.max_branches_per_repo:
                branches_sorted = sorted(
                    repo_map[repo_hash].items(),
                    key=lambda kv: os.path.getmtime(kv[1][-1]),  # 最后一个 commit 的时间
                )
                for branch, files in branches_sorted[:-self.max_branches_per_repo]:
                    branch_path = os.path.join(repo_path, branch)
                    for file in files:
                        os.remove(file)
                    os.rmdir(branch_path)

        # 限制 repo 数量
        if len(repo_map) > self.max_repos:
            repos_sorted = sorted(
                ((h, os.path.getmtime(os.path.join(self.cache_root, h))) for h in repo_map),
                key=lambda kv: kv[1],
            )
            for repo_hash, _ in repos_sorted[:-self.max_repos]:
                repo_path = os.path.join(self.cache_root, repo_hash)
                for root, dirs, files in os.walk(repo_path, topdown=False):
                    for name in files:
                        os.remove(os.path.join(root, name))
                    for name in dirs:
                        os.rmdir(os.path.join(root, name))
                os.rmdir(repo_path)

    def clear_cache(self):
        """清空所有缓存"""
        with self._lock:
            for root, dirs, files in os.walk(self.cache_root, topdown=False):
                for name in files:
                    os.remove(os.path.join(root, name))
                for name in dirs:
                    os.rmdir(os.path.join(root, name))

    def get_cache_info(self) -> dict:
        """获取缓存信息"""
        info = {
            "cache_root": self.cache_root,
            "max_repos": self.max_repos,
            "max_branches_per_repo": self.max_branches_per_repo,
            "max_commits_per_branch": self.max_commits_per_branch,
            "current_repos": 0,
            "total_files": 0,
            "total_size": 0
        }
        
        if os.path.exists(self.cache_root):
            for repo_hash in os.listdir(self.cache_root):
                repo_path = os.path.join(self.cache_root, repo_hash)
                if os.path.isdir(repo_path):
                    info["current_repos"] += 1
                    for root, dirs, files in os.walk(repo_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            info["total_files"] += 1
                            info["total_size"] += os.path.getsize(file_path)
        
        return info 