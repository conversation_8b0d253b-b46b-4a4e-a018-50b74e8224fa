"""
Repository Index Factory Core

仓库索引工厂，负责创建和管理仓库索引实例
"""

from typing import Optional

from .cache_manager import RepositoryIndexCacheManager
from .git_adapter import DefaultGitAdapter, GitServiceAdapter
from .repository_index import RepositoryIndex

# from gate_keeper.config import config



class RepositoryIndexFactory:
    """仓库索引工厂，负责创建和管理仓库索引实例。"""

    @staticmethod
    def get_or_build(
        repo_dir: str,
        branch: str = "main",
        commit_sha: Optional[str] = None,
        exclude_patterns: Optional[list] = None,
        cache_dir: Optional[str] = None,
        max_repos: int = 10,
        max_branches_per_repo: int = 5,
        max_commits_per_branch: int = 20,
        git_adapter: Optional[GitServiceAdapter] = None,
    ) -> RepositoryIndex:
        if exclude_patterns is None:
            exclude_patterns = []
        """
        获取或创建仓库索引。优先从缓存获取，如果缓存不存在则创建新索引。

        Args:
            repo_dir: 仓库目录路径
            branch: 分支名
            commit_sha: 可选的提交SHA，如果不提供则使用分支最新提交
            exclude_patterns: 可选的排除模式列表
            cache_dir: 缓存目录路径
            max_repos: 最大缓存仓库数
            max_branches_per_repo: 每个仓库最大分支数
            max_commits_per_branch: 每个分支最大提交数
            git_adapter: Git 服务适配器

        Returns:
            RepositoryIndex: 仓库索引实例

        Raises:
            ValueError: 参数无效
            Exception: 其他错误
        """
        try:
            repo_index = None 
            cache_manager = None 

            if not repo_dir or not branch:
                raise ValueError("repo_dir and branch are required")

            # 使用默认 Git 适配器（如果没有提供）
            if git_adapter is None:
                git_adapter = DefaultGitAdapter()

            # 如果提供了缓存目录，尝试从缓存加载
            if cache_dir:
                try:
                    cache_manager = RepositoryIndexCacheManager(
                        cache_root=cache_dir,
                        max_repos=max_repos,
                        max_branches_per_repo=max_branches_per_repo,
                        max_commits_per_branch=max_commits_per_branch
                    )
                    repo_index = cache_manager.load(repo_dir, branch, commit_sha or branch)
                    if repo_index:
                        print(f"Loaded repository index from cache for {repo_dir}@{commit_sha or branch}")
                        return repo_index
                except Exception as e:
                    print(f"Warning: Failed to load from cache: {str(e)}")

            # 创建新索引
            repo_index = RepositoryIndex(
                repo_dir=repo_dir,
                branch=branch,
                commit_sha=commit_sha,
                exclude_patterns=exclude_patterns,
                git_adapter=git_adapter
            )

            # 构建索引并保存缓存
            try:
                repo_index.build()
                if cache_manager:
                    cache_manager.save(repo_index)
                    print(f"Indexed and cached repository {repo_dir}@{commit_sha or branch}")
                else:
                    print(f"Indexed repository {repo_dir}@{commit_sha or branch}")
            except Exception as e:
                print(f"Error: Failed to index repository: {str(e)}")
                raise

            return repo_index
        except Exception as e:
            print(f"Error in RepositoryIndexFactory.get_or_build: {str(e)}")
            raise

    @staticmethod
    def build_without_cache(
        repo_dir: str,
        branch: str = "main",
        commit_sha: Optional[str] = None,
        exclude_patterns: Optional[list] = None,
    ) -> RepositoryIndex:
        if exclude_patterns is None:
            exclude_patterns = []
        """
        直接构建仓库索引，不使用缓存。

        Args:
            repo_dir: 仓库目录路径
            branch: 分支名
            commit_sha: 可选的提交SHA
            exclude_patterns: 可选的排除模式列表

        Returns:
            RepositoryIndex: 仓库索引实例
        """
        if not repo_dir or not branch:
            raise ValueError("repo_dir and branch are required")

        repo_index = RepositoryIndex(
            repo_dir=repo_dir,
            branch=branch,
            commit_sha=commit_sha,
            exclude_patterns=exclude_patterns
        )
        repo_index.build()
        return repo_index

    @staticmethod
    def clear_cache(cache_dir: str):
        """清空缓存"""
        cache_manager = RepositoryIndexCacheManager(cache_root=cache_dir)
        cache_manager.clear_cache()
        print(f"Cleared cache in {cache_dir}")

    @staticmethod
    def get_cache_info(cache_dir: str) -> dict:
        """获取缓存信息"""
        cache_manager = RepositoryIndexCacheManager(cache_root=cache_dir)
        return cache_manager.get_cache_info() 