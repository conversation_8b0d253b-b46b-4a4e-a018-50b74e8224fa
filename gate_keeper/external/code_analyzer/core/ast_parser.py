"""
AST Parser Core

AST解析器核心类，整合多语言解析功能
"""

import os
from abc import ABC, abstractmethod
from threading import Lock
from typing import List, Optional

from models.call_relation import FunctionCall
from models.function import Function
from models.macro import Macro
from parsers.base import BaseParser
from parsers.c_parser import CParser
from parsers.np_parser import NPParser
from parsers.python_parser import PythonParser


class ASTParser(ABC):
    """AST解析器抽象基类"""
    
    @abstractmethod
    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        """提取函数定义"""
        pass
    
    @abstractmethod
    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        """提取函数调用"""
        pass
    
    @abstractmethod
    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        """查找父级元素"""
        pass
    
    def extract_macros(self, file_path: str, file_content: Optional[str] = None) -> List[Macro]:
        """提取宏定义（默认实现返回空列表）"""
        return []


class ASTParserFactory:
    """AST解析器工厂类"""
    
    # 全局缓存和锁
    _parser_cache: dict[str, BaseParser] = {}
    _parser_lock = Lock()
    
    @classmethod
    def create(cls, language: str) -> BaseParser:
        """
        根据语言名创建或获取缓存的 AST 解析器实例。
        支持线程安全。
        """
        with cls._parser_lock:
            if language in cls._parser_cache:
                return cls._parser_cache[language]

            if language == "python":
                parser = PythonParser()
            elif language == "c":
                parser = CParser()
            elif language == "np":
                parser = NPParser()
            else:
                raise ValueError(f"不支持的语言类型: '{language}'")

            cls._parser_cache[language] = parser
            return parser
    
    @classmethod
    def clear_cache(cls):
        """清空解析器缓存"""
        with cls._parser_lock:
            cls._parser_cache.clear()


# 为了向后兼容，提供create_parser_by_lang函数
def create_parser_by_lang(lang: str) -> BaseParser:
    """根据语言名创建解析器（向后兼容函数）"""
    return ASTParserFactory.create(lang) 