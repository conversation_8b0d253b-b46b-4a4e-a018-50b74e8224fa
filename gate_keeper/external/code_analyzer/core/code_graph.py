"""
Code Graph

增强的代码图管理器，集成新的分析功能
"""

from typing import Dict, List, Optional

import networkx as nx

from .repository_index import RepositoryIndex
from .static_analyzer import StaticAnalyzer

# from gate_keeper.config import config



class CodeGraph:
    """增强的代码图管理器"""
    
    def __init__(self, repo_index: RepositoryIndex):
        self.repo_index = repo_index
        self.static_analyzer = StaticAnalyzer(repo_index)
        self.graph = repo_index.get_call_graph()
    
    @staticmethod
    def build_from_repository(repo_dir: str, branch: str = "main", 
                            exclude_patterns: Optional[List[str]] = None) -> "CodeGraph":
        if exclude_patterns is None:
            exclude_patterns = []
        """
        从仓库构建增强代码图
        
        Args:
            repo_dir: 仓库目录
            branch: 分支名
            exclude_patterns: 排除模式列表
            
        Returns:
            CodeGraph实例
        """
        repo_index = RepositoryIndex(
            repo_dir=repo_dir,
            branch=branch,
            exclude_patterns=exclude_patterns
        )
        repo_index.build()
        return CodeGraph(repo_index)
    
    # 基础功能（保持与原有 CodeGraph 的兼容性）
    
    def get_upstream_calls(self, function_id: str, max_depth: int = 5) -> List[List[str]]:
        """获取函数的上游调用链"""
        if "::" not in function_id:
            return []
        
        file_path, func_name = function_id.rsplit("::", 1)
        return self.static_analyzer.get_upstream_call_chains(func_name, file_path, max_depth)
    
    def get_downstream_calls(self, function_id: str, max_depth: int = 5) -> List[List[str]]:
        """获取函数的下游调用链"""
        if "::" not in function_id:
            return []
        
        file_path, func_name = function_id.rsplit("::", 1)
        return self.static_analyzer.get_downstream_call_chains(func_name, file_path, max_depth)
    
    def get_related_functions(self, function_id: str, radius: int = 3) -> List[str]:
        """获取相关函数"""
        if "::" not in function_id:
            return []
        
        file_path, func_name = function_id.rsplit("::", 1)
        related_funcs = self.static_analyzer.get_related_functions(func_name, file_path, radius)
        
        # 转换为函数ID格式
        result = []
        for func in related_funcs:
            func_id = f"{func.filepath}::{func.name}"
            result.append(func_id)
        
        return result
    
    def get_function_info(self, function_id: str) -> Optional[dict]:
        """获取函数信息"""
        if "::" not in function_id:
            return None
        
        file_path, func_name = function_id.rsplit("::", 1)
        func = self.repo_index.find_function_definition(func_name, file_path)
        
        if not func:
            return None
        
        return {
            "name": func.name,
            "filepath": func.filepath,
            "start_line": func.range.start_line,
            "end_line": func.range.end_line,
            "code": func.code,
            "signature": {
                "name": func.signature.name,
                "parameters": [{"name": p.name, "type": p.type_hint} for p in func.signature.parameters],
                "return_type": func.signature.return_type
            }
        }
    
    def get_graph_statistics(self) -> dict:
        """获取图统计信息"""
        return self.repo_index.get_graph_statistics()
    
    def find_cycles(self) -> List[List[str]]:
        """查找循环调用"""
        try:
            cycles = list(nx.simple_cycles(self.graph))
            return cycles
        except nx.NetworkXNoCycle:
            return []
    
    def get_strongly_connected_components(self) -> List[List[str]]:
        """获取强连通分量"""
        return list(nx.strongly_connected_components(self.graph))
    
    def get_topological_sort(self) -> List[str]:
        """获取拓扑排序"""
        try:
            return list(nx.topological_sort(self.graph))
        except nx.NetworkXError:
            # 如果有环，返回空列表
            return []
    
    def get_shortest_path(self, source_id: str, target_id: str) -> Optional[List[str]]:
        """获取两个函数之间的最短路径"""
        try:
            path = nx.shortest_path(self.graph, source_id, target_id)
            return path
        except nx.NetworkXNoPath:
            return None
    
    def get_all_paths(self, source_id: str, target_id: str, max_paths: int = 10) -> List[List[str]]:
        """获取两个函数之间的所有路径"""
        try:
            paths = list(nx.all_simple_paths(self.graph, source_id, target_id, cutoff=max_paths))
            return paths[:max_paths]
        except nx.NetworkXNoPath:
            return []
    
    # 新增的增强功能
    
    def get_module_analysis(self, module_path: str) -> Dict:
        """
        获取模块分析结果
        
        Args:
            module_path: 模块路径
            
        Returns:
            模块分析结果
        """
        # 获取模块依赖关系
        deps = self.static_analyzer.get_module_dependencies(module_path)
        
        # 获取跨模块调用
        cross_calls = self.static_analyzer.get_cross_module_calls(module_path)
        
        # 获取模块中的函数
        module_functions = []
        for func_name, funcs in self.repo_index.function_definitions.items():
            for func in funcs:
                if self.repo_index.paths_equal(func.filepath, module_path):
                    module_functions.append({
                        "name": func.name,
                        "start_line": func.range.start_line,
                        "end_line": func.range.end_line,
                        "signature": {"name": func.signature.name, "parameters": [{"name": p.name, "type": p.type_hint} for p in func.signature.parameters], "return_type": func.signature.return_type}
                    })
        
        # 计算模块复杂度
        complexity_score = len(module_functions) + len(cross_calls) * 2
        
        return {
            "module_path": module_path,
            "dependencies": deps,
            "cross_module_calls": cross_calls,
            "functions": module_functions,
            "statistics": {
                "function_count": len(module_functions),
                "import_count": len(deps["imports"]),
                "export_count": len(deps["exports"]),
                "dependent_count": len(deps["dependents"]),
                "cross_call_count": len(cross_calls),
                "complexity_score": complexity_score
            }
        }
    
    def get_import_impact_analysis(self, module_path: str) -> Dict:
        """
        获取导入影响分析
        
        Args:
            module_path: 模块路径
            
        Returns:
            导入影响分析结果
        """
        return self.static_analyzer.analyze_import_impact(module_path)
    
    def get_function_call_graph(self, function_id: str, depth: int = 2) -> Dict:
        """
        获取函数的调用图
        
        Args:
            function_id: 函数ID
            depth: 搜索深度
            
        Returns:
            调用图字典
        """
        if "::" not in function_id:
            return {"nodes": [], "edges": [], "center": None}
        
        file_path, func_name = function_id.rsplit("::", 1)
        return self.static_analyzer.get_function_call_graph(func_name, file_path, depth)
    
    def get_global_function_index(self) -> Dict[str, List[Dict]]:
        """获取全局函数索引"""
        return self.repo_index.get_global_function_index()
    
    def get_modules(self) -> Dict[str, Dict]:
        """获取所有模块信息"""
        modules = self.repo_index.graph_builder.get_modules()
        result = {}
        for path, module in modules.items():
            result[path] = {
                "path": module.path,
                "language": module.language,
                "node_count": len(module.node_ids),
                "import_count": len(module.imports),
                "export_count": len(module.exports)
            }
        return result
    
    def analyze_codebase_structure(self) -> Dict:
        """
        分析代码库结构
        
        Returns:
            代码库结构分析结果
        """
        modules = self.repo_index.graph_builder.get_modules()
        total_modules = len(modules)
        total_functions = len(self.repo_index.function_definitions)
        total_calls = len(self.repo_index.function_calls)
        
        # 分析语言分布
        language_distribution = {}
        for module in modules.values():
            lang = module.language
            language_distribution[lang] = language_distribution.get(lang, 0) + 1
        
        # 分析模块依赖关系
        dependency_matrix = {}
        for module_path in modules.keys():
            deps = self.static_analyzer.get_module_dependencies(module_path)
            dependency_matrix[module_path] = {
                "imports": deps["imports"],
                "dependents": deps["dependents"]
            }
        
        # 计算整体耦合度
        total_imports = sum(len(deps["imports"]) for deps in dependency_matrix.values())
        avg_coupling = total_imports / max(total_modules, 1)
        
        return {
            "overview": {
                "total_modules": total_modules,
                "total_functions": total_functions,
                "total_calls": total_calls,
                "average_coupling": avg_coupling
            },
            "language_distribution": language_distribution,
            "dependency_matrix": dependency_matrix,
            "graph_statistics": self.get_graph_statistics()
        }
    
    def find_high_coupling_modules(self, threshold: float = 0.5) -> List[Dict]:
        """
        查找高耦合模块
        
        Args:
            threshold: 耦合度阈值
            
        Returns:
            高耦合模块列表
        """
        high_coupling_modules = []
        modules = self.repo_index.graph_builder.get_modules()
        total_modules = len(modules)
        
        for module_path in modules.keys():
            impact = self.static_analyzer.analyze_import_impact(module_path)
            coupling_score = impact["statistics"]["coupling_score"]
            
            if coupling_score > threshold:
                high_coupling_modules.append({
                    "module_path": module_path,
                    "coupling_score": coupling_score,
                    "import_count": impact["statistics"]["import_count"],
                    "dependent_count": impact["statistics"]["dependent_count"],
                    "cross_call_count": impact["statistics"]["cross_call_count"]
                })
        
        # 按耦合度排序
        high_coupling_modules.sort(key=lambda x: x["coupling_score"], reverse=True)
        return high_coupling_modules
    
    def find_isolated_modules(self) -> List[str]:
        """
        查找孤立模块（没有导入也没有被依赖）
        
        Returns:
            孤立模块路径列表
        """
        isolated_modules = []
        modules = self.repo_index.graph_builder.get_modules()
        
        for module_path in modules.keys():
            deps = self.static_analyzer.get_module_dependencies(module_path)
            if len(deps["imports"]) == 0 and len(deps["dependents"]) == 0:
                isolated_modules.append(module_path)
        
        return isolated_modules 