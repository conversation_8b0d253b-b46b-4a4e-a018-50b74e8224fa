"""
Repository Index

增强的仓库索引，集成两阶段构建策略
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import networkx as nx
from core.ast_parser import ASTParserFactory
from core.code_graph_builder import CodeGraphBuilder
from core.git_adapter import DefaultGitAdapter, GitServiceAdapter
from models.call_relation import FunctionCall
from models.function import Function
from models.macro import Macro
from models.module import CodeModule
from models.repository import Repository
from networkx import DiGraph
from pydantic import BaseModel, ConfigDict, Field
from tqdm import tqdm
from utils.file_utils import (collect_source_files,
                              determine_language_by_filename)
from utils.path_utils import (get_node_id, normalize_path, to_absolute_path,
                              to_relative_path)

logger = logging.getLogger(__name__)


class RepositoryIndex(BaseModel):
    """增强的仓库索引管理器"""
    
    repo_dir: str
    branch: str = "main"
    commit_sha: Optional[str] = None
    exclude_patterns: List[str] = Field(default_factory=list)
    git_adapter: Optional[GitServiceAdapter] = None
    
    # 核心组件
    graph_builder: CodeGraphBuilder = Field(default_factory=CodeGraphBuilder)
    repo: Optional[Repository] = None
    
    # 兼容性属性（保持与原有 RepositoryIndex 的兼容性）
    function_definitions: Dict[str, List[Function]] = Field(default_factory=dict)
    function_calls: List[FunctionCall] = Field(default_factory=list)
    call_graph: DiGraph = Field(default_factory=nx.DiGraph)
    macro_definitions: List[Macro] = Field(default_factory=list)
    
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.git_adapter is None:
            self.git_adapter = DefaultGitAdapter()
    
    def build(self):
        """构建仓库索引"""
        # 如果有 Git 适配器，先切换到指定分支
        if self.git_adapter and hasattr(self.git_adapter, 'checkout_to_ref'):
            with self.git_adapter.checkout_to_ref(self.repo_dir, self.branch):
                self.__build_index()
        else:
            self.__build_index()
    
    def index_repo(self):
        """兼容性方法：构建仓库索引"""
        return self.build()
    
    def __build_index(self):
        """构建索引"""
        # 收集所有源文件 - 只收集支持的文件类型
        supported_extensions = ['.c', '.cpp', '.h', '.py', '.asm']
        file_paths = collect_source_files(self.repo_dir, extensions=supported_extensions)
        
        # 解析所有文件
        parser_results = []
        logger.info(f"开始解析 {len(file_paths)} 个文件")
        
        for rel_path in tqdm(file_paths):
            abs_path = to_absolute_path(rel_path, self.repo_dir)
            lang = determine_language_by_filename(abs_path)
            if not lang or lang == 'unknown':
                logger.debug(f"跳过文件 {abs_path}: 不支持的语言 {lang}")
                continue
            
            try:
                logger.debug(f"解析文件 {abs_path} (语言: {lang})")
                parser = ASTParserFactory.create(lang)
                functions = parser.extract_functions(abs_path)
                calls = parser.extract_calls(abs_path)
                
                # 提取宏定义（仅对C语言）
                macros = []
                if lang == "c":
                    macros = parser.extract_macros(abs_path)
                    self.macro_definitions.extend(macros)
                
                logger.debug(f"文件 {abs_path} 解析完成: {len(functions)} 个函数, {len(calls)} 个调用, {len(macros)} 个宏")
                parser_results.append((rel_path, functions, calls, macros))
            except Exception as e:
                logger.warning(f"解析文件 {abs_path} 失败: {e}")
                continue
        
        logger.info(f"解析完成，共处理 {len(parser_results)} 个文件")
        
        # 使用两阶段构建策略
        self.graph_builder.build_from_parser_results(parser_results)
        
        # 更新兼容性属性
        self.function_definitions = self.graph_builder.get_function_definitions()
        self.function_calls = self.graph_builder.get_function_calls()
        self.call_graph = self.graph_builder.get_call_graph()
        
        # 构建 Repository 对象
        self._build_repository(parser_results)
    
    def _build_repository(self, parser_results: List[Tuple[str, List[Function], List[FunctionCall], List[Macro]]]):
        """构建 Repository 对象"""
        repo = Repository(repo_dir=str(self.repo_dir), modules={}, branch=self.branch)
        
        for rel_path, functions, calls, macros in parser_results:
            module = CodeModule(path=rel_path, functions=functions, calls=calls)
            repo.add_module(module)
        
        self.repo = repo
    
    # 兼容性方法（保持与原有 RepositoryIndex 的兼容性）
    
    def to_rel_path(self, path: str) -> str:
        """将路径转换为相对于仓库根目录的相对路径"""
        return to_relative_path(path, self.repo_dir)
    
    def to_abs_path(self, rel_path: str) -> str:
        """将相对路径转换为绝对路径"""
        return to_absolute_path(rel_path, self.repo_dir)
    
    def _simplify_path(self, path: str) -> str:
        """简化路径，处理 . 和 .. 等特殊字符"""
        if not path:
            return path
        
        # 预处理：统一反斜杠为正斜杠
        normalized_path = path.replace('\\', '/')
        
        # 分割路径
        parts = normalized_path.split('/')
        result = []
        
        for part in parts:
            if part == '.' or part == '':
                continue
            elif part == '..':
                if result:
                    result.pop()
            else:
                result.append(part)
        
        # 如果结果为空，返回当前目录
        if not result:
            return '.'
        
        return '/'.join(result)
    
    def normalize_path(self, path: Optional[str]) -> Optional[str]:
        """
        将路径标准化为相对于仓库根目录的路径，统一使用正斜杠并屏蔽平台差异。

        Args:
            path: 输入路径，支持绝对或相对路径

        Returns:
            标准化后的路径字符串，如果输入为None则返回None
        """
        if path is None:
            return None
        if not path:
            return ""

        try:
            # 使用 pathlib 对象统一处理
            path_obj = Path(path).expanduser()

            # 仓库根路径
            repo_root = Path(self.repo_dir).resolve()

            # 若是绝对路径，尝试转换为相对路径
            if path_obj.is_absolute():
                try:
                    rel_path = path_obj.resolve().relative_to(repo_root)
                    return rel_path.as_posix()
                except ValueError:
                    # 非仓库路径，尝试从字符串上处理
                    repo_str = repo_root.as_posix()
                    path_str = path_obj.as_posix()
                    if repo_str in path_str:
                        return path_str.replace(repo_str, "").lstrip('/')
                    # fallback：取后半段
                    parts = path_str.split('/')
                    if ':' in parts[0] and len(parts) > 2:
                        return '/'.join(parts[2:])
                    # 如果上面的逻辑失败，尝试直接处理原始路径
                    if ':' in path and '\\' in path:
                        # Windows路径格式
                        win_parts = path.split('\\')
                        if len(win_parts) >= 3:
                            return '/'.join(win_parts[2:])
                    # 处理Unix格式的Windows路径
                    elif ':' in path and '/' in path:
                        # Unix格式的Windows路径
                        unix_parts = path.split('/')
                        if len(unix_parts) >= 4:  # C:, tmp, test_repo, src/main.py
                            return '/'.join(unix_parts[3:])  # 跳过 C:, tmp, test_repo
                    return path_str
            else:
                # 相对路径标准化
                # 检查是否是Windows路径格式
                if ':' in path and '\\' in path:
                    # Windows路径格式，检查是否在仓库内
                    win_parts = path.split('\\')
                    if len(win_parts) >= 4:  # C:, tmp, test_repo, src/main.py
                        # 检查是否在仓库内
                        repo_parts = Path(self.repo_dir).parts
                        # Windows路径格式：C:, tmp, test_repo, src/main.py
                        # 检查仓库目录是否匹配
                        if (len(repo_parts) >= 1 and 
                            self.repo_dir.replace('\\', '/') in path.replace('\\', '/')):
                            # 在仓库内，提取相对部分
                            result = '/'.join(win_parts[3:])  # 跳过 C:, tmp, test_repo
                            return self._simplify_path(result)
                        else:
                            # 不在仓库内，返回标准化后的原路径
                            return path.replace('\\', '/')
                # 检查是否是Unix格式的Windows路径
                elif ':' in path and '/' in path:
                    # Unix格式的Windows路径，检查是否在仓库内
                    unix_parts = path.split('/')
                    if len(unix_parts) >= 4:  # C:, tmp, test_repo, src/main.py
                        # 检查是否在仓库内
                        repo_parts = Path(self.repo_dir).parts
                        # Unix格式Windows路径：C:, tmp, test_repo, src/main.py
                        # 检查仓库目录是否匹配
                        if (len(repo_parts) >= 1 and 
                            self.repo_dir.replace('\\', '/') in path):
                            # 在仓库内，提取相对部分
                            result = '/'.join(unix_parts[3:])  # 跳过 C:, tmp, test_repo
                            return self._simplify_path(result)
                        else:
                            # 不在仓库内，返回标准化后的原路径
                            return path.replace('\\', '/')
                result = path_obj.as_posix().replace('\\', '/')
                # 简化路径中的 . 和 ..
                return self._simplify_path(result)
        except Exception:
            # fallback：粗略替换
            return path.replace("\\", "/")
    
    def paths_equal(self, path1: str, path2: str) -> bool:
        """比较两个路径是否相等（使用pathlib屏蔽平台差异）"""
        """比较两个路径是否相等（使用pathlib屏蔽平台差异）
        
        Args:
            path1: 第一个路径
            path2: 第二个路径
            
        Returns:
            两个路径是否相等
        """
        if not path1 or not path2:
            return path1 == path2
        
        try:
            # 使用pathlib进行路径比较，自动处理平台差异
            path1_obj = Path(path1)
            path2_obj = Path(path2)
            
            # 如果两个路径都是绝对路径，尝试转换为相对路径后比较
            if path1_obj.is_absolute() and path2_obj.is_absolute():
                try:
                    repo_path = Path(self.repo_dir)
                    rel_path1 = path1_obj.relative_to(repo_path)
                    rel_path2 = path2_obj.relative_to(repo_path)
                    return rel_path1 == rel_path2
                except ValueError:
                    # 如果无法转换为相对路径，比较标准化后的绝对路径
                    return path1_obj.resolve() == path2_obj.resolve()
            
            # 统一标准化后比较
            norm_path1 = self.normalize_path(path1)
            norm_path2 = self.normalize_path(path2)
            
            return norm_path1 == norm_path2
        except Exception:
            # 如果pathlib处理失败，回退到字符串比较
            norm_path1 = self.normalize_path(path1)
            norm_path2 = self.normalize_path(path2)
            return norm_path1 == norm_path2
    
    def _get_node_id(self, filepath: str, func_name: str) -> str:
        """生成函数节点ID"""
        # 如果已经是相对路径，直接使用；否则转换为相对路径
        if Path(filepath).is_absolute():
            rel_path = self.to_rel_path(filepath)
        else:
            rel_path = filepath
        return get_node_id(rel_path, func_name)
    
    def find_function_definition(self, func_name: str, filepath: Optional[str] = None) -> Optional[Function]:
        """查找函数定义"""
        if func_name in self.function_definitions:
            funcs = self.function_definitions[func_name]
            if filepath:
                for func in funcs:
                    if self.paths_equal(func.filepath, filepath) and not func.is_declaration:
                        return func
                for func in funcs:
                    if self.paths_equal(func.filepath, filepath):
                        return func
            
            for func in funcs:
                if not func.is_declaration:
                    return func
            return funcs[0]
        return None
    
    def get_related_calls(self, func_name: str) -> List[FunctionCall]:
        """获取相关调用（已排重）"""
        # 使用集合来跟踪已见过的调用关系，避免重复
        seen_calls = set()
        related_calls = []
        
        for call in self.function_calls:
            if call.caller == func_name or call.callee == func_name:
                # 创建唯一标识符用于排重
                # 格式: caller->callee@file_path
                call_key = f"{call.caller}->{call.callee}@{call.file_path}"
                
                if call_key not in seen_calls:
                    seen_calls.add(call_key)
                    related_calls.append(call)
        
        return related_calls
    
    def get_call_graph(self) -> DiGraph:
        """获取调用图"""
        return self.call_graph
    
    def get_changed_functions(self, file_content: str, file_path: str, changed_lines: Optional[list[int]] = None) -> List:
        """获取受影响的函数"""
        from gate_keeper.application.dto.result import AffectedFunction

        affected_functions = []



        for func_name, funcs in self.function_definitions.items():
            for func in funcs:
                logger.debug(f"  检查函数: {func_name}, 文件: {func.filepath}")
                if self.paths_equal(func.filepath, file_path):
                                        # 使用头文件分析器判断是否应该跳过
                    if func.filepath.endswith('.h'):
                        # 导入头文件分析器
                        try:
                            from gate_keeper.application.service.analysis.header_file_analyzer import \
                                header_analyzer
                            if header_analyzer.should_skip_function(func):
                                continue
                        except ImportError:
                            # 如果无法导入，使用简单的判断逻辑
                            if getattr(func, 'is_declaration', False):
                                continue
                    
                    if changed_lines:
                        affected_lines = func.range.get_affected_lines(changed_lines)
                        logger.debug(f"    函数 {func_name} 范围: {func.range.start_line}-{func.range.end_line}, 受影响行: {affected_lines}")
                        if not affected_lines:
                            logger.debug(f"    函数 {func_name} 未受影响，跳过")
                            continue
                    
                    affected_func = AffectedFunction(
                        name=func.name,
                        start_line=func.range.start_line,
                        end_line=func.range.end_line,
                        filepath=func.filepath,
                        code=func.code or "",
                        changed_lines=changed_lines or [],
                        call_chains=[],
                        related_definitions=[],
                        related_calls=[]
                    )
                    affected_functions.append(affected_func)
                    logger.info(f"    ✅ 添加受影响函数: {func.name}")
        
        logger.info(f"📊 get_changed_functions结果: 找到 {len(affected_functions)} 个受影响函数")
        return affected_functions
    
    # 新增的增强功能
    
    def get_module_imports(self, module_path: str) -> List[str]:
        """获取模块的导入列表"""
        modules = self.graph_builder.get_modules()
        if module_path in modules:
            return modules[module_path].imports
        return []
    
    def get_module_exports(self, module_path: str) -> List[str]:
        """获取模块的导出列表"""
        modules = self.graph_builder.get_modules()
        if module_path in modules:
            return modules[module_path].exports
        return []
    
    def get_global_function_index(self) -> Dict[str, List[Dict]]:
        """获取全局函数索引"""
        return self.graph_builder.global_function_index
    
    def find_function_in_imports(self, function_name: str, module_path: str) -> Optional[str]:
        """在导入链中查找函数"""
        return self.graph_builder._find_in_imports(function_name, module_path, set())
    
    def get_graph_statistics(self) -> dict:
        """获取图统计信息"""
        return {
            "nodes": self.call_graph.number_of_nodes(),
            "edges": self.call_graph.number_of_edges(),
            "functions": len(self.function_definitions),
            "calls": len(self.function_calls),
            "modules": len(self.graph_builder.get_modules()),
            "files": len(set(func.filepath for funcs in self.function_definitions.values() for func in funcs))
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "RepositoryIndex":
        """兼容性方法：从字典创建 RepositoryIndex 实例"""
        return cls(**data)
    
    def model_dump(self, **kwargs):
        """重写 model_dump 方法，排除不可序列化的字段"""
        # 排除不可序列化的字段
        exclude_fields = {'git_adapter', 'graph_builder', 'call_graph', 'repo'}
        data = super().model_dump(**kwargs)
        
        # 移除不可序列化的字段
        for field in exclude_fields:
            data.pop(field, None)
        
        return data 