"""
Code Graph Builder

代码图构建器，采用两阶段构建策略
"""

from collections import defaultdict
from typing import Dict, List, Optional, Set, Tuple

import networkx as nx

from models.ast_node import ASTNode, CodeModule, GraphCodeNode
from models.call_relation import FunctionCall
from models.function import Function
from models.macro import Macro
from parsers.base import BaseParser


class CodeGraphBuilder:
    """代码图构建器，采用两阶段构建策略"""
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.global_function_index: Dict[str, List[Dict]] = defaultdict(list)  # name -> [{module_path, node_id}]
        self.module_imports: Dict[str, Set[str]] = defaultdict(set)  # module_path -> set of imports
        self.modules: Dict[str, CodeModule] = {}
        self.function_definitions: Dict[str, List[Function]] = defaultdict(list)
        self.function_calls: List[FunctionCall] = []
    
    def build_from_parser_results(self, parser_results: List[Tuple[str, List[Function], List[FunctionCall], List[Macro]]]):
        """
        从解析器结果构建代码图
        
        Args:
            parser_results: [(file_path, functions, calls, macros), ...]
        """
        # 第一阶段：收集所有定义和导入
        for file_path, functions, calls, macros in parser_results:
            module = CodeModule(
                path=file_path,
                language=self._get_language(file_path)
            )
            self.modules[file_path] = module
            
            # 收集函数定义
            self._collect_function_definitions(functions, module)
            
            # 收集宏定义
            self._collect_macro_definitions(macros, module)
            
            # 收集函数调用
            self._collect_function_calls(calls, module)
            
            # 分析模块导入（从函数调用推断）
            self._analyze_module_imports(functions, calls, module)
        
        # 第二阶段：构建调用关系
        for file_path, functions, calls, macros in parser_results:
            module = self.modules[file_path]
            module_entry_id = self._add_module_entry_node(file_path, module)
            self._add_module_to_graph(module, module_entry_id)
            self._build_call_relationships(calls, module, module_entry_id)
    
    def _get_language(self, file_path: str) -> str:
        """根据文件路径确定语言"""
        if file_path.endswith('.c') or file_path.endswith('.h'):
            return 'c'
        elif file_path.endswith('.py'):
            return 'python'
        return 'unknown'
    
    def _generate_node_id(self, name: str, module_path: str, kind: str) -> str:
        """生成节点ID"""
        return f"{module_path}:{kind}:{name}"
    
    def _add_node(self, node: GraphCodeNode):
        """添加节点到图"""
        self.graph.add_node(node.id, data=node)
    
    def _add_edge(self, source: str, target: str, edge_type: str):
        """添加边到图"""
        self.graph.add_edge(source, target, type=edge_type)
    
    def _add_module_entry_node(self, module_path: str, module: CodeModule) -> str:
        """添加模块入口节点"""
        node_id = self._generate_node_id("__module_entry__", module_path, "entry")
        node = GraphCodeNode(
            id=node_id,
            name="__module_entry__",
            type="entry",
            language=module.language,
            kind="module",
            file=module_path,
            module=module_path
        )
        self._add_node(node)
        return node_id
    
    def _collect_function_definitions(self, functions: List[Function], module: CodeModule):
        """收集函数定义"""
        for func in functions:
            # 更新函数文件路径
            func.filepath = module.path
            
            # 添加到全局索引
            node_id = self._generate_node_id(func.name, module.path, "function")
            func_node = GraphCodeNode(
                id=node_id,
                name=func.name,
                type=func.signature.name,
                language=module.language,
                kind="function",
                file=module.path,
                module=module.path,
                definition=func.code,
                location={
                    "start_line": func.range.start_line,
                    "end_line": func.range.end_line
                },
                metadata={"signature": {"name": func.signature.name, "parameters": [{"name": p.name, "type": p.type_hint} for p in func.signature.parameters], "return_type": func.signature.return_type}}
            )
            
            # 添加到模块
            module.node_ids.append(node_id)
            self.function_definitions[func.name].append(func)
            self.global_function_index[func.name].append({
                "module_path": module.path,
                "node_id": node_id
            })
    
    def _collect_macro_definitions(self, macros: List[Macro], module: CodeModule):
        """收集宏定义"""
        for macro in macros:
            # 添加到全局索引
            node_id = self._generate_node_id(macro.name, module.path, "macro")
            macro_node = GraphCodeNode(
                id=node_id,
                name=macro.name,
                type="macro",
                language=module.language,
                kind="macro",
                file=module.path,
                module=module.path,
                definition=macro.code,
                location={
                    "start_line": macro.range.start_line,
                    "end_line": macro.range.end_line
                },
                metadata={
                    "macro_type": macro.type,
                    "parameters": macro.parameters if hasattr(macro, 'parameters') else [],
                    "value": macro.value if hasattr(macro, 'value') else ""
                }
            )
            
            # 添加到模块
            module.node_ids.append(node_id)
            # 将宏也添加到全局函数索引中，这样调用时可以找到
            self.global_function_index[macro.name].append({
                "module_path": module.path,
                "node_id": node_id
            })
            # 添加宏节点到图中
            self._add_node(macro_node)
    
    def _collect_function_calls(self, calls: List[FunctionCall], module: CodeModule):
        """收集函数调用"""
        for call in calls:
            # 更新调用文件路径
            call.file_path = module.path
            self.function_calls.append(call)
    
    def _analyze_module_imports(self, functions: List[Function], calls: List[FunctionCall], module: CodeModule):
        """分析模块导入（从函数调用推断）"""
        # 收集所有被调用的函数名
        called_functions = {call.callee for call in calls}
        
        # 检查哪些被调用的函数不在当前模块中定义
        local_functions = {func.name for func in functions}
        external_calls = called_functions - local_functions
        
        # 从全局索引中找到这些函数所在的模块
        for func_name in external_calls:
            if func_name in self.global_function_index:
                for entry in self.global_function_index[func_name]:
                    if entry["module_path"] != module.path:
                        module.imports.append(entry["module_path"])
                        self.module_imports[module.path].add(entry["module_path"])
    
    def _add_module_to_graph(self, module: CodeModule, entry_id: str):
        """将模块添加到图中"""
        # 添加模块包含的节点
        for node_id in module.node_ids:
            self._add_edge(entry_id, node_id, "contains")
        
        # 添加模块导入关系
        for imported_path in module.imports:
            if imported_path in self.modules:
                imported_entry_id = self._generate_node_id("__module_entry__", imported_path, "entry")
                self._add_edge(entry_id, imported_entry_id, "imports")
    
    def _build_call_relationships(self, calls: List[FunctionCall], module: CodeModule, module_entry_id: str):
        """构建调用关系"""
        for call in calls:
            caller_id = self._find_function_node(call.caller, module.path)
            callee_id = self._find_function_node(call.callee, module.path)
            
            if caller_id and callee_id:
                self._add_edge(caller_id, callee_id, "calls")
            elif callee_id:
                # 如果调用者不在当前模块，使用模块入口节点
                self._add_edge(module_entry_id, callee_id, "calls")
    
    def _find_function_node(self, function_name: str, current_module_path: str) -> Optional[str]:
        """查找函数节点"""
        entries = self.global_function_index.get(function_name)
        if not entries:
            return None
        
        # 优先查找当前模块中的函数
        for entry in entries:
            if entry["module_path"] == current_module_path:
                return entry["node_id"]
        
        # 查找导入模块中的函数
        visited = set()
        return self._find_in_imports(function_name, current_module_path, visited) or entries[0]["node_id"]
    
    def _find_in_imports(self, function_name: str, module_path: str, visited: Set[str]) -> Optional[str]:
        """在导入链中查找函数"""
        if module_path in visited:
            return None
        visited.add(module_path)
        
        entries = self.global_function_index.get(function_name, [])
        for imported in self.module_imports.get(module_path, []):
            for entry in entries:
                if entry["module_path"] == imported:
                    return entry["node_id"]
            result = self._find_in_imports(function_name, imported, visited)
            if result:
                return result
        return None
    
    def get_call_graph(self) -> nx.DiGraph:
        """获取调用图"""
        return self.graph
    
    def get_function_definitions(self) -> Dict[str, List[Function]]:
        """获取函数定义"""
        return self.function_definitions
    
    def get_function_calls(self) -> List[FunctionCall]:
        """获取函数调用"""
        return self.function_calls
    
    def get_modules(self) -> Dict[str, CodeModule]:
        """获取模块信息"""
        return self.modules 