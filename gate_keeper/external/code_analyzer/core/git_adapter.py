"""
Git Adapter Core

Git 服务适配器，用于适配不同项目的 Git 服务接口
"""

from abc import ABC, abstractmethod
from typing import Optional


class GitServiceAdapter(ABC):
    """Git 服务适配器抽象基类"""
    
    @abstractmethod
    def get_branch_commit_sha(self, repo_dir: str, branch: str) -> str:
        """获取分支的提交 SHA"""
        pass
    
    @abstractmethod
    def checkout_to_ref(self, repo_dir: str, ref: str):
        """切换到指定的引用"""
        pass


class DefaultGitAdapter(GitServiceAdapter):
    """默认 Git 适配器，不依赖外部 Git 服务"""
    
    def get_branch_commit_sha(self, repo_dir: str, branch: str) -> str:
        """获取分支的提交 SHA（默认实现返回分支名）"""
        return branch
    
    def checkout_to_ref(self, repo_dir: str, ref: str):
        """切换到指定的引用（默认实现为空）"""
        # 默认实现返回一个空的上下文管理器
        class EmptyContextManager:
            def __enter__(self):
                return None
            def __exit__(self, exc_type, exc_val, exc_tb):
                pass
        return EmptyContextManager()


class GitKeeperAdapter(GitServiceAdapter):
    """Git Keeper 项目的 Git 服务适配器"""
    
    def __init__(self, git_service):
        self.git_service = git_service
    
    def get_branch_commit_sha(self, repo_dir: str, branch: str) -> str:
        """获取分支的提交 SHA"""
        return self.git_service.get_branch_commit_sha(repo_dir, branch)
    
    def checkout_to_ref(self, repo_dir: str, ref: str):
        """切换到指定的引用"""
        return self.git_service.checkout_to_ref(repo_dir, ref) 