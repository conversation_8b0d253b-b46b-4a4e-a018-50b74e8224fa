"""
Static Analyzer

增强的静态代码分析器，集成新的图分析功能
"""

import os
from typing import Dict, List, Optional, Set

import networkx as nx

from models.function import Function

from .repository_index import RepositoryIndex


class StaticAnalyzer:
    """增强的静态代码分析器"""
    
    def __init__(self, repo_index: RepositoryIndex):
        self.repo_index = repo_index
        self.graph = repo_index.get_call_graph()
        self.graph_builder = repo_index.graph_builder
    
    def get_upstream_call_chains(self, func_name: str, file_path: str, max_depth: int = 5) -> List[List[str]]:
        """
        获取从指定函数（file_path:func_name）向上的调用链（谁调用了它）
        
        Args:
            func_name: 目标函数名
            file_path: 目标函数所在文件路径
            max_depth: 最大调用链深度
            
        Returns:
            向上调用链列表，每个链从调用者到目标函数
        """
        # 使用CodeGraphBuilder的节点ID格式
        target_id = f"{file_path}:function:{func_name}"
        if target_id not in self.graph:
            return []

        return self._find_upstream_layers(target_id, max_depth)
    
    def get_downstream_call_chains(self, func_name: str, file_path: str, max_depth: int = 5) -> List[List[str]]:
        """
        获取从指定函数（file_path:func_name）向下的调用链（它调用了谁）
        
        Args:
            func_name: 目标函数名
            file_path: 目标函数所在文件路径
            max_depth: 最大调用链深度
            
        Returns:
            向下调用链列表，每个链从目标函数到被调用函数
        """
        # 使用CodeGraphBuilder的节点ID格式
        start_id = f"{file_path}:function:{func_name}"
        if start_id not in self.graph:
            return []

        return self._find_downstream_layers(start_id, max_depth)
    
    def get_bidirectional_call_chains(self, func_name: str, file_path: str, max_depth: int = 5) -> List[List[str]]:
        """
        获取以指定函数为中心的上下游调用链（谁调用了它 -> 它 -> 它调用了谁）
        
        Args:
            func_name: 中心函数名
            file_path: 中心函数所在文件路径
            max_depth: 最大调用链深度
            
        Returns:
            双向调用链列表，每个链包含上游和下游部分
        """
        # 使用CodeGraphBuilder的节点ID格式
        center_id = f"{file_path}:function:{func_name}"
        if center_id not in self.graph:
            return []

        # 获取上游和下游路径
        upstream_paths = self.get_upstream_call_chains(func_name, file_path, max_depth)
        downstream_paths = self.get_downstream_call_chains(func_name, file_path, max_depth)

        # 如果没有上游或下游，默认只包含自身
        if not upstream_paths:
            upstream_paths = [[center_id]]
        if not downstream_paths:
            downstream_paths = [[center_id]]

        # 组合上下游路径
        combined_chains = []
        for up in upstream_paths:
            for down in downstream_paths:
                # 合并时去除重复的center_id
                if up[-1] == center_id and down[0] == center_id:
                    combined_chain = up[:-1] + down
                else:
                    combined_chain = up + down
                combined_chains.append(combined_chain)

        return combined_chains
    
    def _find_upstream_layers(self, target_id: str, max_depth: int) -> List[List[str]]:
        """逐层反向查找上游调用链"""
        all_paths = []

        def dfs(node: str, path: List[str], depth: int):
            if depth > max_depth:
                return
            # 如果没有前驱节点，添加当前路径（包含自身）
            if not list(self.graph.predecessors(node)):
                all_paths.append(path)
                return
            for pred in self.graph.predecessors(node):
                if pred in path:
                    continue  # 避免环
                new_path = [pred] + path
                all_paths.append(new_path)
                dfs(pred, new_path, depth + 1)
        
        dfs(target_id, [target_id], 1)
        return all_paths
    
    def _find_downstream_layers(self, start_id: str, max_depth: int) -> List[List[str]]:
        """逐层查找下游调用链"""
        all_paths = []

        def dfs(node: str, path: List[str], depth: int):
            if depth > max_depth:
                return
            # 如果没有后继节点，添加当前路径（包含自身）
            if not list(self.graph.successors(node)):
                all_paths.append(path)
                return
            for succ in self.graph.successors(node):
                if succ in path:
                    continue  # 避免环
                new_path = path + [succ]
                all_paths.append(new_path)
                dfs(succ, new_path, depth + 1)
        
        dfs(start_id, [start_id], 1)
        return all_paths
    
    def get_related_functions(self, func_name: str, file_path: str, radius: int = 3) -> List[Function]:
        """
        获取相关函数
        
        Args:
            func_name: 函数名
            file_path: 文件路径
            radius: 搜索半径
            
        Returns:
            相关函数列表
        """
        # 获取双向调用链
        chains = self.get_bidirectional_call_chains(func_name, file_path, max_depth=radius)
        
        # 提取所有相关函数
        related_funcs = set()
        for chain in chains:
            for node_id in chain:
                # 解析节点ID获取函数名和文件路径
                if ":function:" in node_id:
                    file_path_part, func_name_part = node_id.split(":function:", 1)
                    related_funcs.add((func_name_part, file_path_part))
        
        # 查找函数定义
        result = []
        for func_name_part, file_path_part in related_funcs:
            func_def = self.repo_index.find_function_definition(func_name_part, file_path_part)
            if func_def and not func_def.is_declaration:
                result.append(func_def)
        
        return result
    
    def get_related_elements(self, element_name: str, file_path: str, radius: int = 3) -> Dict[str, List]:
        """
        获取相关元素（函数和宏）
        
        Args:
            element_name: 元素名（函数或宏）
            file_path: 文件路径
            radius: 搜索半径
            
        Returns:
            相关元素字典 {"functions": [...], "macros": [...]}
        """
        # 首先尝试作为函数处理
        function_chains = self.get_bidirectional_call_chains(element_name, file_path, max_depth=radius)
        
        # 然后尝试作为宏处理
        macro_id = f"{file_path}:macro:{element_name}"
        macro_chains = []
        if macro_id in self.graph:
            # 获取宏的上游和下游路径
            upstream_paths = self._find_upstream_layers(macro_id, radius)
            downstream_paths = self._find_downstream_layers(macro_id, radius)
            
            # 组合上下游路径
            if not upstream_paths:
                upstream_paths = [[macro_id]]
            if not downstream_paths:
                downstream_paths = [[macro_id]]
            
            for up in upstream_paths:
                for down in downstream_paths:
                    if up[-1] == macro_id and down[0] == macro_id:
                        combined_chain = up[:-1] + down
                    else:
                        combined_chain = up + down
                    macro_chains.append(combined_chain)
        
        # 合并所有调用链
        all_chains = function_chains + macro_chains
        
        # 提取所有相关元素
        related_funcs = set()
        related_macros = set()
        
        for chain in all_chains:
            for node_id in chain:
                if ":function:" in node_id:
                    file_path_part, func_name_part = node_id.split(":function:", 1)
                    related_funcs.add((func_name_part, file_path_part))
                elif ":macro:" in node_id:
                    file_path_part, macro_name_part = node_id.split(":macro:", 1)
                    related_macros.add((macro_name_part, file_path_part))
        
        # 查找函数定义
        functions = []
        for func_name_part, file_path_part in related_funcs:
            func_def = self.repo_index.find_function_definition(func_name_part, file_path_part)
            if func_def and not func_def.is_declaration:
                functions.append(func_def)
        
        # 查找宏定义
        macros = []
        for macro_name_part, file_path_part in related_macros:
            for macro in self.repo_index.macro_definitions:
                # 使用文件名比较而不是完整路径
                if (macro.name == macro_name_part and 
                    os.path.basename(macro.filepath) == os.path.basename(file_path_part)):
                    macros.append(macro)
                    break
        
        return {
            "functions": functions,
            "macros": macros
        }
    
    # 新增的增强功能
    
    def get_module_dependencies(self, module_path: str) -> Dict[str, List[str]]:
        """
        获取模块依赖关系
        
        Args:
            module_path: 模块路径
            
        Returns:
            依赖关系字典 {"imports": [...], "exports": [...], "dependents": [...]}
        """
        modules = self.graph_builder.get_modules()
        if module_path not in modules:
            return {"imports": [], "exports": [], "dependents": []}
        
        module = modules[module_path]
        imports = module.imports
        exports = module.exports
        
        # 查找依赖此模块的其他模块
        dependents = []
        for other_path, other_module in modules.items():
            if other_path != module_path and module_path in other_module.imports:
                dependents.append(other_path)
        
        return {
            "imports": imports,
            "exports": exports,
            "dependents": dependents
        }
    
    def get_cross_module_calls(self, module_path: str) -> List[Dict]:
        """
        获取跨模块调用
        
        Args:
            module_path: 模块路径
            
        Returns:
            跨模块调用列表
        """
        cross_module_calls = []
        module_imports = self.repo_index.get_module_imports(module_path)
        
        for call in self.repo_index.function_calls:
            if call.file_path == module_path:
                # 检查被调用函数是否在导入的模块中
                callee_module = self._find_function_module(call.callee)
                if callee_module and callee_module in module_imports:
                    cross_module_calls.append({
                        "caller": call.caller,
                        "callee": call.callee,
                        "caller_module": module_path,
                        "callee_module": callee_module,
                        "line": call.line,
                        "code": call.code
                    })
        
        return cross_module_calls
    
    def _find_function_module(self, func_name: str) -> Optional[str]:
        """查找函数所在的模块"""
        global_index = self.repo_index.get_global_function_index()
        if func_name in global_index:
            entries = global_index[func_name]
            if entries:
                return entries[0]["module_path"]
        return None
    
    def get_function_call_graph(self, func_name: str, file_path: str, depth: int = 2) -> Dict:
        """
        获取函数的调用图
        
        Args:
            func_name: 函数名
            file_path: 文件路径
            depth: 搜索深度
            
        Returns:
            调用图字典
        """
        center_id = self.repo_index._get_node_id(file_path, func_name)
        if center_id not in self.graph:
            return {"nodes": [], "edges": []}
        
        # 收集相关节点
        nodes = set([center_id])
        edges = set()
        
        def collect_graph(node: str, current_depth: int):
            if current_depth >= depth:
                return
            
            # 收集前驱节点（调用者）
            for pred in self.graph.predecessors(node):
                nodes.add(pred)
                edges.add((pred, node))
                collect_graph(pred, current_depth + 1)
            
            # 收集后继节点（被调用者）
            for succ in self.graph.successors(node):
                nodes.add(succ)
                edges.add((node, succ))
                collect_graph(succ, current_depth + 1)
        
        collect_graph(center_id, 0)
        
        return {
            "nodes": list(nodes),
            "edges": list(edges),
            "center": center_id
        }
    
    def analyze_import_impact(self, module_path: str) -> Dict:
        """
        分析模块导入影响
        
        Args:
            module_path: 模块路径
            
        Returns:
            导入影响分析结果
        """
        deps = self.get_module_dependencies(module_path)
        cross_calls = self.get_cross_module_calls(module_path)
        
        # 统计信息
        total_imports = len(deps["imports"])
        total_exports = len(deps["exports"])
        total_dependents = len(deps["dependents"])
        total_cross_calls = len(cross_calls)
        
        # 计算耦合度
        coupling_score = (total_imports + total_dependents) / max(len(self.graph_builder.get_modules()), 1)
        
        return {
            "module_path": module_path,
            "imports": deps["imports"],
            "exports": deps["exports"],
            "dependents": deps["dependents"],
            "cross_module_calls": cross_calls,
            "statistics": {
                "import_count": total_imports,
                "export_count": total_exports,
                "dependent_count": total_dependents,
                "cross_call_count": total_cross_calls,
                "coupling_score": coupling_score
            }
        }
    
    # 兼容性方法（保持与原有 StaticAnalyzer 的兼容性）
    
    def get_called_functions(self, func_name: str, depth: int = 1) -> Set[str]:
        """获取下游函数（用于扁平列出）"""
        result = set()
        queue = [(func_name, 0)]
        while queue:
            current, level = queue.pop(0)
            if level >= depth:
                continue
            for callee in self.graph.successors(current):
                if callee not in result:
                    result.add(callee)
                    queue.append((callee, level + 1))
        return result
    
    def get_call_chains_for_function(self, func_name: str, file_path: str, max_depth: int = 5) -> List[List[str]]:
        """获取调用指定函数的调用链（向后兼容）"""
        return self.get_upstream_call_chains(func_name, file_path, max_depth)
    
    def get_call_chains_from_function(self, func_name: str, file_path: str, max_depth: int = 5) -> List[List[str]]:
        """获取从指定函数开始的调用链（向后兼容）"""
        return self.get_downstream_call_chains(func_name, file_path, max_depth) 