"""
Code Analyzer Package

代码分析器包，提供静态代码分析和代码图构建功能
"""

import os
import sys
from pathlib import Path

# 确保当前目录在 Python 路径中
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from core.ast_parser import ASTParserFactory
from core.cache_manager import RepositoryIndexCacheManager as CacheManager
from core.code_graph import CodeGraph
from core.repository_index import RepositoryIndex
from core.repository_index_factory import RepositoryIndexFactory
from core.static_analyzer import StaticAnalyzer
from models.call_relation import FunctionCall
from models.code_range import CodeRange
from models.element import CodeElement
from models.function import Function, FunctionSignature
from models.function import Parameter as FunctionParameter
from models.macro import Macro
from models.module import CodeModule
from models.repository import Repository
from models.struct import Struct, StructField, StructRelation, StructUsage
from parsers.base import BaseParser
from parsers.c_parser import CParser
from parsers.np_parser import NPParser
from parsers.python_parser import PythonParser
from utils.file_utils import (collect_source_files,
                              determine_language_by_filename)
from utils.path_utils import (get_node_id, normalize_path, to_absolute_path,
                              to_relative_path)

__version__ = "1.0.0"

__all__ = [
    # Core components
    "ASTParserFactory",
    "CacheManager",
    "CodeGraph",
    "RepositoryIndex",
    "RepositoryIndexFactory",
    "StaticAnalyzer",
    
    # Models
    "CodeElement",
    "CodeModule",
    "CodeRange",
    "Function",
    "FunctionCall",
    "FunctionParameter",
    "FunctionSignature",
    "Macro",
    "Repository",
    "Struct",
    "StructField",
    "StructRelation",
    "StructUsage",
    
    # Parsers
    "BaseParser",
    "CParser",
    "PythonParser",
"NPParser",
    
    # Utils
    "collect_source_files",
    "determine_language_by_filename",
    "get_node_id",
    "normalize_path",
    "to_absolute_path",
    "to_relative_path",
] 