[{"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{", "after": "move rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}", "expected": "if (cc0.z) {", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": false, "target_token_count": 3, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": true, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_2867825", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326512"}}, {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {", "after": "} else {\nmove rsInfo.bQos = qos;\n}", "expected": "move rsInfo.bQos = BE;", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": true, "target_token_count": 4, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": false, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_1419610", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326627"}}, {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;", "after": "move rsInfo.bQos = qos;\n}", "expected": "} else {", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": false, "target_token_count": 3, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": true, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_5614226", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326735"}}, {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {", "after": "}", "expected": "move rsInfo.bQos = qos;", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": true, "target_token_count": 4, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": false, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_5108603", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326844"}}, {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;", "after": "", "expected": "}", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": false, "target_token_count": 1, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": false, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_4744854", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326953"}}]