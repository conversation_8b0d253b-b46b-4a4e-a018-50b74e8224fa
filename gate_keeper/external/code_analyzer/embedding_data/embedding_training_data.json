{"pairs": [{"query": {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{", "after": "move rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}", "expected": "if (cc0.z) {", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": false, "target_token_count": 3, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": true, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_2867825", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326512"}}, "positive": {"content": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 53, "entity_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "code_range": {"start_line": 53, "end_line": 61}, "metadata": {"return_type": null, "parameters": []}}, "negative": {"content": "uint8   NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 212, "entity_name": "NextHeader", "code_range": {"start_line": 212, "end_line": 212}, "metadata": {"variable_type": "declaration"}}, "similarity_score": 0.7999999999999999}, {"query": {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {", "after": "} else {\nmove rsInfo.bQos = qos;\n}", "expected": "move rsInfo.bQos = BE;", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": true, "target_token_count": 4, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": false, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_1419610", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326627"}}, "positive": {"content": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 53, "entity_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "code_range": {"start_line": 53, "end_line": 61}, "metadata": {"return_type": null, "parameters": []}}, "negative": {"content": "#define IPV6_EXT_NO_NEXT_HEADER 0x3B ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 32, "entity_name": "IPV6_EXT_NO_NEXT_HEADER", "code_range": {"start_line": 32, "end_line": 33}, "metadata": {"definition": "0x3B ", "parameters": []}}, "similarity_score": 0.7999999999999999}, {"query": {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;", "after": "move rsInfo.bQos = qos;\n}", "expected": "} else {", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": false, "target_token_count": 3, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": true, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_5614226", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326735"}}, "positive": {"content": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 53, "entity_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "code_range": {"start_line": 53, "end_line": 61}, "metadata": {"return_type": null, "parameters": []}}, "negative": {"content": "uint8  rbHdrVersion;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 914, "entity_name": "rbHdrVersion", "code_range": {"start_line": 914, "end_line": 914}, "metadata": {"variable_type": "declaration"}}, "similarity_score": 0.7999999999999999}, {"query": {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {", "after": "}", "expected": "move rsInfo.bQos = qos;", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": true, "target_token_count": 4, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": false, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_5108603", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326844"}}, "positive": {"content": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 53, "entity_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "code_range": {"start_line": 53, "end_line": 61}, "metadata": {"return_type": null, "parameters": []}}, "negative": {"content": "struct IPSEC_FLEX_LOCALIP_KEY_S {\n    uint4 SubTid;\n    uint4 Rsvd0;\n    uint24 Rsvd1;\n    uint16 Rsvd2;\n    uint16 VrfId;\n    uint32 SrcIp;\n    uint32 DstIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 202, "entity_name": "IPSEC_FLEX_LOCALIP_KEY_S", "code_range": {"start_line": 202, "end_line": 211}, "metadata": {"field_count": 7, "fields": ["SubTid", "Rsvd0", "Rsvd1", "Rsvd2", "VrfId", "SrcIp", "DstIP"]}}, "similarity_score": 0.7999999999999999}, {"query": {"before": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;", "after": "", "expected": "}", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "line_number": 0, "instruction": null, "env_info": null, "metadata": {"strategy": "waterfall_sequential", "comment_hint": false, "masking_type": "sequential", "line_number": 0, "ast_parent_type": "statement", "target_is_expression": false, "target_token_count": 1, "refers_to_function": [], "refers_to_variable": [], "func_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "extraction_type": "enhanced", "task_type": "code_completion", "mask_level": "enhanced", "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "filepath": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "ast_node_type": "statement", "is_control_flow": false, "complexity_score": 0.16, "call_chain": ["PAE_INFO_QOS_CONFIG_DEFAULT_BE"], "variable_references": [], "related_structs": ["<inferred>", "<inferred>", "<inferred>", "<inferred>", "<inferred>"], "data_id": "np_4744854", "source_type": "inhouse_trace_module", "author": "auto_extract", "version": "v2.3", "seed": 42, "masking_distribution": "uniform", "generation_time": "2025-07-28T16:32:03.326953"}}, "positive": {"content": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 53, "entity_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "code_range": {"start_line": 53, "end_line": 61}, "metadata": {"return_type": null, "parameters": []}}, "negative": {"content": "uint8   Type;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 389, "entity_name": "Type", "code_range": {"start_line": 389, "end_line": 389}, "metadata": {"variable_type": "declaration"}}, "similarity_score": 0.7999999999999999}], "metadata": {}}