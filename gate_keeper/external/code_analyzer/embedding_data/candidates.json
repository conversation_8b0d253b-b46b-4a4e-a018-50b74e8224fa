[{"content": "struct IPADDR_V6_S {\n    WORD_TO_HWORD_S Part0;\n    WORD_TO_HWORD_S Part1;\n    WORD_TO_HWORD_S Part2;\n    WORD_TO_HWORD_S Part3;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 118, "entity_name": "IPADDR_V6_S", "code_range": {"start_line": 118, "end_line": 124}, "metadata": {"field_count": 4, "fields": ["Part0", "Part1", "Part2", "Part3"]}}, {"content": "struct IPV6_HEADER_S {\n    uint4 Version;\n    uint20 FlowLabel;\n    uint16 PayloadLen;\n    uint8 NextHeader;\n    uint8 HopLimit;\n    IPADDR_V6_S SIP;\n    IPADDR_V6_S DIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 126, "entity_name": "IPV6_HEADER_S", "code_range": {"start_line": 126, "end_line": 147}, "metadata": {"field_count": 7, "fields": ["Version", "FlowLabel", "PayloadLen", "NextHeader", "HopLimit", "SIP", "DIP"]}}, {"content": "struct IPV6_DSTOPTS_S {\n    uint8 NextHeader;\n    uint8 HdrExtLen;\n    uint8 OptType;\n    uint8 OptDataLen;\n    uint8 TunEncapLim;\n    uint8 PadNOptType;\n    uint8 PadOptLen;\n    uint8 Rsvd;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 210, "entity_name": "IPV6_DSTOPTS_S", "code_range": {"start_line": 210, "end_line": 220}, "metadata": {"field_count": 8, "fields": ["NextHeader", "HdrExtLen", "OptType", "OptDataLen", "TunEncapLim", "PadNOptType", "PadOptLen", "Rsvd"]}}, {"content": "struct IPV6_WITHOPTS_S {\n    IPV6_HEADER_S IPv6Hdr;\n    IPV6_DSTOPTS_S DstOpts;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 222, "entity_name": "IPV6_WITHOPTS_S", "code_range": {"start_line": 222, "end_line": 226}, "metadata": {"field_count": 2, "fields": ["IPv6Hdr", "DstOpts"]}}, {"content": "struct SMA_OPTS_HDR_S {\n    uint8 OptionType;\n    uint8 OptDataLen;\n    uint4 TagLen;\n    uint4 AiType;\n    uint8 Rsvd0;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 228, "entity_name": "SMA_OPTS_HDR_S", "code_range": {"start_line": 228, "end_line": 235}, "metadata": {"field_count": 5, "fields": ["OptionType", "OptDataLen", "TagLen", "AiType", "Rsvd0"]}}, {"content": "struct DST_SMA_OPTS_HDR_S {\n    uint8 NextHeader;\n    uint8 HdrExtLen;\n    SMA_OPTS_HDR_S SmaOpts;\n    uint32 LW0;\n    uint32 LW1;\n    uint16 LW2;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 238, "entity_name": "DST_SMA_OPTS_HDR_S", "code_range": {"start_line": 238, "end_line": 247}, "metadata": {"field_count": 6, "fields": ["NextHeader", "HdrExtLen", "SmaOpts", "LW0", "LW1", "LW2"]}}, {"content": "struct IPV6_EXT_COMMON_S {\n    uint8 NextHeader;\n    uint8 HdrExtLen;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 249, "entity_name": "IPV6_EXT_COMMON_S", "code_range": {"start_line": 249, "end_line": 253}, "metadata": {"field_count": 2, "fields": ["NextHeader", "HdrExtLen"]}}, {"content": "struct IPV6_HEADER_WITH_EXT_S {\n    IPV6_HEADER_S Ipv6Hdr;\n    IPV6_EXT_COMMON_S ExtHdr;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 255, "entity_name": "IPV6_HEADER_WITH_EXT_S", "code_range": {"start_line": 255, "end_line": 259}, "metadata": {"field_count": 2, "fields": ["Ipv6Hdr", "ExtHdr"]}}, {"content": "struct IPV6_HOP_BY_HOP_S {\n    IPV6_EXT_COMMON_S ExtHdr;\n    uint8 OptionType;\n    uint8 OptionDataLen;\n    uint16 Option1;\n    uint16 Option2;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 261, "entity_name": "IPV6_HOP_BY_HOP_S", "code_range": {"start_line": 261, "end_line": 268}, "metadata": {"field_count": 5, "fields": ["ExtHdr", "OptionType", "OptionDataLen", "Option1", "Option2"]}}, {"content": "struct IPV6_MLDSNP_S {\n    uint8 IcmpV6Type;\n    uint8 Code;\n    uint16 CheckSum;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 270, "entity_name": "IPV6_MLDSNP_S", "code_range": {"start_line": 270, "end_line": 275}, "metadata": {"field_count": 3, "fields": ["IcmpV6Type", "Code", "CheckSum"]}}, {"content": "struct IPV6_HEADER_WITH_HBH_MLDSNP_S {\n    IPV6_HEADER_S Ipv6Hdr;\n    IPV6_HOP_BY_HOP_S HopByHopHdr;\n    IPV6_MLDSNP_S MldSnpHdr;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 277, "entity_name": "IPV6_HEADER_WITH_HBH_MLDSNP_S", "code_range": {"start_line": 277, "end_line": 282}, "metadata": {"field_count": 3, "fields": ["Ipv6Hdr", "HopByHopHdr", "MldSnpHdr"]}}, {"content": "struct IPV6_WITH_DST_SMA_OPTS_S {\n    IPV6_HEADER_S IPv6Hdr;\n    DST_SMA_OPTS_HDR_S DstOpts;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 284, "entity_name": "IPV6_WITH_DST_SMA_OPTS_S", "code_range": {"start_line": 284, "end_line": 288}, "metadata": {"field_count": 2, "fields": ["IPv6Hdr", "DstOpts"]}}, {"content": "struct IPV6_FRAGMENT_HEADER_S {\n    uint8 NextHeader;\n    uint8 Reserved;\n    uint32 Identification;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 290, "entity_name": "IPV6_FRAGMENT_HEADER_S", "code_range": {"start_line": 290, "end_line": 303}, "metadata": {"field_count": 3, "fields": ["NextHeader", "Reserved", "Identification"]}}, {"content": "struct IPV6_HEADER_WITH_FRAG_S {\n    IPV6_HEADER_S Ipv6Hdr;\n    IPV6_FRAGMENT_HEADER_S FragHdr;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 305, "entity_name": "IPV6_HEADER_WITH_FRAG_S", "code_range": {"start_line": 305, "end_line": 309}, "metadata": {"field_count": 2, "fields": ["Ipv6Hdr", "FragHdr"]}}, {"content": "struct ICMPV6_NS_S {\n    uint8 Type;\n    uint8 Code;\n    uint16 Checksum;\n    uint32 Rsvd;\n    IPADDR_V6_S TargetIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 311, "entity_name": "ICMPV6_NS_S", "code_range": {"start_line": 311, "end_line": 321}, "metadata": {"field_count": 5, "fields": ["Type", "Code", "Checksum", "Rsvd", "TargetIP"]}}, {"content": "struct ICMPV6_NS_WITH_SLA_S {\n    uint32 Rsvd;\n    IPADDR_V6_S TargetIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 323, "entity_name": "ICMPV6_NS_WITH_SLA_S", "code_range": {"start_line": 323, "end_line": 360}, "metadata": {"field_count": 2, "fields": ["Rsvd", "TargetIP"]}}, {"content": "struct ICMPV6_NS_RSO_S {\n    uint1 R;\n    uint1 S;\n    uint1 O;\n    uint29 Rsvd;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 362, "entity_name": "ICMPV6_NS_RSO_S", "code_range": {"start_line": 362, "end_line": 368}, "metadata": {"field_count": 4, "fields": ["R", "S", "O", "Rsvd"]}}, {"content": "struct ICMPV6_NA_S {\n    uint8 Type;\n    uint8 Code;\n    uint16 Checksum;\n    ICMPV6_NS_RSO_S RSO;\n    IPADDR_V6_S TargetIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 370, "entity_name": "ICMPV6_NA_S", "code_range": {"start_line": 370, "end_line": 380}, "metadata": {"field_count": 5, "fields": ["Type", "Code", "Checksum", "RSO", "TargetIP"]}}, {"content": "struct ICMPV6_NA_WITH_SLA_S {\n    ICMPV6_NS_RSO_S RSO;\n    IPADDR_V6_S TargetIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 382, "entity_name": "ICMPV6_NA_WITH_SLA_S", "code_range": {"start_line": 382, "end_line": 424}, "metadata": {"field_count": 2, "fields": ["RSO", "TargetIP"]}}, {"content": "struct IPV4O6_UDP_S {\n    IPV6_HEADER_S stIPv6Hdr;\n    IPV4_HEADER_S stIPv4Hdr;\n    UDP_S stUdp;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 426, "entity_name": "IPV4O6_UDP_S", "code_range": {"start_line": 426, "end_line": 431}, "metadata": {"field_count": 3, "fields": ["stIPv6Hdr", "stIPv4Hdr", "stUdp"]}}, {"content": "#define IPV6_PROT_IPV4 0x04", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 7, "entity_name": "IPV6_PROT_IPV4", "code_range": {"start_line": 7, "end_line": 8}, "metadata": {"definition": "0x04", "parameters": []}}, {"content": "#define IPV6_PROT_TCP 0x06", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 8, "entity_name": "IPV6_PROT_TCP", "code_range": {"start_line": 8, "end_line": 9}, "metadata": {"definition": "0x06", "parameters": []}}, {"content": "#define IPV6_PROT_UDP 0x11", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 9, "entity_name": "IPV6_PROT_UDP", "code_range": {"start_line": 9, "end_line": 10}, "metadata": {"definition": "0x11", "parameters": []}}, {"content": "#define IPV6_PROT_RSVP 0x2E", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 10, "entity_name": "IPV6_PROT_RSVP", "code_range": {"start_line": 10, "end_line": 11}, "metadata": {"definition": "0x2E", "parameters": []}}, {"content": "#define IPV6_PROT_GRE 0x2F", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 11, "entity_name": "IPV6_PROT_GRE", "code_range": {"start_line": 11, "end_line": 12}, "metadata": {"definition": "0x2F", "parameters": []}}, {"content": "#define IPV6_PROT_ICMP 0x3A", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 12, "entity_name": "IPV6_PROT_ICMP", "code_range": {"start_line": 12, "end_line": 13}, "metadata": {"definition": "0x3A", "parameters": []}}, {"content": "#define IPV6_PROT_OSPF 0x59", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 13, "entity_name": "IPV6_PROT_OSPF", "code_range": {"start_line": 13, "end_line": 14}, "metadata": {"definition": "0x59", "parameters": []}}, {"content": "#define IPV6_PROT_VRRP 0x70", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 14, "entity_name": "IPV6_PROT_VRRP", "code_range": {"start_line": 14, "end_line": 15}, "metadata": {"definition": "0x70", "parameters": []}}, {"content": "#define IPV6_PROT_PIM 0x67", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 15, "entity_name": "IPV6_PROT_PIM", "code_range": {"start_line": 15, "end_line": 16}, "metadata": {"definition": "0x67", "parameters": []}}, {"content": "#define IPV6_PROT_SCTP 0x84", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 16, "entity_name": "IPV6_PROT_SCTP", "code_range": {"start_line": 16, "end_line": 17}, "metadata": {"definition": "0x84", "parameters": []}}, {"content": "#define SUPPORT_MIN_IPV6_MTU 1280", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 17, "entity_name": "SUPPORT_MIN_IPV6_MTU", "code_range": {"start_line": 17, "end_line": 18}, "metadata": {"definition": "1280", "parameters": []}}, {"content": "#define NEXTHDR_OTHER 0", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 18, "entity_name": "NEXTHDR_OTHER", "code_range": {"start_line": 18, "end_line": 19}, "metadata": {"definition": "0", "parameters": []}}, {"content": "#define NEXTHDR_TCP 1", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 19, "entity_name": "NEXTHDR_TCP", "code_range": {"start_line": 19, "end_line": 20}, "metadata": {"definition": "1", "parameters": []}}, {"content": "#define NEXTHDR_UDP 2", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 20, "entity_name": "NEXTHDR_UDP", "code_range": {"start_line": 20, "end_line": 21}, "metadata": {"definition": "2", "parameters": []}}, {"content": "#define NEXTHDR_CPS 2", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 21, "entity_name": "NEXTHDR_CPS", "code_range": {"start_line": 21, "end_line": 22}, "metadata": {"definition": "2", "parameters": []}}, {"content": "#define NEXTHDR_ICMP 3", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 22, "entity_name": "NEXTHDR_ICMP", "code_range": {"start_line": 22, "end_line": 23}, "metadata": {"definition": "3", "parameters": []}}, {"content": "#define IP_PROT_TCP 0x06", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 23, "entity_name": "IP_PROT_TCP", "code_range": {"start_line": 23, "end_line": 24}, "metadata": {"definition": "0x06", "parameters": []}}, {"content": "#define IP_PROT_UDP 0x11", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 24, "entity_name": "IP_PROT_UDP", "code_range": {"start_line": 24, "end_line": 25}, "metadata": {"definition": "0x11", "parameters": []}}, {"content": "#define IPV6_EXT_HOP_BY_HOP 0x00 //zero(0), IPv6 Hop-by-Hop Option, [RFC2460]", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 26, "entity_name": "IPV6_EXT_HOP_BY_HOP", "code_range": {"start_line": 26, "end_line": 27}, "metadata": {"definition": "0x00 //zero(0), IPv6 Hop-by-Hop Option, [RFC2460]", "parameters": []}}, {"content": "#define IPV6_EXT_IPV6_HEADER 0x29 ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 27, "entity_name": "IPV6_EXT_IPV6_HEADER", "code_range": {"start_line": 27, "end_line": 28}, "metadata": {"definition": "0x29 ", "parameters": []}}, {"content": "#define IPV6_EXT_ROUTING_HEADER 0x2B ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 28, "entity_name": "IPV6_EXT_ROUTING_HEADER", "code_range": {"start_line": 28, "end_line": 29}, "metadata": {"definition": "0x2B ", "parameters": []}}, {"content": "#define IPV6_EXT_FRAGMENT_HEADER 0x2C ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 29, "entity_name": "IPV6_EXT_FRAGMENT_HEADER", "code_range": {"start_line": 29, "end_line": 30}, "metadata": {"definition": "0x2C ", "parameters": []}}, {"content": "#define IPV6_EXT_ESP 0x32 ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 30, "entity_name": "IPV6_EXT_ESP", "code_range": {"start_line": 30, "end_line": 31}, "metadata": {"definition": "0x32 ", "parameters": []}}, {"content": "#define IPV6_EXT_AUTH_HEADER 0x33 ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 31, "entity_name": "IPV6_EXT_AUTH_HEADER", "code_range": {"start_line": 31, "end_line": 32}, "metadata": {"definition": "0x33 ", "parameters": []}}, {"content": "#define IPV6_EXT_NO_NEXT_HEADER 0x3B ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 32, "entity_name": "IPV6_EXT_NO_NEXT_HEADER", "code_range": {"start_line": 32, "end_line": 33}, "metadata": {"definition": "0x3B ", "parameters": []}}, {"content": "#define IPV6_EXT_DEST_OPTIONS 0x3C ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 33, "entity_name": "IPV6_EXT_DEST_OPTIONS", "code_range": {"start_line": 33, "end_line": 34}, "metadata": {"definition": "0x3C ", "parameters": []}}, {"content": "#define IPV6_EXT_MOBILITY_HEADER 0x87 ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 34, "entity_name": "IPV6_EXT_MOBILITY_HEADER", "code_range": {"start_line": 34, "end_line": 35}, "metadata": {"definition": "0x87 ", "parameters": []}}, {"content": "#define IPV6_EXT_MPLS_HEADER 0x89 ", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 35, "entity_name": "IPV6_EXT_MPLS_HEADER", "code_range": {"start_line": 35, "end_line": 36}, "metadata": {"definition": "0x89 ", "parameters": []}}, {"content": "uint8   TC;                 /*Traffic Class*/", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 131, "entity_name": "TC", "code_range": {"start_line": 131, "end_line": 131}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  PayloadLen;             /*Length of packet after IPv6 header*/", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 137, "entity_name": "PayloadLen", "code_range": {"start_line": 137, "end_line": 137}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   NextHeader;                /*Type of next header after this one*/", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 138, "entity_name": "NextHeader", "code_range": {"start_line": 138, "end_line": 138}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   HopLimit;                 /*Hop limit*/", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 139, "entity_name": "HopLimit", "code_range": {"start_line": 139, "end_line": 139}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 212, "entity_name": "NextHeader", "code_range": {"start_line": 212, "end_line": 212}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   HdrExtLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 213, "entity_name": "HdrExtLen", "code_range": {"start_line": 213, "end_line": 213}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 214, "entity_name": "OptType", "code_range": {"start_line": 214, "end_line": 214}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 215, "entity_name": "OptDataLen", "code_range": {"start_line": 215, "end_line": 215}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   TunEncapLim;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 216, "entity_name": "TunEncapLim", "code_range": {"start_line": 216, "end_line": 216}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   PadNOptType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 217, "entity_name": "PadNOptType", "code_range": {"start_line": 217, "end_line": 217}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   PadOptLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 218, "entity_name": "PadOptLen", "code_range": {"start_line": 218, "end_line": 218}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Rsvd;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 219, "entity_name": "Rsvd", "code_range": {"start_line": 219, "end_line": 219}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 230, "entity_name": "OptionType", "code_range": {"start_line": 230, "end_line": 230}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 231, "entity_name": "OptDataLen", "code_range": {"start_line": 231, "end_line": 231}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Rsvd0;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 234, "entity_name": "Rsvd0", "code_range": {"start_line": 234, "end_line": 234}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 240, "entity_name": "NextHeader", "code_range": {"start_line": 240, "end_line": 240}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   HdrExtLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 241, "entity_name": "HdrExtLen", "code_range": {"start_line": 241, "end_line": 241}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  LW0;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 243, "entity_name": "LW0", "code_range": {"start_line": 243, "end_line": 243}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  LW1;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 244, "entity_name": "LW1", "code_range": {"start_line": 244, "end_line": 244}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  LW2;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 245, "entity_name": "LW2", "code_range": {"start_line": 245, "end_line": 245}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 251, "entity_name": "NextHeader", "code_range": {"start_line": 251, "end_line": 251}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   HdrExtLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 252, "entity_name": "HdrExtLen", "code_range": {"start_line": 252, "end_line": 252}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 264, "entity_name": "OptionType", "code_range": {"start_line": 264, "end_line": 264}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 265, "entity_name": "OptionDataLen", "code_range": {"start_line": 265, "end_line": 265}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  Option1;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 266, "entity_name": "Option1", "code_range": {"start_line": 266, "end_line": 266}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  Option2;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 267, "entity_name": "Option2", "code_range": {"start_line": 267, "end_line": 267}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   IcmpV6Type;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 272, "entity_name": "IcmpV6Type", "code_range": {"start_line": 272, "end_line": 272}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Code;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 273, "entity_name": "Code", "code_range": {"start_line": 273, "end_line": 273}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  CheckSum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 274, "entity_name": "CheckSum", "code_range": {"start_line": 274, "end_line": 274}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 292, "entity_name": "NextHeader", "code_range": {"start_line": 292, "end_line": 292}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Reserved;           /*reserved field*/", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 293, "entity_name": "Reserved", "code_range": {"start_line": 293, "end_line": 293}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16 FragOffsetInfo;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 300, "entity_name": "FragOffsetInfo", "code_range": {"start_line": 300, "end_line": 300}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  Identification;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 302, "entity_name": "Identification", "code_range": {"start_line": 302, "end_line": 302}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Type;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 314, "entity_name": "Type", "code_range": {"start_line": 314, "end_line": 314}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Code;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 315, "entity_name": "Code", "code_range": {"start_line": 315, "end_line": 315}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  Checksum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 316, "entity_name": "Checksum", "code_range": {"start_line": 316, "end_line": 316}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  Rsvd;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 318, "entity_name": "Rsvd", "code_range": {"start_line": 318, "end_line": 318}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Type;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 329, "entity_name": "Type", "code_range": {"start_line": 329, "end_line": 329}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Code;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 330, "entity_name": "Code", "code_range": {"start_line": 330, "end_line": 330}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  Checksum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 331, "entity_name": "Checksum", "code_range": {"start_line": 331, "end_line": 331}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 TypeCodeChk;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 333, "entity_name": "TypeCodeChk", "code_range": {"start_line": 333, "end_line": 333}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  Rsvd;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 336, "entity_name": "Rsvd", "code_range": {"start_line": 336, "end_line": 336}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLAType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 343, "entity_name": "SLAType", "code_range": {"start_line": 343, "end_line": 343}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLALength;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 344, "entity_name": "SLALength", "code_range": {"start_line": 344, "end_line": 344}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLAAddrH8;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 348, "entity_name": "SLAAddrH8", "code_range": {"start_line": 348, "end_line": 348}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLAAddrM8;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 349, "entity_name": "SLAAddrM8", "code_range": {"start_line": 349, "end_line": 349}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16 SLAAddrH16;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 351, "entity_name": "SLAAddrH16", "code_range": {"start_line": 351, "end_line": 351}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 SLAAddrL32;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 353, "entity_name": "SLAAddrL32", "code_range": {"start_line": 353, "end_line": 353}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 SLALW0;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 356, "entity_name": "SLALW0", "code_range": {"start_line": 356, "end_line": 356}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 SLALW1;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 357, "entity_name": "SLALW1", "code_range": {"start_line": 357, "end_line": 357}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Type;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 373, "entity_name": "Type", "code_range": {"start_line": 373, "end_line": 373}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Code;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 374, "entity_name": "Code", "code_range": {"start_line": 374, "end_line": 374}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  Checksum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 375, "entity_name": "Checksum", "code_range": {"start_line": 375, "end_line": 375}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Type;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 389, "entity_name": "Type", "code_range": {"start_line": 389, "end_line": 389}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Code;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 390, "entity_name": "Code", "code_range": {"start_line": 390, "end_line": 390}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  Checksum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 391, "entity_name": "Checksum", "code_range": {"start_line": 391, "end_line": 391}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 TypeCodeChk;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 393, "entity_name": "TypeCodeChk", "code_range": {"start_line": 393, "end_line": 393}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLAType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 404, "entity_name": "SLAType", "code_range": {"start_line": 404, "end_line": 404}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLALength;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 405, "entity_name": "SLALength", "code_range": {"start_line": 405, "end_line": 405}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLAAddrH8;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 410, "entity_name": "SLAAddrH8", "code_range": {"start_line": 410, "end_line": 410}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  SLAAddrM8;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 411, "entity_name": "SLAAddrM8", "code_range": {"start_line": 411, "end_line": 411}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16 SLAAddrH16;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 413, "entity_name": "SLAAddrH16", "code_range": {"start_line": 413, "end_line": 413}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 SLAAddrL32;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 415, "entity_name": "SLAAddrL32", "code_range": {"start_line": 415, "end_line": 415}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 SLALW0;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 419, "entity_name": "SLALW0", "code_range": {"start_line": 419, "end_line": 419}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 SLALW1;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 420, "entity_name": "SLALW1", "code_range": {"start_line": 420, "end_line": 420}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 NextHdr;     //Identifies the type of header immediately following the Destination Options header.", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 454, "entity_name": "NextHdr", "code_range": {"start_line": 454, "end_line": 454}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 HdrExtLen;   //Length of the Destination Options header in 8-octet units, not including the first 8 octets.", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 455, "entity_name": "HdrExtLen", "code_range": {"start_line": 455, "end_line": 455}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 456, "entity_name": "OptionType", "code_range": {"start_line": 456, "end_line": 456}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 OptionLen;   //Length of the option, in octets, excluding the Option Type and Option Length fields", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 457, "entity_name": "OptionLen", "code_range": {"start_line": 457, "end_line": 457}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 462, "entity_name": "OptionType", "code_range": {"start_line": 462, "end_line": 462}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 OptionLen;   //Length of the option, in octets, excluding the Option Type and Option Length fields", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 463, "entity_name": "OptionLen", "code_range": {"start_line": 463, "end_line": 463}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 519, "entity_name": "NextHeader", "code_range": {"start_line": 519, "end_line": 519}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       HdrExtLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 520, "entity_name": "HdrExtLen", "code_range": {"start_line": 520, "end_line": 520}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       RoutingType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 521, "entity_name": "RoutingType", "code_range": {"start_line": 521, "end_line": 521}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       SegmentsLeft;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 522, "entity_name": "SegmentsLeft", "code_range": {"start_line": 522, "end_line": 522}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       LastEntry;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 524, "entity_name": "LastEntry", "code_range": {"start_line": 524, "end_line": 524}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16      Reserved;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 526, "entity_name": "Reserved", "code_range": {"start_line": 526, "end_line": 526}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 537, "entity_name": "OptionType", "code_range": {"start_line": 537, "end_line": 537}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 538, "entity_name": "OptDataLen", "code_range": {"start_line": 538, "end_line": 538}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  sliceid;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 541, "entity_name": "sliceid", "code_range": {"start_line": 541, "end_line": 541}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  rsvd2;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 542, "entity_name": "rsvd2", "code_range": {"start_line": 542, "end_line": 542}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   NextHeader;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 547, "entity_name": "NextHeader", "code_range": {"start_line": 547, "end_line": 547}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   HdrExtLen;  // ???1", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 548, "entity_name": "HdrExtLen", "code_range": {"start_line": 548, "end_line": 548}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 559, "entity_name": "OptionType", "code_range": {"start_line": 559, "end_line": 559}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 560, "entity_name": "OptDataLen", "code_range": {"start_line": 560, "end_line": 560}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Flags;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 561, "entity_name": "Flags", "code_range": {"start_line": 561, "end_line": 561}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Label;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 562, "entity_name": "Label", "code_range": {"start_line": 562, "end_line": 562}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 567, "entity_name": "OptionType", "code_range": {"start_line": 567, "end_line": 567}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 568, "entity_name": "OptDataLen", "code_range": {"start_line": 568, "end_line": 568}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Flags;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 569, "entity_name": "Flags", "code_range": {"start_line": 569, "end_line": 569}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16        Padding;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 576, "entity_name": "Padding", "code_range": {"start_line": 576, "end_line": 576}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptionType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 580, "entity_name": "OptionType", "code_range": {"start_line": 580, "end_line": 580}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   OptDataLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 581, "entity_name": "OptDataLen", "code_range": {"start_line": 581, "end_line": 581}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  sliceid;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 584, "entity_name": "sliceid", "code_range": {"start_line": 584, "end_line": 584}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       MsgType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 625, "entity_name": "MsgType", "code_range": {"start_line": 625, "end_line": 625}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8       HopCount;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 626, "entity_name": "HopCount", "code_range": {"start_line": 626, "end_line": 626}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8               MsgType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 635, "entity_name": "MsgType", "code_range": {"start_line": 635, "end_line": 635}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   VRID;                   /*virtual Rtr ID */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 643, "entity_name": "VRID", "code_range": {"start_line": 643, "end_line": 643}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   Priority;               /*Priority */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 644, "entity_name": "Priority", "code_range": {"start_line": 644, "end_line": 644}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   CntIpAddrs;             /*Count IPvX Addr */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 645, "entity_name": "CntIpAddrs", "code_range": {"start_line": 645, "end_line": 645}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  CheckSum;               /*CheckSum */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/pkt_ipv6_hdr.h", "line_number": 650, "entity_name": "CheckSum", "code_range": {"start_line": 650, "end_line": 650}, "metadata": {"variable_type": "declaration"}}, {"content": "inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 53, "entity_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE", "code_range": {"start_line": 53, "end_line": 61}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuPaeRxFragPktCheck()\n{\n(S_NLS_APP2MPF_COMM_HDR) q0b qsApp2MpeHdr;\nmove rwFuncRtnData.Data0 = FAILED;\n// 当前支持对PAE分片报文进行处理的报文有重组报文\ncmp cc0 = psPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_LBAPP_EXT;\nif (cc0.eq) {\ncmp cc0 = qsApp2MpeHdr.NextHeadType, APP2MPF_EXTEND_HDR_TYPE_HWA_REASSEM;\nif (cc0.eq) {\nmove rwFuncRtnData.Data1 = CAUSE_UPF_IPU_REASSTOCPU_DROP_RCV_PAE_FRAG_PKT;\nreturn;\n}\n}\nmove rwFuncRtnData.Data1 = CAUSE_UPF_IPU_DROP_UNKNOWPKT_RCV_PAE_FRAG_PKT;\nreturn;\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 112, "entity_name": "IpuPaeRxFragPktCheck", "code_range": {"start_line": 112, "end_line": 129}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline IpuRxBtobClearDesc()\n{\nuint16 rhRealLength;\nuint8  rhPullmoreLength;\nmove rhRealLength = g_cDesc.RealLength;\nmove rhPullmoreLength = g_cDesc.PullmoreLength;\nmove g_cDescLw.LW0 = 0;\nmove g_cDescLw.LW1 = 0;\nmove g_cDescLw.LW2 = 0;\nmove g_cDescLw.LW3 = 0;\nmove g_cDescLw.LW4 = 0;\nmove g_cDescLw.LW5 = 0;\nmove g_cDescLw.LW6 = 0;\nmove g_cDescLw.LW7 = 0;\nmove g_cDescLw.LW8 = 0;\nmove g_cDescLw.LW9 = 0;\nmove g_cDescLw.LW10 = 0;\nmove g_cDescLw.LW11 = 0;\nmove g_cDescLw.LW12 = 0;\nmove g_cDescLw.LW13 =...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 667, "entity_name": "IpuRxBtobClearDesc", "code_range": {"start_line": 667, "end_line": 703}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline IpuRxBtobHdrVersionCheck(uint8 rbHdrVersion)\n{\ncmp cc0 = rbHdrVersion, IPU_BTOB_TOCP_VERSION_BUTT;\nif (cc0.ge) {\nmove rbHdrVersion = IPU_BTOB_TOCP_VERSION_BUTT - 1;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 706, "entity_name": "IpuRxBtobHdrVersionCheck", "code_range": {"start_line": 706, "end_line": 712}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline iFhSoftSwitch()\n{\nuint8          fhLength;\nuint8          fhL3Stake;\n(FH_U)   E_FH_OFFSET_Q   puFh;\n/* 计算帧头长度 */\nmovezr fhLength[3..2] = puFh.Basic.Fhl;\nadd    fhLength = fhLength, FH_FHE_LEN;\nmovezr fhL3Stake = puFh.Basic.L3stake;\nadd    fhLength = fhLength, fhL3Stake;\nmskcmp cc1 = puFh.Basic.IsUc, FH_UC;\nif (cc1.eq) {\nmskcmp cc1 = puFh.Basic.Fhf, FHF_U3;\n}\nif (cc1.eq) {\n// U3帧同时剥掉MPLS标签\nadd fhLength = fhLength, SIZEOF(SHIM_S);\n}\nadd fhLength = fhLength, SIZEOF(ITM_UC_S);\n// P指向fh帧头后面，Q指...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 767, "entity_name": "iFhSoftSwitch", "code_range": {"start_line": 767, "end_line": 791}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline IpuRxBtobToCpHdrParser(uint8 rbBtobToCpType)\n{\nuint8  rbHdrVersion;\nmove rbBtobToCpType = IPU_BTOB_TOCP_TYPE_MAX;\nmovezr rbHdrVersion = qsIpuBtobTocp.Version;\nIpuRxBtobHdrVersionCheck(rbHdrVersion);\ncmp cc0 = rbHdrVersion, IPU_BTOB_TOCP_VERSION_24_0;\nif (cc0.eq) {\nmovezr rbBtobToCpType = qsIpuBtobTocp.BtobToCpType;\nmove g_cDesc.Dsic.Rqcolor.QindexColor.Qindex = qsIpuBtobTocp.Qindex;\nmove g_cDesc.VpnID = qsIpuBtobTocp.VpnId;\nmove g_cDesc.CauseID = qsIpuBtobTocp.CauseId;\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 912, "entity_name": "IpuRxBtobToCpHdrParser", "code_range": {"start_line": 912, "end_line": 926}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuRxBtobDbgStat(uint8 rbPktType)\n{\nbitcmp cc5 = g_ssScratch5.UpfDbgSwitch, ONE_BIT_MASK;\nif (cc5.nz) {\nUpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U1_ARP, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U1_ARP);\nUpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U1_AT, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U1_AT);\nUpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U1_VLANIF, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U1_VLANIF);\nUpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U2_1_IPV4, CAUSE_UPF_IPU_DBGCNT_...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1002, "entity_name": "IpuRxBtobDbgStat", "code_range": {"start_line": 1002, "end_line": 1020}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuCpCarDropAndExceptTocp(uint32 rwCarid, uint1 TotalCarEn)\n{\nBOARD_CONFIG_S  rsBoardConfig;\n// 基于CarId进行Car\ncmp cc0 = rwCarid, 0;\nif (cc0.ne) {\nCar_Util(rwCarid[:20]);\nbitcmp cc0 = g_cDesc.CarDrop, ONE_BIT_MASK;\nif (cc0.nz) {\nmove rwFuncRtnData.Data0 = FAILED;\nreturn;\n}\n}\n// 整机Car\nbitcmp cc0 = TotalCarEn, ONE_BIT_MASK;\nif (cc0.nz) {\nReadBoardCfg(rsBoardConfig);\nbitcmp cc1 = rsBoardConfig.TotalCarEn, ONE_BIT_MASK;\nif (cc1.nz) {\nCar_Util(rsBoardConfig.TotalCarId);\nbitcmp cc2 = g_cDesc.CarDro...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1109, "entity_name": "IpuCpCarDropAndExceptTocp", "code_range": {"start_line": 1109, "end_line": 1143}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline VFP_MAKE_HASHVALUE_BYIP(uint32 x, uint32 value)\n{\nmove value = 0;\nxor value = x[31:8], x[23:8];\nxor value = value, x[15:8];\nxor value = value, x[7:8];\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1145, "entity_name": "VFP_MAKE_HASHVALUE_BYIP", "code_range": {"start_line": 1145, "end_line": 1151}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline VFP_MAKE_HASHVALUE_BYPORT(uint16 x, uint32 value)\n{\nmove value = 0;\nxor value = x[15:8], x[7:8];\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1153, "entity_name": "VFP_MAKE_HASHVALUE_BYPORT", "code_range": {"start_line": 1153, "end_line": 1157}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuCalcPAEHashKey()\n{\n(IPV4_HEADER_S)                q0b   psIPv4Hdr;\n(IPV6_HEADER_S)                q0b   psIPv6Hdr;\n(IPV4_UDP_S)                   q0b   psIpv4Udp;\n// PAEHash下一次紧挨着的函数OutintfInit里会清空如下寄存器，所以这里先借用g_rsTblData256_2.LW3寄存器，应该不会和别人冲突\n(uint32) g_rsTblData256_2.LW3 rwTmpIpHash0;\n(uint32) g_rsTblData256_2.LW4 rwTmpIpHash1;\n(uint32) g_rsTblData256_2.LW5 rwTmpPortHash;\n(uint32) g_rsTblData256_2.LW6 rwTmpPaeHashValue;\nuint8  rbTmpReOpcode;\nbitcmp cc0 = g_rsUpfDesc.UpfHash<PERSON>ey<PERSON>alid, ON...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1179, "entity_name": "IpuCalcPAEHashKey", "code_range": {"start_line": 1179, "end_line": 1406}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IPEG_CheckDstIpIsWlr()\n{\nRE_S  rsDstRe;\n(IPV4_HEADER_S) q0b qsIPv4Hdr;\nmskcmp cc1 = qsIPv4Hdr.Version, IPV4_VERSION;\nif (cc1.eq) {\nIpv4FibSearch(g_cDesc.VpnID, qsIPv4Hdr.DIP, rsDstRe);\n} else {\nIpv6DipFibSearch(g_cDesc.VpnID, rsDstRe);\n}\nbitcmp cc1 = IO_RESPONSE_STATUS, FOUR_BIT_MASK;\nif (cc1.z) {\nbitcmp cc1 = rsDstRe.V, ONE_BIT_MASK;\nif (cc1.nz) {\nmskcmp cc1 = rsDstRe.OpCode, RE_OPCODE_WLR;\nif (cc1.eq) {\nmove g_cDesc.L3PaRst.L3PaFlag.L3ToCP = 0;\nExcept_DiscardStat(CAUSE_UPF_IPU_NORMAL_WLR_...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1409, "entity_name": "IPEG_CheckDstIpIsWlr", "code_range": {"start_line": 1409, "end_line": 1434}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuOspfMcSuppressionCheck()\n{\n// 与软转发保持一致，IPV4报文判断协议为OSPF+组播IP，根据源IP+VPN查路由为CPU进行组播抑制\n// IPV6报文判断解析结果为OSPF+组播MAC+非LB接收报文，根据源MAC查PORT_MAC表有效进行组播抑制\nuint8  rbSubDip;\nRE_S           rsDstReTemp;\n(IPV4_HEADER_S)             q0b            qsIpv4Hdr;\nmove rwFuncRtnData.Data0 = SUCCESSFUL;\nmskcmp cc0 = qsIpv4Hdr.Version, IPV4_VERSION;\nif (cc0.eq) {\ncmp cc1 = qsIpv4Hdr.TtlProtWord.Protocol, IPV4_PROT_OSPF;\nif (cc1.ne) {\nreturn;\n}\nmovezr rbSubDip = qsIpv4Hdr.DIP[31:4];\ncmp cc1 = rbSubDip, IPV4_MC_DI...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1436, "entity_name": "IpuOspfMcSuppressionCheck", "code_range": {"start_line": 1436, "end_line": 1481}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuOspfMcSuppressionCheckV6()\n{\nIPU_PORT_MAC_DATA_S rsPortMac;\n(ETH_S)                     q0b            qsEthHdr;\nSEARCH_IPU_PORT_MAC(rsPortMac, qsEthHdr.SmacH16, qsEthHdr.SmacL32);\nbitcmp cc1 = rsPortMac.Valid, ONE_BIT_MASK;\nif (cc1.nz) {\nmove rwFuncRtnData.Data0 = FAILED;\n}\nreturn;\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1483, "entity_name": "IpuOspfMcSuppressionCheckV6", "code_range": {"start_line": 1483, "end_line": 1494}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline IpuUj3PacketStat()\n{\n(ARP_PKT_S)         q0b    qsArpPkt;\n(DHCPV6_REQUEST_S)  q48b   qsDhcpv6Pkt;\n(IPV6_ICMP_S)       q0b    qsIpv6IcmpHdr;\nbitcmp cc5 = g_ssScratch5.UpfDbgSwitch, ONE_BIT_MASK;\nif (cc5.nz) branch_path {\n// ARP报文需要进一步解析细化CauseID\ncmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_ETH_ARP;\nif (cc0.eq) {\ncmp cc0 = qsArpPkt.Opcode, ARP_REQUEST_OPCODE;\nif (cc0.eq) {\nmove g_cDesc.CauseID =  EXCP_ID_IPV4_ARP_REQUEST;\n}\ncmp cc0 = qsArpPkt.Opcode, ARP_REPLY_OPCODE;\nif (cc0.eq) {\nmov...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1496, "entity_name": "IpuUj3PacketStat", "code_range": {"start_line": 1496, "end_line": 1585}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#include \"ipu_priv.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1, "entity_name": "import_0", "code_range": {"start_line": 1, "end_line": 1}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_util.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 2, "entity_name": "import_1", "code_range": {"start_line": 2, "end_line": 2}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_causeid.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 3, "entity_name": "import_2", "code_range": {"start_line": 3, "end_line": 3}, "metadata": {"import_type": "include"}}, {"content": "#include \"pkt_hdr.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 4, "entity_name": "import_3", "code_range": {"start_line": 4, "end_line": 4}, "metadata": {"import_type": "include"}}, {"content": "#include \"table_ip_nhp.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 5, "entity_name": "import_4", "code_range": {"start_line": 5, "end_line": 5}, "metadata": {"import_type": "include"}}, {"content": "#include \"ip_util.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 6, "entity_name": "import_5", "code_range": {"start_line": 6, "end_line": 6}, "metadata": {"import_type": "include"}}, {"content": "#include \"desc_common_instance.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 7, "entity_name": "import_6", "code_range": {"start_line": 7, "end_line": 7}, "metadata": {"import_type": "include"}}, {"content": "#include \"table_se.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 8, "entity_name": "import_7", "code_range": {"start_line": 8, "end_line": 8}, "metadata": {"import_type": "include"}}, {"content": "#include \"ingress.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 9, "entity_name": "import_8", "code_range": {"start_line": 9, "end_line": 9}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_cpu2np.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 10, "entity_name": "import_9", "code_range": {"start_line": 10, "end_line": 10}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_headr.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 11, "entity_name": "import_10", "code_range": {"start_line": 11, "end_line": 11}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_func.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 12, "entity_name": "import_11", "code_range": {"start_line": 12, "end_line": 12}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_iptrace.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 13, "entity_name": "import_12", "code_range": {"start_line": 13, "end_line": 13}, "metadata": {"import_type": "include"}}, {"content": "#include \"frame_hdr_ngsf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 14, "entity_name": "import_13", "code_range": {"start_line": 14, "end_line": 14}, "metadata": {"import_type": "include"}}, {"content": "#include \"table_qos.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 15, "entity_name": "import_14", "code_range": {"start_line": 15, "end_line": 15}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 16, "entity_name": "import_15", "code_range": {"start_line": 16, "end_line": 16}, "metadata": {"import_type": "include"}}, {"content": "#include \"pae_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 17, "entity_name": "import_16", "code_range": {"start_line": 17, "end_line": 17}, "metadata": {"import_type": "include"}}, {"content": "#include \"main.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 18, "entity_name": "import_17", "code_range": {"start_line": 18, "end_line": 18}, "metadata": {"import_type": "include"}}, {"content": "#include \"outintf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 19, "entity_name": "import_18", "code_range": {"start_line": 19, "end_line": 19}, "metadata": {"import_type": "include"}}, {"content": "#include \"except_util.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 20, "entity_name": "import_19", "code_range": {"start_line": 20, "end_line": 20}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 21, "entity_name": "import_20", "code_range": {"start_line": 21, "end_line": 21}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_resdef.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 22, "entity_name": "import_21", "code_range": {"start_line": 22, "end_line": 22}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_portinfo.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 23, "entity_name": "import_22", "code_range": {"start_line": 23, "end_line": 23}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_priv.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 24, "entity_name": "import_23", "code_range": {"start_line": 24, "end_line": 24}, "metadata": {"import_type": "include"}}, {"content": "#include \"except_np2cp_hdr.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 25, "entity_name": "import_24", "code_range": {"start_line": 25, "end_line": 25}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_np2cpu.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 26, "entity_name": "import_25", "code_range": {"start_line": 26, "end_line": 26}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_tbl_def.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 27, "entity_name": "import_26", "code_range": {"start_line": 27, "end_line": 27}, "metadata": {"import_type": "include"}}, {"content": "#include \"frame_hdr.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 28, "entity_name": "import_27", "code_range": {"start_line": 28, "end_line": 28}, "metadata": {"import_type": "include"}}, {"content": "#include \"fh_parse.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 29, "entity_name": "import_28", "code_range": {"start_line": 29, "end_line": 29}, "metadata": {"import_type": "include"}}, {"content": "#include \"rb.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 30, "entity_name": "import_29", "code_range": {"start_line": 30, "end_line": 30}, "metadata": {"import_type": "include"}}, {"content": "#include \"lb_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 31, "entity_name": "import_30", "code_range": {"start_line": 31, "end_line": 31}, "metadata": {"import_type": "include"}}, {"content": "#include \"lb_res_def.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 32, "entity_name": "import_31", "code_range": {"start_line": 32, "end_line": 32}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_iptrace.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 34, "entity_name": "import_33", "code_range": {"start_line": 34, "end_line": 34}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_v6udpchksum.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 36, "entity_name": "import_35", "code_range": {"start_line": 36, "end_line": 36}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_softpara.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 37, "entity_name": "import_36", "code_range": {"start_line": 37, "end_line": 37}, "metadata": {"import_type": "include"}}, {"content": "#include \"loadbalance_util.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 38, "entity_name": "import_37", "code_range": {"start_line": 38, "end_line": 38}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_matchcnt.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 39, "entity_name": "import_38", "code_range": {"start_line": 39, "end_line": 39}, "metadata": {"import_type": "include"}}, {"content": "// uint32 g_rwGiRedirectDstIpv6Part3;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 49, "entity_name": "g_rwGiRedirectDstIpv6Part3", "code_range": {"start_line": 49, "end_line": 49}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbHdrLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 223, "entity_name": "rbHdrLen", "code_range": {"start_line": 223, "end_line": 223}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 l3stake;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 224, "entity_name": "l3stake", "code_range": {"start_line": 224, "end_line": 224}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   rbIpver;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 249, "entity_name": "rbIpver", "code_range": {"start_line": 249, "end_line": 249}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  rwPktTheoryLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 272, "entity_name": "rwPktTheoryLen", "code_range": {"start_line": 272, "end_line": 272}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   rbIpv4HdrLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 273, "entity_name": "rbIpv4HdrLen", "code_range": {"start_line": 273, "end_line": 273}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   rbIptraceEnFlag;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 274, "entity_name": "rbIptraceEnFlag", "code_range": {"start_line": 274, "end_line": 274}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  rhTemp;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 275, "entity_name": "rhTemp", "code_range": {"start_line": 275, "end_line": 275}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32  rwPktTheoryLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 417, "entity_name": "rwPktTheoryLen", "code_range": {"start_line": 417, "end_line": 417}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   rbIptraceEnFlag;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 419, "entity_name": "rbIptraceEnFlag", "code_range": {"start_line": 419, "end_line": 419}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16  rhDelLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 420, "entity_name": "rhDelLen", "code_range": {"start_line": 420, "end_line": 420}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16 rhRealLength;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 669, "entity_name": "rhR<PERSON><PERSON><PERSON>th", "code_range": {"start_line": 669, "end_line": 669}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  rhPullmoreLength;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 670, "entity_name": "rhPullmoreLength", "code_range": {"start_line": 670, "end_line": 670}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          fhLength;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 769, "entity_name": "fhLength", "code_range": {"start_line": 769, "end_line": 769}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          fhL3Stake;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 770, "entity_name": "fhL3Stake", "code_range": {"start_line": 770, "end_line": 770}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 rwData;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 877, "entity_name": "rwData", "code_range": {"start_line": 877, "end_line": 877}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  rbHdrVersion;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 914, "entity_name": "rbHdrVersion", "code_range": {"start_line": 914, "end_line": 914}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 l3Stake;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 931, "entity_name": "l3Stake", "code_range": {"start_line": 931, "end_line": 931}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8   rbBtobToCpType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 932, "entity_name": "rbBtobToCpType", "code_range": {"start_line": 932, "end_line": 932}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 cpType;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 955, "entity_name": "cpType", "code_range": {"start_line": 955, "end_line": 955}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 l3Stake;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 992, "entity_name": "l3Stake", "code_range": {"start_line": 992, "end_line": 992}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  rbTmpReOpcode;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1189, "entity_name": "rbTmpReOpcode", "code_range": {"start_line": 1189, "end_line": 1189}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  rbSubDip;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_proc.asm", "line_number": 1440, "entity_name": "rbSubDip", "code_range": {"start_line": 1440, "end_line": 1440}, "metadata": {"variable_type": "declaration"}}, {"content": "void iIpuUsrTrcOutIpV6Judge(uint8 IsInPae)\n{\nuint8 rbIndex;\n/* 单个报文没有多次调用iIpuUsrTrcOutIpV6Judge的情况，*/\n/* 在解析报文前将解析结果初始化一下 */\niIpuUsrTrcChkAlreadyMatch();\nUPF_ZERO_COMMON_32BIT_DATA(rsUpfIpPktL3Info)  // 调用UpfParseIpPkt前显式清空解析结果，避免使用时报告警\nUPF_ZERO_COMMON_128BIT_DATA(rsUpfIpPktL4Info) // 调用UpfParseIpPkt前显式清空解析结果，避免使用时报告警\nUpfParseIpPkt(IP_PARSER_TYPE_INNER_L3);\niIpuUsrTrcChkParseRst();\nmove rbIndex = 0;\nUsrTrcMatch:\ncmp cc0 = rbIndex, USRTRC_RULE_NUM;\nif (cc0.lt)\n{\nREAD_USERTRACE_RULE_TBL_FST_16B(rs...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_usertrace.asm", "line_number": 4, "entity_name": "iIpuUsrTrcOutIpV6Judge", "code_range": {"start_line": 4, "end_line": 62}, "metadata": {"return_type": null, "parameters": []}}, {"content": "uint8 rbIndex;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_usertrace.asm", "line_number": 6, "entity_name": "rbIndex", "code_range": {"start_line": 6, "end_line": 6}, "metadata": {"variable_type": "declaration"}}, {"content": "void UpfPosJudgeNextType()\n{\ncmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_IPV4;\nif (cc0.eq) {\njmp IP_PROC;\n}\ncmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_IPV6;\nif (cc0.eq) {\njmp IP_PROC;\n}\ncmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_MPLSUC;\nif (cc0.eq) {\njmp MPLS_PROC;\n}\njmp FOR_DEFAULT;\n// 将上面的if改写成switchcase\nIP_PROC:\nmovezr rsUpfSpecExtRetInfo.PosInfo.PosNextType = POS_NEXT_HEAD_TYPE_IP;\njmp END;\nMPLS_PROC:\nmovezr rsUpfSpecExtRetInfo.PosIn...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/switch1.asm", "line_number": 1, "entity_name": "UpfPosJudgeNextType", "code_range": {"start_line": 1, "end_line": 29}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void main() {\nprint(MSG);\nvar1 = 5;\nvar2 = 7;\nsum = add(var1, var2);\nprint(sum);\np = Point { x: 3, y: 4 };\nprint_point(p);\n// square\nSQUARE(va1,var2);\nprint(sq);\nif (sq > 40) {\nprint(\"square > 40\");\n} else {\nprint(\"square <= 40\");\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/main.asm", "line_number": 6, "entity_name": "main", "code_range": {"start_line": 6, "end_line": 26}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#include \"math.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/main.asm", "line_number": 1, "entity_name": "import_0", "code_range": {"start_line": 1, "end_line": 1}, "metadata": {"import_type": "include"}}, {"content": "#include \"stdio.np\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/main.asm", "line_number": 2, "entity_name": "import_1", "code_range": {"start_line": 2, "end_line": 2}, "metadata": {"import_type": "include"}}, {"content": "inline IpuUsrTrcBtoBRxSetTrcDir()\n{\nbitcmp cc1 = rsPaeExt.UserTrcUpFlag, ONE_BIT_MASK;\nif (cc1.nz) {\nmove rsUpfDesc.UserTrcDir = NPUSERTRC_TRCTYPE_UP_OUT_FROM_OTHERNP;\nUpfExceptStatNoDiscard(CAUSE_UPF_NORMAL_USRTRC_UP_OUT_BTOBRX);\n} else {\nmove rsUpfDesc.UserTrcDir = NPUSERTRC_TRCTYPE_DN_OUT_FROM_OTHERNP;\nUpfExceptStatNoDiscard(CAUSE_UPF_NORMAL_USRTRC_DN_OUT_BTOBRX);\n}\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipc.asm", "line_number": 2, "entity_name": "IpuUsrTrcBtoBRxSetTrcDir", "code_range": {"start_line": 2, "end_line": 12}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void eUserTrcNpOutGetDstTb()\n{\n(ETM_ETH_PAE_S) q0b qsEtmEthPaeHdr;\n(ETM_ETHONETAG_PAE_S) q0b qsEtmEth1TagPaeHdr;\n(ETM_ETHTWOTAG_PAE_S) q0b qsEtmEth2TagPaeHdr;\ncmp cc0 = qsEtmEthPaeHdr.NoTag.Type, ETH_TYPE_PAE;\ncmp cc1 = qsEtmEth1TagPaeHdr.OneTag.TAG1.TPID, ETH_TPID_TAGGED_STD;\ncmp cc2 = qsEtmEth1TagPaeHdr.OneTag.Type, ETH_TYPE_PAE;\nif (cc0.eq)\n{\nmove rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEtmEthPaeHdr.PaeHdr.DstTbL32);move rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEthPaeHdr.PaeHdr.DstTbL32);\nmove rsF...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipc.asm", "line_number": 14, "entity_name": "eUserTrcNpOutGetDstTb", "code_range": {"start_line": 14, "end_line": 59}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void eUserTrcNpInGetDstTb()\n{\n(ETH_PAE_S) q0b qsEthPaeHdr;\n(ETHONETAG_PAE_S) q0b qsEth1TagPaeHdr;\n(ETHTWOTAG_PAE_S) q0b qsEth2TagPaeHdr;\ncmp cc0 = qsEthPaeHdr.NoTag.Type, ETH_TYPE_PAE;\ncmp cc1 = qsEth1TagPaeHdr.OneTag.TAG1.TPID, ETH_TPID_TAGGED_STD;\ncmp cc2 = qsEth1TagPaeHdr.OneTag.Type, ETH_TYPE_PAE;\nif (cc0.eq)\n{\nmove rsFuncRtnData.Data0 = PAE_LOW_16BIT(qsEthPaeHdr.PaeHdr.DstTbL32);\nmove rsFuncRtnData.Data1 = TRUE;\nreturn;\n}\nif (cc1.eq)\n{\nif (cc2.eq)\n{\nmove rsFuncRtnData.Data0 = PAE_LOW_16BIT(...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipc.asm", "line_number": 61, "entity_name": "eUserTrcNpInGetDstTb", "code_range": {"start_line": 61, "end_line": 103}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#define IPU_TTL_LIMIT_MIN 1", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_priv.h", "line_number": 6, "entity_name": "IPU_TTL_LIMIT_MIN", "code_range": {"start_line": 6, "end_line": 7}, "metadata": {"definition": "1", "parameters": []}}, {"content": "#define IPU_INVALID_SBSP 0x3fff", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_priv.h", "line_number": 63, "entity_name": "IPU_INVALID_SBSP", "code_range": {"start_line": 63, "end_line": 64}, "metadata": {"definition": "0x3fff", "parameters": []}}, {"content": "#define SOFT_LOOP_CHN_MAP_INDEX_MASK 0x00FF", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_priv.h", "line_number": 64, "entity_name": "SOFT_LOOP_CHN_MAP_INDEX_MASK", "code_range": {"start_line": 64, "end_line": 65}, "metadata": {"definition": "0x00FF", "parameters": []}}, {"content": "struct IPU_DSCPMAP_S {\n    uint2 DscpMapType;\n    uint3 Exp;\n    uint3 VlanPri;\n    uint24 Rsvd0;\n    uint32 Rsvd1;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 16, "entity_name": "IPU_DSCPMAP_S", "code_range": {"start_line": 16, "end_line": 23}, "metadata": {"field_count": 5, "fields": ["DscpMapType", "Exp", "VlanPri", "Rsvd0", "Rsvd1"]}}, {"content": "struct BD_BITMAP_S {\n    uint1 Valid1;\n    uint30 Valid2;\n    uint1 Valid3;\n    uint1 Rsvd0;\n    uint30 Rsvd1;\n    uint1 Rsvd2;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 33, "entity_name": "BD_BITMAP_S", "code_range": {"start_line": 33, "end_line": 43}, "metadata": {"field_count": 6, "fields": ["Valid1", "Valid2", "Valid3", "Rsvd0", "Rsvd1", "Rsvd2"]}}, {"content": "struct IPU_PORT_MAC_DATA_S {\n    uint1 Valid;\n    uint15 Rsvd0;\n    PORTINFO_S PortInfo;\n    uint32 Rsvd1;\n    uint32 Rsvd2;\n    uint32 Rsvd3;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 52, "entity_name": "IPU_PORT_MAC_DATA_S", "code_range": {"start_line": 52, "end_line": 60}, "metadata": {"field_count": 6, "fields": ["<PERSON><PERSON>", "Rsvd0", "PortInfo", "Rsvd1", "Rsvd2", "Rsvd3"]}}, {"content": "uint32         Rsvd1;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 22, "entity_name": "Rsvd1", "code_range": {"start_line": 22, "end_line": 22}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd1;                           /* 保留字段 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 57, "entity_name": "Rsvd1", "code_range": {"start_line": 57, "end_line": 57}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd2;                           /* 保留字段 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 58, "entity_name": "Rsvd2", "code_range": {"start_line": 58, "end_line": 58}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd3;                           /* 保留字段 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_def.h", "line_number": 59, "entity_name": "Rsvd3", "code_range": {"start_line": 59, "end_line": 59}, "metadata": {"variable_type": "declaration"}}, {"content": "extern bundle add(a, b);", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/math.h", "line_number": 9, "entity_name": "add", "code_range": {"start_line": 9, "end_line": 9}, "metadata": {"return_type": null, "parameters": []}}, {"content": "extern void print_point(p);", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/math.h", "line_number": 10, "entity_name": "print_point", "code_range": {"start_line": 10, "end_line": 10}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#define E 2.718", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/math.h", "line_number": 1, "entity_name": "E", "code_range": {"start_line": 1, "end_line": 2}, "metadata": {"definition": "2.718", "parameters": []}}, {"content": "#define SQUARE ((x)*(x))", "context_type": "macro_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/math.h", "line_number": 2, "entity_name": "SQUARE", "code_range": {"start_line": 2, "end_line": 3}, "metadata": {"definition": "((x)*(x))", "parameters": ["x"]}}, {"content": "void print_point(p) {\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/math.asm", "line_number": 8, "entity_name": "print_point", "code_range": {"start_line": 8, "end_line": 10}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#include \"math.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/math.asm", "line_number": 1, "entity_name": "import_0", "code_range": {"start_line": 1, "end_line": 1}, "metadata": {"import_type": "include"}}, {"content": "struct IPU_PORT_MAC_KEY_S {\n    uint6 SubTid;\n    uint2 Rsvd0;\n    uint16 DMacH16;\n    uint32 DMacL32;\n    uint32 Rsvd1;\n    uint24 Rsvd2;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 12, "entity_name": "IPU_PORT_MAC_KEY_S", "code_range": {"start_line": 12, "end_line": 20}, "metadata": {"field_count": 6, "fields": ["SubTid", "Rsvd0", "DMacH16", "DMacL32", "Rsvd1", "Rsvd2"]}}, {"content": "struct IPTRC_KEY_S {\n    uint4 Tid;\n    uint4 SubTid;\n    uint8 L4Protol;\n    uint16 L3Protol;\n    uint32 LocalIPPart0;\n    uint32 LocalIPPart1;\n    uint32 LocalIPPart2;\n    uint32 LocalIPPart3;\n    uint32 PeerIPPart0;\n    uint32 PeerIPPart1;\n    uint32 PeerIPPart2;\n    uint32 PeerIPPart3;\n    uint16 SrcPort;\n    uint16 DstPort;\n    uint4 Tid1;\n    uint4 SubTid1;\n    uint16 VpnId;\n    uint4 Direction;\n    uint4 Rsvd0;\n    uint32 Rsvd1;\n    uint32 Rsvd2;\n    uint32 Rsvd3;\n    uint32 Rsvd4;\n    ui...", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 27, "entity_name": "IPTRC_KEY_S", "code_range": {"start_line": 27, "end_line": 67}, "metadata": {"field_count": 28, "fields": ["Tid", "SubTid", "L4Protol", "L3Protol", "LocalIPPart0", "LocalIPPart1", "LocalIPPart2", "LocalIPPart3", "PeerIPPart0", "PeerIPPart1", "PeerIPPart2", "PeerIPPart3", "SrcPort", "DstPort", "Tid1", "SubTid1", "VpnId", "Direction", "Rsvd0", "Rsvd1", "Rsvd2", "Rsvd3", "Rsvd4", "Rsvd5", "Rsvd6", "Rsvd7", "Rsvd8", "Rsvd9"]}}, {"content": "struct IPTRC_KEY1_S {\n    uint4 Tid;\n    uint4 SubTid;\n    uint8 L4Protol;\n    uint16 L3Protol;\n    uint32 LocalIPPart0;\n    uint32 LocalIPPart1;\n    uint32 LocalIPPart2;\n    uint32 LocalIPPart3;\n    uint32 PeerIPPart0;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 69, "entity_name": "IPTRC_KEY1_S", "code_range": {"start_line": 69, "end_line": 90}, "metadata": {"field_count": 9, "fields": ["Tid", "SubTid", "L4Protol", "L3Protol", "LocalIPPart0", "LocalIPPart1", "LocalIPPart2", "LocalIPPart3", "PeerIPPart0"]}}, {"content": "struct IPTRC_KEY2_S {\n    uint32 PeerIPPart1;\n    uint32 PeerIPPart2;\n    uint32 PeerIPPart3;\n    uint16 SrcPort;\n    uint16 DstPort;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 92, "entity_name": "IPTRC_KEY2_S", "code_range": {"start_line": 92, "end_line": 99}, "metadata": {"field_count": 5, "fields": ["PeerIPPart1", "PeerIPPart2", "PeerIPPart3", "SrcPort", "DstPort"]}}, {"content": "struct IPTRC_KEY3_S {\n    uint4 Tid1;\n    uint4 SubTid1;\n    uint16 VpnId;\n    uint4 Direction;\n    uint4 Rsvd0;\n    uint32 Rsvd1;\n    uint32 Rsvd2;\n    uint32 Rsvd3;\n    uint32 Rsvd4;\n    uint32 Rsvd5;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 101, "entity_name": "IPTRC_KEY3_S", "code_range": {"start_line": 101, "end_line": 113}, "metadata": {"field_count": 10, "fields": ["Tid1", "SubTid1", "VpnId", "Direction", "Rsvd0", "Rsvd1", "Rsvd2", "Rsvd3", "Rsvd4", "Rsvd5"]}}, {"content": "struct IPTRC_KEY4_S {\n    uint32 Rsvd6;\n    uint32 Rsvd7;\n    uint32 Rsvd8;\n    uint32 Rsvd9;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 115, "entity_name": "IPTRC_KEY4_S", "code_range": {"start_line": 115, "end_line": 121}, "metadata": {"field_count": 4, "fields": ["Rsvd6", "Rsvd7", "Rsvd8", "Rsvd9"]}}, {"content": "struct IPSEC_FLEX_ACLV4_KEY_S {\n    uint4 Tid;\n    uint4 SubTid;\n    uint8 Rsvd0;\n    uint16 VrfId;\n    uint32 DstIP;\n    uint32 SrcIp;\n    uint32 Rsvd1;\n    uint32 PolicyGroupIndex;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 132, "entity_name": "IPSEC_FLEX_ACLV4_KEY_S", "code_range": {"start_line": 132, "end_line": 142}, "metadata": {"field_count": 8, "fields": ["Tid", "SubTid", "Rsvd0", "VrfId", "DstIP", "SrcIp", "Rsvd1", "PolicyGroupIndex"]}}, {"content": "struct IPSEC_FLEX_ACLV6_KEY_S {\n    uint4 Tid;\n    uint4 SubTid;\n    uint8 Rsvd0;\n    uint16 VrfId;\n    uint32 DstIpPart0;\n    uint32 DstIpPart1;\n    uint32 DstIpPart2;\n    uint32 DstIpPart3;\n    uint32 SrcIpPart0;\n    uint32 SrcIpPart1;\n    uint32 SrcIpPart2;\n    uint32 SrcIpPart3;\n    uint32 PolicyGroupIndex;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 154, "entity_name": "IPSEC_FLEX_ACLV6_KEY_S", "code_range": {"start_line": 154, "end_line": 169}, "metadata": {"field_count": 13, "fields": ["Tid", "SubTid", "Rsvd0", "VrfId", "DstIpPart0", "DstIpPart1", "DstIpPart2", "DstIpPart3", "SrcIpPart0", "SrcIpPart1", "SrcIpPart2", "SrcIpPart3", "PolicyGroupIndex"]}}, {"content": "struct IPSEC_FLEX_ACLV6_KEY1_S {\n    uint4 Tid;\n    uint4 SubTid;\n    uint8 Rsvd0;\n    uint16 VrfId;\n    uint32 DstIpPart0;\n    uint32 DstIpPart1;\n    uint32 DstIpPart2;\n    uint32 DstIpPart3;\n    uint32 SrcIpPart0;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 171, "entity_name": "IPSEC_FLEX_ACLV6_KEY1_S", "code_range": {"start_line": 171, "end_line": 182}, "metadata": {"field_count": 9, "fields": ["Tid", "SubTid", "Rsvd0", "VrfId", "DstIpPart0", "DstIpPart1", "DstIpPart2", "DstIpPart3", "SrcIpPart0"]}}, {"content": "struct IPSEC_FLEX_ACLV6_KEY2_S {\n    uint32 SrcIpPart1;\n    uint32 SrcIpPart2;\n    uint32 SrcIpPart3;\n    uint32 PolicyGroupIndex;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 184, "entity_name": "IPSEC_FLEX_ACLV6_KEY2_S", "code_range": {"start_line": 184, "end_line": 190}, "metadata": {"field_count": 4, "fields": ["SrcIpPart1", "SrcIpPart2", "SrcIpPart3", "PolicyGroupIndex"]}}, {"content": "struct IPSEC_FLEX_LOCALIP_KEY_S {\n    uint4 SubTid;\n    uint4 Rsvd0;\n    uint24 Rsvd1;\n    uint16 Rsvd2;\n    uint16 VrfId;\n    uint32 SrcIp;\n    uint32 DstIP;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 202, "entity_name": "IPSEC_FLEX_LOCALIP_KEY_S", "code_range": {"start_line": 202, "end_line": 211}, "metadata": {"field_count": 7, "fields": ["SubTid", "Rsvd0", "Rsvd1", "Rsvd2", "VrfId", "SrcIp", "DstIP"]}}, {"content": "struct IPSEC_FLEX_TNLINFO_KEY_S {\n    uint4 SubTid;\n    uint4 Rsvd0;\n    uint8 Rsvd1;\n    uint32 LmtIdx;\n    uint16 Rsvd2;\n};", "context_type": "struct_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 223, "entity_name": "IPSEC_FLEX_TNLINFO_KEY_S", "code_range": {"start_line": 223, "end_line": 230}, "metadata": {"field_count": 5, "fields": ["SubTid", "Rsvd0", "Rsvd1", "LmtIdx", "Rsvd2"]}}, {"content": "uint16         DMacH16;                         /* 目的MAC高16bit */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 16, "entity_name": "DMacH16", "code_range": {"start_line": 16, "end_line": 16}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DMacL32;                         /* 目的MAC低32bit */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 17, "entity_name": "DMacL32", "code_range": {"start_line": 17, "end_line": 17}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd1;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 18, "entity_name": "Rsvd1", "code_range": {"start_line": 18, "end_line": 18}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          L4Protol;                        /* 四层协议类型，如ARP、TCP、UDP等， */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 31, "entity_name": "L4Protol", "code_range": {"start_line": 31, "end_line": 31}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         L3Protol;                        /* 协议类型。 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 39, "entity_name": "L3Protol", "code_range": {"start_line": 39, "end_line": 39}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart0;                    /* 本地IP地址，IP地址第1部分（高位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 43, "entity_name": "LocalIPPart0", "code_range": {"start_line": 43, "end_line": 43}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart1;                    /* 本地IP地址，IP地址第2部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 44, "entity_name": "LocalIPPart1", "code_range": {"start_line": 44, "end_line": 44}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart2;                    /* 本地IP地址，IP地址第3部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 45, "entity_name": "LocalIPPart2", "code_range": {"start_line": 45, "end_line": 45}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart3;                    /* 本地IP地址，IP地址第4部分（低位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 46, "entity_name": "LocalIPPart3", "code_range": {"start_line": 46, "end_line": 46}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart0;                     /* 远端IP地址，IP地址第1部分（高位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 47, "entity_name": "PeerIPPart0", "code_range": {"start_line": 47, "end_line": 47}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart1;                     /* 远端IP地址，IP地址第2部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 48, "entity_name": "PeerIPPart1", "code_range": {"start_line": 48, "end_line": 48}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart2;                     /* 远端IP地址，IP地址第3部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 49, "entity_name": "PeerIPPart2", "code_range": {"start_line": 49, "end_line": 49}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart3;                     /* 远端IP地址，IP地址第4部分（低位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 50, "entity_name": "PeerIPPart3", "code_range": {"start_line": 50, "end_line": 50}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         SrcPort;                         /* 源端口 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 51, "entity_name": "SrcPort", "code_range": {"start_line": 51, "end_line": 51}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         DstPort;                         /* 目的端口 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 52, "entity_name": "DstPort", "code_range": {"start_line": 52, "end_line": 52}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         VpnId;                           /* VpnId */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 55, "entity_name": "VpnId", "code_range": {"start_line": 55, "end_line": 55}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd1;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 58, "entity_name": "Rsvd1", "code_range": {"start_line": 58, "end_line": 58}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd2;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 59, "entity_name": "Rsvd2", "code_range": {"start_line": 59, "end_line": 59}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd3;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 60, "entity_name": "Rsvd3", "code_range": {"start_line": 60, "end_line": 60}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd4;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 61, "entity_name": "Rsvd4", "code_range": {"start_line": 61, "end_line": 61}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd5;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 62, "entity_name": "Rsvd5", "code_range": {"start_line": 62, "end_line": 62}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd6;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 63, "entity_name": "Rsvd6", "code_range": {"start_line": 63, "end_line": 63}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd7;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 64, "entity_name": "Rsvd7", "code_range": {"start_line": 64, "end_line": 64}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd8;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 65, "entity_name": "Rsvd8", "code_range": {"start_line": 65, "end_line": 65}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd9;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 66, "entity_name": "Rsvd9", "code_range": {"start_line": 66, "end_line": 66}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          L4Protol;                        /* 四层协议类型，如ARP、TCP、UDP等， */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 73, "entity_name": "L4Protol", "code_range": {"start_line": 73, "end_line": 73}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         L3Protol;                        /* 协议类型。 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 81, "entity_name": "L3Protol", "code_range": {"start_line": 81, "end_line": 81}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart0;                    /* 本地IP地址，IP地址第1部分（高位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 85, "entity_name": "LocalIPPart0", "code_range": {"start_line": 85, "end_line": 85}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart1;                    /* 本地IP地址，IP地址第2部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 86, "entity_name": "LocalIPPart1", "code_range": {"start_line": 86, "end_line": 86}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart2;                    /* 本地IP地址，IP地址第3部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 87, "entity_name": "LocalIPPart2", "code_range": {"start_line": 87, "end_line": 87}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LocalIPPart3;                    /* 本地IP地址，IP地址第4部分（低位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 88, "entity_name": "LocalIPPart3", "code_range": {"start_line": 88, "end_line": 88}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart0;                     /* 远端IP地址，IP地址第1部分（高位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 89, "entity_name": "PeerIPPart0", "code_range": {"start_line": 89, "end_line": 89}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart1;                     /* 远端IP地址，IP地址第2部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 94, "entity_name": "PeerIPPart1", "code_range": {"start_line": 94, "end_line": 94}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart2;                     /* 远端IP地址，IP地址第3部分 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 95, "entity_name": "PeerIPPart2", "code_range": {"start_line": 95, "end_line": 95}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PeerIPPart3;                     /* 远端IP地址，IP地址第4部分（低位） */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 96, "entity_name": "PeerIPPart3", "code_range": {"start_line": 96, "end_line": 96}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         SrcPort;                         /* 源端口 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 97, "entity_name": "SrcPort", "code_range": {"start_line": 97, "end_line": 97}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         DstPort;                         /* 目的端口 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 98, "entity_name": "DstPort", "code_range": {"start_line": 98, "end_line": 98}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         VpnId;                           /* VpnId */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 105, "entity_name": "VpnId", "code_range": {"start_line": 105, "end_line": 105}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd1;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 108, "entity_name": "Rsvd1", "code_range": {"start_line": 108, "end_line": 108}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd2;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 109, "entity_name": "Rsvd2", "code_range": {"start_line": 109, "end_line": 109}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd3;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 110, "entity_name": "Rsvd3", "code_range": {"start_line": 110, "end_line": 110}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd4;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 111, "entity_name": "Rsvd4", "code_range": {"start_line": 111, "end_line": 111}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd5;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 112, "entity_name": "Rsvd5", "code_range": {"start_line": 112, "end_line": 112}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd6;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 117, "entity_name": "Rsvd6", "code_range": {"start_line": 117, "end_line": 117}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd7;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 118, "entity_name": "Rsvd7", "code_range": {"start_line": 118, "end_line": 118}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd8;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 119, "entity_name": "Rsvd8", "code_range": {"start_line": 119, "end_line": 119}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd9;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 120, "entity_name": "Rsvd9", "code_range": {"start_line": 120, "end_line": 120}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          Rsvd0;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 136, "entity_name": "Rsvd0", "code_range": {"start_line": 136, "end_line": 136}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         VrfId;                           /* VPNID */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 137, "entity_name": "VrfId", "code_range": {"start_line": 137, "end_line": 137}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIP;                           /* 目的IP */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 138, "entity_name": "DstIP", "code_range": {"start_line": 138, "end_line": 138}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIp;                           /* 源IP */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 139, "entity_name": "SrcIp", "code_range": {"start_line": 139, "end_line": 139}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         Rsvd1;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 140, "entity_name": "Rsvd1", "code_range": {"start_line": 140, "end_line": 140}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PolicyGroupIndex;                /* uiPolicyGroupIndex */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 141, "entity_name": "PolicyGroupIndex", "code_range": {"start_line": 141, "end_line": 141}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          Rsvd0;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 158, "entity_name": "Rsvd0", "code_range": {"start_line": 158, "end_line": 158}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         VrfId;                           /* VPNID */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 159, "entity_name": "VrfId", "code_range": {"start_line": 159, "end_line": 159}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart0;                      /* 目的IPv6Part0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 160, "entity_name": "DstIpPart0", "code_range": {"start_line": 160, "end_line": 160}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart1;                      /* 目的IPv6Part1 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 161, "entity_name": "DstIpPart1", "code_range": {"start_line": 161, "end_line": 161}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart2;                      /* 目的IPv6Part2 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 162, "entity_name": "DstIpPart2", "code_range": {"start_line": 162, "end_line": 162}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart3;                      /* 目的IPv6Part3 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 163, "entity_name": "DstIpPart3", "code_range": {"start_line": 163, "end_line": 163}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart0;                      /* 源IPv6Part0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 164, "entity_name": "SrcIpPart0", "code_range": {"start_line": 164, "end_line": 164}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart1;                      /* 源IPv6Part1 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 165, "entity_name": "SrcIpPart1", "code_range": {"start_line": 165, "end_line": 165}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart2;                      /* 源IPv6Part2 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 166, "entity_name": "SrcIpPart2", "code_range": {"start_line": 166, "end_line": 166}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart3;                      /* 源IPv6Part3 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 167, "entity_name": "SrcIpPart3", "code_range": {"start_line": 167, "end_line": 167}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PolicyGroupIndex;                /* uiPolicyGroupIndex */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 168, "entity_name": "PolicyGroupIndex", "code_range": {"start_line": 168, "end_line": 168}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          Rsvd0;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 175, "entity_name": "Rsvd0", "code_range": {"start_line": 175, "end_line": 175}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         VrfId;                           /* VPNID */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 176, "entity_name": "VrfId", "code_range": {"start_line": 176, "end_line": 176}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart0;                      /* 目的IPv6Part0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 177, "entity_name": "DstIpPart0", "code_range": {"start_line": 177, "end_line": 177}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart1;                      /* 目的IPv6Part1 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 178, "entity_name": "DstIpPart1", "code_range": {"start_line": 178, "end_line": 178}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart2;                      /* 目的IPv6Part2 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 179, "entity_name": "DstIpPart2", "code_range": {"start_line": 179, "end_line": 179}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIpPart3;                      /* 目的IPv6Part3 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 180, "entity_name": "DstIpPart3", "code_range": {"start_line": 180, "end_line": 180}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart0;                      /* 源IPv6Part0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 181, "entity_name": "SrcIpPart0", "code_range": {"start_line": 181, "end_line": 181}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart1;                      /* 源IPv6Part1 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 186, "entity_name": "SrcIpPart1", "code_range": {"start_line": 186, "end_line": 186}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart2;                      /* 源IPv6Part2 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 187, "entity_name": "SrcIpPart2", "code_range": {"start_line": 187, "end_line": 187}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIpPart3;                      /* 源IPv6Part3 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 188, "entity_name": "SrcIpPart3", "code_range": {"start_line": 188, "end_line": 188}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         PolicyGroupIndex;                /* uiPolicyGroupIndex */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 189, "entity_name": "PolicyGroupIndex", "code_range": {"start_line": 189, "end_line": 189}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         Rsvd2;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 207, "entity_name": "Rsvd2", "code_range": {"start_line": 207, "end_line": 207}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         VrfId;                           /* VPNID */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 208, "entity_name": "VrfId", "code_range": {"start_line": 208, "end_line": 208}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         SrcIp;                           /* 源IP */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 209, "entity_name": "SrcIp", "code_range": {"start_line": 209, "end_line": 209}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         DstIP;                           /* 目的IP */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 210, "entity_name": "DstIP", "code_range": {"start_line": 210, "end_line": 210}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8          Rsvd1;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 227, "entity_name": "Rsvd1", "code_range": {"start_line": 227, "end_line": 227}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32         LmtIdx;                          /* ipsec隧道口索引 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 228, "entity_name": "LmtIdx", "code_range": {"start_line": 228, "end_line": 228}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16         Rsvd2;                           /* 保持等于0 */", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_key_def.h", "line_number": 229, "entity_name": "Rsvd2", "code_range": {"start_line": 229, "end_line": 229}, "metadata": {"variable_type": "declaration"}}, {"content": "void IpuIPV4ING_ModifyTtl()\n{\nuint8    rbTtlDec;\nuint16   rhOldValue;\n(IPV4_HEADER_S)  q0b            psIPv4Hdr;\nmove rwFuncRtnData.Data1 = 0; //使用前清0\nmovezr rbTtlDec = g_iDesc.NoTtlDec;\ncmp cc0 = psIPv4Hdr.TtlProtWord.Ttl, IPU_TTL_LIMIT_MIN;\nif (cc0.le) {\nmove rwFuncRtnData.Data1 = FAILED;\nreturn;\n}\nmove g_iDesc.NoTtlDec = 0;\nmove rhOldValue = q8h;\nsub psIPv4Hdr.TtlProtWord.Ttl = psIPv4Hdr.TtlProtWord.Ttl, rbTtlDec;   //TTL - 1\n//重新计算Checksum\nCalcPartialChecksum(psIPv4Hdr.Checksum, q8h, rhOldVa...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 34, "entity_name": "IpuIPV4ING_ModifyTtl", "code_range": {"start_line": 34, "end_line": 57}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuIPV6ING_ModifyTtl()\n{\nuint8    rbTtlDec;\n(IPV6_HEADER_S)   q0b    psIPv6Hdr;\nmove rwFuncRtnData.Data1 = 0; //使用前清0\nmovezr rbTtlDec = g_iDesc.NoTtlDec;\ncmp cc0 = psIPv6Hdr.HopLimit, IPU_TTL_LIMIT_MIN;\nif (cc0.le) {\nmove rwFuncRtnData.Data1 = FAILED;\nreturn;\n}\nsub psIPv6Hdr.HopLimit = psIPv6Hdr.HopLimit, rbTtlDec;   //TTL - 1\nmove g_iDesc.NoTtlDec = 0;\nSET_L3_DIRTY_LEN(IPV6_HDR_DIRTY_LEN)\nreturn;\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 59, "entity_name": "IpuIPV6ING_ModifyTtl", "code_range": {"start_line": 59, "end_line": 77}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void UpfReadCurNhp(uint24 nhpIndex, uint8 offset)\n{\nREAD_NHP_TBL_PTG(g_rsNhp, nhpIndex, offset);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 79, "entity_name": "UpfReadCurNhp", "code_range": {"start_line": 79, "end_line": 82}, "metadata": {"return_type": null, "parameters": []}}, {"content": "void IpuGetWlrNhp(uint8 rbNhpNum, uint32 rwNhpIndex1, uint8 rbHashNum)\n{\nuint8 rbTmpCnt;\nuint8 rbTmpIndex;\nuint32 rwCheckData;\nmove rwFuncRtnData.Data0 = E_GETWLR_NHP_OK;\ncmp cc0 = rbNhpNum, 1; //超过1个下一跳，循环查找可用的TB，如果遍历到最后一个TB，则不需要判断状态，直接发送\nif (cc0.gt) {\nmove rbTmpIndex = rbHashNum;\n} else {\nmove rbTmpIndex = 0;\n}\nmove rbTmpCnt = 0;\nLOOP_GET_WLRTB:\ncmp cc0 = rbTmpCnt, rbNhpNum;\nif (cc0.lt) {\nsll rwNhpIndex1 = rwNhpIndex1, 1;\nUpfReadCurNhp(rwNhpIndex1[:24], rbTmpIndex); //此处只能调用该函数，后续流程使用了g_rsNhp的...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 88, "entity_name": "IpuGetWlrNhp", "code_range": {"start_line": 88, "end_line": 149}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#include \"ipu_priv.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 1, "entity_name": "import_0", "code_range": {"start_line": 1, "end_line": 1}, "metadata": {"import_type": "include"}}, {"content": "#include \"desc_egress_instance.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 2, "entity_name": "import_1", "code_range": {"start_line": 2, "end_line": 2}, "metadata": {"import_type": "include"}}, {"content": "#include \"desc_common_instance.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 3, "entity_name": "import_2", "code_range": {"start_line": 3, "end_line": 3}, "metadata": {"import_type": "include"}}, {"content": "#include \"desc_egress.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 4, "entity_name": "import_3", "code_range": {"start_line": 4, "end_line": 4}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_causeid_ip.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 5, "entity_name": "import_4", "code_range": {"start_line": 5, "end_line": 5}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 6, "entity_name": "import_5", "code_range": {"start_line": 6, "end_line": 6}, "metadata": {"import_type": "include"}}, {"content": "#include \"type_def_str.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 7, "entity_name": "import_6", "code_range": {"start_line": 7, "end_line": 7}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_func.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 8, "entity_name": "import_7", "code_range": {"start_line": 8, "end_line": 8}, "metadata": {"import_type": "include"}}, {"content": "#include \"lb_res_def.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 9, "entity_name": "import_8", "code_range": {"start_line": 9, "end_line": 9}, "metadata": {"import_type": "include"}}, {"content": "#include \"lb_pkthdr.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 10, "entity_name": "import_9", "code_range": {"start_line": 10, "end_line": 10}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_np2cpu.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 11, "entity_name": "import_10", "code_range": {"start_line": 11, "end_line": 11}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_cpu2np.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 12, "entity_name": "import_11", "code_range": {"start_line": 12, "end_line": 12}, "metadata": {"import_type": "include"}}, {"content": "#include \"outintf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 13, "entity_name": "import_12", "code_range": {"start_line": 13, "end_line": 13}, "metadata": {"import_type": "include"}}, {"content": "#include \"neon_comm.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 14, "entity_name": "import_13", "code_range": {"start_line": 14, "end_line": 14}, "metadata": {"import_type": "include"}}, {"content": "#include \"lb_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 15, "entity_name": "import_14", "code_range": {"start_line": 15, "end_line": 15}, "metadata": {"import_type": "include"}}, {"content": "#include \"pae_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 16, "entity_name": "import_15", "code_range": {"start_line": 16, "end_line": 16}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 17, "entity_name": "import_16", "code_range": {"start_line": 17, "end_line": 17}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_headr.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 18, "entity_name": "import_17", "code_range": {"start_line": 18, "end_line": 18}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_util.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 19, "entity_name": "import_18", "code_range": {"start_line": 19, "end_line": 19}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_resdef.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 20, "entity_name": "import_19", "code_range": {"start_line": 20, "end_line": 20}, "metadata": {"import_type": "include"}}, {"content": "#include \"lb_intf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 21, "entity_name": "import_20", "code_range": {"start_line": 21, "end_line": 21}, "metadata": {"import_type": "include"}}, {"content": "#include \"except_util.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 22, "entity_name": "import_21", "code_range": {"start_line": 22, "end_line": 22}, "metadata": {"import_type": "include"}}, {"content": "#include \"frame_hdr_ngsf.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 23, "entity_name": "import_22", "code_range": {"start_line": 23, "end_line": 23}, "metadata": {"import_type": "include"}}, {"content": "uint8    rbTtlDec;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 36, "entity_name": "rbTtlDec", "code_range": {"start_line": 36, "end_line": 36}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16   rhOldValue;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 37, "entity_name": "rhOldValue", "code_range": {"start_line": 37, "end_line": 37}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8    rbTtlDec;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 61, "entity_name": "rbTtlDec", "code_range": {"start_line": 61, "end_line": 61}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbTmpCnt;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 90, "entity_name": "rbTmpCnt", "code_range": {"start_line": 90, "end_line": 90}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbTmpIndex;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 91, "entity_name": "rbTmpIndex", "code_range": {"start_line": 91, "end_line": 91}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 rwCheckData;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 92, "entity_name": "rwCheckData", "code_range": {"start_line": 92, "end_line": 92}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 rwNhpIndex1;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 194, "entity_name": "rwNhpIndex1", "code_range": {"start_line": 194, "end_line": 194}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbNhpNum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 195, "entity_name": "rbNhpNum", "code_range": {"start_line": 195, "end_line": 195}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbHashNum;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 196, "entity_name": "rbHashNum", "code_range": {"start_line": 196, "end_line": 196}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16 rhTemp;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 197, "entity_name": "rhTemp", "code_range": {"start_line": 197, "end_line": 197}, "metadata": {"variable_type": "declaration"}}, {"content": "uint32 rwPktTheoryLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 198, "entity_name": "rwPktTheoryLen", "code_range": {"start_line": 198, "end_line": 198}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbL3Stake;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 199, "entity_name": "rbL3Stake", "code_range": {"start_line": 199, "end_line": 199}, "metadata": {"variable_type": "declaration"}}, {"content": "uint16 rsResult;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 202, "entity_name": "rsResult", "code_range": {"start_line": 202, "end_line": 202}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8 rbExtAttribLen;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 309, "entity_name": "rbExtAttribLen", "code_range": {"start_line": 309, "end_line": 309}, "metadata": {"variable_type": "declaration"}}, {"content": "uint8  rbTemp;", "context_type": "variable_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_translb.asm", "line_number": 441, "entity_name": "rbTemp", "code_range": {"start_line": 441, "end_line": 441}, "metadata": {"variable_type": "declaration"}}, {"content": "inline SEARCH_NDH_DIP(COMMON_128BIT_DATA_S RESP_DATA, uint16 VlanId, uint16 PortInfo, IPADDR_V6_S rsDipv6)\n{\n(NDH_KEY_S)     k2b     ksNdhKey;\nmove ksSeEmReq = GET_SE_PROFILE(ARPV6_ILA_TID);\nmove ksNdhKey.Tid = 8;\nmove ksNdhKey.Ovid = VlanId[:12];\nmove ksNdhKey.OportInfo = PortInfo;\nmove ksNdhKey.NhipPart1 = rsDipv6.Part0;\nmove ksNdhKey.NhipPart2 = rsDipv6.Part1;\nmove ksNdhKey.NhipPart3 = rsDipv6.Part2;\nmove ksNdhKey.NhipPart4 = rsDipv6.Part3;\nSE_EM_SEARCH_RSP_128BIT(RESP_DATA, ksSeEmReq, ksNdhK...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 20, "entity_name": "SEARCH_NDH_DIP", "code_range": {"start_line": 20, "end_line": 34}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline READ_IPV6_REDIRCT_DIP(RE_S RespData, uint24 ReIndex)\n{\nmove ksLmemReadReq.Profile = GET_MEM_PROFILE(LNS_SESSION_TBL_BASE);\nmovezr ksLmemReadReq.TableIndex = ReIndex;\nLMEM_READ_128BIT(RespData, GET_MEM_COP_ID(LNS_SESSION_TBL_BASE), ksLmemReadReq);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 37, "entity_name": "READ_IPV6_REDIRCT_DIP", "code_range": {"start_line": 37, "end_line": 42}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline READ_DSCPMAP_TBL(PHB_S RESP_DATA, uint8 dscp)\n{\nmove ksLmemReadReq.Profile = GET_MEM_PROFILE(IPU_DSCPMAP_TBL_BASE);\nadd ksLmemReadReq.TableIndex[:16] = dscp, GET_MEM_SUB_OFFSET(IPU_DSCPMAP_TBL_BASE);\nLMEM_READ_64BIT(RESP_DATA, GET_MEM_COP_ID(IPU_DSCPMAP_TBL_BASE), ksLmemReadReq);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 44, "entity_name": "READ_DSCPMAP_TBL", "code_range": {"start_line": 44, "end_line": 49}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPSEC_FLEX_ACLV4(uint32 RESP_DATA, uint16 VrfId, uint32 DstIP, uint32 SrcIp, uint32 PolicyGroupIndex)\n{\n(IPSEC_FLEX_ACLV4_KEY_S)   k0b   rsIoKey;\nmove rsIoKey.Tid = IPSEC_160BIT_ILA_TID[:4];\nmove rsIoKey.SubTid = IPSEC_160BIT_ILA_SUBTID[:4];\nmove rsIoKey.VrfId = VrfId;\nmove rsIoKey.DstIP = DstIP;\nmove rsIoKey.SrcIp = SrcIp;\nmove rsIoKey.PolicyGroupIndex = PolicyGroupIndex;\nCE_AD_LOOKUP_LMGP0_160BIT_RETURN32BIT(RESP_DATA, rsIoKey);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 55, "entity_name": "SEARCH_IPSEC_FLEX_ACLV4", "code_range": {"start_line": 55, "end_line": 66}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPSEC_FLEX_ACLV6(uint32 RESP_DATA, uint16 VrfId, uint32 DstIpPart0, uint32 DstIpPart1, uint32 DstIpPart2,\nuint32 DstIpPart3, uint32 SrcIpPart0, uint32 SrcIpPart1, uint32 SrcIpPart2, uint32 SrcIpPart3,\nuint32 PolicyGroupIndex)\n{\n(IPSEC_FLEX_ACLV6_KEY1_S)   k0b   rsIoKey1;\n(IPSEC_FLEX_ACLV6_KEY2_S)   k0b   rsIoKey2;\nmove rsIoKey1.Tid = IPSEC_320BIT_ILA_TID[:4];\nmove rsIoKey1.SubTid = IPSEC_320BIT_ILA_SUBTID[:4];\nmove rsIoKey1.VrfId = VrfId;\nmove rsIoKey1.DstIpPart0 = DstIpPart0;\nmove...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 72, "entity_name": "SEARCH_IPSEC_FLEX_ACLV6", "code_range": {"start_line": 72, "end_line": 94}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPSEC_LOCALIP(COMMON_192BIT_DATA_S RESP_DATA, uint16 VrfId, uint32 SrcIp, uint32 DstIP)\n{\n(IPSEC_FLEX_LOCALIP_KEY_S)   k2b   rsIoAddr;\nmove ksSeEmReq = GET_SE_PROFILE(TCE_IPSEC_LOCAL_IP_TID);\nmove rsIoAddr.SubTid = TCE_IPSEC_LOCAL_IP_SUBTID[:4];\nmove rsIoAddr.VrfId = VrfId;\nmove rsIoAddr.SrcIp = SrcIp;\nmove rsIoAddr.DstIP = DstIP;\nSE_EM_SEARCH_RSP_192BIT(RESP_DATA, ksSeEmReq, rsIoAddr);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 100, "entity_name": "SEARCH_IPSEC_LOCALIP", "code_range": {"start_line": 100, "end_line": 110}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPSEC_TNL(COMMON_192BIT_DATA_S RESP_DATA, uint32 LmtIdx)\n{\n(IPSEC_FLEX_TNLINFO_KEY_S)   k2b   rsIoAddr;\nmove ksSeEmReq = GET_SE_PROFILE(TCE_IPSEC_TUNNEL_TID);\nmove rsIoAddr.SubTid = TCE_IPSEC_TUNNEL_SUBTID[:4];\nmove rsIoAddr.LmtIdx = LmtIdx;\nSE_EM_SEARCH_RSP_192BIT(RESP_DATA, ksSeEmReq, rsIoAddr);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 116, "entity_name": "SEARCH_IPSEC_TNL", "code_range": {"start_line": 116, "end_line": 124}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPU_PORT_MAC(COMMON_128BIT_DATA_S RESP_DATA, uint16 DMacH16, uint32 DMacL32)\n{\n(IPU_PORT_MAC_KEY_S)   k2b   rsIoAddr;\nmove ksSeEmReq = GET_SE_PROFILE(TCE_PORT_MAC_TID);\nmove rsIoAddr.SubTid = TCE_PORT_MAC_SUBTID[:6];\nmove rsIoAddr.DMacH16 = DMacH16;\nmove rsIoAddr.DMacL32 = DMacL32;\nSE_EM_SEARCH_RSP_128BIT(RESP_DATA, ksSeEmReq, rsIoAddr);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 130, "entity_name": "SEARCH_IPU_PORT_MAC", "code_range": {"start_line": 130, "end_line": 139}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_IPV4(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIp, uint32 PeerIp,\nuint16 SrcPort, uint16 DstPort, uint16 VpnId)\n{\nmovezr RESP_DATA = 0x6;\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 146, "entity_name": "SEARCH_IPTRACE_IPV4", "code_range": {"start_line": 146, "end_line": 150}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_IPV6(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,\nuint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,\nuint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort, uint16 VpnId, uint4 Direction)\n{\nmovezr RESP_DATA = 0x6;\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 152, "entity_name": "SEARCH_IPTRACE_IPV6", "code_range": {"start_line": 152, "end_line": 157}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_ARP(uint32 RESP_DATA, uint16 VpnId, uint4 Direction)\n{\nmovezr RESP_DATA = 0x6;\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 159, "entity_name": "SEARCH_IPTRACE_ARP", "code_range": {"start_line": 159, "end_line": 162}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_IPV4(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIp, uint32 PeerIp,\nuint16 SrcPort, uint16 DstPort, uint16 VpnId)\n{\nSEARCH_IPTRACE(RESP_DATA, L4Protol, ETH_TYPE_IP4,\nLocalIp, zero, zero, zero, PeerIp, zero, zero, zero,\nSrc<PERSON>ort, DstPort, VpnId, zero[:4]);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 165, "entity_name": "SEARCH_IPTRACE_IPV4", "code_range": {"start_line": 165, "end_line": 171}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_IPV6(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,\nuint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,\nuint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort, uint16 VpnId, uint4 Direction)\n{\nSEARCH_IPTRACE(RESP_DATA, L4Protol, ETH_TYPE_IP6,\nLocalIPPart0, LocalIPPart1, LocalIPPart2, LocalIPPart3, PeerIPPart0, PeerIPPart1, PeerIPPart2, PeerIPPart3,\nSrcPort, DstPort, VpnId, Direction);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 173, "entity_name": "SEARCH_IPTRACE_IPV6", "code_range": {"start_line": 173, "end_line": 180}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_ARP(uint32 RESP_DATA, uint16 VpnId, uint4 Direction)\n{\nSEARCH_IPTRACE(RESP_DATA, zero[:8], ETH_TYPE_ARP,\nzero, zero, zero, zero, zero, zero, zero, zero,\nzero[:16], zero[:16], VpnId, Direction);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 182, "entity_name": "SEARCH_IPTRACE_ARP", "code_range": {"start_line": 182, "end_line": 187}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE(uint32 RESP_DATA, uint8 L4Protol, uint16 L3Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,\nuint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,\nuint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort, uint16 VpnId, uint4 Direction)\n{\nSEARCH_IPTRACE_PART1(RESP_DATA, L4Protol, L3Protol,\nLocalIPPart0, LocalIPPart1, LocalIPPart2, LocalIPPart3, PeerIPPart0, PeerIPPart1, PeerIPPart2, PeerIPPart3,\nSrcPort, DstPort);\nSEARCH_IPTRACE_PART...", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 191, "entity_name": "SEARCH_IPTRACE", "code_range": {"start_line": 191, "end_line": 199}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_PART1(uint32 RESP_DATA, uint8 L4Protol, uint16 L3Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,\nuint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,\nuint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort)\n{\n(IPTRC_KEY1_S)   k0b  rsIoKey1;\n(IPTRC_KEY2_S)   k0b  rsIoKey2;\nmove rsIoKey1.Tid = IP_TRACE_640BIT_ILA_TID[:4];\nmove rsIoKey1.SubTid = 0;\nmove rsIoKey1.L4Protol = L4Protol;\nmove rsIoKey1.L3Protol = L3Protol;\nmove rsIoKey1....", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 201, "entity_name": "SEARCH_IPTRACE_PART1", "code_range": {"start_line": 201, "end_line": 225}, "metadata": {"return_type": null, "parameters": []}}, {"content": "inline SEARCH_IPTRACE_PART2(uint32 RESP_DATA, uint16 VpnId, uint4 Direction)\n{\n(IPTRC_KEY3_S)   k0b   rsIoKey3;\n(IPTRC_KEY4_S)   k0b   rsIoKey4;\nmove rsIoKey3.Tid1 = IP_TRACE_640BIT_ILA_TID[:4];\nmove rsIoKey3.SubTid1 = 0;\nmove rsIoKey3.VpnId = VpnId;\nmove rsIoKey3.Direction = Direction;\nCE_AD_LOOKUP_LMGP0_640BIT_RETURN32BIT(RESP_DATA, rsIoKey3);\nCE_AD_LOOKUP_LMGP0_640BIT_RETURN32BIT(RESP_DATA, rsIoKey4);\n}", "context_type": "function_definition", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 227, "entity_name": "SEARCH_IPTRACE_PART2", "code_range": {"start_line": 227, "end_line": 239}, "metadata": {"return_type": null, "parameters": []}}, {"content": "#include \"type_def.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 1, "entity_name": "import_0", "code_range": {"start_line": 1, "end_line": 1}, "metadata": {"import_type": "include"}}, {"content": "#include \"table_access.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 2, "entity_name": "import_1", "code_range": {"start_line": 2, "end_line": 2}, "metadata": {"import_type": "include"}}, {"content": "#include \"upf_common_symbol.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 3, "entity_name": "import_2", "code_range": {"start_line": 3, "end_line": 3}, "metadata": {"import_type": "include"}}, {"content": "#include \"ipu_tbl_key_def.h\"", "context_type": "import_statement", "file_path": "/Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer/examples/np/ipu_tbl_access.asm", "line_number": 4, "entity_name": "import_3", "code_range": {"start_line": 4, "end_line": 4}, "metadata": {"import_type": "include"}}]