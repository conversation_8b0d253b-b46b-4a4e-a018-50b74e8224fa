{"inputs": ["[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\n<hole>\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n} [SEP] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n} [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\n<hole>\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n} [SEP] uint8   NextHeader; [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\n<hole>\n} else {\nmove rsInfo.bQos = qos;\n} [SEP] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n} [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\n<hole>\n} else {\nmove rsInfo.bQos = qos;\n} [SEP] #define IPV6_EXT_NO_NEXT_HEADER 0x3B  [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n<hole>\nmove rsInfo.bQos = qos;\n} [SEP] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n} [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n<hole>\nmove rsInfo.bQos = qos;\n} [SEP] uint8  rbHdrVersion; [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\n<hole>\n} [SEP] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n} [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\n<hole>\n} [SEP] struct IPSEC_FLEX_LOCALIP_KEY_S {\n    uint4 SubTid;\n    uint4 Rsvd0;\n    uint24 Rsvd1;\n    uint16 Rsvd2;\n    uint16 VrfId;\n    uint32 SrcIp;\n    uint32 DstIP;\n}; [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n<hole>\n [SEP] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\n{\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n}\n} [SEP]", "[CLS] inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)\nbitcmp cc0 = flag, ONE_BIT_MASK;\nif (cc0.z) {\nmove rsInfo.bQos = BE;\n} else {\nmove rsInfo.bQos = qos;\n<hole>\n [SEP] uint8   Type; [SEP]"], "labels": [1, 0, 1, 0, 1, 0, 1, 0, 1, 0], "metadata": {}}