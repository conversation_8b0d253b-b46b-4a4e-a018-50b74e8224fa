"""
测试重构后的SFT模块

测试 core/data_gen/sft 模块的功能
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.data_gen.sft import (ContextNode, DataQualityValidator,
                               DependencyEdge, DependencyGraph, DependencyNode,
                               EnhancedStatementMaskData, RealNPAnalyzer,
                               RealStatementMaskData, SFTDataGenerator)


class TestSFTModule(unittest.TestCase):
    """测试SFT模块"""
    
    def setUp(self):
        """测试前准备"""
        self.test_project_dir = "examples/np"
        self.test_np_file = "examples/np/ipu_usertrace.asm"
    
    def test_models_creation(self):
        """测试数据模型创建"""
        # 测试ContextNode
        context_node = ContextNode(
            node_type="function",
            name="test_func",
            filepath="test.c",
            code="void test_func() { }",
            scope="global",
            reference_line=1
        )
        self.assertEqual(context_node.name, "test_func")
        self.assertEqual(context_node.node_type, "function")
        
        # 测试DependencyNode
        dep_node = DependencyNode(id="fn_test", type="function")
        self.assertEqual(dep_node.id, "fn_test")
        self.assertEqual(dep_node.type, "function")
        
        # 测试DependencyEdge
        dep_edge = DependencyEdge(source="fn_a", target="fn_b", relation="calls")
        self.assertEqual(dep_edge.source, "fn_a")
        self.assertEqual(dep_edge.target, "fn_b")
        self.assertEqual(dep_edge.relation, "calls")
        
        # 测试DependencyGraph
        dep_graph = DependencyGraph(nodes=[dep_node], edges=[dep_edge])
        self.assertEqual(len(dep_graph.nodes), 1)
        self.assertEqual(len(dep_graph.edges), 1)
    
    def test_enhanced_statement_mask_data(self):
        """测试增强语句遮盖数据"""
        # 创建测试数据
        context_nodes = [
            ContextNode(
                node_type="function",
                name="test_func",
                filepath="test.c",
                code="void test_func() { }",
                scope="global",
                reference_line=1
            )
        ]
        
        dependency_graph = DependencyGraph(
            nodes=[DependencyNode(id="fn_test", type="function")],
            edges=[]
        )
        
        enhanced_data = EnhancedStatementMaskData(
            before="void test_func() {\n",
            expected="    int x = 1;",
            after="\n}",
            context_nodes=context_nodes,
            dependency_graph=dependency_graph,
            metadata={
                "task_type": "code_completion",
                "strategy": "waterfall_sequential",
                "mask_level": "assignment",
                "function_name": "test_func",
                "filepath": "test.c",
                "is_control_flow": False,
                "comment_hint": False,
                "complexity_score": 0.5
            },
            context_list=["Function: test_func", "Strategy: waterfall_sequential"],
            statement_type="statement"
        )
        
        self.assertEqual(enhanced_data.before, "void test_func() {\n")
        self.assertEqual(enhanced_data.expected, "    int x = 1;")
        self.assertEqual(enhanced_data.after, "\n}")
        self.assertEqual(len(enhanced_data.context_nodes), 1)
        self.assertEqual(len(enhanced_data.dependency_graph.nodes), 1)
    
    def test_real_statement_mask_data(self):
        """测试真实语句遮盖数据"""
        real_data = RealStatementMaskData(
            context_list=["Function: test_func"],
            filepath="test.c",
            before="void test_func() {\n",
            expected="    int x = 1;",
            after="\n}",
            function_name="test_func",
            statement_type="statement",
            line_number=2,
            call_chain=[],
            related_structs=[],
            variable_references=["x"],
            complexity_score=0.5,
            ast_node_type="statement"
        )
        
        self.assertEqual(real_data.function_name, "test_func")
        self.assertEqual(real_data.filepath, "test.c")
        self.assertEqual(real_data.expected, "    int x = 1;")
        self.assertEqual(real_data.variable_references, ["x"])
    
    def test_data_quality_validator(self):
        """测试数据质量验证器"""
        validator = DataQualityValidator()
        
        # 测试有效数据
        valid_enhanced_data = EnhancedStatementMaskData(
            before="void test_func() {\n",
            expected="    int x = 1;",
            after="\n}",
            context_nodes=[ContextNode("function", "test", "test.c", "code")],
            dependency_graph=DependencyGraph([DependencyNode("fn_test", "function")], []),
            metadata={
                "task_type": "code_completion",
                "strategy": "waterfall_sequential",
                "mask_level": "assignment",
                "function_name": "test_func",
                "filepath": "test.c",
                "is_control_flow": False,
                "comment_hint": False,
                "complexity_score": 0.5
            },
            context_list=["Function: test_func"],
            statement_type="statement"
        )
        
        self.assertTrue(validator.validate_enhanced_mask_data(valid_enhanced_data))
        
        # 测试无效数据（before为空）
        invalid_enhanced_data = EnhancedStatementMaskData(
            before="",
            expected="    int x = 1;",
            after="\n}",
            context_nodes=[ContextNode("function", "test", "test.c", "code")],
            dependency_graph=DependencyGraph([DependencyNode("fn_test", "function")], []),
            metadata={
                "task_type": "code_completion",
                "strategy": "waterfall_sequential",
                "mask_level": "assignment",
                "function_name": "test_func",
                "filepath": "test.c",
                "is_control_flow": False,
                "comment_hint": False,
                "complexity_score": 0.5
            },
            context_list=["Function: test_func"],
            statement_type="statement"
        )
        
        self.assertFalse(validator.validate_enhanced_mask_data(invalid_enhanced_data))
    
    @patch('core.data_gen.sft.analyzer.RepositoryIndex')
    @patch('core.data_gen.sft.analyzer.CodeGraph')
    @patch('core.data_gen.sft.analyzer.ASTParserFactory')
    def test_real_np_analyzer_initialization(self, mock_factory, mock_graph, mock_repo):
        """测试RealNPAnalyzer初始化"""
        # 模拟依赖
        mock_factory.create_parser.return_value = Mock()
        mock_graph.return_value = Mock()
        mock_repo.return_value = Mock()
        
        analyzer = RealNPAnalyzer(self.test_project_dir)
        
        self.assertEqual(analyzer.project_path, self.test_project_dir)
        self.assertIsNotNone(analyzer.repo_index)
        self.assertIsNotNone(analyzer.code_graph)
        self.assertIsNotNone(analyzer.parser)
    
    def test_sft_data_generator_initialization(self):
        """测试SFTDataGenerator初始化"""
        # 创建模拟分析器
        mock_analyzer = Mock()
        
        generator = SFTDataGenerator(mock_analyzer)
        
        self.assertEqual(generator.analyzer, mock_analyzer)
    
    def test_validation_report_generation(self):
        """测试验证报告生成"""
        validator = DataQualityValidator()
        
        validation_result = {
            'valid': True,
            'total_samples': 10,
            'valid_samples': 10,
            'invalid_samples': 0,
            'warnings': []
        }
        
        report = validator.generate_validation_report(validation_result)
        
        self.assertIn("SFT数据验证报告", report)
        self.assertIn("总样本数: 10", report)
        self.assertIn("验证状态: 通过", report)


class TestSFTModuleIntegration(unittest.TestCase):
    """测试SFT模块集成功能"""
    
    def setUp(self):
        """测试前准备"""
        self.test_project_dir = "examples/np"
    
    @patch('core.data_gen.sft.analyzer.RepositoryIndex')
    @patch('core.data_gen.sft.analyzer.CodeGraph')
    @patch('core.data_gen.sft.analyzer.ASTParserFactory')
    def test_full_workflow(self, mock_factory, mock_graph, mock_repo):
        """测试完整工作流程"""
        # 模拟依赖
        mock_factory.create_parser.return_value = Mock()
        mock_graph.return_value = Mock()
        mock_repo.return_value = Mock()
        
        # 创建分析器
        analyzer = RealNPAnalyzer(self.test_project_dir)
        
        # 模拟分析结果
        mock_func = Mock()
        mock_func.name = 'test_function'
        mock_func.filepath = 'test.c'
        mock_func.code = 'void test_function() { int x = 1; }'
        mock_func.signature = Mock()
        mock_func.signature.parameters = []
        
        analyzer._functions_cache = {
            'test.c': [mock_func]
        }
        
        # 创建生成器
        generator = SFTDataGenerator(analyzer)
        
        # 测试数据生成（使用较小的max_samples）
        try:
            enhanced_data = generator.generate_enhanced_sft_data(max_samples=1)
            # 由于是模拟数据，可能不会生成实际数据，但应该不会抛出异常
            self.assertIsInstance(enhanced_data, list)
        except Exception as e:
            # 如果生成失败，这是预期的，因为我们使用的是模拟数据
            self.assertIsInstance(e, Exception)
    
    def test_module_imports(self):
        """测试模块导入"""
        # 测试所有主要组件都可以正确导入
        try:
            from core.data_gen.sft import (ContextNode, DataQualityValidator,
                                           DependencyEdge, DependencyGraph,
                                           DependencyNode,
                                           EnhancedStatementMaskData,
                                           RealNPAnalyzer,
                                           RealStatementMaskData,
                                           SFTDataGenerator)
            self.assertTrue(True)  # 导入成功
        except ImportError as e:
            self.fail(f"模块导入失败: {e}")


if __name__ == '__main__':
    unittest.main() 