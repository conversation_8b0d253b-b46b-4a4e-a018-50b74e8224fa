"""
Test Struct Parser

测试结构体解析功能
"""

import os
import tempfile
import unittest
from pathlib import Path

from core.ast_parser import ASTParserFactory
from models.struct import Struct, StructField, StructRelation, StructUsage


class TestStructParser(unittest.TestCase):
    """测试结构体解析器"""

    def setUp(self):
        """设置测试环境"""
        self.parser = ASTParserFactory.create("c")
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_test_file(self, content: str, filename: str = "test.c") -> str:
        """创建测试文件"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path

    def test_extract_basic_struct(self):
        """测试基本结构体提取"""
        code = """
struct Person {
    char name[50];
    int age;
    float height;
};
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        self.assertEqual(struct.name, "Person")
        self.assertEqual(len(struct.fields), 3)
        self.assertFalse(struct.is_typedef)
        
        # 检查字段
        field_names = [field.name for field in struct.fields]
        self.assertIn("name", field_names)
        self.assertIn("age", field_names)
        self.assertIn("height", field_names)

    def test_extract_typedef_struct(self):
        """测试typedef结构体提取"""
        code = """
typedef struct {
    int x;
    int y;
} Point;
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        self.assertTrue(struct.is_typedef)
        self.assertEqual(struct.typedef_name, "Point")
        self.assertEqual(len(struct.fields), 2)

    def test_extract_named_typedef_struct(self):
        """测试命名typedef结构体提取"""
        code = """
typedef struct Point {
    int x;
    int y;
} Point_t;
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        self.assertTrue(struct.is_typedef)
        self.assertEqual(struct.name, "Point")
        self.assertEqual(struct.typedef_name, "Point_t")

    def test_extract_struct_with_pointers(self):
        """测试包含指针的结构体提取"""
        code = """
struct Node {
    int data;
    struct Node* next;
    char* name;
};
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        
        # 检查指针字段
        for field in struct.fields:
            if field.name == "next":
                self.assertTrue(field.is_pointer)
                self.assertEqual(field.type_name, "struct Node")
            elif field.name == "name":
                self.assertTrue(field.is_pointer)
                self.assertEqual(field.type_name, "char")

    def test_extract_struct_with_arrays(self):
        """测试包含数组的结构体提取"""
        code = """
struct Matrix {
    int data[3][3];
    char name[20];
};
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        
        # 检查数组字段
        for field in struct.fields:
            if field.name == "data":
                self.assertTrue(field.is_array)
            elif field.name == "name":
                self.assertTrue(field.is_array)

    def test_extract_struct_relations(self):
        """测试结构体关系提取"""
        code = """
struct Address {
    char street[100];
    char city[50];
};

struct Person {
    char name[50];
    struct Address addr;
    struct Person* spouse;
};
        """
        file_path = self.create_test_file(code)
        
        relations = self.parser.extract_struct_relations(file_path)
        
        # 应该找到Person和Address的关系
        self.assertGreater(len(relations), 0)
        
        # 检查关系类型
        relation_types = [rel.relation_type for rel in relations]
        self.assertIn("composition", relation_types)

    def test_extract_struct_usages(self):
        """测试结构体使用提取"""
        code = """
struct Point {
    int x;
    int y;
};

void print_point(struct Point p) {
    printf("x: %d, y: %d\\n", p.x, p.y);
}

int main() {
    struct Point point1 = {10, 20};
    Point_t point2;
    printf("Size: %zu\\n", sizeof(struct Point));
    return 0;
}
        """
        file_path = self.create_test_file(code)
        
        usages = self.parser.extract_struct_usages(file_path)
        
        self.assertGreater(len(usages), 0)
        
        # 检查使用类型
        usage_types = [usage.usage_type for usage in usages]
        self.assertIn("parameter", usage_types)
        self.assertIn("declaration", usage_types)
        self.assertIn("sizeof", usage_types)

    def test_extract_field_access(self):
        """测试字段访问提取"""
        code = """
struct Rectangle {
    int width;
    int height;
};

int get_area(struct Rectangle rect) {
    return rect.width * rect.height;
}
        """
        file_path = self.create_test_file(code)
        
        usages = self.parser.extract_struct_usages(file_path)
        
        # 应该找到字段访问
        field_access_usages = [u for u in usages if u.usage_type == "field_access"]
        self.assertGreater(len(field_access_usages), 0)

    def test_extract_complex_struct(self):
        """测试复杂结构体提取"""
        code = """
struct Employee {
    char name[100];
    int id;
    struct {
        int year;
        int month;
        int day;
    } birth_date;
    struct Employee* manager;
    float salary;
};
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        self.assertEqual(struct.name, "Employee")
        self.assertEqual(len(struct.fields), 5)

    def test_extract_multiple_structs(self):
        """测试多个结构体提取"""
        code = """
struct Point {
    int x;
    int y;
};

struct Circle {
    struct Point center;
    float radius;
};

struct Rectangle {
    struct Point top_left;
    struct Point bottom_right;
};
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 3)
        
        struct_names = [s.name for s in structs]
        self.assertIn("Point", struct_names)
        self.assertIn("Circle", struct_names)
        self.assertIn("Rectangle", struct_names)

    def test_extract_struct_with_comments(self):
        """测试带注释的结构体提取"""
        code = """
struct Config {
    int port;           /* 端口号 */
    char* hostname;     // 主机名
    bool debug;         /* 调试模式 */
};
        """
        file_path = self.create_test_file(code)
        
        structs = self.parser.extract_structs(file_path)
        
        self.assertEqual(len(structs), 1)
        struct = structs[0]
        self.assertEqual(len(struct.fields), 3)

    def test_extract_struct_in_header_file(self):
        """测试头文件中的结构体提取"""
        code = """
#ifndef PERSON_H
#define PERSON_H

struct Person {
    char name[50];
    int age;
};

typedef struct Person* PersonPtr;

#endif
        """
        file_path = self.create_test_file(code, "person.h")
        
        structs = self.parser.extract_structs(file_path)
        relations = self.parser.extract_struct_relations(file_path)
        
        self.assertEqual(len(structs), 1)
        self.assertGreater(len(relations), 0)


if __name__ == '__main__':
    unittest.main() 