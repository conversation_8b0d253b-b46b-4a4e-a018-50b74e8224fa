#!/usr/bin/env python3
"""
Embedding训练数据生成测试

测试embedding训练数据生成模块的各项功能
"""

import os
import sys
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.embedding import (CandidateSampler, ContextCandidate,
                                     ContextExtractor, ContextType,
                                     EmbeddingDataGenerator, EmbeddingPair,
                                     EmbeddingTrainingData, QueryContext)


class TestEmbeddingGeneration(unittest.TestCase):
    """Embedding训练数据生成测试类"""

    def setUp(self):
        """测试前准备"""
        self.test_dir = os.path.abspath("examples/np")
        
        # 跳过测试如果目录不存在
        if not os.path.exists(self.test_dir):
            self.skipTest(f"测试目录不存在: {self.test_dir}")

    def test_context_extractor(self):
        """测试上下文提取器"""
        print("🔍 测试上下文提取器")
        
        # 创建提取器
        extractor = ContextExtractor()
        
        # 从examples/np目录提取
        candidates = extractor.extract_from_directory(self.test_dir)
        
        print(f"从 {self.test_dir} 提取到 {len(candidates)} 个候选上下文")
        
        # 验证提取结果
        self.assertIsInstance(candidates, list)
        
        # 显示前几个候选
        for i, candidate in enumerate(candidates[:5]):
            print(f"  {i+1}. {candidate.entity_name} ({candidate.context_type.value})")
            print(f"     文件: {candidate.file_path}")
            print(f"     内容: {candidate.content[:100]}...")
            print()
            
            # 验证候选对象
            self.assertIsInstance(candidate, ContextCandidate)
            self.assertIsInstance(candidate.entity_name, str)
            self.assertIsInstance(candidate.context_type, ContextType)
            self.assertIsInstance(candidate.file_path, str)
            self.assertIsInstance(candidate.content, str)
            self.assertGreater(len(candidate.content), 0)

    def test_candidate_sampler(self):
        """测试候选采样器"""
        print("🔍 测试候选采样器")
        
        # 创建采样器
        sampler = CandidateSampler()
        
        # 创建测试查询
        query = QueryContext(
            before="int x = 1;",
            after="return result;",
            expected="int result = add(x, 2);",
            file_path="test.c",
            function_name="test_func",
            line_number=10
        )
        
        # 验证查询对象
        self.assertIsInstance(query, QueryContext)
        self.assertIsInstance(query.before, str)
        self.assertIsInstance(query.after, str)
        self.assertIsInstance(query.expected, str)
        self.assertIsInstance(query.file_path, str)
        self.assertIsInstance(query.function_name, str)
        self.assertIsInstance(query.line_number, int)
        
        # 创建测试候选
        candidates = [
            ContextCandidate(
                content="int add(int a, int b) { return a + b; }",
                context_type=ContextType.FUNCTION_DEF,
                file_path="test.c",
                line_number=5,
                entity_name="add",
                code_range={"start_line": 5, "end_line": 7}
            ),
            ContextCandidate(
                content="int multiply(int a, int b) { return a * b; }",
                context_type=ContextType.FUNCTION_DEF,
                file_path="test.c",
                line_number=8,
                entity_name="multiply",
                code_range={"start_line": 8, "end_line": 10}
            ),
            ContextCandidate(
                content="struct Point { int x; int y; };",
                context_type=ContextType.STRUCT_DEF,
                file_path="test.c",
                line_number=12,
                entity_name="Point",
                code_range={"start_line": 12, "end_line": 15}
            )
        ]
        
        # 采样正样本
        positive_candidates = sampler.sample_positive_candidates(query, candidates)
        print(f"正样本候选: {len(positive_candidates)} 个")
        for candidate in positive_candidates:
            print(f"  - {candidate.entity_name}")
        
        # 验证正样本
        self.assertIsInstance(positive_candidates, list)
        
        # 采样负样本
        negative_candidates = sampler.sample_negative_candidates(query, candidates, positive_candidates)
        print(f"负样本候选: {len(negative_candidates)} 个")
        for candidate in negative_candidates:
            print(f"  - {candidate.entity_name}")
        
        # 验证负样本
        self.assertIsInstance(negative_candidates, list)
        
        # 创建训练对
        pairs = sampler.create_training_pairs(query, positive_candidates, negative_candidates)
        print(f"训练对: {len(pairs)} 个")
        
        # 验证训练对
        self.assertIsInstance(pairs, list)
        for pair in pairs:
            self.assertIsInstance(pair, EmbeddingPair)

    def test_embedding_generator(self):
        """测试embedding生成器"""
        print("🔍 测试Embedding生成器")
        
        # 创建生成器
        generator = EmbeddingDataGenerator()
        
        try:
            # 生成embedding训练数据
            embedding_data = generator.generate_from_project(self.test_dir, max_samples=5)
            
            print(f"生成 {len(embedding_data.pairs)} 个训练对")
            
            # 验证生成结果
            self.assertIsInstance(embedding_data, EmbeddingTrainingData)
            self.assertIsInstance(embedding_data.pairs, list)
            
            # 显示前几个训练对
            for i, pair in enumerate(embedding_data.pairs[:3]):
                print(f"训练对 {i+1}:")
                print(f"  查询: {pair.query.function_name}")
                print(f"  正样本: {pair.positive.entity_name} ({pair.positive.context_type.value})")
                print(f"  负样本: {pair.negative.entity_name} ({pair.negative.context_type.value})")
                print(f"  相似度分数: {pair.similarity_score}")
                print()
                
                # 验证训练对
                self.assertIsInstance(pair, EmbeddingPair)
                self.assertIsInstance(pair.query, QueryContext)
                self.assertIsInstance(pair.positive, ContextCandidate)
                self.assertIsInstance(pair.negative, ContextCandidate)
                self.assertIsInstance(pair.similarity_score, float)
            
            # 生成rerank数据
            rerank_data = generator.generate_rerank_data(embedding_data)
            print(f"生成 {len(rerank_data.inputs)} 个rerank样本")
            
            # 验证rerank数据
            self.assertIsInstance(rerank_data.inputs, list)
            if rerank_data.labels:
                print(f"正样本比例: {sum(rerank_data.labels) / len(rerank_data.labels):.2f}")
                self.assertIsInstance(rerank_data.labels, list)
            else:
                print("正样本比例: 0.00")
            
            # 保存结果
            generator.save_results(embedding_data, rerank_data)
            
        except Exception as e:
            print(f"生成过程中出错: {e}")
            self.fail(f"Embedding生成失败: {e}")

    def test_data_models(self):
        """测试数据模型"""
        print("🔍 测试数据模型")
        
        # 测试QueryContext
        query = QueryContext(
            before="int x = 1;",
            after="return result;",
            expected="int result = add(x, 2);",
            file_path="test.c",
            function_name="test_func",
            line_number=10,
            instruction="计算两个数的和",
            env_info="C语言环境"
        )
        
        print("QueryContext测试:")
        print(f"  查询文本: {query.get_query_text()[:100]}...")
        print(f"  字典格式: {list(query.to_dict().keys())}")
        
        # 验证QueryContext
        self.assertIsInstance(query.get_query_text(), str)
        self.assertIsInstance(query.to_dict(), dict)
        
        # 测试ContextCandidate
        candidate = ContextCandidate(
            content="int add(int a, int b) { return a + b; }",
            context_type=ContextType.FUNCTION_DEF,
            file_path="test.c",
            line_number=5,
            entity_name="add",
            code_range={"start_line": 5, "end_line": 7}
        )
        
        print("\nContextCandidate测试:")
        print(f"  实体名称: {candidate.entity_name}")
        print(f"  上下文类型: {candidate.context_type.value}")
        print(f"  字典格式: {list(candidate.to_dict().keys())}")
        
        # 验证ContextCandidate
        self.assertIsInstance(candidate.entity_name, str)
        self.assertIsInstance(candidate.context_type, ContextType)
        self.assertIsInstance(candidate.to_dict(), dict)
        
        # 测试EmbeddingTrainingData
        training_data = EmbeddingTrainingData()
        training_data.add_pair(EmbeddingPair(
            query=query,
            positive=candidate,
            negative=candidate,  # 简化测试
            similarity_score=0.8
        ))
        
        print("\nEmbeddingTrainingData测试:")
        print(f"  训练对数量: {len(training_data.pairs)}")
        print(f"  字典格式: {list(training_data.to_dict().keys())}")
        
        # 验证EmbeddingTrainingData
        self.assertIsInstance(training_data.pairs, list)
        self.assertEqual(len(training_data.pairs), 1)
        self.assertIsInstance(training_data.to_dict(), dict)


def main():
    """主函数"""
    print("🚀 开始测试Embedding训练数据生成模块")
    print("=" * 80)
    
    # 运行测试
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main() 