#!/usr/bin/env python3
"""
编码处理测试

测试健壮的文件读取和编码处理功能
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.file_utils import (get_file_encoding_info, print_encoding_report,
                              read_file_safe,
                              read_file_with_encoding_detection)


class TestEncodingHandling(unittest.TestCase):
    """编码处理测试类"""

    def setUp(self):
        """测试前准备"""
        # 测试文件
        self.test_file = "examples/np/pkt_ipv6_hdr.h"
        
        # 跳过测试如果文件不存在
        if not os.path.exists(self.test_file):
            self.skipTest(f"测试文件不存在: {self.test_file}")

    def test_encoding_detection(self):
        """测试编码检测功能"""
        print("🔍 测试编码检测功能")
        
        # 获取编码信息
        encoding_info = get_file_encoding_info(self.test_file)
        
        # 验证编码信息
        self.assertIsInstance(encoding_info, dict)
        self.assertIn('encoding', encoding_info)
        self.assertIn('confidence', encoding_info)
        self.assertIn('is_valid', encoding_info)
        self.assertIn('file_size', encoding_info)
        
        print(f"编码信息: {encoding_info}")
        
        # 打印详细报告
        print_encoding_report(self.test_file)

    def test_file_reading(self):
        """测试文件读取功能"""
        print("\n📄 测试文件读取功能")
        
        try:
            # 使用健壮的文件读取
            content, detected_encoding = read_file_with_encoding_detection(self.test_file)
            
            print(f"✅ 文件读取成功")
            print(f"检测到的编码: {detected_encoding}")
            print(f"文件大小: {len(content)} 字符")
            print(f"前100字符: {content[:100]}...")
            
            # 验证结果
            self.assertIsInstance(content, str)
            self.assertIsInstance(detected_encoding, str)
            self.assertGreater(len(content), 0)
            
            # 验证内容是否为UTF-8
            try:
                utf8_bytes = content.encode('utf-8')
                print(f"✅ UTF-8编码验证成功")
            except Exception as e:
                print(f"❌ UTF-8编码验证失败: {e}")
                self.fail(f"UTF-8编码验证失败: {e}")
                
        except Exception as e:
            print(f"❌ 文件读取失败: {e}")
            self.fail(f"文件读取失败: {e}")

    def test_safe_file_reading(self):
        """测试安全文件读取功能"""
        print("\n🛡️ 测试安全文件读取功能")
        
        try:
            # 使用安全文件读取
            content = read_file_safe(self.test_file)
            
            print(f"✅ 安全文件读取成功")
            print(f"文件大小: {len(content)} 字符")
            print(f"前100字符: {content[:100]}...")
            
            # 验证结果
            self.assertIsInstance(content, str)
            self.assertGreater(len(content), 0)
            
            # 验证内容是否为UTF-8
            try:
                utf8_bytes = content.encode('utf-8')
                print(f"✅ UTF-8编码验证成功")
            except Exception as e:
                print(f"❌ UTF-8编码验证失败: {e}")
                self.fail(f"UTF-8编码验证失败: {e}")
                
        except Exception as e:
            print(f"❌ 安全文件读取失败: {e}")
            self.fail(f"安全文件读取失败: {e}")

    def test_gbk_file_creation_and_reading(self):
        """测试GBK文件创建和读取"""
        print("\n🇨🇳 测试GBK文件创建和读取")
        
        # 创建临时GBK文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='gbk') as f:
            gbk_content = "这是一个GBK编码的测试文件\n包含中文字符：你好世界！\n"
            f.write(gbk_content)
            temp_file = f.name
        
        try:
            print(f"创建临时GBK文件: {temp_file}")
            
            # 尝试直接使用UTF-8读取（应该失败）
            try:
                with open(temp_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"❌ 直接UTF-8读取应该失败，但成功了")
                self.fail("直接UTF-8读取应该失败")
            except UnicodeDecodeError:
                print(f"✅ 直接UTF-8读取失败（预期行为）")
            
            # 使用健壮的文件读取
            try:
                content, detected_encoding = read_file_with_encoding_detection(temp_file)
                print(f"✅ 健壮文件读取成功")
                print(f"检测到的编码: {detected_encoding}")
                print(f"文件内容: {content}")
                
                # 验证内容
                self.assertIn("你好世界", content)
                print(f"✅ 中文内容正确读取")
                
            except Exception as e:
                print(f"❌ 健壮文件读取失败: {e}")
                self.fail(f"健壮文件读取失败: {e}")
            
            # 使用安全文件读取
            try:
                content = read_file_safe(temp_file)
                print(f"✅ 安全文件读取成功")
                print(f"文件内容: {content}")
                
                # 验证内容
                self.assertIn("你好世界", content)
                print(f"✅ 中文内容正确转换")
                
            except Exception as e:
                print(f"❌ 安全文件读取失败: {e}")
                self.fail(f"安全文件读取失败: {e}")
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"清理临时文件: {temp_file}")

    def test_batch_encoding_check(self):
        """测试批量编码检查"""
        print("\n📁 测试批量编码检查")
        
        # 检查examples/np目录下的所有文件
        np_dir = "examples/np"
        
        if not os.path.exists(np_dir):
            self.skipTest(f"目录不存在: {np_dir}")
        
        file_count = 0
        success_count = 0
        
        for root, dirs, files in os.walk(np_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_count += 1
                
                try:
                    # 获取编码信息
                    encoding_info = get_file_encoding_info(file_path)
                    
                    # 尝试读取文件
                    content = read_file_safe(file_path)
                    success_count += 1
                    
                    print(f"✅ {file}: {encoding_info['encoding']} (置信度: {encoding_info['confidence']:.2f})")
                    
                    # 验证编码信息
                    self.assertIsInstance(encoding_info, dict)
                    self.assertIn('encoding', encoding_info)
                    self.assertIn('confidence', encoding_info)
                    
                except Exception as e:
                    print(f"❌ {file}: 读取失败 - {e}")
        
        print(f"\n📊 批量检查结果:")
        print(f"总文件数: {file_count}")
        print(f"成功读取: {success_count}")
        print(f"失败数量: {file_count - success_count}")
        
        if file_count > 0:
            success_rate = success_count / file_count * 100
            print(f"成功率: {success_rate:.1f}%")
            
            # 验证成功率
            self.assertGreater(success_rate, 80, f"成功率过低: {success_rate:.1f}%")


def main():
    """主函数"""
    print("🔍 编码处理测试")
    print("=" * 80)
    
    # 运行测试
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main() 