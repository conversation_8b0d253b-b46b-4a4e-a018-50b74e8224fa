#!/usr/bin/env python3
"""
Alpaca SFT数据生成测试

测试Alpaca格式数据生成功能
"""

import json
import os
import sys
import tempfile
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.alpaca_sft import (AlpacaSFTData, AlpacaSFTGenerator,
                                          AlpacaTaskType)
from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.generator import SFTDataGenerator


class TestAlpacaSFTGeneration(unittest.TestCase):
    """Alpaca SFT数据生成测试类"""

    def setUp(self):
        """测试前准备"""
        self.examples_dir = project_root / "examples" / "np"
        
        # 跳过测试如果目录不存在
        if not self.examples_dir.exists():
            self.skipTest(f"示例目录不存在: {self.examples_dir}")

    def test_alpaca_task_types(self):
        """测试Alpaca任务类型"""
        print("=== 测试Alpaca任务类型 ===")
        
        # 测试所有任务类型
        task_types = [
            AlpacaTaskType.CODE_COMPLETION,
            AlpacaTaskType.CODE_EDIT,
            AlpacaTaskType.CODE_EXPLANATION,
            AlpacaTaskType.BUG_FIX,
            AlpacaTaskType.CODE_REFACTOR
        ]
        
        print(f"支持的任务类型: {[t.value for t in task_types]}")
        
        # 验证任务类型
        self.assertIsInstance(task_types, list)
        self.assertGreater(len(task_types), 0)
        
        # 验证每个任务类型
        for task_type in task_types:
            self.assertIsInstance(task_type, AlpacaTaskType)
            self.assertIsInstance(task_type.value, str)
            print(f"  {task_type.value}: {task_type}")

    def test_alpaca_generator(self):
        """测试Alpaca生成器"""
        print("\n=== 测试Alpaca生成器 ===")
        
        try:
            # 初始化组件
            print("1. 初始化分析器...")
            analyzer = RealNPAnalyzer(str(self.examples_dir))
            analyzer.analyze_project()
            
            print("2. 初始化SFT生成器...")
            sft_generator = SFTDataGenerator(analyzer)
            
            print("3. 初始化Alpaca生成器...")
            alpaca_generator = AlpacaSFTGenerator(sft_generator)
            
            # 测试不同任务类型
            task_types = [
                AlpacaTaskType.CODE_COMPLETION,
                AlpacaTaskType.CODE_EDIT,
                AlpacaTaskType.CODE_EXPLANATION
            ]
            
            for task_type in task_types:
                print(f"\n4. 测试 {task_type.value} 任务...")
                
                try:
                    # 生成少量样本进行测试
                    alpaca_data = alpaca_generator.generate_alpaca_sft_data(
                        max_samples=2,
                        task_type=task_type
                    )
                    
                    print(f"   生成了 {len(alpaca_data)} 个样本")
                    
                    # 验证生成结果
                    self.assertIsInstance(alpaca_data, list)
                    
                    if alpaca_data:
                        # 显示第一个样本
                        sample = alpaca_data[0]
                        print(f"   样本信息:")
                        print(f"     文件: {sample.filepath}")
                        print(f"     函数: {sample.function_name}")
                        print(f"     行号: {sample.line_number}")
                        print(f"     任务类型: {sample.task_type}")
                        print(f"     模型类型: {sample.model_type}")
                        
                        print(f"   指令: {sample.instruction}")
                        print(f"   输入长度: {len(sample.input)}")
                        print(f"   输出: {sample.output}")
                        
                        # 验证样本对象
                        self.assertIsInstance(sample, AlpacaSFTData)
                        self.assertIsInstance(sample.filepath, str)
                        self.assertIsInstance(sample.function_name, str)
                        self.assertIsInstance(sample.line_number, int)
                        self.assertIsInstance(sample.task_type, str)
                        self.assertIsInstance(sample.model_type, str)
                        self.assertIsInstance(sample.instruction, str)
                        self.assertIsInstance(sample.input, str)
                        self.assertIsInstance(sample.output, str)
                        
                        # 测试数据验证
                        is_valid = sample.validate()
                        print(f"   数据验证: {'通过' if is_valid else '失败'}")
                        self.assertTrue(is_valid, "数据验证失败")
                        
                        # 测试格式转换
                        alpaca_dict = sample.to_alpaca_dict()
                        print(f"   Alpaca格式字段: {list(alpaca_dict.keys())}")
                        
                        # 验证Alpaca格式
                        self.assertIsInstance(alpaca_dict, dict)
                        self.assertIn('instruction', alpaca_dict)
                        self.assertIn('input', alpaca_dict)
                        self.assertIn('output', alpaca_dict)
                        
                except Exception as e:
                    print(f"   生成失败: {e}")
                    self.fail(f"Alpaca生成失败: {e}")
                    
        except Exception as e:
            print(f"初始化失败: {e}")
            self.fail(f"初始化失败: {e}")

    def test_data_saving(self):
        """测试数据保存功能"""
        print("\n=== 测试数据保存功能 ===")
        
        # 创建测试数据
        test_alpaca_data = [
            {
                "instruction": "请补全代码",
                "input": "void test() {\n  int x = 1;\n",
                "output": "  int result = x + 1;\n"
            },
            {
                "instruction": "请解释代码",
                "input": "int add(int a, int b) { return a + b; }",
                "output": "这是一个加法函数，接受两个整数参数并返回它们的和。"
            }
        ]
        
        # 转换为AlpacaSFTData对象
        alpaca_objects = []
        for i, data in enumerate(test_alpaca_data):
            alpaca_obj = AlpacaSFTData(
                instruction=data["instruction"],
                input=data["input"],
                output=data["output"],
                filepath=f"test_{i}.c",
                function_name=f"test_func_{i}",
                line_number=i + 1,
                task_type="code_completion"
            )
            alpaca_objects.append(alpaca_obj)
        
        # 验证对象创建
        self.assertEqual(len(alpaca_objects), 2)
        for obj in alpaca_objects:
            self.assertIsInstance(obj, AlpacaSFTData)
        
        # 创建临时目录用于测试
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建生成器实例用于保存
            try:
                analyzer = RealNPAnalyzer(str(self.examples_dir))
                sft_generator = SFTDataGenerator(analyzer)
                alpaca_generator = AlpacaSFTGenerator(sft_generator)
                
                # 保存JSON格式
                json_file = Path(temp_dir) / "test_alpaca.json"
                alpaca_generator.save_to_json(alpaca_objects, str(json_file), alpaca_format_only=True)
                print(f"已保存JSON格式: {json_file}")
                
                # 保存JSONL格式
                jsonl_file = Path(temp_dir) / "test_alpaca.jsonl"
                alpaca_generator.save_to_jsonl(alpaca_objects, str(jsonl_file), alpaca_format_only=True)
                print(f"已保存JSONL格式: {jsonl_file}")
                
                # 保存完整格式
                full_json_file = Path(temp_dir) / "test_alpaca_full.json"
                alpaca_generator.save_to_json(alpaca_objects, str(full_json_file), alpaca_format_only=False)
                print(f"已保存完整格式: {full_json_file}")
                
                # 验证保存的文件
                print("\n验证保存的文件:")
                for file_path in [json_file, jsonl_file, full_json_file]:
                    self.assertTrue(file_path.exists(), f"文件不存在: {file_path}")
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        if file_path.suffix == '.jsonl':
                            # JSONL格式，读取第一行
                            first_line = f.readline().strip()
                            data = json.loads(first_line)
                        else:
                            # JSON格式
                            data = json.load(f)
                    
                    print(f"  {file_path.name}:")
                    print(f"    数据类型: {type(data)}")
                    if isinstance(data, list):
                        print(f"    样本数量: {len(data)}")
                        if data:
                            print(f"    字段: {list(data[0].keys())}")
                    else:
                        print(f"    字段: {list(data.keys())}")
                    
                    # 验证数据格式
                    self.assertIsInstance(data, (list, dict))
                    
            except Exception as e:
                print(f"保存测试失败: {e}")
                self.fail(f"保存测试失败: {e}")


def main():
    """主函数"""
    print("Alpaca SFT数据生成器测试")
    print("=" * 50)
    
    # 运行测试
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main() 