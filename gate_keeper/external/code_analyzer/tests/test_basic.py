"""
Basic Tests

基本功能测试
"""

import os
import tempfile
import unittest
from pathlib import Path

from ..core.ast_parser import ASTParserFactory
from ..core.code_graph import CodeGraph
from ..core.repository_index import RepositoryIndex
from ..core.static_analyzer import StaticAnalyzer
from ..models.function import Function, FunctionSignature, Parameter


class TestBasicFunctionality(unittest.TestCase):
    """基本功能测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.temp_dir, "test.py")
        
        # 创建测试Python文件
        test_code = '''
def main():
    print("Hello, World!")
    helper_function()
    return True

def helper_function():
    print("Helper function")
    another_function()
    return "helper"

def another_function():
    print("Another function")
    return 42
'''
        with open(self.test_file, 'w', encoding='utf-8') as f:
            f.write(test_code)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_ast_parser_factory(self):
        """测试AST解析器工厂"""
        # 测试创建Python解析器
        python_parser = ASTParserFactory.create("python")
        self.assertIsNotNone(python_parser)
        
        # 测试创建C解析器
        c_parser = ASTParserFactory.create("c")
        self.assertIsNotNone(c_parser)
        
        # 测试无效语言
        with self.assertRaises(ValueError):
            ASTParserFactory.create("invalid_lang")
    
    def test_python_parser(self):
        """测试Python解析器"""
        parser = ASTParserFactory.create("python")
        
        # 测试函数提取
        functions = parser.extract_functions(self.test_file)
        self.assertGreater(len(functions), 0)
        
        # 验证函数信息
        function_names = [f.name for f in functions]
        self.assertIn("main", function_names)
        self.assertIn("helper_function", function_names)
        self.assertIn("another_function", function_names)
        
        # 测试调用提取
        calls = parser.extract_calls(self.test_file)
        self.assertGreater(len(calls), 0)
    
    def test_repository_index(self):
        """测试仓库索引"""
        repo_index = RepositoryIndex(
            repo_dir=self.temp_dir,
            branch="main"
        )
        repo_index.build()
        
        # 验证索引结果
        self.assertGreater(len(repo_index.function_definitions), 0)
        self.assertGreater(len(repo_index.function_calls), 0)
        
        # 验证调用图
        graph = repo_index.get_call_graph()
        self.assertGreater(graph.number_of_nodes(), 0)
    
    def test_static_analyzer(self):
        """测试静态分析器"""
        repo_index = RepositoryIndex(
            repo_dir=self.temp_dir,
            branch="main"
        )
        repo_index.build()
        
        analyzer = StaticAnalyzer(repo_index)
        
        # 测试调用链分析
        chains = analyzer.get_bidirectional_call_chains("main", "test.py", max_depth=3)
        self.assertGreater(len(chains), 0)
    
    def test_code_graph(self):
        """测试代码图"""
        graph = CodeGraph.build_from_repository(self.temp_dir)
        
        # 测试统计信息
        stats = graph.get_graph_statistics()
        self.assertIn("nodes", stats)
        self.assertIn("edges", stats)
        self.assertIn("functions", stats)
        
        # 测试函数信息获取
        func_info = graph.get_function_info("test.py::main")
        self.assertIsNotNone(func_info)
        self.assertEqual(func_info["name"], "main")


if __name__ == "__main__":
    unittest.main() 