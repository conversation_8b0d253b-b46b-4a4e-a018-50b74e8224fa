"""
Test NP Parser

NP解析器测试
"""

import os
import tempfile
import unittest

from ..core.ast_parser import ASTParserFactory


class TestNPParser(unittest.TestCase):
    """NP解析器测试类"""

    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.test_dir, "test.np")
        
        # 创建测试NP代码
        test_code = """#include <stdio.h>

static void func1(int param1, char* param2) {
    printf("hello");
    func2();
}

static void func2() {
    int x = 10;
    func3(x);
}

static int func3(int value) {
    return value * 2;
}

int main() {
    func1(1, "test");
    return 0;
}"""
        
        with open(self.test_file, 'w', encoding='utf-8') as f:
            f.write(test_code)

    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_file):
            os.remove(self.test_file)
        if os.path.exists(self.test_dir):
            os.rmdir(self.test_dir)

    def test_np_parser_creation(self):
        """测试NP解析器创建"""
        try:
            parser = ASTParserFactory.create("np")
            self.assertIsNotNone(parser)
        except Exception as e:
            self.skipTest(f"NP parser not available: {e}")

    def test_np_function_extraction(self):
        """测试NP函数提取"""
        try:
            parser = ASTParserFactory.create("np")
            functions = parser.extract_functions(self.test_file)
            
            print(f"实际提取到 {len(functions)} 个函数:")
            for i, func in enumerate(functions, 1):
                print(f"  函数 {i}: {func.name}")
                print(f"    位置: {func.range.start_line}-{func.range.end_line}")
            
            # NP解析器至少应该提取到一些函数
            self.assertGreaterEqual(len(functions), 1)
            
            # 检查函数名
            func_names = [f.name for f in functions]
            expected_names = ["func1", "func2", "func3", "main"]
            
            # 至少应该找到一些预期的函数
            found_expected = [name for name in expected_names if name in func_names]
            self.assertGreater(len(found_expected), 0, f"应该至少找到一个预期函数，实际找到: {func_names}")
                
        except Exception as e:
            print(f"NP parser error details: {e}")
            import traceback
            traceback.print_exc()
            self.skipTest(f"NP parser not available: {e}")

    def test_np_call_extraction(self):
        """测试NP函数调用提取"""
        try:
            parser = ASTParserFactory.create("np")
            calls = parser.extract_calls(self.test_file)
            
            # 应该提取到函数调用
            self.assertGreaterEqual(len(calls), 1)
            
            # 检查调用关系
            call_pairs = [(call.caller, call.callee) for call in calls]
            
            # 验证func1调用func2
            self.assertIn(("func1", "func2"), call_pairs)
            
        except Exception as e:
            self.skipTest(f"NP parser not available: {e}")

    def test_np_parent_element_finding(self):
        """测试NP父级元素查找"""
        try:
            parser = ASTParserFactory.create("np")
            
            # 查找第5-7行的父级元素（应该在func1函数内）
            parent = parser.find_parent_element(self.test_file, 5, 7)
            
            self.assertIsNotNone(parent)
            self.assertEqual(parent.name, "func1")
            
        except Exception as e:
            self.skipTest(f"NP parser not available: {e}")


if __name__ == '__main__':
    unittest.main() 