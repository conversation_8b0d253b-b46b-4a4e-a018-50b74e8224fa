#!/usr/bin/env python3
"""
新测试运行脚本

运行从ai_debug中提取的新测试文件
"""

import os
import sys
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_new_tests():
    """运行新创建的测试"""
    print("🧪 运行新创建的测试")
    print("=" * 80)
    
    # 测试文件列表
    test_files = [
        "tests.test_np_struct_usages",
        "tests.test_encoding_handling", 
        "tests.test_embedding_generation",
        "tests.test_alpaca_sft_generation"
    ]
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试
    for test_file in test_files:
        try:
            module = __import__(test_file, fromlist=['*'])
            tests = loader.loadTestsFromModule(module)
            suite.addTests(tests)
            print(f"✅ 加载测试: {test_file}")
        except Exception as e:
            print(f"❌ 加载测试失败: {test_file} - {e}")
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果统计
    print("\n" + "=" * 80)
    print("📊 测试结果统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n❌ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # 返回结果
    return len(result.failures) == 0 and len(result.errors) == 0


def run_individual_test(test_name):
    """运行单个测试"""
    print(f"🧪 运行单个测试: {test_name}")
    print("=" * 80)
    
    try:
        module = __import__(f"tests.{test_name}", fromlist=['*'])
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="运行新创建的测试")
    parser.add_argument("--test", help="运行特定的测试文件（不包含tests.前缀）")
    parser.add_argument("--all", action="store_true", help="运行所有新测试")
    
    args = parser.parse_args()
    
    if args.test:
        success = run_individual_test(args.test)
    else:
        success = run_new_tests()
    
    if success:
        print("\n✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main() 