#!/usr/bin/env python3
"""
补全标记参数化测试

测试Alpaca SFT任务中补全标记的参数化功能
"""

import sys
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.alpaca_sft.tasks.code_completion import \
    CodeCompletionTask


class TestHoleMarkerParameterization(unittest.TestCase):
    """补全标记参数化测试类"""

    def setUp(self):
        """测试前准备"""
        self.task = CodeCompletionTask()

    def test_default_hole_marker(self):
        """测试默认补全标记"""
        print("🔍 测试默认补全标记")
        
        # 使用默认参数
        result = self.task.format_input(
            filepath="test.c",
            function_name="test_func",
            line_number=10,
            before="void test_func() {\n  int x = 1;\n",
            after="  return result;\n}"
        )
        
        print(f"格式化结果:\n{result}")
        
        # 验证默认标记
        self.assertIn("<HOLE>", result)
        self.assertIn("请补全<HOLE>位置的代码", result)

    def test_custom_hole_marker(self):
        """测试自定义补全标记"""
        print("🔍 测试自定义补全标记")
        
        # 使用自定义标记
        custom_marker = "<COMPLETE_HERE>"
        result = self.task.format_input(
            filepath="test.c",
            function_name="test_func",
            line_number=10,
            before="void test_func() {\n  int x = 1;\n",
            after="  return result;\n}",
            hole_marker=custom_marker
        )
        
        print(f"格式化结果:\n{result}")
        
        # 验证自定义标记
        self.assertIn(custom_marker, result)
        self.assertIn(f"请补全{custom_marker}位置的代码", result)
        self.assertNotIn("<HOLE>", result)

    def test_different_markers(self):
        """测试不同的补全标记"""
        print("🔍 测试不同的补全标记")
        
        markers = [
            "<FILL_HERE>",
            "[MISSING_CODE]",
            "___TO_COMPLETE___",
            "<!-- INSERT CODE -->"
        ]
        
        for marker in markers:
            print(f"\n测试标记: {marker}")
            
            result = self.task.format_input(
                filepath="test.c",
                function_name="test_func",
                line_number=10,
                before="void test_func() {\n  int x = 1;\n",
                after="  return result;\n}",
                hole_marker=marker
            )
            
            # 验证标记正确使用
            self.assertIn(marker, result)
            self.assertIn(f"请补全{marker}位置的代码", result)
            
            # 验证代码上下文格式正确
            self.assertIn("```c", result)
            self.assertIn("```", result)
            self.assertIn("文件路径: test.c", result)
            self.assertIn("函数名称: test_func", result)
            self.assertIn("行号: 10", result)

    def test_empty_marker(self):
        """测试空标记"""
        print("🔍 测试空标记")
        
        # 使用空标记
        result = self.task.format_input(
            filepath="test.c",
            function_name="test_func",
            line_number=10,
            before="void test_func() {\n  int x = 1;\n",
            after="  return result;\n}",
            hole_marker=""
        )
        
        print(f"格式化结果:\n{result}")
        
        # 验证空标记处理
        self.assertIn("请补全位置的代码", result)
        # 应该回退到默认标记
        self.assertIn("<HOLE>", result)

    def test_special_characters_marker(self):
        """测试特殊字符标记"""
        print("🔍 测试特殊字符标记")
        
        special_markers = [
            "$$$",
            "###",
            "***",
            "---",
            "+++"
        ]
        
        for marker in special_markers:
            print(f"\n测试特殊标记: {marker}")
            
            result = self.task.format_input(
                filepath="test.c",
                function_name="test_func",
                line_number=10,
                before="void test_func() {\n  int x = 1;\n",
                after="  return result;\n}",
                hole_marker=marker
            )
            
            # 验证特殊字符标记正确使用
            self.assertIn(marker, result)
            self.assertIn(f"请补全{marker}位置的代码", result)


def main():
    """主函数"""
    print("补全标记参数化测试")
    print("=" * 50)
    
    # 运行测试
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main() 