#!/usr/bin/env python3
"""
SFT Data Generation Test Runner

SFT数据生成测试运行器
"""

import os
import sys
import time
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)


def run_sft_tests():
    """运行SFT数据生成测试"""
    print("🧪 开始运行SFT数据生成测试...")
    print("=" * 60)
    
    # 测试套件
    test_suite = unittest.TestSuite()
    
    # 导入测试模块
    from test_sft_data_generation import (TestDataPersistence,
                                          TestDataQualityValidation,
                                          TestErrorHandling,
                                          TestPerformanceAndLimits,
                                          TestSFTDataGeneration,
                                          TestSFTDataStructures)

    # 添加测试类
    test_classes = [
        TestSFTDataStructures,
        TestSFTDataGeneration,
        TestDataQualityValidation,
        TestDataPersistence,
        TestErrorHandling,
        TestPerformanceAndLimits
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    start_time = time.time()
    
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    result = runner.run(test_suite)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📊 测试结果摘要")
    print("=" * 60)
    print(f"运行时间: {duration:.2f} 秒")
    print(f"测试总数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # 返回测试结果
    success = len(result.failures) == 0 and len(result.errors) == 0
    return success


def run_specific_test(test_name):
    """运行特定的测试"""
    print(f"🧪 运行特定测试: {test_name}")
    print("=" * 60)
    
    # 导入测试模块
    from test_sft_data_generation import (TestDataPersistence,
                                          TestDataQualityValidation,
                                          TestErrorHandling,
                                          TestPerformanceAndLimits,
                                          TestSFTDataGeneration,
                                          TestSFTDataStructures)

    # 查找测试方法
    test_method = None
    test_class = None
    
    test_classes = [
        TestSFTDataStructures,
        TestSFTDataGeneration,
        TestDataQualityValidation,
        TestDataPersistence,
        TestErrorHandling,
        TestPerformanceAndLimits
    ]
    
    for cls in test_classes:
        if hasattr(cls, test_name):
            test_method = getattr(cls, test_name)
            test_class = cls
            break
    
    if test_method is None:
        print(f"❌ 未找到测试方法: {test_name}")
        return False
    
    # 运行单个测试
    suite = unittest.TestSuite()
    suite.addTest(test_class(test_name))
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行特定测试
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        # 运行所有测试
        success = run_sft_tests()
    
    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main() 