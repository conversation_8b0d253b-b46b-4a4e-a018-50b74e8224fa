#!/usr/bin/env python3
"""
Prompt SFT架构测试

测试新的prompt_sft三元组架构和alpaca_sft简化功能
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.alpaca_sft.models import AlpacaSFTData, AlpacaTaskType
from core.data_gen.sft.prompt_sft.models import (ModelTokenConfig, ModelType,
                                                 PromptSFTData, PromptTemplate)


class TestPromptSFTArchitecture(unittest.TestCase):
    """Prompt SFT架构测试类"""

    def setUp(self):
        """测试前准备"""
        self.test_dir = os.path.abspath("examples/np")
        
        # 跳过测试如果目录不存在
        if not os.path.exists(self.test_dir):
            self.skipTest(f"测试目录不存在: {self.test_dir}")

    def test_prompt_template_triple_structure(self):
        """测试PromptTemplate的三元组结构"""
        print("🔍 测试PromptTemplate的三元组结构")
        
        # 创建模板
        template = PromptTemplate(
            system="You are a code completion assistant.",
            instruction="Please complete the code at the specified location.",
            system_rules="Follow the coding style and conventions.",
            env="Language: C, Framework: NP",
            user_rules="Use clear variable names.",
            related_context="Function: test_func, Variables: x, y",
            similar_context="Similar function: other_func"
        )
        
        # 测试数据
        before = "void test_func() {\n  int x = 1;\n"
        after = "  return result;\n}"
        token_config = ModelTokenConfig.get_config(ModelType.QWEN_CODER)
        
        # 生成instruction和input
        instruction, input_text = template.to_prompt(before, after, token_config)
        
        # 验证instruction包含所有指令部分
        self.assertIn("You are a code completion assistant.", instruction)
        self.assertIn("Please complete the code", instruction)
        self.assertIn("Follow the coding style", instruction)
        self.assertIn("Language: C", instruction)
        self.assertIn("Use clear variable names", instruction)
        self.assertIn("Function: test_func", instruction)
        self.assertIn("Similar function: other_func", instruction)
        
        # 验证input包含特殊token
        self.assertIn("<|fim_prefix|>", input_text)
        self.assertIn("<|fim_suffix|>", input_text)
        self.assertIn("<|fim_middle|>", input_text)
        self.assertIn(before, input_text)
        self.assertIn(after, input_text)
        
        print("✅ PromptTemplate三元组结构测试通过")

    def test_prompt_sft_data_triple_structure(self):
        """测试PromptSFTData的三元组结构"""
        print("🔍 测试PromptSFTData的三元组结构")
        
        # 创建PromptSFTData
        prompt_data = PromptSFTData(
            instruction="You are a code completion assistant. Please complete the code.",
            input="<|fim_prefix|>void test() {\n  int x = 1;<|fim_suffix|>  return x;\n}<|fim_middle|>",
            output="  int y = x + 1;",
            filepath="test.c",
            function_name="test",
            line_number=10,
            model_type=ModelType.QWEN_CODER,
            before="void test() {\n  int x = 1;",
            expected="  int y = x + 1;",
            after="  return x;\n}"
        )
        
        # 验证三元组结构
        self.assertEqual(prompt_data.instruction, "You are a code completion assistant. Please complete the code.")
        self.assertEqual(prompt_data.input, "<|fim_prefix|>void test() {\n  int x = 1;<|fim_suffix|>  return x;\n}<|fim_middle|>")
        self.assertEqual(prompt_data.output, "  int y = x + 1;")
        
        # 验证兼容性属性
        self.assertIn("You are a code completion assistant", prompt_data.prompt)
        self.assertIn("<|fim_prefix|>", prompt_data.prompt)
        self.assertEqual(prompt_data.completion, "  int y = x + 1;")
        
        # 验证数据质量
        self.assertTrue(prompt_data.validate())
        
        print("✅ PromptSFTData三元组结构测试通过")

    def test_alpaca_sft_data_simplified(self):
        """测试AlpacaSFTData的简化结构"""
        print("🔍 测试AlpacaSFTData的简化结构")
        
        # 创建AlpacaSFTData
        alpaca_data = AlpacaSFTData(
            instruction="You are a code completion assistant. Please complete the code.",
            input="<|fim_prefix|>void test() {\n  int x = 1;<|fim_suffix|>  return x;\n}<|fim_middle|>",
            output="  int y = x + 1;",
            filepath="test.c",
            function_name="test",
            line_number=10,
            model_type="qwen_coder",
            task_type="code_completion",
            before="void test() {\n  int x = 1;",
            expected="  int y = x + 1;",
            after="  return x;\n}"
        )
        
        # 验证核心三元组
        self.assertEqual(alpaca_data.instruction, "You are a code completion assistant. Please complete the code.")
        self.assertEqual(alpaca_data.input, "<|fim_prefix|>void test() {\n  int x = 1;<|fim_suffix|>  return x;\n}<|fim_middle|>")
        self.assertEqual(alpaca_data.output, "  int y = x + 1;")
        
        # 验证元数据
        self.assertEqual(alpaca_data.filepath, "test.c")
        self.assertEqual(alpaca_data.function_name, "test")
        self.assertEqual(alpaca_data.line_number, 10)
        self.assertEqual(alpaca_data.model_type, "qwen_coder")
        self.assertEqual(alpaca_data.task_type, "code_completion")
        
        # 验证数据质量
        self.assertTrue(alpaca_data.validate())
        
        # 测试字典转换
        alpaca_dict = alpaca_data.to_dict()
        self.assertIn("instruction", alpaca_dict)
        self.assertIn("input", alpaca_dict)
        self.assertIn("output", alpaca_dict)
        self.assertIn("model_type", alpaca_dict)
        self.assertIn("task_type", alpaca_dict)
        
        # 测试标准Alpaca格式
        alpaca_standard = alpaca_data.to_alpaca_dict()
        self.assertEqual(len(alpaca_standard), 3)  # 只有instruction, input, output
        self.assertIn("instruction", alpaca_standard)
        self.assertIn("input", alpaca_standard)
        self.assertIn("output", alpaca_standard)
        
        print("✅ AlpacaSFTData简化结构测试通过")

    def test_model_token_configs(self):
        """测试不同模型的token配置"""
        print("🔍 测试不同模型的token配置")
        
        # 测试Qwen Coder配置
        qwen_config = ModelTokenConfig.get_config(ModelType.QWEN_CODER)
        self.assertEqual(qwen_config.start_token, "<|fim_prefix|>")
        self.assertEqual(qwen_config.middle_token, "<|fim_suffix|>")
        self.assertEqual(qwen_config.end_token, "<|fim_middle|>")
        
        # 测试DeepSeek Coder配置
        
        deepseek_config = ModelTokenConfig.get_config(ModelType.DEEPSEEK_CODER)
        self.assertEqual(deepseek_config.start_token, "<｜fim▁begin｜>")
        self.assertEqual(deepseek_config.middle_token, "<｜fim▁hole｜>")
        self.assertEqual(deepseek_config.end_token, "<｜fim▁end｜>")
        
        print("✅ 模型token配置测试通过")

    def test_data_flow_integration(self):
        """测试数据流集成"""
        print("🔍 测试数据流集成")
        
        # 模拟从prompt_sft到alpaca_sft的数据流
        prompt_data = PromptSFTData(
            instruction="You are a code completion assistant. Please complete the code.",
            input="<|fim_prefix|>void test() {\n  int x = 1;<|fim_suffix|>  return x;\n}<|fim_middle|>",
            output="  int y = x + 1;",
            filepath="test.c",
            function_name="test",
            line_number=10,
            model_type=ModelType.QWEN_CODER,
            before="void test() {\n  int x = 1;",
            expected="  int y = x + 1;",
            after="  return x;\n}"
        )
        
        # 转换为Alpaca格式
        alpaca_data = AlpacaSFTData(
            instruction=prompt_data.instruction,
            input=prompt_data.input,
            output=prompt_data.output,
            filepath=prompt_data.filepath,
            function_name=prompt_data.function_name,
            line_number=prompt_data.line_number,
            model_type=prompt_data.model_type.value,
            task_type="code_completion",
            before=prompt_data.before,
            expected=prompt_data.expected,
            after=prompt_data.after
        )
        
        # 验证数据完整性
        self.assertEqual(alpaca_data.instruction, prompt_data.instruction)
        self.assertEqual(alpaca_data.input, prompt_data.input)
        self.assertEqual(alpaca_data.output, prompt_data.output)
        self.assertEqual(alpaca_data.filepath, prompt_data.filepath)
        self.assertEqual(alpaca_data.function_name, prompt_data.function_name)
        self.assertEqual(alpaca_data.line_number, prompt_data.line_number)
        
        # 验证数据质量
        self.assertTrue(prompt_data.validate())
        self.assertTrue(alpaca_data.validate())
        
        print("✅ 数据流集成测试通过")


def main():
    """主函数"""
    print("🧪 运行Prompt SFT架构测试")
    print("=" * 80)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试
    test_classes = [
        TestPromptSFTArchitecture
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
        print(f"✅ 加载测试: {test_class.__name__}")
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 80)
    print(f"测试结果: 运行 {result.testsRun} 个测试")
    print(f"失败: {len(result.failures)} 个")
    print(f"错误: {len(result.errors)} 个")
    
    if result.failures:
        print("\n失败详情:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print("\n错误详情:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    return len(result.failures) + len(result.errors) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 