#!/usr/bin/env python3
"""
SFT数据生成与数据清洗集成测试

测试在SFT数据生成过程中集成数据清洗功能
"""

import json
import os
import sys
import tempfile
import unittest
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.data_gen.clean import TextCleaner
from core.data_gen.sft.analyzer import RealNPAnalyzer


class TestSFTWithCleanIntegration(unittest.TestCase):
    """SFT数据生成与数据清洗集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.cleaner = TextCleaner()
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试项目目录
        self.test_project_dir = os.path.join(self.temp_dir, "test_np_project")
        os.makedirs(self.test_project_dir, exist_ok=True)
        
        # 创建包含异常字符的测试文件
        self._create_test_files()
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_test_files(self):
        """创建测试文件"""
        # 创建包含异常字符的.asm文件
        asm_content = (
            "void testFunction(uint8 param) {\n"
            "    uint8\x00variable;\n"  # 包含空字符
            "    /* 这是一个测试函数 */\n"
            "    if (param > 0) {\n"
            "        variable = param;\uFFFD\n"  # 包含替换字符
            "    }\n"
            "    return variable;\n"
            "}\n"
        )
        
        asm_file = os.path.join(self.test_project_dir, "test.asm")
        with open(asm_file, 'w', encoding='utf-8') as f:
            f.write(asm_content)
        
        # 创建包含异常字符的.h文件
        h_content = (
            "#ifndef TEST_H\n"
            "#define TEST_H\n"
            "\n"
            "struct TestStruct {\n"
            "    uint8\x01field1;\uFFFD\n"  # 包含控制字符和替换字符
            "    uint16 field2;\n"
            "};\n"
            "\n"
            "#endif\n"
        )
        
        h_file = os.path.join(self.test_project_dir, "test.h")
        with open(h_file, 'w', encoding='utf-8') as f:
            f.write(h_content)
    
    def test_file_cleaning_before_analysis(self):
        """测试在分析前进行文件清洗"""
        print("🔍 测试分析前文件清洗...")
        
        # 获取原始文件
        asm_file = os.path.join(self.test_project_dir, "test.asm")
        h_file = os.path.join(self.test_project_dir, "test.h")
        
        # 创建清洗后的文件
        cleaned_asm_file = os.path.join(self.test_project_dir, "cleaned_test.asm")
        cleaned_h_file = os.path.join(self.test_project_dir, "cleaned_test.h")
        
        # 清洗文件
        asm_result = self.cleaner.clean_file(asm_file, cleaned_asm_file)
        h_result = self.cleaner.clean_file(h_file, cleaned_h_file)
        
        # 验证清洗结果
        self.assertTrue(asm_result.is_successful)
        self.assertTrue(h_result.is_successful)
        self.assertGreater(asm_result.removed_chars, 0)
        self.assertGreater(h_result.removed_chars, 0)
        
        # 验证异常字符被移除
        self.assertNotIn('\x00', asm_result.cleaned_content)
        self.assertNotIn('\uFFFD', asm_result.cleaned_content)
        self.assertNotIn('\x01', h_result.cleaned_content)
        
        # 验证正常内容保留
        self.assertIn('void testFunction', asm_result.cleaned_content)
        self.assertIn('struct TestStruct', h_result.cleaned_content)
        
        print(f"✅ 文件清洗成功，移除字符数: ASM={asm_result.removed_chars}, H={h_result.removed_chars}")
    
    def test_batch_cleaning_for_sft(self):
        """测试为SFT数据生成进行批量清洗"""
        print("🧹 测试批量清洗...")
        
        # 创建更多测试文件
        for i in range(3):
            content = f"void function{i}() {{\n    uint8\x00var{i};\uFFFD\n    return var{i};\n}}\n"
            file_path = os.path.join(self.test_project_dir, f"func_{i}.asm")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 创建输出目录
        cleaned_dir = os.path.join(self.test_project_dir, "cleaned")
        os.makedirs(cleaned_dir, exist_ok=True)
        
        # 批量清洗
        results = self.cleaner.batch_clean_files(self.test_project_dir, cleaned_dir, ['.asm', '.h'])
        
        # 验证结果
        self.assertGreater(len(results), 0)
        for result in results:
            self.assertTrue(result.is_successful)
            self.assertGreater(result.removed_chars, 0)
        
        # 验证输出文件
        output_files = list(Path(cleaned_dir).glob("*"))
        self.assertGreater(len(output_files), 0)
        
        print(f"✅ 批量清洗成功，处理文件数: {len(results)}")
    
    def test_encoding_consistency_for_sft(self):
        """测试SFT数据生成的编码一致性"""
        print("🔤 测试编码一致性...")
        
        # 创建不同编码的测试文件
        test_cases = [
            ("utf8_test.asm", "void utf8Func() { return 0; }", "utf-8"),
            ("gbk_test.asm", "void gbkFunc() { return 0; }", "gbk"),
        ]
        
        for filename, content, encoding in test_cases:
            file_path = os.path.join(self.test_project_dir, filename)
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
        
        # 清洗所有文件
        cleaned_dir = os.path.join(self.test_project_dir, "utf8_cleaned")
        os.makedirs(cleaned_dir, exist_ok=True)
        
        results = self.cleaner.batch_clean_files(self.test_project_dir, cleaned_dir, ['.asm'])
        
        # 验证所有输出文件都是UTF-8编码
        for result in results:
            if result.is_successful:
                # 验证清洗后的内容可以正确编码为UTF-8
                try:
                    utf8_bytes = result.cleaned_content.encode('utf-8')
                    self.assertIsInstance(utf8_bytes, bytes)
                except UnicodeEncodeError:
                    self.fail(f"UTF-8编码失败: {result.cleaned_content[:50]}")
        
        print("✅ 编码一致性验证通过")
    
    def test_sft_data_quality_with_cleaning(self):
        """测试清洗对SFT数据质量的影响"""
        print("📊 测试SFT数据质量...")
        
        # 创建包含各种问题的测试文件
        complex_content = (
            "void complexFunction(uint8 param) {\n"
            "    uint8\x00var1;\uFFFD\n"  # 异常字符
            "    uint16 var2;\n"
            "    \n"
            "    if (param > 0) {\n"
            "        var1 = param;\n"
            "        var2 = param * 2;\u200B\n"  # 零宽字符
            "    }\n"
            "    \n"
            "    return var1 + var2;\n"
            "}\n"
        )
        
        complex_file = os.path.join(self.test_project_dir, "complex.asm")
        with open(complex_file, 'w', encoding='utf-8') as f:
            f.write(complex_content)
        
        # 清洗文件
        cleaned_file = os.path.join(self.test_project_dir, "cleaned_complex.asm")
        result = self.cleaner.clean_file(complex_file, cleaned_file)
        
        # 验证清洗结果
        self.assertTrue(result.is_successful)
        
        # 验证数据质量指标
        quality_metrics = {
            'original_size': len(complex_content),
            'cleaned_size': len(result.cleaned_content),
            'removed_chars': result.removed_chars,
            'has_anomalies': '\x00' in complex_content or '\uFFFD' in complex_content,
            'is_utf8_compatible': True,
            'preserves_structure': 'void complexFunction' in result.cleaned_content
        }
        
        # 验证质量指标
        self.assertGreater(quality_metrics['removed_chars'], 0)
        self.assertTrue(quality_metrics['has_anomalies'])
        self.assertTrue(quality_metrics['is_utf8_compatible'])
        self.assertTrue(quality_metrics['preserves_structure'])
        
        print(f"✅ SFT数据质量验证通过:")
        print(f"   - 原始大小: {quality_metrics['original_size']}")
        print(f"   - 清洗后大小: {quality_metrics['cleaned_size']}")
        print(f"   - 移除字符数: {quality_metrics['removed_chars']}")
    
    def test_error_handling_in_sft_pipeline(self):
        """测试SFT流程中的错误处理"""
        print("⚠️ 测试错误处理...")
        
        # 测试不存在的文件
        non_existent_file = os.path.join(self.test_project_dir, "non_existent.asm")
        result = self.cleaner.clean_file(non_existent_file, "output.asm")
        
        self.assertFalse(result.is_successful)
        self.assertIn("无法读取文件", result.error_message)
        
        # 测试空文件
        empty_file = os.path.join(self.test_project_dir, "empty.asm")
        with open(empty_file, 'w', encoding='utf-8') as f:
            f.write("")
        
        result = self.cleaner.clean_file(empty_file, "output.asm")
        self.assertTrue(result.is_successful)
        self.assertEqual(result.cleaned_content, "")
        
        print("✅ 错误处理验证通过")


class TestCleanModuleInSFTWorkflow(unittest.TestCase):
    """测试数据清洗模块在SFT工作流中的集成"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.cleaner = TextCleaner()
        
        # 创建模拟的NP项目结构
        self._create_mock_np_project()
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def _create_mock_np_project(self):
        """创建模拟的NP项目结构"""
        # 创建项目目录结构
        project_structure = {
            "examples/np": [
                "main.asm",
                "utils.asm", 
                "config.h"
            ],
            "lib": [
                "common.asm",
                "helpers.asm"
            ],
            "include": [
                "definitions.h",
                "types.h"
            ]
        }
        
        for dir_path, files in project_structure.items():
            full_dir = os.path.join(self.temp_dir, dir_path)
            os.makedirs(full_dir, exist_ok=True)
            
            for file_name in files:
                file_path = os.path.join(full_dir, file_name)
                content = self._generate_mock_content(file_name)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
    
    def _generate_mock_content(self, file_name):
        """生成模拟文件内容"""
        if file_name.endswith('.asm'):
            return (
                f"void {file_name[:-4]}Function() {{\n"
                f"    uint8\x00var;\n"  # 只包含控制字符，不包含替换字符
                f"    uint16 result;\n"
                f"    \n"
                f"    if (var > 0) {{\n"
                f"        result = var * 2;\n"
                f"    }}\n"
                f"    \n"
                f"    return result;\n"
                f"}}\n"
            )
        elif file_name.endswith('.h'):
            return (
                f"#ifndef {file_name[:-2].upper()}_H\n"
                f"#define {file_name[:-2].upper()}_H\n"
                f"\n"
                f"struct {file_name[:-2]}Struct {{\n"
                f"    uint8\x01field1;\n"  # 只包含控制字符，不包含替换字符
                f"    uint16 field2;\n"
                f"}};\n"
                f"\n"
                f"#endif\n"
            )
        return ""
    
    def test_full_sft_workflow_with_cleaning(self):
        """测试完整的SFT工作流（包含清洗）"""
        print("🔄 测试完整SFT工作流...")
        
        # 1. 批量清洗所有文件
        cleaned_dir = os.path.join(self.temp_dir, "cleaned_project")
        os.makedirs(cleaned_dir, exist_ok=True)
        
        results = self.cleaner.batch_clean_files(self.temp_dir, cleaned_dir, ['.asm', '.h'])
        
        # 验证清洗结果
        self.assertGreater(len(results), 0)
        total_removed = sum(result.removed_chars for result in results if result.is_successful)
        self.assertGreater(total_removed, 0)
        
        # 2. 验证清洗后的文件结构
        cleaned_files = list(Path(cleaned_dir).rglob("*"))
        self.assertGreater(len(cleaned_files), 0)
        
        # 3. 验证所有清洗后的文件都是UTF-8编码且无异常字符
        for file_path in cleaned_files:
            if file_path.is_file() and file_path.suffix in ['.asm', '.h']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 验证无异常字符
                self.assertNotIn('\x00', content)
                self.assertNotIn('\x01', content)
                self.assertNotIn('\uFFFD', content)
                
                # 验证UTF-8编码
                try:
                    content.encode('utf-8')
                except UnicodeEncodeError:
                    self.fail(f"文件 {file_path} 不是有效的UTF-8编码")
        
        print(f"✅ 完整SFT工作流测试通过:")
        print(f"   - 处理文件数: {len(results)}")
        print(f"   - 总移除字符数: {total_removed}")
        print(f"   - 清洗后文件数: {len(cleaned_files)}")
    
    def test_clean_module_performance(self):
        """测试清洗模块的性能"""
        print("⚡ 测试性能...")
        
        import time

        # 创建大文件进行性能测试
        large_content = "void largeFunction() {\n" + "    uint8\x00var;\uFFFD\n" * 1000 + "}\n"
        large_file = os.path.join(self.temp_dir, "large.asm")
        
        with open(large_file, 'w', encoding='utf-8') as f:
            f.write(large_content)
        
        # 测量清洗时间
        start_time = time.time()
        result = self.cleaner.clean_file(large_file, os.path.join(self.temp_dir, "cleaned_large.asm"))
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # 验证性能指标
        self.assertTrue(result.is_successful)
        self.assertLess(processing_time, 5.0, "清洗时间不应超过5秒")
        
        print(f"✅ 性能测试通过:")
        print(f"   - 处理时间: {processing_time:.3f}秒")
        print(f"   - 文件大小: {len(large_content)}字符")
        print(f"   - 移除字符数: {result.removed_chars}")


def run_sft_clean_integration_tests():
    """运行SFT与数据清洗集成测试"""
    print("🧪 开始运行SFT与数据清洗集成测试...")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestSFTWithCleanIntegration,
        TestCleanModuleInSFTWorkflow
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 集成测试结果统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败测试数: {len(result.failures)}")
    print(f"错误测试数: {len(result.errors)}")
    print(f"跳过测试数: {len(result.skipped)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n❌ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n✅ 所有集成测试通过！")
        return True
    else:
        print("\n❌ 部分集成测试失败！")
        return False


if __name__ == "__main__":
    success = run_sft_clean_integration_tests()
    sys.exit(0 if success else 1) 