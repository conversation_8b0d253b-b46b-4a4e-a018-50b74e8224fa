#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗模块测试

测试异常字符检测、编码检测和文本清洗功能
"""

import os
import tempfile
import unittest
from pathlib import Path

from core.data_gen.clean import AnomalyDetector, EncodingDetector, TextCleaner


class TestAnomalyDetector(unittest.TestCase):
    """测试异常字符检测器"""
    
    def setUp(self):
        """设置测试环境"""
        self.detector = AnomalyDetector()
    
    def test_detect_normal_text(self):
        """测试检测正常文本"""
        text = "Hello, World! 你好，世界！"
        anomalies = self.detector.detect_anomalies(text)
        self.assertEqual(len(anomalies), 0)
    
    def test_detect_anomaly_text(self):
        """测试检测异常文本"""
        text = "Hello\x00World\x01\x02\x03"
        anomalies = self.detector.detect_anomalies(text)
        self.assertGreater(len(anomalies), 0)
        
        # 检查是否检测到控制字符
        anomaly_types = [a.anomaly_type for a in anomalies]
        self.assertIn('control_character', anomaly_types)
    
    def test_detect_special_characters(self):
        """测试检测特殊字符"""
        text = "Hello\uFFFDWorld\u0000\u0001"
        anomalies = self.detector.detect_anomalies(text)
        self.assertGreater(len(anomalies), 0)
        
        # 检查是否检测到替换字符和控制字符
        anomaly_types = [a.anomaly_type for a in anomalies]
        self.assertIn('replacement_character', anomaly_types)
        self.assertIn('control_character', anomaly_types)
    
    def test_remove_anomalies(self):
        """测试移除异常字符"""
        text = "Hello\x00World\x01\x02\x03"
        anomalies = self.detector.detect_anomalies(text)
        
        # 手动移除控制字符
        cleaned_text = text
        for anomaly in anomalies:
            if anomaly.anomaly_type == 'control_character':
                cleaned_text = cleaned_text.replace(anomaly.char, '')
        
        self.assertNotIn('\x00', cleaned_text)
        self.assertNotIn('\x01', cleaned_text)
        self.assertNotIn('\x02', cleaned_text)
        self.assertNotIn('\x03', cleaned_text)
        self.assertIn('Hello', cleaned_text)
        self.assertIn('World', cleaned_text)


class TestEncodingDetector(unittest.TestCase):
    """测试编码检测器"""
    
    def setUp(self):
        """设置测试环境"""
        self.detector = EncodingDetector()
    
    def test_detect_utf8(self):
        """测试检测UTF-8编码"""
        text = "Hello, World! 你好，世界！"
        encoding_info = self.detector.detect_encoding(text.encode('utf-8'))
        # 编码检测可能返回unknown，这是正常的
        self.assertIsNotNone(encoding_info.encoding)
    
    def test_detect_gbk(self):
        """测试检测GBK编码"""
        # 创建包含中文的GBK编码文本
        chinese_text = "你好，世界！"
        try:
            gbk_bytes = chinese_text.encode('gbk')
            encoding_info = self.detector.detect_encoding(gbk_bytes)
            # 编码检测可能返回unknown，这是正常的
            self.assertIsNotNone(encoding_info.encoding)
        except UnicodeEncodeError:
            # 如果系统不支持GBK编码，跳过测试
            self.skipTest("System does not support GBK encoding")
    
    def test_ensure_utf8_content(self):
        """测试确保UTF-8内容"""
        utf8_text = "Hello, World! 你好，世界！"
        # 传递字符串而不是字节
        result = self.detector.ensure_utf8_content(utf8_text, 'utf-8')
        # 结果应该是字符串
        self.assertIsInstance(result, str)
        self.assertEqual(result, utf8_text)
    
    def test_multiple_encoding_detection(self):
        """测试多编码检测"""
        text = "Hello, World!"
        encodings = ['utf-8', 'ascii', 'latin-1']
        
        for encoding in encodings:
            try:
                encoded_text = text.encode(encoding)
                encoding_info = self.detector.detect_encoding(encoded_text)
                self.assertIsNotNone(encoding_info.encoding)
            except UnicodeEncodeError:
                continue


class TestTextCleaner(unittest.TestCase):
    """测试文本清洗器"""
    
    def setUp(self):
        """设置测试环境"""
        self.cleaner = TextCleaner()
    
    def test_clean_normal_text(self):
        """测试清洗正常文本"""
        text = "Hello, World! 你好，世界！"
        result = self.cleaner.clean_file(self._create_temp_file(text))
        self.assertTrue(result.is_successful)
        self.assertEqual(result.cleaned_content, text)
    
    def test_clean_anomaly_text(self):
        """测试清洗异常文本"""
        text = "Hello\x00World\x01\x02\x03"
        result = self.cleaner.clean_file(self._create_temp_file(text))
        self.assertTrue(result.is_successful)
        self.assertGreater(result.removed_chars, 0)
        self.assertNotIn('\x00', result.cleaned_content)
        self.assertNotIn('\x01', result.cleaned_content)
        self.assertNotIn('\x02', result.cleaned_content)
        self.assertNotIn('\x03', result.cleaned_content)
    
    def test_clean_file(self):
        """测试清洗文件"""
        text = "Hello, World! 你好，世界！"
        temp_file = self._create_temp_file(text)
        
        result = self.cleaner.clean_file(temp_file)
        self.assertTrue(result.is_successful)
        self.assertEqual(result.cleaned_content, text)
        self.assertEqual(result.removed_chars, 0)
    
    def test_whitespace_normalization(self):
        """测试空白字符标准化"""
        text = "Hello   World\t\n\r"
        result = self.cleaner.clean_file(self._create_temp_file(text))
        self.assertTrue(result.is_successful)
        # 检查是否保留了基本内容
        self.assertIn('Hello', result.cleaned_content)
        self.assertIn('World', result.cleaned_content)
    
    def test_error_handling(self):
        """测试错误处理"""
        result = self.cleaner.clean_file("nonexistent_file.txt")
        self.assertFalse(result.is_successful)
        # 检查错误消息包含文件路径
        self.assertIn("nonexistent_file.txt", result.error_message)
    
    def _create_temp_file(self, content: str) -> str:
        """创建临时文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(content)
            return f.name
    
    def tearDown(self):
        """清理临时文件"""
        # 清理可能创建的临时文件
        pass


class CleanModuleIntegration(unittest.TestCase):
    """数据清洗模块集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.cleaner = TextCleaner()
        self.test_dir = tempfile.mkdtemp()
    
    def test_full_cleaning_workflow(self):
        """测试完整的清洗工作流"""
        # 创建包含异常字符的测试文本
        test_text = "Hello\x00World\x01\x02\x03\n你好，世界！"
        temp_file = self._create_temp_file(test_text)
        
        # 执行清洗
        result = self.cleaner.clean_file(temp_file)
        
        # 验证结果
        self.assertTrue(result.is_successful)
        self.assertGreater(result.removed_chars, 0)
        self.assertIn('Hello', result.cleaned_content)
        self.assertIn('World', result.cleaned_content)
        self.assertIn('你好，世界！', result.cleaned_content)
        
        # 清理
        os.unlink(temp_file)
    
    def test_encoding_consistency(self):
        """测试编码一致性"""
        text = "Hello, World! 你好，世界！"
        temp_file = self._create_temp_file(text)
        
        result = self.cleaner.clean_file(temp_file)
        
        # 验证输出编码为UTF-8
        self.assertEqual(result.encoding_info.encoding.lower(), 'utf-8')
        
        # 清理
        os.unlink(temp_file)
    
    def test_batch_cleaning(self):
        """测试批量清洗"""
        # 创建多个测试文件
        test_files = []
        for i in range(3):
            content = f"File {i}: Hello\x00World\x01"
            temp_file = self._create_temp_file(content)
            test_files.append(temp_file)
        
        # 执行批量清洗
        results = self.cleaner.batch_clean_files(self.test_dir)
        
        # 验证结果
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertTrue(result.is_successful)
            self.assertGreater(result.removed_chars, 0)
        
        # 清理
        for temp_file in test_files:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def _create_temp_file(self, content: str) -> str:
        """创建临时文件"""
        temp_file = os.path.join(self.test_dir, f"test_{len(os.listdir(self.test_dir))}.txt")
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)
        return temp_file
    
    def tearDown(self):
        """清理测试环境"""
        # 清理测试目录
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)


if __name__ == '__main__':
    unittest.main() 