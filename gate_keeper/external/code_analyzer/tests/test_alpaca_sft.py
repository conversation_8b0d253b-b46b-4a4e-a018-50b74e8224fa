"""
Alpaca SFT模块测试

测试Alpaca格式数据生成功能
"""

import json
import os
import tempfile
import unittest
from pathlib import Path

from core.data_gen.sft.alpaca_sft import (AlpacaSFTData, AlpacaSFTGenerator,
                                          AlpacaTaskType,
                                          AlpacaTemplateManager)
from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.generator import SFTDataGenerator


class TestAlpacaModels(unittest.TestCase):
    """测试Alpaca数据模型"""
    
    def test_alpaca_sft_data_creation(self):
        """测试AlpacaSFTData创建"""
        data = AlpacaSFTData(
            instruction="请补全代码",
            input="void test() {\n  int x = 1;\n",
            output="  int result = x + 1;\n",
            filepath="test.c",
            function_name="test",
            line_number=1,
            task_type=AlpacaTaskType.CODE_COMPLETION
        )
        
        self.assertEqual(data.instruction, "请补全代码")
        self.assertEqual(data.input, "void test() {\n  int x = 1;\n")
        self.assertEqual(data.output, "  int result = x + 1;\n")
        self.assertEqual(data.filepath, "test.c")
        self.assertEqual(data.function_name, "test")
        self.assertEqual(data.line_number, 1)
        self.assertEqual(data.task_type, AlpacaTaskType.CODE_COMPLETION)
    
    def test_alpaca_sft_data_validation(self):
        """测试AlpacaSFTData验证"""
        # 有效数据
        valid_data = AlpacaSFTData(
            instruction="请补全代码",
            input="void test() {\n",
            output="  return 0;\n}",
            filepath="test.c"
        )
        self.assertTrue(valid_data.validate())
        
        # 无效数据 - 缺少指令
        invalid_data1 = AlpacaSFTData(
            instruction="",
            input="void test() {\n",
            output="  return 0;\n}",
            filepath="test.c"
        )
        self.assertFalse(invalid_data1.validate())
        
        # 无效数据 - 缺少输出
        invalid_data2 = AlpacaSFTData(
            instruction="请补全代码",
            input="void test() {\n",
            output="",
            filepath="test.c"
        )
        self.assertFalse(invalid_data2.validate())
    
    def test_alpaca_sft_data_to_dict(self):
        """测试AlpacaSFTData转换为字典"""
        data = AlpacaSFTData(
            instruction="请补全代码",
            input="void test() {\n",
            output="  return 0;\n}",
            filepath="test.c",
            function_name="test",
            line_number=1,
            task_type=AlpacaTaskType.CODE_COMPLETION
        )
        
        # 测试标准Alpaca格式
        alpaca_dict = data.to_alpaca_dict()
        self.assertEqual(set(alpaca_dict.keys()), {'instruction', 'input', 'output'})
        self.assertEqual(alpaca_dict['instruction'], "请补全代码")
        self.assertEqual(alpaca_dict['input'], "void test() {\n")
        self.assertEqual(alpaca_dict['output'], "  return 0;\n}")
        
        # 测试完整格式
        full_dict = data.to_dict()
        self.assertIn('filepath', full_dict)
        self.assertIn('function_name', full_dict)
        self.assertIn('line_number', full_dict)
        self.assertIn('task_type', full_dict)


class TestAlpacaTemplateManager(unittest.TestCase):
    """测试Alpaca模板管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.template_manager = AlpacaTemplateManager()
    
    def test_get_all_templates(self):
        """测试获取所有模板"""
        templates = self.template_manager.get_all_templates()
        self.assertIsInstance(templates, dict)
        
        # 检查是否包含所有任务类型
        expected_task_types = [
            AlpacaTaskType.CODE_COMPLETION,
            AlpacaTaskType.CODE_EDIT,
            AlpacaTaskType.CODE_EXPLANATION,
            AlpacaTaskType.BUG_FIX,
            AlpacaTaskType.CODE_REFACTOR
        ]
        
        for task_type in expected_task_types:
            self.assertIn(task_type, templates)
    
    def test_get_template(self):
        """测试获取特定模板"""
        template = self.template_manager.get_template(AlpacaTaskType.CODE_COMPLETION)
        self.assertIsInstance(template, dict)
        self.assertIn('instruction', template)
        self.assertIn('input_template', template)
        self.assertIn('output_template', template)
    
    def test_format_template(self):
        """测试模板格式化"""
        test_data = {
            'filepath': 'test.c',
            'function_name': 'test_func',
            'line_number': 10,
            'language': 'c',
            'before': 'void test_func() {\n  int x = 1;\n',
            'expected': '  int result = x + 1;\n',
            'after': '  return result;\n}'
        }
        
        formatted = self.template_manager.format_template(
            AlpacaTaskType.CODE_COMPLETION, **test_data
        )
        
        self.assertIn('instruction', formatted)
        self.assertIn('input', formatted)
        self.assertIn('output', formatted)
        self.assertIn('test.c', formatted['input'])
        self.assertIn('test_func', formatted['input'])
        self.assertIn('10', formatted['input'])
    
    def test_create_custom_template(self):
        """测试创建自定义模板"""
        custom_instruction = "自定义指令"
        custom_input_template = "自定义输入模板: {filepath}"
        custom_output_template = "自定义输出: {expected}"
        
        self.template_manager.create_custom_template(
            custom_instruction,
            custom_input_template,
            custom_output_template,
            AlpacaTaskType.CODE_COMPLETION
        )
        
        template = self.template_manager.get_template(AlpacaTaskType.CODE_COMPLETION)
        self.assertEqual(template['instruction'], custom_instruction)
        self.assertEqual(template['input_template'], custom_input_template)
        self.assertEqual(template['output_template'], custom_output_template)
    
    def test_list_task_types(self):
        """测试列出任务类型"""
        task_types = self.template_manager.list_task_types()
        self.assertIsInstance(task_types, list)
        self.assertGreater(len(task_types), 0)
        
        # 检查是否包含所有预期的任务类型
        expected_types = [
            AlpacaTaskType.CODE_COMPLETION,
            AlpacaTaskType.CODE_EDIT,
            AlpacaTaskType.CODE_EXPLANATION,
            AlpacaTaskType.BUG_FIX,
            AlpacaTaskType.CODE_REFACTOR
        ]
        
        for expected_type in expected_types:
            self.assertIn(expected_type, task_types)


class TestAlpacaSFTGenerator(unittest.TestCase):
    """测试Alpaca SFT生成器"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp()
        self._create_test_files()
        
        # 初始化分析器和生成器
        self.analyzer = RealNPAnalyzer(self.test_dir)
        self.analyzer.analyze_project()
        
        self.sft_generator = SFTDataGenerator(self.analyzer)
        self.alpaca_generator = AlpacaSFTGenerator(self.sft_generator)
    
    def _create_test_files(self):
        """创建测试文件"""
        # 创建简单的测试文件
        test_content = """
void test_function() {
    int x = 1;
    int y = 2;
    int result = add(x, y);
    return result;
}

int add(int a, int b) {
    return a + b;
}
"""
        
        test_file = os.path.join(self.test_dir, "test.asm")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
    
    def test_generate_alpaca_sft_data(self):
        """测试生成Alpaca SFT数据"""
        alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
            max_samples=2,
            task_type=AlpacaTaskType.CODE_COMPLETION,
            include_metadata=True
        )
        
        self.assertIsInstance(alpaca_data, list)
        self.assertLessEqual(len(alpaca_data), 2)
        
        if alpaca_data:
            sample = alpaca_data[0]
            self.assertIsInstance(sample, AlpacaSFTData)
            self.assertTrue(sample.validate())
            self.assertEqual(sample.task_type, AlpacaTaskType.CODE_COMPLETION)
    
    def test_generate_multiple_task_types(self):
        """测试生成多种任务类型的数据"""
        task_types = [
            AlpacaTaskType.CODE_COMPLETION,
            AlpacaTaskType.CODE_EDIT,
            AlpacaTaskType.CODE_EXPLANATION
        ]
        
        for task_type in task_types:
            alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
                max_samples=1,
                task_type=task_type,
                include_metadata=True
            )
            
            if alpaca_data:
                sample = alpaca_data[0]
                self.assertEqual(sample.task_type, task_type)
                self.assertTrue(sample.validate())
    
    def test_save_to_json(self):
        """测试保存为JSON格式"""
        alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
            max_samples=1,
            task_type=AlpacaTaskType.CODE_COMPLETION
        )
        
        if alpaca_data:
            # 测试标准格式
            temp_file = os.path.join(self.test_dir, "test_alpaca.json")
            self.alpaca_generator.save_to_json(alpaca_data, temp_file, alpaca_format_only=True)
            
            with open(temp_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            self.assertIsInstance(saved_data, list)
            self.assertEqual(len(saved_data), 1)
            self.assertEqual(set(saved_data[0].keys()), {'instruction', 'input', 'output'})
            
            # 测试完整格式
            temp_file_full = os.path.join(self.test_dir, "test_alpaca_full.json")
            self.alpaca_generator.save_to_json(alpaca_data, temp_file_full, alpaca_format_only=False)
            
            with open(temp_file_full, 'r', encoding='utf-8') as f:
                saved_data_full = json.load(f)
            
            self.assertIsInstance(saved_data_full, list)
            self.assertEqual(len(saved_data_full), 1)
            self.assertIn('filepath', saved_data_full[0])
            self.assertIn('function_name', saved_data_full[0])
            self.assertIn('task_type', saved_data_full[0])
    
    def test_save_to_jsonl(self):
        """测试保存为JSONL格式"""
        alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
            max_samples=2,
            task_type=AlpacaTaskType.CODE_COMPLETION
        )
        
        if alpaca_data:
            temp_file = os.path.join(self.test_dir, "test_alpaca.jsonl")
            self.alpaca_generator.save_to_jsonl(alpaca_data, temp_file, alpaca_format_only=True)
            
            with open(temp_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.assertEqual(len(lines), len(alpaca_data))
            
            for line in lines:
                data = json.loads(line.strip())
                self.assertEqual(set(data.keys()), {'instruction', 'input', 'output'})
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
            max_samples=2,
            task_type=AlpacaTaskType.CODE_COMPLETION
        )
        
        stats = self.alpaca_generator.get_statistics(alpaca_data)
        
        self.assertIsInstance(stats, dict)
        self.assertIn('total_samples', stats)
        self.assertIn('avg_instruction_length', stats)
        self.assertIn('avg_input_length', stats)
        self.assertIn('avg_output_length', stats)
        self.assertIn('task_type_distribution', stats)
        self.assertIn('complexity_score_stats', stats)
        
        if alpaca_data:
            self.assertEqual(stats['total_samples'], len(alpaca_data))
            self.assertGreater(stats['avg_instruction_length'], 0)
            self.assertGreater(stats['avg_input_length'], 0)
            self.assertGreater(stats['avg_output_length'], 0)
    
    def test_data_validation(self):
        """测试数据验证"""
        # 测试空数据
        empty_stats = self.alpaca_generator.get_statistics([])
        self.assertEqual(empty_stats['total_samples'], 0)
        self.assertEqual(empty_stats['avg_instruction_length'], 0)
        self.assertEqual(empty_stats['avg_input_length'], 0)
        self.assertEqual(empty_stats['avg_output_length'], 0)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)


class TestAlpacaSFTIntegration(unittest.TestCase):
    """Alpaca SFT集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 使用examples/np目录进行集成测试
        examples_dir = Path(__file__).parent.parent / "examples" / "np"
        if not examples_dir.exists():
            self.skipTest("Examples directory not found")
        
        self.analyzer = RealNPAnalyzer(str(examples_dir))
        self.analyzer.analyze_project()
        
        self.sft_generator = SFTDataGenerator(self.analyzer)
        self.alpaca_generator = AlpacaSFTGenerator(self.sft_generator)
    
    def test_full_pipeline(self):
        """测试完整的数据生成流水线"""
        # 生成不同任务类型的数据
        task_types = [
            AlpacaTaskType.CODE_COMPLETION,
            AlpacaTaskType.CODE_EDIT,
            AlpacaTaskType.CODE_EXPLANATION
        ]
        
        all_results = {}
        
        for task_type in task_types:
            alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
                max_samples=3,
                task_type=task_type,
                include_metadata=True
            )
            
            all_results[task_type.value] = alpaca_data
            
            if alpaca_data:
                # 验证数据质量
                for item in alpaca_data:
                    self.assertTrue(item.validate())
                    self.assertEqual(item.task_type, task_type)
                    self.assertIsInstance(item.instruction, str)
                    self.assertIsInstance(item.input, str)
                    self.assertIsInstance(item.output, str)
        
        # 验证生成了数据
        total_samples = sum(len(data) for data in all_results.values())
        self.assertGreater(total_samples, 0)
    
    def test_data_quality(self):
        """测试数据质量"""
        alpaca_data = self.alpaca_generator.generate_alpaca_sft_data(
            max_samples=5,
            task_type=AlpacaTaskType.CODE_COMPLETION,
            include_metadata=True
        )
        
        if alpaca_data:
            # 检查数据完整性
            for item in alpaca_data:
                self.assertIsNotNone(item.filepath)
                self.assertIsNotNone(item.function_name)
                self.assertGreater(item.line_number, 0)
                self.assertGreater(item.complexity_score, 0)
                self.assertGreater(item.context_relevance_score, 0)
            
            # 检查数据多样性
            filepaths = set(item.filepath for item in alpaca_data)
            function_names = set(item.function_name for item in alpaca_data)
            
            # 应该有多个不同的文件和函数
            self.assertGreater(len(filepaths), 0)
            self.assertGreater(len(function_names), 0)


if __name__ == '__main__':
    unittest.main() 