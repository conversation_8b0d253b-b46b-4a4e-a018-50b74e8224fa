#!/usr/bin/env python3
"""
NP解析器结构体使用提取测试

测试NP解析器的extract_struct_usages功能
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.struct import StructUsage
from parsers.np_parser import NPParser


class TestNPStructUsages(unittest.TestCase):
    """NP解析器结构体使用提取测试类"""

    def setUp(self):
        """测试前准备"""
        try:
            self.parser = NPParser()
        except Exception as e:
            self.skipTest(f"NP解析器初始化失败: {e}")
        
        # 测试文件路径
        self.test_files = [
            "examples/np/switch1.asm",
            "examples/np/ipu_tbl_access.asm",
            "examples/np/ipu_proc.asm",
            "examples/np/main.asm"
        ]

    def test_struct_usages_extraction(self):
        """测试结构体使用提取功能"""
        print("🔍 测试NP解析器结构体使用提取功能")
        
        for test_file in self.test_files:
            if not os.path.exists(test_file):
                print(f"⚠️ 测试文件不存在: {test_file}")
                continue
            
            print(f"\n📄 测试文件: {test_file}")
            
            try:
                # 提取结构体使用
                struct_usages = self.parser.extract_struct_usages(test_file)
                
                print(f"✅ 成功提取 {len(struct_usages)} 个结构体使用")
                
                # 验证提取结果
                self.assertIsInstance(struct_usages, list)
                
                # 按使用类型分组
                usage_types = {}
                for usage in struct_usages:
                    self.assertIsInstance(usage, StructUsage)
                    usage_type = usage.usage_type
                    if usage_type not in usage_types:
                        usage_types[usage_type] = []
                    usage_types[usage_type].append(usage)
                
                # 显示统计信息
                print(f"📊 使用类型统计:")
                for usage_type, usages in usage_types.items():
                    print(f"  {usage_type}: {len(usages)} 个")
                
                # 显示详细信息（前5个）
                print(f"📋 详细信息:")
                for i, usage in enumerate(struct_usages[:5]):
                    print(f"  {i+1}. 类型: {usage.usage_type}")
                    print(f"     结构体: {usage.struct_name}")
                    print(f"     变量: {usage.variable_name}")
                    print(f"     函数: {usage.function_name}")
                    print(f"     行号: {usage.line}")
                    print(f"     代码: {usage.code[:50]}...")
                    print()
                
            except Exception as e:
                print(f"❌ 提取失败: {e}")
                self.fail(f"结构体使用提取失败: {e}")

    def test_specific_patterns(self):
        """测试特定的结构体使用模式"""
        print("\n🎯 测试特定结构体使用模式")
        
        # 创建测试代码
        test_code = """
void testFunction() {
    // 字段访问
    cmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_IPV4;
    move rsInfo.bQos = qos;
    
    // 赋值操作
    move rsInfo.LbHashKey = g_rsUpfDesc.IpTraceId;
    move ksNdhKey.Tid = 8;
    
    // 函数调用中的结构体参数
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_ARP_REPLY, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE191);
}
"""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.asm', delete=False, encoding='utf-8') as f:
            f.write(test_code)
            temp_file = f.name
        
        try:
            print(f"📝 创建测试文件: {temp_file}")
            
            # 提取结构体使用
            struct_usages = self.parser.extract_struct_usages(temp_file)
            
            print(f"✅ 提取到 {len(struct_usages)} 个结构体使用")
            
            # 验证提取结果
            self.assertIsInstance(struct_usages, list)
            
            # 显示详细信息
            for i, usage in enumerate(struct_usages):
                print(f"\n  {i+1}. 使用类型: {usage.usage_type}")
                print(f"     结构体: {usage.struct_name}")
                print(f"     变量: {usage.variable_name}")
                print(f"     函数: {usage.function_name}")
                print(f"     行号: {usage.line}")
                print(f"     代码: {usage.code}")
                
                # 验证StructUsage对象
                self.assertIsInstance(usage, StructUsage)
                self.assertIsInstance(usage.usage_type, str)
                self.assertIsInstance(usage.struct_name, str)
                self.assertIsInstance(usage.variable_name, str)
                self.assertIsInstance(usage.function_name, (str, type(None)))
                self.assertIsInstance(usage.file_path, str)
                self.assertIsInstance(usage.line, list)
                self.assertEqual(len(usage.line), 2)
                self.assertIsInstance(usage.code, str)
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"🧹 清理临时文件: {temp_file}")

    def test_integration_with_analyzer(self):
        """测试与分析器的集成"""
        print("\n🔗 测试与分析器的集成")
        
        try:
            from core.data_gen.sft.analyzer import RealNPAnalyzer

            # 初始化分析器
            examples_dir = os.path.abspath("examples/np")
            if not os.path.exists(examples_dir):
                self.skipTest(f"示例目录不存在: {examples_dir}")
            
            analyzer = RealNPAnalyzer(examples_dir)
            
            # 分析项目
            analyzer.analyze_project()
            
            print("✅ 项目分析完成")
            
            # 检查是否提取到结构体使用
            if hasattr(analyzer, 'repo_index') and analyzer.repo_index:
                struct_usages = analyzer.repo_index.get_struct_usages()
                print(f"📊 从仓库索引中获取到 {len(struct_usages)} 个结构体使用")
                
                # 验证结果
                self.assertIsInstance(struct_usages, list)
                
                # 显示一些示例
                for i, usage in enumerate(struct_usages[:5]):
                    print(f"  {i+1}. {usage.struct_name} - {usage.usage_type} - {usage.file_path}")
                    self.assertIsInstance(usage, StructUsage)
        
        except ImportError:
            self.skipTest("RealNPAnalyzer不可用")
        except Exception as e:
            print(f"❌ 集成测试失败: {e}")
            self.fail(f"集成测试失败: {e}")


def main():
    """主函数"""
    print("🔍 NP解析器结构体使用提取测试")
    print("=" * 80)
    
    # 运行测试
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main() 