#!/usr/bin/env python3
"""
运行所有数据清洗相关测试

包括：
1. 数据清洗模块基础功能测试
2. SFT数据生成与数据清洗集成测试
3. 性能测试
"""

import os
import subprocess
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def run_test_module(module_name, description):
    """运行指定的测试模块"""
    print(f"\n🧪 {description}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 运行测试模块
        result = subprocess.run([
            sys.executable, 
            os.path.join(os.path.dirname(__file__), module_name)
        ], capture_output=True, text=True, timeout=300)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出测试结果
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        print(f"⏱️  测试耗时: {duration:.2f}秒")
        
        return result.returncode == 0, duration
        
    except subprocess.TimeoutExpired:
        print(f"❌ 测试超时: {module_name}")
        return False, 300
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False, 0


def run_quick_validation():
    """运行快速验证测试"""
    print("\n🔍 快速验证测试")
    print("=" * 60)
    
    try:
        # 导入模块验证
        from core.data_gen.clean import (AnomalyDetector, EncodingDetector,
                                         TextCleaner)

        # 创建实例验证
        detector = AnomalyDetector()
        encoder = EncodingDetector()
        cleaner = TextCleaner()
        
        # 基本功能验证
        test_text = "Hello\x00World!\uFFFD你好"
        anomalies = detector.detect_anomalies(test_text, "test.txt")
        
        if len(anomalies) > 0:
            print("✅ 异常字符检测功能正常")
        else:
            print("❌ 异常字符检测功能异常")
            return False
        
        print("✅ 所有模块导入和基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 快速验证失败: {e}")
        return False


def check_test_coverage():
    """检查测试覆盖率"""
    print("\n📊 测试覆盖率检查")
    print("=" * 60)
    
    # 检查测试文件是否存在
    test_files = [
        "test_clean_module.py",
        "test_sft_with_clean.py"
    ]
    
    missing_files = []
    for test_file in test_files:
        if not os.path.exists(os.path.join(os.path.dirname(__file__), test_file)):
            missing_files.append(test_file)
    
    if missing_files:
        print(f"❌ 缺少测试文件: {missing_files}")
        return False
    
    # 检查核心模块是否存在
    core_modules = [
        "core/data_gen/clean/anomaly_detector.py",
        "core/data_gen/clean/encoding_detector.py", 
        "core/data_gen/clean/text_cleaner.py"
    ]
    
    missing_modules = []
    for module in core_modules:
        if not os.path.exists(module):
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少核心模块: {missing_modules}")
        return False
    
    print("✅ 测试覆盖率检查通过")
    return True


def generate_test_report(results):
    """生成测试报告"""
    print("\n📋 测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success, _ in results if success)
    failed_tests = total_tests - passed_tests
    total_time = sum(duration for _, duration in results)
    
    print(f"总测试模块数: {total_tests}")
    print(f"通过测试数: {passed_tests}")
    print(f"失败测试数: {failed_tests}")
    print(f"总测试时间: {total_time:.2f}秒")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！数据清洗功能正常工作。")
        return True
    else:
        print(f"\n⚠️  有 {failed_tests} 个测试失败，请检查相关功能。")
        return False


def main():
    """主函数"""
    print("🚀 开始运行所有数据清洗相关测试")
    print("=" * 60)
    
    # 检查测试覆盖率
    if not check_test_coverage():
        print("❌ 测试覆盖率检查失败，退出测试")
        sys.exit(1)
    
    # 运行快速验证
    if not run_quick_validation():
        print("❌ 快速验证失败，退出测试")
        sys.exit(1)
    
    # 定义测试模块
    test_modules = [
        ("test_clean_module.py", "数据清洗模块基础功能测试"),
        ("test_sft_with_clean.py", "SFT数据生成与数据清洗集成测试")
    ]
    
    # 运行所有测试
    results = []
    for module_name, description in test_modules:
        success, duration = run_test_module(module_name, description)
        results.append((success, duration))
    
    # 生成测试报告
    overall_success = generate_test_report(results)
    
    # 输出详细结果
    print("\n📈 详细测试结果:")
    for i, ((success, duration), (_, description)) in enumerate(zip(results, test_modules)):
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {i+1}. {description}: {status} ({duration:.2f}s)")
    
    # 返回结果
    if overall_success:
        print("\n🎯 测试总结: 所有数据清洗功能测试通过，功能正常工作！")
        sys.exit(0)
    else:
        print("\n⚠️  测试总结: 部分测试失败，请检查相关功能。")
        sys.exit(1)


if __name__ == "__main__":
    main() 