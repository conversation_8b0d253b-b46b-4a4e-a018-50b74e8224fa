{"file_path": "examples/np/hello.asm", "file_size": 321, "node_statistics": {"program": 1, "statement": 2, "include_statement": 1, "#include": 1, "include_path": 1, "\"": 10, "file_name": 1, "define_statement": 1, "keyword_define": 1, "#define": 1, "identifier": 24, "constant": 6, "float_constant": 1, "ERROR": 9, "linkage_type": 1, "static": 1, "void": 2, "parameters": 1, "(": 8, "parameter": 1, "type": 1, ")": 8, "{": 4, "argument_expression": 5, ",": 4, "unary_expression": 8, "!": 2, "primary_expression": 7, "variable": 7, ";": 7, "}": 4, "expression": 9, "expression_assignment": 3, "lvalue": 3, "=": 3, "decimal_constant": 5, "expression_function_call": 2, "argument_list": 2, "if": 1, "parenthesized_expression": 1, "condition_expression": 1, "or_expression": 1, "and_expression": 1, "equality_expression": 1, "relational_expression": 3, ">": 2, "else": 1, "<=": 1, "comment": 1}, "error_nodes": [{"start_point": [2, 0], "end_point": [19, 1], "text": "static void greet(char name) \n{\n    printf(\"Hello, %s!\\n\", name);\n}\n\nvoid main() \n{\n    var1 = 10;\n "}, {"start_point": [4, 11], "end_point": [4, 12], "text": "\""}, {"start_point": [4, 19], "end_point": [7, 9], "text": "%s!\\n\", name);\n}\n\nvoid main"}, {"start_point": [4, 19], "end_point": [4, 20], "text": "%"}, {"start_point": [7, 10], "end_point": [14, 19], "text": ") \n{\n    var1 = 10;\n    var2 = 20;\n    sum = math_bundle(var1, var2);\n    greet(\"Hello, <PERSON>!\");\n\n "}, {"start_point": [12, 10], "end_point": [12, 11], "text": "\""}, {"start_point": [12, 23], "end_point": [12, 25], "text": "!\""}, {"start_point": [15, 14], "end_point": [16, 12], "text": "\"sum > 20\");\n    } else {"}, {"start_point": [17, 14], "end_point": [17, 15], "text": "\""}], "function_nodes": {"statement_function_def": [], "declare_function": [], "function_definition": [], "function_declaration": [], "identifier": [{"text": "PI", "start_point": [1, 8], "end_point": [1, 10]}, {"text": "greet", "start_point": [2, 12], "end_point": [2, 17]}, {"text": "char", "start_point": [2, 18], "end_point": [2, 22]}, {"text": "name", "start_point": [2, 23], "end_point": [2, 27]}, {"text": "printf", "start_point": [4, 4], "end_point": [4, 10]}], "parameters": [{"text": "(char name)", "start_point": [2, 17], "end_point": [2, 28]}], "parameter_list": []}, "extracted_functions": [{"name": "greet", "start_line": 3, "end_line": 6, "parameters": [], "return_type": "void"}, {"name": "main", "start_line": 8, "end_line": 20, "parameters": [], "return_type": "void"}], "extracted_calls": []}