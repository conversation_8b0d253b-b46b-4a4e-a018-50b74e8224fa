"""
Code Range Model

代码范围值对象，表示代码在文件中的位置范围
"""

from typing import List

from pydantic.dataclasses import dataclass


@dataclass(frozen=True)
class CodeRange:
    """代码范围值对象"""
    
    start_line: int
    end_line: int
    
    def __post_init__(self):
        if self.start_line <= 0:
            raise ValueError("起始行必须大于0")
        if self.end_line < self.start_line:
            raise ValueError("结束行不能小于起始行")
    
    @property
    def line_count(self) -> int:
        """行数"""
        return self.end_line - self.start_line + 1
    
    def contains_line(self, line_number: int) -> bool:
        """检查是否包含指定行号"""
        return self.start_line <= line_number <= self.end_line
    
    def overlaps_with(self, other: 'CodeRange') -> bool:
        """检查是否与另一个范围重叠"""
        return not (self.end_line < other.start_line or other.end_line < self.start_line)
    
    def get_affected_lines(self, changed_lines: List[int]) -> List[int]:
        """获取在此范围内的变更行"""
        return [line for line in changed_lines if self.contains_line(line)]
    
    def __str__(self) -> str:
        return f"{self.start_line}-{self.end_line}" 