"""
Code Element Model

代码元素基础模型
"""

from typing import Optional

from pydantic import BaseModel


class CodeElement(BaseModel):
    """代码元素基础类"""
    name: str                     # 函数名、类名、变量名等
    type: str                     # e.g., 'function', 'class', 'variable'
    start_line: int               # 起始行号
    end_line: int                 # 结束行号
    parent_name: Optional[str] = None  # 所属结构（如类名），可选
    filepath: Optional[str] = None     # 所在文件路径
    code: Optional[str] = None         # 原始代码文本（用于上下文分析）


class CheckItem(CodeElement):
    """
    表示代码检查项，继承自 CodeElement，便于类型区分和扩展。
    """
    pass 