"""
Function Model

函数相关的数据模型
"""

from dataclasses import dataclass
from typing import List, Optional

from pydantic import BaseModel

from .code_range import CodeRange


@dataclass(frozen=True)
class Parameter:
    """参数值对象"""
    name: str
    type_hint: Optional[str] = None


@dataclass(frozen=True)
class FunctionSignature:
    """函数签名值对象"""
    name: str
    parameters: List[Parameter]
    return_type: Optional[str] = None


class Function(BaseModel):
    """函数模型"""
    name: str
    range: CodeRange
    filepath: str
    signature: FunctionSignature
    code: Optional[str] = None
    parent_class: Optional[str] = None
    is_declaration: bool = False

    @classmethod
    def create_simple(cls, name: str, start_line: int, end_line: int, filepath: str, 
                     signature: FunctionSignature, code: str = None) -> 'Function':
        """创建简单的函数实例"""
        code_range = CodeRange(start_line=start_line, end_line=end_line)
        return cls(
            name=name,
            range=code_range,
            filepath=filepath,
            signature=signature,
            code=code
        ) 