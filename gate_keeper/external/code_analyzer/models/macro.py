"""
Macro Model

宏定义模型，表示C语言中的宏定义
"""

from dataclasses import dataclass
from typing import List, Optional

from pydantic import BaseModel

from .code_range import CodeRange


@dataclass(frozen=True)
class MacroParameter:
    """宏参数值对象"""
    name: str


class Macro(BaseModel):
    """宏定义模型"""
    name: str                    # 宏名称
    type: str                    # 宏类型: "object", "function", "conditional"
    filepath: str                # 所在文件路径
    range: CodeRange             # 代码范围
    parameters: List[str] = []   # 参数列表（仅函数宏）
    value: Optional[str] = None  # 宏的值/定义内容
    code: Optional[str] = None   # 原始代码文本
    
    @classmethod
    def create_object_macro(cls, name: str, filepath: str, start_line: int, end_line: int, 
                           value: str, code: str = None) -> 'Macro':
        """创建对象宏"""
        code_range = CodeRange(start_line=start_line, end_line=end_line)
        return cls(
            name=name,
            type="object",
            filepath=filepath,
            range=code_range,
            value=value,
            code=code
        )
    
    @classmethod
    def create_function_macro(cls, name: str, filepath: str, start_line: int, end_line: int,
                             parameters: List[str], value: str, code: str = None) -> 'Macro':
        """创建函数宏"""
        code_range = CodeRange(start_line=start_line, end_line=end_line)
        return cls(
            name=name,
            type="function",
            filepath=filepath,
            range=code_range,
            parameters=parameters,
            value=value,
            code=code
        )
    
    @classmethod
    def create_conditional_macro(cls, name: str, filepath: str, start_line: int, end_line: int,
                                code: str = None) -> 'Macro':
        """创建条件编译宏"""
        code_range = CodeRange(start_line=start_line, end_line=end_line)
        return cls(
            name=name,
            type="conditional",
            filepath=filepath,
            range=code_range,
            code=code
        ) 