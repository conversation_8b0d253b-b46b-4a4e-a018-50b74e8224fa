"""
Repository Model

代码仓库模型
"""

from typing import Optional

from pydantic import BaseModel

from .module import CodeModule


class Repository(BaseModel):
    """代码仓库"""
    repo_dir: str = ""
    branch: str = ""
    commit_sha: str = ""
    modules: dict[str, CodeModule] = {}

    def get_module(self, path: str) -> Optional[CodeModule]:
        """获取模块"""
        return self.modules.get(path)
    
    def add_module(self, module: CodeModule):
        """添加模块"""
        self.modules[module.path] = module

    def find_function_definition(self, func_name: str) -> Optional[str]:
        """查找函数定义所在的文件"""
        for module in self.modules.values():
            if any(f.name == func_name for f in module.functions):
                return module.path
        return None

    def build_cross_module_call_map(self):
        """构建跨文件调用映射"""
        call_map = []
        for module in self.modules.values():
            for call in module.calls:
                callee_file = self.find_function_definition(call.callee)
                if callee_file and callee_file != call.file_path:
                    call_map.append({
                        "caller": call.caller,
                        "caller_file": call.file_path,
                        "callee": call.callee,
                        "callee_file": callee_file
                    })
        return call_map 