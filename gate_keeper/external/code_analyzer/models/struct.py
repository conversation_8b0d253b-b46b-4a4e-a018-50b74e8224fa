"""
Struct Models

结构体相关的数据模型
"""

from typing import ClassVar, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from .element import CodeElement


class StructField(BaseModel):
    """结构体字段模型"""
    
    name: str = Field(..., description="字段名称")
    type_name: str = Field(..., description="字段类型")
    is_pointer: bool = Field(default=False, description="是否为指针类型")
    is_array: bool = Field(default=False, description="是否为数组类型")
    array_size: Optional[str] = Field(default=None, description="数组大小（如果适用）")
    comment: Optional[str] = Field(default=None, description="字段注释")


class Struct(CodeElement):
    """结构体定义模型"""
    
    name: str = Field(..., description="结构体名称")
    fields: List[StructField] = Field(default_factory=list, description="结构体字段列表")
    start_line: int = Field(..., description="开始行号")
    end_line: int = Field(..., description="结束行号")
    filepath: str = Field(..., description="文件路径")
    code: str = Field(..., description="结构体完整代码")
    is_typedef: bool = Field(default=False, description="是否为typedef定义")
    typedef_name: Optional[str] = Field(default=None, description="typedef名称（如果适用）")
    
    @classmethod
    def create_simple(cls, name: str, fields: List[StructField], 
                     start_line: int, end_line: int, filepath: str, 
                     code: str, is_typedef: bool = False, 
                     typedef_name: Optional[str] = None) -> "Struct":
        """
        创建简单的结构体实例
        
        Args:
            name: 结构体名称
            fields: 字段列表
            start_line: 开始行号
            end_line: 结束行号
            filepath: 文件路径
            code: 结构体代码
            is_typedef: 是否为typedef
            typedef_name: typedef名称
            
        Returns:
            Struct: 结构体实例
        """
        return cls(
            name=name,
            type="struct",
            fields=fields,
            start_line=start_line,
            end_line=end_line,
            filepath=filepath,
            code=code,
            is_typedef=is_typedef,
            typedef_name=typedef_name
        )


class StructRelation(BaseModel):
    """结构体关系模型"""
    
    source_struct: str = Field(..., description="源结构体名称")
    target_struct: str = Field(..., description="目标结构体名称")
    relation_type: str = Field(..., description="关系类型")
    field_name: Optional[str] = Field(default=None, description="关联字段名称")
    file_path: str = Field(..., description="文件路径")
    line: List[int] = Field(..., description="行号范围")
    code: str = Field(..., description="相关代码")
    
    model_config = ConfigDict(
        json_encoders={
            'Struct': lambda v: v.dict(),
            'StructField': lambda v: v.dict()
        }
    )


class StructUsage(BaseModel):
    """结构体使用模型"""
    
    struct_name: str = Field(..., description="结构体名称")
    usage_type: str = Field(..., description="使用类型")
    variable_name: Optional[str] = Field(default=None, description="变量名称")
    function_name: Optional[str] = Field(default=None, description="函数名称")
    file_path: str = Field(..., description="文件路径")
    line: List[int] = Field(..., description="行号范围")
    code: str = Field(..., description="使用代码")
    
    usage_types: ClassVar[Dict[str, str]] = {
        "declaration": "变量声明",
        "parameter": "函数参数",
        "return_type": "返回类型",
        "field_access": "字段访问",
        "pointer_cast": "指针转换",
        "sizeof": "sizeof操作",
        "assignment": "赋值操作"
    } 