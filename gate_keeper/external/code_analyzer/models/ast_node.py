"""
AST Node Model

AST节点模型，提供细粒度的语法树节点抽象
"""

from typing import Dict, List, Optional

from pydantic import BaseModel


class ASTNode(BaseModel):
    """AST节点基础模型"""
    type: str                     # 节点类型，如 'function_definition', 'call_expression'
    kind: Optional[str] = None    # 节点种类，如 'function', 'class', 'variable'
    name: Optional[str] = None    # 节点名称
    text: Optional[str] = None    # 原始代码文本
    children: Optional[List["ASTNode"]] = None  # 子节点列表
    location: Optional[Dict] = None  # 位置信息 {start_line, end_line, start_column, end_column}
    language: Optional[str] = None   # 编程语言
    file: Optional[str] = None       # 文件路径
    module: Optional[str] = None     # 模块路径
    metadata: Optional[Dict] = None  # 额外元数据


class GraphCodeNode(BaseModel):
    """图代码节点模型"""
    id: str                       # 节点唯一标识
    name: str                     # 节点名称
    type: str                     # 节点类型
    language: str                 # 编程语言
    kind: str                     # 节点种类
    file: Optional[str] = None    # 文件路径
    module: Optional[str] = None  # 模块路径
    definition: Optional[str] = None  # 定义代码
    location: Optional[Dict] = None   # 位置信息
    metadata: Optional[Dict] = None   # 额外元数据


class CodeModule(BaseModel):
    """代码模块模型"""
    path: str                     # 模块路径
    language: str                 # 编程语言
    node_ids: List[str] = []      # 节点ID列表
    imports: List[str] = []       # 导入列表
    exports: List[str] = []       # 导出列表
    metadata: Dict = {}           # 额外元数据 