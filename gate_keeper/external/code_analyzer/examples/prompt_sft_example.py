#!/usr/bin/env python3
"""
提示词SFT数据生成示例

演示如何使用提示词SFT数据生成模块
"""

import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.extract_strategies import (ExtractionConfig,
                                                  ExtractionStrategy)
from core.data_gen.sft.generator import SFTDataGenerator
from core.data_gen.sft.prompt_sft.generator import PromptSFTGenerator
from core.data_gen.sft.prompt_sft.models import ModelType


def demonstrate_streaming_advantage(prompt_generator, output_dir, model_type):
    """演示流式保存的优势（内存使用对比）"""
    print(f"\n=== 流式保存优势演示 ({model_type.value}) ===")
    
    # 模拟大量数据生成场景
    large_sample_count = 10000
    
    print(f"模拟生成 {large_sample_count} 个样本...")
    
    # 方式1：传统批量方式（需要大量内存）
    print("\n方式1：传统批量方式")
    print("  - 所有数据加载到内存")
    print("  - 一次性保存到文件")
    print("  - 内存占用：高")
    
    # 方式2：流式方式（内存友好）
    print("\n方式2：流式方式")
    print("  - 逐条生成和保存")
    print("  - 内存占用：恒定")
    print("  - 支持中断恢复")
    
    # 演示流式保存
    streaming_file = output_dir / f"streaming_demo_{model_type.value}.jsonl"
    print(f"\n开始流式保存演示到: {streaming_file}")
    
    with prompt_generator.create_streaming_saver(
        str(streaming_file),
        max_items_per_file=1000,
        file_format="jsonl"
    ) as saver:
        for i in range(min(large_sample_count, 100)):  # 限制演示数量
            # 模拟生成单个样本
            sample_data = prompt_generator.generate_prompt_sft_data(
                max_samples=1,
                model_type=model_type
            )
            
            if sample_data:
                saver.save_sft_item(sample_data[0])
                if (i + 1) % 10 == 0:
                    print(f"    已保存 {i + 1} 个样本...")
    
    print("流式保存演示完成！")


def main():
    """主函数"""
    print("=== 提示词SFT数据生成示例 ===\n")

    # ====== 参数区（集中管理） ======
    # 示例代码目录
    examples_dir = project_root / "examples" / "np"
    # 输出目录
    output_dir = project_root / "examples" / "prompt_sft_output"
    # 每个模型生成的最大样本数
    max_samples = 500  # 增加样本数量以包含更多策略的数据
    # 要测试的模型类型
    models_to_test = [
        # ModelType.QWEN_CODER,
        ModelType.DEEPSEEK_CODER
    ]
    # 任务类型
    task_type = "code_completion"
    # 提取策略配置
    extraction_strategies = [
        ExtractionStrategy.WATERFALL_SEQUENTIAL,      # 瀑布式顺序遮盖
        ExtractionStrategy.RANDOM_INPLACE,            # 随机插入/替换
        ExtractionStrategy.COMMENT_TO_CODE,           # 注释驱动补全
        ExtractionStrategy.CODE_TO_COMMENT,           # 注释生成
        ExtractionStrategy.CONTROL_FLOW_HALLUCINATION, # 逻辑填空
    ]
    
    # 策略样本数量配置（可选）
    # None表示无限制，数字表示最大样本数
    strategy_max_samples = {
        ExtractionStrategy.WATERFALL_SEQUENTIAL: None,      # 无限制
        ExtractionStrategy.RANDOM_INPLACE: 50,              # 最多50个样本
        ExtractionStrategy.COMMENT_TO_CODE: None,           # 无限制
        ExtractionStrategy.CODE_TO_COMMENT: 20,             # 最多20个样本
        ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: None, # 无限制
    }
    
    # 变量数量限制配置
    max_hidden_variables = 8  # 最大隐藏变量数量，防止上下文爆炸
    variable_selection_strategy = "priority_based"  # 变量选择策略: "priority_based", "random", "first_n"
    
    # 数据保存配置
    save_method = "streaming"  # 保存方式: "batch"(批量), "streaming"(流式), "both"(两种都保存)
    max_items_per_file = 300  # 每文件最大条数
    file_format = "jsonl"      # 文件格式: "json", "jsonl"
    # ====== 参数区结束 ======

    # 1. 初始化SFT分析器
    print("1. 初始化SFT分析器...")

    # 2. 分析示例代码
    print("2. 分析示例代码...")
    if not examples_dir.exists():
        print(f"错误: 示例目录不存在: {examples_dir}")
        return
    print(f"  使用示例目录: {examples_dir}")
    analyzer = RealNPAnalyzer(str(examples_dir))
    analyzer.analyze_project()
    print("  代码分析已完成（通过SFT分析器）")

    # 3. 初始化SFT数据生成器
    print("\n3. 初始化SFT数据生成器...")
    sft_generator = SFTDataGenerator(analyzer)

    # 4. 初始化提示词SFT数据生成器
    print("4. 初始化提示词SFT数据生成器...")
    prompt_generator = PromptSFTGenerator(
        sft_generator,
        max_hidden_variables=max_hidden_variables,
        variable_selection_strategy=variable_selection_strategy
    )
    
    # 显示支持的任务
    supported_tasks = prompt_generator.get_supported_tasks()
    print(f"  支持的任务类型: {', '.join(supported_tasks)}")
    print(f"  当前使用任务: {task_type}")
    print(f"  变量数量限制: 最多 {max_hidden_variables} 个隐藏变量")
    print(f"  变量选择策略: {variable_selection_strategy}")
    print(f"  数据保存方式: {save_method}")
    print(f"  每文件最大条数: {max_items_per_file}")
    print(f"  文件格式: {file_format}")

    # 5. 生成不同模型的提示词SFT数据
    print("\n5. 生成提示词SFT数据...")
    all_results = {}
    for model_type in models_to_test:
        print(f"\n  生成 {model_type.value} 模型数据...")
        try:
            # 使用多种提取策略生成数据
            enhanced_data = sft_generator.generate_enhanced_sft_data_multi_strategy(
                max_samples=max_samples,
                strategies=extraction_strategies,
                strategy_max_samples=strategy_max_samples
            )
            
            # 生成提示词SFT数据
            prompt_sft_data = prompt_generator.generate_prompt_sft_data(
                max_samples=max_samples,
                model_type=model_type,
                task_type=task_type,
                strategies=extraction_strategies,
                strategy_max_samples=strategy_max_samples
            )
            
            # 分析策略分布
            strategy_counts = {}
            for item in prompt_sft_data:
                strategy = item.generation_strategy
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            
            print(f"    策略分布: {strategy_counts}")
            
            stats = prompt_generator.get_statistics(prompt_sft_data)
            all_results[model_type.value] = {
                "data": prompt_sft_data,
                "stats": stats
            }
            print(f"    生成了 {len(prompt_sft_data)} 个样本")
            print(f"    平均提示词长度: {stats['avg_prompt_length']:.1f}")
            print(f"    平均补全长度: {stats['avg_completion_length']:.1f}")
            print(f"    平均复杂度分数: {stats['avg_complexity_score']:.3f}")
            print(f"    平均上下文相关性: {stats['avg_context_relevance_score']:.3f}")
        except Exception as e:
            print(f"    生成失败: {e}")
            continue

    # 6. 保存结果
    print("\n6. 保存结果...")
    output_dir.mkdir(exist_ok=True)
    
    # 使用参数区配置的保存设置
    
    for model_type, result in all_results.items():
        if result["data"]:
            print(f"\n  保存 {model_type} 数据...")
            
            if save_method in ["batch", "both"]:
                # 批量保存（传统方式）
                json_file = output_dir / f"prompt_sft_{model_type}.json"
                saved_files = prompt_generator.save_to_json(
                    result["data"], 
                    str(json_file),
                    max_items_per_file=max_items_per_file
                )
                print(f"    批量保存完成，文件数: {len(saved_files)}")
            
            if save_method in ["streaming", "both"]:
                # 流式保存（内存友好）
                streaming_file = output_dir / f"prompt_sft_{model_type}.{file_format}"
                print(f"    开始流式保存到: {streaming_file}")
                
                with prompt_generator.create_streaming_saver(
                    str(streaming_file),
                    max_items_per_file=max_items_per_file,
                    file_format=file_format
                ) as saver:
                    for i, item in enumerate(result["data"]):
                        saver.save_sft_item(item)
                        if (i + 1) % 100 == 0:
                            print(f"      已保存 {i + 1} 个样本...")
                
                print(f"    流式保存完成")
            
            # 保存统计信息
            stats_file = output_dir / f"stats_{model_type}.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(result["stats"], f, ensure_ascii=False, indent=2)
            print(f"    已保存统计信息到: {stats_file}")

    # 6.5. 流式保存优势演示（可选）
    if save_method in ["streaming", "both"] and all_results:
        # 选择第一个有数据的模型进行演示
        first_model = next(iter(all_results.keys()))
        first_model_type = next(mt for mt in models_to_test if mt.value == first_model)
        demonstrate_streaming_advantage(prompt_generator, output_dir, first_model_type)

    # 7. 显示示例数据
    print("\n7. 显示示例数据...")
    for model_type, result in all_results.items():
        if result["data"]:
            print(f"\n=== {model_type} 示例 ===")
            sample = result["data"][0]
            print(f"文件: {sample.filepath}")
            print(f"函数: {sample.function_name}")
            print(f"行号: {sample.line_number}")
            print(f"模板: {sample.template_name}")
            print(f"生成策略: {sample.generation_strategy}")
            print(f"复杂度分数: {sample.complexity_score:.3f}")
            print(f"上下文相关性: {sample.context_relevance_score:.3f}")
            print("\n提示词 (前100字符):")
            print(sample.prompt[:100] + "..." if len(sample.prompt) > 100 else sample.prompt)
            print("\n补全内容:")
            print(sample.completion)
            print("\n上下文信息:")
            for ctx_type, section in sample.context_sections.items():
                if section.functions or section.variables or section.macros or section.structs:
                    print(f"  {ctx_type.value}: {section.to_text()}")
            break  # 只显示第一个模型的示例
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()

"""
使用说明：

1. 保存方式配置：
   - save_method = "batch": 只使用传统批量保存
   - save_method = "streaming": 只使用流式保存（推荐）
   - save_method = "both": 两种方式都使用

2. 文件格式选择：
   - file_format = "json": 传统JSON数组格式
   - file_format = "jsonl": 每行一个JSON（推荐，支持流式处理）

3. 性能优化：
   - 大数据集推荐使用 streaming + jsonl 组合
   - 可以通过调整 max_items_per_file 控制文件大小
   - 流式保存适合内存受限的环境

4. 输出文件：
   - 批量保存：prompt_sft_{model_type}.json
   - 流式保存：prompt_sft_{model_type}.{file_format}
   - 统计信息：stats_{model_type}.json
   - 演示文件：streaming_demo_{model_type}.jsonl
""" 