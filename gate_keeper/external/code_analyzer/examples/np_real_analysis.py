"""
NP真实项目分析示例

重构后的示例脚本，只保留main执行函数
核心功能已移至 core/data_gen/sft 模块
"""

import json
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.data_gen.sft import (DataQualityValidator, RealNPAnalyzer,
                               SFTDataGenerator)


def main():
    """主执行函数"""
    print("🚀 NP真实项目SFT数据生成")
    print("=" * 60)
    
    # 配置参数
    project_path = "examples/np"
    output_dir = "real_analysis_output"
    max_samples = 100
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. 初始化分析器
        print(f"\n📁 分析项目: {project_path}")
        analyzer = RealNPAnalyzer(project_path)
        
        # 2. 分析项目
        print("🔍 开始项目分析...")
        analysis_result = analyzer.analyze_project()
        
        if not analysis_result:
            print("❌ 项目分析失败：未找到NP文件")
            return
        
        print(f"✅ 项目分析完成，找到 {len(analyzer._functions_cache)} 个文件")
        
        # 3. 提取函数和包信息
        print("\n📊 提取函数和包信息...")
        functions_data = analyzer.extract_functions_and_bundles_real()
        print(f"✅ 提取了 {len(functions_data)} 个函数")
        
        # 4. 初始化数据生成器
        print("\n🔄 初始化SFT数据生成器...")
        generator = SFTDataGenerator(analyzer)
        
        # 5. 生成增强SFT数据
        print(f"\n🎯 生成增强SFT数据 (最多{max_samples}个样本)...")
        enhanced_data = generator.generate_enhanced_sft_data(max_samples)
        
        if not enhanced_data:
            print("❌ 未生成任何增强SFT数据")
            return
        
        print(f"✅ 生成了 {len(enhanced_data)} 个增强SFT数据样本")
        
        # 6. 生成真实遮盖数据
        print(f"\n🎯 生成真实遮盖数据 (最多{max_samples}个样本)...")
        mask_data = generator.generate_real_statement_mask_data(max_samples)
        
        if not mask_data:
            print("❌ 未生成任何遮盖数据")
            return
        
        print(f"✅ 生成了 {len(mask_data)} 个遮盖数据样本")
        
        # 7. 保存增强SFT数据
        enhanced_output_file = os.path.join(output_dir, "enhanced_sft_data.json")
        print(f"\n💾 保存增强SFT数据到: {enhanced_output_file}")
        
        enhanced_data_dict = []
        for item in enhanced_data:
            enhanced_data_dict.append({
                "before": item.before,
                "expected": item.expected,
                "after": item.after,
                "context_nodes": [
                    {
                        "node_type": node.node_type,
                        "name": node.name,
                        "filepath": node.filepath,
                        "code": node.code,
                        "scope": node.scope,
                        "reference_line": node.reference_line
                    }
                    for node in item.context_nodes
                ],
                "dependency_graph": {
                    "nodes": [
                        {
                            "id": node.id,
                            "type": node.type
                        }
                        for node in item.dependency_graph.nodes
                    ],
                    "edges": [
                        {
                            "source": edge.source,
                            "target": edge.target,
                            "relation": edge.relation
                        }
                        for edge in item.dependency_graph.edges
                    ]
                },
                "metadata": item.metadata,
                "context_list": item.context_list,
                "statement_type": item.statement_type
            })
        
        with open(enhanced_output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data_dict, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 增强SFT数据已保存，共 {len(enhanced_data_dict)} 个样本")
        
        # 8. 保存真实遮盖数据
        mask_output_file = os.path.join(output_dir, "real_mask_data.json")
        print(f"\n💾 保存真实遮盖数据到: {mask_output_file}")
        
        mask_data_dict = []
        for item in mask_data:
            mask_data_dict.append({
                "context_list": item.context_list,
                "filepath": item.filepath,
                "before": item.before,
                "expected": item.expected,
                "after": item.after,
                "function_name": item.function_name,
                "statement_type": item.statement_type,
                "line_number": item.line_number,
                "call_chain": item.call_chain,
                "related_structs": item.related_structs,
                "variable_references": item.variable_references,
                "complexity_score": item.complexity_score,
                "ast_node_type": item.ast_node_type
            })
        
        with open(mask_output_file, 'w', encoding='utf-8') as f:
            json.dump(mask_data_dict, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 真实遮盖数据已保存，共 {len(mask_data_dict)} 个样本")
        
        # 9. 数据验证
        print("\n🔍 验证生成的数据质量...")
        validator = DataQualityValidator()
        
        # 验证增强SFT数据
        enhanced_validation = validator.validate_sft_data_file(enhanced_output_file)
        if enhanced_validation['valid']:
            print("✅ 增强SFT数据验证通过")
        else:
            print(f"❌ 增强SFT数据验证失败: {enhanced_validation['invalid_samples']} 个无效样本")
        
        # 验证真实遮盖数据
        mask_validation = validator.validate_sft_data_file(mask_output_file)
        if mask_validation['valid']:
            print("✅ 真实遮盖数据验证通过")
        else:
            print(f"❌ 真实遮盖数据验证失败: {mask_validation['invalid_samples']} 个无效样本")
        
        # 10. 生成总结报告
        print("\n📋 生成总结报告...")
        total_samples = len(enhanced_data_dict) + len(mask_data_dict)
        
        summary = {
            "generation_time": datetime.now().isoformat(),
            "project_path": project_path,
            "output_directory": output_dir,
            "total_samples": total_samples,
            "enhanced_sft_samples": len(enhanced_data_dict),
            "real_mask_samples": len(mask_data_dict),
            "enhanced_sft_file": enhanced_output_file,
            "real_mask_file": mask_output_file,
            "enhanced_validation": enhanced_validation,
            "mask_validation": mask_validation
        }
        
        summary_file = os.path.join(output_dir, "generation_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 总结报告已保存到: {summary_file}")
        
        # 11. 最终统计
        print("\n" + "=" * 60)
        print("🎉 SFT数据生成完成！")
        print("=" * 60)
        print(f"📊 总样本数: {total_samples}")
        print(f"   - 增强SFT数据: {len(enhanced_data_dict)} 个")
        print(f"   - 真实遮盖数据: {len(mask_data_dict)} 个")
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 数据文件:")
        print(f"   - {enhanced_output_file}")
        print(f"   - {mask_output_file}")
        print(f"   - {summary_file}")
        
        if enhanced_validation['valid'] and mask_validation['valid']:
            print("✅ 所有数据验证通过")
        else:
            print("⚠️  部分数据验证失败，请检查警告信息")
        
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)