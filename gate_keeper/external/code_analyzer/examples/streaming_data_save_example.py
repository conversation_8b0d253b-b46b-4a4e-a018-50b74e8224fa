#!/usr/bin/env python3
"""
流式数据保存示例

演示如何使用新的数据保存工具，支持分文件存储和流式保存
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.alpaca_sft.generator import AlpacaSFTGenerator
from core.data_gen.sft.alpaca_sft.models import AlpacaTaskType
from core.data_gen.sft.models import SFTDataGenerator
from core.data_gen.sft.prompt_sft.generator import PromptSFTGenerator
from core.data_gen.sft.prompt_sft.models import ModelType
from core.static_analyzer import RealNPAnalyzer


def example_batch_save():
    """示例：批量保存数据（传统方式）"""
    print("=== 批量保存示例 ===")
    
    # 初始化分析器和生成器
    examples_dir = project_root / "examples" / "np"
    analyzer = RealNPAnalyzer(str(examples_dir))
    analyzer.analyze_project()
    
    sft_generator = SFTDataGenerator(analyzer)
    prompt_generator = PromptSFTGenerator(sft_generator)
    alpaca_generator = AlpacaSFTGenerator(sft_generator)
    
    # 生成数据
    print("生成Prompt SFT数据...")
    prompt_data = prompt_generator.generate_prompt_sft_data(
        max_samples=50,
        model_type=ModelType.QWEN_CODER
    )
    
    print("生成Alpaca SFT数据...")
    alpaca_data = alpaca_generator.generate_alpaca_sft_data(
        max_samples=50,
        model_type=ModelType.DEEPSEEK_CODER
    )
    
    # 批量保存（分文件）
    print("\n保存Prompt SFT数据（分文件）...")
    prompt_files = prompt_generator.save_to_json(
        prompt_data, 
        "output/prompt_sft_data.json",
        max_items_per_file=20  # 每文件20条
    )
    
    print("\n保存Alpaca SFT数据（分文件）...")
    alpaca_files = alpaca_generator.save_to_json(
        alpaca_data,
        "output/alpaca_sft_data.json", 
        max_items_per_file=20
    )
    
    print("\n保存Alpaca SFT数据为JSONL格式（分文件）...")
    alpaca_jsonl_files = alpaca_generator.save_to_jsonl(
        alpaca_data,
        "output/alpaca_sft_data.jsonl",
        max_items_per_file=20
    )
    
    return prompt_files, alpaca_files, alpaca_jsonl_files


def example_streaming_save():
    """示例：流式保存数据（内存友好）"""
    print("\n=== 流式保存示例 ===")
    
    # 初始化分析器和生成器
    examples_dir = project_root / "examples" / "np"
    analyzer = RealNPAnalyzer(str(examples_dir))
    analyzer.analyze_project()
    
    sft_generator = SFTDataGenerator(analyzer)
    prompt_generator = PromptSFTGenerator(sft_generator)
    alpaca_generator = AlpacaSFTGenerator(sft_generator)
    
    # 创建流式保存器
    print("创建流式保存器...")
    with prompt_generator.create_streaming_saver(
        "output/streaming_prompt_sft.jsonl",
        max_items_per_file=15,
        file_format="jsonl"
    ) as prompt_saver:
        
        # 模拟逐条生成和保存数据
        print("流式生成和保存Prompt SFT数据...")
        for i in range(30):
            # 生成单个样本
            sample_data = prompt_generator.generate_prompt_sft_data(
                max_samples=1,
                model_type=ModelType.QWEN_CODER
            )
            
            if sample_data:
                # 立即保存，不占用大量内存
                prompt_saver.save_sft_item(sample_data[0])
                print(f"  已保存第 {i+1} 个样本")
    
    # 创建Alpaca流式保存器
    with alpaca_generator.create_streaming_saver(
        "output/streaming_alpaca_sft.jsonl",
        max_items_per_file=15,
        file_format="jsonl"
    ) as alpaca_saver:
        
        print("流式生成和保存Alpaca SFT数据...")
        for i in range(30):
            # 生成单个样本
            sample_data = alpaca_generator.generate_alpaca_sft_data(
                max_samples=1,
                model_type=ModelType.DEEPSEEK_CODER
            )
            
            if sample_data:
                # 立即保存
                alpaca_saver.save_alpaca_item(sample_data[0], alpaca_format_only=True)
                print(f"  已保存第 {i+1} 个样本")


def example_mixed_formats():
    """示例：混合格式保存"""
    print("\n=== 混合格式保存示例 ===")
    
    # 初始化
    examples_dir = project_root / "examples" / "np"
    analyzer = RealNPAnalyzer(str(examples_dir))
    analyzer.analyze_project()
    
    sft_generator = SFTDataGenerator(analyzer)
    prompt_generator = PromptSFTGenerator(sft_generator)
    
    # 生成数据
    prompt_data = prompt_generator.generate_prompt_sft_data(
        max_samples=25,
        model_type=ModelType.QWEN_CODER
    )
    
    # 保存为JSON格式（分文件）
    print("保存为JSON格式...")
    json_files = prompt_generator.save_to_json(
        prompt_data,
        "output/mixed_format_data.json",
        max_items_per_file=10
    )
    
    # 保存为JSONL格式（分文件）
    print("保存为JSONL格式...")
    jsonl_files = prompt_generator.save_to_jsonl(
        prompt_data,
        "output/mixed_format_data.jsonl",
        max_items_per_file=10
    )
    
    return json_files, jsonl_files


def main():
    """主函数"""
    print("流式数据保存功能演示")
    print("=" * 50)
    
    # 确保输出目录存在
    os.makedirs("output", exist_ok=True)
    
    try:
        # 示例1：批量保存
        prompt_files, alpaca_files, alpaca_jsonl_files = example_batch_save()
        
        # 示例2：流式保存
        example_streaming_save()
        
        # 示例3：混合格式
        json_files, jsonl_files = example_mixed_formats()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成！")
        print("\n生成的文件：")
        
        print("\n批量保存文件：")
        for file in prompt_files + alpaca_files + alpaca_jsonl_files:
            print(f"  - {file}")
        
        print("\n流式保存文件：")
        for file in ["output/streaming_prompt_sft.jsonl", "output/streaming_alpaca_sft.jsonl"]:
            if os.path.exists(file):
                print(f"  - {file}")
        
        print("\n混合格式文件：")
        for file in json_files + jsonl_files:
            print(f"  - {file}")
        
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 