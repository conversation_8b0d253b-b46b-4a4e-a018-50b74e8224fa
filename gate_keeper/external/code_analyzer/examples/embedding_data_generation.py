#!/usr/bin/env python3
"""
Embedding上下文训练数据生成示例

演示如何使用embedding训练数据生成模块
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.embedding import (CandidateSampler, ContextExtractor,
                                     EmbeddingDataGenerator, ExtractionConfig,
                                     GeneratorConfig, SamplingConfig)


def main():
    """主函数"""
    print("🚀 Embedding上下文训练数据生成示例")
    print("=" * 80)
    
    # 配置参数
    project_path = project_root / "examples" / "np"
    output_dir = project_root / "examples" / "embedding_output"
    max_samples = 50
    
    print(f"📁 项目路径: {project_path}")
    print(f"📁 输出目录: {output_dir}")
    print(f"🎯 最大样本数: {max_samples}")
    
    # 确保输出目录存在
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 1. 测试上下文提取器
        print("\n🔍 1. 测试上下文提取器...")
        test_context_extractor(project_path)
        
        # 2. 测试候选采样器
        print("\n🔍 2. 测试候选采样器...")
        test_candidate_sampler()
        
        # 3. 使用默认配置生成数据
        print("\n🔍 3. 使用默认配置生成embedding训练数据...")
        generate_with_default_config(project_path, output_dir, max_samples)  # 增加样本数
        
        # 4. 使用自定义配置生成数据
        print("\n🔍 4. 使用自定义配置生成embedding训练数据...")
        generate_with_custom_config(project_path, output_dir, max_samples)  # 增加样本数
        
        # 5. 生成统计报告
        print("\n🔍 5. 生成统计报告...")
        generate_statistics_report(output_dir)
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()


def test_context_extractor(project_path):
    """测试上下文提取器"""
    print("   创建上下文提取器...")
    extractor = ContextExtractor()
    
    print("   从项目提取候选上下文...")
    candidates = extractor.extract_from_directory(str(project_path))
    
    print(f"   ✅ 提取到 {len(candidates)} 个候选上下文")
    
    # 统计上下文类型分布
    type_counts = {}
    for candidate in candidates:
        context_type = candidate.context_type.value
        type_counts[context_type] = type_counts.get(context_type, 0) + 1
    
    print("   上下文类型分布:")
    for context_type, count in type_counts.items():
        print(f"     - {context_type}: {count} 个")
    
    # 显示前几个候选
    print("   前5个候选上下文:")
    for i, candidate in enumerate(candidates[:5]):
        print(f"     {i+1}. {candidate.entity_name} ({candidate.context_type.value})")
        print(f"         文件: {candidate.file_path}")
        print(f"         内容: {candidate.content[:80]}...")


def test_candidate_sampler():
    """测试候选采样器"""
    print("   创建候选采样器...")
    sampler = CandidateSampler()
    
    # 创建测试数据
    from core.data_gen.embedding.models import (ContextCandidate, ContextType,
                                                QueryContext)
    
    query = QueryContext(
        before="int x = 1;",
        after="return result;",
        expected="int result = add(x, 2);",
        file_path="test.c",
        function_name="test_func",
        line_number=10
    )
    
    candidates = [
        ContextCandidate(
            content="int add(int a, int b) { return a + b; }",
            context_type=ContextType.FUNCTION_DEF,
            file_path="test.c",
            line_number=5,
            entity_name="add",
            code_range={"start_line": 5, "end_line": 7}
        ),
        ContextCandidate(
            content="int multiply(int a, int b) { return a * b; }",
            context_type=ContextType.FUNCTION_DEF,
            file_path="test.c",
            line_number=8,
            entity_name="multiply",
            code_range={"start_line": 8, "end_line": 10}
        ),
        ContextCandidate(
            content="struct Point { int x; int y; };",
            context_type=ContextType.STRUCT_DEF,
            file_path="test.c",
            line_number=12,
            entity_name="Point",
            code_range={"start_line": 12, "end_line": 15}
        )
    ]
    
    print("   测试正样本采样...")
    positive_candidates = sampler.sample_positive_candidates(query, candidates)
    print(f"   ✅ 采样到 {len(positive_candidates)} 个正样本")
    
    print("   测试负样本采样...")
    negative_candidates = sampler.sample_negative_candidates(query, candidates, positive_candidates)
    print(f"   ✅ 采样到 {len(negative_candidates)} 个负样本")
    
    print("   测试训练对生成...")
    pairs = sampler.create_training_pairs(query, positive_candidates, negative_candidates)
    print(f"   ✅ 生成 {len(pairs)} 个训练对")


def generate_with_default_config(project_path, output_dir, max_samples):
    """使用默认配置生成数据"""
    print("   创建默认配置的生成器...")
    generator = EmbeddingDataGenerator()
    
    print("   生成embedding训练数据...")
    embedding_data = generator.generate_from_project(str(project_path), max_samples)
    
    print(f"   ✅ 生成 {len(embedding_data.pairs)} 个embedding训练对")
    
    print("   生成rerank训练数据...")
    rerank_data = generator.generate_rerank_data(embedding_data)
    
    print(f"   ✅ 生成 {len(rerank_data.inputs)} 个rerank样本")
    if rerank_data.labels:
        positive_ratio = sum(rerank_data.labels) / len(rerank_data.labels)
        print(f"   ✅ 正样本比例: {positive_ratio:.2f}")
    
    # 保存结果
    default_output_dir = output_dir / "default_config"
    default_output_dir.mkdir(exist_ok=True)
    
    generator.config.output_dir = str(default_output_dir)
    generator.save_results(embedding_data, rerank_data)
    
    print(f"   💾 结果已保存到: {default_output_dir}")


def generate_with_custom_config(project_path, output_dir, max_samples):
    """使用自定义配置生成数据"""
    print("   创建自定义配置...")
    
    # 自定义提取配置
    extraction_config = ExtractionConfig(
        max_context_length=800,
        min_context_length=20,
        include_comments=True,
        include_imports=True,
        max_functions_per_file=30,
        max_structs_per_file=15,
        max_macros_per_file=20
    )
    
    # 自定义采样配置
    sampling_config = SamplingConfig(
        positive_ratio=0.8,
        negative_ratio=0.2,
        max_candidates_per_query=15,
        min_similarity_score=0.2,
        max_similarity_score=0.8,
        use_hard_negative=False,  # 暂时不使用hard negative
        hard_negative_threshold=0.3,
        random_seed=123
    )
    
    # 自定义生成器配置
    generator_config = GeneratorConfig(
        extraction_config=extraction_config,
        sampling_config=sampling_config,
        max_pairs_per_query=8,
        max_total_pairs=2000,
        output_dir=str(output_dir / "custom_config"),
        save_intermediate=True
    )
    
    print("   创建自定义配置的生成器...")
    generator = EmbeddingDataGenerator(generator_config)
    
    print("   生成embedding训练数据...")
    embedding_data = generator.generate_from_project(str(project_path), max_samples)
    
    print(f"   ✅ 生成 {len(embedding_data.pairs)} 个embedding训练对")
    
    print("   生成rerank训练数据...")
    rerank_data = generator.generate_rerank_data(embedding_data)
    
    print(f"   ✅ 生成 {len(rerank_data.inputs)} 个rerank样本")
    if rerank_data.labels:
        positive_ratio = sum(rerank_data.labels) / len(rerank_data.labels)
        print(f"   ✅ 正样本比例: {positive_ratio:.2f}")
    
    # 保存结果
    generator.save_results(embedding_data, rerank_data)
    
    print(f"   💾 结果已保存到: {generator.config.output_dir}")


def generate_statistics_report(output_dir):
    """生成统计报告"""
    print("   生成统计报告...")
    
    report = {
        "generation_time": datetime.now().isoformat(),
        "output_directory": str(output_dir),
        "configurations": {
            "default": {
                "description": "默认配置",
                "output_dir": str(output_dir / "default_config")
            },
            "custom": {
                "description": "自定义配置",
                "output_dir": str(output_dir / "custom_config")
            }
        }
    }
    
    # 检查生成的文件
    for config_name, config_info in report["configurations"].items():
        config_dir = Path(config_info["output_dir"])
        if config_dir.exists():
            files = list(config_dir.glob("*.json"))
            config_info["files"] = [f.name for f in files]
            
            # 读取统计信息
            stats_file = config_dir / "statistics.json"
            if stats_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    config_info["statistics"] = json.load(f)
    
    # 保存报告
    report_file = output_dir / "generation_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"   💾 统计报告已保存到: {report_file}")
    
    # 显示报告摘要
    print("   生成摘要:")
    for config_name, config_info in report["configurations"].items():
        print(f"     {config_name} 配置:")
        if "statistics" in config_info:
            stats = config_info["statistics"]
            if "embedding_data" in stats:
                embedding_stats = stats["embedding_data"]
                print(f"       - Embedding训练对: {embedding_stats.get('total_pairs', 0)}")
                print(f"       - 唯一查询: {embedding_stats.get('unique_queries', 0)}")
            if "rerank_data" in stats:
                rerank_stats = stats["rerank_data"]
                print(f"       - Rerank样本: {rerank_stats.get('total_samples', 0)}")
                print(f"       - 正样本比例: {rerank_stats.get('positive_ratio', 0):.2f}")


if __name__ == "__main__":
    main() 