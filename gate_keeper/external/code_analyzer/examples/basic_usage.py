"""
Basic Usage Example

基本使用示例
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.ast_parser import ASTParserFactory
from core.code_graph import CodeGraph
from core.repository_index import RepositoryIndex
from core.static_analyzer import StaticAnalyzer


def create_test_project():
    """创建测试项目"""
    temp_dir = tempfile.mkdtemp()
    
    # 创建Python文件
    python_file = os.path.join(temp_dir, "main.py")
    python_code = '''
def main():
    print("Starting application")
    process_data()
    print("Application finished")

def process_data():
    data = load_data()
    result = transform_data(data)
    save_data(result)

def load_data():
    print("Loading data...")
    return [1, 2, 3, 4, 5]

def transform_data(data):
    print("Transforming data...")
    return [x * 2 for x in data]

def save_data(data):
    print("Saving data...")
    print(f"Saved: {data}")
'''
    
    with open(python_file, 'w', encoding='utf-8') as f:
        f.write(python_code)
    
    # 创建C文件
    c_file = os.path.join(temp_dir, "utils.c")
    c_code = '''
#include <stdio.h>

void print_message(const char* message) {
    printf("%s\\n", message);
}

int calculate_sum(int a, int b) {
    return a + b;
}

void process_numbers(int* numbers, int count) {
    for (int i = 0; i < count; i++) {
        printf("Number: %d\\n", numbers[i]);
    }
}
'''
    
    with open(c_file, 'w', encoding='utf-8') as f:
        f.write(c_code)
    
    return temp_dir


def example_ast_parsing():
    """AST解析示例"""
    print("=== AST解析示例 ===")
    
    temp_dir = create_test_project()
    python_file = os.path.join(temp_dir, "main.py")
    
    # 创建Python解析器
    parser = ASTParserFactory.create("python")
    
    # 提取函数
    functions = parser.extract_functions(python_file)
    print(f"发现 {len(functions)} 个函数:")
    for func in functions:
        print(f"  - {func.name} (行 {func.range.start_line}-{func.range.end_line})")
    
    # 提取调用
    calls = parser.extract_calls(python_file)
    print(f"发现 {len(calls)} 个函数调用:")
    for call in calls:
        print(f"  - {call.caller} -> {call.callee}")
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)


def example_repository_index():
    """仓库索引示例"""
    print("\n=== 仓库索引示例 ===")
    
    temp_dir = create_test_project()
    
    # 创建仓库索引
    repo_index = RepositoryIndex(repo_dir=temp_dir, branch="main")
    repo_index.build()
    
    print(f"索引了 {len(repo_index.function_definitions)} 个函数定义")
    print(f"索引了 {len(repo_index.function_calls)} 个函数调用")
    
    # 显示函数信息
    for func_name, funcs in repo_index.function_definitions.items():
        for func in funcs:
            print(f"函数: {func.name} 在 {func.filepath}")
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)


def example_repository_index_factory():
    """仓库索引工厂示例"""
    print("\n=== 仓库索引工厂示例 ===")
    
    temp_dir = create_test_project()
    
    # 使用工厂创建仓库索引（带缓存）
    cache_dir = os.path.join(temp_dir, "cache")
    repo_index = RepositoryIndexFactory.get_or_build(
        repo_dir=temp_dir,
        branch="main",
        cache_dir=cache_dir
    )
    
    print(f"使用工厂索引了 {len(repo_index.function_definitions)} 个函数定义")
    print(f"缓存目录: {cache_dir}")
    
    # 再次获取（应该从缓存加载）
    repo_index2 = RepositoryIndexFactory.get_or_build(
        repo_dir=temp_dir,
        branch="main",
        cache_dir=cache_dir
    )
    
    print("第二次获取完成（从缓存加载）")
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)


def example_cache_manager():
    """缓存管理器示例"""
    print("\n=== 缓存管理器示例 ===")
    
    temp_dir = create_test_project()
    cache_dir = os.path.join(temp_dir, "cache")
    
    # 创建缓存管理器
    cache_manager = RepositoryIndexCacheManager(cache_root=cache_dir)
    
    # 获取缓存信息
    info = cache_manager.get_cache_info()
    print(f"缓存根目录: {info['cache_root']}")
    print(f"最大仓库数: {info['max_repos']}")
    print(f"当前仓库数: {info['current_repos']}")
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)


def example_call_chain_analysis():
    """调用链分析示例"""
    print("\n=== 调用链分析示例 ===")
    
    temp_dir = create_test_project()
    
    # 创建仓库索引和静态分析器
    repo_index = RepositoryIndex(repo_dir=temp_dir, branch="main")
    repo_index.build()
    analyzer = StaticAnalyzer(repo_index)
    
    # 分析main函数的上游调用链
    upstream_chains = analyzer.get_upstream_call_chains("main", "main.py", max_depth=3)
    print(f"main函数的上游调用链 ({len(upstream_chains)} 条):")
    for i, chain in enumerate(upstream_chains):
        print(f"  {i+1}. {' -> '.join(chain)}")
    
    # 分析main函数的下游调用链
    downstream_chains = analyzer.get_downstream_call_chains("main", "main.py", max_depth=3)
    print(f"main函数的下游调用链 ({len(downstream_chains)} 条):")
    for i, chain in enumerate(downstream_chains):
        print(f"  {i+1}. {' -> '.join(chain)}")
    
    # 分析双向调用链
    bi_chains = analyzer.get_bidirectional_call_chains("main", "main.py", max_depth=3)
    print(f"main函数的双向调用链 ({len(bi_chains)} 条):")
    for i, chain in enumerate(bi_chains):
        print(f"  {i+1}. {' -> '.join(chain)}")
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)


def example_code_graph():
    """代码图示例"""
    print("\n=== 代码图示例 ===")
    
    temp_dir = create_test_project()
    
    # 构建代码图
    graph = CodeGraph.build_from_repository(temp_dir)
    
    # 获取统计信息
    stats = graph.get_graph_statistics()
    print(f"图统计信息:")
    print(f"  - 节点数: {stats['nodes']}")
    print(f"  - 边数: {stats['edges']}")
    print(f"  - 函数数: {stats['functions']}")
    print(f"  - 调用数: {stats['calls']}")
    print(f"  - 文件数: {stats['files']}")
    
    # 获取函数信息
    func_info = graph.get_function_info("main.py::main")
    if func_info:
        print(f"main函数信息:")
        print(f"  - 名称: {func_info['name']}")
        print(f"  - 文件: {func_info['filepath']}")
        print(f"  - 行范围: {func_info['start_line']}-{func_info['end_line']}")
    
    # 查找循环
    cycles = graph.find_cycles()
    if cycles:
        print(f"发现 {len(cycles)} 个循环:")
        for i, cycle in enumerate(cycles):
            print(f"  {i+1}. {' -> '.join(cycle)}")
    else:
        print("没有发现循环调用")
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)


def main():
    """主函数"""
    print("Code Analyzer 基本使用示例")
    print("=" * 50)
    
    try:
        example_ast_parsing()
        example_repository_index()
        example_repository_index_factory()
        example_cache_manager()
        example_call_chain_analysis()
        example_code_graph()
        
        print("\n所有示例执行完成！")
        
    except Exception as e:
        print(f"执行示例时出错: {e}")


if __name__ == "__main__":
    main() 