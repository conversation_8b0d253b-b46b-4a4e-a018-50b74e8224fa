pkt_ipv6_hdr.h 


#ifndef __PKT_IPV6_HDR_H__
#define __PKT_IPV6_HDR_H__

#define IPV6_PROT_IPV4                      0x04
#define IPV6_PROT_TCP                       0x06
#define IPV6_PROT_UDP                       0x11
#define IPV6_PROT_RSVP                      0x2E
#define IPV6_PROT_GRE                       0x2F
#define IPV6_PROT_ICMP                      0x3A
#define IPV6_PROT_OSPF                      0x59
#define IPV6_PROT_VRRP                      0x70
#define IPV6_PROT_PIM                       0x67
#define IPV6_PROT_SCTP                      0x84
#define SUPPORT_MIN_IPV6_MTU                1280
#define NEXTHDR_OTHER                       0
#define NEXTHDR_TCP                         1
#define NEXTHDR_UDP                         2
#define NEXTHDR_CPS                         2
#define NEXTHDR_ICMP                        3
#define IP_PROT_TCP                         0x06
#define IP_PROT_UDP                         0x11
/*IPv6 Extension Headers (RFC7045)*/
#define IPV6_EXT_HOP_BY_HOP             0x00 //zero(0), IPv6 Hop-by-Hop Option, [RFC2460]
#define IPV6_EXT_IPV6_HEADER            0x29 /*41, IPV6 IN IPV6,[RFC2473]*/
#define IPV6_EXT_ROUTING_HEADER         0x2B /*43, Routing Header for IPv6, [RFC2460], [RFC5095]*/
#define IPV6_EXT_FRAGMENT_HEADER        0x2C /*44, Fragment Header for IPv6, [RFC2460]*/
#define IPV6_EXT_ESP                    0x32 /*50, Encapsulating Security Payload, [RFC4303]*/
#define IPV6_EXT_AUTH_HEADER            0x33 /*51, Authentication Header, [RFC4302]*/
#define IPV6_EXT_NO_NEXT_HEADER         0x3B /*59, No Next Header for IPv6, [RFC2460]*/
#define IPV6_EXT_DEST_OPTIONS           0x3C /*60, Destination Options for IPv6, [RFC2460]*/
#define IPV6_EXT_MOBILITY_HEADER        0x87 /*135, Mobility Header, [RFC6275]*/
#define IPV6_EXT_MPLS_HEADER            0x89 /*137, Mobility Header, [RFC4023]*/
#define IPV6_EXT_HIP                    0x8B /*139, Experimental use, Host Identity Protocol [RFC5201]*/
#define IPV6_EXT_SHIM6_PROTOCOL         0x8C /*140, Shim6 Protocol, [RFC5533]*/
#define IPV6_EXT_L2_HEADER              0x8F /*143, No Next Header for IPv6 New, [IANA]*/
#define IPV6_EXT_EXP_TESTING_253        0xFD /*253, Use for experimentation and testing, [RFC3692], [RFC4727]*/
#define IPV6_EXT_EXP_TESTING_254        0xFE /*254, Use for experimentation and testing, [RFC3692], [RFC4727]*/
#define IPv6_EXT_BIERV6_IPV6            0x29
#define BIERV6_FIRST_DIP                0x0
#define BIERV6_TTL_EXTEND               0x1
#define IPV6_EXT_BIERV6_IPV4            0x04

#define IPV6_DEST_OPT_EXT_VERSION           0x3C
#define IPV6_DEST_OPT_TYPE_SMA              0x3B
#define IPV6_DEST_L2TPV3_VERSION            0x73
#define IPV6_NEXTHEADER_OPTION              0x0
#define IPV6_VERSION                        6
#define IPV6_HDR_DIRTY_LEN                  8
#define IPV6_EXT_FRAGMENT_HEADER_LEN        8
#define IPV6_ND_NS_PORT                       0x8700
/*----------------------------------------------*
 * IPv6 ICMP TYPE definition                        *
 *----------------------------------------------*/
#define IPV6_ICMP_TYPE_ROUTER_SOLICIT       133
#define IPV6_ICMP_TYPE_ROUTER_ADVERTISE     134
#define IPV6_ICMP_TYPE_NEIGHBOUR_SILICIT    135
#define IPV6_ICMP_TYPE_NEIGHBOUR_ADVERTISE  136

/*----------------------------------------------*
 * IPv6 MACRO definition                        *
 *----------------------------------------------*/
#define IPV6_LINK_LOCAL_PREFIX              0xFE80
#define IPV6_LINK_LOCAL_PREFIX_MASK         0xFFC0
#define IPV6_LINK_LOCAL_PREFIX_10BIT        0x3FA
#define IPV6_RES_MC_PREFIX                  0xFF0
#define IPV6_MC_PREFIX                      0xFF
#define IPV6_HDR_LEN                        40
#define IPV6_ICMP_HDR_LEN                   44
#define IPV6_HDR_DIP_OFFSET                 24
#define IPV6_HDR_SIP_OFFSET                 8
#define IPV6_HDR_SIP_LAST                   8      //5883ipv6over4?ě??????°ü?°??????°???ip???ó8×????á?°?????????????¤?ó??????????°ü?°
#define TCP_CHKSUM_OFFSET                   16
#define IPV6_HDR_TCPCHKSUM_DIRTYLEN         26
#define PW5883_SUB_IPV6HEADER_PORT_LEN      36    //5883????????????????IPv6???ˇ????UDP/TCP????????????????????????L2???¤????
#define IPV6_TCP_FLAG_LEN                   14
#define BSID_ROUTER_TYPE                    0xFF /*BSID??????????????????????routing type ????????*/
#define SRH_ROUTER_TYPE                     4
#define SRH_HEADER_SEGMENTS_LEFT_MAX        20
#define SRH_HDREXTLEN_LEFT_MOVE_NUM         3  /* ??SRH?????IPv6??(Byte??)?????bits?????SRH???1?IPv6????HDREXTLEN??2?????3bit??16(Byte)?
                                        * Hdr Ext Len 8-bit unsigned integer. Length of the Hop-by-Hop Options header in 8-octet units, not including the first 8 octets.
                                        * https://tools.ietf.org/html/rfc8200#page-10 https://datatracker.ietf.org/doc/draft-ietf-6man-segment-routing-header/?include_text=1*/

/*-----------------------------------------*
* ipv6 hbh mdl msg definiton               *
* --------------------------------------- */

/*----------------------------------------------*
 * IPv6 packet format definition                *
 *----------------------------------------------*/
/*RFC 2460 INTERNET PROTOCOL version 6 Specification
    3                   2                   1                   0
  1 0 9 8 7 6 5 4 3 2 1 0 9 8 7 6 5 4 3 2 1 0 9 8 7 6 5 4 3 2 1 0
 +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 |Version| Traffic Class |               Flow Label              |
 +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 |         Payload Length        |  Next Header  |  Hop Limit    |
 +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 |                                                               |0
 +                                                               +
 |                                                               |1
 +                         Source Address                        +
 |                                                               |2
 +                                                               +
 |                                                               |3
 +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 |                                                               |0
 +                                                               +
 |                                                               |1
 +                      Destination Address                      +
 |                                                               |2
 +                                                               +
 |                                                               |3
 +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-
*/
struct IPADDR_V6_S
{
    WORD_TO_HWORD_S  Part0;              /* address [127..96] */
    WORD_TO_HWORD_S  Part1;              /* address [95..64] */
    WORD_TO_HWORD_S  Part2;              /* address [63..32] */
    WORD_TO_HWORD_S  Part3;              /* address [31..0] */
};

struct IPV6_HEADER_S
{
    /*LW0*/
    uint4   Version;                /*IP version 6*/
    union {
        uint8   TC;                 /*Traffic Class*/
        uint6   DSCP;               /*Differentiated services code point*/
    };
    uint20  FlowLabel;                /*Flow Label*/

    /*LW1*/
    uint16  PayloadLen;             /*Length of packet after IPv6 header*/
    uint8   NextHeader;                /*Type of next header after this one*/
    uint8   HopLimit;                 /*Hop limit*/

    /*LW2 ~ LW5 */
    IPADDR_V6_S SIP;                /* source ip */

    /*LW6 ~ LW9*/
    IPADDR_V6_S DIP;                /* destination ip */

};

/*
4.6  Destination Options Header

   The Destination Options header is used to carry optional information
   that need be examined only by a packet's destination node(s).  The
   Destination Options header is identified by a Next Header value of 60
   in the immediately preceding header, and has the following format:

    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    |  Next Header  |  Hdr Ext Len  |                               |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               +
    |                                                               |
    .                                                               .
    .                            Options                            .
    .                                                               .
    |                                                               |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

   Next Header          8-bit selector.  Identifies the type of header
                        immediately following the Destination Options
                        header.  Uses the same values as the IPv4
                        Protocol field [RFC-1700 et seq.].

   Hdr Ext Len          8-bit unsigned integer.  Length of the
                        Destination Options header in 8-octet units, not
                        including the first 8 octets.

   Options              Variable-length field, of length such that the
                        complete Destination Options header is an
                        integer multiple of 8 octets long.  Contains one
                        or  more TLV-encoded options, as described in
                        section 4.2.

   The only destination options defined in this document are the Pad1
   and PadN options specified in section 4.2.

   Note that there are two possible ways to encode optional destination
   information in an IPv6 packet: either as an option in the Destination
   Options header, or as a separate extension header.  The Fragment
   header and the Authentication header are examples of the latter
   approach.  Which approach can be used depends on what action is
   desired of a destination node that does not understand the optional
   information:

      o  If the desired action is for the destination node to discard
         the packet and, only if the packet's Destination Address is not
         a multicast address, send an ICMP Unrecognized Type message to
         the packet's Source Address, then the information may be
         encoded either as a separate header or as an option in the
         Destination Options header whose Option Type has the value 11
         in its highest-order two bits.  The choice may depend on such
         factors as which takes fewer octets, or which yields better
         alignment or more efficient parsing.

      o  If any other action is desired, the information must be encoded
         as an option in the Destination Options header whose Option
         Type has the value 00, 01, or 10 in its highest-order two bits,
         specifying the desired action (see section 4.2).

*/

struct IPV6_DSTOPTS_S
{
    uint8   NextHeader;
    uint8   HdrExtLen;
    uint8   OptType;
    uint8   OptDataLen;
    uint8   TunEncapLim;
    uint8   PadNOptType;
    uint8   PadOptLen;
    uint8   Rsvd;
};

struct IPV6_WITHOPTS_S
{
    IPV6_HEADER_S	IPv6Hdr;
    IPV6_DSTOPTS_S  DstOpts;
};

struct SMA_OPTS_HDR_S
{
    uint8   OptionType;
    uint8   OptDataLen;
    uint4   TagLen;
    uint4   AiType;
    uint8   Rsvd0;
};

/*SMA???????§???¨?????¤?D??¨???????3???¨2????¤??¨??1?¨???LW?¨?¤???¤?????¨???¨???*/
struct DST_SMA_OPTS_HDR_S
{
    uint8   NextHeader;
    uint8   HdrExtLen;
    SMA_OPTS_HDR_S  SmaOpts;
    uint32  LW0;
    uint32  LW1;
    uint16  LW2;

};

struct IPV6_EXT_COMMON_S
{
    uint8   NextHeader;
    uint8   HdrExtLen;
};

struct IPV6_HEADER_WITH_EXT_S
{
    IPV6_HEADER_S	    Ipv6Hdr;
    IPV6_EXT_COMMON_S   ExtHdr;
};

struct IPV6_HOP_BY_HOP_S
{
    IPV6_EXT_COMMON_S   ExtHdr;
    uint8   OptionType;
    uint8   OptionDataLen;
    uint16  Option1;
    uint16  Option2;
};

struct IPV6_MLDSNP_S
{
    uint8   IcmpV6Type;
    uint8   Code;
    uint16  CheckSum;
};

struct IPV6_HEADER_WITH_HBH_MLDSNP_S
{
    IPV6_HEADER_S       Ipv6Hdr;
    IPV6_HOP_BY_HOP_S   HopByHopHdr;
    IPV6_MLDSNP_S       MldSnpHdr;
};

struct IPV6_WITH_DST_SMA_OPTS_S
{
    IPV6_HEADER_S	IPv6Hdr;
    DST_SMA_OPTS_HDR_S  DstOpts;
};

struct IPV6_FRAGMENT_HEADER_S
{
    uint8   NextHeader;
    uint8   Reserved;           /*reserved field*/
    union {
        struct {
            uint13  FragOffset;         /*Fragment offset*/
            uint2   Res;                /*reserved field*/
            uint1   MF;                 /*1 = more fragments; 0 = last fragment*/
        };
        uint16 FragOffsetInfo;
    };
    uint32  Identification;
};

struct IPV6_HEADER_WITH_FRAG_S
{
    IPV6_HEADER_S	    Ipv6Hdr;
    IPV6_FRAGMENT_HEADER_S   FragHdr;
};

struct ICMPV6_NS_S
{
    /*LW0*/
    uint8   Type;
    uint8   Code;
    uint16  Checksum;
    /*LW1*/
    uint32  Rsvd;
    /*LW2~LW5*/
    IPADDR_V6_S  TargetIP;
};

struct ICMPV6_NS_WITH_SLA_S
{
    /*LW0*/
    union
    {
        struct {
            uint8   Type;
            uint8   Code;
            uint16  Checksum;
        };
        uint32 TypeCodeChk;
    };
    /*LW1*/
    uint32  Rsvd;
    /*LW2~LW5*/
    IPADDR_V6_S  TargetIP;
    /*LW6~LW7*/
    union
    {
        struct {
            uint8  SLAType;
            uint8  SLALength;
            union
            {
                struct {
                    uint8  SLAAddrH8;
                    uint8  SLAAddrM8;
                };
                uint16 SLAAddrH16;
            };
            uint32 SLAAddrL32;
        };
        struct {
            uint32 SLALW0;
            uint32 SLALW1;
        };
    };
};

struct ICMPV6_NS_RSO_S
{
    uint1  R;
    uint1  S;
    uint1  O;
    uint29 Rsvd;
};

struct ICMPV6_NA_S
{
    /*LW0*/
    uint8   Type;
    uint8   Code;
    uint16  Checksum;
    /*LW1*/
    ICMPV6_NS_RSO_S  RSO;
    /*LW2~LW5*/
    IPADDR_V6_S  TargetIP;
};

struct ICMPV6_NA_WITH_SLA_S
{
    /*LW0*/
    union
    {
        struct
        {
            uint8   Type;
            uint8   Code;
            uint16  Checksum;
        };
        uint32 TypeCodeChk;
    };
    /*LW1*/
    ICMPV6_NS_RSO_S  RSO;
    /*LW2~LW5*/
    IPADDR_V6_S  TargetIP;
    /*LW6~LW7*/
    union
    {
        struct
        {
            uint8  SLAType;
            uint8  SLALength;
            union
            {
                struct
                {
                    uint8  SLAAddrH8;
                    uint8  SLAAddrM8;
                };
                uint16 SLAAddrH16;
            };
            uint32 SLAAddrL32;
        };
        struct
        {
            uint32 SLALW0;
            uint32 SLALW1;
        };
    };

};

struct IPV4O6_UDP_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    IPV4_HEADER_S  stIPv4Hdr;
    UDP_S          stUdp;
};

struct IPV4O6_ICMP_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    IPV4_HEADER_S  stIPv4Hdr;
    ICMP_S         stIcmp;
};

struct IPV6_ICMP_NS_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    ICMPV6_NS_S    stIcmp;
};

struct IPV6_ICMP_IDENTI_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    ICMP_IDENTIFIER_S         stIcmp;
};

struct IPV6_BIER_EXTEND_HEADER_S
{
    uint8 NextHdr;     //Identifies the type of header immediately following the Destination Options header.
    uint8 HdrExtLen;   //Length of the Destination Options header in 8-octet units, not including the first 8 octets.
    uint8 OptionType;
    uint8 OptionLen;   //Length of the option, in octets, excluding the Option Type and Option Length fields
};

struct IPV6_BIER_OPTION_S
{
    uint8 OptionType;
    uint8 OptionLen;   //Length of the option, in octets, excluding the Option Type and Option Length fields
};

struct BIERV6_IPV6_HEADER_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    IPV6_BIER_EXTEND_HEADER_S stBierv6Ext;
};
/*
Internet-Draft      IPv6 Segment Routing Header (SRH)      February 2017


     0                   1                   2                   3
     0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    | Next Header   |  Hdr Ext Len  | Routing Type  | Segments Left |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    | First Segment |     Flags     |           RESERVED            |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    |                                                               |
    |            Segment List[0] (128 bits IPv6 address)            |
    |                                                               |
    |                                                               |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    |                                                               |
    |                                                               |
                                  ...
    |                                                               |
    |                                                               |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    |                                                               |
    |            Segment List[n] (128 bits IPv6 address)            |
    |                                                               |
    |                                                               |
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
    //                                                             //
    //         Optional Type Length Value objects (variable)       //
    //                                                             //
    +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

*/

struct SRH_FLAGS_S                   /*????§????VXLAN_HEADER????????????????*/
{
    uint1       U;
    uint1       P;
    uint1       O;
    uint1       A;
    uint1       H;
    uint1       encapTefrr;   // encap?????ipv6??tefrr??
    uint1       noFrr; // ??tefrr??
    uint1       U2;   // unused bit 2
};

struct SRH_HEADER_S                   /*???¨????VXLAN_HEADER????????????????*/
{
    uint8       NextHeader;
    uint8       HdrExtLen;
    uint8       RoutingType;
    uint8       SegmentsLeft;

    uint8       LastEntry;
    SRH_FLAGS_S Flags;              /*8bit*/
    uint16      Reserved;
};

struct SLICE_FLAGS_S
{
    uint1   forceSlice;
    uint7   rsvd;
};

struct HBH_SLICEID_TLV
{
    uint8   OptionType;
    uint8   OptDataLen;
    SLICE_FLAGS_S   Flags;
    uint24  rsvd1;
    uint32  sliceid;
    uint32  rsvd2;
};

struct HBH_NEW_HEADER_S
{
    uint8   NextHeader;
    uint8   HdrExtLen;  // ???1
};

struct HBH_NEW_WITH_SLICE_S
{
    HBH_NEW_HEADER_S  HbhHeadr;
    HBH_SLICEID_TLV   SliceTlv;
};

struct HBH_DIP_TLV
{
    uint8   OptionType;
    uint8   OptDataLen;
    uint8   Flags;
    uint8   Label;
};

struct HBH_DIP_COM
{
    uint8   OptionType;
    uint8   OptDataLen;
    uint8   Flags;
};


struct HBH_NEW_WITH_DIP_S {
    HBH_NEW_HEADER_S  HbhHeadr;
    HBH_DIP_TLV   DipTlv;
    uint16        Padding;
};

struct HBH_SLICEID_DIP_TLV {
    uint8   OptionType;
    uint8   OptDataLen;
    SLICE_FLAGS_S   Flags;
    uint24  rsvd1;
    uint32  sliceid;
    HBH_DIP_TLV   DipTlv;
};


struct HBH_NEW_WITH_SLICE_DIP_S {
    HBH_NEW_HEADER_S  HbhHeadr;
    HBH_SLICEID_DIP_TLV   SliceDipTlv;
};
#define HBH_OPTION_SRV6_SLICE   0b11101  /* ??????? */
#define PADN_FOR_SRV6_SLICE_TLV 0x01020000
#define HBH_OPTION_DIP_TYPE  0b00110101
#define HBH_OPTION_DIP_LEN  2
#define PADN_TLV_TYPE 0x01
#define PADN_TLV_DIP_LEN 0x00
/*DHCPv6 RELAY MESSAGE FORMAT

       0                   1                   2                   3
       0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
      +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
      |    msg-type   |   hop-count   |                               |
      +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               |
      |                                                               |
      |                         link-address                          |
      |                                                               |
      |                               +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-|
      |                               |                               |
      +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               |
      |                                                               |
      |                         peer-address                          |
      |                                                               |
      |                               +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-|
      |                               |                               |
      +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               |
      .                                                               .
      .            options (variable number and length)   ....        .
      |                                                               |
      +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
*/
struct DHCPV6_RELAY_MSG_S
{
    uint8       MsgType;
    uint8       HopCount;
    IPADDR_V6_S LinkAddress;
    IPADDR_V6_S PeerAddress;
};

struct IPV6_DHCP_REPLY_MSG_TYPE_S
{
    IPV6_HEADER_S       stIpv6;
    UDP_S               stUdp;
    uint8               MsgType;
};

struct VRRPV3_HEADER_S
{
    /*LW0*/
    uint4   Version;                /*VRRP version */
    uint4   Type;                   /*VRRP Type */
    uint8   VRID;                   /*virtual Rtr ID */
    uint8   Priority;               /*Priority */
    uint8   CntIpAddrs;             /*Count IPvX Addr */

    /*LW1*/
    uint4   rsvd;                   /*rsvd */
    uint12  MaxAdverInt;            /*Max Adver Int */
    uint16  CheckSum;               /*CheckSum */

    /*LW2 ~ LW5 */
    IPADDR_V6_S IPvXAddr1;          /*IPvX Addr*/

};

struct IPV6_GRE_HEADER_S
{
    IPV6_HEADER_S       stIPv6Hdr;
    GRE_HEADER_S        stGreHdr;
};

struct IPV6_TCP_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    TCP_S          stTcp;
};

struct IPV6_UDP_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    UDP_S          stUdp;
};

struct IPV6_QUIC_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    UDP_S          stUdp;
    QUIC_HEADER_S  stQuic;
};

struct IPV6_ICMP_S
{
    IPV6_HEADER_S  stIPv6Hdr;
    ICMP_S         stIcmp;
};

struct IPV6_GTP_HEADER_S
{
    IPV6_HEADER_S       stIPv6Hdr;
    UDP_S               stUdp;
    GTP_HEADER_S        stGtpHdr;
};


struct IPV6_NEW_HBH_S
{
    IPV6_HEADER_S       stIPv6Hdr;
    HBH_NEW_HEADER_S    stNewHbh;
};

struct IPV6_L4_S
{
    IPV6_HEADER_S   Ipv6;
    union {
        TCP_S       Tcp;
        UDP_S       Udp;
        ICMP_S      Icmp;
        IPV6_HOP_BY_HOP_S Hbh;
    };
};

struct IPV6_GRE_K_WITH_C_HEADER_S
{
    IPV6_HEADER_S           stIPv6Hdr;
    GRE_K_WITH_C_HEADER_S   stGreKWithCHdr;
};


struct IPV6_GRE_K_WO_C_HEADER_S
{
    IPV4_HEADER_S           stIPv6Hdr;
    GRE_K_WO_C_HEADER_S     stGreKWoCHdr;
};

struct IPV6_GRE_WHOLE_HEADER_S
{
    IPV6_HEADER_S          stIPv6Hdr;
    GRE_K_WITH_C_HEADER_S  stGreHdr;
};

struct IPV6_DHCP_S
{
    IPV6_HEADER_S       stIpv6;
    UDP_S               stUdp;
    union {
        DHCPV6_RELAY_MSG_S  Relay;
        DHCPV6_REQUEST_S    Request;
    };
};

struct IPV6_SRH_S
{
    IPV6_HEADER_S   Ipv6;
    SRH_HEADER_S    Srh;
};

struct IPV6_SLICE_SRH_S
{
    IPV6_HEADER_S         Ipv6;
    HBH_NEW_WITH_SLICE_S  Hbh;
    SRH_HEADER_S          Srh;
};
#endif /* __PKT_IPV6_HDR_H__ */
