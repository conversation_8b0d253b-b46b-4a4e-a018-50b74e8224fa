void UpfPosJudgeNextType()
{
    cmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_IPV4;
    if (cc0.eq) {
        jmp IP_PROC;
    }
    cmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_IPV6;
    if (cc0.eq) {
        jmp IP_PROC;
    }
    cmp cc0 = rsUpfSpecExtRetInfo.PosInfo.PosType, L2PA_RST_L3TYPE_MPLSUC;
    if (cc0.eq) {
        jmp MPLS_PROC;
    }
    jmp FOR_DEFAULT; 

    // 将上面的if改写成switchcase

    IP_PROC:
        movezr rsUpfSpecExtRetInfo.PosInfo.PosNextType = POS_NEXT_HEAD_TYPE_IP;
        jmp END;
    MPLS_PROC:
        movezr rsUpfSpecExtRetInfo.PosInfo.PosNextType = POS_NEXT_HEAD_TYPE_MPLS;
        jmp END;
    FOR_DEFAULT:
        movezr rsUpfSpecExtRetInfo.PosInfo.PosNextType = POS_NEXT_HEAD_TYPE_NONE;
    END:
        return;
}

/*
        switch
        {
            case (L2PA_RST_L3TYPE_KEEPALIVE == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : FOR_DEFAULT;
            case (L2PA_RST_L3TYPE_ISIS == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : FOR_DEFAULT;
            case (L2PA_RST_L3TYPE_POS_LLDP == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : FOR_DEFAULT;
            case (L2PA_RST_L3TYPE_IPV4 == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : IP_PROC;
            case (L2PA_RST_L3TYPE_IPV6 == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : IP_PROC;
            case (L2PA_RST_L3TYPE_MPLSUC == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : MPLS_PROC;
            case (L2PA_RST_L3TYPE_MPLSMC == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : FOR_DEFAULT;
            case (L2PA_RST_L3TYPE_UNK_L2 == rsUpfSpecExtRetInfo.PosInfo.PosType)
                : FOR_DEFAULT;
            default
                : FOR_DEFAULT;
        } 
*/