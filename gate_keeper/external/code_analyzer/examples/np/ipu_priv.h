ipu_priv.h

#ifndef __IPU_PRIV_H__
#define __IPU_PRIV_H__

#define IPU_TTL_LIMIT_MIN 1

enum E_GETWLR_NHP_RESULT {
    E_GETWLR_NHP_OK = 0x0,
    E_GETWLR_NHP_INVALID,
    E_GETWLR_NHP_NOT_WLR,
    E_GETWLR_NHP_NOT_FIND
};

/* 移植5896的定义内部用 */
enum NGSF_EGRESSPKTTYPE_E {
    NGSF_EGRESSPKTTYPE_NONE = 0,
    NGSF_EGRESSPKTTYPE_M1 = 1,
    NGSF_EGRESSPKTTYPE_M2_1 = 2,
    NGSF_EGRESSPKTTYPE_M2_2 = 3,
    NGSF_EGRESSPKTTYPE_M2_3 = 4,
    NGSF_EGRESSPKTTYPE_M3 = 5,
    NGSF_EGRESSPKTTYPE_M6 = 6,
    NGSF_EGRESSPKTTYPE_MJ = 7,
    NGSF_EGRESSPKTTYPE_U1_AT = 8,
    NGSF_EGRESSPKTTYPE_U1_ARP = 9,
    NGSF_EGRESSPKTTYPE_U1_VLANIF = 10,
    NGSF_EGRESSPKTTYPE_U2_1_IPV4 = 11,
    NGSF_EGRESSPKTTYPE_U2_1_IPV6 = 12,
    NGSF_EGRESSPKTTYPE_U2_3 = 13,
    NGSF_EGRESSPKTTYPE_U2_4 = 14,
    NGSF_EGRESSPKTTYPE_U3 = 15,
    NGSF_EGRESSPKTTYPE_U4 = 16,
    NGSF_EGRESSPKTTYPE_U6_ND = 17,
    NGSF_EGRESSPKTTYPE_U6_NDH = 18,
    NGSF_EGRESSPKTTYPE_U8_NH = 19,
    NGSF_EGRESSPKTTYPE_U8_AT = 20,
    NGSF_EGRESSPKTTYPE_U9 = 21,
    NGSF_EGRESSPKTTYPE_UJ1 = 22,
    NGSF_EGRESSPKTTYPE_UJ2 = 23,
    NGSF_EGRESSPKTTYPE_UJ3 = 24,
    NGSF_EGRESSPKTTYPE_U1_AT_MAIN = 25,
    NGSF_EGRESSPKTTYPE_U6_VLANIF = 26,
    NGSF_EGRESSPKTTYPE_MAX = 27
}; /* 硬件PA解析下行报文类型 */

enum E_IPU_BTOB_EXT_HDR_TYPE {
    IPU_BTOB_EXT_HDR_TYPE_TOCP = 0,
    IPU_BTOB_EXT_HDR_TYPE_PAYLOAD = 15,
    IPU_BTOB_EXT_HDR_TYPE_BUTT = IPU_BTOB_EXT_HDR_TYPE_PAYLOAD
};

enum E_IPU_BTOB_TOCP_VERSION {
    IPU_BTOB_TOCP_VERSION_24_0,
    IPU_BTOB_TOCP_VERSION_BUTT
};

enum E_IPU_BTOB_TOCP_TYPE {
    IPU_BTOB_TOCP_TYPE_BGPRDT = 0,
    IPU_BTOB_TOCP_TYPE_MAX = 7
};

#define IPU_INVALID_SBSP 0x3fff
#define SOFT_LOOP_CHN_MAP_INDEX_MASK    0x00FF


#endif  /* __IPU_PRIV_H__ */
