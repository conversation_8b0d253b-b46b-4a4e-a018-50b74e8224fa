#include "ipu_priv.h"
#include "upf_common_util.h"
#include "upf_common_causeid.h"
#include "pkt_hdr.h"
#include "table_ip_nhp.h"
#include "ip_util.h"
#include "desc_common_instance.h"
#include "table_se.h"
#include "ingress.h"
#include "upf_common_cpu2np.h"
#include "ipu_headr.h"
#include "upf_common_func.h"
#include "ipu_iptrace.h"
#include "frame_hdr_ngsf.h"
#include "table_qos.h"
#include "upf_common_intf.h"
#include "pae_intf.h"
#include "main.h"
#include "outintf.h"
#include "except_util.h"
#include "ipu_intf.h"
#include "upf_common_resdef.h"
#include "upf_common_portinfo.h"
#include "ipu_priv.h"
#include "except_np2cp_hdr.h"
#include "upf_common_np2cpu.h"
#include "ipu_tbl_def.h"
#include "frame_hdr.h"
#include "fh_parse.h"
#include "rb.h"
#include "lb_intf.h"
#include "lb_res_def.h"
#ifdef SUPPORT_TCE
#include "ipu_iptrace.h"
#endif
#include "ipu_v6udpchksum.h"
#include "ipu_softpara.h"
#include "loadbalance_util.h"
#include "upf_common_matchcnt.h"

/* IPU模块全局变量 */
(IPU_PAE_EXT_HDR_S)         q0b                 qsPaeExt;
(IPU_PAE_EXT_HDR_S)         p0b                 psPaeExt;
(EPAT_NEON_S)               g_rsTblData256_1    g_rsEpatLocal;
/* 由于重定向ipv6到eIp_Ipv6NdhSearchProc之间封装mac之间存在g_rbiInternalHdrLen/g_rhiInternalHdrOffset更新覆盖
 * rsGiRedirectDstIpv6最后4字节内容的情况 */
(IPADDR_V6_S)               g_rsTblData_5       rsGiRedirectDstIpv6;
/* 定义4字节全局变量存重定向的ipv6 Part3的4字节内容 */
// uint32 g_rwGiRedirectDstIpv6Part3;

// 与软转实现保持一致，如果配置了QOS映射则使用QOS，如果没有配置则使用默认优先级
// PAE头中的优先级与TM中的优先级相同，无需进行转换，0高优先级、7低优先级
inline PAE_INFO_QOS_CONFIG_DEFAULT_BE(uint1 flag, uint3 qos)
{
    bitcmp cc0 = flag, ONE_BIT_MASK;
    if (cc0.z) {
        move rsInfo.bQos = BE;
    } else {
        move rsInfo.bQos = qos;
    }
}

/* IP-U上送CPU的报文的PAE封装预处理函数 */
IpuTxCpuIpCtrlPrePae():
{
    UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_SND_CPUPKT);

    L2_HDR_INC(SIZEOF(IPU_PAE_EXT_HDR_S));
    move psPaeExt.HType = PAE_APP_HEAD_TYPE_NPCP;
    move psPaeExt.Rsvd0 = 0;

    cmp cc0 = g_cDesc.CauseID, EXCP_ID_IP_TRACE_NP_IN;
    cmp cc1 = g_cDesc.CauseID, EXCP_ID_IP_TRACE_NP_OUT;
    bitcmp cc0 = ccr[:4], 0b0101;
    if (cc0.nz) {                    // 等于其中任何一个
        movezr rsInfo.LbHashKey = g_rsUpfDesc.IpTraceId;
    } else {
        movezr rsInfo.LbHashKey = g_cDesc.CauseID;
    }

    move rsInfo.bQos = g_cDesc.Dsic.Rqcolor.QindexColor.Qindex; // 协议报文保持原有协议优先级
    move rsInfo.bcPlaneid = PAE_PLANEID_AUTOSEL;
    movezr rsInfo.DstTb = g_cDesc.OportInfo.Physical.TB;
    movezr rsInfo.DstTp = g_cDesc.OportInfo.Physical.Port;
    move rsInfo.IsLogicTb = TRUE;
    move rsInfo.IsToIpuc = TRUE;

    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_ARP_REPLY, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE191);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_ARP_REQUEST, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE190);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_REDIRECT_ICMP, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE8);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_REDIRECT_ICMP, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE43);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_DHCP_UC_PKT, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE107);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_AT_MISS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE214);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_ARP_MISS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE82);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_ND_MISS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE66);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_NDH_MISS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE67);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_DHCP, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE400);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_RS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE359);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_RA, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE318);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_NS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE399);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_MC_NS, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE645);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_NA, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE95);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_FRAGMENT, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE648);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_RESERVED_MC_OSPF, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE16);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_RESERVED_MC_OSPF_DR, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE17);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_OSPFDD_TO_CP, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE93);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_NEW_IPV6_OSPF, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE402);
    UpfDbgStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_PACKET_BIG, CAUSE_UPF_IPU_DBGCNT_TOFEI_CAUSE50);
    jmp Pae_HeadEncap;
}

void IpuPaeRxFragPktCheck()
{
    (S_NLS_APP2MPF_COMM_HDR) q0b qsApp2MpeHdr;

    move rwFuncRtnData.Data0 = FAILED;
    // 当前支持对PAE分片报文进行处理的报文有重组报文
    cmp cc0 = psPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_LBAPP_EXT;
    if (cc0.eq) {
        cmp cc0 = qsApp2MpeHdr.NextHeadType, APP2MPF_EXTEND_HDR_TYPE_HWA_REASSEM;
        if (cc0.eq) {
            move rwFuncRtnData.Data1 = CAUSE_UPF_IPU_REASSTOCPU_DROP_RCV_PAE_FRAG_PKT;
            return;
        }
    }

    move rwFuncRtnData.Data1 = CAUSE_UPF_IPU_DROP_UNKNOWPKT_RCV_PAE_FRAG_PKT;
    return;
}

/* PAE收到TB为本板，目的TP为IP的PAE报文，分发给IP-U的入口函数 */
IpuRxFromPaeProc():
{
    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NPCP;
    if (cc0.eq) {
        jmp IpuRxCpuIpCtrlProc();
    }
    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_MPF;
    if (cc0.eq)
    {
        jmp LbAppRxProc(FALSE, FALSE, FALSE);
    }

    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_LBAPP_EXT;
    if (cc0.eq)
    {
        jmp LbAppRxProc(FALSE, FALSE, FALSE);
    }

    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_LBAPP_COM;
    if (cc0.eq)
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_APPPKT_TYPEF806);
        jmp LbAppRxProc(FALSE, FALSE, FALSE);
    }

    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_MPF_EXT;
    if (cc0.eq)
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_APPPKT_CHKSUMEXTCOMM);
        jmp LbAppRxProc(FALSE, TRUE, FALSE);
    }

    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_BTOB;
    if (cc0.eq) {
        jmp IpuRxBtobProc();
    }

    // TODO:先不移进来，待开始调功能打开验证
    // cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_IPSEC;
    // if (cc0.eq)
    // {
    // jmp Ipsecv4RxProc();
    // }

    // TODO:先不移进来，待开始调功能打开验证
    // cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_NLS_IPSECV6;
    // if (cc0.eq)
    // {
    // jmp Ipsecv6RxProc();
    // }

    cmp cc0 = qsPaeExt.HType, PAE_APP_HEAD_TYPE_UPF_NP2APP;
    if (cc0.eq) {
        jmp IpuRxAppCtrlProc();
    }

    jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_PAE_UNKNOWN_PKT);
}

/* IP-U模块接收App控制消息的处理，区别于App给IP-U的数据报文(处理bundle为LbAppRxProc)， */
/* 其HeadType PAE_APP_HEAD_TYPE_UPF_NP2APP为NP加速新增的消息类型 */
IpuRxAppCtrlProc():
{
    (NpApp_Common_Hdr) q0b qsNpAppComHdr;

    cmp cc0 = qsNpAppComHdr.subType, NPAPP_SUBTYPE_NPUSERTRCRULE;
    if (cc0.eq) {
        UpfExceptStatNoDiscard(CAUSE_UPF_PAE_NORMAL_NPUSRTRC);
        /* 新版本进入此bundle肯定是PAE_APP_HEAD_TYPE_UPF_NP2APP类型 */
        /* 此判断兼容老版本通过NP_USRTRC_TP接收PAE_APP_HEAD_TYPE_UPF_NP2APP HdrType+ NPAPP_SUBTYPE_NPUSERTRCRULE类型消息处理 */
        cmp cc0 = qsNpAppComHdr.appHdrType, PAE_APP_HEAD_TYPE_UPF_NP2APP;
        if (cc0.ne) {
            jmp UpfExceptDiscard(CAUSE_UPF_IPU_ERR_USRTRC_TYPE_INVALID);
        }
        SR_Q_EX(SIZEOF(NpApp_Common_Hdr));
        jmp UpfExceptDiscard(100); // IpuUsrTrcRxTrcRuleOper; 先打桩，待跟踪补充
    }

    cmp cc0 = qsNpAppComHdr.subType, NPAPP_SUBTYPE_NPSOFTPARACFG;
    if (cc0.eq) {
        UpfDbgStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_NPAPP_SOFTPARACFG);
        SR_Q_EX(SIZEOF(NpApp_Common_Hdr));
        jmp IpuRxSoftParaCfg;
    }

    jmp UpfExceptDiscard(CAUSE_UPF_IPU_DROP_NPAPP_SUBTYPE_INVALID);
}

/* IP-U模块 CPU下插报文的处理函数 */
IpuRxCpuIpCtrlProc():
{
    uint8 rbHdrLen;
    uint8 l3stake;
    UpfMatchCntIpCtrlIntfL3OffsetMatch();  //特征计数：IPCTRL收包L3OFFSET RULE匹配
    UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_CPUPKT);

    movezr rbHdrLen = qsPaeExt.Padding;
    add rbHdrLen = rbHdrLen, SIZEOF_EX(IPU_PAE_EXT_HDR_S);
    SR_Q_EX(rbHdrLen); // 偏移到Special头

    GET_L3_STAKE(l3stake);
    sub g_cDesc.RealLength = g_cDesc.RealLength, l3stake;

    MovePw(P_WINDOW, l3stake, MOVE_PW_NEED_SIZE_SAFE_MAX);

    SET_P_EQ_Q();
    move g_cDesc.L2Length = 0;

    jmp iLdSpecDecap;
}

/* LB模块或IPU模块接收来自CSLB/UDG_NLS的PAE报文，在解析Common头和扩展头之后，调用IpuRxFromCslbProc进行转发处理
本函数为提供为LB/IPU模块的调用入口，在调用前已完成Common头和扩展头解析；
根据报文净荷为v4或v6，进一步调用IpuRxFromCslbIpv4Proc或IpuRxFromCslbIpv6Proc处理；
*/
IpuRxFromCslbProc():
{
    uint8   rbIpver;
    (IP_HDR_VER_S)    q0b           qsIpHdrVer;
    UpfMatchCntPaeIntfL3OffsetMatch();    //特征计数：PAE接口 L3OFFSET RULE匹配
    movezr rbIpver = qsIpHdrVer.Ver;
    cmp cc0 = rbIpver, IPV4_VERSION;
    if (cc0.eq)
    {
        UpfMatchCntPaeIntfIpv4Match();        //特征计数：PAE接口 Ipv4 RULE匹配
        jmp IpuRxFromCslbIpv4Proc();
    }
    cmp cc0 = rbIpver, IPV6_VERSION;
    if (cc0.eq)
    {
        UpfMatchCntPaeIntfIpv6Match();        //特征计数：PAE接口 Ipv6 RULE匹配
        jmp IpuRxFromCslbIpv6Proc();
    }

    jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_APPPKT_IPVER_ERR);
}

/*对于LB/UDG发给IP的PAE业务报文，净荷为IPv4报文的转发处理入口*/
IpuRxFromCslbIpv4Proc():
{
    uint32  rwPktTheoryLen;
    uint8   rbIpv4HdrLen;
    uint8   rbIptraceEnFlag;
    uint16  rhTemp;
    (IPV4_HEADER_S)   q0b               qsIPv4Hdr;

    // 重组完成的报文使用重组完成计数记录，为保持业务报文统计准确，此处不重复记录
    bitcmp cc0 = g_stMpf2IpHdrInfo.bReassemFlag, ONE_BIT_MASK;
    if (cc0.z) main_path
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_APPPKT_IPV4);
    }
    else
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_IPV4_REASSEM_PKT_FROMSSG); // IPV4重组完成报文计数
    }

    move g_cDesc.AppPkt = TRUE;
    move g_cDesc.L2PaRst.L3Type = L2PA_RST_L3TYPE_IPV4;

    CONTEXT_SWITCH()
    movezr g_cDesc.VpnID = g_stMpf2IpHdrInfo.IpVpnIdx[:16];

    /* TODO: 先判断匹配用户跟踪 */
    // bitcmp cc0 = rsScratchReg5.UserTrcEn, ONE_BIT_MASK;
    // if (cc0.nz)
    // {
    //     iIpuUsrTrcOutIpV4Judge(TRUE);
    //     bitcmp cc0 = rsUpfDesc.UsrTrcInPortEn, ONE_BIT_MASK;
    //     if (cc0.nz)
    //     {
    //         IpuTraceInLoopTx();
    //     }
    // }
    /* 用户跟踪未命中，再进行IP跟踪的匹配 */
    bitcmp cc0 = g_rsUpfDesc.UserTrcMatch, ONE_BIT_MASK;
    if (cc0.z) {
        movezr rbIptraceEnFlag = g_ssScratch4.UpfIptraceEn;
        bitcmp cc0 = rbIptraceEnFlag[:1], ONE_BIT_MASK;
        if (cc0.nz) {
            IpTrace_Ipv4_Search(g_cDesc.VpnID);
            IpuTraceInLoopTx();
        }
    }
    /* 对LB/UGW发过来的报文，不做地址合法性检查 */

    /*长度检查*/
    movezr rbIpv4HdrLen[5..2] = qsIPv4Hdr.HeaderLength;
    cmp cc1 = qsIPv4Hdr.TotalLength, rbIpv4HdrLen;
    add rwPktTheoryLen = qsIPv4Hdr.TotalLength, g_cDesc.AppHeadLen;
    cmp cc2 = rwPktTheoryLen, g_cDesc.RealLength;

    if (cc1.lt)
    {
        //Discard the illegal packet
        jmp UpfExceptDiscardWithDfxInfo(CAUSE_UPF_IPNLS_DROP_APPPKT_TOTAL_LEN_SMALL, qsIPv4Hdr.DIP, qsIPv4Hdr.SIP,
            rwPktTheoryLen, g_cDesc.RealLength);
    }
    if (cc2.gt)
    {
        //TotalLength field is bigger than IP real length
        jmp UpfExceptDiscardWithDfxInfo(CAUSE_UPF_IPNLS_DROP_APPPKT_TOTAL_LEN_BIG, qsIPv4Hdr.DIP, qsIPv4Hdr.SIP,
            rwPktTheoryLen, g_cDesc.RealLength);
    }

    move g_cDesc.DirtyLength = 0;
    bitcmp cc0 = g_stMpf2IpHdrInfo.bIpHdrFlag, ONE_BIT_MASK;
    if (cc0.nz)
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_LIPKT_CALCU_IPID);
        // 计算IPID
        move rhTemp = qsIPv4Hdr.ID;
        // TODO:打桩分配IPID接口
        // UpfIPv4ReAllocIPID(g_stMpf2IpHdrInfo.IpHdrInfo.udwTunnelHashKey, 0); // LI计算IPID使用APP扩展头中的tunnelhashkey
        move qsIPv4Hdr.ID = rwFuncRtnData[:16];
        // 增量计算CHECKSUM
        CalcPartialChecksum(qsIPv4Hdr.Checksum, qsIPv4Hdr.ID, rhTemp);
        add g_cDesc.DirtyLength = g_cDesc.DirtyLength, IPV4_HDR_LEN;
    }

    move g_cDesc.UeFlag = g_stMpf2IpHdrInfo.UeFlag;
    bitcmp cc0 = g_stMpf2IpHdrInfo.bRtHashFlag, ONE_BIT_MASK;
    if (cc0.nz)
    {
        move g_rsUpfDesc.UpfHashKey = g_stMpf2IpHdrInfo.RtHashInfo.udwRtLbKey;
        move g_rsUpfDesc.UpfHashKeyValid = TRUE;
    }
    else
    {
        move g_rsUpfDesc.UpfHashKey = g_stMpf2IpHdrInfo.LbHashKey;
        move g_rsUpfDesc.UpfHashKeyValid = FALSE;
    }

    move g_iDesc.FwdTypeCmd.FwdCmd = 0;
    add rhTemp = g_cDesc.DirtyLength, g_cDesc.AppHeadLen;
    jmp IpuToSearchFibProc(TRUE, g_cDesc.DirtyLength, rhTemp);

}

/* 进行FIB前的预处理 */
IpuPreFibv4Proc(uint1 bRedirect, uint1 bRedirectAct):
{
    (RE_NST_TG_S)     g_rsTblData256_1        rsReNstTg;
    (IPV4_HEADER_S)   q0b               qsIPv4Hdr;

    bitcmp cc0 = bRedirect, ONE_BIT_MASK;
    if (cc0.z)
    {
        SEARCH_IPV4_FIB_TG(rsReNstTg, g_cDesc.FwdInfo.NextHop, g_cDesc.VpnID);
        mskcmp cc1 = IO_RESPONSE_STATUS, SEARCH_MISS;
        if (cc1.eq)
        {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_ERR_RCV_APPPKT_IPV4_FIB_MISS);
        }
    }
    else
    {
        SEARCH_IPV4_FIB_TG(rsReNstTg, g_cDesc.FwdInfo.NextHop, g_cDesc.VpnID);
        mskcmp cc1 = IO_RESPONSE_STATUS, SEARCH_MISS;
        bitcmp cc2 = bRedirectAct, ONE_BIT_MASK;
        if (cc1.eq)
        {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_PERF_IPV4_ING_WEAK_FIB_MISS_RDR_FABRIC);
            if (cc2.nz)
            {
                move g_cDesc.FwdInfo.NextHop = qsIPv4Hdr.DIP;
                SEARCH_IPV4_FIB_TG(rsReNstTg, g_cDesc.FwdInfo.NextHop, g_cDesc.VpnID);
                mskcmp cc1 = IO_RESPONSE_STATUS, SEARCH_MISS;
                if (cc1.eq)
                {
                    jmp UpfExceptDiscard(CAUSE_UPF_IPU_DROP_V4_ING_FIB_MISS_RDR_FAB_FAILACT);
                }
            }
            else
            {
                jmp UpfExceptDiscard(CAUSE_UPF_IPU_DROP_V4_ING_FIB_MISS_RDR);
            }
        }
    }
    jmp iOutIntfDfibReDirectDispatch();
}

/*对于LB/UDG发给IP的PAE业务报文，净荷为IPv6报文的转发处理入口*/
IpuRxFromCslbIpv6Proc():
{
    uint32  rwPktTheoryLen;
    (IPV6_HEADER_S)             q0b             qsIpv6Hdr;
    uint8   rbIptraceEnFlag;
    uint16  rhDelLen;

    // 重组完成的报文使用重组完成计数记录，为保持业务报文统计准确，此处不重复记录
    bitcmp cc0 = g_stMpf2IpHdrInfo.bReassemFlag, ONE_BIT_MASK;
    if (cc0.z) main_path
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_APPPKT_IPV6);
    }
    else
    {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_IPV6_REASSEM_PKT_FROMSSG); // IPV6重组完成报文计数
    }

    move g_cDesc.AppPkt = TRUE;
    move g_cDesc.L2PaRst.L3Type = L2PA_RST_L3TYPE_IPV6;

    CONTEXT_SWITCH()
    movezr g_cDesc.VpnID = g_stMpf2IpHdrInfo.IpVpnIdx[:16];

    bitcmp cc0 = g_stMpf2IpHdrInfo.V6UdpChkSumOlFlag, ONE_BIT_MASK;
    if (cc0.nz) {
        IpuChkSetV6UdpCheckSum(FALSE);
    } else {
        // CheckSum抽样检查NP卸载功能准确性
        IpuChkSumOlSampInspectThresholdCheck();
        cmp cc0 = rwFuncRtnData.Data0, SUCCESSFUL;
        if (cc0.eq) {
            IpuChkSetV6UdpCheckSum(TRUE);
        }
    }

    // /* TODO 先判断匹配用户跟踪 */
    // bitcmp cc0 = rsScratchReg5.UserTrcEn, ONE_BIT_MASK;
    // if (cc0.nz)
    // {
    //     iIpuUsrTrcOutIpV6Judge(TRUE);
    //     bitcmp cc0 = rsUpfDesc.UsrTrcInPortEn, ONE_BIT_MASK;
    //     if (cc0.nz)
    //     {
    //         IpuTraceInLoopTx();
    //     }
    // }
    /* 用户跟踪未命中，再进行IP跟踪的匹配 */
    bitcmp cc0 = g_rsUpfDesc.UserTrcMatch, ONE_BIT_MASK;
    if (cc0.z) {
        movezr rbIptraceEnFlag = g_ssScratch4.UpfIptraceEn;
        bitcmp cc0 = rbIptraceEnFlag[:1], ONE_BIT_MASK;
        if (cc0.nz) {
            IpTrace_Ipv6_Search(g_cDesc.VpnID, 0xFF);
            IpuTraceInLoopTx();
        }
    }

    add rwPktTheoryLen = qsIpv6Hdr.PayloadLen, IPV6_HDR_LEN;
    add rwPktTheoryLen = rwPktTheoryLen, g_cDesc.AppHeadLen;
    cmp cc1 = rwPktTheoryLen, g_cDesc.RealLength;
    if (cc1.gt)
    {
        jmp UpfExceptDiscardWithDfxInfo(CAUSE_UPF_IPNLS_DROP_APPPKT_IPV6_PAYLOAD_LEN_ERR, qsIpv6Hdr.DIP.Part0,
            qsIpv6Hdr.DIP.Part1, qsIpv6Hdr.DIP.Part2, qsIpv6Hdr.DIP.Part3);
    }

    move g_cDesc.UeFlag = g_stMpf2IpHdrInfo.UeFlag;
    bitcmp cc0 = g_stMpf2IpHdrInfo.bRtHashFlag, ONE_BIT_MASK;
    if (cc0.nz)
    {
        move g_rsUpfDesc.UpfHashKey = g_stMpf2IpHdrInfo.RtHashInfo.udwRtLbKey;
        move g_rsUpfDesc.UpfHashKeyValid = TRUE;
    }
    else
    {
        move g_rsUpfDesc.UpfHashKey = g_stMpf2IpHdrInfo.LbHashKey;
        move g_rsUpfDesc.UpfHashKeyValid = FALSE;
    }

    move g_iDesc.FwdTypeCmd.FwdCmd = 0;
    add rhDelLen = g_cDesc.DirtyLength, g_cDesc.AppHeadLen;
    jmp IpuToSearchFibProc(FALSE, g_cDesc.DirtyLength, rhDelLen);
}

IpuPreFibv6Proc(uint1 bRedirect, uint1 bRedirectAct):
{
    (RE_NST_TG_S)               g_rsTblData256_1        rsReNstTg;
    (IPV6_HEADER_S)             q0b             qsIpv6Hdr;

    bitcmp cc0 = bRedirect, ONE_BIT_MASK;
    if (cc0.z)
    {
        SEARCH_IPV6_FIB_TG(rsReNstTg, qsIpv6Hdr.DIP, g_cDesc.VpnID);
        mskcmp cc1 = IO_RESPONSE_STATUS, SEARCH_MISS;
        if (cc1.eq)
        {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_ERR_RCV_APPPKT_IPV6_FIB_MISS);
        }
    }
    else
    {
        SEARCH_IPV6_FIB_TG(rsReNstTg, rsGiRedirectDstIpv6, g_cDesc.VpnID);
        mskcmp cc1 = IO_RESPONSE_STATUS, SEARCH_MISS;
        bitcmp cc2 = bRedirectAct, ONE_BIT_MASK;
        if (cc1.eq)
        {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_PERF_IPV6_ING_WEAK_FIB_MISS_RDR_FABRIC);
            if (cc2.nz)
            {
                SEARCH_IPV6_FIB_TG(rsReNstTg, qsIpv6Hdr.DIP, g_cDesc.VpnID);
                mskcmp cc1 = IO_RESPONSE_STATUS, SEARCH_MISS;
                if (cc1.eq)
                {
                    jmp UpfExceptDiscard(CAUSE_UPF_IPU_DROP_V6_ING_FIB_MISS_RDR_FAB_FAILACT);
                }
                else
                {
                    move rsGiRedirectDstIpv6.Part0 = 0;
                    move rsGiRedirectDstIpv6.Part1 = 0;
                    move rsGiRedirectDstIpv6.Part2 = 0;
                    move rsGiRedirectDstIpv6.Part3 = 0;
                    move g_cDesc.GiRedirectIpv6Flag = FALSE;
                    // move g_rwGiRedirectDstIpv6Part3 = 0;
                }
            }
            else
            {
                jmp UpfExceptDiscard(CAUSE_UPF_IPU_DROP_V6_ING_FIB_MISS_RDR);
            }
        }
    }
    jmp iOutIntfDfibReDirectDispatch();
}

IpuToSearchFibProc(uint1 bIpv4Flag, uint16 rhAddLen, uint16 rhDelLen):
{
    (IPV4_HEADER_S)  q0b  qsIPv4Hdr;
    (IPV6_HEADER_S)  q0b  qsIpv6Hdr;
    (PPA_INPUT_S)       g_rsTblData_2   g_rsPpaInput; // PPA解析用到
    (PPA_OUTPUT_EXT_S)  g_rsTblData_2   g_rsPpaOutputExt; // 不使用，仅作为入参

    bitcmp cc0 = g_ssScratch5.UpfApptoNpLoopSwitch, ONE_BIT_MASK;
    if (cc0.z) /* 先按默认不换回验证 */
    {
        bitcmp cc0 = bIpv4Flag, ONE_BIT_MASK;
        if (cc0.nz)
        {
            PPA0_PARSE_OUTER_IPV4_MAIN(g_rsPpaInput, g_rsPpaOutputExt);
            move g_cDesc.Tos = qsIPv4Hdr.TOS;
            move g_iDesc.FwdTypeCmd.FwdType = INGRESS_DESC_FWDTYPE_ROUTE_V4UC;
            move g_cDesc.L3Policy = 0;
            move g_iDesc.NoTtlDec = TRUE; //设置需要减TTL
            move g_cDesc.FhNotMain = TRUE; //不设置该标志，带option的报文，解析不正确
            move g_cDesc.FwdInfo.NextHop = qsIPv4Hdr.DIP;
            bitcmp cc0 = g_stMpf2IpHdrInfo.bRediFlag, ONE_BIT_MASK;
            if (cc0.nz)
            {
                move g_cDesc.VpnID = g_stMpf2IpHdrInfo.RediInfo.ReVpnId;
                move g_cDesc.FwdInfo.NextHop = g_stMpf2IpHdrInfo.RediInfo.udwReIp46Addr;
            }
            SET_P_EQ_Q();
            jmp IpuPreFibv4Proc(g_stMpf2IpHdrInfo.bRediFlag, g_stMpf2IpHdrInfo.RediInfo.bFailAction);
        }

        PPA0_PARSE_OUTER_IPV6_MAIN(g_rsPpaInput, g_rsPpaOutputExt);
        move g_cDesc.Tos = qsIpv6Hdr.TC;
        move g_iDesc.FwdTypeCmd.FwdType = INGRESS_DESC_FWDTYPE_ROUTE_V6UC;
        move g_cDesc.L3Policy = 0;
        move g_iDesc.NoTtlDec = TRUE; //设置需要减TTL
        move g_cDesc.FhNotMain = TRUE; //与IPV4保持一致
        bitcmp cc0 = g_stMpf2IpHdrInfo.bRediFlag, ONE_BIT_MASK;
        if (cc0.nz)
        {
            move g_cDesc.VpnID = g_stMpf2IpHdrInfo.RediInfo.ReVpnId;
            move rsGiRedirectDstIpv6.Part0 = g_stMpf2IpHdrInfo.RediInfo.rsRediDip6.Part0;
            move rsGiRedirectDstIpv6.Part1 = g_stMpf2IpHdrInfo.RediInfo.rsRediDip6.Part1;
            move rsGiRedirectDstIpv6.Part2 = g_stMpf2IpHdrInfo.RediInfo.rsRediDip6.Part2;
            move rsGiRedirectDstIpv6.Part3 = g_stMpf2IpHdrInfo.RediInfo.rsRediDip6.Part3;
            // move g_rwGiRedirectDstIpv6Part3 = rsGiRedirectDstIpv6.Part3;
            move g_cDesc.GiRedirectIpv6Flag = TRUE;
        }
        SET_P_EQ_Q();
        jmp IpuPreFibv6Proc(g_stMpf2IpHdrInfo.bRediFlag, g_stMpf2IpHdrInfo.RediInfo.bFailAction);
    }
    else //做硬环回后继续fib处理
    {
        // todo:硬环回先打桩，待实现后补齐
        jmp UpfExceptDiscard(1900);
        // jmp IpuHardLoopToFibSend(rhAddLen, rhDelLen, g_stMpf2IpHdrInfo.bRediFlag);
    }
}

/* IP转板流程中，PAE加封装预处理入口，填充传递给PAE的参数 */
IpuTxBtobPrePae():
{
    (IPADDR_V6_S)   p0b       psDstIpv6;

    UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_SND_BTOBPKT);

    bitcmp cc0 = g_cDesc.GiRedirectIpv6Flag, ONE_BIT_MASK; // Gi重定向场景，在转板头和FH帧头之间封装重定向IPv6地址。
    if (cc0.nz) {
        L2_HDR_INC(SIZEOF(IPADDR_V6_S));
        move psDstIpv6.Part0 = rsGiRedirectDstIpv6.Part0;
        move psDstIpv6.Part1 = rsGiRedirectDstIpv6.Part1;
        move psDstIpv6.Part2 = rsGiRedirectDstIpv6.Part2;
        move psDstIpv6.Part3 = rsGiRedirectDstIpv6.Part3;
    }
    L2_HDR_INC(SIZEOF(IPU_PAE_EXT_HDR_S));

    move psPaeExt.GiRedirectIpv6Flag = g_cDesc.GiRedirectIpv6Flag; // 该标记指示是否带ipv6 gi重定向扩展头
    move psPaeExt.HType = PAE_APP_HEAD_TYPE_NLS_BTOB;
    move psPaeExt.MicReplyV6 = g_cDesc.MicReplyIcmpv6Flag;
    move psPaeExt.MatchCntFlag = g_rsUpfDesc.MatchCntFlag;
    move psPaeExt.AppPkt = g_cDesc.AppPkt;
    move psPaeExt.UserTrcMatch = g_rsUpfDesc.UserTrcMatch;
    move psPaeExt.UeIpMatchV4 = g_rsUpfDesc.UeIpMatchV4;
    move psPaeExt.UserTrcUpFlag = FALSE; /* 默认置FALSE */
    bitcmp cc0 = g_rsUpfDesc.UserTrcMatch, ONE_BIT_MASK;
    if (cc0.nz) {
        IpuUsrTrcSetBtoBTxNpInOut();
        move psPaeExt.IpTraceEn = FALSE;
        move psPaeExt.IpTraceId = 0;
        move psPaeExt.UserTrcRuleIndex = g_rsUpfDesc.UserTraceRuleIndex;
    } else {
        move psPaeExt.UserTrcRuleIndex = 0;
        move psPaeExt.IpTraceEn = g_rsUpfDesc.IpTraceEn;
        move psPaeExt.IpTraceId = g_rsUpfDesc.IpTraceId;
    }
    move psPaeExt.Rsvd2 = 0;
    move psPaeExt.BtobComHdrFlag = 0;

    bitcmp cc0 = g_rsUpfDesc.UpfHashKeyValid, ONE_BIT_MASK;
    if (cc0.nz) {
        move rsInfo.LbHashKey = g_rsUpfDesc.UpfHashKey;
    } else {
        move rsInfo.LbHashKey = 0;
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_PAEHASHKEY_INVALID1);
    }

    // IP转板默认使用最低优先级
    PAE_INFO_QOS_CONFIG_DEFAULT_BE(g_cDesc.Dsic.Rqcolor.Remark, g_cDesc.Dsic.Rqcolor.QindexColor.Qindex);
    move rsInfo.bcPlaneid = PAE_PLANEID_AUTOSEL;
    movezr rsInfo.DstTb = g_cDesc.OportInfo.Physical.TB;
    movezr rsInfo.DstTp = NP_IP_TP;
    move rsInfo.IsLogicTb = TRUE;
    move rsInfo.IsToIpuc = FALSE;

    // COPY_TO_PEG(); //超包窗场景, 打桩，后续完善
    jmp Pae_HeadEncap;
}

inline IpuRxBtobClearDesc()
{
    uint16 rhRealLength;
    uint8  rhPullmoreLength;

    move rhRealLength = g_cDesc.RealLength;
    move rhPullmoreLength = g_cDesc.PullmoreLength;
    move g_cDescLw.LW0 = 0;
    move g_cDescLw.LW1 = 0;
    move g_cDescLw.LW2 = 0;
    move g_cDescLw.LW3 = 0;
    move g_cDescLw.LW4 = 0;
    move g_cDescLw.LW5 = 0;
    move g_cDescLw.LW6 = 0;
    move g_cDescLw.LW7 = 0;
    move g_cDescLw.LW8 = 0;
    move g_cDescLw.LW9 = 0;
    move g_cDescLw.LW10 = 0;
    move g_cDescLw.LW11 = 0;
    move g_cDescLw.LW12 = 0;
    move g_cDescLw.LW13 = 0;
    move g_cDescLw.LW14 = 0;
    move g_cDescLw.LW15 = 0;
    move g_cDescLw.LW16 = 0;
    move g_cDescLw.LW17 = 0;
    move g_cDescLw.LW18 = 0;
    move g_cDescLw.LW19 = 0;
    move g_cDescLw.LW20 = 0;
    move g_cDescLw.LW21 = 0;
    move g_cDescLw.LW22 = 0;
    move g_cDescLw.LW23 = 0;

    movezr g_cDescLw.LW0 = rhRealLength;
    move g_cDesc.SrcIntf.Crc = TRUE;
    move g_rsiFastBitmapIntfPolicy.GlobalAcl = g_ssScratch3.iGlobalACL;
    move g_cDesc.PullmoreLength = rhPullmoreLength;
}

/* 报文头版本检查，如果低版本收到了高版本的报文，按照低版本当前可识别的最高版本进行处理；高版本发送报文需要适配历史所有低版本 */
inline IpuRxBtobHdrVersionCheck(uint8 rbHdrVersion)
{
    cmp cc0 = rbHdrVersion, IPU_BTOB_TOCP_VERSION_BUTT;
    if (cc0.ge) {
        move rbHdrVersion = IPU_BTOB_TOCP_VERSION_BUTT - 1;
    }
}

/* IP转板流程中，IPU模块接收处理：剥离PAE头和Common头，识别帧头类型 */
IpuRxBtobProc():
{
    uint1  BtobComHdrFlag;
    uint1  GiRedirectIpv6Flag;
    IPTRACE_INFO_S rsIpTraceInfo;
    (IPADDR_V6_S)  q0b  qsDstIpv6;
    (IPU_BTOB_COM_HDR_S)  q0b  qsIpuBtobCom;

    move g_cDesc.MicReplyIcmpv6Flag = qsPaeExt.MicReplyV6;
    move g_rsUpfDesc.MatchCntFlag = qsPaeExt.MatchCntFlag;
    move g_cDesc.AppPkt = qsPaeExt.AppPkt;
    move g_rsUpfDesc.UeIpMatchV4 = qsPaeExt.UeIpMatchV4;
    move BtobComHdrFlag = qsPaeExt.BtobComHdrFlag;
    move GiRedirectIpv6Flag = qsPaeExt.GiRedirectIpv6Flag;
    move rsIpTraceInfo.IpTraceEn = qsPaeExt.IpTraceEn;
    move rsIpTraceInfo.IpTraceId = qsPaeExt.IpTraceId;
    move rsIpTraceInfo.Rsvd = 0;
    /* IpuRxBtobProcNext后面直接清g_cDesc，此处使用LW23临时缓存IPU_PAE_EXT_HDR_S结构 */
    move g_cDescLw.LW23 = q0w;
    UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_RCV_BTOBPKT);

    SR_Q_EX(SIZEOF(IPU_PAE_EXT_HDR_S));

    bitcmp cc0 = BtobComHdrFlag, ONE_BIT_MASK;
    if (cc0.nz) {
        mskcmp cc0 = qsIpuBtobCom.NextHdrType, IPU_BTOB_EXT_HDR_TYPE_TOCP;
        if (cc0.eq) {
            SR_Q_EX(SIZEOF(IPU_BTOB_COM_HDR_S));
            jmp IpuRxBtobToCpProc(rsIpTraceInfo);
        }
        // 原有转板类型，后续接帧头
        SR_Q_EX(SIZEOF(IPU_BTOB_COM_HDR_S));
    }

    bitcmp cc0 = GiRedirectIpv6Flag, ONE_BIT_MASK;
    if (cc0.nz) {
        move rsGiRedirectDstIpv6.Part0 = qsDstIpv6.Part0;
        move rsGiRedirectDstIpv6.Part1 = qsDstIpv6.Part1;
        move rsGiRedirectDstIpv6.Part2 = qsDstIpv6.Part2;
        move rsGiRedirectDstIpv6.Part3 = qsDstIpv6.Part3;
        // move g_rwGiRedirectDstIpv6Part3 = qsDstIpv6.Part3;
        move g_cDesc.GiRedirectIpv6Flag = TRUE;
        SR_Q_EX(SIZEOF(IPADDR_V6_S));
    }
    // P Q指针对齐，指向TM头
    TnlTerminationReset();
    
    iFhSoftSwitch();

    next IpuRxBtobFhProc;
}

inline iFhSoftSwitch()
{
    uint8          fhLength;
    uint8          fhL3Stake;
    (FH_U)   E_FH_OFFSET_Q   puFh;

    /* 计算帧头长度 */
    movezr fhLength[3..2] = puFh.Basic.Fhl;
    add    fhLength = fhLength, FH_FHE_LEN;
    movezr fhL3Stake = puFh.Basic.L3stake;
    add    fhLength = fhLength, fhL3Stake;

    mskcmp cc1 = puFh.Basic.IsUc, FH_UC;
    if (cc1.eq) {
        mskcmp cc1 = puFh.Basic.Fhf, FHF_U3;
    }
    if (cc1.eq) {
        // U3帧同时剥掉MPLS标签
        add fhLength = fhLength, SIZEOF(SHIM_S);
    }

    add fhLength = fhLength, SIZEOF(ITM_UC_S);
    // P指向fh帧头后面，Q指向TM头
    SR_P_EX_SET_VALUE(fhLength, FH_PARSE_MIN_DEL_LEN, FH_PARSE_MAX_DEL_LEN)
}

IpuRxBtobFhProc():
{
    (FH_U)  E_FH_OFFSET_Q  ruFh;

    switch
    {
        // U1 NextHop
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U1 == ruFh.Basic.Fhf) &&
            (DT_NH == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U1_ARP);

        // U1 AT
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U1 == ruFh.Basic.Fhf) &&
            (DT_AT == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U1_AT);

        // U1 Vlanif
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U1 == ruFh.Basic.Fhf) &&
            (DT_VLANIF == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U1_VLANIF);

        // U21 v4
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_IP4 == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U2_1_IPV4);

        // U21 v6
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_IP6 == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U2_1_IPV6);

        // U23
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_ETH == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U2_3);

        // U24
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_VLL == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U2_4);

        // U3
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U3 == ruFh.Basic.Fhf)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U3);

        // U4
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U4 == ruFh.Basic.Fhf)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U4);

        // U6 ND
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U6 == ruFh.Basic.Fhf) &&
            (DT_ND == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U6_ND);

        // U6 NDH
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U6 == ruFh.Basic.Fhf) &&
            (DT_NDH == ruFh.Basic.DataType)
            :IpuRxBtobProcNext(NGSF_EGRESSPKTTYPE_U6_NDH);

        // /* TODO:其余类型识别待补充 */

        default
            :RX_UNKOWN_FH;
    }

RX_UNKOWN_FH:
    jmp UpfExceptDiscardWithDfxInfo(CAUSE_UPF_IPNLS_DROP_BTOB_RX_UNKOWN_FH, ruFh.Basic.IsUc,
        ruFh.Basic.Fhf, ruFh.Basic.DataType, zero);
}

/* IP转板流程中，IPU模块接收处理：设置HwPa结果，清除一级接口，进入分发流程 */
IpuRxBtobProcNext(uint8 rbPktType):
{
    uint32 rwData;
    (IPU_PAE_EXT_HDR_S) rwData rsPaeExt;
    (E_NP_IN_HDR_S) E_TM_OFFSET_Q qsNpInHdr;
    (ITM_UC_S) E_TM_OFFSET_Q qsTM;

    IpuRxBtobDbgStat(rbPktType);

    move g_rsHwInfo.PortType = PAINFO_PORTTYPE_EGRESS;
    move rsPaeExt = g_cDescLw.LW23;

    IpuRxBtobClearDesc();

    move g_cDesc.Dsic.Rmkqc.RemarkQindex.Qindex = BE;
    move g_rsUpfDesc.UserTrcMatch = rsPaeExt.UserTrcMatch;
    bitcmp cc0 = g_rsUpfDesc.UserTrcMatch, ONE_BIT_MASK;
    if (cc0.nz) {
        IpuUsrTrcBtoBRxSetTrcDir();
        move g_rsUpfDesc.IpTraceEn = FALSE;
        move g_rsUpfDesc.IpTraceId = 0;
        move g_rsUpfDesc.UserTraceRuleIndex = rsPaeExt.UserTrcRuleIndex;
    } else {
        move g_rsUpfDesc.IpTraceEn = rsPaeExt.IpTraceEn;
        move g_rsUpfDesc.IpTraceId = rsPaeExt.IpTraceId;
    } 

    // Egress处理, 5897下行查表用的special结构体，大小和ITM一样都是6字节，这里需要转换一下
    movezr qsNpInHdr.Egress.MatchCntFlag = g_rsUpfDesc.MatchCntFlag;
    movezr qsNpInHdr.Egress.IpTraceEn = g_rsUpfDesc.IpTraceEn;
    movezr qsNpInHdr.Egress.TraceId = g_rsUpfDesc.IpTraceId;
    movezr qsNpInHdr.Egress.FromPae = g_cDesc.AppPkt;
    movezr qsNpInHdr.Egress.EgInChannel = qsTM.ITM.eInputChn;
    jmp MainEgress;
}

// 根据当前能够识别到的最高报文进行报文解析
inline IpuRxBtobToCpHdrParser(uint8 rbBtobToCpType)
{
    uint8  rbHdrVersion;

    move rbBtobToCpType = IPU_BTOB_TOCP_TYPE_MAX;
    movezr rbHdrVersion = qsIpuBtobTocp.Version;
    IpuRxBtobHdrVersionCheck(rbHdrVersion);
    cmp cc0 = rbHdrVersion, IPU_BTOB_TOCP_VERSION_24_0;
    if (cc0.eq) {
        movezr rbBtobToCpType = qsIpuBtobTocp.BtobToCpType;
        move g_cDesc.Dsic.Rqcolor.QindexColor.Qindex = qsIpuBtobTocp.Qindex;
        move g_cDesc.VpnID = qsIpuBtobTocp.VpnId;
        move g_cDesc.CauseID = qsIpuBtobTocp.CauseId;
    }
}

/* IP转板接收上送流程 */
IpuRxBtobToCpProc(IPTRACE_INFO_S rsIpTraceInfo):
{
    uint8 l3Stake;
    uint8   rbBtobToCpType;
    (IPU_BTOB_TOCP_HDR_S)  q0b  qsIpuBtobTocp;

    GET_L3_STAKE(l3Stake);
    sub g_cDesc.RealLength = g_cDesc.RealLength, l3Stake;
    IpuRxBtobClearDesc();
    move g_rsUpfDesc.IpTraceEn = rsIpTraceInfo.IpTraceEn;
    move g_rsUpfDesc.IpTraceId = rsIpTraceInfo.IpTraceId;

    IpuRxBtobToCpHdrParser(rbBtobToCpType);
    // 转板上送不是主流程，因此此处不使用DBG计数
    UpfExceptStatOnCondEqual(rbBtobToCpType, IPU_BTOB_TOCP_TYPE_BGPRDT, CAUSE_UPF_IPU_NORMAL_GRAY_UPDRADE_BGPRDT_RCV_PKT);

    SR_Q_EX(qsIpuBtobTocp.Lenth);
    sub g_cDesc.RealLength = g_cDesc.RealLength, qsIpuBtobTocp.Lenth;
    SET_P_EQ_Q();  // P、Q指针对齐
    move g_cDesc.L2Length = 0;
    
    next IpuRxBtobToCarProc(rbBtobToCpType);
}

IpuRxBtobToCarProc(const uint8 rbBtobToCpType):
{
    uint8 cpType;
    CAUSE_TABLE_S  rsCauseTbl;

    move cpType = rbBtobToCpType;
    READ_CAUSE_TBL(rsCauseTbl, g_cDesc.CauseID);
    IpuCpCarDropAndExceptTocp(rsCauseTbl.CarIndex, rsCauseTbl.TotalCarEn); // 上送Car
    cmp cc0 = rwFuncRtnData.Data0, SUCCESSFUL;
    if (cc0.ne) {
        cmp cc0 = cpType, IPU_BTOB_TOCP_TYPE_BGPRDT;
        if (cc0.eq) {
            movezr g_cDesc.CauseID = CAUSE_UPF_IPU_DROP_GRAY_UPDRADE_BGPRDT_TOCP_CARDROP;
        }
        jmp iExcept_ToCpDrop;
    }

    next IpuRxBtobReadChannelProc(rsCauseTbl.CpuOportInfo.Physical);
}

IpuRxBtobReadChannelProc(const uint14 rbTblIndex):
{
    IFINDEXFWD_S    rsIfIndexContextTbl;
    READ_IFINDEX_CONTEXT_TBL(rsIfIndexContextTbl, rbTblIndex);
    bitcmp cc0 = rsIfIndexContextTbl.V, ONE_BIT_MASK;
    if (cc0.z) {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_ERR_IFINDEX_INVALID);
    }
    next IpuRxBtobEpatProc(rsIfIndexContextTbl.eInputChn);
}

IpuRxBtobEpatProc(const uint10 rbInputChn):
{
    READ_EPAT_TBL(g_rsEpatLocal, rbInputChn);  // AddNp2CpHdr有用EPAT数据
    next IpuRxBtobHdrProc;
}

IpuRxBtobHdrProc():
{
    uint8 l3Stake;
    move g_cDesc.SbSp = IPU_INVALID_SBSP; // 转板上送LDM不处理判断入接口板端口信息，与FEI对齐设置为无效值
    AddNp2CpHdr();                        // 直接封装NP2CP头进行上送处理

    GET_L3_STAKE(l3Stake);
    move g_cDesc.L2Length = l3Stake;

    jmp IpuTxCpuIpCtrlPrePae;
}

void IpuRxBtobDbgStat(uint8 rbPktType)
{
    bitcmp cc5 = g_ssScratch5.UpfDbgSwitch, ONE_BIT_MASK;
    if (cc5.nz) {
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U1_ARP, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U1_ARP);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U1_AT, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U1_AT);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U1_VLANIF, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U1_VLANIF);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U2_1_IPV4, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U2_1_IPV4);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U2_1_IPV6, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U2_1_IPV6);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U2_3, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U2_3);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U2_4, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U2_4);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U3, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U3);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U4, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U4);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U6_ND, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U6_ND);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_U6_NDH, CAUSE_UPF_IPU_DBGCNT_BTOBRX_U6_NDH);
        UpfExceptStatOnCondEqual(rbPktType, NGSF_EGRESSPKTTYPE_MJ, CAUSE_UPF_IPU_DBGCNT_BTOBRX_MJ);
    }
    return;
}
// IP转板发包  P Q指针当前指向TM头，ITM(6)+FH(10/14/...)+IP
IpuTxBtobDbgStat():
{
    (FH_U)      E_FH_OFFSET_Q      ruFh;

    bitcmp cc5 = g_ssScratch5.UpfDbgSwitch, ONE_BIT_MASK;

    switch
    {
        case (cc5.z) // switch之前不应该有跳转指令,所以放到第一个判断
            :IpuTxBtobPrePae;
        // U1 NextHop
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U1 == ruFh.Basic.Fhf) &&
            (DT_NH == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U1_ARP);

        // U1 AT
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U1 == ruFh.Basic.Fhf) &&
            (DT_AT == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U1_AT);

        // U1 Vlanif
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U1 == ruFh.Basic.Fhf) &&
            (DT_VLANIF == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U1_VLANIF);

        // U21 v4
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_IP4 == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U2_1_IPV4);

        // U21 v6
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_IP6 == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U2_1_IPV6);

        // U23
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_ETH == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U2_3);

        // U24
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U2 == ruFh.Basic.Fhf) &&
            (DT_VLL == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U2_4);

        // U3
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U3 == ruFh.Basic.Fhf)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U3);

        // U4
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U4 == ruFh.Basic.Fhf)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U4);

        // U6 ND
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U6 == ruFh.Basic.Fhf) &&
            (DT_ND == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U6_ND);

        // U6 NDH
        case (TRUE == ruFh.Basic.IsUc) &&
            (FHF_U6 == ruFh.Basic.Fhf) &&
            (DT_NDH == ruFh.Basic.DataType)
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_U6_NDH);

        /* TODO:其余类型识别待补充 */

        default
            :IpuTxBtobDbgStatNext(CAUSE_UPF_IPU_DBGCNT_BTOBTX_OTHER_FH);
    }
}

IpuTxBtobDbgStatNext(uint16 StatId):
{
    UpfExceptStatNoDiscard(StatId);
    jmp IpuTxBtobPrePae();
}

void IpuCpCarDropAndExceptTocp(uint32 rwCarid, uint1 TotalCarEn)
{
    BOARD_CONFIG_S  rsBoardConfig;

    // 基于CarId进行Car
    cmp cc0 = rwCarid, 0;
    if (cc0.ne) {
        Car_Util(rwCarid[:20]);
        bitcmp cc0 = g_cDesc.CarDrop, ONE_BIT_MASK;
        if (cc0.nz) {
            move rwFuncRtnData.Data0 = FAILED;
            return;
        }
    }
    // 整机Car
    bitcmp cc0 = TotalCarEn, ONE_BIT_MASK;
    if (cc0.nz) {
        ReadBoardCfg(rsBoardConfig);
        bitcmp cc1 = rsBoardConfig.TotalCarEn, ONE_BIT_MASK;
        if (cc1.nz) {
            Car_Util(rsBoardConfig.TotalCarId);
            bitcmp cc2 = g_cDesc.CarDrop, ONE_BIT_MASK;
            if (cc2.nz) {
                move rwFuncRtnData.Data0 = FAILED;
                return;
            }
        }
    }

    // 两次都不car则记录上送计数
    Except_ToCpStat(g_cDesc.CauseID);

    move rwFuncRtnData.Data0 = SUCCESSFUL;
    return;
}

inline VFP_MAKE_HASHVALUE_BYIP(uint32 x, uint32 value)
{
    move value = 0;
    xor value = x[31:8], x[23:8];
    xor value = value, x[15:8];
    xor value = value, x[7:8];
}

inline VFP_MAKE_HASHVALUE_BYPORT(uint16 x, uint32 value)
{
    move value = 0;
    xor value = x[15:8], x[7:8];
}

/*
    分片报文：DIP + SIP + PRO;
  GI标记报文：
               IPV4:默认： DIP + VPN;
                    UDP: GTPV1: DIP + VPN + TEID; 无TEID: DIP + VPN + SIP + SPT + DST + PRO;

               IPV6: IP + VPN;
    其他报文:
               IPV4:默认: DIP + VPN;
                    UDP: GTPV1: DIP + VPN + TEID;

               IPV6: IP + VPN;

*/
/* 计算PAE hash key
调用此函数时需注意：
1）已读完RE表，且内容未被覆盖；
2）Q指针指向IP头；
*/

void IpuCalcPAEHashKey()
{
    (IPV4_HEADER_S)                q0b   psIPv4Hdr;
    (IPV6_HEADER_S)                q0b   psIPv6Hdr;
    (IPV4_UDP_S)                   q0b   psIpv4Udp;
    // PAEHash下一次紧挨着的函数OutintfInit里会清空如下寄存器，所以这里先借用g_rsTblData256_2.LW3寄存器，应该不会和别人冲突
    (uint32) g_rsTblData256_2.LW3 rwTmpIpHash0;
    (uint32) g_rsTblData256_2.LW4 rwTmpIpHash1;
    (uint32) g_rsTblData256_2.LW5 rwTmpPortHash;
    (uint32) g_rsTblData256_2.LW6 rwTmpPaeHashValue;
    uint8  rbTmpReOpcode;


    bitcmp cc0 = g_rsUpfDesc.UpfHashKeyValid, ONE_BIT_MASK;
    if (cc0.nz) {
        return;
    }

    bitcmp cc0 = g_rsDstRe.V, ONE_BIT_MASK;
    if (cc0.z) {
        return;
    }

    UPF_ZERO_COMMON_32BIT_DATA(rsUpfIpPktL3Info);  // 调用UpfParseIpPkt前显式清空解析结果，避免使用时报告警
    UPF_ZERO_COMMON_128BIT_DATA(rsUpfIpPktL4Info); // 调用UpfParseIpPkt前显式清空解析结果，避免使用时报告警

    /* Gi service报文hashkey计算 */
    movezr rbTmpReOpcode = g_rsDstRe.OpCode;
    cmp cc2 = rbTmpReOpcode, RE_OPCODE_WLR;
    if (cc2.eq) {
        bitcmp cc0 = g_rsDstRe.Wlr.GiServiceFlag, ONE_BIT_MASK;
        if (cc0.nz) {
            /* IPV4RECMP_GetUsrHashKey */
            mskcmp cc0 = psIPv4Hdr.Version, IPV4_VERSION;
            if (cc0.eq) {
                VFP_MAKE_HASHVALUE_BYIP(psIPv4Hdr.DIP, rwTmpIpHash0);
                VFP_MAKE_HASHVALUE_BYPORT(g_rsDstRe.Wlr.VpnId, rwTmpPortHash);
                xor rwTmpPaeHashValue = rwTmpIpHash0, rwTmpPortHash;
                jmp SavePaeHashResult;
            }
            /* IPV6ING_GetPAEHashKey */
            mskcmp cc0 = psIPv4Hdr.Version, IPV6_VERSION;
            if (cc0.eq) {
                jmp Ipv6GiHashCalc;
            } else {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPNLS_DROP_PKT_PARSE_ERR); // 异常报文
                move rwTmpPaeHashValue = 0;
                jmp SavePaeHashResult;
            }
        }
        /* 解析报文 */
        // UpfParseIpPkt(IP_PARSER_TYPE_L3L4); 打桩，待IP解析整改后放开，有些用例IP接触出来后PQ窗内容变了 /* VNAT快转需要解析SPORT, DPORT, SPI */

        mskcmp cc0 = rsUpfIpPktL3Info.ParseCauseId, SUCCESSFUL;
        if (cc0.ne) {
            movezr rwTmpPaeHashValue = rsUpfIpPktL3Info.ParseCauseId;
            add rwTmpPaeHashValue = rwTmpPaeHashValue, CAUSE_UPF_COMMON_PAESER_BASE;
            UpfExceptStatNoDiscard(rwTmpPaeHashValue[:16]); // 异常报文
            move rwTmpPaeHashValue = 0;
            jmp SavePaeHashResult;
        }

        bitcmp cc0 = g_ssScratch5.UpfLbHashSwitch, ONE_BIT_MASK; // 开启后统一按照3元组做hash
        if (cc0.nz) {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_LBHASH_3TUPLE);
            mskcmp cc0 = psIPv4Hdr.Version, IPV4_VERSION;
            if (cc0.eq) {
                jmp Ipv4DefaultHashCalc;
            } else {
                jmp Ipv6DefaultHashCalc;
            }
        }
    }

    /* 解析报文 */
    bitcmp cc0 = rsUpfIpPktL3Info.ParseDoneFlag, TRUE;
    if (cc0.z) {
        // UpfParseIpPkt(IP_PARSER_TYPE_L3L4); 打桩，待IP解析整改后放开，有些用例IP接触出来后PQ窗内容变了 // todo LB卸载后如果由于分片等原因没有解析报文，到LB的时候需要再做报文解析（GI的可以不用）
        mskcmp cc0 = rsUpfIpPktL3Info.ParseCauseId, SUCCESSFUL;
        if (cc0.ne) {
            movezr rwTmpPaeHashValue = rsUpfIpPktL3Info.ParseCauseId;
            add rwTmpPaeHashValue = rwTmpPaeHashValue, CAUSE_UPF_COMMON_PAESER_BASE;
            UpfExceptStatNoDiscard(rwTmpPaeHashValue[:16]); // 异常报文
            move rwTmpPaeHashValue = 0;
            jmp SavePaeHashResult;
        }
    }

    /* 分片报文 使用3元组 */
    bitcmp cc0 = rsUpfIpPktL3Info.FragFlg, TWO_BIT_MASK;
    if (cc0.nz) {
        mskcmp cc0 = psIPv4Hdr.Version, IPV4_VERSION;
        if (cc0.eq) {
            jmp Ipv4DefaultHashCalc;
        } else {
            jmp Ipv6DefaultHashCalc;
        }
    }

    cmp cc0 = rsUpfIpPktL3Info.Protocol, IPV4_PROT_UDP;
    if (cc0.eq) {
        /* GTPV1 数据报文 */
        cmp cc2 = rsUpfIpPktL4Info.AppType, UPF_IP_APPTYPE_GTP;
        if (cc2.eq) {
            bitcmp cc1 = rsUpfIpPktL4Info.GtpPktInfo.GtpuFlag, ONE_BIT_MASK;
            if (cc1.nz) {
                move rwTmpPaeHashValue = rsUpfIpPktL4Info.GtpPktInfo.Teid;
                jmp SavePaeHashResult;
            }
        }

        /* L2TP */
        cmp cc1 = rsUpfIpPktL4Info.DPort, UDP_DPORT_L2TP;
        if (cc1.eq) {
            bitcmp cc0 = rsUpfIpPktL4Info.L2tpPktInfo.TFlg, ONE_BIT_MASK;
            if (cc0.nz) {
                movezr rwTmpPaeHashValue = rsUpfIpPktL4Info.L2tpPktInfo.TunnelIdSessionId.TunnelId;
                jmp SavePaeHashResult;
            }
            jmp Ipv4UdpTcpStcpHashCalc;
        }

        /* IPSEC WITHPAD */
        cmp cc1 = rsUpfIpPktL4Info.DPort, UDP_DPORT_IPSEC_WITHPAD;
        if (cc1.eq) {
            move rwTmpPaeHashValue = rsUpfIpPktL4Info.IpsecPktInfo.Spi;
            jmp SavePaeHashResult;
        }

        /* IPSEC WITHOUTPAD */
        cmp cc1 = rsUpfIpPktL4Info.DPort, UDP_DPORT_IPSEC_WITHOUTPAD;
        if (cc1.eq) {
            move rwTmpPaeHashValue = rsUpfIpPktL4Info.IpsecPktInfo.Spi;
            jmp SavePaeHashResult;
        }
    }

    /* 其他UDP, TCP, SCTP加入端口号 */
    cmp cc1 = rsUpfIpPktL3Info.Protocol, IPV4_PROT_TCP;
    cmp cc2 = rsUpfIpPktL3Info.Protocol, IPV4_PROT_SCTP;
    bitcmp cc5 = ccr[:6], 0b010101;
    if (cc5.nz) {
        mskcmp cc0 = psIPv4Hdr.Version, IPV4_VERSION;
        if (cc0.eq) {
            jmp Ipv4UdpTcpStcpHashCalc;
        } else {
            jmp Ipv6DefaultHashCalc;
        }
    }

    /* ESP */
    cmp cc0 = rsUpfIpPktL3Info.Protocol, IPV4_PROT_ESP;
    if (cc0.eq) {
        move rwTmpPaeHashValue = rsUpfIpPktL4Info.IpsecPktInfo.Spi;
        jmp SavePaeHashResult;
    }

    /* AH */
    cmp cc0 = rsUpfIpPktL3Info.Protocol, IPV4_PROT_AH;
    if (cc0.eq) {
        move rwTmpPaeHashValue = rsUpfIpPktL4Info.IpsecPktInfo.Spi;
        jmp SavePaeHashResult;
    }

    /* 其他协议 */
    mskcmp cc0 = psIPv4Hdr.Version, IPV4_VERSION;
    if (cc0.eq) {
        jmp Ipv4DefaultHashCalc;
    } else {
        jmp Ipv6DefaultHashCalc;
    }

Ipv4UdpTcpStcpHashCalc:
    VFP_MAKE_HASHVALUE_BYIP(psIPv4Hdr.DIP, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv4Hdr.SIP, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpIpHash0, rwTmpIpHash1;
    VFP_MAKE_HASHVALUE_BYPORT(rsUpfIpPktL4Info.SPort, rwTmpPortHash);
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpPortHash;
    VFP_MAKE_HASHVALUE_BYPORT(rsUpfIpPktL4Info.DPort, rwTmpPortHash);
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpPortHash;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rsUpfIpPktL3Info.Protocol;
    jmp SavePaeHashResult;

Ipv4DefaultHashCalc:
    VFP_MAKE_HASHVALUE_BYIP(psIPv4Hdr.DIP, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv4Hdr.SIP, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpIpHash0, rwTmpIpHash1;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rsUpfIpPktL3Info.Protocol;
    jmp SavePaeHashResult;

Ipv6GiHashCalc:
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part0, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part1, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpIpHash0, rwTmpIpHash1;
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part2, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part3, rwTmpIpHash1);
    VFP_MAKE_HASHVALUE_BYPORT(g_rsDstRe.Wlr.VpnId, rwTmpPortHash);
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash0;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash1;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpPortHash;
    jmp SavePaeHashResult;

Ipv6DefaultHashCalc:
    movezr rbTmpReOpcode = rsUpfIpPktL3Info.Protocol; // 借用此临时变量
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part0, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part1, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpIpHash0, rwTmpIpHash1;
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part2, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.DIP.Part3, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash0;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash1;
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.SIP.Part0, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.SIP.Part1, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash0;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash1;
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.SIP.Part2, rwTmpIpHash0);
    VFP_MAKE_HASHVALUE_BYIP(psIPv6Hdr.SIP.Part3, rwTmpIpHash1);
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash0;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rwTmpIpHash1;
    xor rwTmpPaeHashValue = rwTmpPaeHashValue, rbTmpReOpcode;
    jmp SavePaeHashResult;

SavePaeHashResult:
    move g_rsUpfDesc.UpfHashKey = rwTmpPaeHashValue;
    move g_rsUpfDesc.UpfHashKeyValid = TRUE;

    return;
}

// IPv4和IPv6判断是否是WLR路由合一函数
void IPEG_CheckDstIpIsWlr()
{
    RE_S  rsDstRe;
    (IPV4_HEADER_S) q0b qsIPv4Hdr;

    mskcmp cc1 = qsIPv4Hdr.Version, IPV4_VERSION;
    if (cc1.eq) {
        Ipv4FibSearch(g_cDesc.VpnID, qsIPv4Hdr.DIP, rsDstRe);
    } else {
        Ipv6DipFibSearch(g_cDesc.VpnID, rsDstRe);
    }

    bitcmp cc1 = IO_RESPONSE_STATUS, FOUR_BIT_MASK;
    if (cc1.z) {
        bitcmp cc1 = rsDstRe.V, ONE_BIT_MASK;
        if (cc1.nz) {
            mskcmp cc1 = rsDstRe.OpCode, RE_OPCODE_WLR;
            if (cc1.eq) {
                move g_cDesc.L3PaRst.L3PaFlag.L3ToCP = 0;
                Except_DiscardStat(CAUSE_UPF_IPU_NORMAL_WLR_NOT_TOCP);
            }
        }
    }

    return;
}

void IpuOspfMcSuppressionCheck()
{
    // 与软转发保持一致，IPV4报文判断协议为OSPF+组播IP，根据源IP+VPN查路由为CPU进行组播抑制
    // IPV6报文判断解析结果为OSPF+组播MAC+非LB接收报文，根据源MAC查PORT_MAC表有效进行组播抑制
    uint8  rbSubDip;
    RE_S           rsDstReTemp;
    (IPV4_HEADER_S)             q0b            qsIpv4Hdr;

    move rwFuncRtnData.Data0 = SUCCESSFUL;
    mskcmp cc0 = qsIpv4Hdr.Version, IPV4_VERSION;
    if (cc0.eq) {
        cmp cc1 = qsIpv4Hdr.TtlProtWord.Protocol, IPV4_PROT_OSPF;
        if (cc1.ne) {
            return;
        }
        movezr rbSubDip = qsIpv4Hdr.DIP[31:4];
        cmp cc1 = rbSubDip, IPV4_MC_DIP;
        if (cc1.ne) {
            return;
        }
        Ipv4FibSearch(g_cDesc.VpnID, qsIpv4Hdr.SIP, rsDstReTemp);
        bitcmp cc1 = rsDstReTemp.V, ONE_BIT_MASK;
        if (cc1.nz) {
            mskcmp cc0 = rsDstReTemp.OpCode, RE_OPCODE_CPU;
            if (cc0.eq) {
                move rwFuncRtnData.Data0 = FAILED;
            }
        }
    } else {
        mskcmp cc1 = g_cDesc.L3PaRst.L4Type, L3PA_RST_L4TYPE_L4_OSPFV3;
        if (cc1.ne) {
            return;
        }
        mskcmp cc1 = g_cDesc.L2PaRst.DmacType, L2PA_RST_COMMON_INFO_DMACTYPE_MC;
        if (cc1.ne) {
            return;
        }
        bitcmp cc0 = g_cDesc.AppPkt, ONE_BIT_MASK;
        if (cc0.nz) {
            return;
        }
        SL_Q_EX(g_cDesc.L2Length);
        IpuOspfMcSuppressionCheckV6();
        SR_Q_EX(g_cDesc.L2Length);
    }
}

void IpuOspfMcSuppressionCheckV6()
{
    IPU_PORT_MAC_DATA_S rsPortMac;
    (ETH_S)                     q0b            qsEthHdr;

    SEARCH_IPU_PORT_MAC(rsPortMac, qsEthHdr.SmacH16, qsEthHdr.SmacL32);
    bitcmp cc1 = rsPortMac.Valid, ONE_BIT_MASK;
    if (cc1.nz) {
        move rwFuncRtnData.Data0 = FAILED;
    }
    return;
}

inline IpuUj3PacketStat()
{
    (ARP_PKT_S)         q0b    qsArpPkt;
    (DHCPV6_REQUEST_S)  q48b   qsDhcpv6Pkt;
    (IPV6_ICMP_S)       q0b    qsIpv6IcmpHdr;

    bitcmp cc5 = g_ssScratch5.UpfDbgSwitch, ONE_BIT_MASK;
    if (cc5.nz) branch_path {
        // ARP报文需要进一步解析细化CauseID
        cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_ETH_ARP;
        if (cc0.eq) {
            cmp cc0 = qsArpPkt.Opcode, ARP_REQUEST_OPCODE;
            if (cc0.eq) {
                move g_cDesc.CauseID =  EXCP_ID_IPV4_ARP_REQUEST;
            }
            cmp cc0 = qsArpPkt.Opcode, ARP_REPLY_OPCODE;
            if (cc0.eq) {
                move g_cDesc.CauseID =  EXCP_ID_IPV4_ARP_REPLY;
            }
        }

        // Dhcpv6报文具体类型无法通过CauseID区分，需要解析报文进一步解析
        // 入口统一做了scratch的判断，此处直接使用UpfExceptStatNoDiscard统计即可
        cmp cc0 = g_cDesc.CauseID, EXCP_ID_IPV6_DHCP;
        if (cc0.eq) {
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_SOLICIT_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_SOCILIT);
            }
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_REQUEST_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_REQUEST);
            }
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_CONFIRM_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_CONFIRM);
            }
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_RENEW_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_RENEW);
            }
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_REBIND_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_REBIND);
            }
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_RELEASE_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_RELEASE);
            }
            cmp cc0 = qsDhcpv6Pkt.MsgType, DHCPV6_DECLINE_OPTION_VALUE;
            if (cc0.eq) {
                UpfExceptStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_UJ3_DHCPV6_DECLINE);
            }
        }

        // ICMPv6报文需要解析报文内容进一步细分统计
        cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV6;
        if (cc0.eq) {
            cmp cc0 = qsIpv6IcmpHdr.stIPv6Hdr.NextHeader, IPV6_PROT_ICMP;
            if (cc0.eq) {
                UpfExceptStatOnCondEqual(qsIpv6IcmpHdr.stIcmp.Type,
                    ICMPV6_PKT_TYPE_CANNOT_REACH, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMPV6_TYPE1_CANNOT_REACH);
                UpfExceptStatOnCondEqual(qsIpv6IcmpHdr.stIcmp.Type,
                    ICMPV6_PKT_TYPE_TOOBIG, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMPV6_TYPE2_TOOBIG);
                UpfExceptStatOnCondEqual(qsIpv6IcmpHdr.stIcmp.Type,
                    ICMPV6_PKT_TYPE_TIME_OUT, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMPV6_TYPE3_TIME_OUT);
                UpfExceptStatOnCondEqual(qsIpv6IcmpHdr.stIcmp.Type,
                    ICMPV6_PKT_TYPE_PARAM_ERROR, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMPV6_TYPE4_PARAM_ERROR);
                UpfExceptStatOnCondEqual(qsIpv6IcmpHdr.stIcmp.Type,
                    ICMPV6_PKT_TYPE_ECHO_REQUEST, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMPV6_TYPE128_ECHO_REQUEST);
                UpfExceptStatOnCondEqual(qsIpv6IcmpHdr.stIcmp.Type,
                    ICMPV6_PKT_TYPE_ECHO_REPLY, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMPV6_TYPE129_ECHO_REPLY);
            }
        }
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_REDIRECT_ICMP, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMP4);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_REDIRECT_ICMP, CAUSE_UPF_IPU_DBGCNT_UJ3_ICMP6);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_ARP_REQUEST, CAUSE_UPF_IPU_DBGCNT_UJ3_ARP_REQUEST);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV4_ARP_REPLY, CAUSE_UPF_IPU_DBGCNT_UJ3_ARP_REPLY);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_NS, CAUSE_UPF_IPU_DBGCNT_UJ3_NS);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_MC_NS, CAUSE_UPF_IPU_DBGCNT_UJ3_MC_NS);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_NA, CAUSE_UPF_IPU_DBGCNT_UJ3_NA);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_RS, CAUSE_UPF_IPU_DBGCNT_UJ3_RS);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_IPV6_RA, CAUSE_UPF_IPU_DBGCNT_UJ3_RA);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID, EXCP_ID_NEW_IPV6_OSPF, CAUSE_UPF_IPU_DBGCNT_UJ3_NEW_IPV6_OSPF);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID,
            EXCP_ID_IPV4_RESERVED_MC_OSPF, CAUSE_UPF_IPU_DBGCNT_UJ3_IPV4_RESERVED_MC_OSPF);
        UpfExceptStatOnCondEqual(g_cDesc.CauseID,
            EXCP_ID_IPV4_RESERVED_MC_OSPF_DR, CAUSE_UPF_IPU_DBGCNT_UJ3_IPV4_RESERVED_MC_OSPF_DR);
    }
}