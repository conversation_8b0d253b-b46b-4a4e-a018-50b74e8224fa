
/* 调用此函数的条件：1.已经判断scratch跟踪开关使能 */
/* 2.Q指针指向外层IPV6头*/
void iIpuUsrTrcOutIpV6Judge(uint8 IsInPae)
{
    uint8 rbIndex;
    /* 单个报文没有多次调用iIpuUsrTrcOutIpV6Judge的情况，*/
    /* 在解析报文前将解析结果初始化一下 */
    iIpuUsrTrcChkAlreadyMatch();
    UPF_ZERO_COMMON_32BIT_DATA(rsUpfIpPktL3Info)  // 调用UpfParseIpPkt前显式清空解析结果，避免使用时报告警
    UPF_ZERO_COMMON_128BIT_DATA(rsUpfIpPktL4Info) // 调用UpfParseIpPkt前显式清空解析结果，避免使用时报告警
    UpfParseIpPkt(IP_PARSER_TYPE_INNER_L3);
    iIpuUsrTrcChkParseRst();
    move rbIndex = 0;
    


UsrTrcMatch:
    cmp cc0 = rbIndex, USRTRC_RULE_NUM;
    if (cc0.lt)
    {
        READ_USERTRACE_RULE_TBL_FST_16B(rsIoAddr, rbIndex, rsUsrTrcRule16B);
        // IpV4Valid&IpV6Valid
        bitcmp cc0 = rsUsrTrcRule16B.LWInfo.LW0[31:2], TWO_BIT_MASK;
        /* 跟踪开关打开时对应至少有一个跟踪规则是有效的，有某个规则无效也是正常情况，不做计数 */
        /* 规则无效不需要做后面的IP比较匹配，继续判断匹配下一个规则 */
        if (cc0.z)
        {
            add rbIndex = rbIndex, 1;
            jmp UsrTrcMatch;
        }

        move rsUsrTrcRuleBitConf = rsUsrTrcRule16B.CommInfo.BitConf;
        move rbUsrTrcRulePrefixLen = rsUsrTrcRule16B.CommInfo.Ipv6PrefixLen;
        move rsUsrTrcRule16BCtrl.RuleIndex = rbIndex[:1];
        /* 外层是V6，需要判断规则配置V6地址，才去切换读V6 ip信息 */
        bitcmp cc1 = rsUsrTrcRule16B.CommInfo.BitConf.IpV6Valid, ONE_BIT_MASK;
        if (cc1.nz)
        {
            READ_USERTRACE_RULE_TBL_SND_16B(rsIoAddr, rbIndex, rsUsrTrcRule16B);
            move rsUsrTrcRule16BCtrl.IsV4V6Comm = FALSE;
        }
        else
        {
            move rsUsrTrcRule16BCtrl.IsV4V6Comm = TRUE;
        }

        iIpuUsrTrcOutIpV6Cmp(IsInPae);
        bitcmp cc1 = rsUpfDesc.UserTrcMatch, ONE_BIT_MASK;
        /* 报文已经匹配了某个跟踪规则，结束规则遍历直接返回 */
        if (cc1.nz)
        {
            move rsUpfDesc.UserTraceRuleIndex = rbIndex[:1];
            return;
        }

        add rbIndex = rbIndex, 1;
        jmp UsrTrcMatch;
    }

    return;
}