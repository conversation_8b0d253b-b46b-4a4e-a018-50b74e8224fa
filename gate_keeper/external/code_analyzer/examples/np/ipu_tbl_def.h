#ifndef  __IPU_TBL_DEF_H__
#define  __IPU_TBL_DEF_H__

/* ****************************************************************************\
 * ***********              以下是IPU_DSCPMAP的数据定义              ***********
\* ****************************************************************************/
enum IPU_DSCPMAP_DSCPMAPTYPE_E
{
    IPU_DSCPMAP_DSCPMAPTYPE_DISABLE       =   0,
    IPU_DSCPMAP_DSCPMAPTYPE_8021P         =   1,
    IPU_DSCPMAP_DSCPMAPTYPE_EXP           =   2,
    IPU_DSCPMAP_DSCPMAPTYPE_8021P_EXP     =   3
};

/***************************** 表项/接口结构类型开始 **************************/
struct IPU_DSCPMAP_S                    /* 定义IPU_DSCPMAP结构类型 */
{
    uint2          DscpMapType;                     /* 详见ENUM. */
    uint3          Exp;
    uint3          VlanPri;
    uint24         Rsvd0;
    uint32         Rsvd1;
};

/******************************** 结构类型定义完毕 ****************************/

/*****************************************************************************\
 ************               以下是BD_BITMAP的数据定义               ***********
\*****************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct BD_BITMAP_S                      /*定义BD_BITMAP结构类型*/
{
    uint1          Valid1;                          /*BdId=31*/
    uint30         Valid2;                          /*每1bit代表一个BdId的状态；*/
                                                    /*0：unlock*/
                                                    /*1：lock*/
    uint1          Valid3;                          /*BdId=0*/
    uint1          Rsvd0;                           /*保留*/
    uint30         Rsvd1;                           /*保留*/
    uint1          Rsvd2;                           /*保留*/
};

/******************************** 结构类型定义完毕 ****************************/

/* ****************************************************************************\
 * ***********           以下是IPU_PORT_MAC_DATA的数据定义           ***********
\* ****************************************************************************/

/***************************** 表项/接口结构类型开始 **************************/
struct IPU_PORT_MAC_DATA_S              /* 定义IPU_PORT_MAC_DATA结构类型 */
{
    uint1          Valid;                           /* 有效位标志 */
    uint15         Rsvd0;                           /* 保留字段 */
    PORTINFO_S     PortInfo;                        /* 端口信息 */
    uint32         Rsvd1;                           /* 保留字段 */
    uint32         Rsvd2;                           /* 保留字段 */
    uint32         Rsvd3;                           /* 保留字段 */
};

/******************************** 结构类型定义完毕 ****************************/

#endif  /* __IPU_TBL_DEF_H__ */