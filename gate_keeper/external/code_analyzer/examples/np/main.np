
// 真实的NP项目示例

struct Point {
    int x;
    int y;
};

struct Rectangle {
    Point top_left;
    Point bottom_right;
    int area;
};

static void func1(int param1, char* param2) {
    printf("hello");
    func2();
    Point p = {10, 20};
    print_point(p);
    
    if (param1 > 0) {
        Rectangle rect = {{0, 0}, {param1, param1}};
        calculate_area(&rect);
        print_rectangle(rect);
    }
}

static void func2() {
    int x = 10;
    func3(x);
    if (x > 5) {
        printf("x is greater than 5");
        for (int i = 0; i < x; i++) {
            printf("iteration %d", i);
        }
    }
}

static int func3(int value) {
    return value * 2;
}

void print_point(struct Point p) {
    printf("x: %d, y: %d\n", p.x, p.y);
}

void calculate_area(struct Rectangle* rect) {
    int width = rect->bottom_right.x - rect->top_left.x;
    int height = rect->bottom_right.y - rect->top_left.y;
    rect->area = width * height;
}

void print_rectangle(struct Rectangle rect) {
    printf("Rectangle area: %d\n", rect.area);
}

int main() {
    func1(1, "test");
    Point point = {30, 40};
    print_point(point);
    return 0;
}
