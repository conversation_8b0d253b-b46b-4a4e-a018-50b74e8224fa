#include "ipu_priv.h"
#include "desc_egress_instance.h"
#include "desc_common_instance.h"
#include "desc_egress.h"
#include "upf_causeid_ip.h"
#include "ipu_intf.h"
#include "type_def_str.h"
#include "upf_common_func.h"
#include "lb_res_def.h"
#include "lb_pkthdr.h"
#include "upf_common_np2cpu.h"
#include "upf_common_cpu2np.h"
#include "outintf.h"
#include "neon_comm.h"
#include "lb_intf.h"
#include "pae_intf.h"
#include "upf_common_intf.h"
#include "ipu_headr.h"
#include "upf_common_util.h"
#include "upf_common_resdef.h"
#include "lb_intf.h"
#include "except_util.h"
#include "frame_hdr_ngsf.h"


/*IPU模块全局变量*/
(RE_S)                      g_rsTblData_1           g_rsRe;
(COMMON_128BIT_DATA_S)      g_rsTblData_1           rsSrcLoc;
(COMMON_128BIT_DATA_S)      g_rsTblData_4           rsDstLoc;
(NHP_5883_S)                g_rsTblData_3           g_rsNhp;
IPU_ENCAP_INFO              rsEncapInfo;
(COMMON_48BIT_DATA_S)       rsEncapInfo             rsEncapInfoLW;

void IpuIPV4ING_ModifyTtl()
{
    uint8    rbTtlDec;
    uint16   rhOldValue;
    (IPV4_HEADER_S)  q0b            psIPv4Hdr;

    move rwFuncRtnData.Data1 = 0; //使用前清0
    movezr rbTtlDec = g_iDesc.NoTtlDec;
    cmp cc0 = psIPv4Hdr.TtlProtWord.Ttl, IPU_TTL_LIMIT_MIN;
    if (cc0.le) {
        move rwFuncRtnData.Data1 = FAILED;
        return;
    }

    move g_iDesc.NoTtlDec = 0;
    move rhOldValue = q8h;
    sub psIPv4Hdr.TtlProtWord.Ttl = psIPv4Hdr.TtlProtWord.Ttl, rbTtlDec;   //TTL - 1
    //重新计算Checksum

    CalcPartialChecksum(psIPv4Hdr.Checksum, q8h, rhOldValue);
    //设置DirtyLength
    SET_L3_DIRTY_LEN(IPV4_HDR_DIRTY_LEN)
    return;
}

void IpuIPV6ING_ModifyTtl()
{
    uint8    rbTtlDec;
    (IPV6_HEADER_S)   q0b    psIPv6Hdr;

    move rwFuncRtnData.Data1 = 0; //使用前清0
    movezr rbTtlDec = g_iDesc.NoTtlDec;
    cmp cc0 = psIPv6Hdr.HopLimit, IPU_TTL_LIMIT_MIN;
    if (cc0.le) {
        move rwFuncRtnData.Data1 = FAILED;
        return;
    }
    sub psIPv6Hdr.HopLimit = psIPv6Hdr.HopLimit, rbTtlDec;   //TTL - 1
    move g_iDesc.NoTtlDec = 0;

    SET_L3_DIRTY_LEN(IPV6_HDR_DIRTY_LEN)

    return;
}

void UpfReadCurNhp(uint24 nhpIndex, uint8 offset)
{
    READ_NHP_TBL_PTG(g_rsNhp, nhpIndex, offset);
}

/*
* 依据输入获取wlr路由可用的nhp信息(单下一跳和多下一跳处理流程归一)
* 说明：此函数没有完全通用化，使用了g_rsNhp全局变量，因为后续流程使用了该值，暂时保持现状，后续再决定是否做优化；
*/
void IpuGetWlrNhp(uint8 rbNhpNum, uint32 rwNhpIndex1, uint8 rbHashNum)
{
    uint8 rbTmpCnt;
    uint8 rbTmpIndex;
    uint32 rwCheckData;

    move rwFuncRtnData.Data0 = E_GETWLR_NHP_OK;

    cmp cc0 = rbNhpNum, 1; //超过1个下一跳，循环查找可用的TB，如果遍历到最后一个TB，则不需要判断状态，直接发送
    if (cc0.gt) {
        move rbTmpIndex = rbHashNum;
    } else {
        move rbTmpIndex = 0;
    }

    move rbTmpCnt = 0;
LOOP_GET_WLRTB:
    cmp cc0 = rbTmpCnt, rbNhpNum;
    if (cc0.lt) {
        sll rwNhpIndex1 = rwNhpIndex1, 1;
        UpfReadCurNhp(rwNhpIndex1[:24], rbTmpIndex); //此处只能调用该函数，后续流程使用了g_rsNhp的值
        bitcmp cc0 = g_rsNhp.V, ONE_BIT_MASK;
        if (cc0.z) {
            move rwFuncRtnData.Data0 = E_GETWLR_NHP_INVALID;
            return;
        }
        mskcmp cc0 = g_rsNhp.OpCode, NHP_5883_OPCODE_WLR;
        if (cc0.ne) {
            move rwFuncRtnData.Data0 = E_GETWLR_NHP_NOT_WLR;
            return;
        }

        /*以前实现 1.获取本板LB的TBTP，同板转发, 无需判断PST表中的TB状态
                2.读PST表获取TB状态，当前使用NPH表中的状态
          当前实现：先查TBLINK表，确认有效再看LinkState
          原因：和yangcheng 00574409确认，TBLINK可信度较高，LinkState不一定可信但下表触发流程与TBLINK的不同，所以也需要判断
        */
        READ_LB_TBLINK_BITMAP(rwCheckData, g_rsNhp.Wlr.TB);
        bitcmp cc0 = rwCheckData[31:1], ONE_BIT_MASK;
        if (cc0.nz) {
            bitcmp cc1 = g_rsNhp.Wlr.LinkState, ONE_BIT_MASK;
            if (cc1.nz) {
                move rwFuncRtnData.Data0 = E_GETWLR_NHP_OK;
                move rwFuncRtnData.Data1 = g_rsNhp.Wlr.TB;
                return;
            }
        }

        add rbTmpIndex = rbTmpIndex, 1;
        cmp cc0 = rbTmpIndex, rbNhpNum;
        if (cc0.ge) {
            move rbTmpIndex = 0;
        }
        add rbTmpCnt = rbTmpCnt, 1;
        UpfExceptStatNoDiscard(CAUSE_UPF_IPNLS_ERR_TB_STATE_DOWN);    /* 存在PST Down的情况 */
        jmp LOOP_GET_WLRTB;
    }

    move rwFuncRtnData.Data0 = E_GETWLR_NHP_NOT_FIND;

    return;
}

/*IP发送给软LB/硬LB都调用该接口*/
IpuTx2LbPro():
{
    /* 上行转下行，如果配置ACL，会共用R寄存器信息rsiDesc.NsPolicy和g_eDesc.Frag，此处清空；
     * 目前IP跟踪出口报文会进行环回处理，无影响；
     * 用户跟踪出口报文不环回处理，如果外联口分片会进入PAE流程走报文分片处理，因此暂时在各UPF流程中进行单点清空处理
     */
    //此处Q指针指向ip头，设置P指针指向Q指针，删除eth头
    SET_P_EQ_Q();
    //re表和lb全局变量都在r160b，此处将re表在r208b备份，lb_re中使用
    move rsDstLoc.LW0 = rsSrcLoc.LW0;
    move rsDstLoc.LW1 = rsSrcLoc.LW1;
    move rsDstLoc.LW2 = rsSrcLoc.LW2;
    move rsDstLoc.LW3 = rsSrcLoc.LW3;

    move g_eDesc.Frag = 0; //TCE TODO是否需要

    UpfDbgStatNoDiscard(CAUSE_UPF_IPNLS_DBGCNT_WLR_PKT);
    move rsEncapInfoLW.LW0 = 0;
    move rsEncapInfoLW.HW1 = 0;
    move rsEncapInfo.NxtHdr = IP2MPF_EXTEND_HDR_TYPE_PLAYLOAD;

    bitcmp cc3 = g_cDesc.AppPkt, ONE_BIT_MASK; //ip全局寄存器待分配
    if (cc3.nz) {
        move rsEncapInfo.UeFlag = g_cDesc.UeFlag;

        bitcmp cc4 = g_stMpf2IpHdrInfo.bBypassFlag, ONE_BIT_MASK;
        if (cc4.nz) {
            move rsEncapInfo.bTransDataFlg = g_stMpf2IpHdrInfo.bBypassFlag;
            move rsEncapInfo.ByPassApnIdx = g_stMpf2IpHdrInfo.ByPassInfo.APNIndex;
            move rsEncapInfo.ByPassExtInfo = g_stMpf2IpHdrInfo.ByPassInfo.ByPassExtInfo;
        }
        move rsEncapInfo.ReassFlag = g_stMpf2IpHdrInfo.bReassemFlag;
    }

    next IpuTx2LbWlrNhp;
}

/*
* 选择一个可用的LB下一跳
* */
IpuTx2LbWlrNhp():
{
    uint32 rwNhpIndex1;
    uint8 rbNhpNum;
    uint8 rbHashNum;
    uint16 rhTemp;
    uint32 rwPktTheoryLen;
    uint8 rbL3Stake;
    (IPV4_HEADER_S)  q0b  psIpv4Hdr;
    (IPV6_HEADER_S)  q0b  psIpv6Hdr;
    uint16 rsResult;

    movezr rbNhpNum    = g_rsRe.Wlr.NhpNum;
    movezr rwNhpIndex1 = g_rsRe.Wlr.NhpIndex1;
    move rwFuncRtnData = 0;
    move rbHashNum = 0;

    cmp cc0 = rbNhpNum, PAE_ADDR_NUM;
    cmp cc1 = rbNhpNum, 0;
    cmp cc2 = rbNhpNum, 1;
    if (cc0.gt) {
        movezr rbNhpNum = PAE_ADDR_NUM;
    }

    if (cc1.eq) {
        jmp UpfExceptDiscard(CAUSE_UPF_LB_DROP_NO_NEXTHOP);
    }

    if (cc2.ne) {
        //有多个下一跳时，计算hashNum
        bitcmp cc0 = g_rsUpfDesc.UpfHashKeyValid, ONE_BIT_MASK;
        if (cc0.z) {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_PAEHASHKEY_INVALID2);
        }

        UpfHash2SpecifiedBits(g_rsUpfDesc.UpfHashKey, 8);
        move rsResult = 0;
        Mod( rwFuncRtnData.Data0, rbNhpNum, zero[:8]);
        move rbHashNum = g_rhFuncRtnCode[:8];
    }

    IpuGetWlrNhp(rbNhpNum, rwNhpIndex1, rbHashNum);
    cmp cc1 = rwFuncRtnData.Data0, E_GETWLR_NHP_INVALID;
    if (cc1.eq) {
        cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV4;
        if (cc0.eq) {
            jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_NHP_TBL_INVALID);
        } else {
            jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_IPV6_NHP_TBL_INVALID);
        }
    }

    cmp cc1 = rwFuncRtnData.Data0, E_GETWLR_NHP_NOT_WLR;
    if (cc1.eq) {
        cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV4;
        if (cc0.eq) {
            jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_NHP_NO_WLR);
        } else {
            jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_IPV6_NHP_NO_WLR);
        }
    }

    cmp cc1 = rwFuncRtnData.Data0, E_GETWLR_NHP_NOT_FIND;
    if (cc1.eq) {
        jmp UpfExceptDiscard(CAUSE_UPF_IPNLS_DROP_NHP_TB_STATE_ERR);
    }

    //padding判断
    cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV4;
    if (cc0.eq) {
        GET_L3_STAKE(rbL3Stake);
        add rwPktTheoryLen = psIpv4Hdr.TotalLength, rbL3Stake;
        cmp cc0 = rwPktTheoryLen, g_cDesc.RealLength;
        if (cc0.lt) {
            //move g_cDesc.AfterTxWord.UseRealLength = TRUE;
            move g_cDesc.RealLength = rwPktTheoryLen[:16];
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_IPV4_PADDING);
        }
    }

    cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV4;
    if (cc0.eq) {
        IpuIPV4ING_ModifyTtl();
        cmp cc0 = rwFuncRtnData.Data1, FAILED;
        if (cc0.eq) {
            jmp UpfExceptDiscardWithDfxInfo(CAUSE_UPF_IPNLS_DROP_PKT_IP_TTL_LESS_ONE, psIpv4Hdr.DIP, psIpv4Hdr.SIP,
                psIpv4Hdr.TtlProtWord.Protocol, zero);
        }
    }
    cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV6;
    if (cc0.eq) {
        IpuIPV6ING_ModifyTtl();
        cmp cc0 = rwFuncRtnData.Data1, FAILED;
        if (cc0.eq) {
            jmp UpfExceptDiscardWithDfxInfo(CAUSE_UPF_IPNLS_DROP_PKT_IP_TTL_LESS_ONE, psIpv6Hdr.DIP.Part0,
                psIpv6Hdr.DIP.Part1, psIpv6Hdr.DIP.Part2, psIpv6Hdr.DIP.Part3);
        }
    }

    //判断目标TB是否是本NP TB，如果是本NP TB说明是LB卸载，且本板处理
    movezr rhTemp = g_ssScratch5.UpfLocalNpPaeTb;
    cmp cc0 = g_rsNhp.Wlr.TB, rhTemp;
    if (cc0.eq) {
        jmp LbReProc;
    }

    UpfDbgStatNoDiscard(CAUSE_UPF_IPNLS_DBGCNT_GET_TB_SUCC);

    //SET_PWO_IS_INVALID_LEN()
    next IpuTxLbEncapAttriBute;
}

IpuTxLbEncapAttriBute():
{
    (S_VFP_MPF2IP_EXTEND_HDR_ATTRIBUTE) p0b rsAttirButeHdr;
    (S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI) p0b rsAttirButeHdrSubWLR;
    (S_NLS_MPF2APP_SUB_EXTHDR_GRE_ATTRI) p0b rsAttirButeHdrSubGRE;
    uint8 rbExtAttribLen;

    bitcmp cc0 = g_rsRe.WlrExt.PortMaskValid , ONE_BIT_MASK;
    if (cc0.nz) {
        //vNAT引流报文需要封装wlr路由属性扩展头，携带portmask；vNAT引流流程与静态地址路由冗余流程场景上不会同时存在；
        /*
        IP2MPF属性扩展头（IP2MPF_EXTEND_HDR_TYPE_FWD）中，包括2个子属性，其中路由子属性（SUBTLV_WLR_ATTRIBUTE）
        即通常说的透明数据，其data部分包含3个uint32: uiAttribute、uiAttribute1、uiAttribute2
        其中uiAttribute的高8bit为透明数据的类型，
        值为1标识透明数据类型为vNAT portmask， uiAttribute的低16bit为portmask value
        */
        UpfDbgStatNoDiscard(CAUSE_UPF_IPU_DBGCNT_VNAT_EXTEND_HDR);

        /* 路由属性子扩展 */
        L2_HDR_INC(SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI));
        movezr  rbExtAttribLen = SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI);
        move rsAttirButeHdrSubWLR.Type     = SUBTLV_WLR_ATTRIBUTE;
        move rsAttirButeHdrSubWLR.Length   = SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI);
        move rsAttirButeHdrSubWLR.Resv2    = 0;
        movezr rsAttirButeHdrSubWLR.Attribute[:16] = g_rsRe.WlrExt.PortMask;
        move rsAttirButeHdrSubWLR.Attribute[31:8] = E_METADATA_SUBTYPE_PORT_MASK; //第一个字节标识透明数据的类型;
        move rsAttirButeHdrSubWLR.Attribute1    = 0;
        move rsAttirButeHdrSubWLR.Attribute2    = 0;
        /* 属性拓展头 */
        L2_HDR_INC(SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_ATTRIBUTE));
        add  rbExtAttribLen = rbExtAttribLen, SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_ATTRIBUTE);
        move  rsEncapInfo.EnCapHdrLen = rbExtAttribLen;
        move rsAttirButeHdr.NextHdrType  = rsEncapInfo.NxtHdr;
        move rsAttirButeHdr.Length   = rbExtAttribLen;
        move rsAttirButeHdr.Rsvd     = 0;
        move rsEncapInfo.NxtHdr = IP2MPF_EXTEND_HDR_TYPE_FWD;
        jmp IpuTxLbEncapTransData;
    }

    move rbExtAttribLen = 0;
    bitcmp cc0 = g_rsRe.Wlr.RouteAttrib, ONE_BIT_MASK;
    if (cc0.nz) {
        UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_SND_WLR_ATTR);
        bitcmp cc1 = g_cDesc.GreRedundancy, ONE_BIT_MASK;
        if (cc1.nz) {
            UpfExceptStatNoDiscard(CAUSE_UPF_IPU_NORMAL_SND_WLR_GRE_ATTR);
            /* 隧道属性子扩展 */
            L2_HDR_INC(SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_GRE_ATTRI));
            add  rbExtAttribLen = rbExtAttribLen, SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_GRE_ATTRI);
            move rsAttirButeHdrSubGRE.Type  = SUBTLV_GRE_ATTRIBUTE;
            move rsAttirButeHdrSubGRE.Length   = SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_GRE_ATTRI);
            move rsAttirButeHdrSubGRE.Resv2    = 0;
            move rsAttirButeHdrSubGRE.GreAttri     = 1;   /* GRE备份标识使能 */
            move rsAttirButeHdrSubGRE.udwResAttri  = 0;
        }
        /* 路由属性子扩展 */
        L2_HDR_INC(SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI));
        add  rbExtAttribLen = rbExtAttribLen, SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI);
        move rsAttirButeHdrSubWLR.Type     = SUBTLV_WLR_ATTRIBUTE;
        move rsAttirButeHdrSubWLR.Length   = SIZEOF_EX(S_NLS_MPF2APP_SUB_EXTHDR_RT_ATTRI);
        move rsAttirButeHdrSubWLR.Resv2    = 0;
        movezr rsAttirButeHdrSubWLR.Attribute[:1]     = g_rsRe.Wlr.RouteAttrib;
        move rsAttirButeHdrSubWLR.Attribute1    = 0;
        move rsAttirButeHdrSubWLR.Attribute2    = 0;
        /* 属性拓展头 */
        L2_HDR_INC(SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_ATTRIBUTE));
        add  rbExtAttribLen = rbExtAttribLen, SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_ATTRIBUTE);
        move  rsEncapInfo.EnCapHdrLen = rbExtAttribLen;
        move rsAttirButeHdr.NextHdrType  = rsEncapInfo.NxtHdr;
        move rsAttirButeHdr.Length   = rbExtAttribLen;
        move rsAttirButeHdr.Rsvd     = 0;
        move rsEncapInfo.NxtHdr = IP2MPF_EXTEND_HDR_TYPE_FWD;
    }
    next IpuTxLbEncapTransData;
}

IpuTxLbEncapTransData():
{
    (S_VFP_MPF2IP_EXTEND_HDR_TRANSPARENT) p0b rsTransDataHdr;

    bitcmp cc0 = g_cDesc.AppPkt, ONE_BIT_MASK;
    bitcmp cc1 = rsEncapInfo.UeFlag, ONE_BIT_MASK;
    bitcmp cc2 = rsEncapInfo.bTransDataFlg, ONE_BIT_MASK;
    mskcmp cc0 = ccr[:6], 0b101010;
    if (cc0.eq) {
        L2_HDR_INC(SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_TRANSPARENT));
        add  rsEncapInfo.EnCapHdrLen = rsEncapInfo.EnCapHdrLen, SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_TRANSPARENT);
        move rsTransDataHdr.NextHdrType = rsEncapInfo.NxtHdr;
        move rsTransDataHdr.Length = SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_TRANSPARENT);
        move rsTransDataHdr.Value = rsEncapInfo.ByPassApnIdx;
        move rsTransDataHdr.ByPassExtInfo = rsEncapInfo.ByPassExtInfo;
        move rsTransDataHdr.Rsvd = 0;
        move rsTransDataHdr.Rsvd2 = 0;
        move rsEncapInfo.NxtHdr = IP2MPF_EXTEND_HDR_TYPE_BYPASS;
    }
    next IpuTxLbEncapReIp;
}

IpuTxLbEncapReIp():
{
    (S_VFP_MPF2IP_EXTEND_HDR_REIPV4) p0b rsReIpv4Hdr;
    (S_VFP_MPF2IP_EXTEND_HDR_REIPV6) p0b rsReIpv6Hdr;

    cmp cc0 = g_rsNhp.NextHop, 0;
    if (cc0.ne) {
        cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV4;
        if (cc0.eq) {
            L2_HDR_INC(SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_REIPV4));
            add  rsEncapInfo.EnCapHdrLen  = rsEncapInfo.EnCapHdrLen, SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_REIPV4);
            move rsReIpv4Hdr.NextHdrType  = rsEncapInfo.NxtHdr;
            move rsReIpv4Hdr.NextHopIP    = g_rsNhp.NextHop;
            move rsReIpv4Hdr.Rsvd1        = 0;
            move rsReIpv4Hdr.Rsvd2        = 0;

            move rsEncapInfo.NxtHdr = IP2MPF_EXTEND_HDR_TYPE_FRAMED_RT_IPV4;
        }
        cmp cc0 = g_cDesc.L2PaRst.L3Type, L2PA_RST_L3TYPE_IPV6;
        if (cc0.eq) {
            L2_HDR_INC(SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_REIPV6));
            add  rsEncapInfo.EnCapHdrLen  = rsEncapInfo.EnCapHdrLen, SIZEOF_EX(S_VFP_MPF2IP_EXTEND_HDR_REIPV6);
            move rsReIpv6Hdr.NextHdrType  = rsEncapInfo.NxtHdr;
            move rsReIpv6Hdr.NextHopIP0   = g_rsNhp.NextHop;
            move rsReIpv6Hdr.NextHopIP1   = 0;
            move rsReIpv6Hdr.NextHopIP2   = 0;
            move rsReIpv6Hdr.NextHopIP3   = 0;
            move rsReIpv6Hdr.Rsvd1        = 0;
            move rsReIpv6Hdr.Rsvd2        = 0;

            move rsEncapInfo.NxtHdr = IP2MPF_EXTEND_HDR_TYPE_FRAMED_RT_IPV6;
        }
    }
    next IpuTxLbEncapCommHdr;
}

IpuTxLbEncapCommHdr():
{
    (S_VFP_IP2MPF_COMM_HDR) p0b rsCommHdr;
    uint8  rbTemp;

    L2_HDR_INC(SIZEOF_EX(S_VFP_IP2MPF_COMM_HDR));
    move rsCommHdr.AppHdrType   = PAE_APP_HEAD_TYPE_NLS_IP;
    // 继承IP的QOS
    UPF_SET_QINDEX_CONFIG_DEFAULT_BE(rbTemp, rsCommHdr.Qindex, g_cDesc.Dsic.Rqcolor.Remark,
                            g_cDesc.Dsic.Rqcolor.QindexColor.Qindex);
    move rsCommHdr.QosColor     = g_cDesc.Dsic.Rqcolor.QindexColor.Color;
    move rsCommHdr.QosEnable    = g_cDesc.Dsic.Rqcolor.Remark;
    move rsCommHdr.NextHdrType  = rsEncapInfo.NxtHdr;
    move rsCommHdr.IngEgFlag    = 0;
    move rsCommHdr.Rsvd0         = 0;

    bitcmp cc3 = g_cDesc.AppPkt, ONE_BIT_MASK;
    if (cc3.nz) {
        move rsCommHdr.IngEgFlag  = rsEncapInfo.UeFlag;
    }

    movezr rsCommHdr.VpnIndex[:16]     = g_rsRe.Wlr.VpnId;
    move rsCommHdr.ServiceId    = g_rsRe.Wlr.ServieId;
    move rsCommHdr.LbHashKey    = g_rsUpfDesc.UpfHashKey;

    /* Scratch5寄存器中LocalNpPaeTb为0时，表示仅IP卸载场景；
     * 因此只有当LB卸载场景进行LB转板时，认为转板报文是NP内部通信消息，才进行如下携带转板信息；
     * 具体操作为：共用将COMM头中的vpn字段，低16bit保持不变作为有效字段（因为vpn其实只有低16bit有效）,
     * 高16bit用于携带LB转板的信息（matchcnt、IP跟踪、用户跟踪）
     */
    //5897没有单ip卸载场景，不需要判断，直接封转板信息

    move rsCommHdr.LbBtobInfo.MatchCntFlag = g_rsUpfDesc.MatchCntFlag;
    move rsCommHdr.LbBtobInfo.Rsvd1 = 0;
    move rsCommHdr.LbBtobInfo.UpfTrace.UserTrcMatch = g_rsUpfDesc.UserTrcMatch;
    bitcmp cc0 = g_rsUpfDesc.UserTrcMatch, ONE_BIT_MASK;
    if (cc0.nz) {
        move rsCommHdr.LbBtobInfo.UpfTrace.UserTrcIpV4 = g_rsUpfDesc.UeIpMatchV4;
        move rsCommHdr.LbBtobInfo.UpfTrace.Rsvd1 = 0;
        move rsCommHdr.LbBtobInfo.UpfTrace.UserTraceRuleIndex = g_rsUpfDesc.UserTraceRuleIndex;
        move rsCommHdr.LbBtobInfo.UpfTrace.UserTrcDir = g_rsUpfDesc.UserTrcDir;
    } else {
        move g_rsUpfDesc.IsBtob = TRUE;
        move rsCommHdr.LbBtobInfo.UpfTrace.Rsvd0 = 0;
        //move rsCommHdr.LbBtobInfo.UpfTrace.IpTraceEn = g_cDesc.IpTraceEn;通用寄存器结构待确认
        //move rsCommHdr.LbBtobInfo.UpfTrace.IpTraceId = g_cDesc.IpTraceId;通用寄存器结构待确认
    }
    move rsCommHdr.LbBtobInfo.ReassFlag = rsEncapInfo.ReassFlag;

    add  rsEncapInfo.EnCapHdrLen  = rsEncapInfo.EnCapHdrLen, SIZEOF_EX(S_VFP_IP2MPF_COMM_HDR);
    //add  g_cDesc.RealLength = g_cDesc.RealLength, rsEncapInfo.EnCapHdrLen; L2_HDR_INC函数中已执行

    move rsInfo.DstTb = g_rsNhp.Wlr.TB;
    move rsInfo.DstTp = g_rsNhp.Wlr.TP;
    // IP发给其他NLS默认使用最低优先级
    PAE_INFO_QOS_CONFIG_DEFAULT_BE(g_cDesc.Dsic.Rqcolor.Remark, g_cDesc.Dsic.Rqcolor.QindexColor.Qindex);
    move rsInfo.IsLogicTb = FALSE;
    move rsInfo.IsToIpuc  = FALSE;
    move rsInfo.LbHashKey = rsCommHdr.LbHashKey;
    move rsInfo.bcPlaneid = PAE_PLANEID_AUTOSEL;

    //SR_Q_EX(rsEncapInfo.EnCapHdrLen); 5897不需要了

    UpfExceptStatNoDiscard(CAUSE_UPF_IPNLS_NORMAL_SEND_WLR_LB_PKT);
    jmp Pae_HeadEncap;
}