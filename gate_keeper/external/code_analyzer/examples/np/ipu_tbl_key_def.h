#ifndef  __IPU_TBL_KEY_DEF_H__
#define  __IPU_TBL_KEY_DEF_H__
/*****************************************************************************/


/* ****************************************************************************\
 * ***********            以下是IPU_PORT_MAC_KEY的数据定义           ***********
\* ****************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct IPU_PORT_MAC_KEY_S               /* 定义IPU_PORT_MAC_KEY结构类型 */
{
    uint6          SubTid;                          /* SubTid */
    uint2          Rsvd0;                           /* 保持等于0 */
    uint16         DMacH16;                         /* 目的MAC高16bit */
    uint32         DMacL32;                         /* 目的MAC低32bit */
    uint32         Rsvd1;                           /* 保持等于0 */
    uint24         Rsvd2;                           /* 保持等于0 */
};

/******************************** 结构类型定义完毕 ****************************/
/******************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct IPTRC_KEY_S                      /* 定义IPTRC_KEY结构类型 */
{
    uint4          Tid;                             /* Tid */
    uint4          SubTid;                          /* SubTid */
    uint8          L4Protol;                        /* 四层协议类型，如ARP、TCP、UDP等， */
                                                    /* 如果跟踪任务协议类型选为IP时，本字段无效， */
                                                    /* 如果协议类型为ND时，本字段对应为ICMPv6(58)，跟踪任务协议类型。 */
                                                    /* tcp：0x6tcp报文； */
                                                    /* udp，0x17udp报文； */
                                                    /* bgp，58,bgp报文； */
                                                    /* nd，nd报文； */
                                                    /* ospf89ospf报文； */
    uint16         L3Protol;                        /* 协议类型。 */
                                                    /* ip，IPv4:0x0800； */
                                                    /* ipv6:0x86ddip报文； */
                                                    /* arp，0x0806arp报文； */
    uint32         LocalIPPart0;                    /* 本地IP地址，IP地址第1部分（高位） */
    uint32         LocalIPPart1;                    /* 本地IP地址，IP地址第2部分 */
    uint32         LocalIPPart2;                    /* 本地IP地址，IP地址第3部分 */
    uint32         LocalIPPart3;                    /* 本地IP地址，IP地址第4部分（低位） */
    uint32         PeerIPPart0;                     /* 远端IP地址，IP地址第1部分（高位） */
    uint32         PeerIPPart1;                     /* 远端IP地址，IP地址第2部分 */
    uint32         PeerIPPart2;                     /* 远端IP地址，IP地址第3部分 */
    uint32         PeerIPPart3;                     /* 远端IP地址，IP地址第4部分（低位） */
    uint16         SrcPort;                         /* 源端口 */
    uint16         DstPort;                         /* 目的端口 */
    uint4          Tid1;                            /* Tid */
    uint4          SubTid1;                         /* SubTid */
    uint16         VpnId;                           /* VpnId */
    uint4          Direction;                       /* 报文方向, 具体详见ENUM. */
    uint4          Rsvd0;                           /* 保持等于0 */
    uint32         Rsvd1;                           /* 保持等于0 */
    uint32         Rsvd2;                           /* 保持等于0 */
    uint32         Rsvd3;                           /* 保持等于0 */
    uint32         Rsvd4;                           /* 保持等于0 */
    uint32         Rsvd5;                           /* 保持等于0 */
    uint32         Rsvd6;                           /* 保持等于0 */
    uint32         Rsvd7;                           /* 保持等于0 */
    uint32         Rsvd8;                           /* 保持等于0 */
    uint32         Rsvd9;                           /* 保持等于0 */
};

struct IPTRC_KEY1_S
{
    uint4          Tid;                             /* Tid */
    uint4          SubTid;                          /* SubTid */
    uint8          L4Protol;                        /* 四层协议类型，如ARP、TCP、UDP等， */
                                                    /* 如果跟踪任务协议类型选为IP时，本字段无效， */
                                                    /* 如果协议类型为ND时，本字段对应为ICMPv6(58)，跟踪任务协议类型。 */
                                                    /* tcp：0x6tcp报文； */
                                                    /* udp，0x17udp报文； */
                                                    /* bgp，58,bgp报文； */
                                                    /* nd，nd报文； */
                                                    /* ospf89ospf报文； */
    uint16         L3Protol;                        /* 协议类型。 */
                                                    /* ip，IPv4:0x0800； */
                                                    /* ipv6:0x86ddip报文； */
                                                    /* arp，0x0806arp报文； */
    uint32         LocalIPPart0;                    /* 本地IP地址，IP地址第1部分（高位） */
    uint32         LocalIPPart1;                    /* 本地IP地址，IP地址第2部分 */
    uint32         LocalIPPart2;                    /* 本地IP地址，IP地址第3部分 */
    uint32         LocalIPPart3;                    /* 本地IP地址，IP地址第4部分（低位） */
    uint32         PeerIPPart0;                     /* 远端IP地址，IP地址第1部分（高位） */
};

struct IPTRC_KEY2_S
{
    uint32         PeerIPPart1;                     /* 远端IP地址，IP地址第2部分 */
    uint32         PeerIPPart2;                     /* 远端IP地址，IP地址第3部分 */
    uint32         PeerIPPart3;                     /* 远端IP地址，IP地址第4部分（低位） */
    uint16         SrcPort;                         /* 源端口 */
    uint16         DstPort;                         /* 目的端口 */
};

struct IPTRC_KEY3_S
{
    uint4          Tid1;                            /* Tid */
    uint4          SubTid1;                         /* SubTid */
    uint16         VpnId;                           /* VpnId */
    uint4          Direction;                       /* 报文方向, 具体详见ENUM. */
    uint4          Rsvd0;                           /* 保持等于0 */
    uint32         Rsvd1;                           /* 保持等于0 */
    uint32         Rsvd2;                           /* 保持等于0 */
    uint32         Rsvd3;                           /* 保持等于0 */
    uint32         Rsvd4;                           /* 保持等于0 */
    uint32         Rsvd5;                           /* 保持等于0 */
};

struct IPTRC_KEY4_S
{
    uint32         Rsvd6;                           /* 保持等于0 */
    uint32         Rsvd7;                           /* 保持等于0 */
    uint32         Rsvd8;                           /* 保持等于0 */
    uint32         Rsvd9;                           /* 保持等于0 */
};

/******************************** 结构类型定义完毕 ****************************/


/* ****************************************************************************\
 * ***********          以下是IPSEC_FLEX_ACLV4_KEY的数据定义         ***********
\* ****************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct IPSEC_FLEX_ACLV4_KEY_S           /* 定义IPSEC_FLEX_ACLV4_KEY结构类型 */
{
    uint4          Tid;                             /* Tid */
    uint4          SubTid;                          /* SubTid */
    uint8          Rsvd0;                           /* 保持等于0 */
    uint16         VrfId;                           /* VPNID */
    uint32         DstIP;                           /* 目的IP */
    uint32         SrcIp;                           /* 源IP */
    uint32         Rsvd1;                           /* 保持等于0 */
    uint32         PolicyGroupIndex;                /* uiPolicyGroupIndex */
};

/******************************** 结构类型定义完毕 ****************************/
/******************************************************************************/


/* ****************************************************************************\
 * ***********          以下是IPSEC_FLEX_ACLV6_KEY的数据定义         ***********
\* ****************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct IPSEC_FLEX_ACLV6_KEY_S           /* 定义IPSEC_FLEX_ACLV6_KEY结构类型 */
{
    uint4          Tid;                             /* Tid */
    uint4          SubTid;                          /* SubTid */
    uint8          Rsvd0;                           /* 保持等于0 */
    uint16         VrfId;                           /* VPNID */
    uint32         DstIpPart0;                      /* 目的IPv6Part0 */
    uint32         DstIpPart1;                      /* 目的IPv6Part1 */
    uint32         DstIpPart2;                      /* 目的IPv6Part2 */
    uint32         DstIpPart3;                      /* 目的IPv6Part3 */
    uint32         SrcIpPart0;                      /* 源IPv6Part0 */
    uint32         SrcIpPart1;                      /* 源IPv6Part1 */
    uint32         SrcIpPart2;                      /* 源IPv6Part2 */
    uint32         SrcIpPart3;                      /* 源IPv6Part3 */
    uint32         PolicyGroupIndex;                /* uiPolicyGroupIndex */
};

struct IPSEC_FLEX_ACLV6_KEY1_S
{
    uint4          Tid;                             /* Tid */
    uint4          SubTid;                          /* SubTid */
    uint8          Rsvd0;                           /* 保持等于0 */
    uint16         VrfId;                           /* VPNID */
    uint32         DstIpPart0;                      /* 目的IPv6Part0 */
    uint32         DstIpPart1;                      /* 目的IPv6Part1 */
    uint32         DstIpPart2;                      /* 目的IPv6Part2 */
    uint32         DstIpPart3;                      /* 目的IPv6Part3 */
    uint32         SrcIpPart0;                      /* 源IPv6Part0 */
};

struct IPSEC_FLEX_ACLV6_KEY2_S
{
    uint32         SrcIpPart1;                      /* 源IPv6Part1 */
    uint32         SrcIpPart2;                      /* 源IPv6Part2 */
    uint32         SrcIpPart3;                      /* 源IPv6Part3 */
    uint32         PolicyGroupIndex;                /* uiPolicyGroupIndex */
};

/******************************** 结构类型定义完毕 ****************************/
/******************************************************************************/


/* ****************************************************************************\
 * ***********         以下是IPSEC_FLEX_LOCALIP_KEY的数据定义        ***********
\* ****************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct IPSEC_FLEX_LOCALIP_KEY_S         /* 定义IPSEC_FLEX_LOCALIP_KEY结构类型 */
{
    uint4          SubTid;                          /* SubTid */
    uint4          Rsvd0;                           /* 保持等于0 */
    uint24         Rsvd1;                           /* 保持等于0 */
    uint16         Rsvd2;                           /* 保持等于0 */
    uint16         VrfId;                           /* VPNID */
    uint32         SrcIp;                           /* 源IP */
    uint32         DstIP;                           /* 目的IP */
};

/******************************** 结构类型定义完毕 ****************************/
/******************************************************************************/


/* ****************************************************************************\
 * ***********         以下是IPSEC_FLEX_TNLINFO_KEY的数据定义        ***********
\* ****************************************************************************/


/***************************** 表项/接口结构类型开始 **************************/
struct IPSEC_FLEX_TNLINFO_KEY_S         /* 定义IPSEC_FLEX_TNLINFO_KEY结构类型 */
{
    uint4          SubTid;                          /* SubTid */
    uint4          Rsvd0;                           /* 保持等于0 */
    uint8          Rsvd1;                           /* 保持等于0 */
    uint32         LmtIdx;                          /* ipsec隧道口索引 */
    uint16         Rsvd2;                           /* 保持等于0 */
};

/******************************** 结构类型定义完毕 ****************************/
/******************************************************************************/


#endif  /* __NPCPI_TABLE_IPU_TBL_KEY_DEF_H__ */