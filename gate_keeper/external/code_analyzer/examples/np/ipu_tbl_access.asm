#include "type_def.h"
#include "table_access.h"
#include "upf_common_symbol.h"
#include "ipu_tbl_key_def.h"

/*
 * 5887 IO代码规范
 * 1. 所有IO代码使用inline实现，名称全部大写；
 * 2. inline命名使用命令作为前缀，READ_/WRITE_/SEARCH_/READMOD_/POP_/PUSH_/
 *    READCLR_/HCAR_/POLICE_/ADD_/SUB_等，对于统计表，可以是用STAT_作为前缀；
 * 3. inline命名由 {命令前缀}_{表名}组成，如果是Table Group方式，增加_TG作为后缀；
 * 4. inline参数中的返回结果建议使用通用格式并作为第一个参数；
 * 5. 非线性表的查找Key定义和对外零层接口保持一致；
 * 6. 绑定key buffer的变量，命名规范同GPR，但需要使用k作为前缀，比如ksLmemReadReq；
 * 7. 对于线性表，不涉及用户自定义部分，使用在头文件里统一声明的变量，
 *    比如: ksGltReadReq, ksLmemReadReq, ksLmemWriteReq, ksLmemAddReq等等
 */

// 保持和SEARCH_NDH一致
inline SEARCH_NDH_DIP(COMMON_128BIT_DATA_S RESP_DATA, uint16 VlanId, uint16 PortInfo, IPADDR_V6_S rsDipv6)
{
    (NDH_KEY_S)     k2b     ksNdhKey;

    move ksSeEmReq = GET_SE_PROFILE(ARPV6_ILA_TID);
    move ksNdhKey.Tid = 8;
    move ksNdhKey.Ovid = VlanId[:12];
    move ksNdhKey.OportInfo = PortInfo;
    move ksNdhKey.NhipPart1 = rsDipv6.Part0;
    move ksNdhKey.NhipPart2 = rsDipv6.Part1;
    move ksNdhKey.NhipPart3 = rsDipv6.Part2;
    move ksNdhKey.NhipPart4 = rsDipv6.Part3;

    SE_EM_SEARCH_RSP_128BIT(RESP_DATA, ksSeEmReq, ksNdhKey);
}

// 保持和READ_RE一致
inline READ_IPV6_REDIRCT_DIP(RE_S RespData, uint24 ReIndex)
{
    move ksLmemReadReq.Profile = GET_MEM_PROFILE(LNS_SESSION_TBL_BASE);
    movezr ksLmemReadReq.TableIndex = ReIndex;
    LMEM_READ_128BIT(RespData, GET_MEM_COP_ID(LNS_SESSION_TBL_BASE), ksLmemReadReq);
}

inline READ_DSCPMAP_TBL(PHB_S RESP_DATA, uint8 dscp)
{
    move ksLmemReadReq.Profile = GET_MEM_PROFILE(IPU_DSCPMAP_TBL_BASE);
    add ksLmemReadReq.TableIndex[:16] = dscp, GET_MEM_SUB_OFFSET(IPU_DSCPMAP_TBL_BASE);
    LMEM_READ_64BIT(RESP_DATA, GET_MEM_COP_ID(IPU_DSCPMAP_TBL_BASE), ksLmemReadReq);
}


/***********************************
       IPU IPSEC_FLEX_ACL哈希表项
***********************************/
inline SEARCH_IPSEC_FLEX_ACLV4(uint32 RESP_DATA, uint16 VrfId, uint32 DstIP, uint32 SrcIp, uint32 PolicyGroupIndex)
{
    (IPSEC_FLEX_ACLV4_KEY_S)   k0b   rsIoKey;

    move rsIoKey.Tid = IPSEC_160BIT_ILA_TID[:4];
    move rsIoKey.SubTid = IPSEC_160BIT_ILA_SUBTID[:4];
    move rsIoKey.VrfId = VrfId;
    move rsIoKey.DstIP = DstIP;
    move rsIoKey.SrcIp = SrcIp;
    move rsIoKey.PolicyGroupIndex = PolicyGroupIndex;
    CE_AD_LOOKUP_LMGP0_160BIT_RETURN32BIT(RESP_DATA, rsIoKey);
}


/***********************************
       IPU IPSEC_FLEX_ACL哈希表项
***********************************/
inline SEARCH_IPSEC_FLEX_ACLV6(uint32 RESP_DATA, uint16 VrfId, uint32 DstIpPart0, uint32 DstIpPart1, uint32 DstIpPart2,
    uint32 DstIpPart3, uint32 SrcIpPart0, uint32 SrcIpPart1, uint32 SrcIpPart2, uint32 SrcIpPart3,
    uint32 PolicyGroupIndex)
{
    (IPSEC_FLEX_ACLV6_KEY1_S)   k0b   rsIoKey1;
    (IPSEC_FLEX_ACLV6_KEY2_S)   k0b   rsIoKey2;

    move rsIoKey1.Tid = IPSEC_320BIT_ILA_TID[:4];
    move rsIoKey1.SubTid = IPSEC_320BIT_ILA_SUBTID[:4];
    move rsIoKey1.VrfId = VrfId;
    move rsIoKey1.DstIpPart0 = DstIpPart0;
    move rsIoKey1.DstIpPart1 = DstIpPart1;
    move rsIoKey1.DstIpPart2 = DstIpPart2;
    move rsIoKey1.DstIpPart3 = DstIpPart3;
    move rsIoKey1.SrcIpPart0 = SrcIpPart0;
    CE_AD_LOOKUP_LMGP0_320BIT_RETURN32BIT(RESP_DATA, rsIoKey1);

    move rsIoKey2.SrcIpPart1 = SrcIpPart1;
    move rsIoKey2.SrcIpPart2 = SrcIpPart2;
    move rsIoKey2.SrcIpPart3 = SrcIpPart3;
    move rsIoKey2.PolicyGroupIndex = PolicyGroupIndex;
    CE_AD_LOOKUP_LMGP0_320BIT_RETURN32BIT(RESP_DATA, rsIoKey2);
}


/***********************************
       IPU IPSEC_LOCALIP哈希表项
***********************************/
inline SEARCH_IPSEC_LOCALIP(COMMON_192BIT_DATA_S RESP_DATA, uint16 VrfId, uint32 SrcIp, uint32 DstIP)
{
    (IPSEC_FLEX_LOCALIP_KEY_S)   k2b   rsIoAddr;

    move ksSeEmReq = GET_SE_PROFILE(TCE_IPSEC_LOCAL_IP_TID);
    move rsIoAddr.SubTid = TCE_IPSEC_LOCAL_IP_SUBTID[:4];
    move rsIoAddr.VrfId = VrfId;
    move rsIoAddr.SrcIp = SrcIp;
    move rsIoAddr.DstIP = DstIP;
    SE_EM_SEARCH_RSP_192BIT(RESP_DATA, ksSeEmReq, rsIoAddr);
}


/***********************************
       IPU IPSEC_TNL哈希表项
***********************************/
inline SEARCH_IPSEC_TNL(COMMON_192BIT_DATA_S RESP_DATA, uint32 LmtIdx)
{
    (IPSEC_FLEX_TNLINFO_KEY_S)   k2b   rsIoAddr;

    move ksSeEmReq = GET_SE_PROFILE(TCE_IPSEC_TUNNEL_TID);
    move rsIoAddr.SubTid = TCE_IPSEC_TUNNEL_SUBTID[:4];
    move rsIoAddr.LmtIdx = LmtIdx;
    SE_EM_SEARCH_RSP_192BIT(RESP_DATA, ksSeEmReq, rsIoAddr);
}


/***********************************
       IPU PORT_MAC哈希表项
***********************************/
inline SEARCH_IPU_PORT_MAC(COMMON_128BIT_DATA_S RESP_DATA, uint16 DMacH16, uint32 DMacL32)
{
    (IPU_PORT_MAC_KEY_S)   k2b   rsIoAddr;

    move ksSeEmReq = GET_SE_PROFILE(TCE_PORT_MAC_TID);
    move rsIoAddr.SubTid = TCE_PORT_MAC_SUBTID[:6];
    move rsIoAddr.DMacH16 = DMacH16;
    move rsIoAddr.DMacL32 = DMacL32;
    SE_EM_SEARCH_RSP_128BIT(RESP_DATA, ksSeEmReq, rsIoAddr);
}


/***********************************
       IPU IPTRACE哈希表项
***********************************/
#if defined(SUPPORT_TCE_LLT_MIC)
inline SEARCH_IPTRACE_IPV4(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIp, uint32 PeerIp,
    uint16 SrcPort, uint16 DstPort, uint16 VpnId)
{
    movezr RESP_DATA = 0x6;
}

inline SEARCH_IPTRACE_IPV6(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,
    uint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,
    uint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort, uint16 VpnId, uint4 Direction)
{
    movezr RESP_DATA = 0x6;
}

inline SEARCH_IPTRACE_ARP(uint32 RESP_DATA, uint16 VpnId, uint4 Direction)
{
    movezr RESP_DATA = 0x6;
}
#else

inline SEARCH_IPTRACE_IPV4(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIp, uint32 PeerIp,
    uint16 SrcPort, uint16 DstPort, uint16 VpnId)
{
    SEARCH_IPTRACE(RESP_DATA, L4Protol, ETH_TYPE_IP4,
        LocalIp, zero, zero, zero, PeerIp, zero, zero, zero,
        SrcPort, DstPort, VpnId, zero[:4]);
}

inline SEARCH_IPTRACE_IPV6(uint32 RESP_DATA, uint8 L4Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,
    uint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,
    uint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort, uint16 VpnId, uint4 Direction)
{
    SEARCH_IPTRACE(RESP_DATA, L4Protol, ETH_TYPE_IP6,
        LocalIPPart0, LocalIPPart1, LocalIPPart2, LocalIPPart3, PeerIPPart0, PeerIPPart1, PeerIPPart2, PeerIPPart3,
        SrcPort, DstPort, VpnId, Direction);
}

inline SEARCH_IPTRACE_ARP(uint32 RESP_DATA, uint16 VpnId, uint4 Direction)
{
    SEARCH_IPTRACE(RESP_DATA, zero[:8], ETH_TYPE_ARP,
        zero, zero, zero, zero, zero, zero, zero, zero,
        zero[:16], zero[:16], VpnId, Direction);
}

#endif

inline SEARCH_IPTRACE(uint32 RESP_DATA, uint8 L4Protol, uint16 L3Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,
    uint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,
    uint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort, uint16 VpnId, uint4 Direction)
{
    SEARCH_IPTRACE_PART1(RESP_DATA, L4Protol, L3Protol,
        LocalIPPart0, LocalIPPart1, LocalIPPart2, LocalIPPart3, PeerIPPart0, PeerIPPart1, PeerIPPart2, PeerIPPart3,
        SrcPort, DstPort);
    SEARCH_IPTRACE_PART2(RESP_DATA, VpnId, Direction);
}

inline SEARCH_IPTRACE_PART1(uint32 RESP_DATA, uint8 L4Protol, uint16 L3Protol, uint32 LocalIPPart0, uint32 LocalIPPart1,
    uint32 LocalIPPart2, uint32 LocalIPPart3, uint32 PeerIPPart0, uint32 PeerIPPart1, uint32 PeerIPPart2,
    uint32 PeerIPPart3, uint16 SrcPort, uint16 DstPort)
{
    (IPTRC_KEY1_S)   k0b  rsIoKey1;
    (IPTRC_KEY2_S)   k0b  rsIoKey2;

    move rsIoKey1.Tid = IP_TRACE_640BIT_ILA_TID[:4];
    move rsIoKey1.SubTid = 0;
    move rsIoKey1.L4Protol = L4Protol;
    move rsIoKey1.L3Protol = L3Protol;
    move rsIoKey1.LocalIPPart0 = LocalIPPart0;
    move rsIoKey1.LocalIPPart1 = LocalIPPart1;
    move rsIoKey1.LocalIPPart2 = LocalIPPart2;
    move rsIoKey1.LocalIPPart3 = LocalIPPart3;
    move rsIoKey1.PeerIPPart0 = PeerIPPart0;
    CE_AD_LOOKUP_LMGP0_640BIT_RETURN32BIT(RESP_DATA, rsIoKey1);

    move rsIoKey2.PeerIPPart1 = PeerIPPart1;
    move rsIoKey2.PeerIPPart2 = PeerIPPart2;
    move rsIoKey2.PeerIPPart3 = PeerIPPart3;
    move rsIoKey2.SrcPort = SrcPort;
    move rsIoKey2.DstPort = DstPort;
    CE_AD_LOOKUP_LMGP0_640BIT_RETURN32BIT(RESP_DATA, rsIoKey2);
}

inline SEARCH_IPTRACE_PART2(uint32 RESP_DATA, uint16 VpnId, uint4 Direction)
{
    (IPTRC_KEY3_S)   k0b   rsIoKey3;
    (IPTRC_KEY4_S)   k0b   rsIoKey4;

    move rsIoKey3.Tid1 = IP_TRACE_640BIT_ILA_TID[:4];
    move rsIoKey3.SubTid1 = 0;
    move rsIoKey3.VpnId = VpnId;
    move rsIoKey3.Direction = Direction;
    CE_AD_LOOKUP_LMGP0_640BIT_RETURN32BIT(RESP_DATA, rsIoKey3);

    CE_AD_LOOKUP_LMGP0_640BIT_RETURN32BIT(RESP_DATA, rsIoKey4);
}