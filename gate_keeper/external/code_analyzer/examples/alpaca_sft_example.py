#!/usr/bin/env python3
"""
Alpaca SFT数据生成示例

演示如何使用AlpacaSFTGenerator将prompt_sft数据转换为Alpaca格式
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.data_gen.sft.alpaca_sft.generator import AlpacaSFTGenerator
from core.data_gen.sft.alpaca_sft.models import AlpacaTaskType
from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.generator import SFTDataGenerator
from core.data_gen.sft.prompt_sft.models import ModelType


def main():
    """主函数"""
    print("=== Alpaca SFT数据生成示例 ===\n")
    
    # 1. 初始化分析器和生成器
    print("1. 初始化分析器和生成器...")
    examples_dir = project_root / "examples" / "np"
    analyzer = RealNPAnalyzer(str(examples_dir))
    analyzer.analyze_project()
    
    sft_generator = SFTDataGenerator(analyzer)
    alpaca_generator = AlpacaSFTGenerator(sft_generator)
    
    # 2. 显示支持的任务类型
    print(f"\n2. 支持的任务类型: {[task.value for task in alpaca_generator.get_supported_tasks()]}")
    
    # 3. 生成Alpaca SFT数据
    print("\n3. 生成Alpaca SFT数据...")
    
    # 只测试code_completion任务
    task_type = AlpacaTaskType.CODE_COMPLETION
    print(f"\n  生成 {task_type.value} 任务类型的数据...")
    
    alpaca_data = alpaca_generator.generate_alpaca_sft_data(
        max_samples=5,
        model_type=ModelType.DEEPSEEK_CODER,
        task_type=task_type
    )
    
    if alpaca_data:
        # 4. 保存数据
        output_dir = project_root / "examples" / "alpaca_sft_output"
        output_dir.mkdir(exist_ok=True)
        
        # 保存完整格式
        full_output_file = output_dir / f"alpaca_{task_type.value}_full.json"
        alpaca_generator.save_to_json(alpaca_data, str(full_output_file), alpaca_format_only=False)
        
        # 保存标准Alpaca格式
        alpaca_output_file = output_dir / f"alpaca_{task_type.value}.json"
        alpaca_generator.save_to_json(alpaca_data, str(alpaca_output_file), alpaca_format_only=True)
        
        # 保存JSONL格式
        jsonl_output_file = output_dir / f"alpaca_{task_type.value}.jsonl"
        alpaca_generator.save_to_jsonl(alpaca_data, str(jsonl_output_file), alpaca_format_only=True)
        
        # 5. 显示统计信息
        stats = alpaca_generator.get_statistics(alpaca_data)
        print(f"    统计信息:")
        print(f"      总样本数: {stats['total_samples']}")
        print(f"      平均指令长度: {stats['avg_instruction_length']:.1f}")
        print(f"      平均输入长度: {stats['avg_input_length']:.1f}")
        print(f"      平均输出长度: {stats['avg_output_length']:.1f}")
        print(f"      平均复杂度分数: {stats['complexity_score_stats']['avg']:.3f}")
        
        # 6. 显示示例数据
        print(f"\n6. 示例数据:")
        sample = alpaca_data[0]
        print(f"    文件: {sample.filepath}")
        print(f"    函数: {sample.function_name}")
        print(f"    行号: {sample.line_number}")
        print(f"    任务类型: {sample.task_type.value}")
        print(f"    复杂度分数: {sample.complexity_score:.3f}")
        
        print(f"\n    指令:")
        print(f"    {sample.instruction}")
        
        print(f"\n    输入 (前200字符):")
        input_preview = sample.input[:200] + "..." if len(sample.input) > 200 else sample.input
        print(f"    {input_preview}")
        
        print(f"\n    输出:")
        print(f"    {sample.output}")
    else:
        print(f"    未生成任何数据")
    
    print("\n=== 生成完成 ===")
    print(f"输出目录: {project_root / 'examples' / 'alpaca_sft_output'}")


if __name__ == "__main__":
    main() 