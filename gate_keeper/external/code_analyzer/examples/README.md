# 使用示例

本目录包含各种数据生成功能的使用示例。

## 示例脚本

### 1. NP项目真实分析 (`np_real_analysis.py`)
- **功能**: 对NP项目进行真实分析，生成SFT训练数据
- **输出**: 增强SFT数据、函数信息、代码图等
- **用法**: `python examples/np_real_analysis.py`

### 2. 提示词SFT数据生成 (`prompt_sft_example.py`)
- **功能**: 生成适用于不同模型的提示词格式SFT数据
- **支持模型**: Qwen Coder、DeepSeek Coder
- **输出**: 结构化提示词训练数据
- **用法**: `python examples/prompt_sft_example.py`

### 3. Alpaca SFT数据生成 (`alpaca_sft_example.py`)
- **功能**: 生成Alpaca格式的指令微调训练数据
- **支持任务**: 代码补全、代码编辑、代码解释、Bug修复、代码重构
- **输出**: 标准Alpaca格式（instruction/input/output）
- **用法**: `python examples/alpaca_sft_example.py`

### 4. Embedding数据生成 (`embedding_data_generation.py`)
- **功能**: 生成用于上下文检索和排序的训练数据
- **支持**: Embedding对比学习、Rerank排序训练
- **输出**: 查询-候选对训练数据
- **用法**: `python examples/embedding_data_generation.py`

## 输出目录

### `real_analysis_output/`
- `enhanced_sft_data.json` - 增强SFT数据
- `real_functions.json` - 函数分析结果
- `real_code_graph.json` - 代码图信息

### `prompt_sft_output/`
- `prompt_sft_qwen_coder.json` - Qwen Coder格式数据
- `prompt_sft_deepseek_coder.json` - DeepSeek Coder格式数据
- `stats_*.json` - 统计信息

### `alpaca_sft_output/`
- `alpaca_code_completion.json` - 代码补全任务数据
- `alpaca_code_edit.json` - 代码编辑任务数据
- `alpaca_code_explanation.json` - 代码解释任务数据
- `alpaca_bug_fix.json` - Bug修复任务数据
- `alpaca_code_refactor.json` - 代码重构任务数据
- `*.jsonl` - JSONL格式数据
- `*_full.json` - 包含元数据的完整格式

### `embedding_output/`
- `embedding_training_data.json` - Embedding训练数据
- `rerank_training_data.json` - Rerank训练数据

## 数据格式说明

### Alpaca格式
```json
{
  "instruction": "请根据给定的代码上下文，在指定位置补全代码。",
  "input": "文件路径: example.c\n函数名称: foo\n...",
  "output": "int result = x + 1;"
}
```

### 提示词SFT格式
```json
{
  "prompt": "<system>You are a code completion assistant...</system><|fim_prefix|>...",
  "completion": "int result = x + 1;",
  "filepath": "example.c",
  "function_name": "foo"
}
```

### 增强SFT格式
```json
{
  "before": "void foo() {\n  int x = 1;\n",
  "expected": "  int result = x + 1;\n",
  "after": "  return result;\n}",
  "context_nodes": [...],
  "dependency_graph": {...},
  "metadata": {...}
}
```

## 参数配置

所有示例脚本都支持参数化配置，主要参数包括：

- `examples_dir`: 示例代码目录
- `output_dir`: 输出目录
- `max_samples`: 最大生成样本数
- `task_type`: 任务类型（Alpaca）
- `model_type`: 模型类型（提示词SFT）

## 运行环境

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

运行示例前确保项目根目录在Python路径中：
```bash
export PYTHONPATH=/path/to/code_analyzer:$PYTHONPATH
``` 