"""
Code Analyzer Setup

代码分析外部依赖包的安装配置
"""

from setuptools import find_packages, setup

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="code-analyzer",
    version="1.0.0",
    author="Git Keeper Team",
    author_email="<EMAIL>",
    description="Code analysis and AST parsing library",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/gitkeeper/code-analyzer",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Software Development :: Quality Assurance",
        "Topic :: Software Development :: Testing",
    ],
    python_requires=">=3.8",
    install_requires=[
        "tree-sitter>=0.20.0",
        "tree-sitter-python>=0.20.0",
        "tree-sitter-c>=0.20.0",
        "networkx>=2.5",
        "pydantic>=1.8.0",
        "pathlib2>=2.3.0;python_version<'3.4'",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.10",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
            "sphinx>=4.0",
            "sphinx-rtd-theme>=0.5",
        ],
        "test": [
            "pytest>=6.0",
            "pytest-cov>=2.10",
            "pytest-mock>=3.6",
        ],
    },
    entry_points={
        "console_scripts": [
            "code-analyzer=code_analyzer.cli:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
) 