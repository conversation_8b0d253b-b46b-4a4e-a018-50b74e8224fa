# Code Analyzer

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Documentation](https://img.shields.io/badge/Documentation-v2.0.0-blue.svg)](docs/)

一个基于Tree-sitter的智能代码分析工具，专注于生成高质量的SFT（Supervised Fine-Tuning）训练数据，支持多种编程语言的代码补全和编辑任务。

## ✨ 主要特性

### 🔍 智能代码分析
- **多语言支持**: 支持Python、C、NP等多种编程语言
- **Tree-sitter解析**: 基于高效的语法解析框架
- **代码图构建**: 自动构建函数调用图和依赖关系图
- **结构体分析**: 深入分析结构体定义和使用关系

### 🎯 SFT数据生成 (v2)
- **瀑布式遮盖**: 模拟程序员逐行编写代码的流程
- **随机遮盖**: 模拟代码编辑和修改行为
- **控制流检测**: 自动识别if、for、while等控制流语句
- **注释处理**: 智能处理单行、多行、TODO等注释类型
- **依赖图构建**: 构建显式代码依赖关系图，支持图神经网络

### 🛡️ 质量保证
- **质量验证**: 自动验证生成数据的完整性和准确性
- **多样性保证**: 确保数据的多样性和平衡性
- **实用性验证**: 基于真实项目代码生成，确保实用性

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd code_analyzer

# 安装依赖
pip install -e .
```

### 基本使用

```python
from examples.np_real_analysis import RealNPAnalyzer

# 初始化分析器
analyzer = RealNPAnalyzer("examples/np")

# 分析项目并生成SFT数据
analyzer.analyze_project()
enhanced_data = analyzer.generate_enhanced_sft_data(max_samples=50)

# 保存结果
analyzer.save_real_results("output")
```

### 查看结果

生成的数据文件位于 `real_analysis_output/` 目录：
- `enhanced_sft_data.json` - 增强SFT训练数据（v2格式）
- `real_functions.json` - 函数分析结果
- `real_code_graph.json` - 代码图信息

## 📊 数据格式示例

### 增强SFT数据 (v2)

```json
{
  "task_type": "code_completion",
  "strategy": "waterfall_sequential",
  "mask_level": "statement",
  "filepath": "examples/np/ipu_usertrace.asm",
  "function_name": "iIpuUsrTrcOutIpV6Judge",
  "line_number": 7,
  "before": "void iIpuUsrTrcOutIpV6Judge(uint8 IsInPae)\n...",
  "expected": "UPF_ZERO_COMMON_32BIT_DATA(rsUpfIpPktL3Info)  // 调用UpfParseIpPkt前显式清空解析结果",
  "after": "UPF_ZERO_COMMON_128BIT_DATA(rsUpfIpPktL4Info)...",
  "ast_node_type": "statement_with_comment",
  "is_control_flow": false,
  "comment_hint": true,
  "complexity_score": 0.5,
  "dependency_graph": {
    "nodes": [
      {"id": "fn_iIpuUsrTrcOutIpV6Judge", "type": "function"},
      {"id": "var_rsUpfIpPktL3Info", "type": "variable"}
    ],
    "edges": [
      {"source": "fn_iIpuUsrTrcOutIpV6Judge", "target": "var_rsUpfIpPktL3Info", "relation": "uses"}
    ]
  }
}
```

## 📁 项目结构

```
code_analyzer/
├── core/                    # 核心分析模块
│   ├── ast_parser.py       # AST解析器
│   ├── code_graph.py       # 代码图构建
│   └── static_analyzer.py  # 静态分析器
├── models/                 # 数据模型定义
│   ├── ast_node.py        # AST节点模型
│   ├── function.py        # 函数模型
│   └── call_relation.py   # 调用关系模型
├── parsers/               # 语言解析器
│   ├── base.py           # 解析器基类
│   ├── python_parser.py  # Python解析器
│   ├── c_parser.py       # C语言解析器
│   └── np_parser.py      # NP语言解析器
├── examples/              # 使用示例
│   └── np_real_analysis.py # NP项目分析示例
├── docs/                  # 文档目录
│   ├── design/           # 设计文档
│   ├── guides/           # 使用指南
│   └── task/             # 任务文档
└── tests/                # 测试文件
```

## 📚 文档

### 📖 完整文档
- **[文档中心](docs/)** - 完整的项目文档
- **[设计文档](docs/design/)** - 系统设计和技术规范
- **[使用指南](docs/guides/)** - 快速开始和高级用法
- **[API文档](docs/api/)** - 完整的API参考

### 🎯 快速导航
- **[SFT数据生成指南](docs/guides/SFT_DATA_GENERATION_GUIDE.md)** - 快速开始
- **[增强SFT数据设计规范 (v2)](docs/design/ENHANCED_SFT_DATA_DESIGN.md)** - 详细设计
- **[SFT数据生成总结](docs/task/SFT_DATA_GENERATION_SUMMARY.md)** - 实现总结

## 🔧 主要功能

### 代码分析功能
- ✅ **多语言解析**: Python、C、NP语言支持
- ✅ **函数提取**: 自动提取函数定义和签名
- ✅ **调用关系**: 构建函数调用关系图
- ✅ **结构体分析**: 分析结构体定义和使用
- ✅ **变量追踪**: 追踪变量声明和使用

### SFT数据生成功能
- ✅ **瀑布式遮盖**: 模拟逐行编写代码
- ✅ **随机遮盖**: 模拟代码编辑行为
- ✅ **控制流检测**: 自动识别控制流语句
- ✅ **注释处理**: 智能处理各种注释
- ✅ **依赖图构建**: 构建显式依赖关系图
- ✅ **质量验证**: 自动验证数据质量

## 🎯 应用场景

### 代码补全训练
- 生成高质量的代码补全训练数据
- 支持不同粒度的遮盖策略
- 提供丰富的上下文信息

### 代码编辑训练
- 模拟真实的代码编辑场景
- 支持随机位置的遮盖
- 保持代码的语义完整性

### 代码理解训练
- 提供代码结构信息
- 构建依赖关系图
- 支持图神经网络训练

## 🤝 贡献

我们欢迎所有形式的贡献！

### 贡献方式
1. **报告问题**: 在GitHub Issues中报告bug或提出建议
2. **提交代码**: Fork项目并提交Pull Request
3. **改进文档**: 帮助完善文档和示例
4. **分享经验**: 分享使用经验和最佳实践

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd code_analyzer

# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest tests/

# 运行示例
python examples/np_real_analysis.py
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您在使用过程中遇到问题：

1. 查看 [使用指南](docs/guides/SFT_DATA_GENERATION_GUIDE.md)
2. 检查 [常见问题](docs/guides/SFT_DATA_GENERATION_GUIDE.md#常见问题)
3. 在 [GitHub Issues](../../issues) 中搜索或创建新问题

## 📝 更新日志

### v2.0.0 (最新)
- ✅ 新增控制流检测功能 (`is_control_flow`)
- ✅ 新增注释提示检测功能 (`comment_hint`)
- ✅ 新增显式依赖图构建 (`dependency_graph`)
- ✅ 完善数据质量验证机制
- ✅ 优化文档结构和内容

### v1.0.0
- ✅ 基础代码分析功能
- ✅ Tree-sitter解析支持
- ✅ 基础SFT数据生成
- ✅ 代码图构建功能

---

**Code Analyzer** - 让代码分析变得简单高效 🚀

**最后更新**: 2024年12月 | **版本**: v2.0.0 