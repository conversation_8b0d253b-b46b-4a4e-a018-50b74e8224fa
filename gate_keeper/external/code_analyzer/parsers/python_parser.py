"""
Python Parser

Python代码解析器，基于tree-sitter实现
"""

import os
from typing import List, Optional

import tree_sitter_python as tspython
from tree_sitter import Language, Node, Parser

from models.call_relation import FunctionCall
from models.function import Function, FunctionSignature, Parameter

from .base import BaseParser

PY_LANGUAGE = Language(tspython.language())


class PythonParser(BaseParser):
    """Python代码解析器，基于tree-sitter实现。"""

    def __init__(self):
        """初始化Python解析器。"""
        self.parser = Parser(PY_LANGUAGE)

    def _parse_code(self, code: str):
        """解析代码生成语法树"""
        return self.parser.parse(bytes(code, "utf8"))

    def _get_text(self, code: str, node: Node) -> str:
        """
        从代码中提取节点对应的文本。

        Args:
            code: 源代码字符串
            node: 语法树节点

        Returns:
            str: 节点对应的文本
        """
        # code 是字符串，而 start_byte 和 end_byte 是字节索引，所以需要先将字符串encode
        return code.encode("utf-8")[node.start_byte:node.end_byte].decode("utf-8")

    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        """
        从Python代码中提取函数定义。

        Args:
            file_path: 文件路径
            file_content: 可选的文件内容

        Returns:
            List[Function]: 函数定义列表
        """
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root = tree.root_node
            functions = []

            def walk(node: Node):
                if node.type == "function_definition":
                    name_node = node.child_by_field_name("name")
                    if name_node:
                        name = self._get_text(code, name_node)
                        func_code = self._get_text(code, node)
                        
                        # 提取参数
                        parameters = []
                        parameters_node = node.child_by_field_name("parameters")
                        if parameters_node:
                            for param_node in parameters_node.children:
                                if param_node.type == "typed_parameter":
                                    param_name = param_node.child_by_field_name("name")
                                    param_type = param_node.child_by_field_name("type")
                                    if param_name:
                                        name_text = self._get_text(code, param_name)
                                        type_text = None
                                        if param_type:
                                            type_text = self._get_text(code, param_type)
                                        parameters.append(Parameter(name=name_text, type_hint=type_text))
                                elif param_node.type == "identifier":
                                    param_name = self._get_text(code, param_node)
                                    parameters.append(Parameter(name=param_name))
                        
                        # 提取返回类型
                        return_type = None
                        return_type_node = node.child_by_field_name("return_type")
                        if return_type_node:
                            return_type = self._get_text(code, return_type_node)
                        
                        signature = FunctionSignature(name=name, parameters=parameters, return_type=return_type)
                        
                        functions.append(Function.create_simple(
                            name=name,
                            signature=signature,
                            start_line=node.start_point[0] + 1,
                            end_line=node.end_point[0] + 1,
                            filepath=file_path,
                            code=func_code
                        ))
                for child in node.children:
                    walk(child)

            walk(root)
            return functions

        except Exception as e:
            raise ValueError(f"Error extracting functions from {file_path}: {str(e)}")

    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        """提取函数调用"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root = tree.root_node
            calls = []

            def get_func_name(node: Node) -> Optional[str]:
                name_node = node.child_by_field_name("name")
                if name_node:
                    return self._get_text(code, name_node)
                return None

            def get_callee_name(node: Node) -> Optional[str]:
                # 在call节点中，函数名是第一个子节点（identifier类型）
                for child in node.children:
                    if child.type == "identifier":
                        return self._get_text(code, child)
                return None

            def walk(node: Node, current_func: Optional[str] = None):
                if node.type == "function_definition":
                    current_func = get_func_name(node)
                elif node.type == "call":
                    callee = get_callee_name(node)
                    if current_func and callee:
                        calls.append(FunctionCall(
                            caller=current_func,
                            callee=callee,
                            file_path=file_path,
                            line=[node.start_point[0] + 1, node.end_point[0] + 1],
                            code=self._get_text(code, node)
                        ))
                for child in node.children:
                    walk(child, current_func)

            walk(root)
            return calls

        except Exception as e:
            raise ValueError(f"Error extracting calls from {file_path}: {str(e)}")

    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        """查找父级元素"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root_node = tree.root_node

            def traverse(node: Node) -> Optional[Node]:
                start_line_of_node = node.start_point[0] + 1
                end_line_of_node = node.end_point[0] + 1

                # 检查是否包含在给定的行号范围内
                if start_line <= end_line_of_node and end_line >= start_line_of_node:
                    if node.type == 'function_definition':
                        return node
                for child in node.children:
                    found = traverse(child)
                    if found:
                        return found
                return None

            # 在根节点开始遍历查找
            element_node = traverse(root_node)

            if element_node and element_node.type == 'function_definition':
                # 提取函数名
                name_node = element_node.child_by_field_name("name")
                if name_node:
                    func_name = self._get_text(code, name_node)
                else:
                    func_name = "unknown_function"

                return Function.create_simple(
                    name=func_name,
                    start_line=element_node.start_point[0] + 1,
                    end_line=element_node.end_point[0] + 1,
                    filepath=file_path,
                    signature=FunctionSignature(func_name, [], None),
                    code=self._get_text(code, element_node)
                )
            return None

        except Exception as e:
            raise ValueError(f"Error finding parent element in {file_path}: {str(e)}") 