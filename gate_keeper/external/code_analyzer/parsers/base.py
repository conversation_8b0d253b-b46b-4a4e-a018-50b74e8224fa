"""
Base Parser

解析器基类
"""

import os
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple

from tree_sitter import Node, Tree

from models.call_relation import FunctionCall
from models.function import Function
from models.macro import Macro
from models.struct import Struct, StructRelation, StructUsage


class BaseParser(ABC):
    """解析器基类"""
    
    def _get_code_and_tree(self, file_path: str, file_content: Optional[str] = None) -> Tuple[str, Tree, List[str]]:
        """
        获取代码内容和语法树
        
        Args:
            file_path: 文件路径
            file_content: 文件内容（可选）
            
        Returns:
            (代码内容, 语法树, 代码行列表)
        """
        code: Optional[str] = None

        if file_content is not None:
            code = file_content if file_content else ""
        elif os.path.exists(file_path):
            # 使用健壮的文件读取函数
            try:
                from utils.file_utils import read_file_safe
                code = read_file_safe(file_path)
            except Exception as e:
                raise ValueError(f"Failed to read file {file_path}: {e}")
        else:
            raise ValueError(f"File not found: {file_path}")

        if code is None:
            raise ValueError(f"Failed to read code from {file_path}")

        try:
            # 解析语法树
            tree = self._parse_code(code)
        except Exception as e:
            raise ValueError(f"Failed to parse code from {file_path}: {e}")

        lines = code.splitlines()
        return code, tree, lines
    
    @abstractmethod
    def _parse_code(self, code: str) -> Tree:
        """解析代码生成语法树"""
        pass
    
    @abstractmethod
    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        """提取函数定义"""
        pass
    
    @abstractmethod
    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        """提取函数调用"""
        pass
    
    @abstractmethod
    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        """查找父级元素"""
        pass
    
    def extract_macros(self, file_path: str, file_content: Optional[str] = None) -> List[Macro]:
        """提取宏定义（默认实现返回空列表）"""
        return []
    
    def extract_structs(self, file_path: str, file_content: Optional[str] = None) -> List[Struct]:
        """提取结构体定义（默认实现返回空列表）"""
        return []
    
    def extract_struct_relations(self, file_path: str, file_content: Optional[str] = None) -> List[StructRelation]:
        """提取结构体关系（默认实现返回空列表）"""
        return []
    
    def extract_struct_usages(self, file_path: str, file_content: Optional[str] = None) -> List[StructUsage]:
        """提取结构体使用（默认实现返回空列表）"""
        return []
    
    def _get_node_text(self, node: Node, code: str) -> str:
        """获取节点的文本内容"""
        return code[node.start_byte:node.end_byte]
    
    def _get_node_lines(self, node: Node, lines: List[str]) -> List[str]:
        """获取节点的行内容"""
        start_line = node.start_point[0]
        end_line = node.end_point[0]
        return lines[start_line:end_line + 1] 