"""
NP Parser

NP语言解析器，基于tree-sitter-np实现
"""

import os
import sys
from typing import List, Optional

from tree_sitter import Language, Node, Parser

from models.call_relation import FunctionCall
from models.function import Function, FunctionSignature, Parameter
from models.struct import StructUsage

from .base import BaseParser

try:
    # 尝试导入tree-sitter-np
    import tree_sitter_np as tsnp
    NP_LANGUAGE = Language(tsnp.language())
except Exception as e:
    print(f"Warning: Failed to load tree-sitter-np: {e}")
    NP_LANGUAGE = None


class NPParser(BaseParser):
    """NP语言解析器，基于tree-sitter-np实现。"""

    def __init__(self):
        """初始化NP解析器。"""
        if NP_LANGUAGE is None:
            raise ValueError("NP language not available. Please ensure tree-sitter-np is properly built.")
        self.parser = Parser()
        self.parser.language = NP_LANGUAGE

    def _parse_code(self, code: str):
        """解析代码生成语法树"""
        return self.parser.parse(bytes(code, "utf8"))

    def _get_text(self, code: str, node: Node) -> str:
        """
        从代码中提取节点对应的文本。

        Args:
            code: 源代码字符串
            node: 语法树节点

        Returns:
            str: 节点对应的文本
        """
        return code.encode("utf-8")[node.start_byte:node.end_byte].decode("utf-8")

    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        """
        从NP代码中提取函数定义。

        Args:
            file_path: 文件路径
            file_content: 可选的文件内容

        Returns:
            List[Function]: 函数定义列表
        """
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root = tree.root_node
            functions = []

            def walk(node: Node):
                # NP语言中的函数定义节点类型
                if node.type in ["statement_function_def", "declare_function"]:
                    # 查找函数名
                    func_name = self._extract_function_name(node, code)
                    if func_name:
                        func_code = self._get_text(code, node)
                        
                        # 提取参数
                        parameters = self._extract_function_parameters(node, code)
                        
                        # 提取返回类型
                        return_type = self._extract_return_type(node, code)
                        
                        signature = FunctionSignature(name=func_name, parameters=parameters, return_type=return_type)
                        
                        functions.append(Function.create_simple(
                            name=func_name,
                            signature=signature,
                            start_line=node.start_point[0] + 1,
                            end_line=node.end_point[0] + 1,
                            filepath=file_path,
                            code=func_code
                        ))
                
                try:
                    if hasattr(node, 'children') and node.children:
                        if isinstance(node.children, (list, tuple)):
                            for child in node.children:
                                if hasattr(child, 'type'):
                                    walk(child)
                        else:
                            children_list = list(node.children)
                            for child in children_list:
                                if hasattr(child, 'type'):
                                    walk(child)
                except (TypeError, AttributeError):
                    pass

            walk(root)
            
            # 如果tree-sitter解析失败，使用降级方案
            if not functions:
                print(f"  ⚠️ tree-sitter解析未提取到函数，使用降级方案")
                functions = self._extract_functions_fallback(code, file_path)
            
            return functions

        except Exception as e:
            print(f"  ⚠️ tree-sitter解析失败: {e}，使用降级方案")
            # 读取文件内容用于降级方案
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    code = f.read()
            except Exception as read_error:
                print(f"  ❌ 文件读取失败: {read_error}")
                return []
            return self._extract_functions_fallback(code, file_path)

    def _extract_function_name(self, node: Node, code: str) -> Optional[str]:
        """提取函数名"""
        # 在statement_function_def中，函数名是第三个子节点（在linkage_type和void之后）
        if node.type == "statement_function_def":
            children = list(node.children)
            for i, child in enumerate(children):
                if child.type == "identifier":
                    return self._get_text(code, child)
        
        # 递归查找
        try:
            if hasattr(node, 'children') and node.children:
                if isinstance(node.children, (list, tuple)):
                    for child in node.children:
                        if hasattr(child, 'type'):
                            name = self._extract_function_name(child, code)
                            if name:
                                return name
                else:
                    children_list = list(node.children)
                    for child in children_list:
                        if hasattr(child, 'type'):
                            name = self._extract_function_name(child, code)
                            if name:
                                return name
        except (TypeError, AttributeError):
            pass
        return None

    def _extract_function_parameters(self, node: Node, code: str) -> List[Parameter]:
        """提取函数参数"""
        parameters = []
        
        def find_parameters(n: Node):
            if n.type == "parameter_list":
                for param_node in n.children:
                    if param_node.type == "parameter_declaration":
                        param_name = None
                        param_type = None
                        
                        for child in param_node.children:
                            if child.type == "identifier":
                                param_name = self._get_text(code, child)
                            elif child.type == "type_identifier":
                                param_type = self._get_text(code, child)
                        
                        if param_name:
                            parameters.append(Parameter(name=param_name, type_hint=param_type))
            
            try:
                if hasattr(n, 'children') and n.children:
                    if isinstance(n.children, (list, tuple)):
                        for child in n.children:
                            if hasattr(child, 'type'):
                                find_parameters(child)
                    else:
                        children_list = list(n.children)
                        for child in children_list:
                            if hasattr(child, 'type'):
                                find_parameters(child)
            except (TypeError, AttributeError):
                pass
        
        find_parameters(node)
        return parameters

    def _extract_return_type(self, node: Node, code: str) -> Optional[str]:
        """提取返回类型"""
        def find_return_type(n: Node):
            if n.type == "type_identifier":
                return self._get_text(code, n)
            try:
                if hasattr(n, 'children') and n.children:
                    if isinstance(n.children, (list, tuple)):
                        for child in n.children:
                            if hasattr(child, 'type'):
                                result = find_return_type(child)
                                if result:
                                    return result
                    else:
                        children_list = list(n.children)
                        for child in children_list:
                            if hasattr(child, 'type'):
                                result = find_return_type(child)
                                if result:
                                    return result
            except (TypeError, AttributeError):
                pass
            return None
        
        return find_return_type(node)

    def _extract_functions_fallback(self, code: str, file_path: str) -> List[Function]:
        """降级方案：使用文本模式提取函数"""
        import re
        functions = []
        lines = code.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 检测函数定义模式
            patterns = [
                r'(?:static\s+)?(?:void|int|uint8|uint16|uint32)\s+(\w+)\s*\(',
                r'function\s+(\w+)',
                r'bundle\s+(\w+)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    func_name = match.group(1)
                    
                    # 查找函数结束位置
                    end_line = self._find_function_end(lines, i)
                    
                    # 提取函数代码
                    func_code = '\n'.join(lines[i:end_line+1])
                    
                    # 创建函数对象
                    signature = FunctionSignature(
                        name=func_name,
                        parameters=[],
                        return_type="void"
                    )
                    
                    func = Function.create_simple(
                        name=func_name,
                        signature=signature,
                        start_line=i + 1,
                        end_line=end_line + 1,
                        filepath=file_path,
                        code=func_code
                    )
                    functions.append(func)
                    print(f"    ✓ 降级提取函数: {func_name}")
                    break
        
        return functions
    
    def _find_function_end(self, lines: List[str], start_line: int) -> int:
        """查找函数结束位置"""
        brace_count = 0
        started = False
        
        for i in range(start_line, len(lines)):
            line = lines[i].strip()
            
            if '{' in line:
                brace_count += line.count('{')
                started = True
            
            if '}' in line:
                brace_count -= line.count('}')
            
            if started and brace_count == 0:
                return i
        
        return len(lines) - 1

    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        """提取函数调用"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root = tree.root_node
            calls = []

            def get_func_name(node: Node) -> Optional[str]:
                # 查找当前函数名
                try:
                    if hasattr(node, 'children') and node.children:
                        if isinstance(node.children, (list, tuple)):
                            for child in node.children:
                                if hasattr(child, 'type') and child.type == "identifier":
                                    return self._get_text(code, child)
                        else:
                            children_list = list(node.children)
                            for child in children_list:
                                if hasattr(child, 'type') and child.type == "identifier":
                                    return self._get_text(code, child)
                except (TypeError, AttributeError):
                    pass
                return None

            def get_callee_name(node: Node) -> Optional[str]:
                # 在函数调用节点中查找被调用的函数名
                if node.type in ["statement_function_call", "expression_function_call"]:
                    try:
                        if hasattr(node, 'children') and node.children:
                            if isinstance(node.children, (list, tuple)):
                                for child in node.children:
                                    if hasattr(child, 'type') and child.type == "identifier":
                                        return self._get_text(code, child)
                            else:
                                children_list = list(node.children)
                                for child in children_list:
                                    if hasattr(child, 'type') and child.type == "identifier":
                                        return self._get_text(code, child)
                    except (TypeError, AttributeError):
                        pass
                return None

            def walk(node: Node, current_func: Optional[str] = None):
                if node.type in ["statement_function_def", "declare_function"]:
                    current_func = get_func_name(node)
                elif node.type in ["statement_function_call", "expression_function_call"]:
                    callee = get_callee_name(node)
                    if current_func and callee:
                        calls.append(FunctionCall(
                            caller=current_func,
                            callee=callee,
                            file_path=file_path,
                            line=[node.start_point[0] + 1, node.end_point[0] + 1],
                            code=self._get_text(code, node)
                        ))
                
                try:
                    if hasattr(node, 'children') and node.children:
                        if isinstance(node.children, (list, tuple)):
                            for child in node.children:
                                if hasattr(child, 'type'):
                                    walk(child, current_func)
                        else:
                            children_list = list(node.children)
                            for child in children_list:
                                if hasattr(child, 'type'):
                                    walk(child, current_func)
                except (TypeError, AttributeError):
                    pass

            walk(root)
            return calls

        except Exception as e:
            raise ValueError(f"Error extracting calls from {file_path}: {str(e)}")

    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        """查找父级元素"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root_node = tree.root_node

            def traverse(node: Node) -> Optional[Node]:
                start_line_of_node = node.start_point[0] + 1
                end_line_of_node = node.end_point[0] + 1

                # 检查是否包含在给定的行号范围内
                if start_line <= end_line_of_node and end_line >= start_line_of_node:
                    if node.type in ['statement_function_def', 'declare_function']:
                        return node
                
                try:
                    if hasattr(node, 'children') and node.children:
                        if isinstance(node.children, (list, tuple)):
                            for child in node.children:
                                if hasattr(child, 'type'):
                                    found = traverse(child)
                                    if found:
                                        return found
                        else:
                            children_list = list(node.children)
                            for child in children_list:
                                if hasattr(child, 'type'):
                                    found = traverse(child)
                                    if found:
                                        return found
                except (TypeError, AttributeError):
                    pass
                return None

            # 在根节点开始遍历查找
            element_node = traverse(root_node)

            if element_node and element_node.type in ['statement_function_def', 'declare_function']:
                # 提取函数名
                func_name = self._extract_function_name(element_node, code)
                if not func_name:
                    func_name = "unknown_function"

                return Function.create_simple(
                    name=func_name,
                    start_line=element_node.start_point[0] + 1,
                    end_line=element_node.end_point[0] + 1,
                    filepath=file_path,
                    signature=FunctionSignature(func_name, [], None),
                    code=self._get_text(code, element_node)
                )
            return None

        except Exception as e:
            raise ValueError(f"Error finding parent element in {file_path}: {str(e)}")

    def extract_struct_usages(self, file_path: str, file_content: Optional[str] = None) -> List[StructUsage]:
        """提取NP语言中的结构体使用"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []

        if not code:
            return []

        usages = []

        def walk(node: Node, current_func: Optional[str] = None):
            # 处理函数定义，记录当前函数名
            if node.type in ["statement_function_def", "declare_function"]:
                current_func = self._extract_function_name(node, code)

            # 处理字段访问（结构体成员访问）
            elif node.type == "object_field_access":
                _process_field_access_usage(node, current_func)
            
            # 处理变量声明
            elif node.type == "declaration":
                _process_declaration_usage(node, current_func)
            
            # 处理赋值操作
            elif node.type == "expression_assignment":
                _process_assignment_usage(node, current_func)
            
            # 处理函数调用中的结构体参数
            elif node.type in ["statement_function_call", "expression_function_call"]:
                _process_function_call_usage(node, current_func)

            try:
                if hasattr(node, 'children') and node.children:
                    if isinstance(node.children, (list, tuple)):
                        for child in node.children:
                            if hasattr(child, 'type'):
                                walk(child, current_func)
                    else:
                        children_list = list(node.children)
                        for child in children_list:
                            if hasattr(child, 'type'):
                                walk(child, current_func)
            except (TypeError, AttributeError):
                pass

        def _process_field_access_usage(node: Node, current_func: Optional[str]):
            """处理字段访问中的结构体使用"""
            try:
                # 提取字段访问的完整表达式
                field_expr = self._get_text(code, node)
                
                # 分析字段访问模式，如 rsUpfSpecExtRetInfo.PosInfo.PosType
                if '.' in field_expr:
                    # 提取结构体名称（第一个部分）
                    parts = field_expr.split('.')
                    if len(parts) >= 2:
                        struct_var = parts[0]  # 如 rsUpfSpecExtRetInfo
                        field_path = '.'.join(parts[1:])  # 如 PosInfo.PosType
                        
                        usages.append(StructUsage(
                            struct_name="<inferred>",  # 需要从上下文推断具体结构体名
                            usage_type="field_access",
                            variable_name=struct_var,
                            function_name=current_func,
                            file_path=file_path,
                            line=[node.start_point[0] + 1, node.end_point[0] + 1],
                            code=field_expr
                        ))
            except Exception:
                pass

        def _process_declaration_usage(node: Node, current_func: Optional[str]):
            """处理变量声明中的结构体使用"""
            try:
                # 查找类型和变量名
                type_name = None
                var_name = None
                
                for child in node.children:
                    if hasattr(child, 'type'):
                        if child.type in ["type_identifier", "primitive_type"]:
                            type_name = self._get_text(code, child)
                        elif child.type == "init_declarator":
                            # 查找变量名
                            for grandchild in child.children:
                                if hasattr(grandchild, 'type') and grandchild.type == "identifier":
                                    var_name = self._get_text(code, grandchild)
                                    break
                
                if var_name and type_name:
                    # 检查是否是结构体类型（NP语言中可能以大写字母开头）
                    if type_name[0].isupper() or type_name.endswith('_S'):
                        usages.append(StructUsage(
                            struct_name=type_name,
                            usage_type="declaration",
                            variable_name=var_name,
                            function_name=current_func,
                            file_path=file_path,
                            line=[node.start_point[0] + 1, node.end_point[0] + 1],
                            code=self._get_text(code, node)
                        ))
            except Exception:
                pass

        def _process_assignment_usage(node: Node, current_func: Optional[str]):
            """处理赋值操作中的结构体使用"""
            try:
                # 查找赋值的目标（左值）
                left_node = None
                for child in node.children:
                    if hasattr(child, 'type') and child.type == "lvalue":
                        # 在lvalue中查找object_field_access
                        for grandchild in child.children:
                            if hasattr(grandchild, 'type') and grandchild.type == "object_field_access":
                                left_node = grandchild
                                break
                        if left_node:
                            break
                
                if left_node:
                    field_expr = self._get_text(code, left_node)
                    if '.' in field_expr:
                        parts = field_expr.split('.')
                        if len(parts) >= 2:
                            struct_var = parts[0]
                            field_path = '.'.join(parts[1:])
                            
                            usages.append(StructUsage(
                                struct_name="<inferred>",
                                usage_type="assignment",
                                variable_name=struct_var,
                                function_name=current_func,
                                file_path=file_path,
                                line=[node.start_point[0] + 1, node.end_point[0] + 1],
                                code=self._get_text(code, node)
                            ))
            except Exception:
                pass

        def _process_function_call_usage(node: Node, current_func: Optional[str]):
            """处理函数调用中的结构体参数使用"""
            try:
                # 查找函数调用的参数
                for child in node.children:
                    if hasattr(child, 'type') and child.type == "argument_list":
                        for arg_child in child.children:
                            if hasattr(arg_child, 'type'):
                                if arg_child.type == "argument_expression":
                                    # 在参数表达式中查找object_field_access
                                    for grandchild in arg_child.children:
                                        if hasattr(grandchild, 'type') and grandchild.type == "object_field_access":
                                            field_expr = self._get_text(code, grandchild)
                                            if '.' in field_expr:
                                                parts = field_expr.split('.')
                                                if len(parts) >= 2:
                                                    struct_var = parts[0]
                                                    
                                                    usages.append(StructUsage(
                                                        struct_name="<inferred>",
                                                        usage_type="function_argument",
                                                        variable_name=struct_var,
                                                        function_name=current_func,
                                                        file_path=file_path,
                                                        line=[node.start_point[0] + 1, node.end_point[0] + 1],
                                                        code=self._get_text(code, node)
                                                    ))
                                                    break
            except Exception:
                pass

        walk(tree.root_node)
        return usages 