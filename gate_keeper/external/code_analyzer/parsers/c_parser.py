"""
C Parser

C代码解析器，基于tree-sitter实现
"""

import os
from typing import List, Optional, Tuple

import tree_sitter_c as tsc
from tree_sitter import Language, Node, Parser, Tree

from models.call_relation import FunctionCall
from models.function import Function, FunctionSignature, Parameter
from models.macro import Macro
from models.struct import Struct, StructField, StructRelation, StructUsage
from parsers.base import BaseParser

C_LANGUAGE = Language(tsc.language())


class CParser(BaseParser):
    """C代码解析器，基于tree-sitter实现"""

    def __init__(self):
        self.parser = Parser(C_LANGUAGE)

    def _parse_code(self, code: str) -> Tree:
        """解析代码生成语法树"""
        return self.parser.parse(code.encode("utf-8"))

    def _get_text(self, code: str, node: Node) -> str:
        """获取节点的文本内容"""
        # code 是字符串，而 start_byte 和 end_byte 是字节索引，所以需要先将字符串encode
        return code.encode("utf-8")[node.start_byte:node.end_byte].decode("utf-8")

    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        """提取函数定义"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []

        if not code:
            return []

        functions = []

        def walk(node: Node):
            if node.type == "function_definition":
                # 处理函数定义（.c / .h 中都可能有）
                declarator = node.child_by_field_name("declarator")
                if declarator:
                    identifier = declarator.child_by_field_name("declarator") or declarator.child_by_field_name("identifier")
                    name = self._get_text(code, identifier) if identifier else "<anonymous>"
                    signature = self._get_text(code, declarator)

                    # 加上前面的修饰符，例如 static inline
                    specifiers = extract_specifiers(node, code)
                    full_signature = f"{specifiers} {signature}".strip()

                    func_code = "\n".join(lines[node.start_point[0]: node.end_point[0] + 1])
                    functions.append(Function.create_simple(
                        name=name,
                        start_line=node.start_point[0] + 1,
                        end_line=node.end_point[0] + 1,
                        filepath=file_path,
                        signature=FunctionSignature(name, [], None),
                        code=func_code
                    ))

            elif node.type == "declaration":
                # 处理函数声明（一般在 .h, 也在 .c）
                type_node = node.child_by_field_name("type")
                return_type = self._get_text(code, type_node) or ""
                declarator = node.child_by_field_name("declarator")

                if declarator and declarator.type == "function_declarator":
                    identifier = declarator.child_by_field_name("declarator") or declarator.child_by_field_name("identifier")
                    name = self._get_text(code, identifier) if identifier else "<anonymous>"
                    signature = self._get_text(code, declarator)

                    specifiers = extract_specifiers(node, code)
                    full_signature = f"{specifiers} {return_type} {signature}".strip()

                    func_code = self._get_text(code, node)
                    func = Function.create_simple(
                        name=name,
                        start_line=node.start_point[0] + 1,
                        end_line=node.end_point[0] + 1,
                        filepath=file_path,
                        signature=FunctionSignature(name, [], None),
                        code=func_code
                    )
                    func.is_declaration = True
                    functions.append(func)

            for child in node.children:
                walk(child)

        def extract_specifiers(node: Node, code: str) -> str:
            """提取 static、inline 等修饰符"""
            specifiers = []
            for child in node.children:
                if child.type in ["storage_class_specifier", "type_qualifier", "type_specifier"]:
                    spec = self._get_text(code, child)
                    if spec:
                        specifiers.append(spec)
                # 特殊情况：specifiers 是包在一个 declaration_specifiers 节点里
                elif child.type == "declaration_specifiers":
                    specifiers += [self._get_text(code, c) for c in child.children 
                                 if c.type in ["storage_class_specifier", "type_qualifier", "type_specifier"]]
            return " ".join(specifiers)

        walk(tree.root_node)
        return functions

    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        """提取函数调用"""
        try:
            code, tree, _ = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []
        
        if not code:
            return []

        calls = []

        def walk(node: Node, current_func: Optional[str] = None):
            nonlocal calls

            if node.type == "function_definition":
                declarator = node.child_by_field_name("declarator")
                identifier = declarator.child_by_field_name("declarator") if declarator else None
                current_func = self._get_text(code, identifier) if identifier else None

            elif node.type == "call_expression" and current_func:
                _process_call(node, current_func)

            elif node.type == "expression_statement" and current_func:
                # 防御性判断：expression_statement 可能没有 children[0]
                if len(node.children) > 0 and node.children[0].type == "call_expression":
                    _process_call(node.children[0], current_func)

            for child in node.children:
                walk(child, current_func)

        def _process_call(call_node: Node, current_func: str):
            fn_node = call_node.child_by_field_name("function")
            callee = self._get_text(code, fn_node) if fn_node else None

            if callee and current_func:
                calls.append(FunctionCall(
                    caller=current_func,
                    callee=callee,
                    line=[call_node.start_point[0] + 1, call_node.end_point[0] + 1],
                    file_path=file_path,
                    code=self._get_text(code, call_node)
                ))

        walk(tree.root_node)
        return calls

    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        """查找父级元素"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
            root_node = tree.root_node

            def find_enclosing_function(node: Node) -> Optional[Node]:
                # 当前 node 的行号范围
                node_start = node.start_point[0] + 1
                node_end = node.end_point[0] + 1

                # 检查是否包含在给定的行号范围内
                if start_line <= node_end and end_line >= node_start:
                    if node.type == 'function_definition':
                        return node

                # 递归查找子节点
                for child in node.children:
                    found = find_enclosing_function(child)
                    if found:
                        return found
                return None

            # 在根节点开始遍历查找
            element_node = find_enclosing_function(root_node)

            if element_node and element_node.type == 'function_definition':
                # 提取函数名
                declarator = element_node.child_by_field_name("declarator")
                if declarator:
                    identifier = declarator.child_by_field_name("declarator") or declarator.child_by_field_name("identifier")
                    func_name = self._get_text(code, identifier) if identifier else "unknown_function"
                else:
                    func_name = "unknown_function"

                return Function.create_simple(
                    name=func_name,
                    start_line=element_node.start_point[0] + 1,
                    end_line=element_node.end_point[0] + 1,
                    filepath=file_path,
                    signature=FunctionSignature(func_name, [], None),
                    code=self._get_text(code, element_node)
                )
            return None

        except Exception as e:
            return None

    def extract_macros(self, file_path: str, file_content: Optional[str] = None) -> List[Macro]:
        """提取宏定义"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []

        if not code:
            return []

        macros = []

        def walk(node: Node):
            # 对象宏定义 (#define NAME value)
            if node.type == "preproc_def":
                name_node = node.child_by_field_name("name")
                value_node = node.child_by_field_name("value")
                
                if name_node:
                    name = self._get_text(code, name_node)
                    value = self._get_text(code, value_node) if value_node else None
                    macro_code = self._get_text(code, node)
                    
                    macros.append(Macro.create_object_macro(
                        name=name,
                        filepath=file_path,
                        start_line=node.start_point[0] + 1,
                        end_line=node.end_point[0] + 1,
                        value=value or "",
                        code=macro_code
                    ))

            # 函数宏定义 (#define NAME(params) value)
            elif node.type == "preproc_function_def":
                name_node = node.child_by_field_name("name")
                parameters_node = node.child_by_field_name("parameters")
                value_node = node.child_by_field_name("value")
                
                if name_node:
                    name = self._get_text(code, name_node)
                    parameters = []
                    if parameters_node:
                        for param_node in parameters_node.children:
                            if param_node.type == "identifier":
                                parameters.append(self._get_text(code, param_node))
                    
                    value = self._get_text(code, value_node) if value_node else ""
                    macro_code = self._get_text(code, node)
                    
                    macros.append(Macro.create_function_macro(
                        name=name,
                        filepath=file_path,
                        start_line=node.start_point[0] + 1,
                        end_line=node.end_point[0] + 1,
                        parameters=parameters,
                        value=value,
                        code=macro_code
                    ))

            # 条件编译宏 (#ifdef, #ifndef, #if)
            elif node.type in ["preproc_if", "preproc_ifdef", "preproc_ifndef"]:
                # 对于 preproc_ifdef，条件在第一个子节点（identifier）
                if node.type == "preproc_ifdef" and len(node.children) > 1:
                    condition_node = node.children[1]  # identifier 节点
                    name = self._get_text(code, condition_node)
                    macro_code = self._get_text(code, node)
                    
                    macros.append(Macro.create_conditional_macro(
                        name=name,
                        filepath=file_path,
                        start_line=node.start_point[0] + 1,
                        end_line=node.end_point[0] + 1,
                        code=macro_code
                    ))

            for child in node.children:
                walk(child)

        walk(tree.root_node)
        return macros 

    def extract_structs(self, file_path: str, file_content: Optional[str] = None) -> List[Struct]:
        """提取结构体定义"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []

        if not code:
            return []

        structs = []

        def walk(node: Node, in_field_declaration=False):
            # 处理结构体定义 - 直接的结构体声明
            if node.type == "struct_specifier" and not in_field_declaration:
                _process_struct_specifier(node)
            # 处理typedef结构体
            elif node.type == "type_definition":
                _process_typedef_struct(node)
                # 不递归处理typedef的子节点，避免重复处理结构体
                return
            # 标记进入字段声明
            elif node.type == "field_declaration":
                in_field_declaration = True

            for child in node.children:
                walk(child, in_field_declaration)

        def _process_struct_specifier(node: Node):
            """处理结构体声明"""
            # 查找结构体名称
            struct_name = None
            fields = []
            
            # 遍历子节点查找结构体名称和字段
            for child in node.children:
                if child.type == "type_identifier":
                    struct_name = self._get_text(code, child)
                elif child.type == "field_declaration_list":
                    fields = _extract_struct_fields(child)
            
            if struct_name:
                struct_code = self._get_text(code, node)
                structs.append(Struct.create_simple(
                    name=struct_name,
                    fields=fields,
                    start_line=node.start_point[0] + 1,
                    end_line=node.end_point[0] + 1,
                    filepath=file_path,
                    code=struct_code,
                    is_typedef=False
                ))

        def _process_typedef_struct(node: Node):
            """处理typedef结构体"""
            typedef_name = None
            struct_name = None
            fields = []
            
            # 查找typedef名称和结构体信息
            for child in node.children:
                if child.type == "type_identifier":
                    # 最后一个type_identifier是typedef名称
                    typedef_name = self._get_text(code, child)
                elif child.type == "struct_specifier":
                    # 递归处理结构体声明
                    for grandchild in child.children:
                        if grandchild.type == "type_identifier":
                            struct_name = self._get_text(code, grandchild)
                        elif grandchild.type == "field_declaration_list":
                            fields = _extract_struct_fields(grandchild)
            
            # 获取结构体代码
            struct_code = self._get_text(code, node)
            
            if typedef_name:
                # 如果没有找到结构体名称，使用typedef名称
                if not struct_name:
                    struct_name = typedef_name
                
                structs.append(Struct.create_simple(
                    name=struct_name,
                    fields=fields,
                    start_line=node.start_point[0] + 1,
                    end_line=node.end_point[0] + 1,
                    filepath=file_path,
                    code=struct_code,
                    is_typedef=True,
                    typedef_name=typedef_name
                ))

        def _extract_struct_fields(field_list_node: Node) -> List[StructField]:
            """提取结构体字段"""
            fields = []
            
            for child in field_list_node.children:
                if child.type == "field_declaration":
                    field_name = None
                    field_type = None
                    is_pointer = False
                    is_array = False
                    array_size = None
                    comment = None
                    
                    # 提取字段信息
                    for grandchild in child.children:
                        if grandchild.type in ["type_identifier", "primitive_type"]:
                            field_type = self._get_text(code, grandchild)
                        elif grandchild.type == "field_identifier":
                            field_name = self._get_text(code, grandchild)
                        elif grandchild.type == "pointer_declarator":
                            is_pointer = True
                            # 查找字段名
                            for ggchild in grandchild.children:
                                if ggchild.type == "field_identifier":
                                    field_name = self._get_text(code, ggchild)
                        elif grandchild.type == "array_declarator":
                            is_array = True
                            # 查找数组大小
                            for ggchild in grandchild.children:
                                if ggchild.type == "field_identifier":
                                    field_name = self._get_text(code, ggchild)
                                elif ggchild.type == "number_literal":
                                    array_size = self._get_text(code, ggchild)
                        elif grandchild.type == "struct_specifier":
                            # 处理结构体类型字段
                            field_type = self._get_text(code, grandchild)
                        elif grandchild.type == "comment":
                            comment = self._get_text(code, grandchild)
                    
                    if field_name and field_type:
                        fields.append(StructField(
                            name=field_name,
                            type_name=field_type,
                            is_pointer=is_pointer,
                            is_array=is_array,
                            array_size=array_size,
                            comment=comment
                        ))
            
            return fields

        walk(tree.root_node)
        return structs

    def extract_struct_relations(self, file_path: str, file_content: Optional[str] = None) -> List[StructRelation]:
        """提取结构体关系"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []

        if not code:
            return []

        relations = []
        current_struct = None

        def walk(node: Node, in_struct=False):
            nonlocal current_struct
            
            # 处理结构体定义，记录当前结构体名称
            if node.type == "struct_specifier" and not in_struct:
                # 查找结构体名称
                for child in node.children:
                    if child.type == "type_identifier":
                        current_struct = self._get_text(code, child)
                        break
                in_struct = True
            # 处理typedef结构体
            elif node.type == "type_definition":
                _process_typedef_relation(node)
                return
            
            # 处理结构体字段中的结构体类型
            if node.type == "field_declaration" and current_struct:
                _process_field_relation(node, current_struct)

            for child in node.children:
                walk(child, in_struct)

        def _process_field_relation(node: Node, source_struct: str):
            """处理字段中的结构体关系"""
            field_name = None
            field_type = None
            
            for child in node.children:
                if child.type in ["type_identifier", "primitive_type"]:
                    field_type = self._get_text(code, child)
                elif child.type == "field_identifier":
                    field_name = self._get_text(code, child)
                elif child.type == "struct_specifier":
                    field_type = self._get_text(code, child)
            
            if field_name and field_type:
                # 检查是否是结构体类型
                if field_type.startswith("struct "):
                    target_struct = field_type.replace("struct ", "")
                    relations.append(StructRelation(
                        source_struct=source_struct,
                        target_struct=target_struct,
                        relation_type="composition",
                        field_name=field_name,
                        file_path=file_path,
                        line=[node.start_point[0] + 1, node.end_point[0] + 1],
                        code=self._get_text(code, node)
                    ))

        def _process_typedef_relation(node: Node):
            """处理typedef中的结构体关系"""
            typedef_name = None
            struct_name = None
            
            for child in node.children:
                if child.type == "type_identifier":
                    # 最后一个type_identifier是typedef名称
                    typedef_name = self._get_text(code, child)
                elif child.type == "struct_specifier":
                    # 查找结构体名称
                    for grandchild in child.children:
                        if grandchild.type == "type_identifier":
                            struct_name = self._get_text(code, grandchild)
                            break
                elif child.type == "pointer_declarator":
                    # 处理指针类型，查找typedef名称
                    for grandchild in child.children:
                        if grandchild.type == "type_identifier":
                            typedef_name = self._get_text(code, grandchild)
                            break
            
            if typedef_name and struct_name:
                relations.append(StructRelation(
                    source_struct=typedef_name,
                    target_struct=struct_name,
                    relation_type="typedef",
                    field_name=None,
                    file_path=file_path,
                    line=[node.start_point[0] + 1, node.end_point[0] + 1],
                    code=self._get_text(code, node)
                ))

        walk(tree.root_node)
        return relations

    def extract_struct_usages(self, file_path: str, file_content: Optional[str] = None) -> List[StructUsage]:
        """提取结构体使用"""
        try:
            code, tree, lines = self._get_code_and_tree(file_path, file_content)
        except Exception as e:
            return []

        if not code:
            return []

        usages = []

        def walk(node: Node, current_func: Optional[str] = None):
            # 处理函数定义，记录当前函数名
            if node.type == "function_definition":
                declarator = node.child_by_field_name("declarator")
                identifier = declarator.child_by_field_name("declarator") if declarator else None
                current_func = self._get_text(code, identifier) if identifier else None

            # 处理变量声明
            elif node.type == "declaration":
                _process_declaration_usage(node, current_func)
            
            # 处理函数参数
            elif node.type == "parameter_declaration":
                _process_parameter_usage(node, current_func)
            
            # 处理字段访问
            elif node.type == "field_expression":
                _process_field_access_usage(node, current_func)
            
            # 处理sizeof操作
            elif node.type == "sizeof_expression":
                _process_sizeof_usage(node, current_func)

            for child in node.children:
                walk(child, current_func)

        def _process_declaration_usage(node: Node, current_func: Optional[str]):
            """处理变量声明中的结构体使用"""
            # 查找类型和声明符
            type_name = None
            var_name = None
            
            for child in node.children:
                if child.type in ["primitive_type", "type_identifier"]:
                    type_name = self._get_text(code, child)
                elif child.type == "struct_specifier":
                    type_name = self._get_text(code, child)
                elif child.type == "init_declarator":
                    # 查找变量名
                    for grandchild in child.children:
                        if grandchild.type == "identifier":
                            var_name = self._get_text(code, grandchild)
                            break
            
            if var_name and type_name:
                # 检查是否是结构体类型
                if type_name.startswith("struct "):
                    struct_name = type_name.replace("struct ", "")
                    usages.append(StructUsage(
                        struct_name=struct_name,
                        usage_type="declaration",
                        variable_name=var_name,
                        function_name=current_func,
                        file_path=file_path,
                        line=[node.start_point[0] + 1, node.end_point[0] + 1],
                        code=self._get_text(code, node)
                    ))

        def _process_parameter_usage(node: Node, current_func: Optional[str]):
            """处理函数参数中的结构体使用"""
            type_node = node.child_by_field_name("type")
            declarator = node.child_by_field_name("declarator")
            
            if type_node and declarator:
                type_name = self._get_text(code, type_node)
                param_name = None
                
                if declarator.type == "identifier":
                    param_name = self._get_text(code, declarator)
                elif declarator.type == "pointer_declarator":
                    for child in declarator.children:
                        if child.type == "identifier":
                            param_name = self._get_text(code, child)
                            break
                
                if param_name and type_name.startswith("struct "):
                    struct_name = type_name.replace("struct ", "")
                    usages.append(StructUsage(
                        struct_name=struct_name,
                        usage_type="parameter",
                        variable_name=param_name,
                        function_name=current_func,
                        file_path=file_path,
                        line=[node.start_point[0] + 1, node.end_point[0] + 1],
                        code=self._get_text(code, node)
                    ))

        def _process_field_access_usage(node: Node, current_func: Optional[str]):
            """处理字段访问中的结构体使用"""
            field_node = node.child_by_field_name("field")
            if field_node:
                field_name = self._get_text(code, field_node)
                usages.append(StructUsage(
                    struct_name="<unknown>",  # 需要从上下文推断
                    usage_type="field_access",
                    variable_name=None,
                    function_name=current_func,
                    file_path=file_path,
                    line=[node.start_point[0] + 1, node.end_point[0] + 1],
                    code=self._get_text(code, node)
                ))

        def _process_sizeof_usage(node: Node, current_func: Optional[str]):
            """处理sizeof操作中的结构体使用"""
            # 查找类型描述符
            for child in node.children:
                if child.type == "type_descriptor":
                    for grandchild in child.children:
                        if grandchild.type == "struct_specifier":
                            struct_name = self._get_text(code, grandchild)
                            if struct_name.startswith("struct "):
                                struct_name = struct_name.replace("struct ", "")
                                usages.append(StructUsage(
                                    struct_name=struct_name,
                                    usage_type="sizeof",
                                    variable_name=None,
                                    function_name=current_func,
                                    file_path=file_path,
                                    line=[node.start_point[0] + 1, node.end_point[0] + 1],
                                    code=self._get_text(code, node)
                                ))
                                break

        walk(tree.root_node)
        return usages 