# Code Analyzer 文档中心

欢迎来到 Code Analyzer 项目的文档中心！这里包含了项目的完整文档，帮助您快速了解和使用我们的代码分析工具。

## 📚 文档导航

### 🏗️ 设计文档 (`design/`)
- **[增强SFT数据设计规范 (v2)](design/ENHANCED_SFT_DATA_DESIGN.md)** - 详细的SFT数据生成设计文档，包含v2版本的所有新功能

### 📖 使用指南 (`guides/`)
- **[SFT数据生成使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)** - 快速开始和高级用法指南
- **[安装指南](installation.md)** - 详细的安装和配置说明
- **[API文档](api/)** - 完整的API参考文档

### 📋 任务文档 (`task/`)
- **[SFT数据生成总结](task/SFT_DATA_GENERATION_SUMMARY.md)** - 数据生成系统的完整总结和实现细节

### 🔧 技术文档
- **[解析器文档](parsers/)** - 各种语言解析器的详细说明
- **[示例文档](examples/)** - 代码示例和使用案例
- **[结构体功能总结](STRUCT_FEATURE_SUMMARY.md)** - 结构体分析功能详解

## 🚀 快速开始

### 1. 安装项目
```bash
pip install -e .
```

### 2. 基本使用
```python
from examples.np_real_analysis import RealNPAnalyzer

# 初始化分析器
analyzer = RealNPAnalyzer("examples/np")

# 分析项目并生成SFT数据
analyzer.analyze_project()
enhanced_data = analyzer.generate_enhanced_sft_data(max_samples=50)

# 保存结果
analyzer.save_real_results("output")
```

### 3. 查看结果
生成的数据文件位于 `real_analysis_output/` 目录：
- `enhanced_sft_data.json` - 增强SFT训练数据
- `real_functions.json` - 函数分析结果
- `real_code_graph.json` - 代码图信息

## 🎯 主要功能

### 代码分析
- **Tree-sitter解析**: 支持多种编程语言的语法解析
- **代码图构建**: 自动构建函数调用图和依赖关系图
- **结构体分析**: 深入分析结构体定义和使用

### SFT数据生成
- **瀑布式遮盖**: 模拟程序员逐行编写代码
- **随机遮盖**: 模拟代码编辑和修改行为
- **控制流检测**: 自动识别控制流语句
- **注释处理**: 智能处理各种注释类型
- **依赖图构建**: 构建显式代码依赖关系图

### 数据质量保证
- **质量验证**: 自动验证生成数据的质量
- **多样性保证**: 确保数据的多样性和平衡性
- **实用性验证**: 基于真实项目代码生成

## 📊 项目结构

```
code_analyzer/
├── core/                 # 核心分析模块
├── models/              # 数据模型定义
├── parsers/             # 语言解析器
├── examples/            # 使用示例
├── tests/               # 测试文件
├── docs/                # 文档目录
│   ├── design/          # 设计文档
│   ├── guides/          # 使用指南
│   ├── task/            # 任务文档
│   ├── api/             # API文档
│   └── parsers/         # 解析器文档
└── real_analysis_output/ # 分析结果输出
```

## 🔍 详细文档

### 设计文档
- **[增强SFT数据设计规范 (v2)](design/ENHANCED_SFT_DATA_DESIGN.md)**
  - 数据模型设计
  - 遮盖策略实现
  - 质量控制机制
  - 性能优化方案

### 使用指南
- **[SFT数据生成使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)**
  - 快速开始
  - 参数配置
  - 高级用法
  - 常见问题

### 任务总结
- **[SFT数据生成总结](task/SFT_DATA_GENERATION_SUMMARY.md)**
  - 系统概述
  - 实现细节
  - 运行结果
  - 质量验证

## 🤝 贡献指南

如果您想为项目做出贡献，请：

1. 阅读相关设计文档
2. 查看现有代码示例
3. 编写测试用例
4. 更新相关文档

## 📞 支持

如果您在使用过程中遇到问题：

1. 查看 [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)
2. 检查 [常见问题](guides/SFT_DATA_GENERATION_GUIDE.md#常见问题)
3. 查看 [API文档](api/) 获取详细信息

## 📝 更新日志

### v2.0.0 (最新)
- ✅ 新增控制流检测功能
- ✅ 新增注释提示检测功能
- ✅ 新增显式依赖图构建
- ✅ 完善数据质量验证机制
- ✅ 优化文档结构和内容

### v1.0.0
- ✅ 基础代码分析功能
- ✅ Tree-sitter解析支持
- ✅ 基础SFT数据生成
- ✅ 代码图构建功能

---

**最后更新**: 2024年12月
**文档版本**: v2.0.0