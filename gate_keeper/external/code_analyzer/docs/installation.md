# 安装指南

本指南将帮助您安装和配置 Code Analyzer。

## 系统要求

### 基本要求

- **Python**: 3.8 或更高版本
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议 4GB 或更多
- **磁盘空间**: 至少 500MB 可用空间

### 可选依赖

- **Git**: 用于仓库分析功能
- **tree-sitter**: 用于语言解析（会自动安装）

## 安装方法

### 方法一：从源码安装（推荐）

1. **克隆仓库**
```bash
git clone <repository-url>
cd code_analyzer
```

2. **创建虚拟环境**（推荐）
```bash
# 使用 venv
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **安装项目**
```bash
pip install -e .
```

### 方法二：使用 pip 安装

```bash
pip install code-analyzer
```

## 语言解析器安装

### Python 解析器

Python 解析器会自动安装，无需额外配置。

### C 解析器

C 解析器会自动安装，无需额外配置。

### NP 解析器

NP 解析器需要手动安装 tree-sitter-np：

```bash
# 进入 tree-sitter-np 目录
cd /path/to/tree-sitter-np

# 安装 Python 绑定
pip install -e .
```

## 验证安装

### 1. 检查基本安装

```python
from code_analyzer import ASTParserFactory

# 测试 Python 解析器
try:
    parser = ASTParserFactory.create("python")
    print("✓ Python 解析器安装成功")
except Exception as e:
    print(f"✗ Python 解析器安装失败: {e}")

# 测试 C 解析器
try:
    parser = ASTParserFactory.create("c")
    print("✓ C 解析器安装成功")
except Exception as e:
    print(f"✗ C 解析器安装失败: {e}")

# 测试 NP 解析器
try:
    parser = ASTParserFactory.create("np")
    print("✓ NP 解析器安装成功")
except Exception as e:
    print(f"✗ NP 解析器安装失败: {e}")
```

### 2. 运行测试

```bash
# 运行所有测试
python -m pytest

# 运行特定测试
python -m pytest tests/test_basic.py -v
```

### 3. 命令行测试

```bash
# 测试 CLI 工具
python -m cli.main --help

# 测试文件分析
python -m cli.main file examples/basic_usage.py --language python
```

## 配置

### 环境变量

可以设置以下环境变量来自定义行为：

```bash
# 设置缓存目录
export CODE_ANALYZER_CACHE_DIR="/path/to/cache"

# 设置日志级别
export CODE_ANALYZER_LOG_LEVEL="INFO"

# 设置最大缓存大小（MB）
export CODE_ANALYZER_MAX_CACHE_SIZE="1000"
```

### 配置文件

创建 `~/.code_analyzer/config.yaml` 文件：

```yaml
# 缓存配置
cache:
  directory: ~/.code_analyzer/cache
  max_size_mb: 1000
  max_repos: 10

# 日志配置
logging:
  level: INFO
  file: ~/.code_analyzer/logs/analyzer.log

# 解析器配置
parsers:
  python:
    enabled: true
  c:
    enabled: true
  np:
    enabled: true
    tree_sitter_path: /path/to/tree-sitter-np
```

## 故障排除

### 常见问题

#### 1. 导入错误

**问题**: `ModuleNotFoundError: No module named 'code_analyzer'`

**解决方案**:
```bash
# 确保在正确的目录
cd code_analyzer

# 重新安装
pip install -e .

# 检查 Python 路径
python -c "import sys; print(sys.path)"
```

#### 2. tree-sitter 错误

**问题**: `tree_sitter.Language` 相关错误

**解决方案**:
```bash
# 重新安装 tree-sitter
pip uninstall tree-sitter
pip install tree-sitter

# 对于 NP 解析器，确保 tree-sitter-np 已安装
cd /path/to/tree-sitter-np
pip install -e .
```

#### 3. 权限错误

**问题**: 文件或目录权限错误

**解决方案**:
```bash
# 检查权限
ls -la

# 修改权限
chmod +x setup.py
chmod -R 755 .

# 使用 sudo（如果需要）
sudo pip install -e .
```

#### 4. 依赖冲突

**问题**: 依赖包版本冲突

**解决方案**:
```bash
# 创建新的虚拟环境
python -m venv fresh_env
source fresh_env/bin/activate  # 或 fresh_env\Scripts\activate

# 重新安装
pip install -r requirements.txt
pip install -e .
```

### 调试模式

启用调试模式获取更多信息：

```bash
# 设置调试环境变量
export CODE_ANALYZER_DEBUG=1
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 运行测试
python -m pytest -v --tb=long
```

## 性能优化

### 1. 缓存配置

```python
from code_analyzer import RepositoryIndexFactory

# 使用缓存目录
repo_index = RepositoryIndexFactory.get_or_build(
    "path/to/repo",
    cache_dir="/path/to/cache"
)
```

### 2. 内存优化

```python
# 清理缓存
from code_analyzer import ASTParserFactory
ASTParserFactory.clear_cache()

# 限制缓存大小
import os
os.environ['CODE_ANALYZER_MAX_CACHE_SIZE'] = '500'
```

### 3. 并行处理

```python
import concurrent.futures
from code_analyzer import ASTParserFactory

def analyze_file(file_path):
    parser = ASTParserFactory.create("python")
    return parser.extract_functions(file_path)

# 并行处理多个文件
with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
    results = list(executor.map(analyze_file, file_list))
```

## 卸载

### 完全卸载

```bash
# 卸载包
pip uninstall code-analyzer

# 删除缓存
rm -rf ~/.code_analyzer

# 删除虚拟环境（如果使用）
rm -rf venv
```

### 部分卸载

```bash
# 只卸载特定解析器
pip uninstall tree-sitter-np  # 卸载 NP 解析器

# 清理缓存
rm -rf ~/.code_analyzer/cache
```

## 更新

### 更新 Code Analyzer

```bash
# 拉取最新代码
git pull origin main

# 重新安装
pip install -e . --upgrade
```

### 更新依赖

```bash
# 更新所有依赖
pip install -r requirements.txt --upgrade

# 更新特定依赖
pip install tree-sitter --upgrade
```

## 支持

如果遇到安装问题：

1. 查看 [常见问题](faq.md)
2. 检查 [Issues](../../issues)
3. 创建新的 Issue 并包含以下信息：
   - 操作系统和版本
   - Python 版本
   - 错误信息
   - 安装步骤

---

**安装完成！** 现在您可以开始使用 Code Analyzer 了。查看 [快速开始](../README.md) 了解如何使用。 