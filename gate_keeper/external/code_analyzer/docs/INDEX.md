# Code Analyzer 文档索引

## 📚 文档分类导航

### 🏗️ 设计文档 (`design/`)
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [增强SFT数据设计规范 (v2)](design/ENHANCED_SFT_DATA_DESIGN.md) | 详细的SFT数据生成设计文档，包含v2版本的所有新功能 | 架构师、开发者 |
| [结构体功能总结](STRUCT_FEATURE_SUMMARY.md) | 结构体分析功能的详细说明 | 开发者 |

### 📖 使用指南 (`guides/`)
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [SFT数据生成使用指南](guides/SFT_DATA_GENERATION_GUIDE.md) | 快速开始和高级用法指南 | 用户、开发者 |
| [安装指南](installation.md) | 详细的安装和配置说明 | 新用户 |
| [API文档](api/) | 完整的API参考文档 | 开发者 |

### 📋 任务文档 (`task/`)
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [SFT数据生成总结](task/SFT_DATA_GENERATION_SUMMARY.md) | 数据生成系统的完整总结和实现细节 | 开发者、维护者 |

### 🔧 技术文档
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [解析器文档](parsers/) | 各种语言解析器的详细说明 | 开发者 |
| [示例文档](examples/) | 代码示例和使用案例 | 用户、开发者 |

## 🎯 按使用场景导航

### 🚀 新用户入门
1. [安装指南](installation.md) - 安装和配置
2. [SFT数据生成使用指南](guides/SFT_DATA_GENERATION_GUIDE.md) - 快速开始
3. [示例文档](examples/) - 查看使用案例

### 🔧 开发者参考
1. [增强SFT数据设计规范 (v2)](design/ENHANCED_SFT_DATA_DESIGN.md) - 系统设计
2. [API文档](api/) - API参考
3. [解析器文档](parsers/) - 解析器实现

### 📊 项目维护
1. [SFT数据生成总结](task/SFT_DATA_GENERATION_SUMMARY.md) - 实现总结
2. [结构体功能总结](STRUCT_FEATURE_SUMMARY.md) - 功能说明
3. [文档总结](DOCUMENTATION_SUMMARY.md) - 文档概览

## 🔍 按功能模块导航

### 核心功能
- **代码分析**: [API文档](api/) → [解析器文档](parsers/)
- **SFT数据生成**: [设计规范](design/ENHANCED_SFT_DATA_DESIGN.md) → [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)
- **结构体分析**: [结构体功能总结](STRUCT_FEATURE_SUMMARY.md)

### 技术实现
- **Tree-sitter解析**: [解析器文档](parsers/)
- **代码图构建**: [API文档](api/) → [设计规范](design/ENHANCED_SFT_DATA_DESIGN.md)
- **质量控制**: [设计规范](design/ENHANCED_SFT_DATA_DESIGN.md) → [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)

## 📋 按语言支持导航

### 支持的编程语言
| 语言 | 解析器文档 | 示例 | 状态 |
|------|------------|------|------|
| Python | [Python解析器](parsers/python_parser.md) | [Python示例](examples/python_examples.md) | ✅ 完整支持 |
| C | [C解析器](parsers/c_parser.md) | [C示例](examples/c_examples.md) | ✅ 完整支持 |
| NP | [NP解析器](parsers/NP_PARSER_README.md) | [NP示例](examples/np_examples.md) | ✅ 完整支持 |

## 🎯 按版本导航

### v2.0.0 (最新版本)
- [增强SFT数据设计规范 (v2)](design/ENHANCED_SFT_DATA_DESIGN.md) - 新功能说明
- [SFT数据生成使用指南](guides/SFT_DATA_GENERATION_GUIDE.md) - v2功能使用
- [SFT数据生成总结](task/SFT_DATA_GENERATION_SUMMARY.md) - v2实现总结

### v1.0.0 (基础版本)
- [安装指南](installation.md) - 基础安装
- [API文档](api/) - 基础API
- [解析器文档](parsers/) - 基础解析器

## 🔧 快速查找

### 常用链接
- **[项目主页](../../)** - 项目概览和快速开始
- **[文档中心](README.md)** - 文档导航中心
- **[API参考](api/)** - 完整API文档
- **[示例代码](examples/)** - 使用示例

### 搜索关键词
- **安装**: [安装指南](installation.md)
- **快速开始**: [SFT数据生成使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)
- **API**: [API文档](api/)
- **设计**: [增强SFT数据设计规范 (v2)](design/ENHANCED_SFT_DATA_DESIGN.md)
- **解析器**: [解析器文档](parsers/)
- **示例**: [示例文档](examples/)
- **问题**: [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md#常见问题)

## 📞 获取帮助

### 文档支持
1. 查看 [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)
2. 检查 [常见问题](guides/SFT_DATA_GENERATION_GUIDE.md#常见问题)
3. 搜索 [API文档](api/)

### 社区支持
1. 在 [GitHub Issues](../../issues) 中搜索问题
2. 创建新的 Issue 报告问题
3. 参与项目讨论和贡献

---

**文档索引** - 快速找到您需要的文档 📚

**最后更新**: 2024年12月 | **索引版本**: v2.0.0 