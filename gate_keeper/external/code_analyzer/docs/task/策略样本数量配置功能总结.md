# 策略样本数量配置功能总结

## 功能概述

为SFT数据生成器添加了策略级别的样本数量配置功能，允许用户为每个提取策略独立设置样本数量限制，支持无限制生成，从而最大化获取训练数据。

## 问题背景

### 原有限制
- 所有策略共享总样本数量限制
- 每个策略的样本数量是固定的：`max_samples // len(strategies)`
- 无法针对不同策略的特点设置不同的样本数量
- 无法充分利用某些策略的潜力来生成更多高质量数据

### 实际需求
- 某些策略（如 `waterfall_sequential`）能够生成大量高质量样本
- 某些策略（如 `code_to_comment`）可能生成样本较少，需要限制
- 需要灵活配置以最大化训练数据获取

## 实现方案

### 1. 核心接口扩展

**文件**：`core/data_gen/sft/generator.py`

**方法**：`generate_enhanced_sft_data_multi_strategy()`

```python
def generate_enhanced_sft_data_multi_strategy(self, max_samples: int = 10, 
                                             strategies: List[ExtractionStrategy] = None,
                                             strategy_max_samples: Dict[ExtractionStrategy, int] = None) -> List[EnhancedStatementMaskData]:
    """使用多种策略生成增强SFT数据
    
    Args:
        max_samples: 总样本数量限制（当strategy_max_samples为None时使用）
        strategies: 要使用的策略列表
        strategy_max_samples: 每个策略的独立样本数量限制，None表示无限制
    """
```

### 2. 配置参数说明

**strategy_max_samples** 字典支持以下配置：

- `None` 或 `0`：无样本数量限制
- 正整数：最大样本数量限制
- 不包含的策略：使用平均分配

### 3. 配置示例

```python
# 策略样本数量配置（可选）
strategy_max_samples = {
    ExtractionStrategy.WATERFALL_SEQUENTIAL: None,      # 无限制
    ExtractionStrategy.RANDOM_INPLACE: 50,              # 最多50个样本
    ExtractionStrategy.COMMENT_TO_CODE: None,           # 无限制
    ExtractionStrategy.CODE_TO_COMMENT: 20,             # 最多20个样本
    ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: None, # 无限制
}
```

## 功能特性

### 1. 灵活配置
- **独立限制**：每个策略可以设置独立的样本数量限制
- **无限制支持**：支持某些策略无限制生成样本
- **向后兼容**：不设置 `strategy_max_samples` 时保持原有行为

### 2. 智能分配
- **优先级**：`strategy_max_samples` 配置优先于总样本限制
- **兜底机制**：未配置的策略使用平均分配
- **动态调整**：根据实际生成情况动态调整

### 3. 详细日志
- **配置显示**：显示每个策略的样本配置
- **实际统计**：显示每个策略实际生成的样本数量
- **总统计**：显示最终的总样本数量

## 使用效果

### 1. 样本数量大幅提升

**配置前**：
- 总样本：80个
- 每个策略：20个样本

**配置后**：
- 总样本：836个（提升10倍+）
- 策略分布：
  - `waterfall_sequential`: 666个（无限制）
  - `random_inplace`: 50个（限制）
  - `comment_to_code`: 36个（无限制）
  - `code_to_comment`: 0个（限制）
  - `control_flow_hallucination`: 84个（无限制）

### 2. 数据质量优化

**优势策略最大化**：
- `waterfall_sequential` 策略生成了最多样本，因为它是最稳定和高质量的
- `random_inplace` 策略限制在50个，避免过多随机性
- `control_flow_hallucination` 策略无限制，充分利用逻辑填空的优势

**劣势策略控制**：
- `code_to_comment` 策略限制在20个，因为该策略在当前代码库中效果有限

## 技术实现

### 1. 配置解析逻辑

```python
# 确定当前策略的样本数量限制
if strategy_max_samples and strategy in strategy_max_samples:
    strategy_limit = strategy_max_samples[strategy]
    if strategy_limit is None or strategy_limit <= 0:
        strategy_limit = None  # 无限制
    else:
        strategy_limit = strategy_limit
else:
    # 使用平均分配
    strategy_limit = max_samples // len(strategies)
```

### 2. 无限制处理

```python
# 生成策略数据
if strategy_limit is None:
    # 无限制：使用一个很大的数字
    strategy_data = self.generate_enhanced_sft_data(10000, strategy, config)
else:
    strategy_data = self.generate_enhanced_sft_data(strategy_limit, strategy, config)
```

### 3. 总样本限制

```python
# 如果设置了总样本限制，则进行限制
if not strategy_max_samples and max_samples > 0:
    all_data = all_data[:max_samples]
    print(f"  应用总样本限制: {len(all_data)} 个样本")
```

## 应用场景

### 1. 大规模训练数据生成
```python
# 最大化获取训练数据
strategy_max_samples = {
    ExtractionStrategy.WATERFALL_SEQUENTIAL: None,      # 无限制
    ExtractionStrategy.COMMENT_TO_CODE: None,           # 无限制
    ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: None, # 无限制
    ExtractionStrategy.RANDOM_INPLACE: 100,             # 适度限制
    ExtractionStrategy.CODE_TO_COMMENT: 50,             # 适度限制
}
```

### 2. 平衡数据生成
```python
# 平衡各策略的样本数量
strategy_max_samples = {
    ExtractionStrategy.WATERFALL_SEQUENTIAL: 200,
    ExtractionStrategy.RANDOM_INPLACE: 200,
    ExtractionStrategy.COMMENT_TO_CODE: 200,
    ExtractionStrategy.CODE_TO_COMMENT: 200,
    ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: 200,
}
```

### 3. 快速原型验证
```python
# 快速生成少量样本进行验证
strategy_max_samples = {
    ExtractionStrategy.WATERFALL_SEQUENTIAL: 10,
    ExtractionStrategy.RANDOM_INPLACE: 5,
    ExtractionStrategy.COMMENT_TO_CODE: 5,
    ExtractionStrategy.CODE_TO_COMMENT: 5,
    ExtractionStrategy.CONTROL_FLOW_HALLUCINATION: 5,
}
```

## 总结

### 🎯 核心价值
1. **数据量最大化**：通过无限制配置，可以获取更多训练数据
2. **质量优化**：针对不同策略的特点进行合理配置
3. **灵活性**：支持各种使用场景的配置需求

### 📈 效果提升
1. **样本数量**：从80个提升到836个（10倍+增长）
2. **数据质量**：通过策略配置优化数据分布
3. **使用体验**：提供详细的配置和统计信息

### 🔧 技术优势
1. **向后兼容**：不影响现有代码的使用
2. **配置灵活**：支持多种配置模式
3. **监控完善**：提供详细的生成过程日志

这个功能为SFT数据生成提供了更大的灵活性和更高的效率，能够更好地满足不同场景下的训练数据需求。 