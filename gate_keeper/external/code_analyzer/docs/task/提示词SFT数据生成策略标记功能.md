# 提示词SFT数据生成策略标记功能

## 功能概述

为提示词SFT训练数据添加生成策略标记功能，确保每个生成的样本都包含其使用的生成策略信息，便于后续分析和评估不同策略的效果。

## 实现内容

### 1. 数据模型扩展

**文件**：`core/data_gen/sft/prompt_sft/models.py`

**修改内容**：
- 在 `PromptSFTData` 类中添加了 `generation_strategy` 字段
- 更新了 `to_dict()` 方法以包含生成策略信息

```python
@dataclass
class PromptSFTData:
    # ... 其他字段 ...
    
    # 生成策略信息
    generation_strategy: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            # ... 其他字段 ...
            "generation_strategy": self.generation_strategy,
            # ... 其他字段 ...
        }
```

### 2. 生成器逻辑更新

**文件**：`core/data_gen/sft/prompt_sft/generator.py`

**修改内容**：
- 在 `generate_prompt_sft_data()` 方法中，从 `sft_item.metadata` 提取策略信息
- 更新了 `get_statistics()` 方法以统计不同生成策略的使用情况

```python
# 创建PromptSFTData时包含策略信息
prompt_sft_item = PromptSFTData(
    # ... 其他字段 ...
    generation_strategy=sft_item.metadata.get('strategy', ''),
    # ... 其他字段 ...
)

# 统计信息中包含策略统计
stats = {
    # ... 其他字段 ...
    "generation_strategies": {},
    # ... 其他字段 ...
}

# 统计生成策略
strategy = item.generation_strategy
if strategy:
    stats["generation_strategies"][strategy] = stats["generation_strategies"].get(strategy, 0) + 1
```

### 3. 示例脚本更新

**文件**：`examples/prompt_sft_example.py`

**修改内容**：
- 在显示示例数据时包含生成策略信息

```python
print(f"生成策略: {sample.generation_strategy}")
```

## 功能验证

### 1. 数据生成验证

运行脚本后，生成的样本正确包含生成策略信息：

```json
{
  "prompt": "...",
  "completion": "if (cc0.z) {",
  "filepath": "/path/to/file.asm",
  "function_name": "PAE_INFO_QOS_CONFIG_DEFAULT_BE",
  "line_number": 0,
  "model_type": "deepseek_coder",
  "generation_strategy": "waterfall_sequential",
  "complexity_score": 0.160,
  "context_relevance_score": 0.018
}
```

### 2. 统计信息验证

统计文件正确包含生成策略分布：

```json
{
  "total_samples": 100,
  "model_types": {
    "deepseek_coder": 100
  },
  "template_names": {
    "code_completion": 100
  },
  "generation_strategies": {
    "waterfall_sequential": 100
  },
  "avg_prompt_length": 2344.66,
  "avg_completion_length": 35.88,
  "avg_complexity_score": 0.375,
  "avg_context_relevance_score": 0.02285064935064935
}
```

### 3. 示例输出验证

脚本运行时正确显示生成策略信息：

```
=== deepseek_coder 示例 ===
文件: /path/to/file.asm
函数: PAE_INFO_QOS_CONFIG_DEFAULT_BE
行号: 0
模板: code_completion
生成策略: waterfall_sequential
复杂度分数: 0.160
上下文相关性: 0.018
```

## 支持策略类型

当前支持的生成策略包括：

1. **waterfall_sequential** - 瀑布式顺序遮盖
2. **random_inplace** - 随机插入/替换
3. **comment_to_code** - 注释驱动补全
4. **code_to_comment** - 注释生成
5. **control_flow_hallucination** - 逻辑填空

## 数据流程

1. **策略生成**：各种提取策略生成增强SFT数据时，在metadata中标记策略名称
2. **数据传递**：策略信息通过 `sft_item.metadata.get('strategy', '')` 传递
3. **数据存储**：策略信息存储在 `PromptSFTData.generation_strategy` 字段中
4. **统计分析**：统计信息中包含各策略的使用分布
5. **数据导出**：JSON文件中包含每个样本的生成策略信息

## 应用价值

1. **策略效果分析**：可以分析不同生成策略产生的数据质量差异
2. **数据平衡性**：确保不同策略生成的数据分布均衡
3. **模型训练优化**：根据策略效果调整训练数据配比
4. **可追溯性**：每个训练样本都可以追溯到其生成策略
5. **质量控制**：针对特定策略的数据进行质量评估

## 后续扩展

1. **策略参数记录**：除了策略名称，还可以记录策略的具体参数
2. **策略组合标记**：支持多种策略组合使用的标记
3. **策略效果评估**：基于策略标记进行效果对比分析
4. **动态策略选择**：根据数据质量动态选择最优策略 