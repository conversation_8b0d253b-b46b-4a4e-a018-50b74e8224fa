# 数据分文件保存功能实现总结

## 功能概述

实现了通用的数据保存工具，支持按条数分文件存储，避免单文件过大导致的性能问题。该功能与数据内容无关，是一个通用的工具。

## 主要特性

### 1. 分文件存储
- **默认配置**：每文件最多10,000条记录
- **可配置**：通过参数可以调整每文件的记录数
- **自动分割**：当数据量超过限制时自动创建新文件
- **文件命名**：自动生成带序号的文件名（如 `data_part001.json`）

### 2. 多种格式支持
- **JSON格式**：传统的数组格式，适合小数据集
- **JSONL格式**：每行一个JSON，适合大数据集，支持流式处理

### 3. 流式保存
- **内存友好**：支持逐条保存，避免将所有数据加载到内存
- **上下文管理**：支持 `with` 语句，自动管理文件资源
- **实时保存**：生成一条保存一条，适合大规模数据处理

## 实现架构

### 1. 核心类

#### `DataSaver` - 通用数据保存器
```python
class DataSaver:
    def __init__(self, max_items_per_file=10000, output_dir=None, 
                 filename_prefix="data", filename_suffix="json")
    
    def save_data(self, data, to_dict_func=None, show_progress=True)
    def save_jsonl_data(self, data, to_dict_func=None, show_progress=True)
    def get_file_info(self, data)
```

#### `SFTDataSaver` - SFT数据专用保存器
```python
class SFTDataSaver(DataSaver):
    def save_sft_data(self, data, show_progress=True)
    def save_alpaca_data(self, data, alpaca_format_only=False, show_progress=True)
    def save_jsonl_alpaca_data(self, data, alpaca_format_only=False, show_progress=True)
```

#### `StreamingDataSaver` - 流式数据保存器
```python
class StreamingDataSaver:
    def __init__(self, output_file, max_items_per_file=10000, file_format="jsonl")
    
    def save_item(self, item, to_dict_func=None)
    def save_items(self, items, to_dict_func=None)
    def close()
    
    def __enter__(self)
    def __exit__(self, exc_type, exc_val, exc_tb)
```

#### `StreamingSFTDataSaver` - SFT数据流式保存器
```python
class StreamingSFTDataSaver(StreamingDataSaver):
    def save_sft_item(self, item)
    def save_alpaca_item(self, item, alpaca_format_only=False)
```

### 2. 集成到现有生成器

#### Prompt SFT生成器
```python
class PromptSFTGenerator:
    def save_to_json(self, prompt_sft_data, output_file, max_items_per_file=10000)
    def save_to_jsonl(self, prompt_sft_data, output_file, max_items_per_file=10000)
    def create_streaming_saver(self, output_file, max_items_per_file=10000, file_format="jsonl")
```

#### Alpaca SFT生成器
```python
class AlpacaSFTGenerator:
    def save_to_json(self, alpaca_data, output_file, alpaca_format_only=False, max_items_per_file=10000)
    def save_to_jsonl(self, alpaca_data, output_file, alpaca_format_only=False, max_items_per_file=10000)
    def create_streaming_saver(self, output_file, max_items_per_file=10000, file_format="jsonl")
```

## 使用示例

### 1. 批量保存（传统方式）

```python
# 生成数据
prompt_data = prompt_generator.generate_prompt_sft_data(max_samples=1000)

# 分文件保存
saved_files = prompt_generator.save_to_json(
    prompt_data, 
    "output/prompt_sft_data.json",
    max_items_per_file=5000  # 每文件5000条
)

print(f"数据已保存到 {len(saved_files)} 个文件")
```

### 2. 流式保存（内存友好）

```python
# 创建流式保存器
with prompt_generator.create_streaming_saver(
    "output/streaming_data.jsonl",
    max_items_per_file=1000,
    file_format="jsonl"
) as saver:
    
    # 逐条生成和保存
    for i in range(10000):
        sample = generate_single_sample()
        saver.save_sft_item(sample)
```

### 3. JSONL格式保存

```python
# 保存为JSONL格式（每行一个JSON）
saved_files = alpaca_generator.save_to_jsonl(
    alpaca_data,
    "output/alpaca_data.jsonl",
    alpaca_format_only=True,
    max_items_per_file=2000
)
```

## 文件命名规则

### 单文件模式
- 输出：`data.json` 或 `data.jsonl`

### 分文件模式
- 输出：`data_part001.json`, `data_part002.json`, ...
- 或：`data_part001.jsonl`, `data_part002.jsonl`, ...

## 性能优势

### 1. 内存使用优化
- **传统方式**：所有数据加载到内存，然后一次性保存
- **流式方式**：逐条处理，内存占用恒定

### 2. 文件大小控制
- **避免大文件**：单文件不会过大，便于传输和处理
- **并行处理**：多个小文件可以并行处理

### 3. 容错性
- **部分失败**：某个文件损坏不影响其他文件
- **断点续传**：可以从中断点继续处理

## 配置参数

### 通用参数
- `max_items_per_file`: 每文件最大条数（默认10000）
- `output_dir`: 输出目录
- `filename_prefix`: 文件名前缀
- `filename_suffix`: 文件扩展名

### 格式参数
- `file_format`: 文件格式（"json" 或 "jsonl"）
- `alpaca_format_only`: 是否只保存标准Alpaca格式

### 显示参数
- `show_progress`: 是否显示进度条

## 统计信息

保存完成后会显示：
- 数据总量
- 分文件数
- 每文件条数
- 保存的文件路径列表

## 兼容性

### 向后兼容
- 现有的保存方法仍然可用
- 新增的参数都有默认值
- 不会破坏现有代码

### 扩展性
- 支持自定义数据转换函数
- 支持不同的数据格式
- 可以轻松添加新的保存器类型

## 测试验证

创建了完整的示例文件 `examples/streaming_data_save_example.py`，包含：
- 批量保存示例
- 流式保存示例
- 混合格式保存示例
- 错误处理示例

## 总结

这个数据分文件保存功能解决了大规模数据处理中的关键问题：

1. **性能问题**：避免单文件过大导致的读写性能问题
2. **内存问题**：支持流式处理，避免内存溢出
3. **通用性**：与数据内容无关，可以用于各种数据类型
4. **易用性**：提供简单的API，易于集成和使用

该功能已经集成到现有的SFT数据生成流程中，可以立即使用。 