# Code Analyzer 迁移指南

本文档指导如何从原有的 Git Keeper 代码分析功能迁移到新的 Code Analyzer 外部依赖包。

## 概述

Code Analyzer 是一个独立的代码分析库，从 Git Keeper 项目中提取了核心的代码分析功能，包括：

- AST解析（支持Python、C等语言）
- 代码图构建
- 调用链分析
- 静态代码分析
- 仓库索引管理

## 主要变化

### 1. 包结构变化

**原有结构：**
```
gate_keeper/
├── infrastructure/ast/
├── application/service/repo_analyzer/
├── application/service/repo_index/
└── domain/code/
```

**新结构：**
```
code_analyzer/
├── core/
├── models/
├── parsers/
├── utils/
└── cli/
```

### 2. 导入路径变化

**原有导入：**
```python
from gate_keeper.infrastructure.ast.tree_sitter.parser import create_parser_by_lang
from gate_keeper.application.service.repo_index.repository_index import RepositoryIndex
from gate_keeper.external.code_analyzer import StaticAnalyzer
```

**新导入：**
```python
from code_analyzer import ASTParserFactory, RepositoryIndex, StaticAnalyzer, CodeGraph
# 或者
from code_analyzer.core.ast_parser import ASTParserFactory
from code_analyzer.core.repository_index import RepositoryIndex
from code_analyzer.core.static_analyzer import StaticAnalyzer
from code_analyzer.core.code_graph import CodeGraph
```

### 3. API变化

#### AST解析器

**原有方式：**
```python
from gate_keeper.infrastructure.ast.tree_sitter.parser import create_parser_by_lang

parser = create_parser_by_lang("python")
functions = parser.extract_functions("example.py")
```

**新方式：**
```python
from code_analyzer import ASTParserFactory

parser = ASTParserFactory.create("python")
functions = parser.extract_functions("example.py")
```

#### 仓库索引

**原有方式：**
```python
from gate_keeper.application.service.repo_index.repository_index import RepositoryIndex

repo_index = RepositoryIndex(repo_dir="/path/to/repo", branch="main", git_service=git_service)
repo_index.index_repo()
```

**新方式：**
```python
from code_analyzer import RepositoryIndex

repo_index = RepositoryIndex(repo_dir="/path/to/repo", branch="main")
repo_index.build()
```

#### 静态分析器

**原有方式：**
```python
from gate_keeper.external.code_analyzer import StaticAnalyzer

analyzer = StaticAnalyzer(repo_index)
chains = analyzer.get_bidirectional_call_chains("function_name", "file.py")
```

**新方式：**
```python
from code_analyzer import StaticAnalyzer

analyzer = StaticAnalyzer(repo_index)
chains = analyzer.get_bidirectional_call_chains("function_name", "file.py")
```

## 迁移步骤

### 步骤1：安装 Code Analyzer

```bash
pip install code-analyzer
```

### 步骤2：更新导入语句

在需要代码分析功能的文件中，将原有的导入语句替换为新的导入语句。

### 步骤3：更新API调用

根据上述API变化，更新相关的函数调用。

### 步骤4：处理依赖变化

移除对原有 Git Keeper 代码分析模块的依赖，确保只使用新的 Code Analyzer。

## 详细迁移示例

### 示例1：AST解析

**原有代码：**
```python
from gate_keeper.infrastructure.ast.tree_sitter.parser import create_parser_by_lang
from gate_keeper.domain.code.function import Function

def analyze_file(file_path: str) -> List[Function]:
    parser = create_parser_by_lang("python")
    functions = parser.extract_functions(file_path)
    return functions
```

**新代码：**
```python
from code_analyzer import ASTParserFactory
from code_analyzer.models.function import Function

def analyze_file(file_path: str) -> List[Function]:
    parser = ASTParserFactory.create("python")
    functions = parser.extract_functions(file_path)
    return functions
```

### 示例2：仓库分析

**原有代码：**
```python
from gate_keeper.application.service.repo_index.repository_index import RepositoryIndex
from gate_keeper.external.code_analyzer import StaticAnalyzer

def analyze_repository(repo_dir: str, git_service):
    repo_index = RepositoryIndex(repo_dir=repo_dir, branch="main", git_service=git_service)
    repo_index.index_repo()
    
    analyzer = StaticAnalyzer(repo_index)
    chains = analyzer.get_bidirectional_call_chains("main", "main.py")
    return chains
```

**新代码：**
```python
from code_analyzer import RepositoryIndex, StaticAnalyzer

def analyze_repository(repo_dir: str):
    repo_index = RepositoryIndex(repo_dir=repo_dir, branch="main")
    repo_index.build()
    
    analyzer = StaticAnalyzer(repo_index)
    chains = analyzer.get_bidirectional_call_chains("main", "main.py")
    return chains
```

### 示例3：代码图分析

**原有代码：**
```python
from gate_keeper.application.service.repo_index.repository_index import RepositoryIndex
from gate_keeper.external.code_analyzer import StaticAnalyzer

def analyze_call_graph(repo_dir: str, git_service):
    repo_index = RepositoryIndex(repo_dir=repo_dir, branch="main", git_service=git_service)
    repo_index.index_repo()
    
    graph = repo_index.get_call_graph()
    return graph
```

**新代码：**
```python
from code_analyzer import CodeGraph

def analyze_call_graph(repo_dir: str):
    graph = CodeGraph.build_from_repository(repo_dir)
    return graph.graph
```

## 新功能

### CodeGraph类

CodeGraph 是一个新的高级接口，提供了更丰富的图分析功能：

```python
from code_analyzer import CodeGraph

# 构建代码图
graph = CodeGraph.build_from_repository("/path/to/repo")

# 获取统计信息
stats = graph.get_graph_statistics()

# 查找循环
cycles = graph.find_cycles()

# 获取强连通分量
components = graph.get_strongly_connected_components()

# 拓扑排序
topo_sort = graph.get_topological_sort()
```

### CLI工具

Code Analyzer 提供了命令行工具：

```bash
# 分析仓库
code-analyzer analyze /path/to/repo

# 分析文件
code-analyzer file example.py

# 分析调用链
code-analyzer chain /path/to/repo main main.py

# 图分析
code-analyzer graph /path/to/repo --stats --cycles
```

## 兼容性说明

### 保持兼容的API

- `extract_functions()` - 函数提取
- `extract_calls()` - 调用提取
- `find_parent_element()` - 父级元素查找
- `get_upstream_call_chains()` - 上游调用链
- `get_downstream_call_chains()` - 下游调用链
- `get_bidirectional_call_chains()` - 双向调用链

### 移除的依赖

- Git服务依赖（RepositoryIndex不再需要git_service参数）
- 原有的接口抽象层
- 特定的应用层服务

### 新增功能

- CodeGraph高级接口
- CLI命令行工具
- 更丰富的图分析功能
- 更好的错误处理

## 测试迁移

迁移完成后，建议运行以下测试：

1. **单元测试**：确保所有功能正常工作
2. **集成测试**：验证与其他组件的集成
3. **性能测试**：确保性能没有显著下降
4. **功能测试**：验证所有功能按预期工作

## 故障排除

### 常见问题

1. **导入错误**：确保正确安装了 code-analyzer 包
2. **API不兼容**：检查是否使用了已更改的API
3. **依赖冲突**：确保没有版本冲突

### 获取帮助

如果遇到问题，可以：

1. 查看本文档
2. 运行 `code-analyzer --help` 查看CLI帮助
3. 查看示例代码
4. 提交Issue到项目仓库

## 总结

Code Analyzer 提供了更清晰、更独立的代码分析功能，同时保持了与原有API的兼容性。通过遵循本迁移指南，可以顺利完成从原有代码到新库的迁移。 