# 提示词SFT数据生成模块使用指南

## 概述

提示词SFT数据生成模块是一个专门用于生成面向提示词的代码补全训练数据的工具。它能够将现有的SFT数据转换为符合不同模型要求的提示词格式，支持多种代码补全模型。

## 快速开始

### 1. 基本使用

```python
from core.data_gen.sft.analyzer import RealNPAnalyzer
from core.data_gen.sft.generator import SFTDataGenerator
from core.data_gen.sft.prompt_sft.generator import PromptSFTGenerator
from core.data_gen.sft.prompt_sft.models import ModelType

# 1. 初始化SFT分析器
analyzer = RealNPAnalyzer("path/to/your/project")
analyzer.analyze_project()

# 2. 初始化SFT数据生成器
sft_generator = SFTDataGenerator(analyzer)

# 3. 初始化提示词SFT数据生成器
prompt_generator = PromptSFTGenerator(sft_generator)

# 4. 生成提示词SFT数据
prompt_sft_data = prompt_generator.generate_prompt_sft_data(
    max_samples=10,
    model_type=ModelType.QWEN_CODER
)

# 5. 保存结果
prompt_generator.save_to_json(prompt_sft_data, "output.json")
```

### 2. 支持的模型类型

```python
from core.data_gen.sft.prompt_sft.models import ModelType

# 支持的模型类型
models = [
    ModelType.QWEN_CODER,        # Qwen Coder
    ModelType.DEEPSEEK_CODER     # DeepSeek Coder
]
```

### 3. 自定义模板

```python
from core.data_gen.sft.prompt_sft.templates import PromptTemplateManager

# 获取模板管理器
manager = PromptTemplateManager()

# 创建自定义模板
custom_template = manager.create_custom_template(
    system="You are a specialized embedded C assistant.",
    instruction="Complete the code with embedded constraints.",
    system_rules="- Use static allocation only\n- Consider interrupt safety",
    template_name="embedded_custom"
)

# 使用自定义模板生成数据
prompt_sft_data = prompt_generator.generate_prompt_sft_data(
    max_samples=5,
    model_type=ModelType.QWEN_CODER,
    template_name="embedded_custom"
)
```

## 数据格式

### 输入格式

模块接受现有的`EnhancedStatementMaskData`结构：

```python
@dataclass
class EnhancedStatementMaskData:
    before: str          # 补全前代码
    expected: str        # 期望补全内容
    after: str           # 补全后代码
    context_nodes: List[ContextNode]
    dependency_graph: DependencyGraph
    metadata: Dict[str, Any]
```

### 输出格式

生成的数据包含以下字段：

```python
@dataclass
class PromptSFTData:
    prompt: str          # 完整提示词
    completion: str      # 补全目标
    filepath: str        # 文件路径
    function_name: str   # 函数名
    line_number: int     # 行号
    model_type: ModelType # 模型类型
    before: str          # 原始before代码
    expected: str        # 原始expected代码
    after: str           # 原始after代码
    context_sections: Dict[ContextType, ContextSection]  # 上下文信息
    similar_contexts: List[SimilarContext]  # 相似上下文
    template_name: str   # 使用的模板名称
    complexity_score: float  # 复杂度分数
    context_relevance_score: float  # 上下文相关性分数
```

### JSON输出示例

```json
{
  "prompt": "<system>\nYou are a code completion assistant...\n</system>\n<|fim_prefix|>\nvoid foo() {\n  int x = 1;\n<|fim_suffix|>\n  return y;\n}\n<|fim_middle|>",
  "completion": "  int y = x + 1;",
  "filepath": "example.c",
  "function_name": "foo",
  "line_number": 3,
  "model_type": "qwen_coder",
  "template_name": "basic_c",
  "complexity_score": 0.5,
  "context_relevance_score": 0.8
}
```

## 提示词模板结构

生成的提示词包含以下结构化部分：

### 1. 系统角色设定 (`<system>`)
```text
<system>
You are a code completion assistant specialized in C-style languages.
</system>
```

### 2. 任务指令 (`<instruction>`)
```text
<instruction>
请在 Fill token 处补全代码，保持变量命名风格一致，避免重复声明。
</instruction>
```

### 3. 系统规则 (`<system_rules>`)
```text
<system_rules>
- 所有变量必须命名规范
- 不得出现未定义行为
- 优先使用已有的宏封装逻辑
- 注释需对边界行为进行解释
</system_rules>
```

### 4. 环境信息 (`<env>`)
```text
<env>
文件路径: example.c
当前函数: foo()
语言: c
</env>
```

### 5. 用户规则 (`<user_rules>`)
```text
<user_rules>
请不要生成未声明变量；遵守 C 语言语法。
</user_rules>
```

### 6. 相关上下文 (`<related_context>`)
```text
<related_context>
【已使用上下文】
函数: bar(), baz()
变量: x, y
宏: MAX_SIZE

【推测使用上下文】
函数: validate_input()
</related_context>
```

### 7. 相似上下文 (`<similar_context>`)
```text
<similar_context>
函数: similar_function()
代码:
void similar_function() {
    // 相似逻辑
}
</similar_context>
```

### 8. 代码补全部分
```text
<|fim_prefix|>
void foo() {
  int x = 1;
<|fim_suffix|>
  return y;
}
<|fim_middle|>
```

## 预定义模板

模块提供了多种预定义模板：

### 1. 基础C语言模板 (`basic_c`)
- 适用于通用C语言代码补全
- 包含基本的编码规范和语法要求

### 2. NP汇编模板 (`np_assembly`)
- 专门针对NP汇编语言
- 包含寄存器使用规范和流水线约束

### 3. 嵌入式C模板 (`embedded_c`)
- 适用于嵌入式系统开发
- 包含资源约束和实时性要求

### 4. 网络编程模板 (`network_c`)
- 适用于网络编程
- 包含协议正确性和错误处理

### 5. 驱动开发模板 (`driver_c`)
- 适用于设备驱动开发
- 包含硬件操作和并发安全

## 高级功能

### 1. 上下文分析

模块会自动分析代码上下文：

- **可见上下文**: 从`before`和`after`中提取已使用的实体
- **推测上下文**: 从`expected`中提取可能需要的实体
- **相似上下文**: 基于启发式规则查找相似函数

### 2. 质量评估

生成的数据包含质量指标：

- **复杂度分数**: 基于代码复杂度计算
- **上下文相关性**: 评估上下文与补全目标的相关性

### 3. 统计信息

```python
# 获取数据统计信息
stats = prompt_generator.get_statistics(prompt_sft_data)
print(f"总样本数: {stats['total_samples']}")
print(f"平均提示词长度: {stats['avg_prompt_length']:.1f}")
print(f"平均补全长度: {stats['avg_completion_length']:.1f}")
```

## 扩展性

### 1. 添加新模型支持

```python
from core.data_gen.sft.prompt_sft.token_adapters import TokenAdapter, TokenAdapterFactory
from core.data_gen.sft.prompt_sft.models import ModelType

class NewModelAdapter(TokenAdapter):
    def format_prompt(self, before: str, after: str) -> str:
        return f"<prefix>{before}<suffix>{after}<middle>"
    
    def get_model_type(self) -> ModelType:
        return ModelType.NEW_MODEL

# 注册新适配器
TokenAdapterFactory.register_adapter(ModelType.NEW_MODEL, NewModelAdapter())
```

### 2. 创建新模板

```python
from core.data_gen.sft.prompt_sft.templates import PromptTemplateManager

manager = PromptTemplateManager()
manager.create_custom_template(
    system="New domain specific system",
    instruction="Domain specific instruction",
    system_rules="Domain specific rules",
    template_name="new_domain"
)
```

## 最佳实践

### 1. 数据质量

- 确保输入代码质量良好
- 定期验证生成数据的有效性
- 监控复杂度分数和相关性分数

### 2. 性能优化

- 根据需求调整`max_samples`参数
- 使用合适的模板类型
- 考虑数据缓存和批量处理

### 3. 模板选择

- 根据代码类型选择合适的模板
- 考虑项目特定的编码规范
- 定期更新和优化模板内容

## 故障排除

### 常见问题

1. **没有生成数据**
   - 检查输入代码是否包含有效函数
   - 确认SFT分析器正常工作
   - 验证文件路径和权限

2. **提示词过长**
   - 调整上下文长度限制
   - 减少相似上下文数量
   - 优化模板内容

3. **上下文相关性低**
   - 改进上下文提取算法
   - 优化相似性计算
   - 调整相关性阈值

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查中间结果
enhanced_data = sft_generator.generate_enhanced_sft_data(5)
print(f"生成了 {len(enhanced_data)} 个增强SFT数据")

# 验证数据质量
for item in prompt_sft_data:
    if not item.validate():
        print(f"数据验证失败: {item.filepath}")
```

## 示例脚本

完整的使用示例请参考：
- `examples/prompt_sft_example.py` - 基本使用示例
- `ai_debug/test_prompt_sft_simple.py` - 功能测试脚本
- `tests/test_prompt_sft.py` - 单元测试

## 总结

提示词SFT数据生成模块提供了一个完整的解决方案，用于生成高质量的代码补全训练数据。通过合理使用模板、上下文分析和质量评估，可以生成适合不同模型和场景的训练数据。 