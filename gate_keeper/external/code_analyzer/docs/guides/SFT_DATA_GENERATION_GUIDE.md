# SFT数据生成使用指南

## 快速开始

### 1. 基本使用

```python
from examples.np_real_analysis import RealNPAnalyzer

# 初始化分析器
analyzer = RealNPAnalyzer("examples/np")

# 分析项目并生成数据
analyzer.analyze_project()
enhanced_data = analyzer.generate_enhanced_sft_data(max_samples=50)

# 保存结果
analyzer.save_real_results("output")
```

### 2. 关键参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `max_samples` | 生成样本数量 | 20-100 |
| `max_depth` | 调用链深度 | 3-5 |
| `complexity_threshold` | 复杂度阈值 | 0.1-0.5 |

## 数据生成策略

### 瀑布式遮盖 (Waterfall Sequential)
- **用途**: 模拟程序员逐行编写代码
- **特点**: 按顺序遮盖函数体中的语句
- **适用场景**: 代码补全任务

### 随机遮盖 (Random Inplace)
- **用途**: 模拟代码编辑和修改
- **特点**: 随机选择语句进行遮盖
- **适用场景**: 代码编辑任务

## 输出文件说明

### enhanced_sft_data.json
增强SFT训练数据，包含：
- 任务类型和策略
- 代码图上下文信息
- 语义上下文信息
- 元数据信息

### real_functions.json
函数分析结果，包含：
- 函数定义和签名
- 复杂度分析
- 调用关系

### real_code_graph.json
代码图信息，包含：
- 函数调用图
- 结构体使用关系
- 变量引用关系

## 质量控制

### 自动质量检查
- 内容长度验证
- 复杂度分数验证
- 上下文完整性验证
- 语义一致性验证

### 手动质量调整
```python
# 调整质量参数
analyzer.min_complexity_score = 0.2
analyzer.min_before_length = 30
analyzer.max_context_nodes = 15
```

## 常见问题

### Q: 解析失败怎么办？
A: 系统会自动使用fallback解析，支持正则表达式和启发式方法。

### Q: 如何提高数据质量？
A: 调整质量控制参数，增加上下文窗口大小，提高复杂度阈值。

### Q: 生成速度太慢？
A: 减少max_samples参数，或者分批处理大项目。

## 高级用法

### 自定义遮盖策略
```python
# 自定义策略比例
analyzer.waterfall_ratio = 0.7
analyzer.random_ratio = 0.3

# 自定义遮盖级别概率
analyzer.subexpression_prob = 0.4
analyzer.comment_prob = 0.3
```

### 批量处理
```python
# 处理多个项目
projects = ["project1", "project2", "project3"]
all_data = []

for project in projects:
    analyzer = RealNPAnalyzer(project)
    analyzer.analyze_project()
    data = analyzer.generate_enhanced_sft_data(max_samples=20)
    all_data.extend(data)
```

## 数据格式示例

```json
{
  "task_type": "code_completion",
  "strategy": "waterfall_sequential",
  "mask_level": "statement",
  "filepath": "examples/np/ipu_usertrace.asm",
  "function_name": "iIpuUsrTrcOutIpV6Judge",
  "line_number": 6,
  "before": "void iIpuUsrTrcOutIpV6Judge() {\n    uint8 rbIndex;\n    ",
  "expected": "UserTrcRule16B rsUsrTrcRule16B;",
  "after": "\n    // 处理IPv6判断逻辑\n    ...\n}",
  "complexity_score": 0.5,
  "context_nodes": [...],
  "semantic_context": ["Function: iIpuUsrTrcOutIpV6Judge", "Operation: variable_declaration"],
  "code_context": ["File: ipu_usertrace.asm", "Position: statement_2"],
  "metadata": {"strategy": "waterfall_sequential", "order": 2}
}
``` 