# 解析器概览

Code Analyzer 支持多种编程语言的代码解析，每种语言都有专门的解析器实现。

## 支持的解析器

| 解析器 | 语言 | 状态 | 文档 | 特性 |
|--------|------|------|------|------|
| PythonParser | Python | ✅ 完整支持 | [详细文档](python_parser.md) | 函数定义、调用、类型提示 |
| CParser | C/C++ | ✅ 完整支持 | [详细文档](c_parser.md) | 函数定义、调用、宏定义 |
| NPParser | NP | ✅ 完整支持 | [详细文档](NP_PARSER_README.md) | 混合语法、函数分析 |

## 解析器架构

所有解析器都继承自 `BaseParser` 抽象基类，提供统一的接口：

```python
class BaseParser(ABC):
    @abstractmethod
    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        """提取函数定义"""
        pass
    
    @abstractmethod
    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        """提取函数调用"""
        pass
    
    @abstractmethod
    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        """查找父级元素"""
        pass
```

## 使用方式

### 通过工厂创建

```python
from code_analyzer import ASTParserFactory

# 创建Python解析器
python_parser = ASTParserFactory.create("python")

# 创建C解析器
c_parser = ASTParserFactory.create("c")

# 创建NP解析器
np_parser = ASTParserFactory.create("np")
```

### 直接实例化

```python
from code_analyzer import PythonParser, CParser, NPParser

# 直接创建解析器实例
python_parser = PythonParser()
c_parser = CParser()
np_parser = NPParser()
```

## 功能对比

| 功能 | Python | C | NP |
|------|--------|---|----|
| 函数定义提取 | ✅ | ✅ | ✅ |
| 函数调用提取 | ✅ | ✅ | ✅ |
| 参数类型提示 | ✅ | ✅ | ✅ |
| 返回类型提取 | ✅ | ✅ | ✅ |
| 宏定义提取 | ❌ | ✅ | ❌ |
| 类定义提取 | ✅ | ❌ | ❌ |
| 装饰器支持 | ✅ | ❌ | ❌ |

## 性能特点

### Python解析器
- **优势**: 完整的语法支持，类型提示解析
- **特点**: 基于tree-sitter-python，支持Python 3.x语法
- **适用**: Python项目分析

### C解析器
- **优势**: 支持C/C++语法，宏定义提取
- **特点**: 基于tree-sitter-c，支持标准C语法
- **适用**: C/C++项目分析

### NP解析器
- **优势**: 支持混合语法（C + 汇编）
- **特点**: 基于tree-sitter-np，支持NP语言特性
- **适用**: NP语言项目分析

## 扩展新语言

要添加新的语言支持，需要：

1. **创建解析器类**
```python
class NewLanguageParser(BaseParser):
    def __init__(self):
        # 初始化语言特定的解析器
        pass
    
    def _parse_code(self, code: str) -> Tree:
        # 实现代码解析
        pass
    
    def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
        # 实现函数提取
        pass
    
    def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
        # 实现调用提取
        pass
    
    def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                           file_content: Optional[str] = None) -> Optional[Function]:
        # 实现父级元素查找
        pass
```

2. **注册到工厂**
```python
# 在 ASTParserFactory.create() 方法中添加
elif language == "new_language":
    parser = NewLanguageParser()
```

3. **添加测试**
```python
# 创建测试文件 tests/test_new_language_parser.py
class TestNewLanguageParser(unittest.TestCase):
    def test_parser_creation(self):
        parser = ASTParserFactory.create("new_language")
        self.assertIsNotNone(parser)
```

4. **更新文档**
- 创建解析器文档
- 更新此概览文档
- 添加使用示例

## 最佳实践

### 1. 选择合适的解析器

```python
# 根据文件扩展名选择解析器
def get_parser_for_file(file_path: str):
    ext = file_path.lower().split('.')[-1]
    if ext == 'py':
        return ASTParserFactory.create("python")
    elif ext in ['c', 'cpp', 'h', 'hpp']:
        return ASTParserFactory.create("c")
    elif ext == 'np':
        return ASTParserFactory.create("np")
    else:
        raise ValueError(f"不支持的文件类型: {ext}")
```

### 2. 错误处理

```python
try:
    parser = ASTParserFactory.create("python")
    functions = parser.extract_functions("example.py")
except ValueError as e:
    print(f"解析器创建失败: {e}")
except Exception as e:
    print(f"解析失败: {e}")
```

### 3. 性能优化

```python
# 重用解析器实例
parser = ASTParserFactory.create("python")

# 批量处理文件
for file_path in file_list:
    functions = parser.extract_functions(file_path)
    calls = parser.extract_calls(file_path)
    # 处理结果...
```

## 相关文档

- [BaseParser API](api/base_parser.md) - 解析器基类详细文档
- [ASTParserFactory API](api/ast_parser_factory.md) - 解析器工厂详细文档
- [Function Model](api/function_model.md) - 函数模型文档
- [FunctionCall Model](api/call_relation_model.md) - 调用关系模型文档 