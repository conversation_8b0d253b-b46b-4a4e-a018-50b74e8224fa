# NP解析器 (NP Parser)

## 概述

NP解析器是基于tree-sitter-np实现的NP语言代码分析工具，支持函数定义提取、函数调用关系分析等功能。

## 功能特性

- ✅ 函数定义提取
- ✅ 函数调用关系分析
- ✅ 父级元素查找
- ✅ 支持NP语言语法（C语言 + 汇编混合语法）

## 安装要求

### 1. 安装tree-sitter-np

NP解析器依赖于tree-sitter-np，需要先安装：

```bash
# 进入tree-sitter-np目录
cd /Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/ai-common/code/tree_sitters/languages/tree-sitter-np

# 安装Python绑定
pip install -e .
```

### 2. 安装code-analyzer

```bash
# 进入code-analyzer目录
cd /Users/<USER>/local_home/synologydrive/crossworkspace/coding/AI/code/code_analyzer

# 安装code-analyzer
pip install -e .
```

## 使用方法

### 基本用法

```python
from code_analyzer import ASTParserFactory

# 创建NP解析器
parser = ASTParserFactory.create("np")

# 分析NP代码文件
functions = parser.extract_functions("example.np")
calls = parser.extract_calls("example.np")

# 打印结果
for func in functions:
    print(f"函数: {func.name}")
    print(f"位置: {func.start_line}-{func.end_line}")
    print(f"参数: {[p.name for p in func.signature.parameters]}")

for call in calls:
    print(f"调用: {call.caller} -> {call.callee}")
```

### 直接使用NPParser

```python
from parsers.np_parser import NPParser

# 创建解析器
parser = NPParser()

# 分析代码
functions = parser.extract_functions("example.np")
calls = parser.extract_calls("example.np")
```

## NP语言语法支持

NP解析器支持以下NP语言语法结构：

### 函数定义
```c
// 函数声明
static int calculate_sum(int a, int b);

// 函数定义
static int calculate_sum(int a, int b) {
    int sum = a + b;
    return sum;
}
```

### 函数调用
```c
// 函数调用
func1(1, 2);
printf("hello");
```

### 混合语法
```c
#include "test.c"

static void func1(){
    func2()
}

static void func2(){
    sdfsdf
}%
```

## 语法树节点类型

NP解析器识别以下主要节点类型：

- `statement_function_def`: 函数定义
- `declare_function`: 函数声明
- `statement_function_call`: 函数调用语句
- `expression_function_call`: 函数调用表达式
- `identifier`: 标识符（函数名、变量名等）

## 测试

运行NP解析器测试：

```bash
python -m pytest tests/test_np_parser.py -v
```

运行演示：

```bash
python demo_np_parser.py
```

## 示例代码

### 示例1: 基本函数分析

```python
# 创建测试NP代码
test_code = """#include <stdio.h>

static void func1(int param1) {
    printf("hello");
    func2();
}

static void func2() {
    int x = 10;
}

int main() {
    func1(1);
    return 0;
}"""

# 保存到文件并分析
with open("test.np", "w") as f:
    f.write(test_code)

parser = ASTParserFactory.create("np")
functions = parser.extract_functions("test.np")
calls = parser.extract_calls("test.np")

print(f"找到 {len(functions)} 个函数")
print(f"找到 {len(calls)} 个函数调用")
```

### 示例2: 复杂代码分析

```python
test_code = """#include <stdio.h>
#include <stdlib.h>

// 函数声明
static int calculate_sum(int a, int b);
static void print_result(int result);

// 函数定义
static int calculate_sum(int a, int b) {
    int sum = a + b;
    print_result(sum);
    return sum;
}

static void print_result(int result) {
    printf("Result: %d\\n", result);
}

int main() {
    int x = 10;
    int y = 20;
    
    int sum = calculate_sum(x, y);
    return 0;
}"""

# 分析代码
parser = ASTParserFactory.create("np")
functions = parser.extract_functions("complex.np")

for func in functions:
    print(f"函数: {func.name}")
    print(f"参数: {[p.name for p in func.signature.parameters]}")
    if func.signature.return_type:
        print(f"返回类型: {func.signature.return_type}")
    print()
```

## 注意事项

1. **依赖安装**: 确保tree-sitter-np已正确安装并构建
2. **语法支持**: NP解析器支持C语言语法和部分汇编语法
3. **错误处理**: 解析器会优雅地处理语法错误，继续分析可解析的部分
4. **性能**: 基于tree-sitter的高效解析，支持大文件分析

## 故障排除

### 常见问题

1. **导入错误**: 确保tree-sitter-np已正确安装
2. **解析失败**: 检查NP代码语法是否正确
3. **函数提取不完整**: 检查函数定义语法是否符合NP语言规范

### 调试技巧

使用调试脚本查看语法树结构：

```bash
python debug_np_tree.py
```

## 贡献

欢迎提交Issue和Pull Request来改进NP解析器！

## 许可证

本项目采用MIT许可证。 