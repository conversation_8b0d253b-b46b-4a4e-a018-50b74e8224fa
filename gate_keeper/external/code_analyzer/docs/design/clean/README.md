# 数据清洗模块

本模块提供完整的数据清洗功能，专门用于检测和清理文本文件中的异常字符和编码问题。

## 功能特性

### 🔍 异常字符检测
- **控制字符检测**: 检测并识别各种控制字符（NULL、BELL、ESCAPE等）
- **Unicode异常字符**: 检测超出正常范围的Unicode字符
- **替换字符检测**: 检测Unicode替换字符（U+FFFD）
- **零宽字符检测**: 检测各种零宽字符
- **详细报告**: 提供字符位置、类型、建议等详细信息

### 📄 编码检测与处理
- **自动编码检测**: 使用chardet库自动检测文件编码
- **多编码尝试**: 支持17种常见编码格式
- **编码验证**: 验证编码的正确性和错误数量
- **智能建议**: 根据检测结果提供最佳编码建议

### 🧹 文本清洗
- **控制字符移除**: 自动移除有害的控制字符
- **替换字符清理**: 移除Unicode替换字符
- **零宽字符清理**: 移除各种零宽字符
- **空白字符规范化**: 规范化空格、制表符等
- **常见问题修复**: 修复常见的编码问题
- **UTF-8编码确保**: 强制将所有输出文件转换为UTF-8编码

### 📊 批量处理
- **批量清洗**: 支持批量处理整个目录
- **文件过滤**: 支持按文件扩展名过滤
- **进度跟踪**: 实时显示处理进度
- **统计报告**: 生成详细的清洗统计报告

### 🔄 UTF-8编码确保
- **强制UTF-8输出**: 所有清洗后的文件都使用UTF-8编码保存
- **编码转换**: 自动将其他编码转换为UTF-8
- **编码验证**: 验证输出文件的UTF-8编码正确性
- **兼容性保证**: 确保后续处理的数据都是UTF-8编码

## 模块结构

```
core/data_gen/clean/
├── __init__.py              # 模块初始化
├── anomaly_detector.py      # 异常字符检测器
├── encoding_detector.py     # 编码检测器
└── text_cleaner.py         # 文本清洗器
```

## 核心组件

### AnomalyDetector - 异常字符检测器

```python
from core.data_gen.clean import AnomalyDetector

detector = AnomalyDetector()

# 检测异常字符
anomalies = detector.detect_anomalies(text, file_path)

# 打印报告
detector.print_anomaly_report(anomalies, file_path)

# 获取统计信息
stats = detector.get_anomaly_statistics(anomalies)
```

**检测的异常字符类型**:
- `control_character`: 控制字符
- `replacement_character`: Unicode替换字符
- `out_of_range`: 超出正常范围的字符
- `unicode_category`: Unicode类别异常
- `null_character`: 空字符
- `start_of_heading`: 标题开始
- `end_of_text`: 文本结束
- 等等...

### EncodingDetector - 编码检测器

```python
from core.data_gen.clean import EncodingDetector

detector = EncodingDetector()

# 检测文件编码
encoding_info = detector.detect_encoding(file_path)

# 尝试多种编码
encoding_infos = detector.try_multiple_encodings(file_path)

# 使用指定编码读取文件
content, encoding_info = detector.read_file_with_encoding(file_path)

# 确保内容为UTF-8编码
utf8_content = detector.ensure_utf8_content(content, encoding_info.encoding)
```

**支持的编码格式**:
- UTF-8, UTF-8-SIG, UTF-16, UTF-16-LE, UTF-16-BE
- GBK, GB2312, GB18030, Big5
- Latin-1, ASCII, CP1252
- ISO-8859-1, ISO-8859-15
- Shift_JIS, EUC-JP, EUC-KR

### TextCleaner - 文本清洗器

```python
from core.data_gen.clean import TextCleaner

cleaner = TextCleaner()

# 清洗单个文件
result = cleaner.clean_file(input_file, output_file)

# 批量清洗
results = cleaner.batch_clean_files(
    input_dir="examples/np",
    output_dir="cleaned_examples",
    file_extensions=['.asm', '.h', '.c']
)
```

## 使用示例

### 1. 检测文件中的异常字符

```python
from core.data_gen.clean import AnomalyDetector

detector = AnomalyDetector()

# 读取文件
with open("examples/np/pkt_ipv6_hdr.h", "r", encoding="utf-8", errors="replace") as f:
    content = f.read()

# 检测异常字符
anomalies = detector.detect_anomalies(content, "pkt_ipv6_hdr.h")

# 打印详细报告
detector.print_anomaly_report(anomalies, "pkt_ipv6_hdr.h")
```

### 2. 检测文件编码

```python
from core.data_gen.clean import EncodingDetector

detector = EncodingDetector()

# 检测编码
encoding_info = detector.detect_encoding("examples/np/pkt_ipv6_hdr.h")
detector.print_encoding_report(encoding_info, "pkt_ipv6_hdr.h")

# 尝试多种编码
encoding_infos = detector.try_multiple_encodings("examples/np/pkt_ipv6_hdr.h")
detector.print_multiple_encodings_report(encoding_infos, "pkt_ipv6_hdr.h")
```

### 3. 清洗单个文件

```python
from core.data_gen.clean import TextCleaner

cleaner = TextCleaner()

# 清洗文件
result = cleaner.clean_file(
    "examples/np/pkt_ipv6_hdr.h",
    "cleaned_pkt_ipv6_hdr.h"
)

if result.is_successful:
    print(f"清洗成功！移除 {result.removed_chars} 个字符")
    print(f"清洗统计: {result.cleaning_stats}")
else:
    print(f"清洗失败: {result.error_message}")
```

### 4. 批量清洗

```python
from core.data_gen.clean import TextCleaner

cleaner = TextCleaner()

# 批量清洗整个目录
results = cleaner.batch_clean_files(
    input_dir="examples/np",
    output_dir="cleaned_examples",
    file_extensions=['.asm', '.h', '.c', '.cpp']
)

# 查看结果
successful_count = sum(1 for r in results if r.is_successful)
total_removed = sum(r.removed_chars for r in results)

print(f"成功清洗: {successful_count}/{len(results)} 个文件")
print(f"总移除字符数: {total_removed}")
```

### 5. UTF-8编码确保

```python
from core.data_gen.clean import EncodingDetector, TextCleaner

# 方法1: 使用编码检测器确保UTF-8
detector = EncodingDetector()
content, encoding_info = detector.read_file_with_encoding("input.txt")
utf8_content = detector.ensure_utf8_content(content, encoding_info.encoding)

# 方法2: 使用清洗器自动确保UTF-8
cleaner = TextCleaner()
result = cleaner.clean_file("input.txt", "output.txt")
# 输出文件自动使用UTF-8编码

# 验证UTF-8编码
try:
    with open("output.txt", "r", encoding="utf-8") as f:
        content = f.read()
    print("✅ 文件使用UTF-8编码")
except Exception as e:
    print(f"❌ 文件编码验证失败: {e}")
```

## 测试结果示例

### 异常字符检测结果

```
❌ 在文件 examples/np/pkt_ipv6_hdr.h 中发现 5 个异常字符:
--------------------------------------------------------------------------------
 1. 行   77, 列  80: 字符 'ˇ' (U+02C7)
    类型: out_of_range
    描述: 超出正常范围的字符: U+02C7
    建议: 检查字符编码或删除此字符

 2. 行  224, 列  18: 字符 '     ' (U+0009)
    类型: out_of_range
    描述: 超出正常范围的字符: U+0009
    建议: 检查字符编码或删除此字符
```

### 编码检测结果

```
📄 多编码检测报告: examples/np/pkt_ipv6_hdr.h
------------------------------------------------------------
 1. ✅ utf-8        (置信度: 1.00, 错误: 0)
    语言: universal, 建议: 推荐使用 utf-8 编码
 2. ✅ utf-8-sig    (置信度: 1.00, 错误: 0)
    语言: unknown, 建议: 推荐使用 utf-8-sig 编码
 3. ✅ gbk          (置信度: 1.00, 错误: 0)
    语言: chinese, 建议: 推荐使用 gbk 编码
```

### 清洗结果

```
📊 文本清洗报告: examples/np/pkt_ipv6_hdr.h
------------------------------------------------------------
原始文件大小: 23571 字符
清洗后大小: 16409 字符
移除字符数: 7162
原始编码: utf-8 (置信度: 1.00)
输出编码: UTF-8
异常字符数: 5
清洗统计:
  whitespace_normalized: 7162
✅ 文件清洗成功 (已转换为UTF-8编码)
```

### UTF-8编码验证结果

```
🔍 测试UTF-8编码确保功能
============================================================
原始内容: 'UTF-8内容' (编码: utf-8)
UTF-8内容: 'UTF-8内容'
✅ UTF-8编码验证成功，字节长度: 11

原始内容: 'GBK内容测试' (编码: gbk)
UTF-8内容: 'GBKݲ'
✅ UTF-8编码验证成功，字节长度: 23

📄 测试文件UTF-8转换
============================================================
✅ 文件清洗和UTF-8转换成功！
原始编码: utf-8
输出编码: UTF-8
移除字符数: 7162
✅ 输出文件UTF-8编码验证成功
输出文件大小: 16409 字符
```

## 配置选项

### TextCleaner 配置

```python
cleaner = TextCleaner()

# 清洗配置
cleaner.remove_control_chars = True      # 移除控制字符
cleaner.remove_replacement_chars = True  # 移除替换字符
cleaner.normalize_whitespace = True      # 规范化空白字符
cleaner.remove_zero_width_chars = True   # 移除零宽字符
cleaner.fix_common_issues = True         # 修复常见问题
```

### AnomalyDetector 配置

```python
detector = AnomalyDetector()

# 可以自定义正常字符范围
detector.normal_ranges = [
    (0x0020, 0x007F),  # 基本拉丁字母
    (0x4E00, 0x9FFF),  # 中文汉字
    # 添加更多范围...
]
```

## 性能特点

- **高效检测**: 使用优化的算法快速检测异常字符
- **内存友好**: 支持大文件处理，内存占用可控
- **批量处理**: 支持批量处理，提高效率
- **详细报告**: 提供详细的检测和清洗报告

## 依赖要求

```bash
pip install chardet
```

## 注意事项

1. **备份原文件**: 清洗前建议备份原始文件
2. **编码问题**: 某些文件可能存在多种编码问题，需要多次清洗
3. **特殊字符**: 某些特殊字符可能是合法的，清洗时需要谨慎
4. **大文件处理**: 对于超大文件，建议分批处理
5. **UTF-8编码**: 所有输出文件都使用UTF-8编码，确保后续处理的兼容性
6. **编码转换**: 从其他编码转换为UTF-8时，某些字符可能被替换为占位符

## 故障排除

### 常见问题

1. **编码检测失败**
   - 尝试手动指定编码
   - 检查文件是否损坏

2. **清洗后文件为空**
   - 检查清洗配置是否过于严格
   - 查看清洗统计报告

3. **异常字符过多**
   - 检查文件来源和编码
   - 考虑使用更宽松的检测规则

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看原始字节
with open(file_path, 'rb') as f:
    raw_bytes = f.read()
    print(raw_bytes[:100])  # 查看前100个字节
```

## 扩展功能

模块设计为可扩展的，可以轻松添加：

- 新的异常字符类型检测
- 自定义清洗规则
- 更多编码格式支持
- 特定领域的清洗策略

## 贡献指南

欢迎贡献代码和改进建议：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。 