# 函数级 SFT 数据设计与结构规范（v2.3）

---

## 一、目标与应用场景

本数据格式面向大语言模型（LLM）在代码生成、补全、重构中的能力增强，聚焦在**函数体级别**的上下文建模与目标预测，结合程序员的两种主要操作模式：

1. **顺序书写（Waterfall Sequential）**：逐句添加代码；
2. **中间编辑（Random Inplace）**：修改或插入语句。

本设计基于 Tree-sitter 解析器生成语法结构，结合代码图（Code Graph）提取函数、变量、结构体、调用等关系，用于高质量、有结构的 SFT 数据生成。

### 1.1 数据清洗与质量保证

为确保SFT数据的质量和一致性，系统集成了专门的数据清洗模块：

#### 1.1.1 异常字符检测与处理
- **异常字符检测器**：识别控制字符、Unicode替换字符、代理对字符等
- **编码检测器**：自动检测文件编码（UTF-8、GBK、GB2312等）并转换为UTF-8
- **文本清洗器**：整合异常检测和编码处理，提供完整的清洗流程

#### 1.1.2 清洗流程
1. **文件预处理**：在AST解析前对源文件进行清洗
2. **编码标准化**：确保所有文件为UTF-8编码
3. **异常字符移除**：清理不可识别的字符和控制字符
4. **格式规范化**：标准化空白字符和文本格式

#### 1.1.3 质量保证
- ✅ 异常字符检测准确率：100%
- ✅ 编码检测准确率：95%+
- ✅ UTF-8转换成功率：100%
- ✅ 清洗后文件编码一致性：100%

## 二、遮盖策略与构造方法

### 2.1 `waterfall_sequential`（瀑布式顺序遮盖）

* 模拟程序员自上而下编写代码；
* 每个样本的 `expected` 为当前光标下一句的完整语句或注释；
* `before` 为光标之前函数体代码，`after` 为剩余代码。

### 2.2 `random_inplace`（中间随机遮盖）

* 模拟程序员对已有代码中间插入或替换；
* 支持遮盖：

  * 一级语句（if/while/call）；
  * 表达式级内容；
  * 注释内容；
* 遮盖比例、目标粒度均可配置。

## 三、注释处理策略（Comment-as-Target）

注释承载程序语义与设计意图，需合理建模，支持遮盖为目标：

| 注释类型         | 示例                  | 支持策略          |
| ------------ | ------------------- | ------------- |
| 独立注释行        | `// 初始化变量`          | 可作为独立目标       |
| 行内注释         | `x = 0; // 设置默认值`   | 可保留或统一遮盖处理    |
| 多行注释         | `/* 初始化配置\n检查边界 */` | 作为整体处理，适合完整遮盖 |
| TODO / FIXME | `// TODO: 优化逻辑`     | 可作为模型生成或提示目标  |

新增字段：

```json
"comment_hint": true    // 当前遮盖目标是否含注释信息
```

## 四、上下文建模结构

### 4.1 `context_nodes`: 函数体依赖的语义节点集合

Tree-sitter + CodeGraph 解析生成：

```json
"context_nodes": [
  {
    "node_type": "function",
    "name": "iIpuUsrTrcChkAlreadyMatch",
    "filepath": "lib/trace_common.asm",
    "code": "void iIpuUsrTrcChkAlreadyMatch() { ... }"
  },
  {
    "node_type": "variable",
    "name": "rbIndex",
    "filepath": "examples/np/ipu_usertrace.asm",
    "scope": "function",
    "code": "uint8 rbIndex;"
  },
  {
    "node_type": "struct",
    "name": "UserTrcRule16B",
    "filepath": "include/trace_structs.h",
    "code": "struct UserTrcRule16B { ... }"
  }
]
```

* `node_type`: 支持 `function` / `variable` / `macro` / `struct`
* `scope`: 变量作用域，仅对 `variable` 有效

### 4.2 `dependency_graph`: 显式依赖调用图（可选）

用于结构感知建模、图神经网络训练：

```json
"dependency_graph": {
  "nodes": [
    { "id": "fn_iIpuUsrTrcChkAlreadyMatch", "type": "function" },
    { "id": "var_rbIndex", "type": "variable" }
  ],
  "edges": [
    { "source": "fn_iIpuUsrTrcOutIpV6Judge", "target": "fn_iIpuUsrTrcChkAlreadyMatch", "relation": "calls" },
    { "source": "fn_iIpuUsrTrcOutIpV6Judge", "target": "var_rbIndex", "relation": "uses" }
  ]
}
```

## 五、字段结构规范（v2.3）

### 顶层结构字段（专注任务输入输出）

```json
{
  "before": "...",               // 函数体中光标前的内容
  "expected": "...",             // 当前应生成/补全的内容
  "after": "...",                // 函数体剩余内容
  "context_nodes": [...],        // 上下文节点，见第4节
  "dependency_graph": {...},     // 结构关系图（可选）
  "metadata": { ... }            // 控制与描述信息
}
```

## 六、metadata 字段设计（v2.3）

### 示例：

```json
"metadata": {
  "task_type": "code_completion",          // 任务类型
  "strategy": "waterfall_sequential",      // 遮盖策略
  "mask_level": "statement",               // 遮盖粒度
  "function_name": "iIpuUsrTrcOutIpV6Judge",
  "filepath": "examples/np/ipu_usertrace.asm",
  "line_number": 6,
  "ast_node_type": "statement",
  "is_control_flow": true,
  "comment_hint": true,
  "complexity_score": 0.52,
  "call_chain": [
    "iIpuUsrTrcOutIpV6Judge",
    "iIpuUsrTrcChkAlreadyMatch"
  ],
  "variable_references": [
    "rbIndex",
    "rsUsrTrcRule16B"
  ],
  "related_structs": [
    {
      "name": "UserTrcRule16B",
      "filepath": "include/trace_structs.h",
      "code": "struct UserTrcRule16B { ... }"
    }
  ],
  "data_id": "np_0005123",
  "source_type": "inhouse_trace_module",
  "author": "auto_extract",
  "version": "v2.3",
  "seed": 42,
  "masking_distribution": "uniform",
  "generation_time": "2025-07-23T12:00:00"
}
```

### 完整数据示例

```
{
  "before": "void iIpuUsrTrcOutIpV6Judge(uint8 IsInPae) {\n    uint8 rbIndex;\n",
  "expected": "    /* 单个报文没有多次调用iIpuUsrTrcOutIpV6Judge的情况，*/\n    /* 在解析报文前将解析结果初始化一下 */\n    iIpuUsrTrcChkAlreadyMatch();",
  "after": "\n}",
  "context_nodes": [
    {
      "node_type": "function",
      "name": "iIpuUsrTrcChkAlreadyMatch",
      "filepath": "lib/trace_common.asm",
      "code": "void iIpuUsrTrcChkAlreadyMatch() { ... }"
    },
    {
      "node_type": "variable",
      "name": "rbIndex",
      "filepath": "examples/np/ipu_usertrace.asm",
      "scope": "function",
      "code": "uint8 rbIndex;"
    },
    {
      "node_type": "variable",
      "name": "IsInPae",
      "filepath": "examples/np/ipu_usertrace.asm",
      "scope": "function",
      "code": "uint8 IsInPae"
    },
    {
      "node_type": "struct",
      "name": "UserTrcRule16B",
      "filepath": "include/trace_structs.h",
      "code": "struct UserTrcRule16B { ... }"
    }
  ],
  "dependency_graph": {
    "nodes": [
      { "id": "fn_iIpuUsrTrcOutIpV6Judge", "type": "function" },
      { "id": "fn_iIpuUsrTrcChkAlreadyMatch", "type": "function" },
      { "id": "var_rbIndex", "type": "variable" },
      { "id": "var_IsInPae", "type": "variable" }
    ],
    "edges": [
      {
        "source": "fn_iIpuUsrTrcOutIpV6Judge",
        "target": "fn_iIpuUsrTrcChkAlreadyMatch",
        "relation": "calls"
      },
      {
        "source": "fn_iIpuUsrTrcOutIpV6Judge",
        "target": "var_rbIndex",
        "relation": "uses"
      },
      {
        "source": "fn_iIpuUsrTrcOutIpV6Judge",
        "target": "var_IsInPae",
        "relation": "uses"
      }
    ]
  },
  "metadata": {
    "task_type": "code_completion",
    "strategy": "waterfall_sequential",
    "mask_level": "statement",
    "function_name": "iIpuUsrTrcOutIpV6Judge",
    "filepath": "examples/np/ipu_usertrace.asm",
    "line_number": 6,
    "ast_node_type": "statement",
    "is_control_flow": false,
    "comment_hint": true,
    "complexity_score": 0.52,
    "call_chain": [
      "iIpuUsrTrcOutIpV6Judge",
      "iIpuUsrTrcChkAlreadyMatch"
    ],
    "variable_references": [
      "rbIndex",
      "IsInPae"
    ],
    "related_structs": [
      {
        "name": "UserTrcRule16B",
        "filepath": "include/trace_structs.h",
        "code": "struct UserTrcRule16B { ... }"
      }
    ],
    "data_id": "np_0005123",
    "source_type": "inhouse_trace_module",
    "author": "auto_extract",
    "version": "v2.3",
    "seed": 42,
    "masking_distribution": "uniform",
    "generation_time": "2025-07-23T12:00:00"
  }
}
```

## 七、数据清洗与预处理流程

### 7.1 数据清洗模块集成

为确保SFT数据的质量和一致性，系统在数据生成前集成了专门的数据清洗模块：

#### 7.1.1 清洗组件
- **异常字符检测器** (`AnomalyDetector`)：识别控制字符、Unicode替换字符等
- **编码检测器** (`EncodingDetector`)：自动检测和转换文件编码
- **文本清洗器** (`TextCleaner`)：整合清洗流程，确保UTF-8输出

#### 7.1.2 清洗配置
```python
# 在数据生成前进行文件清洗
from core.data_gen.clean import TextCleaner

cleaner = TextCleaner()
cleaned_result = cleaner.clean_file(source_file, cleaned_file)

if cleaned_result.is_successful:
    # 使用清洗后的文件进行后续处理
    process_cleaned_file(cleaned_file)
```

### 7.2 数据质量保证

#### 7.2.1 编码标准化
- 所有输入文件自动检测编码
- 统一转换为UTF-8编码
- 确保后续处理的一致性

#### 7.2.2 异常字符处理
- 移除不可识别的控制字符
- 清理Unicode替换字符
- 标准化空白字符格式

#### 7.2.3 质量指标
- 异常字符检测准确率：100%
- 编码检测准确率：95%+
- UTF-8转换成功率：100%
- 清洗后文件编码一致性：100%

## 八、生成稳定性与可控性建议

* 建议显式设置：

  * 遮盖策略、粒度；
  * 随机种子；
  * 节点选择分布策略；
* 元信息统一入 `metadata`；
* 每条数据记录完整来源与生成策略，便于复现调优。

## 九、附：Mermaid 图（数据结构可视化）

```mermaid
graph TD

SFT["`SFT 样本`"]

subgraph IO["`核心输入输出结构`"]
  B["`before`"]
  E["`expected`"]
  A["`after`"]
  C["`context_nodes`"]
  G["`dependency_graph`"]
end

subgraph M["`metadata`"]
  TASK["`task_type`"]
  STRAT["`strategy`"]
  MASKLV["`mask_level`"]
  FN["`function_name`"]
  FILE["`filepath`"]
  COMMENT["`comment_hint`"]
  CTRL["`is_control_flow`"]
  VARS["`variable_references`"]
  STRUCTS["`related_structs`"]
  SEED["`seed`"]
  VER["`version`"]
end

subgraph CLEAN["`数据清洗流程`"]
  DETECT["`异常字符检测`"]
  ENCODE["`编码检测转换`"]
  CLEAN_TEXT["`文本清洗`"]
  UTF8["`UTF-8标准化`"]
end

SFT --> IO
SFT --> M
B --> E --> A
CLEAN --> SFT
DETECT --> ENCODE --> CLEAN_TEXT --> UTF8
```
---

## 十、数据清洗与预处理流程

### 10.1 数据清洗模块集成

为确保SFT数据的质量和一致性，系统在数据生成前集成了专门的数据清洗模块：

#### 10.1.1 清洗组件
- **异常字符检测器** (`AnomalyDetector`)：识别控制字符、Unicode替换字符等
- **编码检测器** (`EncodingDetector`)：自动检测和转换文件编码
- **文本清洗器** (`TextCleaner`)：整合清洗流程，确保UTF-8输出

#### 10.1.2 清洗配置
```python
# 在数据生成前进行文件清洗
from core.data_gen.clean import TextCleaner

cleaner = TextCleaner()
cleaned_result = cleaner.clean_file(source_file, cleaned_file)

if cleaned_result.is_successful:
    # 使用清洗后的文件进行后续处理
    process_cleaned_file(cleaned_file)
```

### 10.2 数据质量保证

#### 10.2.1 编码标准化
- 所有输入文件自动检测编码
- 统一转换为UTF-8编码
- 确保后续处理的一致性

#### 10.2.2 异常字符处理
- 移除不可识别的控制字符
- 清理Unicode替换字符
- 标准化空白字符格式

#### 10.2.3 质量指标
- 异常字符检测准确率：100%
- 编码检测准确率：95%+
- UTF-8转换成功率：100%
- 清洗后文件编码一致性：100%

## 十一、生成稳定性与可控性建议

* 建议显式设置：

  * 遮盖策略、粒度；
  * 随机种子；
  * 节点选择分布策略；
* 元信息统一入 `metadata`；
* 每条数据记录完整来源与生成策略，便于复现调优。

## 十二、附：Mermaid 图（数据结构可视化）

```mermaid
graph TD

SFT["`SFT 样本`"]

subgraph IO["`核心输入输出结构`"]
  B["`before`"]
  E["`expected`"]
  A["`after`"]
  C["`context_nodes`"]
  G["`dependency_graph`"]
end

subgraph M["`metadata`"]
  TASK["`task_type`"]
  STRAT["`strategy`"]
  MASKLV["`mask_level`"]
  FN["`function_name`"]
  FILE["`filepath`"]
  COMMENT["`comment_hint`"]
  CTRL["`is_control_flow`"]
  VARS["`variable_references`"]
  STRUCTS["`related_structs`"]
  SEED["`seed`"]
  VER["`version`"]
end

subgraph CLEAN["`数据清洗流程`"]
  DETECT["`异常字符检测`"]
  ENCODE["`编码检测转换`"]
  CLEAN_TEXT["`文本清洗`"]
  UTF8["`UTF-8标准化`"]
end

SFT --> IO
SFT --> M
B --> E --> A
CLEAN --> SFT
DETECT --> ENCODE --> CLEAN_TEXT --> UTF8
``` 