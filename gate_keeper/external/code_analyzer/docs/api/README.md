# API 文档

Code Analyzer 的完整 API 参考文档。

## 📋 目录

### 核心模块

- [AST解析器工厂](ast_parser_factory.md) - `ASTParserFactory` 类
- [仓库索引工厂](repository_index_factory.md) - `RepositoryIndexFactory` 类
- [代码图构建器](code_graph_builder.md) - `CodeGraphBuilder` 类
- [仓库索引](repository_index.md) - `RepositoryIndex` 类

### 解析器

- [解析器基类](base_parser.md) - `BaseParser` 抽象基类
- [Python解析器](python_parser.md) - `PythonParser` 类
- [C解析器](c_parser.md) - `CParser` 类
- [NP解析器](np_parser.md) - `NPParser` 类

### 数据模型

- [函数模型](function_model.md) - `Function` 和 `FunctionSignature` 类
- [调用关系模型](call_relation_model.md) - `FunctionCall` 类
- [代码图模型](code_graph_model.md) - `CodeGraph` 类
- [仓库模型](repository_model.md) - `Repository` 类

### 工具模块

- [文件工具](file_utils.md) - 文件操作工具函数
- [路径工具](path_utils.md) - 路径处理工具函数

## 🚀 快速参考

### 导入常用类

```python
from code_analyzer import (
    ASTParserFactory,
    RepositoryIndexFactory,
    CodeGraph,
    Function,
    FunctionCall
)
```

### 基本使用模式

```python
# 1. 创建解析器
parser = ASTParserFactory.create("python")

# 2. 提取函数和调用
functions = parser.extract_functions("file.py")
calls = parser.extract_calls("file.py")

# 3. 构建仓库索引
repo_index = RepositoryIndexFactory.get_or_build("repo_path")

# 4. 获取代码图
graph = repo_index.get_code_graph()
```

## 📝 文档约定

- **类名**: 使用 `ClassName` 格式
- **方法名**: 使用 `method_name()` 格式
- **参数**: 使用 `param_name: type` 格式
- **返回值**: 使用 `-> return_type` 格式
- **示例代码**: 使用代码块格式

## 🔍 搜索

使用浏览器的搜索功能 (Ctrl+F) 快速查找特定的类或方法。 