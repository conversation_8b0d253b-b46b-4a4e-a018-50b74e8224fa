# 函数模型 (Function)

`Function` 类表示代码中的函数定义，包含函数的名称、签名、位置等信息。

## 类定义

```python
class Function(Element):
    """函数定义模型"""
    
    name: str
    signature: FunctionSignature
    start_line: int
    end_line: int
    filepath: str
    code: str
```

## 属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `name` | `str` | 函数名称 |
| `signature` | `FunctionSignature` | 函数签名（参数、返回类型等） |
| `start_line` | `int` | 函数开始行号（从1开始） |
| `end_line` | `int` | 函数结束行号（从1开始） |
| `filepath` | `str` | 函数所在文件路径 |
| `code` | `str` | 函数的完整代码 |

## 类方法

### create_simple()

创建简单的函数实例。

```python
@classmethod
def create_simple(cls, name: str, signature: FunctionSignature, 
                 start_line: int, end_line: int, filepath: str, 
                 code: str) -> "Function":
    """
    创建简单的函数实例。
    
    Args:
        name: 函数名称
        signature: 函数签名
        start_line: 开始行号
        end_line: 结束行号
        filepath: 文件路径
        code: 函数代码
        
    Returns:
        Function: 函数实例
    """
```

**示例:**
```python
from code_analyzer import Function, FunctionSignature

signature = FunctionSignature("hello_world", [], None)
func = Function.create_simple(
    name="hello_world",
    signature=signature,
    start_line=1,
    end_line=3,
    filepath="example.py",
    code="def hello_world():\n    print('Hello')"
)
```

## 使用示例

### 1. 基本使用

```python
from code_analyzer import ASTParserFactory

# 提取函数
parser = ASTParserFactory.create("python")
functions = parser.extract_functions("example.py")

for func in functions:
    print(f"函数名: {func.name}")
    print(f"位置: {func.start_line}-{func.end_line}")
    print(f"文件: {func.filepath}")
    print(f"参数数量: {len(func.signature.parameters)}")
    print(f"返回类型: {func.signature.return_type}")
    print(f"代码长度: {len(func.code)} 字符")
    print("---")
```

### 2. 函数签名分析

```python
for func in functions:
    signature = func.signature
    print(f"函数: {func.name}")
    
    # 分析参数
    if signature.parameters:
        print("  参数:")
        for param in signature.parameters:
            type_info = f" ({param.type_hint})" if param.type_hint else ""
            print(f"    - {param.name}{type_info}")
    else:
        print("  无参数")
    
    # 分析返回类型
    if signature.return_type:
        print(f"  返回类型: {signature.return_type}")
    else:
        print("  无返回类型")
```

### 3. 代码分析

```python
for func in functions:
    print(f"函数: {func.name}")
    print(f"代码行数: {func.end_line - func.start_line + 1}")
    print(f"代码预览:")
    print(func.code[:100] + "..." if len(func.code) > 100 else func.code)
    print("---")
```

## 函数签名 (FunctionSignature)

`FunctionSignature` 类表示函数的签名信息。

### 类定义

```python
class FunctionSignature:
    """函数签名模型"""
    
    name: str
    parameters: List[Parameter]
    return_type: Optional[str]
```

### 属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `name` | `str` | 函数名称 |
| `parameters` | `List[Parameter]` | 参数列表 |
| `return_type` | `Optional[str]` | 返回类型（可选） |

### 使用示例

```python
from code_analyzer import FunctionSignature, Parameter

# 创建函数签名
signature = FunctionSignature(
    name="calculate_sum",
    parameters=[
        Parameter("a", "int"),
        Parameter("b", "int")
    ],
    return_type="int"
)

print(f"函数: {signature.name}")
print(f"参数: {[f'{p.name}: {p.type_hint}' for p in signature.parameters]}")
print(f"返回: {signature.return_type}")
```

## 参数 (Parameter)

`Parameter` 类表示函数参数。

### 类定义

```python
class Parameter:
    """函数参数模型"""
    
    name: str
    type_hint: Optional[str]
```

### 属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `name` | `str` | 参数名称 |
| `type_hint` | `Optional[str]` | 类型提示（可选） |

### 使用示例

```python
from code_analyzer import Parameter

# 创建参数
param1 = Parameter("name", "str")
param2 = Parameter("age", "int")
param3 = Parameter("optional_param")  # 无类型提示

print(f"参数1: {param1.name} ({param1.type_hint})")
print(f"参数2: {param2.name} ({param2.type_hint})")
print(f"参数3: {param3.name} ({param3.type_hint})")
```

## 语言特定支持

### Python

```python
# Python函数示例
def python_function():
    functions = parser.extract_functions("example.py")
    
    for func in functions:
        # Python特有的类型提示
        if func.signature.return_type:
            print(f"返回类型: {func.signature.return_type}")
        
        # 参数类型提示
        for param in func.signature.parameters:
            if param.type_hint:
                print(f"参数 {param.name}: {param.type_hint}")
```

### C语言

```c
// C函数示例
int c_function(int a, char* b) {
    return 0;
}
```

```python
# C函数分析
c_functions = parser.extract_functions("example.c")

for func in c_functions:
    print(f"C函数: {func.name}")
    print(f"返回类型: {func.signature.return_type}")
    for param in func.signature.parameters:
        print(f"  参数: {param.type_hint} {param.name}")
```

### NP语言

```c
// NP函数示例
static void np_function(int param1) {
    printf("hello");
}
```

```python
# NP函数分析
np_functions = parser.extract_functions("example.np")

for func in np_functions:
    print(f"NP函数: {func.name}")
    print(f"位置: {func.start_line}-{func.end_line}")
```

## 性能考虑

- **内存使用**: 函数代码会完整保存在内存中，大函数可能占用较多内存
- **序列化**: 函数对象支持序列化，可以保存到文件或数据库
- **比较**: 函数对象支持相等性比较，基于名称、位置和文件路径

## 相关类

- [FunctionCall](call_relation_model.md) - 函数调用模型
- [Element](element_model.md) - 基础元素模型
- [BaseParser](base_parser.md) - 解析器基类 