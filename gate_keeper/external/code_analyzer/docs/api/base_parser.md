# 解析器基类 (BaseParser)

`BaseParser` 是所有语言解析器的抽象基类，定义了解析器的通用接口和基础功能。

## 类定义

```python
class BaseParser(ABC):
    """解析器基类"""
```

## 抽象方法

### extract_functions()

提取函数定义。

```python
@abstractmethod
def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
    """
    从代码文件中提取函数定义。
    
    Args:
        file_path: 文件路径
        file_content: 可选的文件内容，如果提供则直接使用而不读取文件
        
    Returns:
        List[Function]: 函数定义列表
        
    Raises:
        ValueError: 文件读取或解析失败时抛出
    """
```

### extract_calls()

提取函数调用。

```python
@abstractmethod
def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
    """
    从代码文件中提取函数调用。
    
    Args:
        file_path: 文件路径
        file_content: 可选的文件内容，如果提供则直接使用而不读取文件
        
    Returns:
        List[FunctionCall]: 函数调用列表
        
    Raises:
        ValueError: 文件读取或解析失败时抛出
    """
```

### find_parent_element()

查找父级元素。

```python
@abstractmethod
def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                       file_content: Optional[str] = None) -> Optional[Function]:
    """
    查找指定行号范围内的父级函数元素。
    
    Args:
        file_path: 文件路径
        start_line: 起始行号（从1开始）
        end_line: 结束行号（从1开始）
        file_content: 可选的文件内容
        
    Returns:
        Optional[Function]: 找到的父级函数，如果没找到则返回None
        
    Raises:
        ValueError: 文件读取或解析失败时抛出
    """
```

### _parse_code()

解析代码生成语法树。

```python
@abstractmethod
def _parse_code(self, code: str) -> Tree:
    """
    解析代码生成语法树。
    
    Args:
        code: 源代码字符串
        
    Returns:
        Tree: 语法树对象
    """
```

## 具体方法

### extract_macros()

提取宏定义（默认实现返回空列表）。

```python
def extract_macros(self, file_path: str, file_content: Optional[str] = None) -> List[Macro]:
    """
    从代码文件中提取宏定义。
    默认实现返回空列表，子类可以重写此方法。
    
    Args:
        file_path: 文件路径
        file_content: 可选的文件内容
        
    Returns:
        List[Macro]: 宏定义列表
    """
    return []
```

### _get_code_and_tree()

获取代码内容和语法树。

```python
def _get_code_and_tree(self, file_path: str, file_content: Optional[str] = None) -> Tuple[str, Tree, List[str]]:
    """
    获取代码内容和语法树。
    
    Args:
        file_path: 文件路径
        file_content: 可选的文件内容
        
    Returns:
        Tuple[str, Tree, List[str]]: (代码内容, 语法树, 代码行列表)
        
    Raises:
        ValueError: 文件读取或解析失败时抛出
    """
```

### _get_node_text()

获取节点的文本内容。

```python
def _get_node_text(self, node: Node, code: str) -> str:
    """
    获取语法树节点的文本内容。
    
    Args:
        node: 语法树节点
        code: 源代码字符串
        
    Returns:
        str: 节点对应的文本内容
    """
```

### _get_node_lines()

获取节点的行内容。

```python
def _get_node_lines(self, node: Node, lines: List[str]) -> List[str]:
    """
    获取语法树节点对应的代码行。
    
    Args:
        node: 语法树节点
        lines: 代码行列表
        
    Returns:
        List[str]: 节点对应的代码行列表
    """
```

## 使用示例

### 1. 基本使用

```python
from code_analyzer import ASTParserFactory

# 创建解析器（具体实现）
parser = ASTParserFactory.create("python")

# 提取函数定义
functions = parser.extract_functions("example.py")
for func in functions:
    print(f"函数: {func.name}")
    print(f"位置: {func.start_line}-{func.end_line}")
    print(f"参数: {[p.name for p in func.signature.parameters]}")

# 提取函数调用
calls = parser.extract_calls("example.py")
for call in calls:
    print(f"调用: {call.caller} -> {call.callee}")

# 查找父级元素
parent = parser.find_parent_element("example.py", 10, 15)
if parent:
    print(f"父级函数: {parent.name}")
```

### 2. 使用文件内容

```python
code_content = """
def hello_world():
    print("Hello, World!")

def main():
    hello_world()
"""

parser = ASTParserFactory.create("python")

# 直接使用代码内容而不读取文件
functions = parser.extract_functions("dummy.py", code_content)
calls = parser.extract_calls("dummy.py", code_content)
```

### 3. 错误处理

```python
try:
    parser = ASTParserFactory.create("python")
    functions = parser.extract_functions("nonexistent.py")
except ValueError as e:
    print(f"解析失败: {e}")
```

## 实现指南

要创建新的语言解析器，需要：

1. **继承BaseParser**
```python
class MyLanguageParser(BaseParser):
    def __init__(self):
        # 初始化语言特定的解析器
        pass
```

2. **实现抽象方法**
```python
def _parse_code(self, code: str) -> Tree:
    # 实现代码解析逻辑
    pass

def extract_functions(self, file_path: str, file_content: Optional[str] = None) -> List[Function]:
    # 实现函数提取逻辑
    pass

def extract_calls(self, file_path: str, file_content: Optional[str] = None) -> List[FunctionCall]:
    # 实现调用提取逻辑
    pass

def find_parent_element(self, file_path: str, start_line: int, end_line: int, 
                       file_content: Optional[str] = None) -> Optional[Function]:
    # 实现父级元素查找逻辑
    pass
```

3. **可选：重写具体方法**
```python
def extract_macros(self, file_path: str, file_content: Optional[str] = None) -> List[Macro]:
    # 实现宏定义提取（如果语言支持）
    pass
```

## 性能考虑

- **文件读取**: 支持多种编码格式，自动尝试UTF-8、GB2312、GBK
- **缓存机制**: 解析器实例通常会被缓存以提高性能
- **内存使用**: 大文件解析时注意内存使用情况
- **错误恢复**: 解析失败时会抛出异常，需要适当的错误处理

## 相关类

- [Function](function_model.md) - 函数模型
- [FunctionCall](call_relation_model.md) - 函数调用模型
- [Macro](macro_model.md) - 宏定义模型
- [ASTParserFactory](ast_parser_factory.md) - 解析器工厂 