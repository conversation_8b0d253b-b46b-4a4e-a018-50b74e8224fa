# AST解析器工厂 (ASTParserFactory)

`ASTParserFactory` 是创建和管理AST解析器实例的工厂类，支持多语言解析器的创建和缓存。

## 类定义

```python
class ASTParserFactory:
    """AST解析器工厂类"""
```

## 类属性

- `_parser_cache: dict[str, BaseParser]` - 解析器实例缓存
- `_parser_lock: Lock` - 线程锁，确保线程安全

## 类方法

### create()

创建或获取缓存的AST解析器实例。

```python
@classmethod
def create(cls, language: str) -> BaseParser:
    """
    根据语言名创建或获取缓存的 AST 解析器实例。
    支持线程安全。
    
    Args:
        language: 编程语言名称 ("python", "c", "np")
        
    Returns:
        BaseParser: 对应语言的解析器实例
        
    Raises:
        ValueError: 不支持的语言类型
    """
```

**参数:**
- `language: str` - 编程语言名称，支持的值：
  - `"python"` - Python语言解析器
  - `"c"` - C语言解析器  
  - `"np"` - NP语言解析器

**返回值:**
- `BaseParser` - 对应语言的解析器实例

**异常:**
- `ValueError` - 当指定的语言不被支持时抛出

**示例:**
```python
from code_analyzer import ASTParserFactory

# 创建Python解析器
python_parser = ASTParserFactory.create("python")

# 创建C解析器
c_parser = ASTParserFactory.create("c")

# 创建NP解析器
np_parser = ASTParserFactory.create("np")

# 错误示例 - 不支持的语言
try:
    parser = ASTParserFactory.create("java")
except ValueError as e:
    print(f"错误: {e}")  # 错误: 不支持的语言类型: 'java'
```

### clear_cache()

清空解析器缓存。

```python
@classmethod
def clear_cache(cls):
    """
    清空解析器缓存。
    线程安全。
    """
```

**示例:**
```python
# 清空缓存
ASTParserFactory.clear_cache()
```

## 使用模式

### 1. 基本使用

```python
from code_analyzer import ASTParserFactory

# 创建解析器
parser = ASTParserFactory.create("python")

# 分析代码
functions = parser.extract_functions("example.py")
calls = parser.extract_calls("example.py")
```

### 2. 多语言分析

```python
languages = ["python", "c", "np"]
parsers = {}

for lang in languages:
    try:
        parsers[lang] = ASTParserFactory.create(lang)
        print(f"✓ {lang} 解析器创建成功")
    except ValueError as e:
        print(f"✗ {lang} 解析器创建失败: {e}")
```

### 3. 线程安全使用

```python
import threading
from code_analyzer import ASTParserFactory

def analyze_file(language, file_path):
    parser = ASTParserFactory.create(language)
    functions = parser.extract_functions(file_path)
    return len(functions)

# 多线程安全使用
threads = []
for lang in ["python", "c"]:
    thread = threading.Thread(
        target=analyze_file, 
        args=(lang, f"example.{lang}")
    )
    threads.append(thread)
    thread.start()

for thread in threads:
    thread.join()
```

## 缓存机制

`ASTParserFactory` 使用缓存机制来提高性能：

- **缓存策略**: 单例模式，每种语言只创建一个解析器实例
- **线程安全**: 使用锁机制确保多线程环境下的安全性
- **内存管理**: 解析器实例会一直保存在内存中，直到调用 `clear_cache()`

## 扩展支持

要添加新的语言支持，需要：

1. 在 `parsers/` 目录下创建新的解析器类
2. 继承 `BaseParser` 并实现必要的方法
3. 在 `create()` 方法中添加新的语言分支

**示例 - 添加Java支持:**
```python
# 在 create() 方法中添加
elif language == "java":
    parser = JavaParser()
```

## 性能考虑

- **首次创建**: 解析器首次创建时会有一定的初始化开销
- **缓存命中**: 后续使用相同语言的解析器会直接从缓存获取，性能更好
- **内存占用**: 每种语言的解析器会占用一定的内存空间
- **线程开销**: 多线程环境下会有轻微的锁竞争开销

## 错误处理

```python
try:
    parser = ASTParserFactory.create("unknown_lang")
except ValueError as e:
    print(f"不支持的语言: {e}")
    # 使用默认解析器或处理错误
```

## 相关类

- [BaseParser](base_parser.md) - 解析器基类
- [PythonParser](python_parser.md) - Python解析器
- [CParser](c_parser.md) - C解析器  
- [NPParser](np_parser.md) - NP解析器 