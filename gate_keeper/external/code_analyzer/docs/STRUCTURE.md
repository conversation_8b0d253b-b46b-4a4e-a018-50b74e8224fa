# Code Analyzer 文档结构概览

## 📁 文档目录结构

```
docs/
├── README.md                           # 文档中心主页
├── INDEX.md                            # 文档索引导航
├── QUICKSTART.md                       # 快速开始指南
├── STRUCTURE.md                        # 本文档 - 结构概览
├── installation.md                     # 安装指南
├── DOCUMENTATION_SUMMARY.md            # 文档总结
├── STRUCT_FEATURE_SUMMARY.md           # 结构体功能总结
│
├── design/                             # 设计文档
│   └── ENHANCED_SFT_DATA_DESIGN.md    # 增强SFT数据设计规范 (v2)
│
├── guides/                             # 使用指南
│   ├── SFT_DATA_GENERATION_GUIDE.md   # SFT数据生成使用指南
│   ├── MIGRATION_GUIDE.md             # 迁移指南
│   └── REFACTORING_SUMMARY.md         # 重构总结
│
├── task/                               # 任务文档
│   └── SFT_DATA_GENERATION_SUMMARY.md # SFT数据生成总结
│
├── api/                                # API文档
│   ├── README.md                      # API文档主页
│   ├── ast_parser_factory.md          # AST解析器工厂
│   ├── base_parser.md                 # 解析器基类
│   └── function_model.md              # 函数模型
│
└── parsers/                           # 解析器文档
    ├── README.md                      # 解析器概览
    └── NP_PARSER_README.md            # NP解析器详细说明
```

## 📚 文档分类说明

### 🏗️ 设计文档 (`design/`)
**用途**: 系统架构设计和技术规范
**目标读者**: 架构师、核心开发者

| 文档 | 内容 | 重要性 |
|------|------|--------|
| `ENHANCED_SFT_DATA_DESIGN.md` | SFT数据生成系统v2设计规范 | ⭐⭐⭐⭐⭐ |

### 📖 使用指南 (`guides/`)
**用途**: 用户使用指南和操作手册
**目标读者**: 用户、开发者

| 文档 | 内容 | 重要性 |
|------|------|--------|
| `SFT_DATA_GENERATION_GUIDE.md` | SFT数据生成使用指南 | ⭐⭐⭐⭐⭐ |
| `MIGRATION_GUIDE.md` | 版本迁移说明 | ⭐⭐⭐ |
| `REFACTORING_SUMMARY.md` | 项目重构历史 | ⭐⭐ |

### 📋 任务文档 (`task/`)
**用途**: 项目任务总结和实现细节
**目标读者**: 开发者、维护者

| 文档 | 内容 | 重要性 |
|------|------|--------|
| `SFT_DATA_GENERATION_SUMMARY.md` | SFT数据生成系统实现总结 | ⭐⭐⭐⭐ |

### 🔧 技术文档
**用途**: 技术实现细节和API参考
**目标读者**: 开发者

#### API文档 (`api/`)
| 文档 | 内容 | 重要性 |
|------|------|--------|
| `README.md` | API文档主页 | ⭐⭐⭐⭐ |
| `ast_parser_factory.md` | AST解析器工厂 | ⭐⭐⭐ |
| `base_parser.md` | 解析器基类 | ⭐⭐⭐ |
| `function_model.md` | 函数模型 | ⭐⭐⭐ |

#### 解析器文档 (`parsers/`)
| 文档 | 内容 | 重要性 |
|------|------|--------|
| `README.md` | 解析器概览 | ⭐⭐⭐⭐ |
| `NP_PARSER_README.md` | NP解析器详细说明 | ⭐⭐⭐ |

### 📄 根目录文档
**用途**: 导航和快速参考
**目标读者**: 所有用户

| 文档 | 内容 | 重要性 |
|------|------|--------|
| `README.md` | 文档中心主页 | ⭐⭐⭐⭐⭐ |
| `INDEX.md` | 文档索引导航 | ⭐⭐⭐⭐ |
| `QUICKSTART.md` | 快速开始指南 | ⭐⭐⭐⭐⭐ |
| `installation.md` | 安装指南 | ⭐⭐⭐⭐ |
| `STRUCT_FEATURE_SUMMARY.md` | 结构体功能总结 | ⭐⭐⭐ |
| `DOCUMENTATION_SUMMARY.md` | 文档总结 | ⭐⭐ |

## 🎯 文档使用路径

### 🚀 新用户路径
1. `QUICKSTART.md` - 5分钟快速上手
2. `installation.md` - 安装配置
3. `guides/SFT_DATA_GENERATION_GUIDE.md` - 详细使用指南
4. `examples/` - 查看示例代码

### 🔧 开发者路径
1. `design/ENHANCED_SFT_DATA_DESIGN.md` - 系统设计
2. `api/README.md` - API参考
3. `parsers/README.md` - 解析器文档
4. `task/SFT_DATA_GENERATION_SUMMARY.md` - 实现总结

### 📊 维护者路径
1. `task/SFT_DATA_GENERATION_SUMMARY.md` - 实现总结
2. `guides/REFACTORING_SUMMARY.md` - 重构历史
3. `guides/MIGRATION_GUIDE.md` - 迁移指南
4. `api/` - API文档

## 📋 文档维护规范

### 文档更新原则
1. **及时性**: 代码更新时同步更新文档
2. **准确性**: 确保文档内容与代码一致
3. **完整性**: 覆盖所有重要功能和API
4. **可读性**: 使用清晰的格式和示例

### 文档版本控制
- **v2.0.0**: 当前版本，包含所有v2功能
- **v1.0.0**: 基础版本，保留兼容性文档

### 文档质量检查
- ✅ 链接有效性检查
- ✅ 代码示例可运行性
- ✅ 格式一致性检查
- ✅ 内容完整性验证

## 🔍 文档搜索指南

### 按功能搜索
- **安装配置**: `installation.md`
- **快速开始**: `QUICKSTART.md`
- **SFT数据生成**: `guides/SFT_DATA_GENERATION_GUIDE.md`
- **系统设计**: `design/ENHANCED_SFT_DATA_DESIGN.md`
- **API参考**: `api/README.md`
- **解析器**: `parsers/README.md`

### 按关键词搜索
- **Tree-sitter**: `parsers/`, `api/base_parser.md`
- **代码图**: `design/ENHANCED_SFT_DATA_DESIGN.md`
- **控制流**: `design/ENHANCED_SFT_DATA_DESIGN.md`
- **注释处理**: `design/ENHANCED_SFT_DATA_DESIGN.md`
- **依赖图**: `design/ENHANCED_SFT_DATA_DESIGN.md`

## 📞 文档支持

### 获取帮助
1. 查看 [文档索引](INDEX.md)
2. 搜索 [快速开始指南](QUICKSTART.md)
3. 阅读 [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)

### 贡献文档
1. 遵循现有文档格式
2. 更新相关索引文件
3. 确保链接有效性
4. 添加适当的示例代码

---

**文档结构概览** - 了解Code Analyzer文档组织 📚

**最后更新**: 2024年12月 | **版本**: v2.0.0 