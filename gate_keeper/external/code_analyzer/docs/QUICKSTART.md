# Code Analyzer 快速开始指南

## 🚀 5分钟快速上手

### 1. 安装项目

```bash
# 克隆项目
git clone <repository-url>
cd code_analyzer

# 安装依赖
pip install -e .
```

### 2. 运行示例

```bash
# 运行NP项目分析示例
python examples/np_real_analysis.py
```

### 3. 查看结果

生成的数据文件位于 `real_analysis_output/` 目录：
- `enhanced_sft_data.json` - 增强SFT训练数据
- `real_functions.json` - 函数分析结果
- `real_code_graph.json` - 代码图信息

## 📝 基本使用

### 分析NP项目

```python
from examples.np_real_analysis import RealNPAnalyzer

# 初始化分析器
analyzer = RealNPAnalyzer("examples/np")

# 分析项目
analyzer.analyze_project()

# 生成SFT数据
enhanced_data = analyzer.generate_enhanced_sft_data(max_samples=20)

# 保存结果
analyzer.save_real_results("output")
```

### 查看生成的数据

```python
import json

# 读取增强SFT数据
with open("real_analysis_output/enhanced_sft_data.json", "r") as f:
    data = json.load(f)

# 查看第一个样本
sample = data[0]
print(f"任务类型: {sample['task_type']}")
print(f"遮盖策略: {sample['strategy']}")
print(f"函数名: {sample['function_name']}")
print(f"控制流: {sample['is_control_flow']}")
print(f"注释提示: {sample['comment_hint']}")
```

## 🎯 主要功能

### 代码分析
- ✅ **多语言支持**: Python、C、NP语言
- ✅ **函数提取**: 自动提取函数定义和签名
- ✅ **调用关系**: 构建函数调用关系图
- ✅ **结构体分析**: 分析结构体定义和使用

### SFT数据生成 (v2)
- ✅ **瀑布式遮盖**: 模拟程序员逐行编写代码
- ✅ **随机遮盖**: 模拟代码编辑行为
- ✅ **控制流检测**: 自动识别控制流语句
- ✅ **注释处理**: 智能处理各种注释类型
- ✅ **依赖图构建**: 构建显式依赖关系图

## 📊 数据格式

### 增强SFT数据示例

```json
{
  "task_type": "code_completion",
  "strategy": "waterfall_sequential",
  "mask_level": "statement",
  "filepath": "examples/np/ipu_usertrace.asm",
  "function_name": "iIpuUsrTrcOutIpV6Judge",
  "line_number": 7,
  "before": "void iIpuUsrTrcOutIpV6Judge(uint8 IsInPae)\n...",
  "expected": "UPF_ZERO_COMMON_32BIT_DATA(rsUpfIpPktL3Info)  // 调用UpfParseIpPkt前显式清空解析结果",
  "after": "UPF_ZERO_COMMON_128BIT_DATA(rsUpfIpPktL4Info)...",
  "ast_node_type": "statement_with_comment",
  "is_control_flow": false,
  "comment_hint": true,
  "complexity_score": 0.5,
  "dependency_graph": {
    "nodes": [
      {"id": "fn_iIpuUsrTrcOutIpV6Judge", "type": "function"},
      {"id": "var_rsUpfIpPktL3Info", "type": "variable"}
    ],
    "edges": [
      {"source": "fn_iIpuUsrTrcOutIpV6Judge", "target": "var_rsUpfIpPktL3Info", "relation": "uses"}
    ]
  }
}
```

## 🔧 常用参数

### 数据生成参数
| 参数 | 说明 | 默认值 | 推荐值 |
|------|------|--------|--------|
| `max_samples` | 最大生成样本数量 | 10 | 20-100 |
| `max_depth` | 调用链深度 | 5 | 3-5 |
| `complexity_threshold` | 复杂度阈值 | 0.3 | 0.1-0.5 |

### 遮盖策略参数
| 参数 | 说明 | 默认值 | 推荐值 |
|------|------|--------|--------|
| `waterfall_ratio` | 瀑布式遮盖比例 | 0.6 | 0.5-0.7 |
| `random_ratio` | 随机遮盖比例 | 0.4 | 0.3-0.5 |
| `subexpression_prob` | 子表达式遮盖概率 | 0.3 | 0.2-0.4 |

## 🎯 应用场景

### 代码补全训练
```python
# 生成代码补全训练数据
analyzer = RealNPAnalyzer("your_project")
analyzer.analyze_project()
completion_data = analyzer.generate_enhanced_sft_data(
    max_samples=100,
    strategy="waterfall_sequential"
)
```

### 代码编辑训练
```python
# 生成代码编辑训练数据
edit_data = analyzer.generate_enhanced_sft_data(
    max_samples=50,
    strategy="random_inplace"
)
```

### 代码理解训练
```python
# 获取代码图信息
graph_data = analyzer._call_graph
dependency_data = analyzer._struct_usage_map
```

## 🚨 常见问题

### Q: 安装失败怎么办？
A: 确保Python版本为3.8+，并检查依赖安装：
```bash
pip install -r requirements.txt
```

### Q: 解析器不支持我的语言？
A: 目前支持Python、C、NP语言，其他语言需要扩展解析器。

### Q: 生成的数据质量不高？
A: 调整参数设置：
```python
enhanced_data = analyzer.generate_enhanced_sft_data(
    max_samples=50,
    complexity_threshold=0.2
)
```

### Q: 如何处理大项目？
A: 分批处理或增加内存：
```python
# 分批处理
for batch in project_batches:
    analyzer = RealNPAnalyzer(batch)
    data = analyzer.generate_enhanced_sft_data(max_samples=20)
```

## 📚 更多资源

- **[完整文档](README.md)** - 详细的使用指南
- **[API文档](api/)** - 完整的API参考
- **[设计文档](design/)** - 系统设计和技术规范
- **[示例代码](examples/)** - 更多使用示例

## 🤝 获取帮助

- 查看 [使用指南](guides/SFT_DATA_GENERATION_GUIDE.md)
- 检查 [常见问题](guides/SFT_DATA_GENERATION_GUIDE.md#常见问题)
- 在 [GitHub Issues](../../issues) 中搜索或创建问题

---

**快速开始指南** - 5分钟上手 Code Analyzer ⚡

**最后更新**: 2024年12月 | **版本**: v2.0.0 