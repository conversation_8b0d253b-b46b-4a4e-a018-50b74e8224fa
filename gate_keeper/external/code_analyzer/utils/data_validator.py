#!/usr/bin/env python3
"""
SFT Data Validator

SFT数据验证工具
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple


class SFTDataValidator:
    """SFT数据验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.validation_errors = []
        self.validation_warnings = []
        self.stats = {
            'total_samples': 0,
            'valid_samples': 0,
            'invalid_samples': 0,
            'warnings': 0
        }
    
    def validate_enhanced_sft_data(self, data_file: str) -> bool:
        """验证增强SFT数据文件"""
        print(f"🔍 验证SFT数据文件: {data_file}")
        
        if not os.path.exists(data_file):
            self.validation_errors.append(f"文件不存在: {data_file}")
            return False
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            self.validation_errors.append(f"JSON解析错误: {e}")
            return False
        except Exception as e:
            self.validation_errors.append(f"文件读取错误: {e}")
            return False
        
        if not isinstance(data, list):
            self.validation_errors.append("数据必须是列表格式")
            return False
        
        self.stats['total_samples'] = len(data)
        
        # 验证每个样本
        for i, sample in enumerate(data):
            is_valid = self._validate_single_sample(sample, i)
            if is_valid:
                self.stats['valid_samples'] += 1
            else:
                self.stats['invalid_samples'] += 1
        
        return len(self.validation_errors) == 0
    
    def _validate_single_sample(self, sample: Dict[str, Any], index: int) -> bool:
        """验证单个数据样本"""
        is_valid = True
        
        # 验证顶层结构
        if not self._validate_top_level_structure(sample, index):
            is_valid = False
        
        # 验证metadata
        if not self._validate_metadata(sample.get('metadata', {}), index):
            is_valid = False
        
        # 验证context_nodes
        if not self._validate_context_nodes(sample.get('context_nodes', []), index):
            is_valid = False
        
        # 验证dependency_graph
        if not self._validate_dependency_graph(sample.get('dependency_graph', {}), index):
            is_valid = False
        
        return is_valid
    
    def _validate_top_level_structure(self, sample: Dict[str, Any], index: int) -> bool:
        """验证顶层结构"""
        required_fields = ['before', 'expected', 'after', 'context_nodes', 'dependency_graph', 'metadata']
        
        for field in required_fields:
            if field not in sample:
                self.validation_errors.append(f"样本 {index}: 缺少必需字段 '{field}'")
                return False
        
        # 验证字段类型
        if not isinstance(sample['before'], str):
            self.validation_errors.append(f"样本 {index}: 'before' 必须是字符串")
            return False
        
        if not isinstance(sample['expected'], str):
            self.validation_errors.append(f"样本 {index}: 'expected' 必须是字符串")
            return False
        
        if not isinstance(sample['after'], str):
            self.validation_errors.append(f"样本 {index}: 'after' 必须是字符串")
            return False
        
        if not isinstance(sample['context_nodes'], list):
            self.validation_errors.append(f"样本 {index}: 'context_nodes' 必须是列表")
            return False
        
        if not isinstance(sample['dependency_graph'], dict):
            self.validation_errors.append(f"样本 {index}: 'dependency_graph' 必须是字典")
            return False
        
        if not isinstance(sample['metadata'], dict):
            self.validation_errors.append(f"样本 {index}: 'metadata' 必须是字典")
            return False
        
        return True
    
    def _validate_metadata(self, metadata: Dict[str, Any], index: int) -> bool:
        """验证metadata字段"""
        required_fields = [
            'task_type', 'strategy', 'mask_level', 'function_name',
            'filepath', 'line_number', 'ast_node_type', 'is_control_flow',
            'comment_hint', 'complexity_score', 'data_id', 'source_type',
            'author', 'version', 'seed', 'masking_distribution', 'generation_time'
        ]
        
        for field in required_fields:
            if field not in metadata:
                self.validation_errors.append(f"样本 {index}: metadata缺少必需字段 '{field}'")
                return False
        
        # 验证字段值
        valid_task_types = ['code_completion', 'code_edit']
        if metadata['task_type'] not in valid_task_types:
            self.validation_errors.append(f"样本 {index}: 无效的task_type: {metadata['task_type']}")
            return False
        
        valid_strategies = ['waterfall_sequential', 'random_inplace']
        if metadata['strategy'] not in valid_strategies:
            self.validation_errors.append(f"样本 {index}: 无效的strategy: {metadata['strategy']}")
            return False
        
        valid_mask_levels = ['statement', 'subexpression', 'comment']
        if metadata['mask_level'] not in valid_mask_levels:
            self.validation_errors.append(f"样本 {index}: 无效的mask_level: {metadata['mask_level']}")
            return False
        
        if not isinstance(metadata['line_number'], int):
            self.validation_errors.append(f"样本 {index}: line_number必须是整数")
            return False
        
        if not isinstance(metadata['is_control_flow'], bool):
            self.validation_errors.append(f"样本 {index}: is_control_flow必须是布尔值")
            return False
        
        if not isinstance(metadata['comment_hint'], bool):
            self.validation_errors.append(f"样本 {index}: comment_hint必须是布尔值")
            return False
        
        if not isinstance(metadata['complexity_score'], (int, float)):
            self.validation_errors.append(f"样本 {index}: complexity_score必须是数字")
            return False
        
        if not isinstance(metadata['seed'], int):
            self.validation_errors.append(f"样本 {index}: seed必须是整数")
            return False
        
        # 验证版本号
        if metadata['version'] != 'v2.3':
            self.validation_warnings.append(f"样本 {index}: 版本号不是v2.3: {metadata['version']}")
            self.stats['warnings'] += 1
        
        return True
    
    def _validate_context_nodes(self, context_nodes: List[Dict[str, Any]], index: int) -> bool:
        """验证context_nodes"""
        if not context_nodes:
            self.validation_warnings.append(f"样本 {index}: context_nodes为空")
            self.stats['warnings'] += 1
            return True  # 允许为空，但发出警告
        
        for i, node in enumerate(context_nodes):
            if not isinstance(node, dict):
                self.validation_errors.append(f"样本 {index}: context_nodes[{i}] 必须是字典")
                return False
            
            required_fields = ['node_type', 'name', 'filepath', 'code']
            for field in required_fields:
                if field not in node:
                    self.validation_errors.append(f"样本 {index}: context_nodes[{i}] 缺少字段 '{field}'")
                    return False
            
            valid_node_types = ['function', 'variable', 'macro', 'struct']
            if node['node_type'] not in valid_node_types:
                self.validation_errors.append(f"样本 {index}: context_nodes[{i}] 无效的node_type: {node['node_type']}")
                return False
        
        return True
    
    def _validate_dependency_graph(self, graph: Dict[str, Any], index: int) -> bool:
        """验证dependency_graph"""
        if not isinstance(graph, dict):
            self.validation_errors.append(f"样本 {index}: dependency_graph必须是字典")
            return False
        
        if 'nodes' not in graph or 'edges' not in graph:
            self.validation_errors.append(f"样本 {index}: dependency_graph缺少nodes或edges字段")
            return False
        
        if not isinstance(graph['nodes'], list):
            self.validation_errors.append(f"样本 {index}: dependency_graph.nodes必须是列表")
            return False
        
        if not isinstance(graph['edges'], list):
            self.validation_errors.append(f"样本 {index}: dependency_graph.edges必须是列表")
            return False
        
        # 验证节点
        for i, node in enumerate(graph['nodes']):
            if not isinstance(node, dict):
                self.validation_errors.append(f"样本 {index}: dependency_graph.nodes[{i}] 必须是字典")
                return False
            
            if 'id' not in node or 'type' not in node:
                self.validation_errors.append(f"样本 {index}: dependency_graph.nodes[{i}] 缺少id或type字段")
                return False
        
        # 验证边
        for i, edge in enumerate(graph['edges']):
            if not isinstance(edge, dict):
                self.validation_errors.append(f"样本 {index}: dependency_graph.edges[{i}] 必须是字典")
                return False
            
            if 'source' not in edge or 'target' not in edge or 'relation' not in edge:
                self.validation_errors.append(f"样本 {index}: dependency_graph.edges[{i}] 缺少必需字段")
                return False
        
        return True
    
    def print_validation_report(self):
        """打印验证报告"""
        print("\n" + "=" * 60)
        print("📊 SFT数据验证报告")
        print("=" * 60)
        
        print(f"总样本数: {self.stats['total_samples']}")
        print(f"有效样本: {self.stats['valid_samples']}")
        print(f"无效样本: {self.stats['invalid_samples']}")
        print(f"警告数量: {self.stats['warnings']}")
        
        if self.validation_errors:
            print(f"\n❌ 验证错误 ({len(self.validation_errors)}个):")
            for error in self.validation_errors[:10]:  # 只显示前10个错误
                print(f"  - {error}")
            if len(self.validation_errors) > 10:
                print(f"  ... 还有 {len(self.validation_errors) - 10} 个错误")
        
        if self.validation_warnings:
            print(f"\n⚠️  验证警告 ({len(self.validation_warnings)}个):")
            for warning in self.validation_warnings[:5]:  # 只显示前5个警告
                print(f"  - {warning}")
            if len(self.validation_warnings) > 5:
                print(f"  ... 还有 {len(self.validation_warnings) - 5} 个警告")
        
        if not self.validation_errors and not self.validation_warnings:
            print("\n✅ 所有数据验证通过！")
        elif not self.validation_errors:
            print("\n✅ 数据格式正确，但有警告需要关注")
        else:
            print("\n❌ 数据格式存在问题，需要修复")


def validate_sft_data_file(data_file: str) -> bool:
    """验证SFT数据文件的便捷函数"""
    validator = SFTDataValidator()
    is_valid = validator.validate_enhanced_sft_data(data_file)
    validator.print_validation_report()
    return is_valid


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python data_validator.py <data_file>")
        sys.exit(1)
    
    data_file = sys.argv[1]
    is_valid = validate_sft_data_file(data_file)
    sys.exit(0 if is_valid else 1)


if __name__ == '__main__':
    main() 