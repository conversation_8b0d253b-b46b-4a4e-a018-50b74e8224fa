"""
Path Utils

路径处理工具模块
"""

import os
from pathlib import Path
from typing import Union


def normalize_path(path: Union[str, Path]) -> str:
    """
    标准化路径，统一使用正斜杠
    
    Args:
        path: 路径
        
    Returns:
        标准化后的路径字符串
    """
    return str(Path(path)).replace('\\', '/')


def to_relative_path(path: Union[str, Path], base_dir: Union[str, Path]) -> str:
    """
    将路径转换为相对于基目录的相对路径
    
    Args:
        path: 目标路径
        base_dir: 基目录
        
    Returns:
        相对路径字符串
    """
    try:
        path = Path(path).resolve()
        base_dir = Path(base_dir).resolve()
        rel_path = path.relative_to(base_dir)
        return normalize_path(rel_path)
    except ValueError:
        # 如果路径不在基目录内，返回标准化后的原始路径
        return normalize_path(path)


def to_absolute_path(rel_path: str, base_dir: Union[str, Path]) -> str:
    """
    将相对路径转换为绝对路径
    
    Args:
        rel_path: 相对路径
        base_dir: 基目录
        
    Returns:
        绝对路径字符串
    """
    normalized_rel_path = normalize_path(rel_path)
    abs_path = Path(base_dir) / normalized_rel_path
    return normalize_path(abs_path.resolve())


def get_node_id(file_path: str, func_name: str) -> str:
    """
    生成函数节点ID，屏蔽平台差异
    
    Args:
        file_path: 文件路径
        func_name: 函数名
        
    Returns:
        节点ID字符串
    """
    if not file_path:
        file_path = "<external>"
    elif not file_path == "<external>":
        # 标准化路径
        file_path = normalize_path(file_path)
    
    return f"{file_path}::{func_name}" 