"""
文件工具模块

提供健壮的文件读取功能，自动处理编码问题
"""

import os
from pathlib import Path
from typing import List, Optional, Tuple

import chardet

# 语言映射常量
LANGUAGE_MAP = {
    'python': ['.py'],
    'c': ['.c', '.h'],
    'cpp': ['.cpp', '.cc', '.cxx', '.hpp'],
    'np': ['.asm', '.s'],  # 修改：assembly -> np
    'java': ['.java'],
    'javascript': ['.js'],
    'typescript': ['.ts'],
    'go': ['.go'],
    'rust': ['.rs'],
    'ruby': ['.rb'],
    'php': ['.php'],
    'csharp': ['.cs'],
    'swift': ['.swift'],
    'kotlin': ['.kt'],
    'scala': ['.scala'],
    'clojure': ['.clj'],
    'haskell': ['.hs'],
    'ocaml': ['.ml'],
    'fsharp': ['.fs'],
    'r': ['.r'],
    'matlab': ['.m'],
    'perl': ['.pl'],
    'bash': ['.sh'],
    'sql': ['.sql'],
    'html': ['.html'],
    'css': ['.css'],
    'xml': ['.xml'],
    'json': ['.json'],
    'yaml': ['.yaml', '.yml'],
    'toml': ['.toml'],
    'ini': ['.ini', '.cfg', '.conf'],
    'markdown': ['.md'],
    'rst': ['.rst'],
    'latex': ['.tex'],
}


def read_file_with_encoding_detection(file_path: str, fallback_encodings: List[str] = None) -> Tuple[str, str]:
    """
    使用编码检测读取文件，自动处理编码问题
    
    Args:
        file_path: 文件路径
        fallback_encodings: 备用编码列表，如果自动检测失败则尝试这些编码
        
    Returns:
        (文件内容, 检测到的编码)
        
    Raises:
        Exception: 如果所有编码都失败
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    # 默认备用编码列表
    if fallback_encodings is None:
        fallback_encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin-1', 'cp1252']
    
    # 读取文件字节内容
    with open(file_path, 'rb') as f:
        raw_data = f.read()
    
    # 使用chardet检测编码
    try:
        detection_result = chardet.detect(raw_data)
        detected_encoding = detection_result['encoding']
        confidence = detection_result['confidence']
        
        # 如果检测置信度较高，尝试使用检测到的编码
        if detected_encoding and confidence > 0.7:
            try:
                content = raw_data.decode(detected_encoding)
                return content, detected_encoding
            except (UnicodeDecodeError, LookupError):
                pass  # 检测到的编码失败，继续尝试备用编码
    except Exception:
        pass  # chardet检测失败，继续尝试备用编码
    
    # 尝试备用编码列表
    for encoding in fallback_encodings:
        try:
            content = raw_data.decode(encoding)
            return content, encoding
        except (UnicodeDecodeError, LookupError):
            continue
    
    # 最后尝试使用UTF-8 with errors='replace'
    try:
        content = raw_data.decode('utf-8', errors='replace')
        return content, 'utf-8 (with replacement)'
    except Exception as e:
        raise Exception(f"无法读取文件 {file_path}，所有编码尝试都失败: {str(e)}")


def read_file_safe(file_path: str, default_encoding: str = 'utf-8') -> str:
    """
    安全读取文件，自动处理编码问题
    
    Args:
        file_path: 文件路径
        default_encoding: 默认编码
        
    Returns:
        文件内容（UTF-8编码）
    """
    content, detected_encoding = read_file_with_encoding_detection(file_path)
    
    # 如果检测到的编码不是UTF-8，需要重新读取并转换
    if detected_encoding.lower() not in ['utf-8', 'utf8']:
        try:
            # 重新读取文件字节内容
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # 使用检测到的编码解码
            content = raw_data.decode(detected_encoding, errors='replace')
            
        except Exception:
            # 如果转换失败，直接使用UTF-8编码
            content = content.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
    
    return content


def write_file_utf8(file_path: str, content: str) -> None:
    """
    使用UTF-8编码写入文件
    
    Args:
        file_path: 文件路径
        content: 要写入的内容
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 使用UTF-8编码写入
    with open(file_path, 'w', encoding='utf-8', errors='ignore') as f:
        f.write(content)


def get_file_encoding_info(file_path: str) -> dict:
    """
    获取文件编码信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        编码信息字典
    """
    if not os.path.exists(file_path):
        return {
            'encoding': 'unknown',
            'confidence': 0.0,
            'error': '文件不存在'
        }
    
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        
        # 使用chardet检测
        detection_result = chardet.detect(raw_data)
        
        # 验证编码
        detected_encoding = detection_result['encoding']
        confidence = detection_result['confidence']
        
        is_valid = False
        if detected_encoding:
            try:
                raw_data.decode(detected_encoding)
                is_valid = True
            except (UnicodeDecodeError, LookupError):
                pass
        
        return {
            'encoding': detected_encoding or 'unknown',
            'confidence': confidence or 0.0,
            'is_valid': is_valid,
            'file_size': len(raw_data),
            'error': None
        }
        
    except Exception as e:
        return {
            'encoding': 'unknown',
            'confidence': 0.0,
            'is_valid': False,
            'file_size': 0,
            'error': str(e)
        }


def batch_convert_to_utf8(input_dir: str, output_dir: str, file_extensions: List[str] = None) -> List[dict]:
    """
    批量转换文件为UTF-8编码
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        file_extensions: 文件扩展名列表，如果为None则处理所有文件
        
    Returns:
        转换结果列表
    """
    results = []
    
    if not os.path.exists(input_dir):
        return results
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    for root, dirs, files in os.walk(input_dir):
        for file in files:
            # 检查文件扩展名
            if file_extensions:
                file_ext = Path(file).suffix.lower()
                if file_ext not in file_extensions:
                    continue
            
            input_file = os.path.join(root, file)
            
            # 计算相对路径
            rel_path = os.path.relpath(input_file, input_dir)
            output_file = os.path.join(output_dir, rel_path)
            
            try:
                # 读取文件
                content = read_file_safe(input_file)
                
                # 写入UTF-8文件
                write_file_utf8(output_file, content)
                
                results.append({
                    'input_file': input_file,
                    'output_file': output_file,
                    'success': True,
                    'error': None
                })
                
            except Exception as e:
                results.append({
                    'input_file': input_file,
                    'output_file': output_file,
                    'success': False,
                    'error': str(e)
                })
    
    return results


def print_encoding_report(file_path: str) -> None:
    """
    打印文件编码报告
    
    Args:
        file_path: 文件路径
    """
    print(f"📄 文件编码报告: {file_path}")
    print("-" * 60)
    
    encoding_info = get_file_encoding_info(file_path)
    
    print(f"检测到的编码: {encoding_info['encoding']}")
    print(f"置信度: {encoding_info['confidence']:.2f}")
    print(f"是否有效: {'✅ 是' if encoding_info['is_valid'] else '❌ 否'}")
    print(f"文件大小: {encoding_info['file_size']} 字节")
    
    if encoding_info['error']:
        print(f"错误: {encoding_info['error']}")
    
    print()


def collect_source_files(directory: str, extensions: List[str] = None) -> List[str]:
    """
    收集源代码文件
    
    Args:
        directory: 目录路径
        extensions: 文件扩展名列表，如果为None则收集所有文件
        
    Returns:
        文件路径列表（相对于directory的相对路径）
    """
    source_files = []
    
    if not os.path.exists(directory):
        return source_files
    
    directory = Path(directory).resolve()
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if extensions:
                file_ext = Path(file).suffix.lower()
                if file_ext not in extensions:
                    continue
            # 返回相对于directory的相对路径
            file_path = Path(root) / file
            rel_path = file_path.relative_to(directory)
            source_files.append(str(rel_path))
    
    return source_files


def determine_language_by_filename(filename: str) -> str:
    """
    根据文件名确定编程语言
    
    Args:
        filename: 文件名
        
    Returns:
        编程语言名称
    """
    file_ext = Path(filename).suffix.lower()
    
    language_map = {
        '.py': 'python',
        '.c': 'c',
        '.cpp': 'cpp',
        '.cc': 'cpp',
        '.cxx': 'cpp',
        '.h': 'c',
        '.hpp': 'cpp',
        '.asm': 'np',
        '.s': 'np',
        '.java': 'java',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.go': 'go',
        '.rs': 'rust',
        '.rb': 'ruby',
        '.php': 'php',
        '.cs': 'csharp',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.clj': 'clojure',
        '.hs': 'haskell',
        '.ml': 'ocaml',
        '.fs': 'fsharp',
        '.r': 'r',
        '.m': 'matlab',
        '.pl': 'perl',
        '.sh': 'bash',
        '.sql': 'sql',
        '.html': 'html',
        '.css': 'css',
        '.xml': 'xml',
        '.json': 'json',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.toml': 'toml',
        '.ini': 'ini',
        '.cfg': 'ini',
        '.conf': 'ini',
        '.md': 'markdown',
        '.rst': 'rst',
        '.tex': 'latex',
        '.r': 'r',
        '.m': 'matlab',
        '.pl': 'perl',
        '.sh': 'bash',
        '.sql': 'sql',
        '.html': 'html',
        '.css': 'css',
        '.xml': 'xml',
        '.json': 'json',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.toml': 'toml',
        '.ini': 'ini',
        '.cfg': 'ini',
        '.conf': 'ini',
        '.md': 'markdown',
        '.rst': 'rst',
        '.tex': 'latex',
    }
    
    return language_map.get(file_ext, 'unknown') 