"""
Code Analyzer CLI Main

代码分析命令行工具主程序
"""

import argparse
import json
import sys
from pathlib import Path
from typing import List, Optional

from ..core.ast_parser import ASTParserFactory
from ..core.code_graph import CodeGraph
from ..core.repository_index import RepositoryIndex
from ..core.static_analyzer import StaticAnalyzer


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Code Analyzer - 代码分析工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 分析仓库命令
    analyze_parser = subparsers.add_parser("analyze", help="分析代码仓库")
    analyze_parser.add_argument("repo_dir", help="仓库目录")
    analyze_parser.add_argument("--branch", default="main", help="分支名")
    analyze_parser.add_argument("--exclude", nargs="*", help="排除模式")
    analyze_parser.add_argument("--output", help="输出文件")
    
    # 分析文件命令
    file_parser = subparsers.add_parser("file", help="分析单个文件")
    file_parser.add_argument("file_path", help="文件路径")
    file_parser.add_argument("--language", help="编程语言")
    file_parser.add_argument("--output", help="输出文件")
    
    # 调用链分析命令
    chain_parser = subparsers.add_parser("chain", help="分析调用链")
    chain_parser.add_argument("repo_dir", help="仓库目录")
    chain_parser.add_argument("function", help="函数名")
    chain_parser.add_argument("file_path", help="文件路径")
    chain_parser.add_argument("--depth", type=int, default=5, help="最大深度")
    chain_parser.add_argument("--type", choices=["upstream", "downstream", "bidirectional"], 
                             default="bidirectional", help="调用链类型")
    chain_parser.add_argument("--output", help="输出文件")
    
    # 图分析命令
    graph_parser = subparsers.add_parser("graph", help="图分析")
    graph_parser.add_argument("repo_dir", help="仓库目录")
    graph_parser.add_argument("--stats", action="store_true", help="显示统计信息")
    graph_parser.add_argument("--cycles", action="store_true", help="查找循环")
    graph_parser.add_argument("--components", action="store_true", help="强连通分量")
    graph_parser.add_argument("--topological", action="store_true", help="拓扑排序")
    graph_parser.add_argument("--output", help="输出文件")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "analyze":
            analyze_repository(args)
        elif args.command == "file":
            analyze_file(args)
        elif args.command == "chain":
            analyze_call_chain(args)
        elif args.command == "graph":
            analyze_graph(args)
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


def analyze_repository(args):
    """分析仓库"""
    print(f"正在分析仓库: {args.repo_dir}")
    
    repo_index = RepositoryIndex(
        repo_dir=args.repo_dir,
        branch=args.branch,
        exclude_patterns=args.exclude
    )
    repo_index.build()
    
    result = {
        "repository": {
            "dir": args.repo_dir,
            "branch": args.branch,
            "functions": len(repo_index.function_definitions),
            "calls": len(repo_index.function_calls),
            "files": len(set(func.filepath for funcs in repo_index.function_definitions.values() for func in funcs))
        },
        "functions": {}
    }
    
    for func_name, funcs in repo_index.function_definitions.items():
        result["functions"][func_name] = []
        for func in funcs:
            result["functions"][func_name].append({
                "filepath": func.filepath,
                "start_line": func.range.start_line,
                "end_line": func.range.end_line,
                "is_declaration": func.is_declaration
            })
    
    output_result(result, args.output)


def analyze_file(args):
    """分析文件"""
    print(f"正在分析文件: {args.file_path}")
    
    # 确定语言
    if args.language:
        language = args.language
    else:
        from ..utils.file_utils import determine_language_by_filename
        language = determine_language_by_filename(args.file_path)
        if not language:
            print("无法确定文件语言，请使用 --language 参数指定")
            return
    
    parser = ASTParserFactory.create(language)
    functions = parser.extract_functions(args.file_path)
    calls = parser.extract_calls(args.file_path)
    
    result = {
        "file": args.file_path,
        "language": language,
        "functions": [
            {
                "name": func.name,
                "start_line": func.range.start_line,
                "end_line": func.range.end_line,
                "signature": {
                    "name": func.signature.name,
                    "parameters": [{"name": p.name, "type": p.type_hint} for p in func.signature.parameters],
                    "return_type": func.signature.return_type
                }
            }
            for func in functions
        ],
        "calls": [
            {
                "caller": call.caller,
                "callee": call.callee,
                "line": call.line,
                "code": call.code
            }
            for call in calls
        ]
    }
    
    output_result(result, args.output)


def analyze_call_chain(args):
    """分析调用链"""
    print(f"正在分析调用链: {args.function} in {args.file_path}")
    
    repo_index = RepositoryIndex(repo_dir=args.repo_dir)
    repo_index.build()
    
    analyzer = StaticAnalyzer(repo_index)
    
    if args.type == "upstream":
        chains = analyzer.get_upstream_call_chains(args.function, args.file_path, args.depth)
    elif args.type == "downstream":
        chains = analyzer.get_downstream_call_chains(args.function, args.file_path, args.depth)
    else:  # bidirectional
        chains = analyzer.get_bidirectional_call_chains(args.function, args.file_path, args.depth)
    
    result = {
        "function": args.function,
        "file": args.file_path,
        "type": args.type,
        "depth": args.depth,
        "chains": chains
    }
    
    output_result(result, args.output)


def analyze_graph(args):
    """图分析"""
    print(f"正在分析图: {args.repo_dir}")
    
    graph = CodeGraph.build_from_repository(args.repo_dir)
    
    result = {
        "repository": args.repo_dir,
        "statistics": graph.get_graph_statistics()
    }
    
    if args.cycles:
        result["cycles"] = graph.find_cycles()
    
    if args.components:
        result["components"] = [list(comp) for comp in graph.get_strongly_connected_components()]
    
    if args.topological:
        result["topological_sort"] = graph.get_topological_sort()
    
    output_result(result, args.output)


def output_result(result: dict, output_file: Optional[str]):
    """输出结果"""
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {output_file}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main() 