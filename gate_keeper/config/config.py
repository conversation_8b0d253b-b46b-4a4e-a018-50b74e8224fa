import importlib
import os
from pathlib import Path

from dotenv import load_dotenv

load_dotenv()

DEBUG="not_set"

repo_dir="not_set"
repo_project_id="not_set"
repo_url="not_set"
mr_id="not_set"
token="not_set"
devBranch="not_set"
baseBranch="not_set"

project_root_dir="not_set"
dest_dir="not_set"
output_file_name="not_set"
llm_model_id="not_set"
llm_concurrent="not_set"
default_llm_max_tokens="not_set"
log_dir="not_set"
log_level="not_set"
repo_cache_dir="not_set"
max_cache_repos="not_set"
max_cache_branches="not_set"
max_cache_branch_commits="not_set"
rule_file_path="not_set"
rule_include_sheets="not_set"
rule_merge_on="not_set"
rule_file_url="not_set"
git_platform="not_set"
code_check_main_instruction="not_set"
exclude_patterns = []
max_diff_results=None


# 规则分组配置
# 分组策略选项:
# - "category": 按规则类别分组
# - "similarity": 按规则内容相似度分组
# - "adaptive": 自适应分组，综合考虑类别、相似度和组大小
# - "minimize_calls": 最小调用次数分组，专门用于减少LLM调用
rule_grouping_strategy = "minimize_calls"  # 改为minimize_calls策略，减少分组数量
min_rule_group_size = 8          # 增加最小分组大小
max_rule_group_size = 15         # 增加最大分组大小
target_rule_group_size = 10      # 增加目标分组大小
rule_similarity_threshold = 0.7  # 相似度分组阈值 (仅在similarity策略时使用)

# LLM调用次数限制配置
# 1. 任务级限制：整个MR分析任务的总LLM调用次数限制
max_llm_calls_per_task = None    # 每次任务的总LLM调用次数限制，None表示不设限

# 2. 被修改函数级限制：每个被修改函数的LLM调用次数限制
max_llm_calls_per_affected_function = 2   # 每个被修改函数最多允许的LLM调用次数，防止单个函数因规则分组过多导致LLM调用爆炸

# 3. 规则组级限制：每个规则组的LLM调用次数限制
max_llm_calls_per_rule_group = None    # 每个规则组最多允许的LLM调用次数，None表示不设限, 防止调用链过多导致LLM调用爆炸

# 上下文优化配置
use_optimized_context = True     # 是否使用基于调用链的优化上下文（解决上下文膨胀问题）
max_context_size = 8000          # 单个上下文的最大字符数,过滤掉超大函数带来的影响
max_context_chains = 3           # 每个函数最多包含的调用链数量
max_context_chain_depth = 3      # 单条调用链的最大深度（半径）

# 上下文管理器详细配置
# 相关性计算权重配置
context_relevance_weight_chain_length = 0.3      # 调用链长度权重
context_relevance_weight_function_position = 0.3 # 函数位置权重
context_relevance_weight_call_density = 0.2      # 调用密度权重
context_relevance_weight_file_relevance = 0.2    # 文件相关性权重

# 选择策略配置
context_function_selection_strategy = "balanced"  # 函数选择策略: balanced, same_file_first, code_first
context_selection_strategy = "relevance_first"    # 上下文选择策略: relevance_first, size_first, balanced

# 日志配置
context_enable_detailed_logging = False  # 是否启用详细日志
context_log_context_building = True      # 是否记录上下文构建日志



# 过滤配置
context_min_relevance_threshold = 0.1   # 最小相关性阈值
context_max_context_size_ratio = 0.8    # 最大上下文大小比例（相对于总限制）

# 格式化配置
context_include_chain_info = True       # 是否包含调用链信息
context_include_relevance_score = True  # 是否包含相关性分数
context_include_file_paths = True       # 是否包含文件路径


ENV = os.environ.get("GK_ENV", "test")  # 默认使用test环境
env_map = {
    "home": "config_home",
    "dev": "config_dev",
    "prod": "config_prod",
    "test": "config_test"
}

sub_config_name = env_map.get(ENV, "config_dev")
sub_config_module = f"gate_keeper.config.{sub_config_name}"
sub_config = importlib.import_module(sub_config_module)

for k in dir(sub_config):
    if not k.startswith("_"):
        attr_value = getattr(sub_config, k)
        globals()[k] = attr_value