# 开发调试工具

本目录包含用于开发过程中调试和分析的工具。

## 🔧 调试工具

### AST解析调试
- `ast_parser_debug.py` - AST解析器调试工具
- `debug_ast_parser_direct.py` - 直接AST解析调试

### 调用图分析
- `debug_call_graph_building.py` - 调用图构建调试
- `debug_affected_function_name.py` - 受影响函数分析
- `debug_full_dataflow.py` - 完整数据流分析

### 文件和路径调试  
- `debug_specific_file.py` - 特定文件调试
- `debug_mr_info.py` - MR信息调试

### 解析器调试
- `markdown_parser_debug.py` - Markdown解析调试
- `debug_parser_legacy.py` - 旧版解析器调试

### 规则分组调试
- `debug_rule_grouping.py` - 规则分组策略分析和调试
- `simulate_rule_grouping.py` - 规则分组模拟工具

### 系统工具
- `clear_repo_index_cache.py` - 清理仓库索引缓存

## 📚 使用方法

### AST调试
```bash
# 调试AST解析
python tools/development/ast_parser_debug.py

# 直接AST解析测试
python tools/development/debug_ast_parser_direct.py
```

### 调用图调试
```bash
# 调试调用图构建
python tools/development/debug_call_graph_building.py

# 分析受影响的函数
python tools/development/debug_affected_function_name.py
```

### 缓存管理
```bash
# 清理仓库缓存
python tools/development/clear_repo_index_cache.py
```

### 特定问题调试
```bash
# 调试特定文件
python tools/development/debug_specific_file.py

# 调试MR信息
python tools/development/debug_mr_info.py
```

### 规则分组调试
```bash
# 分析当前配置的分组策略
python tools/development/debug_rule_grouping.py

# 对比所有分组策略
python tools/development/debug_rule_grouping.py --strategy all

# 模拟多函数场景
python tools/development/debug_rule_grouping.py --simulate 221

# 详细分析分组情况
python tools/development/debug_rule_grouping.py --detailed

# 输出结果到JSON文件
python tools/development/debug_rule_grouping.py --strategy all --output results.json
```

## 🎯 使用场景

### 开发新功能
1. 使用AST调试工具验证解析逻辑
2. 使用调用图工具分析代码关系
3. 使用缓存清理工具重置状态

### 问题排查
1. 使用特定文件调试工具定位问题
2. 使用数据流分析工具追踪问题
3. 使用解析器调试工具验证解析结果
4. 使用规则分组调试工具分析分组策略效果

### 性能优化
1. 使用调用图分析工具找出性能瓶颈
2. 使用缓存工具管理系统状态
3. 使用规则分组工具优化LLM调用次数

## ⚙️ 开发环境

### 环境要求
- Python 3.11+
- git_keeper虚拟环境
- 项目依赖包

### 调试配置
```python
# 启用调试日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔗 相关文档

- [生产工具](../production/README.md)
- [测试工具](../testing/README.md)
- [项目架构文档](../../docs/design.md) 