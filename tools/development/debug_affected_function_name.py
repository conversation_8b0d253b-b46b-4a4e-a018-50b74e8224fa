#!/usr/bin/env python3
"""
调试AffectedFunction的name属性问题
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.application.service.git import GitService
from gate_keeper.external.code_analyzer import \
    RepositoryIndexFactory
from gate_keeper.infrastructure.git.gitee.client import Gitee


def debug_affected_function_name():
    """调试AffectedFunction的name属性"""
    
    # 使用一个简单的测试文件
    test_file = "test_function_name.py"
    test_content = """
def test_function():
    print("Hello World")
    return True

def another_function(param1, param2):
    result = param1 + param2
    return result
"""
    
    # 创建测试文件
    with open(test_file, "w") as f:
        f.write(test_content)
    
    try:
        # 创建GitService
        git_service = GitService(comment_service=Gitee("dummy_token"))
        
        # 创建RepositoryIndex
        repo_index = RepositoryIndexFactory.get_or_build(
            repo_dir=".",
            git_service=git_service,
            branch="main",
            commit_sha=None
        )
        
        print("=== 检查Function对象的name属性 ===")
        for func_name, func_list in repo_index.function_definitions.items():
            print(f"Function name: '{func_name}'")
            for func in func_list:
                print(f"  - name: '{func.name}'")
                print(f"  - type: {type(func.name)}")
                print(f"  - length: {len(func.name)}")
                print(f"  - start_line: {func.start_line}")
                print(f"  - end_line: {func.end_line}")
                print(f"  - signature: '{func.signature}'")
                print()
        
        print("=== 检查call_graph节点的name属性 ===")
        for node_id, data in repo_index.call_graph.nodes(data=True):
            print(f"Node ID: {node_id}")
            print(f"  - name: '{data.get('name', 'N/A')}'")
            print(f"  - type: {type(data.get('name', 'N/A'))}")
            print(f"  - filepath: {data.get('filepath', 'N/A')}")
            print()
        
        print("=== 测试get_changed_functions ===")
        affected_functions = repo_index.get_changed_functions(
            file_content=test_content,
            file_path=test_file,
            changed_lines=[2, 3, 4]  # 第一行函数的内容
        )
        
        for af in affected_functions:
            print(f"AffectedFunction:")
            print(f"  - name: '{af.name}'")
            print(f"  - type: {type(af.name)}")
            print(f"  - length: {len(af.name)}")
            print(f"  - start_line: {af.start_line}")
            print(f"  - end_line: {af.end_line}")
            print()
            
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

if __name__ == "__main__":
    debug_affected_function_name() 