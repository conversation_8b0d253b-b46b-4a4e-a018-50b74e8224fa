#!/usr/bin/env python3
"""
模拟当前配置下的规则分组情况
用法：python tools/development/simulate_rule_grouping.py [--strategy STRATEGY] [--rule-file RULE_FILE] [--show-ids]
"""
import argparse
import os
import sys
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.config import config
from gate_keeper.external.code_analyzer.models.element import CodeElement


class DummyCheckItem(CodeElement):
    def __init__(self, name="dummy_func", type="function", filepath="dummy.py"):
        super().__init__(name=name, type=type, filepath=filepath)


def main():
    parser = argparse.ArgumentParser(description="模拟当前配置下的规则分组情况")
    parser.add_argument('--strategy', type=str, default=None, help='分组策略（可选，覆盖配置）')
    parser.add_argument('--rule-file', type=str, default=None, help='规则文件路径（可选，覆盖配置）')
    parser.add_argument('--show-ids', action='store_true', help='显示每组规则ID')
    args = parser.parse_args()

    # 环境变量
    env = os.environ.get('GK_ENV', 'test')
    print(f"当前GK_ENV: {env}")
    print(f"分组策略: {args.strategy or config.rule_grouping_strategy}")
    print(f"min_rule_group_size: {getattr(config, 'min_rule_group_size', None)}")
    print(f"max_rule_group_size: {getattr(config, 'max_rule_group_size', None)}")
    print(f"target_rule_group_size: {getattr(config, 'target_rule_group_size', None)}")
    print(f"规则文件: {args.rule_file or getattr(config, 'rule_file_path', None)}")

    # 构造RuleManager
    rule_file = args.rule_file or getattr(config, 'rule_file_path', None)
    if not rule_file or not Path(rule_file).exists():
        print(f"规则文件不存在: {rule_file}")
        sys.exit(1)
    rule_manager = RuleManager(rule_file_path=Path(rule_file))

    # 构造一个虚拟的检查项
    check_item = DummyCheckItem()

    # 获取分组
    grouped = rule_manager.get_applicable_rules(check_item, grouping_strategy=args.strategy)
    total_rules = sum(len(v) for v in grouped.values())
    print(f"\n分组数量: {len(grouped)}")
    print(f"总规则数: {total_rules}")
    print("每组规则数:")
    for i, (group_name, rules) in enumerate(grouped.items()):
        print(f"  组{i+1} [{group_name}]: {len(rules)} 条规则", end='')
        if args.show_ids:
            ids = [getattr(r, 'id', str(r)) for r in rules]
            print(f" -> {ids}")
        else:
            print()

if __name__ == "__main__":
    main() 