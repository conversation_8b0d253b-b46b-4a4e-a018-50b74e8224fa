#!/usr/bin/env python3
"""
调试C语言宏识别功能
"""

import tree_sitter_c as tsc
from tree_sitter import Language, Parser


def debug_c_macro_parsing():
    """调试C语言宏解析"""
    
    # 测试代码包含各种宏定义
    test_code = """
#include <stdio.h>

// 简单宏定义
#define MAX_SIZE 100
#define PI 3.14159

// 带参数的宏
#define SQUARE(x) ((x) * (x))
#define MAX(a, b) ((a) > (b) ? (a) : (b))

// 多行宏定义
#define PRINT_INFO(msg) do { \\
    printf("Info: %s\\n", msg); \\
    printf("File: %s, Line: %d\\n", __FILE__, __LINE__); \\
} while(0)

// 条件编译宏
#ifdef DEBUG
#define DEBUG_PRINT(msg) printf("DEBUG: %s\\n", msg)
#else
#define DEBUG_PRINT(msg)
#endif

// 函数定义
int main() {
    int value = SQUARE(5);
    int max_val = MAX(10, 20);
    
    PRINT_INFO("Hello World");
    DEBUG_PRINT("Debug message");
    
    return 0;
}

// 另一个函数
void test_function() {
    printf("Test function\\n");
}
"""
    
    # 创建解析器
    C_LANGUAGE = Language(tsc.language())
    parser = Parser(C_LANGUAGE)
    
    # 解析代码
    tree = parser.parse(test_code.encode("utf-8"))
    root_node = tree.root_node
    
    print("=== C语言宏识别调试 ===")
    print(f"根节点类型: {root_node.type}")
    print()
    
    def print_node_info(node, indent=0):
        """打印节点信息"""
        node_text = test_code.encode("utf-8")[node.start_byte:node.end_byte].decode("utf-8")
        print("  " * indent + f"{node.type} ({node.start_point} - {node.end_point})")
        if len(node_text.strip()) > 0 and len(node_text.strip()) < 100:
            print("  " * indent + f"  文本: {repr(node_text.strip())}")
        
        for child in node.children:
            print_node_info(child, indent + 1)
    
    # 打印完整的语法树
    print("完整语法树:")
    print_node_info(root_node)
    print()
    
    # 查找宏定义节点
    def find_macro_definitions(node):
        """查找宏定义节点"""
        macros = []
        
        if node.type == "preprocessor_function_def":
            # 函数式宏定义
            name_node = node.child_by_field_name("name")
            if name_node:
                name = test_code.encode("utf-8")[name_node.start_byte:name_node.end_byte].decode("utf-8")
                macros.append(("function_macro", name, node))
        
        elif node.type == "preprocessor_object_def":
            # 对象式宏定义
            name_node = node.child_by_field_name("name")
            if name_node:
                name = test_code.encode("utf-8")[name_node.start_byte:name_node.end_byte].decode("utf-8")
                macros.append(("object_macro", name, node))
        
        elif node.type == "preprocessor_conditional":
            # 条件编译宏
            macros.append(("conditional_macro", "conditional", node))
        
        elif node.type == "preprocessor_include":
            # 包含指令
            macros.append(("include_macro", "include", node))
        
        # 递归查找子节点
        for child in node.children:
            macros.extend(find_macro_definitions(child))
        
        return macros
    
    # 查找所有宏定义
    macros = find_macro_definitions(root_node)
    
    print("=== 找到的宏定义 ===")
    for macro_type, name, node in macros:
        node_text = test_code.encode("utf-8")[node.start_byte:node.end_byte].decode("utf-8")
        print(f"类型: {macro_type}")
        print(f"名称: {name}")
        print(f"位置: {node.start_point} - {node.end_point}")
        print(f"内容: {repr(node_text.strip())}")
        print()
    
    # 查找函数定义
    def find_functions(node):
        """查找函数定义"""
        functions = []
        
        if node.type == "function_definition":
            declarator = node.child_by_field_name("declarator")
            if declarator:
                identifier = declarator.child_by_field_name("declarator") or declarator.child_by_field_name("identifier")
                if identifier:
                    name = test_code.encode("utf-8")[identifier.start_byte:identifier.end_byte].decode("utf-8")
                    functions.append((name, node))
        
        for child in node.children:
            functions.extend(find_functions(child))
        
        return functions
    
    functions = find_functions(root_node)
    
    print("=== 找到的函数定义 ===")
    for name, node in functions:
        node_text = test_code.encode("utf-8")[node.start_byte:node.end_byte].decode("utf-8")
        print(f"函数名: {name}")
        print(f"位置: {node.start_point} - {node.end_point}")
        print(f"内容: {repr(node_text.strip()[:100])}...")
        print()

if __name__ == "__main__":
    debug_c_macro_parsing() 