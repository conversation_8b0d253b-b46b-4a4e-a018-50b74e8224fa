# 规则分组调试工具使用示例

## 🎯 工具概述

`debug_rule_grouping.py` 是一个专门用于分析和调试规则分组策略的工具，帮助开发者理解不同分组策略的效果，优化LLM调用次数。

## 📊 功能特性

- **策略对比**: 对比所有可用的分组策略
- **性能分析**: 分析分组策略对LLM调用次数的影响
- **详细分析**: 查看每个分组的详细规则分布
- **模拟场景**: 模拟多函数场景下的分组效果
- **结果导出**: 将分析结果导出为JSON格式

## 🚀 使用示例

### 1. 基础分析

分析当前配置的分组策略：

```bash
python tools/development/debug_rule_grouping.py
```

输出示例：
```
🔧 当前配置:
   环境: home
   分组策略: adaptive
   规则文件: resource/编码规范_python.md
   最小分组大小: 3
   最大分组大小: 8

================================================================================
规则分组分析结果
================================================================================
📊 策略: adaptive
📈 分组数量: 10
📋 总规则数: 37
📊 平均每组规则数: 3.7
📉 最小分组大小: 3
📈 最大分组大小: 5
```

### 2. 策略对比

对比所有可用的分组策略：

```bash
python tools/development/debug_rule_grouping.py --strategy all
```

输出示例：
```
================================================================================
策略对比分析
================================================================================
策略              分组数      总规则数       平均大小       最小     最大    
--------------------------------------------------------------------------------
category        14       37         2.6        1      5     
similarity      37       37         1.0        1      1     
adaptive        10       37         3.7        3      5     
minimize_calls  4        37         9.2        9      10    
--------------------------------------------------------------------------------

🎯 推荐策略: minimize_calls (分组数最少)
```

### 3. 多函数场景模拟

模拟221个函数的分组情况（基于你的评估日志）：

```bash
python tools/development/debug_rule_grouping.py --simulate 221
```

输出示例：
```
================================================================================
多函数模拟分析
================================================================================
📊 函数数量: 221
🎯 使用策略: adaptive
📈 每个函数分组数: 10
📊 总分组数: 2210
📋 总规则处理数: 8177
```

### 4. 详细分析

查看每个分组的详细规则分布：

```bash
python tools/development/debug_rule_grouping.py --detailed
```

输出示例：
```
📋 详细分组信息:
   组1 [adaptive_group_0]: 3 条规则
     规则ID: ['4.1', '4.2', '4.3']
     类别: ['编程实践']

   组2 [adaptive_group_1]: 5 条规则
     规则ID: ['7.1', '7.2', '7.3', '7.4', '7.5']
     类别: ['代码质量检查项']
```

### 5. 结果导出

将分析结果导出为JSON文件：

```bash
python tools/development/debug_rule_grouping.py --strategy all --output analysis_results.json
```

## 📈 性能优化建议

### 基于分析结果的优化

1. **选择最优策略**: 根据对比结果选择分组数最少的策略
2. **调整分组参数**: 根据详细分析调整 `min_rule_group_size` 和 `max_rule_group_size`
3. **监控调用次数**: 使用模拟功能预测实际调用次数

### 配置优化示例

```python
# 优化前 (adaptive策略)
rule_grouping_strategy = "adaptive"
min_rule_group_size = 3
max_rule_group_size = 8

# 优化后 (minimize_calls策略)
rule_grouping_strategy = "minimize_calls"
min_rule_group_size = 8
max_rule_group_size = 15
```

## 🔍 问题排查

### 常见问题

1. **分组数量过多**
   - 原因: 使用 `similarity` 策略或分组参数设置不当
   - 解决: 切换到 `minimize_calls` 策略

2. **分组大小不均匀**
   - 原因: 规则类别分布不均
   - 解决: 调整 `target_rule_group_size` 参数

3. **LLM调用次数过多**
   - 原因: 分组策略效率低
   - 解决: 使用模拟功能对比不同策略

### 调试技巧

1. **使用详细分析**: `--detailed` 参数查看分组详情
2. **对比策略**: `--strategy all` 找出最优策略
3. **模拟验证**: `--simulate` 参数验证实际效果

## 📋 参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `--strategy` | 分析策略 | `all`, `category`, `similarity`, `adaptive`, `minimize_calls` |
| `--simulate` | 模拟函数数量 | `221` |
| `--detailed` | 详细分析 | 无参数 |
| `--rule-file` | 规则文件路径 | `resource/编码规范_c.md` |
| `--output` | 输出JSON文件 | `results.json` |

## 🎯 最佳实践

1. **定期分析**: 在修改规则文件后重新分析分组效果
2. **性能监控**: 使用模拟功能预测生产环境的调用次数
3. **策略优化**: 根据实际使用情况调整分组策略
4. **结果记录**: 保存分析结果用于后续对比和优化 