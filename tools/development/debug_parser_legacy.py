#!/usr/bin/env python3
"""
调试AST解析器
"""

from gate_keeper.external.code_analyzer.parsers.c_parser import CParser
from gate_keeper.external.code_analyzer.parsers.python_parser import \
    PythonParser


def test_c_parser():
    print("\n=== 测试C解析器 ===")
    c_code = """
#include <stdio.h>

int main() {
    printf("Hello, World!\\n");
    helper_function();
    return 0;
}

void helper_function() {
    printf("Helper function\\n");
    another_function();
}

void another_function() {
    // Empty function
}
"""
    parser = CParser()
    
    # 测试实际的解析器方法
    print("=== 测试实际解析器方法 ===")
    try:
        functions = parser.extract_functions("", c_code)
        print(f"提取到 {len(functions)} 个函数:")
        for f in functions:
            print(f"  - {f.name}: {f.start_line}-{f.end_line}")
    except Exception as e:
        print(f"函数提取失败: {e}")
    
    try:
        calls = parser.extract_calls("", c_code)
        print(f"提取到 {len(calls)} 个调用:")
        for c in calls:
            print(f"  - {c.caller} -> {c.callee}")
    except Exception as e:
        print(f"调用提取失败: {e}")

if __name__ == "__main__":
    test_c_parser() 