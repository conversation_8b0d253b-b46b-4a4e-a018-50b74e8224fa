#!/usr/bin/env python3
"""
调试规则分组工具
分析规则分组策略的效果和性能

用法：
    python tools/development/debug_rule_grouping.py                    # 分析当前配置
    python tools/development/debug_rule_grouping.py --strategy all     # 对比所有策略
    python tools/development/debug_rule_grouping.py --simulate 221     # 模拟221个函数的分组
    python tools/development/debug_rule_grouping.py --detailed         # 详细分析
"""
import argparse
import json
import os
import sys
from pathlib import Path
from typing import Any, Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.config import config
from gate_keeper.external.code_analyzer.models.element import CodeElement
from gate_keeper.shared.log import app_logger as logger


class DummyCheckItem(CodeElement):
    """虚拟检查项，用于测试规则分组"""
    def __init__(self, name="dummy_func", type="function", filepath="dummy.c"):
        super().__init__(name=name, type=type, filepath=filepath, start_line=1, end_line=10)


class RuleGroupingAnalyzer:
    """规则分组分析器"""
    
    def __init__(self, rule_file_path: str = None):
        self.rule_file_path = rule_file_path or getattr(config, 'rule_file_path', 'resource/编码规范_c.md')
        self.rule_manager = RuleManager(rule_file_path=Path(self.rule_file_path))
        self.check_item = DummyCheckItem()
        
    def analyze_single_strategy(self, strategy: str) -> Dict[str, Any]:
        """分析单个策略的效果"""
        grouped = self.rule_manager.get_applicable_rules(self.check_item, grouping_strategy=strategy)
        
        total_rules = sum(len(rules) for rules in grouped.values())
        group_sizes = [len(rules) for rules in grouped.values()]
        
        return {
            'strategy': strategy,
            'group_count': len(grouped),
            'total_rules': total_rules,
            'group_sizes': group_sizes,
            'avg_group_size': sum(group_sizes) / len(group_sizes) if group_sizes else 0,
            'min_group_size': min(group_sizes) if group_sizes else 0,
            'max_group_size': max(group_sizes) if group_sizes else 0,
            'groups': grouped
        }
    
    def compare_strategies(self) -> Dict[str, Dict[str, Any]]:
        """对比所有策略的效果"""
        strategies = ['category', 'similarity', 'adaptive', 'minimize_calls']
        results = {}
        
        for strategy in strategies:
            try:
                results[strategy] = self.analyze_single_strategy(strategy)
            except Exception as e:
                logger.warning(f"策略 {strategy} 分析失败: {e}")
                results[strategy] = {
                    'strategy': strategy,
                    'error': str(e)
                }
        
        return results
    
    def simulate_multiple_functions(self, num_functions: int, strategy: str = None) -> Dict[str, Any]:
        """模拟多个函数的分组情况"""
        strategy = strategy or getattr(config, 'rule_grouping_strategy', 'adaptive')
        single_result = self.analyze_single_strategy(strategy)
        
        return {
            'num_functions': num_functions,
            'strategy': strategy,
            'groups_per_function': single_result['group_count'],
            'total_groups': num_functions * single_result['group_count'],
            'total_rules_processed': num_functions * single_result['total_rules'],
            'single_function_analysis': single_result
        }
    
    def detailed_analysis(self, strategy: str = None) -> Dict[str, Any]:
        """详细分析规则分组"""
        strategy = strategy or getattr(config, 'rule_grouping_strategy', 'adaptive')
        result = self.analyze_single_strategy(strategy)
        
        # 分析每组规则的分布
        group_analysis = []
        for i, (group_name, rules) in enumerate(result['groups'].items()):
            rule_ids = [getattr(rule, 'id', f'rule_{j}') for j, rule in enumerate(rules)]
            rule_categories = [getattr(rule, 'category', 'unknown') for rule in rules]
            
            # 处理类别，确保可以转换为set
            unique_categories = []
            for category in rule_categories:
                if isinstance(category, (list, tuple)):
                    unique_categories.extend(category)
                else:
                    unique_categories.append(category)
            
            group_analysis.append({
                'group_index': i + 1,
                'group_name': group_name,
                'rule_count': len(rules),
                'rule_ids': rule_ids,
                'categories': list(set(unique_categories))
            })
        
        result['group_analysis'] = group_analysis
        return result


def print_analysis_results(results: Dict[str, Any], detailed: bool = False):
    """打印分析结果"""
    print("=" * 80)
    print("规则分组分析结果")
    print("=" * 80)
    
    if 'error' in results:
        print(f"❌ 分析失败: {results['error']}")
        return
    
    print(f"📊 策略: {results['strategy']}")
    print(f"📈 分组数量: {results['group_count']}")
    print(f"📋 总规则数: {results['total_rules']}")
    print(f"📊 平均每组规则数: {results['avg_group_size']:.1f}")
    print(f"📉 最小分组大小: {results['min_group_size']}")
    print(f"📈 最大分组大小: {results['max_group_size']}")
    
    if detailed and 'group_analysis' in results:
        print(f"\n📋 详细分组信息:")
        for group in results['group_analysis']:
            print(f"   组{group['group_index']} [{group['group_name']}]: {group['rule_count']} 条规则")
            print(f"     规则ID: {group['rule_ids']}")
            print(f"     类别: {group['categories']}")
            print()


def print_strategy_comparison(comparison_results: Dict[str, Dict[str, Any]]):
    """打印策略对比结果"""
    print("=" * 80)
    print("策略对比分析")
    print("=" * 80)
    
    # 表头
    print(f"{'策略':<15} {'分组数':<8} {'总规则数':<10} {'平均大小':<10} {'最小':<6} {'最大':<6}")
    print("-" * 80)
    
    for strategy, result in comparison_results.items():
        if 'error' in result:
            print(f"{strategy:<15} {'ERROR':<8}")
            continue
            
        print(f"{strategy:<15} {result['group_count']:<8} {result['total_rules']:<10} "
              f"{result['avg_group_size']:<10.1f} {result['min_group_size']:<6} {result['max_group_size']:<6}")
    
    print("-" * 80)
    
    # 推荐最佳策略
    valid_results = {k: v for k, v in comparison_results.items() if 'error' not in v}
    if valid_results:
        best_strategy = min(valid_results.keys(), 
                          key=lambda x: valid_results[x]['group_count'])
        print(f"\n🎯 推荐策略: {best_strategy} (分组数最少)")


def print_simulation_results(simulation_results: Dict[str, Any]):
    """打印模拟结果"""
    print("=" * 80)
    print("多函数模拟分析")
    print("=" * 80)
    
    print(f"📊 函数数量: {simulation_results['num_functions']}")
    print(f"🎯 使用策略: {simulation_results['strategy']}")
    print(f"📈 每个函数分组数: {simulation_results['groups_per_function']}")
    print(f"📊 总分组数: {simulation_results['total_groups']}")
    print(f"📋 总规则处理数: {simulation_results['total_rules_processed']}")
    
    # 计算优化效果
    if simulation_results['strategy'] == 'minimize_calls':
        old_groups = simulation_results['num_functions'] * 10  # adaptive策略
        new_groups = simulation_results['total_groups']
        reduction = (old_groups - new_groups) / old_groups * 100
        print(f"🚀 优化效果: 相比adaptive策略减少 {reduction:.1f}% 的分组")


def main():
    parser = argparse.ArgumentParser(description="调试规则分组工具")
    parser.add_argument('--strategy', type=str, choices=['all', 'category', 'similarity', 'adaptive', 'minimize_calls'],
                       help='分析策略 (all=对比所有策略)')
    parser.add_argument('--simulate', type=int, help='模拟指定数量的函数')
    parser.add_argument('--detailed', action='store_true', help='显示详细分析')
    parser.add_argument('--rule-file', type=str, help='规则文件路径')
    parser.add_argument('--output', type=str, help='输出结果到JSON文件')
    args = parser.parse_args()
    
    # 显示当前配置
    print("🔧 当前配置:")
    print(f"   环境: {os.environ.get('GK_ENV', 'test')}")
    print(f"   分组策略: {getattr(config, 'rule_grouping_strategy', 'unknown')}")
    print(f"   规则文件: {getattr(config, 'rule_file_path', 'unknown')}")
    print(f"   最小分组大小: {getattr(config, 'min_rule_group_size', 'unknown')}")
    print(f"   最大分组大小: {getattr(config, 'max_rule_group_size', 'unknown')}")
    print()
    
    # 创建分析器
    analyzer = RuleGroupingAnalyzer(args.rule_file)
    
    results = {}
    
    if args.strategy == 'all':
        # 对比所有策略
        comparison_results = analyzer.compare_strategies()
        print_strategy_comparison(comparison_results)
        results = comparison_results
        
    elif args.simulate:
        # 模拟多函数场景
        simulation_results = analyzer.simulate_multiple_functions(args.simulate)
        print_simulation_results(simulation_results)
        results = simulation_results
        
    else:
        # 分析当前策略
        current_strategy = getattr(config, 'rule_grouping_strategy', 'adaptive')
        analysis_results = analyzer.detailed_analysis(current_strategy) if args.detailed else analyzer.analyze_single_strategy(current_strategy)
        print_analysis_results(analysis_results, args.detailed)
        results = analysis_results
    
    # 输出到JSON文件
    if args.output:
        # 处理结果，确保可以序列化为JSON
        def convert_for_json(obj):
            if isinstance(obj, dict):
                return {str(k): convert_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [convert_for_json(item) for item in obj]
            elif hasattr(obj, '__dict__'):
                return str(obj)
            else:
                return obj
        
        json_results = convert_for_json(results)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n💾 结果已保存到: {args.output}")


if __name__ == "__main__":
    main() 