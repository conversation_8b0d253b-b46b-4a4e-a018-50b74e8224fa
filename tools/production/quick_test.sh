#!/bin/bash
# Quick MR Test Script
# 快速测试MR检查能力的便捷脚本

set -e  # 遇到错误时退出

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"  # tools/production -> tools -> project_root

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 自动激活虚拟环境
activate_virtual_env() {
    # 检查当前Python路径，如果不是conda环境中的Python，则需要激活
    local current_python=$(which python3)
    
    # 如果已经在正确的git_keeper环境中
    if [[ "$current_python" == *"/envs/git_keeper/"* ]] && [[ "$CONDA_DEFAULT_ENV" == "git_keeper" ]]; then
        print_info "已在git_keeper conda环境中"
        return 0
    fi
    
    # 尝试激活conda环境
    if command -v conda &> /dev/null; then
        if conda env list | grep -q "git_keeper"; then
            print_info "激活conda虚拟环境: git_keeper"
            # 初始化conda
            eval "$(conda shell.bash hook)" 2>/dev/null || true
            conda activate git_keeper 2>/dev/null || true
            
            # 验证激活是否成功
            local new_python=$(which python3)
            if [[ "$new_python" == *"/envs/git_keeper/"* ]]; then
                print_success "成功激活git_keeper环境"
                return 0
            else
                print_warning "conda activate可能未生效，当前Python: $new_python"
            fi
        fi
    fi
    
    # 尝试激活venv环境
    if [[ -f "$PROJECT_ROOT/venv/bin/activate" ]]; then
        print_info "激活venv虚拟环境"
        source "$PROJECT_ROOT/venv/bin/activate"
        return 0
    elif [[ -f "$PROJECT_ROOT/.venv/bin/activate" ]]; then
        print_info "激活.venv虚拟环境"
        source "$PROJECT_ROOT/.venv/bin/activate"
        return 0
    fi
    
    # 备用方案：查找conda环境中的Python
    local conda_base_paths=(
        "$HOME/miniconda3/envs/git_keeper/bin"
        "$HOME/anaconda3/envs/git_keeper/bin"
        "/usr/local/miniconda3/envs/git_keeper/bin"
        "/opt/miniconda3/envs/git_keeper/bin"
        "/opt/anaconda3/envs/git_keeper/bin"
    )
    
    for conda_path in "${conda_base_paths[@]}"; do
        if [[ -f "$conda_path/python3" ]]; then
            print_warning "conda activate未生效，将直接使用conda环境Python: $conda_path"
            export PATH="$conda_path:$PATH"
            return 0
        fi
    done
    
    # 如果找不到虚拟环境，给出提示
    print_error "未找到git_keeper虚拟环境"
    print_info "请确保已安装并激活git_keeper conda环境"
    return 1
}

# 显示帮助信息
show_help() {
    cat << EOF
Git Keeper - Quick MR Test Script

用法:
    $0 [options]

选项:
    -h, --help              显示此帮助信息
    -m, --mr-id MR_ID      指定MR ID (默认使用配置文件中的值)
    -d, --dev BRANCH       指定开发分支
    -b, --base BRANCH      指定基础分支
    -l, --limit NUMBER     限制最大分析文件数 (用于快速测试)
    -c, --concurrent NUM   LLM并发数
    --debug                启用调试模式
    --dry-run              仅检查配置，不执行分析
    --check-env            检查运行环境

示例:
    $0                                    # 使用默认配置
    $0 -m 10                             # 分析MR #10
    $0 -m 10 -l 2                        # 分析MR #10，限制2个文件
    $0 -m 10 -d feature/new -b master    # 指定分支
    $0 --dry-run                         # 仅检查配置
    $0 --check-env                       # 检查环境

EOF
}

# 检查Python环境
check_python_env() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        return 1
    fi
    
    # 显示Python版本和路径
    local python_version=$(python3 --version)
    local python_path=$(which python3)
    print_info "Python版本: $python_version"
    print_info "Python路径: $python_path"
    
    # 检查虚拟环境状态
    if [[ -n "$VIRTUAL_ENV" ]]; then
        print_success "检测到venv虚拟环境: $(basename "$VIRTUAL_ENV")"
    elif [[ -n "$CONDA_DEFAULT_ENV" ]]; then
        print_success "检测到conda虚拟环境: $CONDA_DEFAULT_ENV"
    else
        print_warning "未检测到标准虚拟环境，但可能使用conda环境"
    fi
    
    # 检查必要的Python包
    print_info "检查Python依赖..."
    cd "$PROJECT_ROOT"
    
    if python3 -c "import gate_keeper" 2>/dev/null; then
        print_success "gate_keeper 包可正常导入"
    else
        print_error "gate_keeper 包导入失败，请检查Python路径或安装依赖"
        print_info "尝试运行: pip install -r requirements.txt"
        return 1
    fi
    
    return 0
}

# 检查Ollama服务
check_ollama_service() {
    print_info "检查Ollama服务..."
    
    # 从配置中读取Ollama端点
    OLLAMA_ENDPOINT=$(python3 -c "from gate_keeper.config import config; print(config.ollama_endpoint)" 2>/dev/null || echo "http://127.0.0.1:11434")
    
    if curl -s "$OLLAMA_ENDPOINT/api/tags" >/dev/null 2>&1; then
        print_success "Ollama服务正常 ($OLLAMA_ENDPOINT)"
        
        # 检查模型是否存在
        LLM_MODEL=$(python3 -c "from gate_keeper.config import config; print(config.llm_model_id)" 2>/dev/null || echo "unknown")
        if curl -s "$OLLAMA_ENDPOINT/api/tags" | grep -q "$LLM_MODEL"; then
            print_success "模型 $LLM_MODEL 可用"
        else
            print_warning "模型 $LLM_MODEL 可能未下载"
        fi
    else
        print_error "Ollama服务不可用 ($OLLAMA_ENDPOINT)"
        print_info "请确保Ollama服务正在运行"
        return 1
    fi
    
    return 0
}

# 解析命令行参数
parse_args() {
    ARGS=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -m|--mr-id)
                ARGS+=("--mr-id" "$2")
                shift 2
                ;;
            -d|--dev)
                ARGS+=("--dev-branch" "$2")
                shift 2
                ;;
            -b|--base)
                ARGS+=("--base-branch" "$2")
                shift 2
                ;;
            -l|--limit)
                ARGS+=("--max-diff-results" "$2")
                shift 2
                ;;
            -c|--concurrent)
                ARGS+=("--llm-concurrent" "$2")
                shift 2
                ;;
            --debug)
                ARGS+=("--debug")
                shift
                ;;
            --dry-run)
                ARGS+=("--dry-run")
                shift
                ;;
            --check-env)
                CHECK_ENV_ONLY=1
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    print_info "Git Keeper Quick MR Test"
    echo
    
    # 解析参数
    parse_args "$@"
    
    # 设置测试环境变量，确保使用config_test配置
    export GK_ENV=test
    print_info "设置环境变量: GK_ENV=test (使用config_test.py配置)"
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 尝试激活虚拟环境
    activate_virtual_env
    
    # 检查环境
    if ! check_python_env; then
        exit 1
    fi
    
    if ! check_ollama_service; then
        exit 1
    fi
    
    # 如果只检查环境，则退出
    if [[ "${CHECK_ENV_ONLY:-0}" == "1" ]]; then
        print_success "环境检查完成"
        exit 0
    fi
    
    # 运行Python脚本
    print_info "启动MR分析..."
    echo
    
    # 确保在项目根目录下执行Python脚本
    # 如果当前Python不是conda环境中的，尝试使用强制激活脚本
    local current_python=$(which python3)
    if [[ "$current_python" != *"/envs/git_keeper/"* ]]; then
        print_warning "当前Python不在git_keeper环境中，尝试强制激活..."
        if "$PROJECT_ROOT/tools/production/activate_and_run.sh" python3 "$PROJECT_ROOT/tools/production/quick_test_mr.py" "${ARGS[@]}"; then
            echo
            print_success "测试成功完成"
            exit 0
        else
            echo
            print_error "强制激活环境失败"
            exit 1
        fi
    fi
    
    if python3 "$PROJECT_ROOT/tools/production/quick_test_mr.py" "${ARGS[@]}"; then
        echo
        print_success "测试成功完成"
        exit 0
    else
        echo
        print_error "测试执行失败"
        exit 1
    fi
}

# 执行主函数
main "$@" 