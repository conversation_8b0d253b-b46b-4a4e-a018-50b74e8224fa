# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['../../gate_keeper/interfaces/cli/main.py'],
    pathex=[],
    binaries=[],
    datas=[('../../.env','.')],
    hiddenimports=[
        'gate_keeper.config.config_dev',
        'gate_keeper.config.config_prod',
        'gate_keeper.config.config_test'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['logs', '.cache', 'cache'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)