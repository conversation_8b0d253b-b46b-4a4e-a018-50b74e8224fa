#!/bin/bash
# Git Keeper Quick Test - 安全版本
# 直接使用conda环境中的Python，避免环境激活问题

set -e

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# 查找conda环境中的Python
find_conda_python() {
    local conda_python_paths=(
        "$HOME/miniconda3/envs/git_keeper/bin/python3"
        "$HOME/anaconda3/envs/git_keeper/bin/python3"
        "/usr/local/miniconda3/envs/git_keeper/bin/python3"
        "/opt/miniconda3/envs/git_keeper/bin/python3"
        "/opt/anaconda3/envs/git_keeper/bin/python3"
        "/opt/homebrew/anaconda3/envs/git_keeper/bin/python3"
        "/opt/homebrew/miniconda3/envs/git_keeper/bin/python3"
    )
    
    for python_path in "${conda_python_paths[@]}"; do
        if [[ -f "$python_path" ]]; then
            echo "$python_path"
            return 0
        fi
    done
    
    return 1
}

# 主函数
main() {
    print_info "Git Keeper Quick Test - 安全版本"
    echo
    
    # 查找conda环境中的Python
    local conda_python=$(find_conda_python)
    if [[ -z "$conda_python" ]]; then
        print_error "未找到git_keeper conda环境中的Python"
        print_info "请确保已安装git_keeper conda环境"
        exit 1
    fi
    
    print_success "找到conda Python: $conda_python"
    
    # 验证Python环境
    if "$conda_python" -c "import gate_keeper" 2>/dev/null; then
        print_success "gate_keeper包导入成功"
    else
        print_error "gate_keeper包导入失败"
        print_info "请在conda环境中安装项目依赖"
        exit 1
    fi
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 运行Python脚本
    print_info "启动MR分析..."
    echo
    
    if "$conda_python" "$PROJECT_ROOT/tools/production/quick_test_mr.py" "$@"; then
        echo
        print_success "测试成功完成"
        exit 0
    else
        echo
        print_error "测试执行失败"
        exit 1
    fi
}

main "$@" 