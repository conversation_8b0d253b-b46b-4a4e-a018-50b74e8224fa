#!/bin/bash
# 强制激活conda环境并运行命令的脚本

# 查找conda安装路径
find_conda() {
    local conda_paths=(
        "$HOME/miniconda3/bin/conda"
        "$HOME/anaconda3/bin/conda"
        "/usr/local/miniconda3/bin/conda"
        "/opt/miniconda3/bin/conda"
        "/opt/anaconda3/bin/conda"
        "/opt/homebrew/anaconda3/bin/conda"
        "/opt/homebrew/miniconda3/bin/conda"
    )
    
    for conda_path in "${conda_paths[@]}"; do
        if [[ -f "$conda_path" ]]; then
            echo "$conda_path"
            return 0
        fi
    done
    
    # 尝试使用系统conda
    if command -v conda &> /dev/null; then
        echo "conda"
        return 0
    fi
    
    return 1
}

# 强制激活git_keeper环境
activate_git_keeper() {
    local conda_cmd=$(find_conda)
    if [[ -z "$conda_cmd" ]]; then
        echo "❌ 未找到conda命令"
        return 1
    fi
    
    echo "🔧 使用conda: $conda_cmd"
    
    # 初始化conda
    eval "$($conda_cmd shell.bash hook)" 2>/dev/null || {
        echo "❌ conda初始化失败"
        return 1
    }
    
    # 激活环境
    conda activate git_keeper || {
        echo "❌ 激活git_keeper环境失败"
        return 1
    }
    
    echo "✅ 成功激活git_keeper环境"
    echo "📍 Python路径: $(which python3)"
    return 0
}

# 主逻辑
main() {
    echo "🚀 强制激活conda环境..."
    
    if activate_git_keeper; then
        echo "▶️  执行命令: $@"
        exec "$@"
    else
        echo "❌ 环境激活失败"
        exit 1
    fi
}

main "$@" 