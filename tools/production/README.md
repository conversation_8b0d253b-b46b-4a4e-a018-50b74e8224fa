# 生产环境工具

本目录包含用于生产环境和日常使用的稳定工具。

## 🚀 主要工具

### quick_test.sh - 快速MR检查脚本
主要工具，需要环境激活配合使用。

### quick_test_safe.sh - 安全版本MR检查脚本
**推荐使用！直接使用conda环境Python，避免环境激活问题。**

快速检查MR代码质量的便捷脚本，自动查找conda环境中的Python。

**使用方法（推荐安全版本）：**
```bash
# 基本使用（推荐）
./quick_test_safe.sh

# 快速测试（限制文件数）
./quick_test_safe.sh --max-diff-results 2

# 检查特定MR
./quick_test_safe.sh --mr-id 123

# 查看帮助
./quick_test_safe.sh --help
```

**原版本使用方法：**
```bash
# 基本使用
./quick_test.sh

# 快速测试（限制文件数）
./quick_test.sh -l 2

# 检查特定MR
./quick_test.sh -m 123

# 只检查环境
./quick_test.sh --check-env
```

**功能特点：**
- ✅ 自动环境检查和虚拟环境激活
- ✅ 快速代码规范检查
- ✅ 生成详细分析报告
- ✅ 支持多种配置参数

### quick_test_mr.py - MR分析核心脚本
底层MR分析脚本，通常由shell脚本调用。

### build.sh - 项目构建脚本
用于项目构建和打包。

### merge_folders.sh - 文件夹合并工具
合并指定文件夹的实用工具。

## 📋 使用建议

### 日常开发
```bash
# 提交前快速检查
./tools/production/quick_test.sh -l 3

# 完整检查
./tools/production/quick_test.sh
```

### CI/CD集成
```bash
# 在CI脚本中使用
./tools/production/quick_test.sh --dry-run
./tools/production/quick_test.sh -l 5
```

## ⚠️ 注意事项

1. **环境要求**：需要激活git_keeper虚拟环境
2. **Ollama服务**：确保Ollama服务正在运行
3. **权限**：确保脚本有执行权限 (chmod +x)

## 🔗 相关文档

- [项目主README](../../README.md)
- [测试工具](../testing/README.md)
- [开发工具](../development/README.md) 