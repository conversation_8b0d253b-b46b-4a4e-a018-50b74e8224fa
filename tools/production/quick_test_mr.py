#!/usr/bin/env python3
"""
Quick MR Check Test Script
快速测试MR检查能力的脚本

Usage:
    python scripts/quick_test_mr.py [options]
    
Examples:
    # 使用默认配置测试
    python scripts/quick_test_mr.py
    
    # 指定MR ID测试
    python scripts/quick_test_mr.py --mr-id 10
    
    # 指定分支测试
    python scripts/quick_test_mr.py --mr-id 10 --dev-branch feature/new --base-branch master
    
    # 限制分析结果数量（用于快速测试）
    python scripts/quick_test_mr.py --mr-id 10 --max-diff-results 2
"""

import argparse
import json
import os
import sys
import time
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent  # tools/production -> tools -> project_root
sys.path.insert(0, str(project_root))

# 确保当前工作目录正确
os.chdir(project_root)

from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.config import config
from gate_keeper.infrastructure.llm.client.client_factory import LLMFactory
from gate_keeper.shared.log import app_logger as logger


def print_banner():
    """打印脚本横幅"""
    print("=" * 60)
    print("🚀 Git Keeper - Quick MR Check Test")
    print("=" * 60)
    print(f"项目: {config.repo_dir}")
    print(f"配置环境: {config.ENV} (使用 {config.sub_config_name}.py)")
    print(f"分支: {config.devBranch} -> {config.baseBranch}")
    print(f"LLM: {config.llm_model_id}")
    print("-" * 60)


def print_config_summary():
    """打印配置摘要"""
    print("\n📋 当前配置:")
    key_configs = [
        ("配置环境", "ENV"),
        ("仓库目录", "repo_dir"),
        ("MR ID", "mr_id"),
        ("开发分支", "devBranch"),
        ("基础分支", "baseBranch"),
        ("LLM模型", "llm_model_id"),
        ("并发数", "llm_concurrent"),
        ("最大结果数", "max_diff_results"),
        ("规则文件", "rule_file_path"),
        ("输出目录", "dest_dir"),
        ("日志级别", "log_level"),
        ("Git平台", "git_platform"),
    ]
    
    for name, key in key_configs:
        value = getattr(config, key, "未设置")
        if key == "repo_dir" and value:
            # 显示相对路径更简洁
            value = os.path.relpath(value, project_root)
        print(f"  {name}: {value}")


def check_prerequisites():
    """检查运行前提条件"""
    print("\n🔍 检查运行环境...")
    
    issues = []
    
    # 检查仓库目录
    if not os.path.exists(config.repo_dir):
        issues.append(f"仓库目录不存在: {config.repo_dir}")
    
    # 检查规则文件
    rule_file = Path(project_root) / config.rule_file_path
    if not rule_file.exists():
        issues.append(f"规则文件不存在: {rule_file}")
    
    # 检查输出目录
    dest_dir = Path(config.dest_dir)
    if not dest_dir.exists():
        try:
            dest_dir.mkdir(parents=True, exist_ok=True)
            print(f"  ✅ 创建输出目录: {dest_dir}")
        except Exception as e:
            issues.append(f"无法创建输出目录 {dest_dir}: {e}")
    else:
        print(f"  ✅ 输出目录存在: {dest_dir}")
    
    # 检查Token
    if not config.token or config.token == "your_token_here":
        issues.append("Gitee Token未配置或使用默认值")
    else:
        print(f"  ✅ Token已配置: {config.token[:10]}...")
    
    if issues:
        print("\n❌ 发现问题:")
        for issue in issues:
            print(f"  • {issue}")
        return False
    
    print("  ✅ 环境检查通过")
    return True


def run_analysis():
    """执行MR分析"""
    print(f"\n🔄 开始分析 MR #{config.mr_id}...")
    start_time = time.time()
    
    try:
        # 构建依赖
        print("  📝 初始化服务...")
        from gate_keeper.infrastructure.git.client_factory import \
            GitClientFactory

        # 使用工厂创建Git客户端，支持多平台
        git_client = GitClientFactory.create_comment_service(
            access_token=config.token,
            repo_url=config.repo_url
        )
        git_service = GitService(comment_service=git_client)
        llm_client = LLMFactory.create("ollama")
        llm_service = LLMService(llm_client)
        
        usecase = AnalyzeMRAndReportUsecase(git_service, llm_service)
        
        # 执行分析
        print("  🔍 执行代码分析...")
        analyze_results, report_content = usecase.execute(
            repo_dir=config.repo_dir,
            mr_id=config.mr_id,
            base_branch=config.baseBranch,
            dev_branch=config.devBranch,
            max_context_chain_depth=config.max_context_chain_depth,
            base_commit_sha=None,
            dev_commit_sha=None,
            project_id=getattr(config, 'repo_project_id', None),
            exclude_patterns=config.exclude_patterns,
            max_diff_results=getattr(config, 'max_diff_results', None),
        )
        
        # 保存结果
        print("  💾 保存分析结果...")
        dest_path = Path(config.dest_dir)
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = dest_path / f"mr_{config.mr_id}_report_{timestamp}.md"
        result_path = dest_path / f"mr_{config.mr_id}_result_{timestamp}.json"
        
        # 保存报告
        report_path.write_text(report_content, encoding="utf-8")
        
        # 保存分析结果
        try:
            with open(result_path, 'w', encoding='utf-8') as f:
                # 使用Pydantic的model_dump方法序列化
                if hasattr(analyze_results, 'model_dump'):
                    result_dict = analyze_results.model_dump()
                else:
                    # 备用方案：转换为字典
                    result_dict = analyze_results.__dict__
                json.dump(result_dict, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"  📄 分析结果已保存到: {result_path}")
        except Exception as e:
            print(f"  ⚠️  保存分析结果失败: {e}")
        
        elapsed_time = time.time() - start_time
        
        # 输出结果摘要
        print_analysis_summary(analyze_results, report_path, result_path, elapsed_time)
        
        return True
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"\n❌ 分析失败 (耗时: {elapsed_time:.2f}s)")
        print(f"错误: {e}")
        if "--debug" in sys.argv or config.DEBUG:
            traceback.print_exc()
        return False


def print_analysis_summary(analyze_results, report_path, result_path, elapsed_time):
    """打印分析统计信息"""
    print("\n" + "="*50)
    print("📊 分析统计信息")
    print("="*50)
    
    # 统计差异文件和函数
    total_diffs = len(analyze_results.diffs)
    total_functions = sum(len(diff.affected_functions) for diff in analyze_results.diffs)
    
    print(f"🔍 检查范围:")
    print(f"  - MR ID: {analyze_results.mr_id}")
    print(f"  - 基础分支: {analyze_results.base_branch}")
    print(f"  - 开发分支: {analyze_results.dev_branch}")
    print(f"  - 差异文件数: {total_diffs}")
    print(f"  - 受影响函数数: {total_functions}")
    
    # 统计文件类型
    file_types = {}
    for diff in analyze_results.diffs:
        ext = diff.filepath.split('.')[-1] if '.' in diff.filepath else 'no_ext'
        file_types[ext] = file_types.get(ext, 0) + 1
    
    if file_types:
        print(f"\n📁 文件类型分布:")
        for ext, count in sorted(file_types.items()):
            print(f"  - .{ext}: {count} 个文件")
    
    # 统计代码质量问题
    total_violations = 0
    for diff in analyze_results.diffs:
        for func in diff.affected_functions:
            for llm_result in func.llm_results:
                if 'violations' in llm_result:
                    total_violations += len(llm_result['violations'])
    
    print(f"\n✅ 分析结果:")
    print(f"  - 总违规问题数: {total_violations}")
    print(f"  - 分析耗时: {elapsed_time:.2f}s")
    
    print(f"\n📄 输出文件:")
    print(f"  - 报告文件: {report_path}")
    print(f"  - 结果文件: {result_path}")
    
    print("="*50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="快速测试MR检查能力",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # 添加常用参数
    parser.add_argument("--mr-id", type=int, 
                       help=f"MR ID (默认: {getattr(config, 'mr_id', 'N/A')})")
    parser.add_argument("--dev-branch", type=str,
                       help=f"开发分支 (默认: {getattr(config, 'devBranch', 'N/A')})")
    parser.add_argument("--base-branch", type=str,
                       help=f"基础分支 (默认: {getattr(config, 'baseBranch', 'N/A')})")
    parser.add_argument("--max-diff-results", type=int,
                       help="限制最大diff结果数量 (用于快速测试)")
    parser.add_argument("--llm-concurrent", type=int,
                       help=f"LLM并发数 (默认: {getattr(config, 'llm_concurrent', 1)})")
    parser.add_argument("--debug", action="store_true",
                       help="启用调试模式")
    parser.add_argument("--dry-run", action="store_true",
                       help="仅检查配置，不执行分析")
    
    args = parser.parse_args()
    
    # 覆盖配置
    if args.mr_id is not None:
        config.mr_id = args.mr_id
    if args.dev_branch:
        config.devBranch = args.dev_branch
    if args.base_branch:
        config.baseBranch = args.base_branch
    if args.max_diff_results is not None:
        config.max_diff_results = args.max_diff_results
    if args.llm_concurrent is not None:
        config.llm_concurrent = args.llm_concurrent
    if args.debug:
        config.DEBUG = True
        config.log_level = "DEBUG"
    
    # 打印信息
    print_banner()
    print_config_summary()
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 环境检查失败，请修复上述问题后重试")
        return 1
    
    # 如果是干跑模式，直接退出
    if args.dry_run:
        print("\n🔍 配置检查完成 (dry-run模式)")
        return 0
    
    # 执行分析
    success = run_analysis()
    
    if success:
        print("\n🎉 测试完成！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 