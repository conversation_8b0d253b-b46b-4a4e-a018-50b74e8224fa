#!/usr/bin/env python3
"""
测试max_diff_results功能

验证限制最大diff result数量的功能是否正常工作
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from gate_keeper.application.service.git import GitService
from gate_keeper.application.service.llm import LLMService
from gate_keeper.application.usecases.orchestrators.analyze_mr_report import \
    AnalyzeMRAndReportUsecase
from gate_keeper.config import config
from gate_keeper.infrastructure.git.codehub.client import CodeHub
from gate_keeper.infrastructure.llm.client.client_factory import LLMFactory
from gate_keeper.shared.log import app_logger as logger


def test_max_diff_results():
    """测试max_diff_results功能"""
    print("🧪 开始测试max_diff_results功能")
    print("=" * 60)
    
    # 设置测试参数
    test_cases = [
        {"max_diff_results": 0, "description": "限制0个（不处理任何diff）"},
        {"max_diff_results": 1, "description": "限制1个"},
        {"max_diff_results": 10, "description": "限制10个"},
    ]
    
    for test_case in test_cases:
        max_diff_results = test_case["max_diff_results"]
        description = test_case["description"]
        
        print(f"\n📋 测试用例: {description}")
        print(f"   限制数量: {max_diff_results}")
        print("-" * 40)
        
        try:
            # 构建依赖
            gitee_client = CodeHub(config.token)
            git_service = GitService(comment_service=gitee_client)
            llm_client = LLMFactory.create("ollama")
            llm_service = LLMService(llm_client)
            
            usecase = AnalyzeMRAndReportUsecase(git_service, llm_service)
            
            # 执行分析
            analyze_results, report_content = usecase.execute(
                repo_dir=config.repo_dir,
                mr_id=config.mr_id,
                base_branch=config.baseBranch,
                dev_branch=config.devBranch,
                max_context_chain_depth=config.max_context_chain_depth,
                base_commit_sha=None,
                dev_commit_sha=None,
                project_id=None,
                exclude_patterns=config.exclude_patterns,
                max_diff_results=max_diff_results,
            )
            
            # 统计结果
            total_functions = 0
            for diff in analyze_results.diffs:
                total_functions += len(diff.affected_functions)
            
            print(f"分析完成")
            print(f"   总diff数量: {len(analyze_results.diffs)}")
            print(f"   总函数数量: {total_functions}")
            print(f"   报告长度: {len(report_content)} 字符")
            
            # 验证限制是否生效
            if max_diff_results is not None:
                if total_functions <= max_diff_results:
                    print(f"限制生效: {total_functions} <= {max_diff_results}")
                else:
                    print(f"❌ 限制未生效: {total_functions} > {max_diff_results}")
            else:
                print(f"ℹ️ 无限制模式")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.error(f"测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")


if __name__ == "__main__":
    # 检查必要的环境变量
    if not config.token:
        print("❌ 错误: 未设置Gitee token")
        print("请在 config.py 中设置正确的 token")
        sys.exit(1)
    
    if not config.repo_dir:
        print("❌ 错误: 未设置仓库目录")
        print("请在 config.py 中设置正确的 repo_dir")
        sys.exit(1)
    
    # 运行测试
    test_max_diff_results() 