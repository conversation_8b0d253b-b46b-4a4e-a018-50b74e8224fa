#!/usr/bin/env python3
"""
测试命令行规则分组参数功能

使用方法:
    python tools/testing/test_cli_rule_grouping_args.py --help
    python tools/testing/test_cli_rule_grouping_args.py --rule-grouping-strategy adaptive --min-rule-group-size 5
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import argparse

from gate_keeper.config import config


def test_rule_grouping_args():
    """测试规则分组参数解析"""
    
    # 创建参数解析器
    parser = argparse.ArgumentParser(description="测试规则分组参数")
    
    # 添加规则分组相关参数
    parser.add_argument(
        "--rule-grouping-strategy", 
        type=str, 
        choices=["category", "similarity", "adaptive", "minimize_calls"],
        help="规则分组策略: category(按类别), similarity(按相似度), adaptive(自适应), minimize_calls(最小调用次数)"
    )
    parser.add_argument(
        "--min-rule-group-size", 
        type=int,
        help="最小规则分组大小 (仅在adaptive策略时使用)"
    )
    parser.add_argument(
        "--max-rule-group-size", 
        type=int,
        help="最大规则分组大小 (所有策略都会应用此限制)"
    )
    parser.add_argument(
        "--target-rule-group-size", 
        type=int,
        help="目标规则分组大小 (仅在adaptive策略时使用)"
    )
    parser.add_argument(
        "--rule-similarity-threshold", 
        type=float,
        help="规则相似度分组阈值 (仅在similarity策略时使用)"
    )
    parser.add_argument(
        "--max-llm-calls-per-check-mr-result", 
        type=int,
        help="每个变更函数最多允许的LLM调用次数"
    )
    
    # 解析参数
    args = parser.parse_args()
    
    print("=== 规则分组参数测试 ===")
    print(f"当前配置:")
    print(f"  分组策略: {config.rule_grouping_strategy}")
    print(f"  最小分组大小: {config.min_rule_group_size}")
    print(f"  最大分组大小: {config.max_rule_group_size}")
    print(f"  目标分组大小: {config.target_rule_group_size}")
    print(f"  相似度阈值: {config.rule_similarity_threshold}")
    print(f"  最大LLM调用次数: {config.max_llm_calls_per_check_mr_result}")
    
    print(f"\n命令行参数:")
    print(f"  分组策略: {args.rule_grouping_strategy}")
    print(f"  最小分组大小: {args.min_rule_group_size}")
    print(f"  最大分组大小: {args.max_rule_group_size}")
    print(f"  目标分组大小: {args.target_rule_group_size}")
    print(f"  相似度阈值: {args.rule_similarity_threshold}")
    print(f"  最大LLM调用次数: {args.max_llm_calls_per_check_mr_result}")
    
    # 模拟参数绑定到配置
    if args.rule_grouping_strategy:
        config.rule_grouping_strategy = args.rule_grouping_strategy
    if args.min_rule_group_size:
        config.min_rule_group_size = args.min_rule_group_size
    if args.max_rule_group_size:
        config.max_rule_group_size = args.max_rule_group_size
    if args.target_rule_group_size:
        config.target_rule_group_size = args.target_rule_group_size
    if args.rule_similarity_threshold:
        config.rule_similarity_threshold = args.rule_similarity_threshold
    if args.max_llm_calls_per_check_mr_result:
        config.max_llm_calls_per_check_mr_result = args.max_llm_calls_per_check_mr_result
    
    print(f"\n更新后的配置:")
    print(f"  分组策略: {config.rule_grouping_strategy}")
    print(f"  最小分组大小: {config.min_rule_group_size}")
    print(f"  最大分组大小: {config.max_rule_group_size}")
    print(f"  目标分组大小: {config.target_rule_group_size}")
    print(f"  相似度阈值: {config.rule_similarity_threshold}")
    print(f"  最大LLM调用次数: {config.max_llm_calls_per_check_mr_result}")
    
    # 验证参数有效性
    print(f"\n参数验证:")
    
    if config.rule_grouping_strategy == "adaptive":
        if config.min_rule_group_size > config.max_rule_group_size:
            print("  ❌ 错误: 最小分组大小不能大于最大分组大小")
        elif config.target_rule_group_size < config.min_rule_group_size or config.target_rule_group_size > config.max_rule_group_size:
            print("  ❌ 错误: 目标分组大小应在最小和最大分组大小之间")
        else:
            print("  ✅ adaptive策略参数有效")
    
    elif config.rule_grouping_strategy == "similarity":
        if not (0.0 <= config.rule_similarity_threshold <= 1.0):
            print("  ❌ 错误: 相似度阈值应在0.0到1.0之间")
        else:
            print("  ✅ similarity策略参数有效")
    
    elif config.rule_grouping_strategy == "category":
        print("  ✅ category策略参数有效")
    
    elif config.rule_grouping_strategy == "minimize_calls":
        print("  ✅ minimize_calls策略参数有效")
    
    if config.max_llm_calls_per_check_mr_result <= 0:
        print("  ❌ 错误: 最大LLM调用次数应大于0")
    else:
        print("  ✅ 最大LLM调用次数参数有效")


def test_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    examples = [
        {
            "description": "使用adaptive策略，自定义分组大小",
            "command": "python main.py --rule-grouping-strategy adaptive --min-rule-group-size 5 --max-rule-group-size 12 --target-rule-group-size 8"
        },
        {
            "description": "使用similarity策略，自定义相似度阈值",
            "command": "python main.py --rule-grouping-strategy similarity --rule-similarity-threshold 0.3"
        },
        {
            "description": "使用category策略（默认参数）",
            "command": "python main.py --rule-grouping-strategy category"
        },
        {
            "description": "使用minimize_calls策略，限制LLM调用次数",
            "command": "python main.py --rule-grouping-strategy minimize_calls --max-llm-calls-per-check-mr-result 3"
        },
        {
            "description": "组合使用多个参数",
            "command": "python main.py --rule-grouping-strategy adaptive --min-rule-group-size 3 --max-rule-group-size 10 --target-rule-group-size 6 --max-llm-calls-per-check-mr-result 5"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}")
        print(f"   {example['command']}")
        print()


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有参数，显示帮助信息
        print("规则分组参数测试工具")
        print("使用方法:")
        print("  python test_cli_rule_grouping_args.py --help")
        print("  python test_cli_rule_grouping_args.py --rule-grouping-strategy adaptive --min-rule-group-size 5")
        print()
        test_usage_examples()
    else:
        test_rule_grouping_args() 