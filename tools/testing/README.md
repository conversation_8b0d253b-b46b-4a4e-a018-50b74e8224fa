# 测试工具

本目录包含用于各种测试场景的脚本和工具。

## 🧪 测试脚本分类

### 集成测试
- `run_gitee_integration_test.sh` - Gitee集成测试
- `run_gitee_complete_workflow_test.sh` - 完整工作流测试
- `run_mr_detection_test.sh` - MR检测测试

### 单元测试
- `test_config_optimization.py` - 配置优化测试
- `test_function_name_fix.py` - 函数名修复测试
- `test_affected_function_fix.py` - 受影响函数修复测试

### 性能测试
- `test_max_diff_results.py` - 最大差异结果测试
- `simple_test_max_diff.py` - 简化差异测试

### 工具测试
- `run_gitee_test.py` - Gitee测试工具
- `analyze_mr.sh` - MR分析测试
- `cleanup_gitee_mr_comments.py` - 清理测试评论

## 📚 使用方法

### 运行集成测试
```bash
# Gitee集成测试
./tools/testing/run_gitee_integration_test.sh

# 完整工作流测试
./tools/testing/run_gitee_complete_workflow_test.sh

# MR检测测试
./tools/testing/run_mr_detection_test.sh
```

### 运行单元测试
```bash
# 配置优化测试
python tools/testing/test_config_optimization.py

# 函数名修复测试
python tools/testing/test_function_name_fix.py

# 受影响函数测试
python tools/testing/test_affected_function_fix.py
```

### 性能测试
```bash
# 最大差异结果测试
python tools/testing/test_max_diff_results.py

# 简化差异测试
python tools/testing/simple_test_max_diff.py
```

### 清理和维护
```bash
# 清理Gitee测试评论
python tools/testing/cleanup_gitee_mr_comments.py

# MR分析测试
./tools/testing/analyze_mr.sh
```

## 🎯 测试场景

### 开发阶段测试
```bash
# 快速单元测试
python tools/testing/test_function_name_fix.py

# 配置测试
python tools/testing/test_config_optimization.py
```

### 集成测试
```bash
# 完整Gitee工作流
./tools/testing/run_gitee_complete_workflow_test.sh

# MR检测功能
./tools/testing/run_mr_detection_test.sh
```

### 性能回归测试
```bash
# 差异处理性能
python tools/testing/test_max_diff_results.py

# 简化性能测试
python tools/testing/simple_test_max_diff.py
```

## ⚙️ 测试环境

### 环境要求
- Python 3.11+
- git_keeper虚拟环境
- Ollama服务运行
- 测试数据准备

### Gitee测试要求
- Gitee API token配置
- 测试仓库访问权限
- 网络连接

### 测试数据
```bash
# 确保测试项目存在
ls test_external_projects/git_keeper/

# 检查测试配置
cat gate_keeper/config/config_home.py
```

## 📊 测试报告

测试结果通常输出到：
- 控制台输出
- `temp/` 目录下的结果文件
- 测试日志文件

## 🔧 故障排查

### 常见问题
1. **环境问题**：检查虚拟环境激活
2. **服务问题**：确认Ollama服务运行
3. **权限问题**：检查脚本执行权限
4. **网络问题**：检查Gitee API连接

### 调试模式
```bash
# 启用调试输出
export DEBUG=1
./tools/testing/run_gitee_integration_test.sh
```

## 🔗 相关文档

- [生产工具](../production/README.md)
- [开发工具](../development/README.md)
- [测试文档](../../docs/testing.md) 