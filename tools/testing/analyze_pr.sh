#!/bin/bash

# AI调试工具：MR分析脚本

echo "🔧 AI调试工具：MR代码分析"
echo "=========================================="

# 切换到项目根目录
cd "$(dirname "$0")/../../"

# 检查必要文件
if [ ! -f "gate_keeper/interfaces/cli/main.py" ]; then
    echo "❌ 错误: 未找到主程序文件"
    exit 1
fi

if [ ! -f "resource/编码规范.xlsx" ]; then
    echo "❌ 错误: 未找到规则文件 resource/编码规范.xlsx"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 定义分析指令
instruction=$(cat <<EOF
请分析代码是否存在用户关注的问题。
<important-rules>
1. 仅检查用户指定的规则。
2. 只有非常确定时，才能判断检查失败。
3. 回答使用中文。
</important-rules>
EOF
)

echo "🚀 开始MR分析..."
echo "目标MR: 9"
echo "规则文件: resource/编码规范_python.md"
echo ""

# 运行分析
python gate_keeper/interfaces/cli/main.py \
    --debug \
    --mr-id 9 
#   --repo-dir "temp/git_keeper" \
#   --token "2325efc68be50943d222727b7384b623" \
#   --base-branch master \
#   --dev-branch dev \
#   --ollama-endpoint "http://127.0.0.1:11434" \
#   --ollama-token "jdx7758521" \
#   --llm-model-id "qwen2.5-coder:32b" \
#   --llm-concurrent 3 \
#   --log-dir "logs" \
#   --log-level "DEBUG" \
#   --repo-cache-dir ".cache" \
#   --max-cache-repos 10 \
#   --max-cache-branches 10 \
#   --max-cache-branch-commits 10 \
#   --graph-call-chain-radius 3 \
#   --rule-file-path "resource/编码规范.xlsx" \
#   --rule-include-sheets "基础篇" \
#   --rule-merge-on "规则ID" "小类" \
#   --code-check-main-instruction "$instruction" \
#   --dest-dir "temp" \
#   --output-file-name "result.json" \

# 检查分析结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "✅ MR分析完成"
    echo "=========================================="
    echo ""
    echo "📊 分析结果已保存到: temp/result.json"
    echo "📁 日志文件位置: logs/"
    echo ""
    echo "🎉 分析成功！"
else
    echo ""
    echo "=========================================="
    echo "❌ MR分析失败"
    echo "=========================================="
    echo ""
    echo "🔍 请检查:"
    echo "1. 网络连接是否正常"
    echo "2. Ollama服务是否启动"
    echo "3. 规则文件是否正确"
    echo "4. 目标MR是否存在"
    echo ""
    exit 1
fi 