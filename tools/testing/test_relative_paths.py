#!/usr/bin/env python3
"""
测试RepositoryIndex相对路径处理功能

验证目的：
1. 确保RepositoryIndex在索引时使用相对路径
2. 确保跨平台路径兼容性
3. 验证路径标准化功能
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.git import GitService
from gate_keeper.external.code_analyzer import RepositoryIndex
from gate_keeper.external.code_analyzer.models.function import Function, FunctionSignature
from gate_keeper.external.code_analyzer.models.call_relation import FunctionCall


def test_relative_path_handling():
    """测试相对路径处理"""
    print("=" * 60)
    print("RepositoryIndex 相对路径处理测试")
    print("=" * 60)
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试仓库结构
        repo_dir = Path(temp_dir) / "test_repo"
        repo_dir.mkdir()
        
        # 创建子目录和文件
        src_dir = repo_dir / "src"
        src_dir.mkdir()
        
        test_file = src_dir / "main.py"
        with open(test_file, 'w') as f:
            f.write("""
def main():
    print("Hello, World!")
    helper()
    
def helper():
    print("Helper function")
""")
        
        # 创建GitService mock
        git_service = GitService()
        
        # 创建RepositoryIndex
        repo_index = RepositoryIndex(
            repo_dir=str(repo_dir),
            branch="main",
            git_service=git_service
        )
        
        print(f"仓库根目录: {repo_index.repo_dir}")
        print(f"测试文件绝对路径: {test_file}")
        print(f"测试文件相对路径: {test_file.relative_to(repo_dir)}")
        
        # 测试路径转换
        print("\n1. 路径转换测试:")
        print("-" * 40)
        
        # 测试绝对路径转相对路径
        abs_path = str(test_file)
        rel_path = repo_index.to_rel_path(abs_path)
        print(f"绝对路径: {abs_path}")
        print(f"转换后相对路径: {rel_path}")
        
        # 测试相对路径转绝对路径
        converted_abs_path = repo_index.to_abs_path(rel_path)
        print(f"转换回绝对路径: {converted_abs_path}")
        
        # 验证路径一致性
        assert Path(converted_abs_path).resolve() == Path(abs_path).resolve(), "路径转换不一致"
        print("✅ 路径转换一致性验证通过")
        
        # 测试节点ID生成
        print("\n2. 节点ID生成测试:")
        print("-" * 40)
        
        node_id = repo_index._get_node_id(rel_path, "main")
        print(f"函数: main")
        print(f"文件路径: {rel_path}")
        print(f"生成的节点ID: {node_id}")
        
        # 验证节点ID格式
        assert "::" in node_id, "节点ID格式不正确"
        assert "\\" not in node_id, "节点ID包含反斜杠"
        print("✅ 节点ID格式验证通过")
        
        # 测试函数索引
        print("\n3. 函数索引测试:")
        print("-" * 40)
        
        # 创建测试函数
        functions = [
            Function.create_simple(
                name="main",
                filepath=str(test_file),
                start_line=2,
                end_line=5,
                signature=FunctionSignature(name="main", parameters=[], return_type=None)
            ),
            Function.create_simple(
                name="helper",
                filepath=str(test_file),
                start_line=7,
                end_line=9,
                signature=FunctionSignature(name="helper", parameters=[], return_type=None)
            )
        ]
        
        calls = [
            FunctionCall(
                caller="main",
                callee="helper",
                file_path=str(test_file),
                line=(4, 4),
                code="helper()"
            )
        ]
        
        # 索引模块
        repo_index.index_module(str(test_file), functions, calls)
        
        # 验证索引结果
        print(f"函数定义数量: {len(repo_index.function_definitions)}")
        print(f"函数调用数量: {len(repo_index.function_calls)}")
        print(f"调用图节点数量: {len(repo_index.call_graph.nodes)}")
        print(f"调用图边数量: {len(repo_index.call_graph.edges)}")
        
        # 验证节点中的路径
        print("\n4. 节点路径验证:")
        print("-" * 40)
        
        for node_id, node_data in repo_index.call_graph.nodes(data=True):
            filepath = node_data.get("filepath")
            if filepath:
                print(f"节点: {node_id}")
                print(f"  文件路径: {filepath}")
                print(f"  是否为相对路径: {not Path(filepath).is_absolute()}")
                print(f"  路径分隔符: {'/' if '/' in filepath else '\\' if '\\' in filepath else '无'}")
        
        # 验证边中的路径
        print("\n5. 边路径验证:")
        print("-" * 40)
        
        for edge in repo_index.call_graph.edges(data=True):
            file_path = edge[2].get("file_path")
            if file_path:
                print(f"边: {edge[0]} -> {edge[1]}")
                print(f"  文件路径: {file_path}")
                print(f"  是否为相对路径: {not Path(file_path).is_absolute()}")
                print(f"  路径分隔符: {'/' if '/' in file_path else '\\' if '\\' in file_path else '无'}")
        
        print("\n✅ 所有测试通过！")
        print("\n总结:")
        print("- RepositoryIndex 现在使用相对路径进行索引")
        print("- 路径分隔符统一为 '/'")
        print("- 支持跨平台路径兼容性")
        print("- 节点ID和边数据都使用相对路径")


if __name__ == "__main__":
    test_relative_path_handling() 