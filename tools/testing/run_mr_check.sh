#!/bin/bash

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")" && pwd)

# 进入项目根目录
cd "$PROJECT_ROOT"

# === 配置参数（请根据实际情况修改） ===
REPO_DIR="test_external_projects/git_keeper"
RULE_FILE_PATH="resource/编码规范_python.md"
# RULE_FILE_PATH="resource/编码规范_clean.md"
MR_ID=""

# REPO_DIR="test_external_projects/screen_steward"
# RULE_FILE_PATH="resource/编码规范_c.md"
# MR_ID=5

        # 替换为实际 MR id
BASE_BRANCH="main"     # 替换为实际 base 分支
DEV_BRANCH="dev"      # 替换为实际 dev 分支
OUTPUT_FILE="mr_check_result.json"
DEST_DIR="$REPO_DIR"
# =====================================

# 设置 PYTHONPATH 并执行 CLI，显示 debug 日志
PYTHONPATH="$PROJECT_ROOT" \
python gate_keeper/interfaces/cli/main.py \
  --repo-dir "$REPO_DIR" \
  --mr-id "$MR_ID" \
  --base-branch "$BASE_BRANCH" \
  --dev-branch "$DEV_BRANCH" \
  --output-file-name "$OUTPUT_FILE" \
  --dest-dir "$DEST_DIR" \
  --rule-file-path "$RULE_FILE_PATH" \
  --log-level DEBUG

# 可选：查看输出结果
if [ -f "$REPO_DIR/$OUTPUT_FILE" ]; then
  echo "==== 检查结果 ===="
  cat "$REPO_DIR/$OUTPUT_FILE"
else
  echo "未生成输出报告：$REPO_DIR/$OUTPUT_FILE"
fi 