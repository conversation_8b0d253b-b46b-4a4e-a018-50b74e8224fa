#!/usr/bin/env python3
"""
调试规则分组分析
分析为什么会出现只有一条规则的分组
"""

import os
import sys

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.shared.log import app_logger as logger


def analyze_similarity_thresholds():
    """分析不同相似度阈值对分组的影响"""
    
    # 创建规则管理器
    rule_file_path = "resource/编码规范_python.md"
    rule_manager = RuleManager(rule_file_path)
    
    # 加载规则
    rules = rule_manager.load_rules()
    print(f"加载规则总数: {len(rules)}")
    
    # 测试不同的相似度阈值
    thresholds = [0.1, 0.3, 0.5, 0.7, 0.8, 0.9]
    
    print("\n" + "="*80)
    print("相似度阈值对分组的影响分析")
    print("="*80)
    
    for threshold in thresholds:
        print(f"\n--- 测试相似度阈值: {threshold} ---")
        
        # 创建模拟的代码元素
        from gate_keeper.external.code_analyzer.models.element import CodeElement
        mock_element = CodeElement(
            name="test_function",
            type="function",
            language="python",
            file_path="test.py",
            start_line=1,
            end_line=10
        )
        
        # 临时修改配置
        import gate_keeper.config.config_home as config
        original_threshold = config.rule_similarity_threshold
        config.rule_similarity_threshold = threshold
        
        try:
            # 执行分组
            grouped_rules = rule_manager.get_applicable_rules(mock_element, 'similarity')
            
            # 统计分组情况
            group_sizes = [len(rules) for rules in grouped_rules.values()]
            single_rule_groups = sum(1 for size in group_sizes if size == 1)
            
            print(f"  分组数量: {len(grouped_rules)}")
            print(f"  单规则分组数: {single_rule_groups}")
            print(f"  多规则分组数: {len(grouped_rules) - single_rule_groups}")
            if group_sizes:
                print(f"  平均每组: {sum(group_sizes)/len(group_sizes):.1f} 条规则")
                print(f"  最大分组: {max(group_sizes)} 条规则")
                print(f"  最小分组: {min(group_sizes)} 条规则")
        
        finally:
            # 恢复原始配置
            config.rule_similarity_threshold = original_threshold

def analyze_category_distribution():
    """分析类别分布情况"""
    
    # 创建规则管理器
    rule_file_path = "resource/编码规范_python.md"
    rule_manager = RuleManager(rule_file_path)
    
    # 加载规则
    rules = rule_manager.load_rules()
    
    print("\n" + "="*80)
    print("规则类别分布分析")
    print("="*80)
    
    # 统计类别分布
    category_count = {}
    for rule in rules:
        if rule.category and len(rule.category) > 1:
            group = tuple(rule.category[:-1])
        elif rule.category:
            group = tuple(rule.category)
        else:
            group = ("default",)
        
        category_count[group] = category_count.get(group, 0) + 1
    
    # 按规则数量排序
    sorted_categories = sorted(category_count.items(), key=lambda x: x[1], reverse=True)
    
    print(f"总类别数: {len(category_count)}")
    print("\n类别分布:")
    for category, count in sorted_categories:
        print(f"  {category}: {count} 条规则")
    
    # 找出只有一条规则的类别
    single_rule_categories = [(cat, count) for cat, count in sorted_categories if count == 1]
    if single_rule_categories:
        print(f"\n只有一条规则的类别 ({len(single_rule_categories)} 个):")
        for category, count in single_rule_categories:
            print(f"  {category}: {count} 条规则")

def analyze_rule_similarity():
    """分析规则之间的相似度"""
    
    # 创建规则管理器
    rule_file_path = "resource/编码规范_python.md"
    rule_manager = RuleManager(rule_file_path)
    
    # 加载规则
    rules = rule_manager.load_rules()
    
    print("\n" + "="*80)
    print("规则相似度分析")
    print("="*80)
    
    # 计算所有规则对之间的相似度
    similarities = []
    for i, rule1 in enumerate(rules):
        for j, rule2 in enumerate(rules[i+1:], i+1):
            text1 = f"{rule1.name} {rule1.rule_value} {rule1.description}".lower()
            text2 = f"{rule2.name} {rule2.rule_value} {rule2.description}".lower()
            words1 = set(text1.split())
            words2 = set(text2.split())
            
            if not words1 or not words2:
                similarity = 0.0
            else:
                intersection = words1.intersection(words2)
                union = words1.union(words2)
                similarity = len(intersection) / len(union) if union else 0.0
            
            similarities.append(similarity)
    
    if similarities:
        print(f"规则对总数: {len(similarities)}")
        print(f"平均相似度: {sum(similarities)/len(similarities):.3f}")
        print(f"最大相似度: {max(similarities):.3f}")
        print(f"最小相似度: {min(similarities):.3f}")
        
        # 统计不同阈值下的相似度分布
        thresholds = [0.1, 0.3, 0.5, 0.7, 0.8, 0.9]
        print("\n相似度分布:")
        for threshold in thresholds:
            count = sum(1 for sim in similarities if sim >= threshold)
            percentage = (count / len(similarities)) * 100
            print(f"  >= {threshold}: {count} 对 ({percentage:.1f}%)")

if __name__ == "__main__":
    analyze_similarity_thresholds()
    analyze_category_distribution()
    analyze_rule_similarity() 