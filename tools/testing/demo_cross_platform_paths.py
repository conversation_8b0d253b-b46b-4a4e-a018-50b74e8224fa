#!/usr/bin/env python3
"""
跨平台路径处理演示脚本

演示Unix路径在Windows平台上的处理效果
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.git import GitService
from gate_keeper.external.code_analyzer import \
    RepositoryIndex


def demo_unix_path_on_windows():
    """演示Unix路径在Windows平台上的处理"""
    
    print("=" * 60)
    print("跨平台路径处理演示")
    print("=" * 60)
    
    # 创建RepositoryIndex实例
    repo_dir = Path.cwd() / "demo_repo"
    repo_dir.mkdir(exist_ok=True)
    
    git_service = GitService()
    repo_index = RepositoryIndex(
        repo_dir=str(repo_dir),
        branch="main",
        git_service=git_service
    )
    
    # 测试Unix风格的路径
    unix_paths = [
        "/test/repo/main.py",
        "/home/<USER>/project/src/utils.py",
        "/var/www/app/controllers/user.py",
        "src/components/Button.tsx",
        "tests/unit/test_parser.py"
    ]
    
    print("\n1. Unix路径处理演示:")
    print("-" * 40)
    
    for unix_path in unix_paths:
        # 转换为相对路径
        rel_path = repo_index.to_rel_path(unix_path)
        
        # 生成节点ID
        node_id = repo_index._get_node_id(unix_path, "test_function")
        
        print(f"Unix路径: {unix_path}")
        print(f"  相对路径: {rel_path}")
        print(f"  节点ID: {node_id}")
        has_backslash = '\\' in rel_path or '\\' in node_id
        print(f"  包含反斜杠: {'是' if has_backslash else '否'}")
        print()
    
    # 测试Windows风格的路径
    windows_paths = [
        "C:\\test\\repo\\main.py",
        "C:\\Users\\<USER>\\project\\src\\utils.py",
        "D:\\var\\www\\app\\controllers\\user.py",
        "src\\components\\Button.tsx",
        "tests\\unit\\test_parser.py"
    ]
    
    print("\n2. Windows路径处理演示:")
    print("-" * 40)
    
    for windows_path in windows_paths:
        # 转换为相对路径
        rel_path = repo_index.to_rel_path(windows_path)
        
        # 生成节点ID
        node_id = repo_index._get_node_id(windows_path, "test_function")
        
        print(f"Windows路径: {windows_path}")
        print(f"  相对路径: {rel_path}")
        print(f"  节点ID: {node_id}")
        has_backslash = '\\' in rel_path or '\\' in node_id
        print(f"  包含反斜杠: {'是' if has_backslash else '否'}")
        print()
    
    # 测试混合路径
    mixed_paths = [
        "/test/repo\\subdir\\file.py",
        "C:\\test\\repo/subdir/file.py",
        "src\\components/Button.tsx"
    ]
    
    print("\n3. 混合路径处理演示:")
    print("-" * 40)
    
    for mixed_path in mixed_paths:
        # 转换为相对路径
        rel_path = repo_index.to_rel_path(mixed_path)
        
        # 生成节点ID
        node_id = repo_index._get_node_id(mixed_path, "test_function")
        
        print(f"混合路径: {mixed_path}")
        print(f"  相对路径: {rel_path}")
        print(f"  节点ID: {node_id}")
        has_backslash = '\\' in rel_path or '\\' in node_id
        print(f"  包含反斜杠: {'是' if has_backslash else '否'}")
        print()
    
    # 测试路径转换的一致性
    print("\n4. 路径转换一致性演示:")
    print("-" * 40)
    
    test_paths = [
        "src/utils/helper.py",
        "tests/unit/test_parser.py"
    ]
    
    for test_path in test_paths:
        print(f"测试路径: {test_path}")
        
        # 转换为绝对路径
        abs_path = repo_index.to_abs_path(test_path)
        print(f"  绝对路径: {abs_path}")
        
        # 再转换回相对路径
        back_to_rel = repo_index.to_rel_path(abs_path)
        print(f"  转换回相对路径: {back_to_rel}")
        
        # 验证一致性
        is_consistent = test_path.replace('\\', '/') == back_to_rel.replace('\\', '/')
        print(f"  一致性: {'✓' if is_consistent else '✗'}")
        print()
    
    print("=" * 60)
    print("演示完成！")
    print("=" * 60)


if __name__ == "__main__":
    demo_unix_path_on_windows() 