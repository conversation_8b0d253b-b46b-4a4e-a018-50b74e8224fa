#!/usr/bin/env python3
"""
测试配置优化的脚本
验证userid和base_url的配置化是否正常工作
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_values():
    """测试配置值是否正确加载"""
    print("=== 测试配置值加载 ===")
    
    try:
        from gate_keeper.config import config
        
        print(f"fuyao_base_url: {getattr(config, 'fuyao_base_url', 'NOT_FOUND')}")
        print(f"fuyao_userid: {getattr(config, 'fuyao_userid', 'NOT_FOUND')}")
        print(f"ollama_endpoint: {config.ollama_endpoint}")
        print(f"llm_model_id: {config.llm_model_id}")
        
        print("✓ 配置值加载正常")
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()

def test_fuyao_client_config():
    """测试fuyao客户端是否正确使用配置"""
    print("\n=== 测试fuyao客户端配置 ===")
    
    try:
        from gate_keeper.config import config
        from gate_keeper.infrastructure.llm.client.fuyao import FUYAOClient

        # 创建fuyao客户端
        client = FUYAOClient()
        
        # 检查客户端配置
        client_config = client.get_config()
        print(f"客户端配置: {client_config}")
        
        # 检查默认值
        print(f"默认模型: {client.llm_config.model_id}")
        
        print("✓ fuyao客户端配置正常")
        
    except Exception as e:
        print(f"✗ fuyao客户端测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_llm_factory():
    """测试LLM工厂是否正确支持fuyao"""
    print("\n=== 测试LLM工厂 ===")
    
    try:
        from gate_keeper.infrastructure.llm.client.client_factory import \
            LLMFactory

        # 测试创建fuyao客户端
        fuyao_client = LLMFactory.create("fuyao")
        print(f"fuyao客户端类型: {type(fuyao_client)}")
        
        # 测试创建ollama客户端
        ollama_client = LLMFactory.create("ollama")
        print(f"ollama客户端类型: {type(ollama_client)}")
        
        print("✓ LLM工厂支持fuyao")
        
    except Exception as e:
        print(f"✗ LLM工厂测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_environment_config():
    """测试不同环境的配置"""
    print("\n=== 测试环境配置 ===")
    
    try:
        import os

        # 测试dev环境
        os.environ["GK_ENV"] = "dev"
        from gate_keeper.config import config as dev_config
        print(f"dev环境 fuyao_base_url: {getattr(dev_config, 'fuyao_base_url', 'NOT_FOUND')}")
        
        # 测试prod环境
        os.environ["GK_ENV"] = "prod"
        from gate_keeper.config import config as prod_config
        print(f"prod环境 fuyao_base_url: {getattr(prod_config, 'fuyao_base_url', 'NOT_FOUND')}")
        
        # 恢复默认环境
        os.environ["GK_ENV"] = "dev"
        
        print("✓ 环境配置切换正常")
        
    except Exception as e:
        print(f"✗ 环境配置测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_values()
    test_fuyao_client_config()
    test_llm_factory()
    test_environment_config()
    print("\n=== 配置优化测试完成 ===") 