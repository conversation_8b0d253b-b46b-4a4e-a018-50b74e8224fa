#!/usr/bin/env python3
"""
测试规则分组配置功能
验证不同配置下的分组策略是否正常工作
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.config import config


def test_rule_grouping_with_config():
    """测试配置驱动的规则分组功能"""
    
    print(f"当前环境: {config.ENV}")
    print(f"分组策略: {getattr(config, 'rule_grouping_strategy', '未配置')}")
    print(f"最大分组大小: {getattr(config, 'max_rule_group_size', '未配置')}")
    print(f"相似度阈值: {getattr(config, 'rule_similarity_threshold', '未配置')}")
    print("-" * 50)
    
    # 创建 RuleManager 实例
    rule_manager = RuleManager(
        rule_file_path=Path(config.rule_file_path),
        merge_on=getattr(config, 'rule_merge_on', None),
        include_sheets=getattr(config, 'include_sheets', None) if hasattr(config, 'include_sheets') else None,
    )
    
    # 加载规则
    rules = rule_manager.load_rules()
    print(f"总规则数: {len(rules)}")
    
    if not rules:
        print("无可用规则，跳过分组测试")
        return
    
    # 测试不同分组策略
    strategies = ['category', 'similarity', 'adaptive']
    
    for strategy in strategies:
        print(f"\n=== 测试 {strategy} 分组策略 ===")
        
        try:
            if strategy == 'category':
                grouped = rule_manager.group_rules_by_category()
            elif strategy == 'similarity':
                threshold = getattr(config, 'rule_similarity_threshold', 0.7)
                grouped = rule_manager.group_rules_by_similarity(threshold)
            elif strategy == 'adaptive':
                min_size = getattr(config, 'min_rule_group_size', 3)
                max_size = getattr(config, 'max_rule_group_size', 8)
                target_size = getattr(config, 'target_rule_group_size', 5)
                grouped = rule_manager.group_rules_adaptive(
                    min_group_size=min_size,
                    max_group_size=max_size,
                    target_group_size=target_size
                )
            
            print(f"生成分组数: {len(grouped)}")
            group_sizes = [len(group) for group in grouped.values()]
            print(f"分组大小分布: {group_sizes}")
            print(f"最大组大小: {max(group_sizes) if group_sizes else 0}")
            print(f"最小组大小: {min(group_sizes) if group_sizes else 0}")
            print(f"平均组大小: {sum(group_sizes) / len(group_sizes):.1f}" if group_sizes else 0)
            
            # 验证组大小是否符合配置
            max_configured_size = getattr(config, 'max_rule_group_size', 8)
            oversized_groups = [size for size in group_sizes if size > max_configured_size]
            if oversized_groups:
                print(f"警告: 发现超过配置大小的分组: {oversized_groups}")
            else:
                print("✓ 所有分组都符合大小限制")
                
        except Exception as e:
            print(f"错误: {strategy} 分组策略测试失败: {e}")
    
    print("\n=== 测试当前配置的分组策略 ===")
    current_strategy = getattr(config, 'rule_grouping_strategy', 'adaptive')
    print(f"当前配置策略: {current_strategy}")
    
    # 模拟 LLMService 中的分组逻辑
    temp_rule_manager = RuleManager(
        rule_file_path=Path(config.rule_file_path),
        merge_on=getattr(config, 'rule_merge_on', None),
        include_sheets=getattr(config, 'include_sheets', None) if hasattr(config, 'include_sheets') else None,
    )
    temp_rule_manager._rules = rules
    temp_rule_manager._loaded = True
    
    try:
        if current_strategy == 'category':
            grouped_rules_dict = temp_rule_manager.group_rules_by_category()
        elif current_strategy == 'similarity':
            similarity_threshold = getattr(config, 'rule_similarity_threshold', 0.7)
            grouped_rules_dict = temp_rule_manager.group_rules_by_similarity(similarity_threshold)
        elif current_strategy == 'adaptive':
            min_size = getattr(config, 'min_rule_group_size', 3)
            max_size = getattr(config, 'max_rule_group_size', 8)
            target_size = getattr(config, 'target_rule_group_size', 5)
            grouped_rules_dict = temp_rule_manager.group_rules_adaptive(
                min_group_size=min_size,
                max_group_size=max_size,
                target_group_size=target_size
            )
        else:
            print(f"未知策略 {current_strategy}，使用默认分组")
            grouped_rules_dict = {"default_group": rules}
        
        rule_groups = list(grouped_rules_dict.values())
        print(f"当前配置下生成分组数: {len(rule_groups)}")
        if rule_groups:
            group_sizes = [len(group) for group in rule_groups]
            print(f"分组大小分布: {group_sizes}")
            print("✓ 配置驱动的分组功能正常工作")
        else:
            print("⚠ 未生成任何分组")
            
    except Exception as e:
        print(f"✗ 配置驱动的分组功能测试失败: {e}")


if __name__ == "__main__":
    test_rule_grouping_with_config() 