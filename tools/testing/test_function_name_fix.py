#!/usr/bin/env python3
"""
测试函数名提取修复
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.external.code_analyzer.parsers.python_parser import \
    PythonParser


def test_function_name_extraction():
    """测试函数名提取"""
    
    # 测试代码
    test_code = """
def test_function():
    print("Hello World")
    return True

def another_function(param1, param2):
    result = param1 + param2
    return result

class TestClass:
    def class_method(self):
        return "class method"
"""
    
    parser = PythonParser()
    
    print("=== 测试函数名提取 ===")
    
    # 测试extract_functions方法
    functions = parser.extract_functions("test.py", test_code)
    print(f"提取到 {len(functions)} 个函数:")
    for func in functions:
        print(f"  函数名: '{func.name}'")
        print(f"  签名: '{func.signature}'")
        print(f"  行范围: {func.start_line}-{func.end_line}")
        print(f"  代码长度: {len(func.code)} 字符")
        print()
    
    # 测试find_parent_element方法
    print("=== 测试find_parent_element方法 ===")
    for line_num in [2, 6, 11]:  # 测试不同行的函数
        element = parser.find_parent_element("test.py", line_num, line_num, test_code)
        if element:
            print(f"第{line_num}行的元素:")
            print(f"  名称: '{element.name}'")
            print(f"  类型: {type(element.name)}")
            print(f"  签名: '{element.signature}'")
            print()

if __name__ == "__main__":
    test_function_name_extraction() 