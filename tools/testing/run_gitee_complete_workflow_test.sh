#!/bin/bash

# Gitee完整MR检测工作流集成测试运行脚本
# 用于回归测试，验证完整的MR检测流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🔍 Gitee完整MR检测工作流集成测试${NC}"
echo "=================================================="
echo -e "项目根目录: ${YELLOW}$PROJECT_ROOT${NC}"
echo -e "测试时间: ${YELLOW}$(date)${NC}"
echo ""

# 检查Python环境
echo -e "${BLUE}📋 检查Python环境${NC}"
if ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python未安装${NC}"
    exit 1
fi

python_version=$(python --version 2>&1)
echo -e "${GREEN}✅ Python版本: $python_version${NC}"

# 检查依赖
echo -e "${BLUE}📋 检查项目依赖${NC}"
if [ ! -f "$PROJECT_ROOT/requirements.txt" ]; then
    echo -e "${RED}❌ requirements.txt不存在${NC}"
    exit 1
fi

# 检查配置文件
echo -e "${BLUE}📋 检查配置文件${NC}"
if [ ! -f "$PROJECT_ROOT/gate_keeper/config/config_home.py" ]; then
    echo -e "${RED}❌ config_home.py不存在${NC}"
    exit 1
fi

# 检查规范文档
echo -e "${BLUE}📋 检查规范文档${NC}"
rule_file="$PROJECT_ROOT/resource/编码规范_python.md"
if [ ! -f "$rule_file" ]; then
    echo -e "${RED}❌ 规范文档不存在: $rule_file${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 规范文档存在: $(basename "$rule_file")${NC}"

# 检查测试仓库
echo -e "${BLUE}📋 检查测试仓库${NC}"
repo_dir="$PROJECT_ROOT/test_external_projects/git_keeper"
if [ ! -d "$repo_dir" ]; then
    echo -e "${YELLOW}⚠️ 测试仓库不存在，将跳过仓库相关检查${NC}"
else
    echo -e "${GREEN}✅ 测试仓库存在: $repo_dir${NC}"
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 运行完整工作流测试
echo -e "${BLUE}🚀 运行完整工作流测试${NC}"
echo "=================================================="

# 运行主测试
echo -e "${YELLOW}📝 执行主工作流测试...${NC}"
python -m pytest tests/integration/test_gitee_complete_workflow.py::TestGiteeCompleteWorkflow::test_gitee_complete_mr_detection_workflow -v -s

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 主工作流测试通过${NC}"
else
    echo -e "${RED}❌ 主工作流测试失败${NC}"
    exit 1
fi

# 运行排重测试
echo -e "${YELLOW}📝 执行排重功能测试...${NC}"
python -m pytest tests/integration/test_gitee_complete_workflow.py::TestGiteeCompleteWorkflow::test_gitee_detection_deduplication -v -s

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 排重功能测试通过${NC}"
else
    echo -e "${RED}❌ 排重功能测试失败${NC}"
    exit 1
fi

# 运行性能测试
echo -e "${YELLOW}📝 执行性能测试...${NC}"
python -m pytest tests/integration/test_gitee_complete_workflow.py::TestGiteeCompleteWorkflow::test_gitee_detection_performance -v -s

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 性能测试通过${NC}"
else
    echo -e "${RED}❌ 性能测试失败${NC}"
    exit 1
fi

# 运行所有标记的测试
echo -e "${YELLOW}📝 执行所有集成测试...${NC}"
python -m pytest tests/integration/test_gitee_complete_workflow.py -v -s -m "integration and gitee"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 所有集成测试通过${NC}"
else
    echo -e "${RED}❌ 部分集成测试失败${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Gitee完整MR检测工作流集成测试全部通过！${NC}"
echo "=================================================="
echo -e "测试完成时间: ${YELLOW}$(date)${NC}"
echo ""
echo -e "${BLUE}📊 测试总结:${NC}"
echo -e "  ✅ 配置验证"
echo -e "  ✅ MR信息获取"
echo -e "  ✅ MR检测分析"
echo -e "  ✅ 报告生成"
echo -e "  ✅ Gitee评论提交"
echo -e "  ✅ 排重功能"
echo -e "  ✅ 性能测试"
echo ""
echo -e "${BLUE}💡 使用说明:${NC}"
echo -e "  1. 此测试使用 config_home.py 中的配置"
echo -e "  2. 规范文档: resource/编码规范_python.md"
echo -e "  3. 目标MR: 配置文件中指定的mr_id"
echo -e "  4. 测试会自动清理生成的评论"
echo ""
echo -e "${BLUE}🔧 如需修改配置:${NC}"
echo -e "  1. 编辑 gate_keeper/config/config_home.py"
echo -e "  2. 修改 repo_url, mr_id, token 等参数"
echo -e "  3. 重新运行此脚本" 