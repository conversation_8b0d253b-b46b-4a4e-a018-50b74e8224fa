#!/usr/bin/env python3
"""
测试规则分组日志功能
演示不同分组策略的日志输出
"""

import os
import sys

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from gate_keeper.application.service.rule import RuleManager
from gate_keeper.shared.log import app_logger as logger


def test_rule_grouping_logs():
    """测试规则分组的日志功能"""
    
    # 设置日志级别为INFO，确保能看到分组统计信息
    logger.setLevel('INFO')
    
    # 创建规则管理器
    rule_file_path = "resource/编码规范_python.md"
    rule_manager = RuleManager(rule_file_path)
    
    print("=" * 80)
    print("规则分组日志功能测试")
    print("=" * 80)
    
    # 加载规则
    rules = rule_manager.load_rules()
    print(f"加载规则总数: {len(rules)}")
    
    # 测试不同的分组策略
    strategies = ['category', 'similarity', 'adaptive']
    
    for strategy in strategies:
        print(f"\n{'='*20} 测试 {strategy.upper()} 分组策略 {'='*20}")
        
        # 创建一个模拟的代码元素
        from gate_keeper.external.code_analyzer.models.element import CodeElement
        mock_element = CodeElement(
            name="test_function",
            type="function",
            language="python",
            file_path="test.py",
            start_line=1,
            end_line=10
        )
        
        # 执行分组
        grouped_rules = rule_manager.get_applicable_rules(mock_element, strategy)
        
        print(f"\n分组结果摘要:")
        print(f"  总分组数: {len(grouped_rules)}")
        total_rules = sum(len(rules) for rules in grouped_rules.values())
        print(f"  总规则数: {total_rules}")
        
        # 显示每个分组的规则数量
        for group_name, group_rules in grouped_rules.items():
            print(f"    {group_name}: {len(group_rules)} 条规则")

    print("==================== 测试 MINIMIZE_CALLS 分组策略 ====================")
    # 创建一个模拟的代码元素用于 minimize_calls 策略
    check_item = CodeElement(
        name="test_function",
        type="function",
        language="python",
        file_path="test.py",
        start_line=1,
        end_line=10
    )
    grouped_rules = rule_manager.get_applicable_rules(check_item, "minimize_calls")
    
    print("\n分组结果摘要:")
    print(f"  总分组数: {len(grouped_rules)}")
    print(f"  总规则数: {sum(len(rules) for rules in grouped_rules.values())}")
    for group_name, rules in grouped_rules.items():
        print(f"    {group_name}: {len(rules)} 条规则")

    print("\n" + "="*80)
    print("测试完成！")

if __name__ == "__main__":
    test_rule_grouping_logs() 