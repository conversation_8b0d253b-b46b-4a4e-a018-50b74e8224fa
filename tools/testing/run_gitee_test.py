#!/usr/bin/env python3
"""
Gitee集成测试运行器

简单的测试运行器，用于快速测试Gitee功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

def main():
    """主函数"""
    print("🚀 开始Gitee集成测试")
    print("=" * 50)
    
    try:
        # 导入并运行测试
        from tests.integration.test_gitee_integration import \
            run_gitee_integration_test

        # 运行测试
        success = run_gitee_integration_test()
        
        if success:
            print("\n🎉 所有测试通过！")
            return 0
        else:
            print("\n❌ 部分测试失败")
            return 1
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖已正确安装")
        return 1
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 