#!/usr/bin/env python3
"""
测试AffectedFunction修复的脚本
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.external.code_analyzer.models.function import Function


def test_affected_function_creation():
    """测试AffectedFunction的创建"""
    print("=== 测试AffectedFunction创建 ===")
    
    # 创建一个简单的AffectedFunction
    af = AffectedFunction(
        name="test_function",
        start_line=1,
        end_line=10,
        changed_lines=[2, 3, 4],
        code="def test_function():\n    pass",
        filepath="test.py"
    )
    
    print(f"创建成功: {af.name}")
    print(f"type字段: {af.type}")
    print(f"llm_results字段: {af.llm_results}")
    
    # 测试添加llm_results
    af.llm_results = [{"rule": "test_rule", "result": "pass"}]
    print(f"添加llm_results后: {af.llm_results}")
    
    print("✓ AffectedFunction创建和字段访问正常")

def test_affected_function_from_repository_index():
    """测试从RepositoryIndex获取的AffectedFunction"""
    print("\n=== 测试从RepositoryIndex获取AffectedFunction ===")
    
    try:
        from gate_keeper.application.service.git import GitService
        from gate_keeper.external.code_analyzer import \
            RepositoryIndex
        from gate_keeper.infrastructure.git.gitee.client import Gitee

        # 创建模拟的GitService
        git_service = GitService(comment_service=Gitee("dummy_token"))
        
        # 创建RepositoryIndex
        repo_index = RepositoryIndex(
            repo_dir=".",
            branch="main",
            git_service=git_service,
            commit_sha="test_commit"
        )
        
        # 模拟文件内容
        file_content = """def test_function():
    pass

def another_function():
    pass"""
        
        # 测试get_changed_functions
        affected_functions = repo_index.get_changed_functions(
            file_content=file_content,
            file_path="test.py",
            changed_lines=[2, 3, 4]
        )
        
        print(f"获取到 {len(affected_functions)} 个AffectedFunction")
        
        for af in affected_functions:
            print(f"函数名: {af.name}")
            print(f"type字段: {af.type}")
            print(f"llm_results字段: {af.llm_results}")
            print(f"changed_lines: {af.changed_lines}")
            print("---")
        
        print("✓ RepositoryIndex.get_changed_functions正常工作")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_affected_function_creation()
    test_affected_function_from_repository_index()
    print("\n=== 测试完成 ===") 