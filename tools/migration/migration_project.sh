#!/bin/bash

# ---------- 跨平台兼容性检查 ----------
check_compatibility() {
  # 检查必需的命令
  local required_commands=("find" "mkdir" "cp" "mv" "sed" "cmp")
  for cmd in "${required_commands[@]}"; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
      echo "❌ 错误: 必需的命令 '$cmd' 未找到" >&2
      echo "   在 Windows 上，请使用 Git Bash 或 WSL" >&2
      exit 1
    fi
  done
  
  # 检测操作系统
  case "$(uname -s 2>/dev/null || echo 'Unknown')" in
    Darwin*) echo "🍎 检测到 macOS 系统" ;;
    Linux*) echo "🐧 检测到 Linux 系统" ;;
    CYGWIN*|MINGW*|MSYS*) echo "🪟 检测到 Windows 系统 (Git Bash)" ;;
    *) echo "⚠️ 未知系统，尝试继续执行..." ;;
  esac
}

# ---------- 文件内容比较函数 ----------
files_are_identical() {
  local file1="$1"
  local file2="$2"
  
  # 检查文件是否存在
  [[ ! -f "$file1" || ! -f "$file2" ]] && return 1
  
  # 使用 cmp 命令比较文件内容（跨平台兼容）
  cmp -s "$file1" "$file2" 2>/dev/null
}

# ---------- 通用安全执行封装 ----------
safe_run() {
  "$@" || echo "⚠️ 命令失败: $*" >&2
}


safe_cp() {
  mkdir -p "$(dirname "$2")" && cp "$1" "$2" || echo "❌ 复制失败: $1 → $2" >&2
}

safe_mv() {
  mkdir -p "$(dirname "$2")" && mv "$1" "$2" || echo "❌ 移动失败: $1 → $2" >&2
}

# ---------- 参数解析 ----------
if [[ $# -lt 2 ]]; then
  echo "用法: $0 <A项目路径> <B项目路径> [配置文件路径] [--dry-run] [--debug-ignore]"
  exit 1
fi

# 运行兼容性检查
check_compatibility

A_PATH="$1"
B_PATH="$2"
CONFIG_PATH=""
DRY_RUN=0
DEBUG_IGNORE=0

shift 2
while [[ $# -gt 0 ]]; do
  case "$1" in
    --dry-run) DRY_RUN=1 ;;
    --debug-ignore) DEBUG_IGNORE=1 ;;
    *) CONFIG_PATH="$1" ;;
  esac
  shift
done

[[ ! -d "$A_PATH" ]] && echo "错误: A项目路径不存在: $A_PATH" >&2 && exit 1
[[ ! -d "$B_PATH" ]] && echo "错误: B项目路径不存在: $B_PATH" >&2 && exit 1

# 标准化路径（移除末尾斜杠）
A_PATH="${A_PATH%/}"
B_PATH="${B_PATH%/}"

declare -a MERGE_RULES=()
declare -a IGNORE_RULES=()

count_new=0
count_override=0
count_same_content=0  # 新增：内容相同跳过覆盖的计数
count_keep_b=0
count_keep_both=0
count_ignored=0
count_deleted=0

# ---------- 读取配置文件 ----------
if [[ -n "$CONFIG_PATH" && -f "$CONFIG_PATH" ]]; then
  echo "📖 读取配置文件: $CONFIG_PATH"
  while IFS= read -r line; do
    # 移除回车符（Windows兼容）
    line="${line//$'\r'/}"
    # 移除注释
    line="${line%%#*}"
    # 移除前后空格
    line="$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')"
    [[ -z "$line" ]] && continue
    
    if [[ "$line" =~ ^ignore[[:space:]]*=?[[:space:]]*(.+)$ ]]; then
      ignore_pattern="${BASH_REMATCH[1]}"
      IGNORE_RULES+=("$ignore_pattern")
      echo "🚫 添加忽略规则: $ignore_pattern"
    elif [[ "$line" =~ ^(keep_a|keep_b|keep_both)[[:space:]]+(.+)$ ]]; then
      rule="${BASH_REMATCH[1]}"
      path="${BASH_REMATCH[2]}"
      MERGE_RULES+=("${path}|||${rule}")
      echo "📋 添加合并规则: $rule -> $path"
    fi
  done < "$CONFIG_PATH"
fi

# ---------- 加载 .gitignore ----------
load_gitignore() {
  local file="$1/.gitignore"
  [[ ! -f "$file" ]] && return
  echo "📁 加载 .gitignore: $file"
  while IFS= read -r line || [[ -n "$line" ]]; do
    # 移除回车符（Windows兼容）
    line="${line//$'\r'/}"
    # 移除注释
    line="${line%%#*}"
    # 移除前后空格
    line="$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')"
    [[ -n "$line" ]] && IGNORE_RULES+=("$line")
  done < "$file"
}

load_gitignore "$A_PATH"
load_gitignore "$B_PATH"

# ---------- 改进的忽略判断逻辑 ----------
is_ignored() {
  local rel_path="$1"
  
  for pattern in "${IGNORE_RULES[@]}"; do
    # 调试输出
    [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] 检查 '$rel_path' 是否匹配模式 '$pattern'" >&2
    
         # 处理目录模式（以 / 结尾）
     if [[ "$pattern" == */ ]]; then
       dir_pattern="${pattern%/}"
      # 检查是否为该目录或其子目录
      if [[ "$rel_path" == "$dir_pattern" ]] || [[ "$rel_path" == "$dir_pattern"/* ]]; then
        [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] ✅ 目录匹配: '$rel_path' 匹配 '$pattern'" >&2
        return 0
      fi
    else
      # 处理文件模式
      if [[ "$pattern" == *"*"* ]]; then
        # 通配符模式 - 检查完整路径和文件名
        case "$rel_path" in 
          $pattern) 
            [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] ✅ 通配符路径匹配: '$rel_path' 匹配 '$pattern'" >&2
            return 0 
            ;;
        esac
        case "${rel_path##*/}" in 
          $pattern) 
            [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] ✅ 通配符文件名匹配: '${rel_path##*/}' 匹配 '$pattern'" >&2
            return 0 
            ;;
        esac
      else
        # 精确匹配 - 检查完整路径和文件名
        if [[ "$rel_path" == "$pattern" ]] || [[ "${rel_path##*/}" == "$pattern" ]]; then
          [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] ✅ 精确匹配: '$rel_path' 匹配 '$pattern'" >&2
          return 0
        fi
        
        # 检查是否为顶级目录匹配
        if [[ "$rel_path" == "$pattern"/* ]]; then
          [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] ✅ 顶级目录匹配: '$rel_path' 在 '$pattern' 目录下" >&2
          return 0
        fi
      fi
    fi
  done
  
  [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] ❌ 未匹配: '$rel_path'" >&2
  return 1
}

# ---------- 合并规则 ----------
get_merge_rule() {
  local rel_path="$1"
  local A_file="$A_PATH/$rel_path"
  local B_file="$B_PATH/$rel_path"
  local matched_rule=""
  local matched_len=0
  
  for entry in "${MERGE_RULES[@]}"; do
    local rule_path="${entry%%|||*}"
    local rule_val="${entry##*|||}"
    
    if [[ "$rule_path" == */ ]]; then
      # 目录规则
      if [[ "$rel_path/" == "$rule_path"* || "$rel_path" == "${rule_path%/}" ]]; then
        if (( ${#rule_path} > matched_len )); then
          matched_rule="$rule_val"
          matched_len=${#rule_path}
        fi
      fi
    else
      # 文件规则
      if [[ "$rel_path" == "$rule_path" ]]; then
        if (( ${#rule_path} > matched_len )); then
          matched_rule="$rule_val"
          matched_len=${#rule_path}
        fi
      fi
    fi
  done
  
  local rule="${matched_rule:-keep_a}"
  local A_exists=0 B_exists=0
  [[ -f "$A_file" ]] && A_exists=1
  [[ -f "$B_file" ]] && B_exists=1
  
  [[ "$DEBUG_IGNORE" == 1 ]] && echo "[DEBUG] $rel_path: rule=$rule, A_exists=$A_exists, B_exists=$B_exists" >&2
  
  if (( A_exists == 1 && B_exists == 1 )); then
    case "$rule" in
      keep_a) echo "override" ;;
      keep_b) echo "skip" ;;
      keep_both) echo "keep_both" ;;
      *) echo "override" ;;
    esac
  elif (( A_exists == 1 && B_exists == 0 )); then
    echo "add"
  elif (( A_exists == 0 && B_exists == 1 )); then
    [[ "$rule" == "keep_b" ]] && echo "skip" || echo "move_to_delete"
  else
    echo "skip"
  fi
}

echo "🔍 开始分析项目文件..."

# 直接使用 find 获取文件列表
echo "📁 扫描项目A: $A_PATH"
A_LIST=()
while IFS= read -r -d '' file; do
  file="${file#./}"
  file="${file//\\//}"
  A_LIST+=("$file")
done < <(cd "$A_PATH" && find . -type f -print0 2>/dev/null)

echo "📁 扫描项目B: $B_PATH"
B_LIST=()
while IFS= read -r -d '' file; do
  file="${file#./}"
  file="${file//\\//}"
  B_LIST+=("$file")
done < <(cd "$B_PATH" && find . -type f -print0 2>/dev/null)

echo "📂 项目A文件数: ${#A_LIST[@]}"
echo "📂 项目B文件数: ${#B_LIST[@]}"
[[ ${#A_LIST[@]} -eq 0 ]] && echo "⚠️ 警告: 项目A中没有找到文件" >&2

TO_DELETE="$B_PATH/to_delete"
[[ "$DRY_RUN" == 0 ]] && mkdir -p "$TO_DELETE"

echo
echo "🚀 开始合并操作..."
echo

for relpath in "${A_LIST[@]}"; do
  if is_ignored "$relpath"; then
    ((count_ignored++))
    [[ "$DEBUG_IGNORE" == 1 ]] && echo "🚫 [忽略] $relpath"
    continue
  fi
  
  src="$A_PATH/$relpath"
  dst="$B_PATH/$relpath"
  action=$(get_merge_rule "$relpath")
  
  case "$action" in
    add)
      echo "➕ [新增] A → B: $relpath"
      ((count_new++))
      [[ "$DRY_RUN" == 0 ]] && safe_cp "$src" "$dst"
      ;;
    override)
      # 检查文件内容是否相同
      if files_are_identical "$src" "$dst"; then
        echo "⚪ [内容相同] 跳过覆盖: $relpath"
        ((count_same_content++))
      else
        echo "🔁 [覆盖] A → B: $relpath"
        ((count_override++))
        [[ "$DRY_RUN" == 0 ]] && safe_cp "$src" "$dst"
      fi
      ;;
    skip)
      echo "🙅 [跳过] 保留 B: $relpath"
      ((count_keep_b++))
      ;;
    keep_both)
      echo "🪄 [保留两份] B保留，A存为 .a_version: $relpath"
      ((count_keep_both++))
      [[ "$DRY_RUN" == 0 ]] && safe_cp "$src" "$dst.a_version"
      ;;
  esac
done

for relpath in "${B_LIST[@]}"; do
  if is_ignored "$relpath"; then
    ((count_ignored++))
    [[ "$DEBUG_IGNORE" == 1 ]] && echo "🚫 [忽略] $relpath"
    continue
  fi
  action=$(get_merge_rule "$relpath")
  if [[ "$action" == "move_to_delete" ]]; then
    echo "🗑️ [移动到 to_delete] $relpath"
    ((count_deleted++))
    [[ "$DRY_RUN" == 0 ]] && safe_mv "$B_PATH/$relpath" "$TO_DELETE/$relpath"
  fi
done

echo
echo "📊 操作统计："
echo "  ➕ 新增文件：       $count_new"
echo "  🔁 覆盖文件：       $count_override"
echo "  ⚪ 内容相同跳过：   $count_same_content"
echo "  🙅 保留 B 文件：    $count_keep_b"
echo "  🪄 保留两份：       $count_keep_both"
echo "  🚫 忽略文件：       $count_ignored"
echo "  🗑️ 移动到 to_delete：$count_deleted"
echo
[[ "$DRY_RUN" == 1 ]] && echo "🔍 这是干运行模式，实际文件未被修改" || echo "✅ 合并完成"
