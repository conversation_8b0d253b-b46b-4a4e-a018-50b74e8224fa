# 项目迁移脚本 (migration_project.sh)

## 概述
用于自动合并两个项目目录的跨平台脚本，支持灵活的忽略规则和合并策略。

## 功能特性
- ✅ **跨平台支持**: macOS, Linux, Windows (Git Bash/WSL)
- ✅ **智能忽略**: 支持 .gitignore 风格的模式匹配
- ✅ **灵活合并策略**: keep_a, keep_b, keep_both
- ✅ **智能内容比较**: 覆盖前检查文件内容，相同则跳过
- ✅ **干运行模式**: 预览操作而不实际修改文件
- ✅ **调试模式**: 详细的匹配过程输出
- ✅ **安全操作**: 自动创建 to_delete 目录避免误删

## 使用方法

### 基础语法
```bash
./migration_project.sh <A项目路径> <B项目路径> [配置文件路径] [--dry-run] [--debug-ignore]
```

### 参数说明
- `A项目路径`: 源项目目录
- `B项目路径`: 目标项目目录
- `配置文件路径`: 可选，合并规则配置文件
- `--dry-run`: 干运行模式，只显示操作不实际执行
- `--debug-ignore`: 调试模式，显示详细的忽略规则匹配过程

### 示例
```bash
# 基础合并（使用默认规则）
./migration_project.sh /path/to/project_a /path/to/project_b

# 使用配置文件的干运行
./migration_project.sh project_a project_b merge_config.txt --dry-run

# 调试模式查看忽略规则匹配
./migration_project.sh project_a project_b config.txt --dry-run --debug-ignore
```

## 配置文件格式

### 忽略规则
```
# 忽略文件/目录
ignore *.log              # 忽略所有 .log 文件
ignore build/             # 忽略 build 目录及其内容
ignore tools/             # 忽略 tools 目录及其内容
ignore node_modules/      # 忽略 node_modules
ignore **/__pycache__/**  # 忽略所有 __pycache__ 目录
```

### 合并策略
```
# 合并策略
keep_a README.md          # A项目的README覆盖B项目
keep_b src/config.py      # 保留B项目的配置文件
keep_both package.json    # 保留两份，A项目版本存为 .a_version
```

### 完整配置示例
```
# 合并策略
keep_b README.md
keep_b src/generated/*
keep_both package.json

# 忽略规则
ignore *.log
ignore build/
ignore dist/
ignore node_modules/
ignore .git/
ignore tools/
ignore docs/
ignore tests/
ignore __pycache__/**
```

## 支持的忽略模式

### 文件模式
- `file.txt` - 精确匹配文件名
- `*.log` - 通配符匹配（所有 .log 文件）
- `README.md` - 精确匹配特定文件

### 目录模式
- `tools/` - 忽略 tools 目录及其所有内容
- `build/` - 忽略 build 目录
- `**/__pycache__/**` - 忽略所有 __pycache__ 目录

## 跨平台使用

### macOS / Linux
```bash
chmod +x migration_project.sh
./migration_project.sh project_a project_b config.txt --dry-run
```

### Windows
需要使用 Git Bash 或 WSL：

**Git Bash:**
```bash
# 在 Git Bash 中运行
./migration_project.sh project_a project_b config.txt --dry-run
```

**WSL (Windows Subsystem for Linux):**
```bash
# 在 WSL 中运行
chmod +x migration_project.sh
./migration_project.sh /mnt/c/project_a /mnt/c/project_b config.txt --dry-run
```

**PowerShell (通过 Git Bash):**
```powershell
# 启动 Git Bash 并运行脚本
& "C:\Program Files\Git\bin\bash.exe" -c "./migration_project.sh project_a project_b config.txt --dry-run"
```

## 操作统计说明

脚本完成后会显示操作统计：
- ➕ **新增文件**: 从A复制到B的新文件
- 🔁 **覆盖文件**: A覆盖B的已存在文件（内容不同）
- ⚪ **内容相同跳过**: 规则要求覆盖但内容相同，跳过实际操作
- 🙅 **保留B文件**: 按规则保留B项目的文件
- 🪄 **保留两份**: 保留两个版本的文件
- 🚫 **忽略文件**: 被忽略规则匹配的文件
- 🗑️ **移动到 to_delete**: B项目中被移动到 to_delete 目录的文件

## 安全特性

1. **干运行模式**: 使用 `--dry-run` 先预览操作
2. **智能内容比较**: 覆盖前自动检查文件内容，避免不必要的操作
3. **to_delete 目录**: 不直接删除文件，而是移动到 to_delete 目录
4. **兼容性检查**: 自动检测系统环境和必需命令
5. **错误处理**: 安全的文件操作封装

## 故障排除

### 常见问题

1. **命令未找到错误**
   ```
   ❌ 错误: 必需的命令 'find' 未找到
   ```
   - Windows: 请使用 Git Bash 或 WSL
   - 确保系统有基础的 Unix 工具

2. **没有文件被处理**
   - 检查项目路径是否正确
   - 确认目录中有文件
   - 检查是否所有文件都被忽略规则匹配

3. **忽略规则不生效**
   - 使用 `--debug-ignore` 查看匹配过程
   - 确认配置文件格式正确
   - 检查文件路径分隔符（使用 `/`）

### 调试技巧

```bash
# 查看详细的忽略匹配过程
./migration_project.sh project_a project_b config.txt --dry-run --debug-ignore

# 只查看前20行输出
./migration_project.sh project_a project_b config.txt --dry-run | head -20

# 保存操作日志
./migration_project.sh project_a project_b config.txt --dry-run > migration.log 2>&1
```

## 注意事项

1. **备份重要数据**: 在正式合并前建议备份重要项目
2. **先用干运行**: 总是先使用 `--dry-run` 预览操作
3. **检查 to_delete**: 合并后检查 to_delete 目录确认被移动的文件
4. **路径格式**: 在配置文件中使用 Unix 风格路径分隔符 (`/`) 