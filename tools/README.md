# Git Keeper 工具目录

本目录包含Git Keeper项目的所有工具和脚本，按功能进行分类组织。

## 📁 目录结构

```
tools/
├── production/     # 生产环境工具
├── development/    # 开发调试工具  
├── testing/        # 测试工具
└── README.md       # 本文件
```

## 🚀 生产工具 (production/)

用于生产环境或日常使用的稳定工具：

- `quick_test.sh` - 快速MR检查脚本（主要工具）
- `quick_test_mr.py` - MR分析核心脚本
- `build.sh` - 项目构建脚本
- `merge_folders.sh` - 文件夹合并工具

## 🔧 开发工具 (development/)

用于开发过程中的调试和分析：

- `debug_*.py` - 各种调试工具
- `ast_parser_debug.py` - AST解析调试
- `markdown_parser_debug.py` - Markdown解析调试
- `clear_repo_index_cache.py` - 清理缓存工具

## 🧪 测试工具 (testing/)

用于各种测试场景：

- `test_*.py` - 单元测试和集成测试
- `run_*.sh` - 自动化测试脚本
- `analyze_mr.sh` - MR分析测试
- `cleanup_gitee_mr_comments.py` - 清理测试评论

## 📚 使用指南

### 快速开始
```bash
# 运行快速测试（推荐安全版本）
./tools/production/quick_test_safe.sh

# 快速测试，限制文件数
./tools/production/quick_test_safe.sh --max-diff-results 2

# 查看帮助
./tools/production/quick_test_safe.sh --help
```

### 开发调试
```bash
# 调试AST解析
python tools/development/ast_parser_debug.py
```

### 运行测试
```bash
# 运行集成测试
./tools/testing/run_gitee_integration_test.sh
``` 