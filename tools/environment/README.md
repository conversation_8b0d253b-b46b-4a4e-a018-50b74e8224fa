# 环境变量管理工具

本目录包含 Git Keeper 项目的环境变量管理工具，帮助你轻松设置和管理项目的环境变量配置。

## 工具列表

### 1. 环境变量检查脚本 (`check_env.sh`)

检查当前环境变量配置状态，验证必需变量是否已设置。

**用法：**
```bash
./tools/environment/check_env.sh
```

**功能：**
- ✅ 检查必需环境变量
- ⚠️ 检查可选环境变量  
- 📁 检查 .env 文件和权限
- 🐍 检查 python-dotenv 依赖
- ⚙️ 检查配置文件
- 🧪 测试配置加载

### 2. 配置验证脚本 (`validate_config.py`)

深度验证项目配置，显示详细的配置信息和状态。

**用法：**
```bash
python tools/environment/validate_config.py
```

**功能：**
- 📋 显示完整配置信息
- ✅ 检查必需配置项
- 📁 验证文件路径
- 🌍 检查环境变量
- 📊 生成验证总结

### 3. 环境变量快速设置脚本 (`setup_env.sh`)

交互式设置环境变量，自动生成 .env 文件。

**用法：**
```bash
./tools/environment/setup_env.sh
```

**功能：**
- 🚀 交互式配置收集
- 📝 自动生成 .env 文件
- 🔒 设置安全文件权限
- 🔍 自动验证配置
- 💾 备份现有配置

## 快速开始

### 方法1：使用快速设置脚本 (推荐新用户)

```bash
# 运行交互式设置
./tools/environment/setup_env.sh

# 按提示输入配置信息
# 脚本会自动创建 .env 文件并验证配置
```

### 方法2：手动创建 .env 文件

```bash
# 复制示例文件
cp .env.example .env

# 编辑配置文件
vim .env  # 或使用你喜欢的编辑器

# 设置文件权限
chmod 600 .env

# 验证配置
./tools/environment/check_env.sh
```

### 方法3：使用系统环境变量

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export GK_ENV=home
export GIT_TOKEN="your_token_here"
export OLLAMA_ENDPOINT="http://127.0.0.1:11434"
export LLM_MODEL_ID="qwen2.5-coder:3b"

# 重新加载配置
source ~/.bashrc
```

## 必需的环境变量

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `GK_ENV` | 运行环境 | `home`, `dev`, `test`, `prod` |
| `GIT_TOKEN` | Git平台令牌 | `your_git_token_here` |
| `OLLAMA_ENDPOINT` | Ollama服务地址 | `http://127.0.0.1:11434` |
| `LLM_MODEL_ID` | LLM模型ID | `qwen2.5-coder:3b` |

## 常用可选环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `REPO_DIR` | 仓库本地目录 | - |
| `MAX_DIFF_RESULTS` | 最大分析结果数 | `10` |
| `LOG_LEVEL` | 日志级别 | `INFO` |
| `HTTP_PROXY` | HTTP代理 | - |
| `HTTPS_PROXY` | HTTPS代理 | - |

## 使用场景

### 本地开发环境

```bash
# 设置本地开发环境
export GK_ENV=home
export GIT_TOKEN="your_personal_token"
export OLLAMA_ENDPOINT="http://127.0.0.1:11434"
export LOG_LEVEL=DEBUG
```

### CI/CD 环境

```bash
# 设置测试环境
export GK_ENV=test
export GIT_TOKEN="${CI_GIT_TOKEN}"
export OLLAMA_ENDPOINT="http://ollama-service:11434"
export LOG_LEVEL=INFO
```

### 生产环境

```bash
# 设置生产环境
export GK_ENV=prod
export GIT_TOKEN="${PROD_GIT_TOKEN}"
export OLLAMA_ENDPOINT="https://ollama.prod.company.com"
export LOG_LEVEL=WARNING
```

## 安全最佳实践

### 1. 文件权限

```bash
# 设置 .env 文件权限为仅所有者可读写
chmod 600 .env

# 检查权限
ls -la .env
# 应该显示: -rw------- 1 <USER> <GROUP>
```

### 2. Git 忽略

确保 `.gitignore` 包含：
```
.env
.env.local
.env.*.local
```

### 3. 敏感信息处理

- ✅ 使用 `.env.example` 作为模板
- ✅ 在示例文件中使用占位符
- ❌ 永远不要提交真实的敏感信息

### 4. 环境隔离

```bash
# 为不同环境创建不同的配置文件
.env.development
.env.staging
.env.production

# 通过脚本加载对应环境的配置
load_env() {
    local env=${1:-development}
    if [ -f ".env.${env}" ]; then
        export $(cat .env.${env} | xargs)
        echo "✅ 加载 ${env} 环境配置"
    fi
}
```

## 故障排除

### 常见问题

#### 1. 环境变量未生效

```bash
# 检查环境变量
echo $GK_ENV
printenv | grep GK_

# 重新加载 .env 文件
source .env

# 或重启终端
```

#### 2. python-dotenv 未安装

```bash
# 安装依赖
pip install python-dotenv

# 或添加到 requirements.txt
echo "python-dotenv" >> requirements.txt
pip install -r requirements.txt
```

#### 3. 配置文件权限问题

```bash
# 检查文件权限
ls -la .env

# 修复权限
chmod 600 .env
```

#### 4. 配置加载失败

```bash
# 运行诊断
python tools/environment/validate_config.py

# 检查配置文件语法
python -c "from gate_keeper.config import config; print('配置加载成功')"
```

### 调试工具

#### 环境变量摘要

```python
from gate_keeper.config.env_loader import get_loader

# 获取环境变量摘要
summary = get_loader().get_environment_summary()
print(summary)
```

#### 配置映射检查

```python
from gate_keeper.config.env_loader import get_loader

# 查看环境变量映射
mapping = get_loader().get_config_mapping()
for env_var, config_attr in mapping.items():
    print(f"{env_var} -> {config_attr}")
```

## 集成到项目中

### 在配置模块中使用

```python
# gate_keeper/config/config.py
from .env_loader import load_environment_variables, apply_env_to_config

# 加载 .env 文件
load_environment_variables()

# 应用环境变量到当前模块
applied_vars = apply_env_to_config(sys.modules[__name__])
```

### 在应用启动时使用

```python
# gate_keeper/interfaces/cli/main.py
from gate_keeper.config.env_loader import load_environment_variables

def main():
    # 确保环境变量已加载
    load_environment_variables()
    
    # 其他初始化代码...
```

## 扩展和自定义

### 添加新的环境变量

1. 在 `env_loader.py` 的 `get_config_mapping()` 方法中添加映射
2. 在 `.env.example` 中添加示例
3. 更新文档说明

### 自定义类型转换

在 `env_loader.py` 的 `_convert_env_value()` 方法中添加自定义转换逻辑。

---

## 相关文档

- [环境变量设置指南](../../docs/environment-setup-guide.md)
- [项目配置文档](../../gate_keeper/config/README.md)
- [部署指南](../../docs/deployment-guide.md) 