#!/usr/bin/env python3
"""
Git Keeper 配置验证脚本
用法: python tools/environment/validate_config.py
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from gate_keeper.config import config
except ImportError as e:
    print(f"❌ 无法导入配置模块: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def mask_sensitive_value(key: str, value: str) -> str:
    """对敏感信息进行脱敏处理"""
    sensitive_keys = ['token', 'key', 'userid', 'password', 'secret']
    
    if any(sensitive in key.lower() for sensitive in sensitive_keys):
        if len(value) <= 8:
            return "***"
        return f"{value[:4]}***{value[-4:]}"
    return value

def validate_config():
    """验证当前配置"""
    print("📋 Git Keeper 配置验证")
    print("=" * 40)
    
    # 基础配置检查
    print(f"\n🔧 基础配置:")
    print(f"  环境: {config.ENV}")
    print(f"  调试模式: {getattr(config, 'DEBUG', 'not_set')}")
    print(f"  日志级别: {getattr(config, 'log_level', 'not_set')}")
    print(f"  项目根目录: {getattr(config, 'project_root_dir', 'not_set')}")
    
    # Git 配置检查
    print(f"\n🔗 Git 配置:")
    print(f"  Git平台: {getattr(config, 'git_platform', 'not_set')}")
    token = getattr(config, 'token', 'not_set')
    print(f"  Token: {mask_sensitive_value('token', token)}")
    print(f"  仓库目录: {getattr(config, 'repo_dir', 'not_set')}")
    print(f"  仓库URL: {getattr(config, 'repo_url', 'not_set')}")
    print(f"  开发分支: {getattr(config, 'devBranch', 'not_set')}")
    print(f"  基础分支: {getattr(config, 'baseBranch', 'not_set')}")
    print(f"  MR ID: {getattr(config, 'mr_id', 'not_set')}")
    
    # LLM 配置检查
    print(f"\n🤖 LLM 配置:")
    print(f"  LLM模型: {getattr(config, 'llm_model_id', 'not_set')}")
    print(f"  Ollama端点: {getattr(config, 'ollama_endpoint', 'not_set')}")
    ollama_token = getattr(config, 'ollama_token', 'not_set')
    print(f"  Ollama Token: {mask_sensitive_value('token', ollama_token)}")
    print(f"  并发数: {getattr(config, 'llm_concurrent', 'not_set')}")
    print(f"  最大Token数: {getattr(config, 'default_llm_max_tokens', 'not_set')}")
    
    # 华为服务配置检查
    fuyao_base_url = getattr(config, 'fuyao_base_url', 'not_set')
    fuyao_userid = getattr(config, 'fuyao_userid', 'not_set')
    if fuyao_base_url != 'not_set' or fuyao_userid != 'not_set':
        print(f"\n🏢 华为服务配置:")
        print(f"  Fuyao URL: {fuyao_base_url}")
        print(f"  Fuyao UserID: {mask_sensitive_value('userid', fuyao_userid)}")
    
    # 规则配置检查
    print(f"\n📜 规则配置:")
    print(f"  规则文件路径: {getattr(config, 'rule_file_path', 'not_set')}")
    print(f"  规则文件URL: {getattr(config, 'rule_file_url', 'not_set')}")
    print(f"  规则表格: {getattr(config, 'rule_include_sheets', 'not_set')}")
    print(f"  规则合并字段: {getattr(config, 'rule_merge_on', 'not_set')}")
    
    # 性能配置检查
    print(f"\n⚡ 性能配置:")
    print(f"  最大分析结果数: {getattr(config, 'max_diff_results', 'not_set')}")
    print(f"  每项最大LLM调用: {getattr(config, 'max_llm_calls_per_check_mr_result', 'not_set')}")
    print(f"  调用图半径: {getattr(config, 'graph_call_chain_radius', 'not_set')}")
    
    # 缓存配置检查
    print(f"\n💾 缓存配置:")
    print(f"  缓存目录: {getattr(config, 'repo_cache_dir', 'not_set')}")
    print(f"  最大缓存仓库: {getattr(config, 'max_cache_repos', 'not_set')}")
    print(f"  最大缓存分支: {getattr(config, 'max_cache_branches', 'not_set')}")
    print(f"  最大缓存提交: {getattr(config, 'max_cache_branch_commits', 'not_set')}")
    
    # 文件过滤配置检查
    exclude_patterns = getattr(config, 'exclude_patterns', [])
    if exclude_patterns:
        print(f"\n🚫 文件过滤配置:")
        for pattern in exclude_patterns[:5]:  # 只显示前5个
            print(f"  - {pattern}")
        if len(exclude_patterns) > 5:
            print(f"  ... 还有 {len(exclude_patterns) - 5} 个模式")
    
    # 输出配置检查
    print(f"\n📤 输出配置:")
    print(f"  输出目录: {getattr(config, 'dest_dir', 'not_set')}")
    print(f"  输出文件名: {getattr(config, 'output_file_name', 'not_set')}")
    print(f"  日志目录: {getattr(config, 'log_dir', 'not_set')}")
    
    return True

def check_required_configs():
    """检查必需的配置项"""
    print(f"\n✅ 必需配置检查:")
    
    required_configs = [
        ('token', 'Git Token'),
        ('ollama_endpoint', 'Ollama 端点'),
        ('llm_model_id', 'LLM 模型ID'),
        ('git_platform', 'Git 平台'),
        ('rule_file_path', '规则文件路径'),
    ]
    
    missing_configs = []
    
    for config_key, display_name in required_configs:
        value = getattr(config, config_key, 'not_set')
        if value == 'not_set' or not value:
            missing_configs.append(display_name)
            print(f"  ❌ {display_name}: 未设置")
        else:
            print(f"  ✅ {display_name}: 已设置")
    
    if missing_configs:
        print(f"\n❌ 缺少必需配置: {', '.join(missing_configs)}")
        return False
    
    print(f"\n✅ 所有必需配置都已设置")
    return True

def check_file_paths():
    """检查文件路径是否存在"""
    print(f"\n📁 文件路径检查:")
    
    paths_to_check = [
        ('rule_file_path', '规则文件'),
        ('repo_dir', '仓库目录'),
        ('dest_dir', '输出目录'),
        ('log_dir', '日志目录'),
    ]
    
    for config_key, display_name in paths_to_check:
        path = getattr(config, config_key, 'not_set')
        if path == 'not_set' or not path:
            print(f"  ⚠️  {display_name}: 未配置")
            continue
            
        path_obj = Path(path)
        if path_obj.exists():
            if path_obj.is_file():
                print(f"  ✅ {display_name}: 文件存在 ({path})")
            elif path_obj.is_dir():
                print(f"  ✅ {display_name}: 目录存在 ({path})")
        else:
            print(f"  ❌ {display_name}: 路径不存在 ({path})")

def check_environment_variables():
    """检查环境变量设置"""
    print(f"\n🌍 环境变量检查:")
    
    env_vars_to_check = [
        'GK_ENV',
        'GIT_TOKEN', 
        'OLLAMA_ENDPOINT',
        'LLM_MODEL_ID',
        'HTTP_PROXY',
        'HTTPS_PROXY',
    ]
    
    for var in env_vars_to_check:
        value = os.environ.get(var)
        if value:
            masked_value = mask_sensitive_value(var, value)
            print(f"  ✅ {var}: {masked_value}")
        else:
            print(f"  ⚠️  {var}: 未设置")

def main():
    """主函数"""
    try:
        # 基础配置验证
        validate_config()
        
        # 必需配置检查
        required_ok = check_required_configs()
        
        # 文件路径检查
        check_file_paths()
        
        # 环境变量检查
        check_environment_variables()
        
        # 总结
        print(f"\n📊 验证总结:")
        if required_ok:
            print("✅ 配置验证通过！你的配置看起来不错。")
            print("\n💡 接下来你可以:")
            print("1. 运行测试: python -m pytest tests/")
            print("2. 执行分析: python gate_keeper/interfaces/cli/main.py --help")
            print("3. 查看文档: docs/environment-setup-guide.md")
            return True
        else:
            print("❌ 配置验证失败，请修复缺少的配置项。")
            print("\n💡 修复建议:")
            print("1. 检查 .env 文件或环境变量设置")
            print("2. 参考文档: docs/environment-setup-guide.md")
            print("3. 运行环境检查: ./tools/environment/check_env.sh")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 