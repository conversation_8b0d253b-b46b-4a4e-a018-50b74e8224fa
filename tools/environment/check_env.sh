#!/bin/bash
# Git Keeper 环境变量检查脚本
# 用法: ./tools/environment/check_env.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo "🔍 Git Keeper 环境变量配置检查"
echo "=================================="

# 必需的环境变量
required_vars=(
    "GK_ENV"
    "GIT_TOKEN"
    "OLLAMA_ENDPOINT"
    "LLM_MODEL_ID"
)

# 可选但重要的环境变量
optional_vars=(
    "REPO_DIR"
    "MAX_DIFF_RESULTS"
    "LOG_LEVEL"
    "HTTP_PROXY"
    "HTTPS_PROXY"
    "FUYAO_BASE_URL"
    "FUYAO_USERID"
    "OPENAI_API_KEY"
    "DEEPSEEK_API_KEY"
)

# 检查必需环境变量
echo ""
echo "📋 必需环境变量检查:"
missing_required=0
for var in "${required_vars[@]}"; do
    if [ -n "${!var}" ]; then
        # 对敏感信息进行脱敏显示
        if [[ "$var" == *"TOKEN"* ]] || [[ "$var" == *"KEY"* ]]; then
            masked_value="${!var:0:8}***"
            print_success "$var = $masked_value"
        else
            print_success "$var = ${!var}"
        fi
    else
        print_error "$var = (未设置)"
        missing_required=$((missing_required + 1))
    fi
done

# 检查可选环境变量
echo ""
echo "📋 可选环境变量检查:"
for var in "${optional_vars[@]}"; do
    if [ -n "${!var}" ]; then
        # 对敏感信息进行脱敏显示
        if [[ "$var" == *"TOKEN"* ]] || [[ "$var" == *"KEY"* ]] || [[ "$var" == *"USERID"* ]]; then
            masked_value="${!var:0:8}***"
            print_success "$var = $masked_value"
        else
            print_success "$var = ${!var}"
        fi
    else
        print_warning "$var = (未设置)"
    fi
done

echo ""
echo "🔧 当前配置环境: ${GK_ENV:-home}"

# 检查 .env 文件
echo ""
echo "📁 配置文件检查:"
if [ -f ".env" ]; then
    print_success ".env 文件存在"
    
    # 检查文件权限
    perm=$(stat -f %A .env 2>/dev/null || stat -c %a .env 2>/dev/null)
    if [ "$perm" = "600" ]; then
        print_success ".env 文件权限正确 ($perm)"
    else
        print_warning ".env 文件权限: $perm (建议设置为 600)"
        echo "  运行: chmod 600 .env"
    fi
else
    print_warning ".env 文件不存在"
    echo "  可以创建 .env 文件来管理环境变量"
fi

# 检查 python-dotenv
echo ""
echo "🐍 Python 依赖检查:"
if python3 -c "import dotenv" 2>/dev/null; then
    print_success "python-dotenv 已安装"
else
    print_warning "python-dotenv 未安装"
    echo "  运行: pip install python-dotenv"
fi

# 检查配置文件
echo ""
echo "⚙️  配置文件检查:"
config_env=${GK_ENV:-home}
config_file="gate_keeper/config/config_${config_env}.py"

if [ -f "$config_file" ]; then
    print_success "配置文件存在: $config_file"
else
    print_error "配置文件不存在: $config_file"
fi

# 测试配置加载
echo ""
echo "🧪 配置加载测试:"
if python3 -c "
import sys
sys.path.append('.')
try:
    from gate_keeper.config import config
    print(f'✅ 配置加载成功')
    print(f'  环境: {config.ENV}')
    print(f'  Git平台: {config.git_platform}')
    print(f'  LLM模型: {config.llm_model_id}')
except Exception as e:
    print(f'❌ 配置加载失败: {e}')
    sys.exit(1)
" 2>/dev/null; then
    print_success "配置加载测试通过"
else
    print_error "配置加载测试失败"
fi

# 总结
echo ""
echo "📊 检查总结:"
if [ $missing_required -eq 0 ]; then
    print_success "所有必需的环境变量都已设置"
    echo ""
    print_info "🎉 环境配置检查完成！你的环境变量配置看起来不错。"
else
    print_error "缺少 $missing_required 个必需的环境变量"
    echo ""
    print_error "❗ 请设置缺少的环境变量后再运行程序。"
    echo ""
    echo "💡 快速设置建议:"
    echo "1. 创建 .env 文件: touch .env"
    echo "2. 参考文档设置环境变量: docs/environment-setup-guide.md"
    echo "3. 运行配置验证: python tools/environment/validate_config.py"
    exit 1
fi 