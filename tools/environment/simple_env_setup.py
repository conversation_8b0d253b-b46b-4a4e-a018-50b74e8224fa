#!/usr/bin/env python3
"""
Git Keeper 简化环境变量设置工具
只处理项目实际使用的少数环境变量
"""

import os
import sys
from pathlib import Path


def check_python_dotenv():
    """检查是否安装了 python-dotenv"""
    try:
        import dotenv
        return True
    except ImportError:
        return False

def load_dotenv_if_available():
    """如果可用，加载 .env 文件"""
    if not check_python_dotenv():
        print("⚠️  python-dotenv 未安装，无法自动加载 .env 文件")
        print("   可选安装: pip install python-dotenv")
        return False
    
    try:
        from dotenv import load_dotenv
        env_file = Path(".env")
        if env_file.exists():
            load_dotenv(env_file)
            print(f"✅ 已加载 .env 文件")
            return True
        else:
            print("ℹ️  未找到 .env 文件")
            return False
    except Exception as e:
        print(f"❌ 加载 .env 文件失败: {e}")
        return False

def check_current_env_vars():
    """检查当前项目实际使用的环境变量"""
    print("\n📋 项目实际使用的环境变量:")
    
    # 项目实际使用的环境变量
    env_vars = {
        'GK_ENV': {
            'description': '配置环境选择',
            'default': 'home',
            'options': 'home/dev/test/prod',
            'required': False
        },
        'EXCLUDE_PATTERNS': {
            'description': '文件过滤模式',
            'default': '无',
            'options': '逗号分隔的模式',
            'required': False
        },
        'webhook_info': {
            'description': 'Git webhook信息',
            'default': '无',
            'options': 'webhook URL',
            'required': False
        }
    }
    
    for var_name, info in env_vars.items():
        current_value = os.environ.get(var_name)
        if current_value:
            # 对敏感信息进行简单脱敏
            if 'token' in var_name.lower() or 'webhook' in var_name.lower():
                display_value = f"{current_value[:8]}..." if len(current_value) > 8 else "***"
            else:
                display_value = current_value
            print(f"  ✅ {var_name}: {display_value}")
        else:
            print(f"  ⚪ {var_name}: 未设置 (默认: {info['default']})")
        
        print(f"     说明: {info['description']}")
        if info['options'] != '无':
            print(f"     选项: {info['options']}")
        print()

def create_simple_env_file():
    """创建简化的 .env 文件"""
    print("\n🔧 创建简化的 .env 文件")
    
    if Path(".env").exists():
        choice = input("⚠️  .env 文件已存在，是否覆盖？(y/n): ")
        if choice.lower() != 'y':
            print("取消创建")
            return
    
    print("\n请输入以下配置 (按 Enter 使用默认值):")
    
    # 收集用户输入
    gk_env = input("运行环境 [home/dev/test/prod] (默认: home): ").strip() or "home"
    exclude_patterns = input("文件过滤模式 (可选，逗号分隔): ").strip()
    
    # 创建 .env 文件
    env_content = f"""# Git Keeper 环境变量配置
# 自动生成于 {os.popen('date').read().strip()}

# 配置环境选择
GK_ENV={gk_env}
"""
    
    if exclude_patterns:
        env_content += f"\n# 文件过滤模式\nEXCLUDE_PATTERNS={exclude_patterns}\n"
    
    env_content += """
# 其他可选环境变量:
# webhook_info=your_webhook_url_here
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    # 设置文件权限
    os.chmod(".env", 0o600)
    
    print("✅ .env 文件已创建")
    print("✅ 文件权限已设置为 600")

def show_config_usage():
    """显示配置使用说明"""
    print("""
💡 配置使用说明:

1. 环境变量 vs 配置文件:
   - 环境变量: 用于少数全局设置 (如 GK_ENV)
   - 配置文件: 用于大部分应用配置 (gate_keeper/config/config_*.py)

2. 配置优先级:
   环境变量 > 配置文件 > 默认值

3. 主要配置在这些文件中:
   - gate_keeper/config/config_home.py  (本地开发)
   - gate_keeper/config/config_dev.py   (开发环境)
   - gate_keeper/config/config_test.py  (测试环境)
   - gate_keeper/config/config_prod.py  (生产环境)

4. 使用 python-dotenv (可选):
   pip install python-dotenv
   # 自动加载 .env 文件中的环境变量

5. 验证当前配置:
   python -c "from gate_keeper.config import config; print(f'当前环境: {config.ENV}')"
""")

def main():
    """主函数"""
    print("🔧 Git Keeper 简化环境变量设置")
    print("=" * 40)
    
    # 检查 python-dotenv
    has_dotenv = check_python_dotenv()
    if has_dotenv:
        print("✅ python-dotenv 已安装")
        load_dotenv_if_available()
    else:
        print("⚪ python-dotenv 未安装 (可选)")
    
    # 检查当前环境变量
    check_current_env_vars()
    
    # 询问用户操作
    print("请选择操作:")
    print("1. 创建/更新 .env 文件")
    print("2. 查看配置使用说明")
    print("3. 退出")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        create_simple_env_file()
    elif choice == "2":
        show_config_usage()
    elif choice == "3":
        print("退出")
        return
    else:
        print("无效选择")
        return
    
    # 再次检查环境变量
    if choice == "1":
        print("\n📋 更新后的环境变量:")
        load_dotenv_if_available()
        check_current_env_vars()

if __name__ == "__main__":
    main() 