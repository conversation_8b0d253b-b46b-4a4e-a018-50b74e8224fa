#!/bin/bash
# Git Keeper 环境变量快速设置脚本
# 用法: ./tools/environment/setup_env.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
    echo "=================================="
}

# 检查是否在项目根目录
if [ ! -f "gate_keeper/config/config.py" ]; then
    print_error "请在项目根目录运行此脚本"
    exit 1
fi

print_header "🚀 Git Keeper 环境变量快速设置"

# 检查现有 .env 文件
if [ -f ".env" ]; then
    print_warning ".env 文件已存在"
    read -p "是否要备份现有文件？(y/n): " backup_choice
    if [[ $backup_choice =~ ^[Yy]$ ]]; then
        backup_file=".env.backup.$(date +%Y%m%d_%H%M%S)"
        cp .env "$backup_file"
        print_success "已备份到: $backup_file"
    fi
    
    read -p "是否要覆盖现有 .env 文件？(y/n): " overwrite_choice
    if [[ ! $overwrite_choice =~ ^[Yy]$ ]]; then
        print_info "取消设置，退出脚本"
        exit 0
    fi
fi

echo ""
print_info "请输入以下配置信息 (按 Enter 使用默认值):"

# 收集用户输入
echo ""
echo "🔧 基础配置:"
read -p "运行环境 [home/dev/test/prod] (默认: home): " env_input
GK_ENV=${env_input:-home}

echo ""
echo "🔗 Git 配置:"
read -p "Git Token (必需): " git_token
while [ -z "$git_token" ]; do
    print_error "Git Token 是必需的"
    read -p "Git Token (必需): " git_token
done

read -p "仓库 URL (可选): " repo_url
read -p "仓库本地目录 (可选): " repo_dir

echo ""
echo "🤖 LLM 配置:"
read -p "Ollama 端点 (默认: http://127.0.0.1:11434): " ollama_endpoint
ollama_endpoint=${ollama_endpoint:-http://127.0.0.1:11434}

read -p "LLM 模型 ID (默认: qwen2.5-coder:3b): " llm_model
llm_model=${llm_model:-qwen2.5-coder:3b}

read -p "LLM 并发数 (默认: 1): " llm_concurrent
llm_concurrent=${llm_concurrent:-1}

echo ""
echo "⚙️  应用配置:"
read -p "日志级别 [DEBUG/INFO/WARNING/ERROR] (默认: INFO): " log_level
log_level=${log_level:-INFO}

read -p "最大分析结果数 (默认: 10): " max_diff_results
max_diff_results=${max_diff_results:-10}

echo ""
echo "🌐 代理配置 (可选):"
read -p "HTTP 代理 (如: http://127.0.0.1:7890): " http_proxy
read -p "HTTPS 代理 (如: http://127.0.0.1:7890): " https_proxy

echo ""
echo "🏢 华为服务配置 (可选):"
read -p "Fuyao Base URL: " fuyao_base_url
read -p "Fuyao User ID: " fuyao_userid

# 创建 .env 文件
print_info "正在创建 .env 文件..."

cat > .env << EOF
# Git Keeper 环境变量配置
# 由 setup_env.sh 脚本生成于 $(date)

# ================================
# 基础环境配置
# ================================
GK_ENV=${GK_ENV}

# ================================
# Git 平台配置
# ================================
GIT_TOKEN=${git_token}
EOF

# 添加可选的 Git 配置
if [ -n "$repo_url" ]; then
    echo "REPO_URL=${repo_url}" >> .env
fi

if [ -n "$repo_dir" ]; then
    echo "REPO_DIR=${repo_dir}" >> .env
fi

cat >> .env << EOF

# ================================
# LLM 服务配置
# ================================
OLLAMA_ENDPOINT=${ollama_endpoint}
LLM_MODEL_ID=${llm_model}
LLM_CONCURRENT=${llm_concurrent}

# ================================
# 应用配置
# ================================
LOG_LEVEL=${log_level}
MAX_DIFF_RESULTS=${max_diff_results}

# ================================
# 文件过滤配置
# ================================
EXCLUDE_PATTERNS=*.pyc,__pycache__,*.log,node_modules/**,tests/*
EOF

# 添加可选的代理配置
if [ -n "$http_proxy" ]; then
    echo "" >> .env
    echo "# ================================" >> .env
    echo "# 代理配置" >> .env
    echo "# ================================" >> .env
    echo "HTTP_PROXY=${http_proxy}" >> .env
fi

if [ -n "$https_proxy" ]; then
    if [ -z "$http_proxy" ]; then
        echo "" >> .env
        echo "# ================================" >> .env
        echo "# 代理配置" >> .env
        echo "# ================================" >> .env
    fi
    echo "HTTPS_PROXY=${https_proxy}" >> .env
fi

# 添加可选的华为服务配置
if [ -n "$fuyao_base_url" ] || [ -n "$fuyao_userid" ]; then
    echo "" >> .env
    echo "# ================================" >> .env
    echo "# 华为服务配置" >> .env
    echo "# ================================" >> .env
    
    if [ -n "$fuyao_base_url" ]; then
        echo "FUYAO_BASE_URL=${fuyao_base_url}" >> .env
    fi
    
    if [ -n "$fuyao_userid" ]; then
        echo "FUYAO_USERID=${fuyao_userid}" >> .env
    fi
fi

# 设置文件权限
chmod 600 .env
print_success ".env 文件已创建并设置权限为 600"

# 安装 python-dotenv (如果需要)
echo ""
print_info "检查 python-dotenv 依赖..."
if python3 -c "import dotenv" 2>/dev/null; then
    print_success "python-dotenv 已安装"
else
    print_warning "python-dotenv 未安装"
    read -p "是否要安装 python-dotenv？(y/n): " install_dotenv
    if [[ $install_dotenv =~ ^[Yy]$ ]]; then
        pip install python-dotenv
        print_success "python-dotenv 安装完成"
    fi
fi

# 验证配置
echo ""
print_info "验证配置..."
if ./tools/environment/check_env.sh; then
    print_success "环境变量设置完成！"
    echo ""
    print_info "接下来你可以:"
    echo "1. 运行配置验证: python tools/environment/validate_config.py"
    echo "2. 运行测试: python -m pytest tests/"
    echo "3. 执行分析: python gate_keeper/interfaces/cli/main.py --help"
    echo "4. 查看文档: docs/environment-setup-guide.md"
else
    print_warning "配置验证发现一些问题，请检查输出信息"
fi

echo ""
print_info "💡 提示:"
echo "- .env 文件包含敏感信息，请勿提交到版本控制系统"
echo "- 如需修改配置，可以直接编辑 .env 文件"
echo "- 或重新运行此脚本: ./tools/environment/setup_env.sh" 