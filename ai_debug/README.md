# AI调试工具集

本目录包含用于调试和测试Git Keeper项目的各种工具脚本。

## 工具列表

### 1. 清理Gitee MR评论脚本
**文件**: `cleanup_gitee_mr_comments.py`

**功能**: 一次性清理Gitee指定MR下的所有评论

**使用方法**:
1. 修改脚本中的 `MR_ID` 变量为需要清理的MR编号
2. 确保 `config.py` 中配置了正确的 `token` 和 `repo_url`
3. 运行脚本: `python ai_debug/cleanup_gitee_mr_comments.py`

**特性**:
- 分页获取指定MR的所有评论
- 批量删除所有评论
- 显示删除进度和结果统计
- 防止API限流的延时机制
- 用户确认机制，避免误删

**示例输出**:
```
🔧 AI调试工具：清理 Gitee MR #8 的所有评论
仓库: https://gitee.com/your-repo/your-project
============================================================
📋 正在获取评论列表...
共获取到 15 条评论

⚠️  确认删除 MR #8 的所有 15 条评论吗？(y/N): y

🗑️  开始删除评论...
✅ [1/15] 已删除评论ID: 12345 | 作者: user1 | 内容: 这是一条测试评论
...
🎯 清理完成统计:
   📊 总评论数: 15
   ✅ 成功删除: 15
   ❌ 删除失败: 0
   📈 成功率: 100.0%
```

### 2. Gitee集成测试脚本
**文件**: `run_gitee_integration_test.sh`

**功能**: 运行Gitee平台的完整集成测试

**使用方法**:
```bash
# 直接运行
./ai_debug/run_gitee_integration_test.sh

# 或者
bash ai_debug/run_gitee_integration_test.sh
```

**测试内容**:
- 获取MR信息
- 获取MR评论列表
- 发布单条评论
- 评论排重检测
- 批量评论发布
- MR分析集成功能
- ReportService直接发布
- 测试评论统计

### 3. MR分析脚本
**文件**: `analyze_mr.sh`

**功能**: 使用AI模型分析MR代码质量

**使用方法**:
```bash
# 直接运行
./ai_debug/analyze_mr.sh

# 或者
bash ai_debug/analyze_mr.sh
```

**功能特性**:
- 基于Ollama本地AI模型
- 使用编码规范Excel文件
- 支持并发分析
- 生成详细分析报告
- 调试模式支持

### 4. 其他调试工具
- `ast_parser_debug.py` - AST解析器调试
- `markdown_parser_debug.py` - Markdown解析器调试
- `debug_parser_legacy.py` - 遗留解析器调试

## 使用示例

### 清理MR评论
```bash
# 修改脚本中的MR_ID后运行
python ai_debug/cleanup_gitee_mr_comments.py
```

### 运行集成测试
```bash
# 测试Gitee平台功能
./ai_debug/run_gitee_integration_test.sh
```

### 分析MR代码
```bash
# 使用AI分析代码质量
./ai_debug/analyze_mr.sh
```

## 注意事项

1. **权限要求**: 确保有足够的Gitee API权限来删除评论
2. **Token配置**: 使用有效的Gitee Personal Access Token
3. **API限流**: 脚本内置延时机制，避免触发API限流
4. **备份建议**: 删除前建议先备份重要评论内容
5. **环境要求**: 确保Python环境和依赖包已正确安装
6. **Ollama服务**: MR分析需要本地Ollama服务运行

## 开发规范

- 所有调试工具应包含详细的使用说明
- 提供清晰的错误处理和进度显示
- 支持用户确认机制，避免误操作
- 遵循项目的编码规范和命名约定
- Shell脚本应自动切换到项目根目录
- 提供环境检查和错误提示

## 调试最佳实践

1. **环境检查**: 运行前检查必要文件和配置
2. **错误处理**: 提供详细的错误信息和解决建议
3. **进度显示**: 长时间操作应显示进度信息
4. **结果统计**: 提供操作结果的统计信息
5. **日志记录**: 重要操作应记录日志

## 扩展指南

添加新的调试工具：

1. 在`ai_debug`目录下创建新的Python文件
2. 实现调试逻辑
3. 添加命令行参数支持
4. 更新本README文档
5. 添加使用示例

## 注意事项

- 调试工具仅用于开发阶段
- 生产环境中应移除或禁用调试输出
- 注意保护敏感信息，避免在调试输出中泄露

### 5. Gitee集成测试工具

#### `run_gitee_integration_test.sh`
- **功能**: 运行Gitee平台集成测试
- **用途**: 验证Gitee客户端功能、评论管理、MR分析等
- **运行**: `./run_gitee_integration_test.sh`

#### `run_mr_detection_test.sh`
- **功能**: 运行MR检测、结果提交、重复检测和排重的集成测试
- **用途**: 验证完整的MR检测流程和评论排重功能
- **运行**: `./run_mr_detection_test.sh`

### 6. 评论清理工具

#### `cleanup_gitee_mr_comments.py`
- **功能**: 清理Gitee MR的测试评论
- **用途**: 删除指定MR的所有评论，用于测试清理
- **运行**: `python cleanup_gitee_mr_comments.py`

#### `cleanup_gitee_mr_comments.sh`
- **功能**: 批量清理Gitee MR评论的Shell脚本
- **用途**: 分页获取并删除所有评论
- **运行**: `./cleanup_gitee_mr_comments.sh`

### 7. 调试脚本

#### `gitee_deduplication_debug.py`
- **功能**: Gitee评论排重功能调试
- **用途**: 测试评论排重算法和Gitee集成
- **运行**: `python gitee_deduplication_debug.py`

## 测试场景

### MR检测集成测试场景

#### 场景1: 基础MR检测和提交流程
- 获取MR信息
- 首次检测和提交结果
- 重复检测和提交（排重验证）
- 验证排重效果

#### 场景2: 相似度排重测试
- 提交相似评论（应该被排重）
- 提交不同评论（不应该被排重）
- 验证排重算法准确性

#### 场景3: 批量评论排重测试
- 批量提交包含重复内容的评论
- 验证批量排重效果
- 确认最终评论数量正确

#### 场景4: MR分析集成测试
- 模拟MR分析结果
- 生成和提交分析报告
- 验证报告内容和格式

#### 场景5: 错误处理和恢复测试
- 测试无效输入处理
- 模拟网络错误
- 验证错误恢复机制

#### 场景6: 性能和限流测试
- 测试批量操作性能
- 验证API限流处理
- 确认响应时间符合要求

#### 场景7: 数据一致性验证
- 验证MR信息字段完整性
- 验证评论数据格式
- 确认数据一致性

#### 场景8: 评论管理完整工作流
- 提交多种类型评论
- 验证评论类型分布
- 确认排重功能正常

#### 场景9: 真实场景模拟
- 模拟首次代码提交和检测
- 模拟问题修复后重新提交
- 验证最终状态

## 配置说明

### 测试配置
测试使用 `tests/integration/test_config.py` 进行配置管理：

```python
# 测试MR配置
TEST_MR_CONFIG = {
    "mr_id": 8,  # 测试用的MR ID
    "repo_url": "https://gitee.com/archer/git_keeper",  # 测试仓库URL
    "base_branch": "main",  # 基础分支
    "dev_branch": "feature/test",  # 开发分支
}

# 评论排重测试配置
COMMENT_DEDUPLICATION_CONFIG = {
    "similarity_threshold": 0.8,  # 相似度阈值
    "max_retries": 3,  # 最大重试次数
    "retry_delay": 1.0,  # 重试延时（秒）
    "api_rate_limit_delay": 0.5,  # API限流延时（秒）
}
```

### 环境要求
- Python 3.8+
- 有效的Gitee Token
- 网络连接
- 目标MR存在且可访问

## 使用方法

### 运行完整测试
```bash
# 运行Gitee集成测试
./run_gitee_integration_test.sh

# 运行MR检测集成测试
./run_mr_detection_test.sh
```

### 运行特定测试
```bash
# 运行特定测试类
python -m pytest tests/integration/test_gitee_mr_detection_deduplication.py::TestGiteeMRDetectionDeduplication::test_gitee_mr_detection_and_submission_flow -v -s

# 运行特定标记的测试
python -m pytest tests/integration/test_gitee_mr_detection_deduplication.py -m "gitee or integration" -v -s
```

### 清理测试数据
```bash
# 清理指定MR的评论
python cleanup_gitee_mr_comments.py

# 批量清理评论
./cleanup_gitee_mr_comments.sh
```

## 故障排除

### 常见问题

1. **Token无效或过期**
   - 检查 `gate_keeper/config/config.py` 中的token配置
   - 确认token有足够的权限

2. **网络连接问题**
   - 检查网络连接
   - 确认可以访问Gitee API

3. **MR不存在或无法访问**
   - 检查MR ID是否正确
   - 确认MR状态和权限

4. **API限流**
   - 增加重试延时
   - 减少并发请求

5. **测试数据问题**
   - 检查测试配置文件
   - 确认测试数据格式正确

### 调试建议

1. **启用详细输出**
   ```bash
   python -m pytest -v -s --tb=long
   ```

2. **检查日志**
   - 查看测试输出日志
   - 检查错误信息

3. **分步调试**
   - 运行单个测试用例
   - 逐步验证每个步骤

## 扩展开发

### 添加新测试
1. 在 `tests/integration/` 目录下创建新的测试文件
2. 继承现有的测试基类
3. 添加新的测试方法
4. 更新配置文件

### 支持新平台
1. 实现新的Git客户端
2. 创建平台特定的测试类
3. 复用通用的测试逻辑

### 添加新功能
1. 扩展测试配置
2. 添加新的测试场景
3. 更新文档说明

## 维护说明

### 定期维护
- 更新测试数据
- 检查API兼容性
- 清理过期的测试评论

### 版本兼容性
- 确保测试与最新代码兼容
- 更新测试用例以反映代码变更
- 维护向后兼容性

## 贡献指南

1. 遵循现有的代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 确保所有测试通过

## 许可证

本工具集遵循项目的整体许可证。 