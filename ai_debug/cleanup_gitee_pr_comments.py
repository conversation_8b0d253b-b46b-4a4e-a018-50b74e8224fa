#!/usr/bin/env python3
"""
AI调试工具：一次性清理Gitee指定MR下所有评论的脚本

使用方法:
1. 修改脚本中的 MR_ID 变量为需要清理的MR编号
2. 确保 config.py 中配置了正确的 token 和 repo_url
3. 运行脚本: python ai_debug/cleanup_gitee_mr_comments.py

功能:
- 分页获取指定MR的所有评论
- 批量删除所有评论
- 显示删除进度和结果统计
- 防止API限流的延时机制
"""
import os
import sys
import time

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from gate_keeper.config.config import repo_url, token
from gate_keeper.infrastructure.git.gitee.client import Gitee
# ====== 用户需手动设置 ======
# 使用统一的测试配置
from tests.integration.test_config import TEST_MR_CONFIG

MR_ID = TEST_MR_CONFIG["mr_id"]  # 需要清理的MR编号
# ===========================

def get_all_mr_comments(gitee, repo_url, mr_id):
    """分页获取MR的所有评论"""
    all_comments = []
    page = 1
    per_page = 100
    while True:
        comments = gitee.get_mr_comments(repo_url, mr_id, page=page, per_page=per_page)
        if not comments:
            break
        all_comments.extend(comments)
        if len(comments) < per_page:
            break
        page += 1
    return all_comments

def main():
    print(f"🔧 AI调试工具：清理 Gitee MR #{MR_ID} 的所有评论")
    print(f"仓库: {repo_url}")
    print("=" * 60)
    
    gitee = Gitee(token)
    
    # 获取所有评论（分页）
    print("📋 正在获取评论列表...")
    comments = get_all_mr_comments(gitee, repo_url, MR_ID)
    print(f"共获取到 {len(comments)} 条评论")
    
    if not comments:
        print("✅ 无评论，无需清理。")
        return
    
    # 确认删除
    confirm = input(f"\n⚠️  确认删除 MR #{MR_ID} 的所有 {len(comments)} 条评论吗？(y/N): ")
    if confirm.lower() != 'y':
        print("❌ 用户取消操作")
        return
    
    print(f"\n🗑️  开始删除评论...")
    deleted_count = 0
    failed_count = 0
    
    for i, comment in enumerate(comments, 1):
        comment_id = comment.get('id')
        body = comment.get('body', '')
        author = comment.get('user', {}).get('login', 'unknown')
        
        try:
            success = gitee.delete_mr_comment(repo_url, comment_id)
            if success:
                print(f"✅ [{i}/{len(comments)}] 已删除评论ID: {comment_id} | 作者: {author} | 内容: {body[:30].replace(chr(10),' ')}")
                deleted_count += 1
            else:
                print(f"❌ [{i}/{len(comments)}] 删除评论ID: {comment_id} 失败")
                failed_count += 1
        except Exception as e:
            print(f"❌ [{i}/{len(comments)}] 删除评论ID: {comment_id} 异常: {e}")
            failed_count += 1
        
        time.sleep(0.5)  # 防止API限流
    
    print("\n" + "=" * 60)
    print(f"🎯 清理完成统计:")
    print(f"   📊 总评论数: {len(comments)}")
    print(f"   ✅ 成功删除: {deleted_count}")
    print(f"   ❌ 删除失败: {failed_count}")
    print(f"   📈 成功率: {deleted_count/len(comments)*100:.1f}%")

if __name__ == "__main__":
    main() 