#!/usr/bin/env python3
"""
测试修复验证脚本

验证所有修复的测试问题：
1. MockLLMClient参数签名问题
2. 正则表达式超时hang问题
3. 语法错误问题
"""

import subprocess
import sys
import time
from pathlib import Path

def run_test(test_path, description):
    """运行单个测试并记录结果"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"路径: {test_path}")
    print(f"{'='*60}")
    
    start_time = time.time()
    try:
        result = subprocess.run(
            ["python", "-m", "pytest", test_path, "-v"],
            capture_output=True,
            text=True,
            timeout=60  # 60秒超时
        )
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 测试通过 (耗时: {elapsed:.2f}秒)")
            return True
        else:
            print(f"❌ 测试失败 (耗时: {elapsed:.2f}秒)")
            print("STDOUT:", result.stdout[-500:])  # 显示最后500字符
            print("STDERR:", result.stderr[-500:])
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时 (>60秒)")
        return False
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Git Keeper 测试修复验证")
    print("验证所有已修复的测试问题...")
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent
    import os
    os.chdir(project_root)
    
    # 定义要测试的项目
    test_cases = [
        # MockLLMClient参数签名修复验证
        {
            "path": "tests/application/test_llm_service_call_chains.py",
            "description": "LLM服务调用链测试 (MockLLMClient修复)"
        },
        {
            "path": "tests/integration/test_call_chains_integration.py", 
            "description": "调用链集成测试 (MockLLMClient修复)"
        },
        
        # 正则表达式超时问题修复验证
        {
            "path": "tests/application/test_regex_matcher.py::TestRegexMatcherPerformance::test_timeout_handling",
            "description": "正则表达式超时处理测试 (hang问题修复)"
        },
        {
            "path": "tests/application/test_regex_matcher.py",
            "description": "完整正则表达式匹配器测试"
        },
        
        # 其他核心测试
        {
            "path": "tests/application/test_llm_service.py",
            "description": "LLM服务基础测试"
        },
        {
            "path": "tests/application/test_llm_service_limits.py",
            "description": "LLM服务限制测试"
        }
    ]
    
    # 运行测试
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        if run_test(test_case["path"], test_case["description"]):
            passed += 1
    
    # 总结
    print(f"\n{'='*60}")
    print(f"📊 测试总结")
    print(f"{'='*60}")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试都通过了！修复成功！")
        return 0
    else:
        print("⚠️  仍有测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
