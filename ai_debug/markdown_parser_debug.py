#!/usr/bin/env python3
"""
Markdown规则解析调试工具
"""
import argparse
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from gate_keeper.application.service.rule import RuleManager


class MarkdownParserDebug:
    """Markdown解析调试类"""
    
    def __init__(self, file_path: str):
        """
        初始化调试器
        
        Args:
            file_path: Markdown文件路径
        """
        self.file_path = file_path
        self.rule_manager = RuleManager(file_path)
        
    def parse_and_print(self):
        """解析并打印结果"""
        try:
            rules = self.rule_manager.load_rules()
            print(f"\n成功解析规则文件: {self.file_path}")
            print(f"共发现 {len(rules)} 条规则\n")
            
            # 转换规则为字典并打印
            for rule in rules:
                rule_dict = {
                    'id': rule.id,
                    'name': rule.name,
                    'category': rule.category,
                    'severity': rule.severity,
                    'languages': rule.languages,
                    'enabled': rule.enabled,
                    'rule_value': rule.rule_value,
                    'description': rule.description
                }
                print(json.dumps(rule_dict, ensure_ascii=False, indent=2))
                print("-" * 80)
                
        except Exception as e:
            print(f"解析失败: {str(e)}")
            raise


def main():
    parser = argparse.ArgumentParser(description='Markdown规则文件解析调试工具')
    parser.add_argument('file_path', help='Markdown文件路径')
    args = parser.parse_args()
    
    if not os.path.exists(args.file_path):
        print(f"错误: 文件不存在 - {args.file_path}")
        sys.exit(1)
        
    debugger = MarkdownParserDebug(args.file_path)
    debugger.parse_and_print()


if __name__ == '__main__':
    main() 