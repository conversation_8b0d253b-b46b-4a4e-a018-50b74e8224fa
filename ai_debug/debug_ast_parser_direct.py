#!/usr/bin/env python3
"""
直接测试AST解析器，检查Function对象的name属性
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.external.code_analyzer.parsers.python_parser import \
    PythonParser


def debug_ast_parser_direct():
    """直接测试AST解析器"""
    
    # 测试代码 - 模拟你遇到的问题
    test_code = """
def t_rule_manager_summary(self):
    return "summary"
"""
    
    parser = PythonParser()
    
    print("=== 直接测试AST解析器 ===")
    print(f"测试代码:\n{test_code}")
    print()
    
    # 测试extract_functions方法
    functions = parser.extract_functions("test.py", test_code)
    print(f"提取到 {len(functions)} 个函数:")
    for i, func in enumerate(functions):
        print(f"函数 {i+1}:")
        print(f"  name: '{func.name}'")
        print(f"  type: {type(func.name)}")
        print(f"  length: {len(func.name)}")
        print(f"  signature: '{func.signature}'")
        print(f"  start_line: {func.start_line}")
        print(f"  end_line: {func.end_line}")
        print(f"  code: '{func.code}'")
        print()
    
    # 测试find_parent_element方法
    print("=== 测试find_parent_element方法 ===")
    for line_num in [2, 3]:  # 测试函数定义行
        element = parser.find_parent_element("test.py", line_num, line_num, test_code)
        if element:
            print(f"第{line_num}行的元素:")
            print(f"  name: '{element.name}'")
            print(f"  type: {type(element.name)}")
            print(f"  length: {len(element.name)}")
            print(f"  signature: '{element.signature}'")
            print()

if __name__ == "__main__":
    debug_ast_parser_direct() 