#!/usr/bin/env python3
"""
AST解析器调试工具

功能：
1. 调试不同语言的AST解析器
2. 可视化AST树结构
3. 验证函数和调用提取逻辑
4. 对比不同解析器的行为

使用方法：
python ai_debug/ast_parser_debug.py [language]
language: python, c, all (默认)
"""

import os
import sys
from typing import Any, Dict, List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.application.service.ast_service import ASTService
from gate_keeper.external.code_analyzer.models.function import Function
from gate_keeper.external.code_analyzer.parsers.c_parser import CParser
from gate_keeper.external.code_analyzer.parsers.python_parser import \
    PythonParser
from gate_keeper.shared.log import app_logger as logger


class ASTParserDebugger:
    """AST解析器调试工具，用于分析和可视化AST解析结果。"""

    def __init__(self):
        """初始化AST解析器调试工具。"""
        self.ast_service = ASTService()

    def debug_output(self, title: str, content: Any) -> None:
        """
        标准化的调试输出格式。

        Args:
            title: 输出标题
            content: 要输出的内容
        """
        print(f"\n{'='*50}")
        print(f"{title}")
        print(f"{'='*50}")
        print(content)

    def analyze_file(self, filepath: str) -> Dict:
        """
        分析单个文件的AST结构。

        Args:
            filepath: 要分析的文件路径

        Returns:
            Dict: 包含分析结果的字典

        Raises:
            IOError: 文件读取错误
            Exception: 其他错误
        """
        try:
            parser = self.ast_service.get_parser_for_file(filepath)
            functions = parser.extract_functions(filepath)
            
            self.debug_output("File Analysis", f"Analyzing {filepath}")
            self.debug_output("Functions Found", f"Found {len(functions)} functions")
            
            for func in functions:
                self.debug_output(
                    f"Function: {func.name}",
                    f"Location: {func.start_line}-{func.end_line}\n"
                    f"Signature: {func.signature}\n"
                    f"Code:\n{func.code}"
                )

            return {
                "filepath": filepath,
                "function_count": len(functions),
                "functions": [
                    {
                        "name": f.name,
                        "start_line": f.start_line,
                        "end_line": f.end_line,
                        "signature": f.signature
                    }
                    for f in functions
                ]
            }

        except Exception as e:
            logger.error(f"Error analyzing file {filepath}: {str(e)}")
            raise

    def analyze_function(self, filepath: str, function_name: str) -> Optional[Dict]:
        """
        分析特定函数的AST结构。

        Args:
            filepath: 文件路径
            function_name: 函数名

        Returns:
            Optional[Dict]: 函数分析结果，如果未找到函数则返回None

        Raises:
            IOError: 文件读取错误
            Exception: 其他错误
        """
        try:
            parser = self.ast_service.get_parser_for_file(filepath)
            functions = parser.extract_functions(filepath)
            
            target_func = next((f for f in functions if f.name == function_name), None)
            if not target_func:
                self.debug_output("Error", f"Function {function_name} not found in {filepath}")
                return None

            self.debug_output(
                f"Function Analysis: {function_name}",
                f"Location: {target_func.start_line}-{target_func.end_line}\n"
                f"Signature: {target_func.signature}\n"
                f"Code:\n{target_func.code}"
            )

            # 获取函数的调用关系
            context = self.ast_service.get_code_context(filepath, target_func.start_line)
            if context["related_calls"]:
                self.debug_output(
                    "Related Calls",
                    "\n".join(f"- {call['caller']} -> {call['callee']} (line {call['line']})"
                             for call in context["related_calls"])
                )

            return {
                "function": {
                    "name": target_func.name,
                    "start_line": target_func.start_line,
                    "end_line": target_func.end_line,
                    "signature": target_func.signature,
                    "code": target_func.code
                },
                "related_calls": context["related_calls"]
            }

        except Exception as e:
            logger.error(f"Error analyzing function {function_name} in {filepath}: {str(e)}")
            raise


def main():
    """主函数"""
    language = sys.argv[1] if len(sys.argv) > 1 else "all"
    
    if language not in ["python", "c", "all"]:
        print("用法: python ast_parser_debug.py [python|c|all]")
        print("默认: all")
        return
    
    debugger = ASTParserDebugger()
    debugger.run(language)


if __name__ == "__main__":
    main() 