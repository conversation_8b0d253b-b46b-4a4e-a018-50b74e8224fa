#!/usr/bin/env python3
"""
测试正则表达式匹配功能

验证新增的match_regex和suggestion字段的解析和应用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.rule.regex_matcher import (RegexCache,
                                                                RegexMatcher)
from gate_keeper.application.service.rule.rule_service.manager import \
    RuleManager
from gate_keeper.external.code_analyzer.models.element import CodeElement


class MockCodeElement(CodeElement):
    """模拟代码元素，用于测试"""

    def __init__(self, name="test_func", content="", filepath="test.c"):
        super().__init__(name=name, type="function", filepath=filepath, start_line=1, end_line=10, code=content)


def test_yaml_parsing():
    """测试YAML解析是否正确处理新字段"""
    print("=== 测试YAML解析 ===")
    
    # 使用更新后的评测集
    rule_manager = RuleManager(
        rule_file_path="eval/datasets/c_evalset_real.yaml",
        enable_regex_matching=True
    )
    
    rules = rule_manager.load_rules()
    print(f"加载规则数量: {len(rules)}")
    
    # 检查是否有规则包含新字段
    rules_with_regex = [r for r in rules if r.match_regex]
    rules_with_suggestion = [r for r in rules if r.suggestion]
    
    print(f"包含match_regex的规则: {len(rules_with_regex)}")
    print(f"包含suggestion的规则: {len(rules_with_suggestion)}")
    
    # 显示几个示例
    if rules_with_regex:
        print("\n示例规则（包含match_regex）:")
        for i, rule in enumerate(rules_with_regex[:3]):
            print(f"  {i+1}. {rule.name[:50]}...")
            print(f"     match_regex: {rule.match_regex}")
            print(f"     suggestion: {rule.suggestion[:100]}...")
            print()
    
    return rules


def test_regex_matching():
    """测试正则表达式匹配功能"""
    print("=== 测试正则表达式匹配 ===")
    
    matcher = RegexMatcher()
    
    # 测试用例
    test_cases = [
        {
            "name": "内存拷贝函数",
            "code": """
            void copy_data(char *dst, char *src, int len) {
                memcpy(dst, src, len);
            }
            """,
            "expected_patterns": ["memcpy\\s*\\("]
        },
        {
            "name": "数组访问",
            "code": """
            int process_array(int arr[], int index) {
                return arr[index];
            }
            """,
            "expected_patterns": ["\\[\\s*\\w+\\s*\\]"]
        },
        {
            "name": "除法运算",
            "code": """
            int divide(int a, int b) {
                return a / b;
            }
            """,
            "expected_patterns": ["\\w+\\s*/\\s*\\w+"]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        print(f"代码: {test_case['code'].strip()}")
        
        for pattern in test_case['expected_patterns']:
            match_result = matcher.match_code(test_case['code'], pattern)
            print(f"  模式 '{pattern}': {'匹配' if match_result else '不匹配'}")


def test_rule_filtering():
    """测试基于regex的规则筛选"""
    print("\n=== 测试规则筛选 ===")
    
    rule_manager = RuleManager(
        rule_file_path="eval/datasets/c_evalset_real.yaml",
        enable_regex_matching=True
    )
    
    # 测试代码示例
    test_codes = [
        {
            "name": "内存拷贝代码",
            "content": """
            void unsafe_copy(char *dst, char *src, int len) {
                memcpy(dst, src, len);  // 未检查边界
            }
            """
        },
        {
            "name": "数组访问代码", 
            "content": """
            int get_element(int arr[], int index) {
                return arr[index];  // 未检查边界
            }
            """
        },
        {
            "name": "普通函数",
            "content": """
            int add(int a, int b) {
                return a + b;
            }
            """
        }
    ]
    
    for test_code in test_codes:
        print(f"\n测试代码: {test_code['name']}")
        
        # 创建模拟代码元素
        code_element = MockCodeElement(
            name=test_code['name'],
            content=test_code['content']
        )
        
        # 传统方法筛选
        traditional_rules = rule_manager.get_applicable_rules(code_element)
        traditional_count = sum(len(rules) for rules in traditional_rules.values())
        
        # 基于regex筛选
        regex_rules = rule_manager.get_applicable_rules_with_regex(code_element)
        regex_count = sum(len(rules) for rules in regex_rules.values())
        
        print(f"  传统方法匹配规则数: {traditional_count}")
        print(f"  Regex方法匹配规则数: {regex_count}")
        print(f"  筛选效果: {((traditional_count - regex_count) / traditional_count * 100):.1f}% 减少" if traditional_count > 0 else "无变化")


def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    import time
    
    rule_manager = RuleManager(
        rule_file_path="eval/datasets/c_evalset_real.yaml",
        enable_regex_matching=True
    )
    
    # 准备测试代码
    test_code = """
    void process_data(char *buffer, int size) {
        if (size > 0) {
            memcpy(buffer, source, size);
            for (int i = 0; i < size; i++) {
                buffer[i] = process_byte(buffer[i]);
            }
        }
    }
    """
    
    code_element = MockCodeElement(content=test_code)
    
    # 测试传统方法
    start_time = time.time()
    for _ in range(100):
        traditional_rules = rule_manager.get_applicable_rules(code_element)
    traditional_time = time.time() - start_time
    
    # 测试regex方法
    start_time = time.time()
    for _ in range(100):
        regex_rules = rule_manager.get_applicable_rules_with_regex(code_element)
    regex_time = time.time() - start_time
    
    print(f"传统方法100次耗时: {traditional_time:.3f}秒")
    print(f"Regex方法100次耗时: {regex_time:.3f}秒")
    print(f"性能比较: {'Regex更快' if regex_time < traditional_time else 'Regex更慢'} ({abs(regex_time - traditional_time):.3f}秒差异)")
    
    # 显示缓存统计
    stats = rule_manager.get_regex_matching_stats()
    print(f"\nRegex匹配统计: {stats}")


def main():
    """主函数"""
    print("开始测试正则表达式匹配功能...\n")
    
    try:
        # 测试YAML解析
        rules = test_yaml_parsing()
        
        # 测试正则匹配
        test_regex_matching()
        
        # 测试规则筛选
        test_rule_filtering()
        
        # 测试性能
        test_performance()
        
        print("\n=== 测试完成 ===")
        print("所有测试已完成，请检查输出结果。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()
