#!/usr/bin/env python3
"""
调试完整的数据流，从AST解析到AffectedFunction创建
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.application.dto.result import AffectedFunction
from gate_keeper.external.code_analyzer.parsers.python_parser import \
    PythonParser
from gate_keeper.shared.file import determine_language_by_filename


def debug_full_dataflow():
    """调试完整的数据流"""
    
    # 测试代码 - 模拟你遇到的问题
    test_code = """
def t_rule_manager_summary(self):
    return "summary"
"""
    
    test_file = "test_function.py"
    
    print("=== 调试完整数据流 ===")
    print(f"测试文件: {test_file}")
    print(f"测试代码:\n{test_code}")
    print()
    
    # 1. 检查语言检测
    lang = determine_language_by_filename(test_file)
    print(f"1. 语言检测结果: {lang}")
    print()
    
    # 2. 检查AST解析
    parser = PythonParser()
    functions = parser.extract_functions(test_file, test_code)
    print(f"2. AST解析结果 - 提取到 {len(functions)} 个函数:")
    for i, func in enumerate(functions):
        print(f"   函数 {i+1}:")
        print(f"     name: '{func.name}'")
        print(f"     type: {type(func.name)}")
        print(f"     length: {len(func.name)}")
        print(f"     signature: '{func.signature}'")
        print(f"     code: '{func.code}'")
        print()
    
    # 3. 模拟AffectedFunction创建
    if functions:
        func = functions[0]
        print("3. 模拟AffectedFunction创建:")
        af = AffectedFunction(
            name=func.name,
            type="function",
            start_line=func.start_line,
            end_line=func.end_line,
            changed_lines=[2, 3],
            code=func.code,
            filepath=test_file
        )
        print(f"   AffectedFunction.name: '{af.name}'")
        print(f"   AffectedFunction.name type: {type(af.name)}")
        print(f"   AffectedFunction.name length: {len(af.name)}")
        print()
    
    # 4. 检查是否有缓存问题
    print("4. 检查缓存问题:")
    print("   重新创建解析器...")
    parser2 = PythonParser()
    functions2 = parser2.extract_functions(test_file, test_code)
    if functions2:
        print(f"   重新解析结果 - name: '{functions2[0].name}'")
    print()
    
    # 5. 测试不同的文件扩展名
    print("5. 测试不同文件扩展名:")
    for ext in [".py", ".pyc", ".txt", ".md"]:
        test_file_with_ext = f"test_function{ext}"
        lang = determine_language_by_filename(test_file_with_ext)
        print(f"   文件 {test_file_with_ext}: 语言 = {lang}")

if __name__ == "__main__":
    debug_full_dataflow() 