#!/usr/bin/env python3
"""
清理RepositoryIndex缓存
"""

import os
import shutil
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.config import config


def clear_repo_index_cache():
    """清理RepositoryIndex缓存"""
    
    cache_dir = config.repo_cache_dir or ".cache"
    
    print(f"=== 清理RepositoryIndex缓存 ===")
    print(f"缓存目录: {cache_dir}")
    
    if os.path.exists(cache_dir):
        try:
            # 删除整个缓存目录
            shutil.rmtree(cache_dir)
            print(f"✅ 成功删除缓存目录: {cache_dir}")
        except Exception as e:
            print(f"❌ 删除缓存目录失败: {e}")
    else:
        print(f"ℹ️  缓存目录不存在: {cache_dir}")
    
    print()
    print("💡 提示：")
    print("   - 下次运行时会重新构建RepositoryIndex")
    print("   - 这可能会花费一些时间，但会使用最新的AST解析器")
    print("   - 如果问题仍然存在，请检查具体的文件内容")

if __name__ == "__main__":
    clear_repo_index_cache() 