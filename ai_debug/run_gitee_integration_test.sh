#!/bin/bash

# AI调试工具：Gitee集成测试运行脚本

echo "🔧 AI调试工具：Gitee集成测试"
echo "=========================================="

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python"
    exit 1
fi

# 检查配置文件
if [ ! -f "../gate_keeper/config/config_dev.py" ]; then
    echo "❌ 错误: 未找到配置文件 gate_keeper/config/config_dev.py"
    echo "请确保已正确配置Gitee token和仓库URL"
    exit 1
fi

# 检查测试文件
if [ ! -f "../tests/integration/test_gitee_integration.py" ]; then
    echo "❌ 错误: 未找到测试文件 tests/integration/test_gitee_integration.py"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 运行测试
echo "🚀 运行Gitee集成测试..."
echo "测试目标: MR #8 (test_dev -> test_base)"
echo ""

python tests/integration/test_gitee_integration.py

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "✅ Gitee集成测试完成"
    echo "=========================================="
    echo ""
    echo "📋 测试内容:"
    echo "1. ✅ 获取MR信息"
    echo "2. ✅ 获取MR评论列表"
    echo "3. ✅ 发布单条评论"
    echo "4. ✅ 简单评论排重检测"
    echo "5. ✅ 批量评论发布"
    echo "6. ✅ MR分析集成功能"
    echo "7. ✅ ReportService直接发布"
    echo "8. ✅ 测试评论统计"
    echo ""
    echo "🎉 所有测试通过！"
else
    echo ""
    echo "=========================================="
    echo "❌ Gitee集成测试失败"
    echo "=========================================="
    echo ""
    echo "🔍 请检查:"
    echo "1. Gitee token是否正确配置"
    echo "2. 仓库URL是否正确"
    echo "3. MR #8是否存在且可访问"
    echo "4. 网络连接是否正常"
    echo ""
    exit 1
fi 