#!/usr/bin/env python3
"""
测试规则预筛选功能

验证不同预筛选策略的效果和性能
"""

import sys
import time
from pathlib import Path
from unittest.mock import Mock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from gate_keeper.application.service.rule.rule_service.manager import RuleManager
from gate_keeper.application.service.rule.rule_prescreening import PreScreeningStrategy
from gate_keeper.external.code_analyzer.models.element import CodeElement


def create_mock_code_element(name: str, code: str, filepath: str = "test.c") -> CodeElement:
    """创建模拟代码元素"""
    element = Mock(spec=CodeElement)
    element.name = name
    element.type = "function"
    element.filepath = filepath
    element.code = code
    element.start_line = 1
    element.end_line = 10
    return element


def test_prescreening_strategies():
    """测试不同的预筛选策略"""
    
    print("=" * 60)
    print("测试规则预筛选策略")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "内存拷贝函数",
            "code": """
            void unsafe_copy(char *dst, char *src, int len) {
                memcpy(dst, src, len);  // 未检查边界
            }
            """,
            "expected_keywords": ["memcpy", "内存", "拷贝", "边界"]
        },
        {
            "name": "数组访问函数",
            "code": """
            int get_element(int arr[], int index) {
                return arr[index];  // 未检查边界
            }
            """,
            "expected_keywords": ["数组", "下标", "边界"]
        },
        {
            "name": "除法运算函数",
            "code": """
            int divide(int a, int b) {
                return a / b;  // 未检查除零
            }
            """,
            "expected_keywords": ["除法", "除零"]
        },
        {
            "name": "普通函数",
            "code": """
            int add(int a, int b) {
                return a + b;
            }
            """,
            "expected_keywords": []
        }
    ]
    
    # 测试策略
    strategies = [
        {"name": "无预筛选", "config": {"strategy": "none"}},
        {"name": "正则预筛选", "config": {"strategy": "regex"}},
        # 注意：embedding策略需要API密钥，在测试中可能会失败
        # {"name": "语义预筛选", "config": {"strategy": "embedding", "embedding_similarity_threshold": 0.6}},
    ]
    
    # 加载规则
    rule_file_path = "eval/datasets/c_evalset_real.yaml"
    if not Path(rule_file_path).exists():
        print(f"⚠️ 评测集文件不存在: {rule_file_path}")
        print("使用模拟规则进行测试...")
        return
    
    results = {}
    
    for strategy in strategies:
        print(f"\n--- 测试策略: {strategy['name']} ---")
        
        try:
            # 创建规则管理器
            rule_manager = RuleManager(
                rule_file_path=rule_file_path,
                prescreening_config=strategy['config']
            )
            
            strategy_results = []
            
            for test_case in test_cases:
                print(f"\n测试用例: {test_case['name']}")
                
                # 创建代码元素
                code_element = create_mock_code_element(
                    test_case['name'],
                    test_case['code']
                )
                
                # 测试性能
                start_time = time.time()
                
                # 使用预筛选
                prescreened_rules = rule_manager.get_applicable_rules_with_prescreening(code_element)
                prescreened_count = sum(len(rules) for rules in prescreened_rules.values())
                
                # 传统方法
                traditional_rules = rule_manager.get_applicable_rules(code_element)
                traditional_count = sum(len(rules) for rules in traditional_rules.values())
                
                processing_time = time.time() - start_time
                
                # 计算筛选效果
                if traditional_count > 0:
                    reduction_rate = (traditional_count - prescreened_count) / traditional_count * 100
                else:
                    reduction_rate = 0
                
                result = {
                    "test_case": test_case['name'],
                    "traditional_count": traditional_count,
                    "prescreened_count": prescreened_count,
                    "reduction_rate": reduction_rate,
                    "processing_time": processing_time
                }
                
                strategy_results.append(result)
                
                print(f"  传统方法规则数: {traditional_count}")
                print(f"  预筛选规则数: {prescreened_count}")
                print(f"  筛选效果: {reduction_rate:.1f}% 减少")
                print(f"  处理时间: {processing_time:.3f}s")
            
            results[strategy['name']] = strategy_results
            
            # 获取统计信息
            stats = rule_manager.get_prescreening_stats()
            print(f"\n策略统计:")
            print(f"  总请求数: {stats.get('total_requests', 0)}")
            print(f"  降级次数: {stats.get('fallback_count', 0)}")
            if 'performance' in stats:
                for perf_key, perf_values in stats['performance'].items():
                    if perf_values and isinstance(perf_values, list):
                        avg_time = sum(perf_values) / len(perf_values)
                        print(f"  {perf_key}平均时间: {avg_time:.3f}s")
            
        except Exception as e:
            print(f"❌ 策略 {strategy['name']} 测试失败: {e}")
            continue
    
    # 输出对比结果
    print(f"\n" + "=" * 60)
    print("策略对比结果")
    print("=" * 60)
    
    for strategy_name, strategy_results in results.items():
        print(f"\n{strategy_name}:")
        total_reduction = 0
        total_time = 0
        
        for result in strategy_results:
            total_reduction += result['reduction_rate']
            total_time += result['processing_time']
        
        avg_reduction = total_reduction / len(strategy_results) if strategy_results else 0
        avg_time = total_time / len(strategy_results) if strategy_results else 0
        
        print(f"  平均筛选效果: {avg_reduction:.1f}% 减少")
        print(f"  平均处理时间: {avg_time:.3f}s")


def test_configuration_options():
    """测试配置选项"""
    
    print(f"\n" + "=" * 60)
    print("测试配置选项")
    print("=" * 60)
    
    # 测试不同的配置
    configs = [
        {
            "name": "默认正则配置",
            "config": {"strategy": "regex"}
        },
        {
            "name": "严格正则配置",
            "config": {
                "strategy": "regex",
                "regex_timeout": 0.5,
                "max_processing_time": 2.0
            }
        },
        {
            "name": "无预筛选配置",
            "config": {"strategy": "none"}
        }
    ]
    
    rule_file_path = "eval/datasets/c_evalset_real.yaml"
    if not Path(rule_file_path).exists():
        print(f"⚠️ 评测集文件不存在，跳过配置测试")
        return
    
    test_code = create_mock_code_element(
        "test_function",
        "void test() { memcpy(dst, src, len); }"
    )
    
    for config_info in configs:
        print(f"\n--- {config_info['name']} ---")
        
        try:
            rule_manager = RuleManager(
                rule_file_path=rule_file_path,
                prescreening_config=config_info['config']
            )
            
            start_time = time.time()
            rules = rule_manager.get_applicable_rules_with_prescreening(test_code)
            processing_time = time.time() - start_time
            
            rule_count = sum(len(rule_list) for rule_list in rules.values())
            
            print(f"  规则数量: {rule_count}")
            print(f"  处理时间: {processing_time:.3f}s")
            
            # 获取统计信息
            stats = rule_manager.get_prescreening_stats()
            print(f"  策略: {stats.get('manager_type', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ 配置测试失败: {e}")


def test_dynamic_configuration():
    """测试动态配置更新"""
    
    print(f"\n" + "=" * 60)
    print("测试动态配置更新")
    print("=" * 60)
    
    rule_file_path = "eval/datasets/c_evalset_real.yaml"
    if not Path(rule_file_path).exists():
        print(f"⚠️ 评测集文件不存在，跳过动态配置测试")
        return
    
    try:
        # 创建初始配置
        rule_manager = RuleManager(
            rule_file_path=rule_file_path,
            prescreening_config={"strategy": "regex"}
        )
        
        test_code = create_mock_code_element(
            "test_function",
            "void test() { memcpy(dst, src, len); }"
        )
        
        # 测试初始配置
        print("初始配置 (regex):")
        rules1 = rule_manager.get_applicable_rules_with_prescreening(test_code)
        count1 = sum(len(rule_list) for rule_list in rules1.values())
        print(f"  规则数量: {count1}")
        
        # 更新配置
        print("\n更新配置 (none):")
        rule_manager.update_prescreening_config({"strategy": "none"})
        
        rules2 = rule_manager.get_applicable_rules_with_prescreening(test_code)
        count2 = sum(len(rule_list) for rule_list in rules2.values())
        print(f"  规则数量: {count2}")
        
        print(f"\n配置更新效果: {count1} -> {count2} 规则")
        
    except Exception as e:
        print(f"❌ 动态配置测试失败: {e}")


def main():
    """主函数"""
    
    print("开始测试规则预筛选功能...\n")
    
    try:
        # 测试预筛选策略
        test_prescreening_strategies()
        
        # 测试配置选项
        test_configuration_options()
        
        # 测试动态配置
        test_dynamic_configuration()
        
        print(f"\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        print("✅ 规则预筛选功能测试完成")
        print("\n注意事项:")
        print("- embedding策略需要配置API密钥才能正常工作")
        print("- 生产环境建议使用regex策略以确保性能稳定")
        print("- 可以通过配置文件调整各种参数")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
