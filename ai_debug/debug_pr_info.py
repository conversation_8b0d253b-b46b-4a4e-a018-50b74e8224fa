#!/usr/bin/env python3
"""
调试MR信息获取脚本
用于验证Gitee API调用和MR信息获取
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.config.config import repo_url, token
from gate_keeper.infrastructure.git.gitee.client import Gite<PERSON>


def debug_mr_info():
    """调试MR信息获取"""
    print("🔍 调试MR信息获取")
    print("=" * 50)
    
    # 初始化Gitee客户端
    gitee_client = Gitee(token)
    
    # 测试参数
    test_mr_number = 8  # MR编号
    print(f"📋 测试参数:")
    print(f"   - 仓库URL: {repo_url}")
    print(f"   - MR编号: {test_mr_number}")
    print(f"   - Token: {'*' * 10 + token[-4:] if token else 'None'}")
    print()
    
    # 步骤1: 解析仓库URL
    print("🔧 步骤1: 解析仓库URL")
    try:
        owner, repo = gitee_client._parse_repo_url(repo_url)
        print(f"   ✅ 解析成功: owner={owner}, repo={repo}")
    except Exception as e:
        print(f"   ❌ 解析失败: {e}")
        return
    print()
    
    # 步骤2: 构建API URL
    print("🔧 步骤2: 构建API URL")
    api_url = f"{gitee_client.api_base}/repos/{owner}/{repo}/pulls/{test_mr_number}"
    print(f"   API URL: {api_url}")
    print()
    
    # 步骤3: 获取MR信息
    print("🔧 步骤3: 获取MR信息")
    try:
        mr_info = gitee_client.get_mr_info(repo_url, test_mr_number)
        print(f"   API响应: {mr_info}")
        
        if mr_info is None:
            print("   ❌ 获取MR信息失败，返回None")
            print("   💡 可能的原因:")
            print("      - MR编号不存在")
            print("      - Token权限不足")
            print("      - 仓库不存在或无权访问")
            print("      - 网络连接问题")
        else:
            print("   ✅ 获取MR信息成功")
            print(f"   📊 MR详情:")
            print(f"      - ID: {mr_info.get('id')}")
            print(f"      - 编号: {mr_info.get('number')}")
            print(f"      - 标题: {mr_info.get('title')}")
            print(f"      - 状态: {mr_info.get('state')}")
            print(f"      - 创建者: {mr_info.get('user', {}).get('login')}")
            print(f"      - 创建时间: {mr_info.get('created_at')}")
            
    except Exception as e:
        print(f"   ❌ 获取MR信息异常: {e}")
    print()
    
    # 步骤4: 测试获取MR列表（可选）
    print("🔧 步骤4: 获取MR列表（验证仓库访问权限）")
    try:
        # 构建获取MR列表的URL
        mr_list_url = f"{gitee_client.api_base}/repos/{owner}/{repo}/pulls"
        params = {"access_token": token, "state": "all", "per_page": 10}
        
        import requests
        resp = requests.get(mr_list_url, params=params)
        print(f"   MR列表API状态码: {resp.status_code}")
        
        if resp.status_code == 200:
            mr_list = resp.json()
            print(f"   ✅ 获取MR列表成功，共{len(mr_list)}个MR")
            print("   📋 MR列表:")
            for mr in mr_list[:5]:  # 只显示前5个
                print(f"      - #{mr.get('number')}: {mr.get('title')} (状态: {mr.get('state')})")
        else:
            print(f"   ❌ 获取MR列表失败: {resp.status_code} {resp.text}")
            
    except Exception as e:
        print(f"   ❌ 获取MR列表异常: {e}")
    print()
    
    # 步骤5: 测试Token有效性
    print("🔧 步骤5: 测试Token有效性")
    try:
        # 获取用户信息
        user_url = f"{gitee_client.api_base}/user"
        params = {"access_token": token}
        
        import requests
        resp = requests.get(user_url, params=params)
        print(f"   用户信息API状态码: {resp.status_code}")
        
        if resp.status_code == 200:
            user_info = resp.json()
            print(f"   ✅ Token有效")
            print(f"   👤 用户信息: {user_info.get('login')} ({user_info.get('name')})")
        else:
            print(f"   ❌ Token无效: {resp.status_code} {resp.text}")
            
    except Exception as e:
        print(f"   ❌ 测试Token异常: {e}")
    print()
    
    print("=" * 50)
    print("🔍 调试完成")

if __name__ == "__main__":
    debug_mr_info() 