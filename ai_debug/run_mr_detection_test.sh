#!/bin/bash

# Gitee MR检测、结果提交、重复检测和排重的集成测试脚本
# 测试场景：针对同一个MR多次提交导致重复评论的排重功能

set -e

echo "🔍 Gitee MR检测、结果提交、重复检测和排重的集成测试"
echo "=================================================="

# 检查环境
echo "📋 检查测试环境..."
if [ ! -f "gate_keeper/config/config.py" ]; then
    echo "❌ 配置文件不存在: gate_keeper/config/config.py"
    exit 1
fi

# 检查Token配置
if ! grep -q "token" gate_keeper/config/config.py; then
    echo "❌ 未找到Token配置"
    exit 1
fi

echo "✅ 环境检查通过"

# 运行集成测试
echo ""
echo "🚀 开始运行Gitee MR检测集成测试..."
echo ""

# 运行Gitee专用的集成测试
python -m pytest tests/integration/test_gitee_mr_detection_deduplication.py \
    -v \
    -s \
    --tb=short \
    --durations=10 \
    --color=yes \
    -m "gitee or integration"

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 所有Gitee MR检测集成测试通过！"
    echo ""
    echo "✅ 测试覆盖范围："
    echo "   - Gitee MR检测和结果提交流程"
    echo "   - Gitee评论排重功能"
    echo "   - Gitee相似度排重算法"
    echo "   - Gitee批量评论排重"
    echo "   - Gitee MR分析集成"
    echo "   - Gitee错误处理和恢复"
    echo "   - Gitee性能和API限流"
    echo "   - Gitee数据一致性验证"
    echo "   - Gitee评论管理完整工作流"
    echo "   - Gitee真实场景模拟"
    echo ""
    echo "📊 测试结果总结："
    echo "   - 完整验证了Gitee MR检测到结果提交的流程"
    echo "   - 验证了Gitee评论排重功能的有效性"
    echo "   - 确认了Gitee集成的稳定性"
    echo "   - 验证了Gitee错误处理和恢复机制"
    echo "   - 确认了Gitee性能和限流处理"
    echo "   - 验证了Gitee真实场景的适用性"
else
    echo ""
    echo "❌ Gitee MR检测集成测试失败"
    echo ""
    echo "🔧 故障排除建议："
    echo "   1. 检查网络连接和Gitee API访问"
    echo "   2. 验证Token权限和有效性"
    echo "   3. 确认目标MR存在且可访问"
    echo "   4. 检查API限流和重试机制"
    echo "   5. 查看详细错误日志"
    echo "   6. 确认测试配置文件正确"
    exit 1
fi

echo ""
echo "📝 测试完成时间: $(date)"
echo "==================================================" 