#!/usr/bin/env python3
"""
调试特定文件的Function对象创建过程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gate_keeper.external.code_analyzer.parsers.python_parser import PythonParser
from gate_keeper.external.code_analyzer.parsers.c_parser import CParser
from gate_keeper.external.code_analyzer.core.ast_parser import create_parser_by_lang
from gate_keeper.shared.file import determine_language_by_filename

def debug_specific_file(file_path):
    """调试特定文件的Function对象创建过程"""
    
    print(f"=== 调试文件: {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    # 1. 检查文件内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"1. 文件大小: {len(content)} 字符")
        print(f"   文件编码: UTF-8")
        print(f"   前100字符: {remr(content[:100])}")
        print()
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 2. 检查语言检测
    lang = determine_language_by_filename(file_path)
    print(f"2. 语言检测结果: {lang}")
    print()
    
    # 3. 检查解析器
    if lang:
        try:
            parser = create_parser_by_lang(lang)
            print(f"3. 解析器类型: {type(parser).__name__}")
        except Exception as e:
            print(f"❌ 创建解析器失败: {e}")
            return
    else:
        print("❌ 无法识别文件语言")
        return
    
    # 4. 检查函数提取
    try:
        functions = parser.extract_functions(file_path)
        print(f"4. 提取到的函数数量: {len(functions)}")
        
        for i, func in enumerate(functions):
            print(f"   函数 {i+1}:")
            print(f"     name: '{func.name}'")
            print(f"     name type: {type(func.name)}")
            print(f"     name length: {len(func.name)}")
            print(f"     signature: '{func.signature}'")
            print(f"     start_line: {func.start_line}")
            print(f"     end_line: {func.end_line}")
            print(f"     code preview: {remr(func.code[:100])}")
            print()
            
            # 检查是否有问题的函数名
            if len(func.name) > 50 or '\n' in func.name:
                print(f"    ⚠️  警告：函数名可能有问题！")
                print(f"       长度: {len(func.name)}")
                print(f"       包含换行符: {'\\n' in func.name}")
                print()
        
    except Exception as e:
        print(f"❌ 函数提取失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 5. 检查调用提取
    try:
        calls = parser.extract_calls(file_path)
        print(f"5. 提取到的调用数量: {len(calls)}")
        for i, call in enumerate(calls[:5]):  # 只显示前5个
            print(f"   调用 {i+1}:")
            print(f"     caller: '{call.caller}'")
            print(f"     callee: '{call.callee}'")
            print(f"     line: {call.line}")
            print()
    except Exception as e:
        print(f"❌ 调用提取失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        debug_specific_file(file_path)
    else:
        print("用法: python debug_specific_file.py <文件路径>")
        print("例如: python debug_specific_file.py /path/to/your/file.py") 