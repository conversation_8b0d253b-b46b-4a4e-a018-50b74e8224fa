from setuptools import find_packages, setup

setup(
    name="gate_keeper",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "requests>=2.25.0",
        "pytest>=6.0.0",
        "pydantic>=1.8.0",
        "tree-sitter>=0.20.0",
        "numpy>=1.19.0",
        "pandas>=1.2.0",
        "scikit-learn>=0.24.0",
        "python-Levenshtein>=0.12.0",
    ],
    python_requires=">=3.8",
    description="A code review and quality assurance tool",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
) 